#oracle database setting
jdbc.driver=org.postgresql.Driver
#測試環境
jdbc.url=*************************************************************************
jdbc.username=hrsign_ipebg
jdbc.password=Hrsign_123
#正式環境
#jdbc.url=************************************************************************
#jdbc.username=hrsign_ipebg
#jdbc.password=sign#1521iPEBG

#connection pool settings
jdbc.pool.maxIdle=5
jdbc.pool.maxActive=40

#hibernate settings
hibernate.show_sql=false
hibernate.format_sql=false
hibernate.dialect=org.hibernate.dialect.PostgreSQL82Dialect
hibernate.search.default.indexBase=indexes
#hibernate.hbm2ddl.auto=update

#cache settings
hibernate.ehcache.configFile=cache/ehcache-hibernate-local.xml
hibernate.ehcache.use_second_level_cache=true
hibernate.ehcache.use_query_cache=true
ehcache.configFile=cache/ehcache-local.xml

#admin path
adminPath=/admin
#部署項目名稱
sendMailAuditPath=/newEsignPg

task.core_pool_size=10
task.max_pool_size=50
task.queue_capacity=1000
task.keep_alive_seconds=300

rabbit.host=**************
rabbit.virtual-host=/
rabbit.port=5672
rabbit.username=guest
rabbit.password=guest

spring.kafka.product.bootstrap-servers=*************:9092
spring.kafka.product.retries=3
spring.kafka.product.batch-size=16384
spring.kafka.product.buffer-memory=33554432
spring.kafka.product.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.product.value-serializer=org.apache.kafka.common.serialization.StringSerializer

##對象存儲
aws.accessKey=AKIDpSmnUrtbz3nDK6ZiX2C2XhX3F6dqiFSK
aws.secretKey=dvtWomQlmSopWtvcz3nDI3VcPr0oISHm
aws.bucket=newesign-1255000011
aws.endpoint=http://cos.zhengzhou.fii-foxconn.com
#最大文件上传限制，单位字节. 2*10M=2*10*1024*1024(B)=2*10485760=20971520 bytes
aws.fileSize=20971520
aws.pathStyleAccess=true
aws.maxConnections=100
directory=entfrmTest