#代码生成器，配置信息
#包名
package=com.foxconn.ipebg.buessness.information
#作者
author=S6114648
#Email
email=<EMAIL>
#表前缀(类名不会包含表前缀)
tablePrefix=T_QH
#baseDao 路径
baseDao=com.foxconn.ipebg.common.persistence
#utils 路径
utils=com.foxconn.ipebg.common.utils
#模块配置
module=information

#类型转换，配置信息

numeric=Integer
timestamp=Date
varchar=String
bpchar=String
CLOB=String
BLOB=String
CHAR=String

#流程編碼code dzqh_yuangongjiajiang_v1
workFlowId=dzqh_feiyongyutibiao

#生成文件绝对路径
projectPath=D:\\IdeaWork\\IPEBG\\
#entity=E:\\IDEAWork\\hxyFrame_20171103_github\\hxyFrame\\frame-service\\src\\main\\java\\com\\hxy\\sys\\entity
#dao=E:\\IDEAWork\\hxyFrame_20171103_github\\hxyFrame\\frame-service\\src\\main\\java\\com\\hxy\\sys\\dao
#mapping=E:\\IDEAWork\\hxyFrame_20171103_github\\hxyFrame\\frame-service\\src\\main\\resources\\mapping
#service=E:\\IDEAWork\\hxyFrame_20171103_github\\hxyFrame\\frame-service\\src\\main\\java\\com\\hxy\\sys\\service
#serviceImpl=E:\\IDEAWork\\hxyFrame_20171103_github\\hxyFrame\\frame-service\\src\\main\\java\\com\\hxy\\sys\\service\\impl
#controller=E:\\IDEAWork\\hxyFrame_20171103_github\\hxyFrame\\frame-admin\\src\\main\\java\\com\\hxy\\sys\\controller
#view=E:\\IDEAWork\\hxyFrame_20171103_github\\hxyFrame\\frame-admin\\src\\main\\webapp\\WEB-INF\\views\\sys
#js=E:\\IDEAWork\\hxyFrame_20171103_github\\hxyFrame\\frame-admin\\src\\main\\webapp\\statics\\js\\sys
#sql=E:\\IDEAWork\\hxyFrame_20171103_github\\hxyFrame\\doc\\sql
