<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:task="http://www.springframework.org/schema/task"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
      http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
      http://www.springframework.org/schema/context
      http://www.springframework.org/schema/context/spring-context-3.2.xsd
      http://www.springframework.org/schema/task 
      http://www.springframework.org/schema/task/spring-task-3.2.xsd">

	<!-- 项目用了数据库持久化，没用此配置文件的方式，供参考 -->
	<!-- 启动触发器的配置开始 -->
	<!-- 总管理类 如果将lazy-init='false'那么容器启动就会执行调度程序  -->
	<bean name="startQuertz" lazy-init="false" autowire="no" class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
		<property name="triggers">
			<list>
				<ref bean="myJobTrigger" />
			</list>
		</property>
	</bean>
	<!-- 启动触发器的配置结束 -->

	<!-- 调度的配置开始 -->
	<!--quartz-1.8以前的配置 
	<bean id="myJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean"></bean>
	-->
	<!-- quartz-2.x的配置 -->
	<!--<bean id="myJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail">
			<ref bean="myJobDetail" />
		</property>
		<property name="cronExpression">
			<value>0 0 12 * * ?</value>
		</property>
	</bean>-->
	<!-- 调度的配置结束 -->

	<!-- job的配置开始 -->
	<!--<bean id="myJobDetail"
		class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
		<property name="targetObject">
			<ref bean="myJob" />
		</property>
		<property name="targetMethod">
			<value>work</value>
		</property>
	</bean>-->
	<!-- job的配置结束 -->

	<!-- 工作的bean -->
	<!--<bean id="myJob" class="com.ty.tianyu.common.utils.MyJob" />-->
	
</beans>