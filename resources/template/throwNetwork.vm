<root>
    <!--承辦人信息-->
    <undertaker>
        <id>${wfcommnetProcessEntity.serialno}</id>
        <bg>${businessGroupName}</bg>
        <staff_id>${wfcommnetProcessEntity.makerno}</staff_id>
        <staff_name>${wfcommnetProcessEntity.makername}</staff_name>
        <departno>${wfcommnetProcessEntity.makerdeptno}</departno>
        <departname>${wfcommnetProcessEntity.makerdeptname}</departname>
        <tel>${wfcommnetProcessEntity.makertel}</tel>
        <createdate>${wfcommnetProcessEntity.createtime}</createdate>
        <lastuserverifydate>${wfcommnetProcessEntity.complettime}</lastuserverifydate>
        <yn_artical>Y</yn_artical>
        <email>${wfcommnetProcessEntity.makeremail}</email>
</undertaker>
    <!--簽核流程（有5個flow分別是 C：承辦人 UZG：直屬主管審核 JG：經管主管會簽 ITC：IT主管會簽 UHV：主管核准）-->
    #if ($flowList && $flowList.size() > 0)
    #foreach($flow in $flowList) 
	<flow>
         <node_id>${flow.node_id}</node_id>
         <node_name>${flow.node_name}</node_name>
         <deputy_id>${flow.deputy_id}</deputy_id>
         <deputy_name>${flow.deputy_name}</deputy_name>
         <memo>${flow.memo}</memo>
         <signdate>${flow.signdate}</signdate>
         <ip>${flow.ip}</ip>
         <signer>${flow.signer}</signer>
         <signername>${flow.signername}</signername>
         <signemail>${flow.signemail}</signemail>
    </flow>
    #end   
	#end   
    <!--申請信息-->
    <info>
       <userinfo>
           <staff_no>${wfcommnetProcessEntity.applyno}</staff_no>
           <staff_name>${wfcommnetProcessEntity.applyname}</staff_name>
           <fac_name>${facName}</fac_name>
           <bg>${businessGroupName}</bg>
           <departname>${wfcommnetProcessEntity.applydepartname}</departname>
           <tel>${wfcommnetProcessEntity.applytel}</tel>
           <cost_no>${wfcommnetProcessEntity.applycostno}</cost_no>
           <email>${wfcommnetProcessEntity.applyemail}</email>
           <longevity>${wfcommnetProcessEntity.applyLevel}</longevity>
           <duty>${wfcommnetProcessEntity.applyPost}</duty>
           <company_code>$!{wfcommnetProcessEntity.applycompanycode}</company_code>
           <company_name>$!{wfcommnetProcessEntity.applycompanyname}</company_name>
       </userinfo>
       <!--E:分機號碼并機 F:刪除分機 J:刪除SIP號碼 K:刪除IP Phone號碼 Q:刪除直線號碼-->
       #if($!wfcommnetProcessEntity.mergeNo && $!wfcommnetProcessEntity.mergeNo != "")
       	 <item>
            <reqtype>E</reqtype>
            <req_content>${wfcommnetProcessEntity.mergeNo}</req_content>
            <numberamount>${wfcommnetProcessEntity.mergeNum}</numberamount>
        	<memo><![CDATA[${wfcommnetProcessEntity.mergeDetail}]]></memo>
       	</item>
       #end

       #if($!wfcommnetProcessEntity.deleteNo && $!wfcommnetProcessEntity.deleteNo != "")
       	 <item>
            <reqtype>F</reqtype>
            <req_content>${wfcommnetProcessEntity.deleteNo}</req_content>
            <numberamount>${wfcommnetProcessEntity.deleteNum}</numberamount>
        	<memo><![CDATA[${wfcommnetProcessEntity.deleteDetail}]]></memo>
       	</item>
       #end

       #if($!wfcommnetProcessEntity.deleteSipNo && $!wfcommnetProcessEntity.deleteSipNo != "")
       	 <item>
            <reqtype>J</reqtype>
            <req_content>${wfcommnetProcessEntity.deleteSipNo}</req_content>
            <numberamount>${wfcommnetProcessEntity.deleteSipNum}</numberamount>
        	<memo><![CDATA[${wfcommnetProcessEntity.deleteSipDetail}]]></memo>
       	</item>
       #end

       #if($!wfcommnetProcessEntity.deleteCiscoNo && $!wfcommnetProcessEntity.deleteCiscoNo != "")
       	 <item>
            <reqtype>K</reqtype>
            <req_content>${wfcommnetProcessEntity.deleteCiscoNo}</req_content>
            <numberamount>${wfcommnetProcessEntity.deleteCiscoNum}</numberamount>
        	<memo><![CDATA[${wfcommnetProcessEntity.deleteCiscoDetail}]]></memo>
       	</item>
       #end

       #if($!wfcommnetProcessEntity.deleteLineNo && $!wfcommnetProcessEntity.deleteLineNo != "")
       	 <item>
            <reqtype>Q</reqtype>
            <req_content>${wfcommnetProcessEntity.deleteLineNo}</req_content>
            <numberamount>${wfcommnetProcessEntity.deleteLineNum}</numberamount>
        	<memo><![CDATA[${wfcommnetProcessEntity.deleteLineDetail}]]></memo>
       	</item>
       #end

       <!--B:網絡不通 X:光纖不通 Y:有線電視不通 M:新增網點 N:網線遷移 O:新增直線電話-->
       #if($!wfcommnetProcessEntity.newDotLocation && $!wfcommnetProcessEntity.newDotLocation != "")
       <item>
            <reqtype>M</reqtype>
            <req_content>${wfcommnetProcessEntity.newDotLocation}</req_content>
            <numberamount>${wfcommnetProcessEntity.newDotNum}</numberamount>
            <memo><![CDATA[${wfcommnetProcessEntity.newDotDetail}]]></memo>
       </item>
       #end

       #if($!wfcommnetProcessEntity.transDotLocation && $!wfcommnetProcessEntity.transDotLocation != "")
       <item>
            <reqtype>N</reqtype>
            <req_content>${wfcommnetProcessEntity.transDotLocation}</req_content>
            <numberamount>${wfcommnetProcessEntity.transDotNum}</numberamount>
            <memo><![CDATA[${wfcommnetProcessEntity.transDotDetail}]]></memo>
       </item>
       #end
        #if($!wfcommnetProcessEntity.otherRequireDetail && $!wfcommnetProcessEntity.otherRequireDetail != "")
            <item>
                <reqtype>W</reqtype>
                <memo><![CDATA[${wfcommnetProcessEntity.otherRequireDetail}]]></memo>
            </item>
        #end
        #if($!wfcommnetProcessEntity.interComFmDetail && $!wfcommnetProcessEntity.interComFmDetail != "")
            <item>
                <reqtype>U</reqtype>
                <memo><![CDATA[${wfcommnetProcessEntity.interComFmDetail}]]></memo>
            </item>
        #end

       #if($!wfcommnetProcessEntity.newLineLocation && $!wfcommnetProcessEntity.newLineLocation != "")
       <item>
            <reqtype>O</reqtype>
            <req_content>${wfcommnetProcessEntity.newLineLocation}</req_content>
            <numberamount>${wfcommnetProcessEntity.newLineNum}</numberamount>
            <memo><![CDATA[${wfcommnetProcessEntity.newLineDetail}]]></memo>
       </item>
       #end

       <!--C:新增分機號碼 G:新增SIP號碼-->
        #if ($newTelephones && $newTelephones.size() > 0) 
	    <item>
            <reqtype>C</reqtype>
            #foreach($v in $newTelephones) 
            <record>
                <staff_no>${v.empNo}</staff_no>
                <staff_name>${v.empName}</staff_name>
                <address>${v.requireLocation}</address>
                <memo><![CDATA[${v.requireDetail}]]></memo>
            </record>
            #end
        </item>
	    #end

	    #if ($newSipnos && $newSipnos.size() > 0) 
	    <item>
            <reqtype>G</reqtype>
            #foreach($v in $newSipnos)
            <record>
                <staff_no>${v.empNo}</staff_no>
                <staff_name>${v.empName}</staff_name>
                <address>${v.requireLocation}</address>
                <memo><![CDATA[${v.requireDetail}]]></memo>
            </record>
            #end
        </item>
	    #end

       <!--D:分機號碼遷移-->
        #if ($transTelephones && $transTelephones.size() > 0) 
	    <item>
            <reqtype>D</reqtype>
            #foreach($v in $transTelephones) 
            <record>
                <phoneno>${v.transNo}</phoneno>
                <staff_no>${v.empNo}</staff_no>
                <staff_name>${v.empName}</staff_name>
                <old_address>${v.oldLocation}</old_address>
                <address>${v.requireLocation}</address>
                <memo><![CDATA[${v.requireDetail}]]></memo>
            </record>
            #end
       </item>
	    #end
       <!--H:新增思科IP Phone號碼-->
        #if ($newCiscos && $newCiscos.size() > 0) 
	    <item>
            <reqtype>H</reqtype>
            #foreach($v in $newCiscos) 
            <record>
                <staff_no>${v.empNo}</staff_no>
                <staff_name>${v.empName}</staff_name>
                <phonetype>${v.model}</phonetype>
                <phonesn>${v.imei}</phonesn>
                <phonemacid>${v.macAddress}</phonemacid>
                <address>${v.requireLocation}</address>
                <memo><![CDATA[${v.requireDetail}]]></memo>
            </record>
             #end
       </item>
	    #end
       <!--I:思科號碼更換話機 L:思科號碼并機-->
       #if ($transCiscos && $transCiscos.size() > 0) 
	   <item>
            <reqtype>I</reqtype>
            #foreach($v in $transCiscos)
            <record>
                <phoneno>${v.ipPhoneNo}</phoneno>
                <staff_no>${v.empNo}</staff_no>
                <staff_name>${v.empName}</staff_name>
                <phonetype>${v.model}</phonetype>
                <phonesn>${v.imei}</phonesn>
                <phonemacid>${v.macAddress}</phonemacid>
                <memo><![CDATA[${v.requireDetail}]]></memo>
            </record>
            #end
       </item>
       #end 

       #if ($mergeCiscos && $mergeCiscos.size() > 0) 
	   <item>
            <reqtype>L</reqtype>
            #foreach($v in $mergeCiscos)
            <record>
                <phoneno>${v.ipPhoneNo}</phoneno>
                <staff_no>${v.empNo}</staff_no>
                <staff_name>${v.empName}</staff_name>
                <phonetype>${v.model}</phonetype>
                <phonesn>${v.imei}</phonesn>
                <phonemacid>${v.macAddress}</phonemacid>
                <memo><![CDATA[${v.requireDetail}]]></memo>
            </record>
            #end
       </item>
       #end

       <!--P:遷移直線電話-->
        #if ($transLines && $transLines.size() > 0) 
            <item>
                <reqtype>P</reqtype>
                #foreach($v in $transLines)
                    <record>
                        <phoneno>${v.transNo}</phoneno>
                        <old_address>${v.oldLocation}</old_address>
                        <address>${v.newLocation}</address>
                        <memo><![CDATA[${v.requireDetail}]]></memo>
                    </record>
                #end
            </item>
        #end
       <!--R:話機功能設定 -->
       #if($!wfcommnetProcessEntity.displayNo && $!wfcommnetProcessEntity.displayNo != "" && $!wfcommnetProcessEntity.functionSet && $!wfcommnetProcessEntity.functionSet != "")
       <item>
            <reqtype>R</reqtype>
           #if(${wfcommnetProcessEntity.functionSet} == '1')
               <func_type>A</func_type>
           #elseif(${wfcommnetProcessEntity.functionSet} == '2')
               <func_type>B</func_type>
           #elseif(${wfcommnetProcessEntity.functionSet} == '3')
               <func_type>C</func_type>
           #else
               <func_type>D</func_type>
           #end
            <req_content>${wfcommnetProcessEntity.displayNo}</req_content>
            <numberamount>${wfcommnetProcessEntity.displayNum}</numberamount>
           #if(${wfcommnetProcessEntity.functionSet} == '4')
               <phonetype>${wfcommnetProcessEntity.displayType}</phonetype>
           #end
            <memo><![CDATA[${wfcommnetProcessEntity.displayDetail}]]></memo>
       </item>
       #end
    </info>
    <!--附件信息（多個請寫多個attach）-->
    #if ($attachList && $attachList.size() > 0) 
	   #foreach($v in $attachList)
	   <attach>
       	<filename><![CDATA[${v.filename}]]></filename>
       	<fileinfo>${v.fileinfo}</fileinfo>
       </attach>
	   #end
    #end
</root>
