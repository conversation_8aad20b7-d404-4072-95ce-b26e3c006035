package ${package}.entity;

        import java.io.Serializable;
        import java.util.Date;
        import javax.persistence.*;

        import com.foxconn.ipebg.common.entity.DataEntity;
        import com.fasterxml.jackson.annotation.JsonFormat;
        import org.hibernate.annotations.DynamicInsert;
        import org.hibernate.annotations.DynamicUpdate;
#if(${hasBigDecimal})
        import java.math.BigDecimal;
#end


/**
 * ${comments}
 *
 * <AUTHOR>
 * @email ${email}
 * @date ${datetime}
 */
@Entity
@Table(name = "${tableName}")
@DynamicUpdate
@DynamicInsert
public class ${className}Entity extends DataEntity<${className}Entity> implements Serializable {
    private static final long serialVersionUID = 1L;

    #foreach ($column in $columns)
        #if($column.attrname != 'id'&&$column.attrname != 'createBy'&&$column.attrname !=
            'createDate'&&$column.attrname != 'updateBy'&&$column.attrname != 'updateDate'&&$column.attrname !=
            'delFlag')
            //$column.comments
            private $column.attrType $column.attrname;
        #end
    #end
    //審核人，不和數據庫欄位對應
    private String auditUser;
    //審核人名稱，不和數據庫欄位對應
    private String nodeName;
    //審核進度，不和數據庫欄位對應
    private String auditProgress;
    //審核人郵箱，不和數據庫欄位對應
    private String userEmail;
    //審核人聯繫方式，不和數據庫欄位對應
    private String userPhone;
    @Transient
    public String getNodeName() {
        return nodeName;
    }
    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }
    @Transient
    public String getAuditUser() {
        return auditUser;
    }
    public void setAuditUser(String auditUser) {
        this.auditUser = auditUser;
    }
    @Transient
    public String getAuditProgress() {
        return auditProgress;
    }
    public void setAuditProgress(String auditProgress) {
        this.auditProgress = auditProgress;
    }
    @Transient
    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }
    @Transient
    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }
    #foreach ($column in $columns)
        #if($column.attrname != 'id'&&$column.attrname != 'createBy'&&$column.attrname !=
            'createDate'&&$column.attrname != 'updateBy'&&$column.attrname != 'updateDate'&&$column.attrname !=
            'delFlag')
            /**
             * 设置：${column.comments}
             */
            public void set${column.attrName}($column.attrType $column.attrname) {
                this.$column.attrname = $column.attrname;
            }

        /**
         * 获取：${column.comments}
         */
        #*#if($column.columnName == $pk.columnName)
        @Id
        @SequenceGenerator(name = "generator", sequenceName = "${tableName}_SEQUENCE", allocationSize = 1)
        @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "generator")
        @Column(name = "${pk.columnName}", unique = true, nullable = false)
        public $column.attrType get${column.attrName}() {
            return ${column.attrname};
        }
        #else*#
            #if($column.attrType=='Date')
                @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
                @Column(name = "${column.columnName}", nullable = false, length = 20)
                public $column.attrType get${column.attrName}() {
                return ${column.attrname};
            }
            #elseif($column.attrType=='CLOB'||$column.attrType=='BLOB')
##                @Lob
                @Basic(fetch = FetchType.EAGER)
                @Column(name = "${column.columnName}", nullable = false, length = 20)
                public $column.attrType get${column.attrName}() {
                    return ${column.attrname};
                }
            #else
                @Column(name = "${column.columnName}", nullable = false, length = 20)
                public $column.attrType get${column.attrName}() {
                    return ${column.attrname};
                }
            #end
    #end

#end
}
