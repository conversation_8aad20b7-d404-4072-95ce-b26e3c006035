package ${package}.controller;

        import java.util.List;
        import java.util.Map;

        import javax.servlet.http.HttpServletRequest;
        import javax.servlet.http.HttpServletResponse;
        import javax.servlet.http.HttpSession;
        import javax.validation.Valid;
        import java.util.ArrayList;
        import java.util.LinkedHashMap;
        import java.util.List;
        import java.util.Map;

        import org.apache.shiro.authz.annotation.RequiresPermissions;
        import org.springframework.beans.factory.annotation.Autowired;
        import org.springframework.stereotype.Controller;
        import org.springframework.ui.Model;
        import org.springframework.web.bind.annotation.*;

        import com.foxconn.ipebg.common.utils.StringUtils;
        import com.foxconn.ipebg.common.utils.Constant;
        import com.foxconn.ipebg.common.utils.ConvertUtils;
        import com.foxconn.ipebg.common.persistence.Page;
        import com.foxconn.ipebg.common.persistence.PropertyFilter;
        import com.foxconn.ipebg.common.web.BaseController;
        import com.foxconn.ipebg.buessness.workflow.entity.WfConifgEntity;
        import ${package}.entity.${className}Entity;
        import ${package}.service.${className}Service;
        import com.foxconn.ipebg.system.utils.ExcelUtil;
        import com.foxconn.ipebg.system.utils.UserUtil;
        import com.foxconn.ipebg.buessness.other.dto.FormQueryDto;
        import com.foxconn.ipebg.buessness.workflow.entity.TaskNode;
        import com.foxconn.ipebg.system.entity.Dict;
        import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
        import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
        import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;
        import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
        import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
        import com.foxconn.ipebg.system.service.OrganizationService;
        import com.foxconn.ipebg.system.service.DictService;
        import com.foxconn.ipebg.system.service.TPubFileobjectService;


/**
 * ${comments}
 *
 * <AUTHOR>
 * @email ${email}
 * @date ${datetime}
 */
@Controller
@RequestMapping("${pathName}" )
public class ${className}Controller extends BaseController {
    /**
     * 方法描述: 導入需要的service
     * @Author: ${author}
     * @CreateDate:   ${datetime}
     * @Return
     **/
    @Autowired
    private ${className}Service ${classname}Service;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private DictService dictService;
    @Autowired
    private WorkFlowService flowService;
    @Autowired
    private TQhAllRelationService allRelationService;
    @Autowired
    private TPubFileobjectService tPubFileobjectService;
    @Autowired
    private WfNodeinfoService wfNodeinfoService;

    public static String workFlowId = "${workFlowId}" ;
    private static final String filePath = "/static/resources/download" ;

    /**
     * 方法描述: 列表信息
     * @Author: ${author}
     * @CreateDate:   ${datetime}
     * @Return
     **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("${module}:${pathName}:list")
    public String list() {
        //查询列表数据
        return "buessness/${module}/${pathName}/list" ;
    }

    /**
     * 方法描述:  分頁查詢信息
     * @Author: ${author}
     * @CreateDate:   ${datetime}
     * @Return
     **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("${module}:${pathName}:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<${className}Entity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        //查詢權限begin
        List<String> deptList = organizationService.getUserAllDeptPermision();
        if (deptList != null && deptList.size() > 0) {
            if (!deptList.contains("-1" )) {
                PropertyFilter filter = new PropertyFilter("INS_makerdeptno", deptList);
                filters.add(filter);
                #foreach ($column in $columns)
                    #if($column.attrname.equals('dealfactoryid')){
                        //PropertyFilter filter_factroy = new PropertyFilter("EQS_makerfactoryid_OR_dealfactoryid", UserUtil.getCurrentUserMoreInfo().getFactoryid());
                        //filters.add(filter_factroy);
                    }
                    #elseif($column.attrname.equals('applyfactoryid')){
                        //PropertyFilter filter_factroy = new PropertyFilter("EQS_makerfactoryid_OR_applyfactoryid", UserUtil.getCurrentUserMoreInfo().getFactoryid());
                        //filters.add(filter_factroy);
                    }
                    #end
                #end
            }
        } else {
            #foreach ($column in $columns)
                #if($column.attrname.equals('applyno')){
                    PropertyFilter filter = new PropertyFilter("EQS_makerno_OR_applyno", UserUtil.getCurrentUser().getLoginName());
                    filters.add(filter);
                }
                #elseif($column.attrname.equals('dealno')){
                    PropertyFilter filter = new PropertyFilter("EQS_makerno_OR_dealno", UserUtil.getCurrentUser().getLoginName());
                    filters.add(filter);
                }
                #end
            #end
        }
        //查詢權限end
##        page.setOrderBy("createDate" );
##        page.setOrder("desc" );
        page = ${classname}Service.search(page, filters);

        //審核狀態替換為字典值
        ConvertUtils.convertPropertyToDictLabel(page, "workstatus", "audit_status" );
        String[] auditInfo = {"nodeName", "auditUser", "auditProgress","userEmail","userPhone"};
        ConvertUtils.convertPropertyAuditInfo(page, auditInfo, "serialno" );

        return getEasyUIData(page);
    }

    /**
     * 方法描述: 保存
     * @Author: ${author}
     * @CreateDate:   ${datetime}
     * @Return
     **/

    @RequestMapping(value = "create/{flag}", method = RequestMethod.POST)
    //@RequiresPermissions("${module}:${pathName}:add")
    @ResponseBody
    public String create(@Valid ${className}Entity ${classname}, Model model,
                         @RequestParam(value = "ids", required = false) String ids,
                         @PathVariable("flag" ) String flag)  throws Exception {
    ##        ${classname}.setNewRecord(true);
    ##        ${classname}Service.save(${classname});
        if (ids != null) {
            ${classname}.setId(ids);
        }
        if (StringUtils.isBlank(${classname}.getMakerno())){
            ${classname}.setMakerno(UserUtil.getCurrentUser().getLoginName());
            ${classname}.setMakername(UserUtil.getCurrentUser().getName());
            ${classname}.setMakerdeptno(UserUtil.getCurrentUserMoreInfo().getDeptno());
            ${classname}.setMakerfactoryid(UserUtil.getCurrentUserMoreInfo().getFactoryid());
        }
        String result = ${classname}Service.startProcess(${classname}, flag, workFlowId);
        if (result.equals(Constant.RESULT.CODE_YES.getValue())) {
            return Constant.SUCCESS;
        } else {
            return Constant.FAIL;
        }
    }

    /**
     * 審核頁面
     *
     * @Author: ${author}
     * @CreateDate:   ${datetime}
     * @Return
     */
    @RequestMapping(value = "audit/{serialno}", method = RequestMethod.GET)
    public String auditForm(@PathVariable("serialno" ) String serialno, Model model) {
            ${className}Entity entityB = ${classname}Service.findBySerialno(serialno);
        model.addAttribute("${classname}Entity", entityB);
        TQhAllRelationEntity entity = allRelationService.queryByEntity(serialno);
        model.addAttribute("processId", entity.getProcessid());
        model.addAttribute("chargeNodeInfo", flowService.getChargeNodeInfoByVersion(entityB, entity));
        model.addAttribute("file", tPubFileobjectService.findByIds(entityB.getAttachids()));
        model.addAttribute("nodeName", flowService.getNodeName(entity.getProcessid()));
        return "buessness/${module}/${pathName}/listFormAudit" ;
    }

    /**
     * 詳細查詢頁面
     *
     * @Author: ${author}
     * @CreateDate:   ${datetime}
     * @Return
     */
    @RequestMapping(value = "view/{serialno}", method = RequestMethod.GET)
    public String viewForm(@PathVariable("serialno" ) String serialno, Model model) {
            ${className}Entity entityB = ${classname}Service.findBySerialno(serialno);
        model.addAttribute("${classname}Entity", entityB);
        TQhAllRelationEntity entity = allRelationService.queryByEntity(serialno);
        model.addAttribute("processId", entity.getProcessid());
        model.addAttribute("chargeNodeInfo", flowService.getChargeNodeInfoByVersion(entityB, entity));
        model.addAttribute("file", tPubFileobjectService.findByIds(entityB.getAttachids()));
        return "buessness/${module}/${pathName}/listFormView" ;
    }

    /**
     * 駁回頁面
     *
     * @Author: ${author}
     * @CreateDate:   ${datetime}
     * @Return
     */
    @RequestMapping(value = "reject/{serialno}", method = RequestMethod.GET)
    public String rejectDetail(@PathVariable("serialno" ) String serialno, Model model) {
            ${className}Entity entityA = ${classname}Service.findBySerialno(serialno);
        TQhAllRelationEntity entity = allRelationService.queryByEntity(serialno);
        flowService.setBeanAutorBlank(entityA, entity.getWorkflowid());
        model.addAttribute("${classname}Entity", entityA);
        model.addAttribute("requiredMap", wfNodeinfoService.getRequiredMap(workFlowId));
        model.addAttribute("processId", entity.getProcessid());
        model.addAttribute("file", tPubFileobjectService.findByIds(entityA.getAttachids()));
        return "buessness/${module}/${pathName}/listFormReject" ;
    }

    /**
     * 零時保存
     *
     * @Author: ${author}
     * @CreateDate:   ${datetime}
     * @Return
     */
    @RequestMapping(value = "create/{serialno}", method = RequestMethod.GET)
    public String reCreateForm(@PathVariable("serialno" ) String serialno, Model model) {
            ${className}Entity entityA = ${classname}Service.findBySerialno(serialno);
        TQhAllRelationEntity entity = allRelationService.queryByEntity(serialno);
        flowService.setBeanAutorBlank(entityA, entity.getWorkflowid());
        model.addAttribute("${classname}Entity", entityA);
        model.addAttribute("requiredMap", wfNodeinfoService.getRequiredMap(workFlowId));
        model.addAttribute("processId", entity.getProcessid());
        model.addAttribute("file", tPubFileobjectService.findByIds(entityA.getAttachids()));
        return "buessness/${module}/${pathName}/listForm" ;
    }

    /**
     * 方法描述: 添加跳轉
     * @Author: ${author}
     * @CreateDate:   ${datetime}
     * @Return
     **/

    //@RequiresPermissions("${module}:${pathName}:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("${classname}Entity", new ${className}Entity());
        model.addAttribute("action", "create" );
        model.addAttribute("requiredMap", wfNodeinfoService.getRequiredMap(workFlowId));
        return "buessness/${module}/${pathName}/listForm" ;
    }

    /**
     * 方法描述: 根據主鍵刪除
     * @Author: ${author}
     * @CreateDate:   ${datetime}
     * @Return
     **/

    @RequestMapping("delete/{${pk.attrname}}" )
    //@RequiresPermissions("${module}:${pathName}:delete")
    @ResponseBody
    public String delete(@PathVariable("${pk.attrname}" ) ${pk.attrType} ${pk.attrname}) {
            ${classname}Service.delete(${pk.attrname});
        return "success" ;
    }

    /**
     * 导出excel
     * @Author: ${author}
     * @CreateDate:   ${datetime}
     * @Return
     **/
    @RequestMapping("exportExcel" )
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {

        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        //查詢權限begin
        List<String> deptList = organizationService.getUserAllDeptPermision();
        if (deptList != null && deptList.size() > 0) {
            if (!deptList.contains("-1" )) {
                PropertyFilter filter = new PropertyFilter("INS_makerdeptno", deptList);
                filters.add(filter);
                #foreach ($column in $columns)
                    #if($column.attrname.equals('dealfactoryid')){
                        //PropertyFilter filter_factroy = new PropertyFilter("EQS_makerfactoryid_OR_dealfactoryid", UserUtil.getCurrentUserMoreInfo().getFactoryid());
                        //filters.add(filter_factroy);
                    }
                    #elseif($column.attrname.equals('applyfactoryid')){
                        //PropertyFilter filter_factroy = new PropertyFilter("EQS_makerfactoryid_OR_applyfactoryid", UserUtil.getCurrentUserMoreInfo().getFactoryid());
                        //filters.add(filter_factroy);
                    }
                    #end
                #end
            }
        } else {
            #foreach ($column in $columns)
                #if($column.attrname.equals('applyno')){
                    PropertyFilter filter = new PropertyFilter("EQS_makerno_OR_applyno", UserUtil.getCurrentUser().getLoginName());
                    filters.add(filter);
                }
                #elseif($column.attrname.equals('dealno')){
                    PropertyFilter filter = new PropertyFilter("EQS_makerno_OR_dealno", UserUtil.getCurrentUser().getLoginName());
                    filters.add(filter);
                }
                #end
            #end
        }

        Page<${className}Entity> page = getPage(request);
        page.orderBy("createDate" );
        page.order("desc" );
        page = ${classname}Service.search(page, filters);
        List<${className}Entity> list = page.getResult();
        ConvertUtils.convertPropertyToDictLabel(page, "workstatus", "audit_status" );
        String[] auditInfo = {"nodeName", "auditUser","userEmail","userPhone"};
        ConvertUtils.convertPropertyAuditInfo(page, auditInfo, "serialno" );
        ConvertUtils.confertDateToStanded(list);//日期格式化
        if (list.size() > 0) {
            //導出excel2007
            LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();
            fieldMap.put("serialno", "任務編號" );
            #foreach ($column in $columns)
                #if($column.attrname.equals('applyno'))
                    fieldMap.put("applyno", "承辦人工號" );
                #elseif($column.attrname.equals('applyname'))
                    fieldMap.put("applyname", "承辦人名稱" );
                #elseif($column.attrname.equals('applydeptno'))
                    fieldMap.put("applydeptno", "承辦人單位代碼" );
                #elseif($column.attrname.equals('applydeptname'))
                    fieldMap.put("applydeptname", "承辦人單位名稱" );
                #elseif($column.attrname.equals('dealno'))
                    fieldMap.put("dealno", "承辦人工號" );
                #elseif($column.attrname.equals('dealname'))
                    fieldMap.put("dealname", "承辦人名稱" );
                #elseif($column.attrname.equals('dealdeptno'))
                    fieldMap.put("dealdeptno", "承辦人單位代碼" );
                #elseif($column.attrname.equals('dealdeptname'))
                    fieldMap.put("dealdeptname", "承辦人單位名稱" );
                #end
            #end
//            fieldMap.put("queryEntity.filename", "文件名稱");
            fieldMap.put("workstatus", "表單狀態" );
            fieldMap.put("nodeName", "當前簽核節點" );
            fieldMap.put("auditUser", "當前簽核人" );
            fieldMap.put("complettime", "簽核完成時間" );

            WfConifgEntity entity = flowService.findByWorkFlowId(workFlowId);
            String name = entity.getWorkflowname();
            ExcelUtil.listToExcel(list, fieldMap, name, response);

        }
    }
}
