#set($jquery="$")
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>${comments}</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/${pathName}/${action}" method="post">
          <!--
		    #foreach($column in $columns)
    ${column.attrname} ${column.comments}
#end
			-->
    <input id="ids" name="ids" type="hidden" value="${${classname}Entity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${${classname}Entity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${${classname}Entity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${${classname}Entity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${${classname}Entity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${${classname}Entity.makerfactoryid }"/>
    <div class="commonW">
    <div class="headTitle">${comments}</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${${classname}Entity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${${classname}Entity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${${classname}Entity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <fmt:formatDate value='${${classname}Entity.createtime}' pattern='yyyy-MM-dd HH:mm'/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${jquery}{empty ${classname}Entity.makerno}">
			  <div class="position_R margin_R">填單人：<span style="color:#999;">${user.loginName}/${user.name}</span></div>
        </c:if>
        <c:if test="${jquery}{not empty ${classname}Entity.makerno}">
                <div class="position_R margin_R">填單人：<span style="color:#999;">${${classname}Entity.makerno}/${${classname}Entity.makername}</span></div>
        </c:if>
        <div class="clear"></div>
		<table class="formList">
		   <tr>
                <td>
                    <table class="formList">
                    </table>
                </td>
           </tr>
		   <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件<font color="red">*</font></td>
                            <td width="90%" class="td_style1">
                                <span class="sl-custom-file"> <input type="button" value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file" onchange="uploadFile();" class="ui-input-file"/>
								</span> <input type="hidden" id="attachids" name="attachids" value="${${classname}Entity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/newEsign/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="delAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)" onclick="showWfImag('','${comments}');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
            <tr>
                <td colspan="10" style="text-align:left;">
                    <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                            #foreach ($nodeinfo in $nodeinfos)
    #if($velocityCount%5==1||$velocityCount==1)
	                       <tr>
                            <td style="border:none">
                            #end
    #if($nodeinfo.signtype.equals('0'))
                                <table width="18%" style="float: left;margin-left: 5px;" id="${nodeinfo.colname.replace('no','')}Table">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${nodeinfo.nodename}</td>
                                                    <td style="border: none;">
                                                    #if($nodeinfo.dynfield01&&$nodeinfo.dynfield01.equals('0'))
                                                        <div class="float_L qhUserIcon" onclick="selectRole($('#dealdeptno').val(),'${nodeinfo.colname}','${nodeinfo.colname.replace( 'no','name')}',$('#dealfactoryid').val())"></div>
                                                     #elseif($nodeinfo.dynfield01&&$nodeinfo.dynfield01.equals('1'))
                                                             <div class="float_L qhUserIcon" onclick="selectRole4(100000,'${nodeinfo.colname}','${nodeinfo.colname.replace( 'no','name')}',$('#dealfactoryid').val())"></div>
                                                     #else
                                                             <div class="float_L qhUserIcon" onclick="selectRole($('#dealdeptno').val(),'${nodeinfo.colname}','${nodeinfo.colname.replace( 'no','name')}',$('#dealfactoryid').val())"></div>
                                                    #end
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    #elseif($nodeinfo.signtype.equals('1'))
                                    <table width="18%" style="float: left;margin-left: 5px;"  id="${nodeinfo.colname.replace( 'no','')}Table">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${nodeinfo.nodename}</td>
                                                    <td style="border: none;">
                                                     #if($nodeinfo.dynfield01&&$nodeinfo.dynfield01.equals('0'))
                                                        <div class="float_L qhUserIcon" onclick="selectRole3(100000,'${nodeinfo.colname.replace( 'no','')}Table','${nodeinfo.colname}','${nodeinfo.colname.replace('no','name')}',$('#dealfactoryid').val(),null)"></div>
                                                     #elseif($nodeinfo.dynfield01&&$nodeinfo.dynfield01.equals('1'))
                                                             <div class="float_L qhUserIcon" onclick="selectRole2(100000,'${nodeinfo.colname.replace( 'no','')}Table','${nodeinfo.colname}','${nodeinfo.colname.replace('no','name')}',$('#dealfactoryid').val(),null)"></div>
                                                     #else
                                                             <div class="float_L qhUserIcon" onclick="selectRole2(100000,'${nodeinfo.colname.replace( 'no','')}Table','${nodeinfo.colname}','${nodeinfo.colname.replace('no','name')}',$('#dealfactoryid').val(),null)"></div>
                                                    #end</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    #end
                                    <tr>
##                                    #if($nodeinfo.required&&$nodeinfo.required.equals('true'))
                                        <td><input id="${nodeinfo.colname}" name="${nodeinfo.colname}" class="easyui-validatebox" data-options="width:80,required:${jquery}{requiredMap['${nodeinfo.colname}']}" readonly
                                              value="${${classname}Entity.${nodeinfo.colname} }"/><c:if test="${jquery}{requiredMap['${nodeinfo.colname}'].equals('true')}"><font color="red">*</font></c:if>
                                             /<input id="${nodeinfo.colname.replace( 'no','name')}" name="${nodeinfo.colname.replace('no','name')}"
                                              readonly class="easyui-validatebox"
                                              data-options="width:80,required:${jquery}{requiredMap['${nodeinfo.colname}']}"
                                              value="${${classname}Entity.${nodeinfo.colname.replace('no','name')} }"/>
                                        </td>
##                                        #elseif($nodeinfo.required&&$nodeinfo.required.equals('false'))
##                                        <td><input id="${nodeinfo.colname}" name="${nodeinfo.colname}"
##                                                   class="easyui-validatebox" data-options="width:80"
##                                                   readonly
##                                                   value="${${classname}Entity.${nodeinfo.colname} }"/>
##                                            /<input id="${nodeinfo.colname.replace('no','name')}" name="${nodeinfo.colname.replace('no','name')}"
##                                                    readonly class="easyui-validatebox"
##                                                    data-options="width:80"
##                                                    value="${${classname}Entity.${nodeinfo.colname.replace('no','name')} }"/>
##                                        </td>#else
##                                        <td><input id="${nodeinfo.colname}" name="${nodeinfo.colname}"
##                                                   class="easyui-validatebox" data-options="width:80"
##                                                   readonly
##                                                   value="${${classname}Entity.${nodeinfo.colname} }"/>
##                                            /<input id="${nodeinfo.colname.replace('no','name')}" name="${nodeinfo.colname.replace('no','name')}"
##                                                    readonly class="easyui-validatebox"
##                                                    data-options="width:80"
##                                                    value="${${classname}Entity.${nodeinfo.colname.replace('no','name')} }"/>
##                                        </td>
##                                        #end
##
                                    </tr>
                                </table>

                                #if($velocityCount%5==0||$velocityCount == $nodeinfos.size())
	                       </td>
                        </tr>
                            #end
#end

                    </table>
                </td>
            </tr>

		    <tr>
                <td colspan="10" style="text-align:left;">
                    <table class="flowList" style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                        <tr>
                            <td>簽核時間</td>
                            <td>簽核節點</td>
                            <td>簽核主管</td>
                            <td>簽核意見</td>
                            <td>批註</td>
                            <td>簽核電腦IP</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                    <a href="javascript:;" id="btnSave" class="easyui-linkbutton" data-options="iconCls:'icon-add'"
                       style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                    <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                       style="width: 100px;" onclick="saveInfo(2);">提交</a>
                </td>
            </tr>
        </table>
        </td>
        </tr>
        </table>
	</div>
	<input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
	</form>
  </div>
<script src='${ctx}/static/js/${module}/${pathName}.js?random=<%= Math.random()%>'></script>
</body>
</html>