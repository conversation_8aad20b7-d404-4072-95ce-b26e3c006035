var dg;
var d;
#set($jquery="$")
$(function(){
    /*${jquery}.ajax({
        url : ctx + "/system/dict/getDictByType/audit_status",
        dataType:"json",
        type : "GET",
        success : function(data) {
            //绑定第一个下拉框
            ${jquery}("#qysjzt").combobox({
                data : data,
                valueField : "value",
                textField : "label",
                editable : false,
                panelHeight : 350,
                loadFilter : function(data) {
                    data.unshift({
                        value : '',
                        label : '請選擇審核狀態'
                    });
                    return data;
                }
            });
        },
        error : function(error) {
            alert("初始化下拉控件失败");
        }
    });*/
    //獲取廠區
    /*${jquery}.get(ctx + '/tqhfactoryidconfig/allFactorys/', function(result) {
        ${jquery}("#dealfactoryid").combobox({
            data : result,
            valueField : "factoryid",
            textField : "factoryname",
            editable : false,
            panelHeight : 350,
            loadFilter : function(data) {
                data.unshift({
                    factoryid : '',
                    factoryname : '請選擇廠區'
                });
                return data;
            }
        });
    },"json");*/
});

//弹窗增加
function add() {
    d=$("#dlg").dialog({
        title: '添加${comments}',
        width: 380,
        height: 380,
        href:ctx+'/${pathName}/create',
        maximizable:true,
        modal:true,
        buttons:[{
            text:'确认',
            handler:function(){
                $("#mainform").submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}
//保存
function saveInfo(flag) {
    if (flag == '2') {
        var isValid = $("#mainform").form('validate');
        if (!isValid) {
            return false;
        }
    }
    ${jquery}.ajax({
        url: ctx + '/${pathName}/create/' + flag,
        type: 'POST',
        beforeSend: ajaxLoading,
        // dataType: 'json',
        data: $('#mainform').serialize(),
        success: function (data) {
            ajaxLoadEnd();
            if(data=='success') {
                successTip(data, dg, d);
                window.parent.mainpage.mainTabs.closeCurrentTab();
            }else {
                parent.${jquery}.messager.alert("稍後重試，如重複出現，請聯繫管理員");
            }
        }
    });
}
//删除
function del(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    parent.${jquery}.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data){
        if (data){
	${jquery}.ajax({
                type:'get',
                url:ctx+"/${pathName}/delete/"+row.${pk.attrname},
                success: function(data){
                    successTip(data,dg);
                }
            },'text');
        }
    });
}

//弹窗修改
function upd(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    d=$("#dlg").dialog({
        title: '修改${comments}',
        width: 380,
        height: 340,
        href:ctx+'/${pathName}/update/'+row.${pk.attrname},
        maximizable:true,
        modal:true,
        buttons:[{
            text:'修改',
            handler:function(){
                $('#mainform').submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}