package ${package}.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.lang.Assert;
import ${package}.entity.${className}Entity;
import ${package}.dao.${className}Dao;
import com.foxconn.ipebg.buessness.common.service.UserServiceRedisUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.buessness.common.service.TProxyUserinfoService;
import com.foxconn.ipebg.buessness.generalAffairs.dto.ProxTaskInfo;
import com.foxconn.ipebg.common.exception.BusinessException;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.common.utils.ResultEnum;
import com.foxconn.ipebg.system.utils.MyBeanUtils;
import com.foxconn.ipebg.system.utils.UserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfConifgEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.util.Date;
import java.util.List;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @email ${email}
 * @date ${datetime}
 */
@Service
@Transactional(readOnly=true)
public class  ${className}Service extends BaseBusinessService<${className}Entity, ${pk.attrType}>{
    @Autowired
    private ${className}Dao ${classname}Dao;
    @Autowired
    private WorkFlowService flowService;
    @Autowired
    private WfNodeinfoService wfNodeinfoService;
    @Autowired
    private WfNoderuleService noderuleService;
    @Autowired
    private WfConifgService conifgService;
    @Autowired
    private TQhAllRelationService allRelationService;
    @Autowired
    private UserServiceRedisUtil serviceUtil;
    @Autowired
    private DictService dictService;
    @Autowired
    private TProxyUserinfoService tProxyUserinfoService;

    @Override
    public HibernateDao<${className}Entity, ${pk.attrType}> getEntityDao() {
        return ${classname}Dao;
    }

    public ${className}Entity findBySerialno(String serialno) {
        return this.${classname}Dao.findUniqueBy("serialno", serialno);
    }
    /**
      * 方法描述: 啟動流程
      * @Author: ${author}
      * @CreateDate:   ${datetime}
      * @Return
      **/

    @Transactional(rollbackFor=Exception.class)
    public String startProcess(${className}Entity ${classname}, String flag, String workFlowId) {
        try {
            //檢驗填單人部門欄位必須存在，數據權限控制，切記必須有
            Assert.isTrue(MyBeanUtils.checkIfExitMakerdeptno(${className}Entity.class,"makerdeptno"),"填單人部門代碼必須填寫，請在表中創建相關欄位");
            ${classname}.setMakerdeptno(UserUtil.getCurrentUserMoreInfo().getDeptno());
            //自動填充沒有設置的審核人為自動審核
            flowService.setWfAutorBlank(${classname}, workFlowId);
            //設置流水號
            ${classname}.setCreatetime(new Date());
            //保存和臨時保存
            if (flag.equals(Constant.RESULT.CODE_SAVE.getValue())) {
                if (StringUtils.isBlank(${classname}.getId())) {
                    ${classname}.setSerialno(serviceUtil.createSerialno(${classname}.getMakerno()));
                    ${classname}.setNewRecord(true);
                }
                ${classname}.setWorkstatus(Constant.RESULT.CODE_SAVE.getValue());
                this.save(${classname});

                //保存中間表信息
                TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(${classname}.getSerialno());
                if (relationEntity == null) {
                    WfConifgEntity wfConifgEntity = conifgService.findUnique(workFlowId);
                    relationEntity = new TQhAllRelationEntity();
                    relationEntity.setDtoName("${className}Entity");
                    relationEntity.setSerialno(${classname}.getSerialno());
                    relationEntity.setWfName(wfConifgEntity.getWorkflowname());
                    relationEntity.setFormFrom(StrUtil.equals(wfConifgEntity.getIsLocalFlow(),"Y")?"L":"F");
                    relationEntity.setWorkstatus(Constant.RESULT.CODE_SAVE.getValue());
                }
                Dict dict = dictService.getDictByTypeAndVlaue("workflow_code", workFlowId);
                relationEntity.setWorkflowid(workFlowId);
                relationEntity.setVersion(dict.getLabel());
                allRelationService.save(relationEntity);

            } else if (flag.equals(Constant.RESULT.CODE_RUNNING.getValue())) {
                /**
                 * 方法描述: 提交
                 * @Author: S6114648
                 * @CreateDate: 2018/10/23  下午 04:01
                 * @Return
                 **/

                if (StringUtils.isEmpty(${classname}.getSerialno()) || ${classname}.getSerialno() == null) {

                    ${classname}.setNewRecord(true);
                    ${classname}.setSerialno(serviceUtil.createSerialno(${classname}.getMakerno()));
                } else {
                    ${classname}.setNewRecord(false);
                }
                ${classname}.setWorkstatus(Constant.RESULT.CODE_RUNNING.getValue());
                this.save(${classname});
                //流程參數
                WorkFlowEntity entity = new WorkFlowEntity();
                entity.setWorkflowId(workFlowId);
                entity.setSerialNo(${classname}.getSerialno());

                /**
                 * 方法描述: 獲取普通用戶參數
                 * @Author: S6114648
                 **/

                List<WfNodeinfoEntity> infoList = wfNodeinfoService.findForUser(workFlowId);
                String taskUser = "";
                for (WfNodeinfoEntity nodeinfoEntity : infoList) {
                    taskUser += nodeinfoEntity.getNodename() + ":" + Reflections.getFieldValue(${classname}, nodeinfoEntity.getColname()) + ";";
                }
                entity.setTaskUsers(taskUser);

                //獲取會簽節點參數
                List<WfNodeinfoEntity> infoLists = wfNodeinfoService.findForHuiqian(workFlowId);
                String huiqian = "";
                WfNoderuleEntity noderuleEntity = null;
                for (WfNodeinfoEntity list : infoLists) {
                    noderuleEntity = noderuleService.findUniqByNodeId(list.getNodeid(), workFlowId);
                    huiqian += noderuleEntity.getNodeparamname() + ":" + Reflections.getFieldValue(${classname}, list.getColname()) + ";";
                }
                entity.setHuiqian(huiqian);

                String processId = flowService.processStart(entity, ${classname});
	            if (StrUtil.isEmpty(processId)) {
		            throw new BusinessException(ResultEnum.TEST_ERRORR);
	            }
                //創建會簽節點
//                flowService.createHuiqianTaskInfor(entity, processId, tQhWfbuildprojectrocess);
                //防止第一個節點為空  自動推動任務
//                flowService.AutoCompleteTask(processId);
                //保存中間表信息
                TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(${classname}.getSerialno());
                if (relationEntity == null) {
                    relationEntity = new TQhAllRelationEntity();
                    relationEntity.setDtoName("${className}Entity");
                    relationEntity.setSerialno(${classname}.getSerialno());
                }
                WfConifgEntity wfConifgEntity = conifgService.findUnique(workFlowId);
                relationEntity.setWfName(wfConifgEntity.getWorkflowname());
                relationEntity.setFormFrom(StrUtil.equals(wfConifgEntity.getIsLocalFlow(),"Y")?"L":"F");
                Dict dict = dictService.getDictByTypeAndVlaue("workflow_code", workFlowId);
                relationEntity.setWorkflowid(workFlowId);
                relationEntity.setVersion(dict.getLabel());
                relationEntity.setProcessid(processId);
                relationEntity.setWorkstatus(Constant.RESULT.CODE_RUNNING.getValue());
                //添加代理人信息
                ProxTaskInfo taskInfo = tProxyUserinfoService.findNodeName(relationEntity);
                relationEntity.setNodename(taskInfo.getTaskName());
                relationEntity.setDeptNo(${classname}.getMakerdeptno());
                allRelationService.save(relationEntity);
            }
        } catch (Exception e) {
            this.logger.info(e.getMessage(), e);
	        throw e;
        }
        return Constant.RESULT.CODE_YES.getValue();
    }
}
