<?xml version="1.1" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:mvc="http://www.springframework.org/schema/mvc"
	   xmlns:aop="http://www.springframework.org/schema/aop" xmlns:cache="http://www.springframework.org/schema/cache"
	   xsi:schemaLocation="http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-3.2.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.2.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.2.xsd http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache.xsd">

	<aop:aspectj-autoproxy proxy-target-class="true"/>
	<!-- 自动扫描且只扫描@Controller -->
	<context:component-scan base-package="com.foxconn.ipebg" use-default-filters="false">
		<context:include-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
		<context:include-filter type="annotation" expression="org.springframework.web.bind.annotation.ControllerAdvice"/>
	</context:component-scan>

	<mvc:annotation-driven>
		<mvc:message-converters register-defaults="true">
			<!-- 将StringHttpMessageConverter的默认编码设为UTF-8 -->
			<bean class="org.springframework.http.converter.StringHttpMessageConverter">
				<constructor-arg value="UTF-8" />
			</bean>
			<bean class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
				<property name="supportedMediaTypes">
					<list>
						<value>text/html;charset=UTF-8</value>
					</list>
				</property>
			</bean>
		</mvc:message-converters>
	</mvc:annotation-driven>

	<!-- 定义JSP文件的位置 -->
	<bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
		<property name="prefix" value="/WEB-INF/views/"/>
		<property name="suffix" value=".jsp"/>
	</bean>


	<bean class="org.springframework.web.servlet.mvc.annotation.AnnotationMethodHandlerAdapter">
		<property name="messageConverters">
			<list>
				<!--配置下载返回类型-->
				<bean class="org.springframework.http.converter.ByteArrayHttpMessageConverter"/>
				<bean class="org.springframework.http.converter.StringHttpMessageConverter">
					<!--配置编码方式-->
					<property name="supportedMediaTypes" value="application/json; charset=UTF-8" />
				</bean>
			</list>
		</property>
	</bean>
	<bean id="multipartResolver"
		  class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
		<!-- 上传文件大小上限，单位为字节（20MB） -->
		<property name="maxUploadSize">
			<value>20971520</value>
		</property>
		<!-- 请求的编码格式，必须和jSP的pageEncoding属性一致，以便正确读取表单的内容，默认为ISO-8859-1 -->
		<property name="defaultEncoding">
			<value>UTF-8</value>
		</property>
	</bean>
	<!-- 容器默认的DefaultServletHandler处理 所有静态内容与无RequestMapping处理的URL-->
	<mvc:default-servlet-handler/>

	<!-- 定义无需Controller的url<->view直接映射 -->
	<mvc:view-controller path="/admin" view-name="/system/index"/>

	<!-- 将Controller抛出的异常转到特定View, 保持SiteMesh的装饰效果 -->
	<bean class="org.springframework.web.servlet.handler.SimpleMappingExceptionResolver">
		<property name="exceptionMappings">
			<props>
				<prop key="org.apache.shiro.authz.UnauthorizedException">error/403</prop>
				<prop key="java.lang.Throwable">error/500</prop>
			</props>
		</property>
	</bean>

	<!-- 自定义拦截链配置 -->
	<mvc:interceptors>
		<mvc:interceptor>
			<mvc:mapping path="/*/*/create"/>
			<mvc:mapping path="/*/create/*"/>
			<mvc:mapping path="/*/*/delete/*"/>
			<mvc:mapping path="/*/delete/*"/>
			<mvc:mapping path="/*/*/update"/>
			<mvc:mapping path="/*/update"/>
			<mvc:mapping path="/*/completeTask"/>
			<!--<mvc:mapping path="/*/exportExcel"/>-->
			<mvc:mapping path="/*/cancelTask"/>
			<mvc:mapping path="/*/audit/login"/>
			<mvc:mapping path="/admin/upload"/>
			<mvc:mapping path="/admin/uploadCompatible"/>
			<mvc:mapping path="/admin/download/*"/>
			<mvc:mapping path="/admin/fileUpload"/>
			<mvc:mapping path="/watermark/upload"/>
			<mvc:mapping path="/watermark/uploadCompatible"/>
			<mvc:mapping path="/watermark/download/*"/>
			<mvc:mapping path="/admin"/>
			<bean class="com.foxconn.ipebg.system.interceptor.LogInterceptor"></bean>
		</mvc:interceptor>
		<!--點擊率攔截器-->
		<mvc:interceptor>
			<mvc:mapping path="/**"/>
			<mvc:exclude-mapping path="/static/**"/>
			<bean class="com.foxconn.ipebg.system.interceptor.ClickRateInterceptor"></bean>
		</mvc:interceptor>
		
	</mvc:interceptors>
	<!--靜態資源映射-->
	<mvc:resources mapping="/static/**" location="/static/" />

	<!--<cache:annotation-driven cache-manager="springCacheManager" proxy-target-class="false"/>
	<bean id="ehcacheManager" class="org.springframework.cache.ehcache.EhCacheManagerFactoryBean">
		<property name="configLocation" value="classpath:cache/ehcache-hibernate-local.xml"/>
		<property name="cacheManagerName" value="ehcache"/>
	</bean>
	<bean id="springCacheManager" class="org.springframework.cache.ehcache.EhCacheCacheManager">
		<property name="cacheManager" ref="ehcacheManager"/>
	</bean>-->
<!--	<bean id="redisCacheService" class="com.foxconn.ipebg.system.utils.RedisCacheService" ></bean>-->
	<bean id="autoCompleteReceipt" class="com.foxconn.ipebg.buessness.common.service.AutoCompleteReceipt"></bean>

	<bean id="poolTaskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
		<!-- 线程池维护线程的最少数量，默认为1  -->
		<property name="corePoolSize" value="1" />
		<!-- 线程池维护线程的最大数量，默认为Integer.MAX_VALUE -->
		<property name="maxPoolSize" value="10" />
		<!-- 允许的空闲时间，单位/秒，默认60秒 -->
		<property name="keepAliveSeconds" value="30" />
		<!-- 缓存队列个数，默认为Integer.MAX_VALUE -->
		<property name="queueCapacity" value="10" />
		<!-- 对拒绝任务(无线程可用)的处理策略 ThreadPoolExecutor.CallerRunsPolicy策略 ,调用者的线程会执行该任务,如果执行器已关闭,则丢弃.-->
		<property name="rejectedExecutionHandler">
			<bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy" />
		</property>
	</bean>
</beans>
