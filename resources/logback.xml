<?xml version="1.0" encoding="UTF-8"?>
<!--
scan：当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true。
scanPeriod：设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒当scan为true时，此属性生效。默认的时间间隔为1分钟。
debug：当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。
-->
<configuration scanPeriod="60 seconds" debug="false">
    <define name="ip" class="com.foxconn.ipebg.system.utils.LogIpConfig"/>
    <!--配置屬性變量-->
    <!-- magenta:洋红 -->
    <!-- boldMagenta:粗红-->
    <!-- cyan:青色 -->
    <!-- white:白色 -->
    <!-- magenta:洋红 -->
    <!--    "%black", "%red", "%green","%yellow","%blue", "%magenta","%cyan", "%white", "%gray", "%boldRed","%boldGreen", "%boldYellow", "%boldBlue", "%boldMagenta""%boldCyan", "%boldWhite" and "%highlight"-->
    <property name="CONSOLE_LOG_PATTERN"
              value="%gray(%date{yyyy-MM-dd HH:mm:ss}) |%highlight(%-5level) |%gray(%thread) |%boldBlue(%file:%line) |%green(%logger) |%boldWhite(%msg%n)"/>
    <!-- 定义日志的根目录 -->
    <property name="FILE_PATH"
              value="/www/new_esign/tomcat70/logs/log"/>
    <!-- ch.qos.logback.core.ConsoleAppender 表示控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!--
日志输出格式：%d表示日期时间，%thread表示线程名，%-5level：级别从左显示5个字符宽度
%logger{50} 表示logger名字最长50个字符，否则按照句点分割。 %msg：日志消息，%n是换行符
-->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder" charset="UTF-8">
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <!--<charset>UTF-8</charset>-->
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>
    <!--日誌寫入數據庫表-->
    <!--<appender name="db-classic-oracle-pool" class="ch.qos.logback.classic.db.DBAppender">
        <connectionSource class="ch.qos.logback.core.db.DataSourceConnectionSource">
            <dataSource class="org.apache.commons.dbcp.BasicDataSource">
                <driverClassName>oracle.jdbc.driver.OracleDriver</driverClassName>
                <url>*****************************************</url>
                <username>caags</username>
                <password>caags180516</password>
            </dataSource>
        </connectionSource>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>-->
    <!-- 滚动记录文件，先将日志记录到指定文件，当符合某个条件时，将日志记录到其他文件 -->
    <appender name="ROLLING-FILE-1" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--
当发生滚动时，决定 RollingFileAppender 的行为，涉及文件移动和重命名
TimeBasedRollingPolicy： 最常用的滚动策略，它根据时间来制定滚动策略，既负责滚动也负责出发滚动。
-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 指定日志文件的名称 -->
            <fileNamePattern>${FILE_PATH}/${ip}.newEsignLog.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!--
      可选节点，控制保留的归档文件的最大数量，超出数量就删除旧文件。假设设置每天滚动，
      且maxHistory是365，则只保存最近365天的文件，删除之前的旧文件。注意，删除旧文件是，
      那些为了归档而创建的目录也会被删除。
      -->
            <maxHistory>30</maxHistory>
            <!--
            当日志文件超过maxFileSize指定的大小是，根据上面提到的%i进行日志文件滚动 注意此处配置SizeBasedTriggeringPolicy是无法实现按文件大小进行滚动的，必须配置timeBasedFileNamingAndTriggeringPolicy
            -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>20MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <!--
日志输出格式：%d表示日期时间，%thread表示线程名，%-5level：级别从左显示5个字符宽度 %logger{50} 表示logger名字最长50个字符，否则按照句点分割。 %msg：日志消息，%n是换行符
-->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder" charset="UTF-8">
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <!--<charset>UTF-8</charset>-->
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>

    <!-- 异步输出 -->
    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>256</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="ROLLING-FILE-1"/>
    </appender>

    <!--
logger主要用于存放日志对象，也可以定义日志类型、级别
name：表示匹配的logger类型前缀，也就是包的前半部分
level：要记录的日志级别，包括 TRACE < DEBUG < INFO < WARN < ERROR
additivity：作用在于children-logger是否使用 rootLogger配置的appender进行输出，false：表示只用当前logger的appender-ref，true：表示当前logger的appender-ref和rootLogger的appender-ref都有效
-->
    <logger name="org.springframework.core.env" level="ERROR" additivity="false"/>
    <logger name="com.zyd.shiro" level="INFO"/>
    <!--<logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="INFO" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>
    <logger name="org.hibernate.type.descriptor.sql.BasicExtractor" level="INFO" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>
    <logger name="org.hibernate.SQL" level="INFO" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>
    <logger name="org.hibernate.engine.QueryParameters" level="INFO" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>
    <logger name="org.hibernate.engine.query.HQLQueryPlan" level="INFO" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>-->
    <logger name="org.quartz" level="info"></logger>
    <logger name="org.springframework" level="info"></logger>
    <logger name="org.apache.shiro" level="info"></logger>
    <logger name="net.sf" level="info"></logger>
    <logger name="org.redisson.command" level="info"></logger>
    <logger name="org.apache.http.impl.conn.PoolingHttpClientConnectionManager" level="info"></logger>

    <!--
root与logger是父子关系，没有特别定义则默认为root，任何一个类只会和一个logger对应，
要么是定义的logger，要么是root，判断的关键在于找到这个logger，然后判断这个logger的appender和level。
-->
    <root level="DEBUG">
        <!--<root level="INFO">-->
        <appender-ref ref="ASYNC"/>
        <appender-ref ref="STDOUT"/>
        <!--<appender-ref ref="db-classic-oracle-pool"/>-->
    </root>

</configuration>
