###############################################################################
# finder-eye.conf
###############################################################################
## 监控开关
finder.eye.monitor.enabled = true

## 采样频率, 单位秒
finder.eye.monitor.interval = 5

## 磁盘设备名, 多个以逗号分隔
finder.eye.monitor.disk.dev_names =

## cpu负载阀值, 百分比, 取值范围(0 - 100)
finder.eye.monitor.cpu.load_threshold = 70

## 内存使用阀值, 百分比, 取值范围(0 - 100)
finder.eye.monitor.mem.used_threshold = 70

## 网络接收阀值, 字节数, 可使用的单位 b, k, m, g
finder.eye.monitor.net.recv_threshold = 10M

## 网络发送阀值, 字节数, 可使用的单位 b, k, m, g
finder.eye.monitor.net.sent_threshold = 10M

## 磁盘使用阀值, 百分比, 取值范围(0 - 100)
finder.eye.monitor.disk.used_threshold = 80

## 监控预警
## finder的系统监控支持两种方式发送报警数据, 允许同时开启
## 1. http方式, 发送报警数据到指定的URL
## 2. mail方式, 发送报警数据到指定的邮件地址
## 监控预警, http方式, 发送数据的url地址
finder.eye.monitor.action.http_post_url = 

## 监控预警, mail方式, 接收邮件的邮件地址
finder.eye.monitor.action.mail_address =

## 邮件发送间隔
finder.eye.monitor.action.mail_send_period = 10

