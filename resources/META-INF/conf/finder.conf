###############################################################################
# finder.conf
###############################################################################
## 超级管理员的用户名
## ============================================================================
finder.security.root = admin

## 必选项 - Finder内部通讯使用的安全KEY
## 随机生成一个UUID即可, 所有机器必须相同
## ============================================================================
finder.cluster.security.key = b60b960e-df7f-4924-adb2-0abf10c7a6aa

## RSA PublicKey
## ============================================================================
finder.security.public-key = 
finder.security.private-key = 

## 会话配置
## 可选单位: [d: 天, h: 时, m: 分, s: 秒]
## 单位不区分大小写, 无单位或者其他都按照秒处理
## 如果为0表示当前窗口有效, 用户关闭窗口再打开需要重新登录
## 现代浏览器都支持tab页打开, tab页关闭会话仍然有效
## ============================================================================
finder.session.timeout = 0
finder.session.name = passport
finder.session.key = b2175390-b6b9-44f0-8782-5ad6cbe22d49

## 允许使用tail,less,grep功能的文件类型, 英文逗号分隔, *表示全部允许
## 如果文件类型不属于此处定义的文件类型, 界面上的tail等操作将不可用
finder.text.type = log, txt, text, js, css, htm, html, xml, ini, conf

## 大文件上传时使用的分片大小
## 一般服务器都有最大请求体大小限制, 通常情况下可以修改服务器的该设置
## 但是某些环境下可能没有权限修改服务器设置
## 此处设置客户端上传文件的分片大小, 即使在服务器设置较小的情况下也能上传大文件
## 为了上传的可靠性, 即便服务器设置的较大, 此处也不要设置太大, 建议设置为10M为宜
## 如果网络状况不好, 客户端在上传每片出错的时候自动重试三次
## ============================================================================
finder.upload.part-size = 5M

## 显示的操作按钮, 文件列表每项后面的操作按钮, 使用英文逗号分隔
## 显示的文字内容请在国际化配置文件中修改, 例如zh_cn.properties
## 如果此处没有指定某项操作, 即便用户拥有该权限界面上也不会显示对应的操作按钮
## ============================================================================
finder.display.operate-button = tail, less, grep, open, download, delete

## 是否开启访客账号
## ============================================================================
finder.user.guest.enabled = false

## 是否开启文件分享
## ============================================================================
finder.file.share.enabled = false

## 缺省的账号密码, 如果不需要删除该配置即可
## ============================================================================
finder.demo.userName = admin
finder.demo.password = 1234

finder.conf.version = 3
finder.update.check = false
finder.office.api.url = 
