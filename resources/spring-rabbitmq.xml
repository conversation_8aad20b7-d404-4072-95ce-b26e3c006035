<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:rabbit="http://www.springframework.org/schema/rabbit"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
                        http://www.springframework.org/schema/context
                        http://www.springframework.org/schema/context/spring-context-3.0.xsd
                        http://www.springframework.org/schema/rabbit
                        http://www.springframework.org/schema/rabbit/spring-rabbit-1.6.xsd
                        http://www.springframework.org/schema/util
    					http://www.springframework.org/schema/util/spring-util-3.0.xsd">

    <!-- 创建连接工厂 -->
    <rabbit:connection-factory
            id="connectionFactory" virtual-host="${rabbit.virtual-host}"
            username="${rabbit.username}" password="${rabbit.password}"
            host="${rabbit.host}" port="${rabbit.port}" />
<!--    <rabbit:admin connection-factory="connectionFactory" />-->

    <!-- 替换JACKSON为FASTJSON -->
    <!-- <bean id="messageConverter"  class="自定义的转换器"></bean> -->

    <!-- 开启rabbitMQ注解 -->
    <rabbit:annotation-driven />

    <!-- 消息监听 -->
    <bean id="rabbitListenerContainerFactory"
          class="org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory">
        <!-- <property name="messageConverter" ref="messageConverter" /> -->
        <property name="connectionFactory" ref="connectionFactory" />
        <property name="concurrentConsumers" value="3" />
        <property name="maxConcurrentConsumers" value="10" />
    </bean>
</beans>
