<?xml version="1.1" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context" xsi:schemaLocation="
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
		http://www.springframework.org/schema/context  http://www.springframework.org/schema/context/spring-context-3.2.xsd"
	default-lazy-init="true">

	<description><PERSON>ro安全配置</description>

	<!-- 读取配置文件 -->
	<context:property-placeholder ignore-unresolvable="true" location="classpath*:/application.properties" />
	<bean id="userRealm" class="com.foxconn.ipebg.system.service.UserRealm"></bean>
	<!-- <PERSON>ro的主要业务层对象基于web的应用程序 -->
	<bean id="securityManager" class="org.apache.shiro.web.mgt.DefaultWebSecurityManager">
		<property name="realm" ref="userRealm" />
		<property name="cacheManager" ref="shiroEhcacheManager" />
	</bean>

	<!-- Shiro Filter -->
	<bean id="myCaptchaFilter" class="com.foxconn.ipebg.system.utils.FormAuthenticationCaptchaFilter"/>
	<bean id="logout" class="org.apache.shiro.web.filter.authc.LogoutFilter">
		<property name="redirectUrl" value="${adminPath}/login"/>
	</bean>
	<!-- Shiro Filter -->
	<bean id="shiroFilter" class="org.apache.shiro.spring.web.ShiroFilterFactoryBean">
		<property name="securityManager" ref="securityManager" />
		<property name="loginUrl" value="${adminPath}/login" />
		<property name="successUrl" value="${adminPath}" />
		<property name="filters">
		    <map>
		        <entry key="authc" value-ref="myCaptchaFilter"/>
				<entry key="logout" value-ref="logout" />
		    </map>
		</property>
		<property name="filterChainDefinitions">
			<value>
				/static/** = anon
				/finder = anon
				/audit/login = anon
				/thirdLogin/login = anon
				/system/user/updatePwdNew = anon
				/admin/downLoad = anon
				/commendilegalall/list = anon
				/tqhchargepath/downLoad = anon
				/admin/showImg/** = anon
				/logout = logout
				/wfcontroller/listForOld = anon
				${adminPath}/login = authc
				${adminPath}/** = user
				/rest/**=authcBasic
				/wfonlineprocessForMobile/** = anon
				/wfsystemprocessForMobile/** = anon
				/wfcommonForMobile/** = anon
				/wfcontrollerForMobile/** = anon
				/wffileesignprocessForMobie/** = anon
				/esignForMobile/** = anon
				/esignForOtner/** = anon
				/entrfrm/** = anon
				/wffileesignprocessApi/** = anon
				/proxyForMobile/** = anon
				/systemvideo/** = anon
			</value>
		</property>

	</bean>

	<!-- 用户授权信息Cache, 采用EhCache -->
	<bean id="shiroEhcacheManager" class="org.apache.shiro.cache.ehcache.EhCacheManager">
		<property name="cacheManagerConfigFile" value="classpath:cache/ehcache-shiro.xml"/>
	</bean>

	<!-- 保证实现了Shiro内部lifecycle函数的bean执行 -->
	<bean id="lifecycleBeanPostProcessor" class="org.apache.shiro.spring.LifecycleBeanPostProcessor"/>

	<!-- AOP式方法级权限检查  -->
	<bean class="org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator" depends-on="lifecycleBeanPostProcessor">
		<property name="proxyTargetClass" value="true" />
	</bean>
	<bean class="org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor">
    	<property name="securityManager" ref="securityManager"/>
	</bean>
</beans>
