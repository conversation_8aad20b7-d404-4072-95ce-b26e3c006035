{"clusterServersConfig": {"idleConnectionTimeout": 10000, "pingTimeout": 1000, "connectTimeout": 10000, "timeout": 3000, "retryAttempts": 3, "retryInterval": 5000, "reconnectionTimeout": 5000, "failedAttempts": 3, "password": "123456", "subscriptionsPerConnection": 5, "clientName": null, "loadBalancer": {"class": "org.redisson.connection.balancer.RoundRobinLoadBalancer"}, "slaveSubscriptionConnectionMinimumIdleSize": 1, "slaveSubscriptionConnectionPoolSize": 50, "slaveConnectionMinimumIdleSize": 5, "slaveConnectionPoolSize": 64, "masterConnectionMinimumIdleSize": 5, "masterConnectionPoolSize": 64, "readMode": "MASTER_SLAVE", "nodeAddresses": ["redis://*************:6379", "redis://127.0.0.1:6379", "redis://127.0.0.1:7003", "redis://127.0.0.1:7004", "redis://127.0.0.1:7005", "redis://127.0.0.1:7006"], "scanInterval": 5000}, "threads": 0, "codec": {"class": "org.redisson.codec.SnappyCodec"}, "useLinuxNativeEpoll": false}