<?xml version="1.1" encoding="UTF-8"?>
<ehcache updateCheck="false" name="hibernateCache">
	<diskStore path="java.io.tmpdir"/>
	<!-- DefaultCache setting. -->
	<defaultCache
			maxElementsInMemory="100000"
			eternal="false"
			timeToIdleSeconds="1800"
			timeToLiveSeconds="0"
			overflowToDisk="true"
			maxElementsOnDisk="10000000"
			diskPersistent="false"
			diskExpiryThreadIntervalSeconds="120"
			memoryStoreEvictionPolicy="LRU"
	/>
	<!--
            <cache     name 缓存名唯一标识
                       maxElementsInMemory="1000" 内存中最大缓存对象数
                       eternal="false" 是否永久缓存
                       timeToIdleSeconds="3600" 缓存清除时间 默认是0 即永不过期
                       timeToLiveSeconds="0" 缓存存活时间 默认是0 即永不过期
                       overflowToDisk="true" 缓存对象达到最大数后，将其写入硬盘
                       maxElementsOnDisk="10000"  磁盘最大缓存数
                       diskPersistent="false" 磁盘持久化
                       diskExpiryThreadIntervalSeconds="120" 磁盘缓存的清理线程运行间隔
                       memoryStoreEvictionPolicy="FIFO" 缓存清空策略
                       FIFO 先进先出
                       LFU  less frequently used  最少使用
                       LRU  least recently used 最近最少使用
        />
        -->
</ehcache>
