package com.foxconn.ipebg.buessness.information.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.buessness.generalAffairs.dto.ProxTaskInfo;
import com.foxconn.ipebg.buessness.common.service.TProxyUserinfoService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;
import com.foxconn.ipebg.buessness.common.service.UserServiceRedisUtil;
import com.foxconn.ipebg.buessness.information.dao.WffilesignprocessDao;
import com.foxconn.ipebg.buessness.information.dto.FileSignParams;
import com.foxconn.ipebg.buessness.information.entity.SignModelPositionEntity;
import com.foxconn.ipebg.buessness.information.entity.WffilesignprocessEntity;
import com.foxconn.ipebg.buessness.workflow.entity.*;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.common.utils.*;
import com.foxconn.ipebg.system.entity.*;
import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.service.FtpInfoService;
import com.foxconn.ipebg.system.service.TPubMailrecordService;
import com.foxconn.ipebg.system.utils.MyBeanUtils;
import com.foxconn.ipebg.system.utils.SendMailUtil;
import com.foxconn.ipebg.system.utils.SendMailUtilProx;
import com.foxconn.ipebg.system.utils.UserUtil;
import org.hibernate.SQLQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 文檔簽核申請表 (合併失敗，重新提交)
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-01-29 10:58:09
 */
@Service
@Transactional(readOnly = true)
public class WffilesignprocessService extends BaseBusinessService<WffilesignprocessEntity, String> {
    @Autowired
    private WffilesignprocessDao wffilesignprocessDao;
    @Autowired
    private WorkFlowService flowService;
    @Autowired
    private WfNodeinfoService wfNodeinfoService;
    @Autowired
    private WfNoderuleService noderuleService;
    @Autowired
    private WfConifgService conifgService;
    @Autowired
    private TQhAllRelationService allRelationService;
    @Autowired
    private UserServiceRedisUtil serviceUtil;
    @Autowired
    private DictService dictService;
    @Autowired
    private TProxyUserinfoService tProxyUserinfoService;
    @Autowired
    private SignModelPositionService signModelPositionService;
    @Autowired
    private FtpInfoService infoService;
    @Autowired
    private TPubMailrecordService mailrecordService;
    @Autowired
    private TQhUserformhsService tQhUserformhsService;

    @Override
    public HibernateDao<WffilesignprocessEntity, String> getEntityDao() {
        return wffilesignprocessDao;
    }

    public WffilesignprocessEntity findBySerialno(String serialno) {
        return this.wffilesignprocessDao.findUniqueBy("serialno", serialno);
    }

    /**
     * 方法描述: 啟動流程
     *
     * @Author: S6073061
     * @CreateDate: 2021-01-29 10:58:09
     * @Return
     **/

    @Transactional
    public String startProcess(WffilesignprocessEntity wffilesignprocess, String flag, String workFlowId,List<SignModelPositionEntity> signModelPositionList) {
        try {
          //檢驗填單人部門欄位必須存在，數據權限控制，切記必須有
            Assert.isTrue(MyBeanUtils.checkIfExitMakerdeptno(WffilesignprocessEntity.class, "makerdeptno"), "填單人部門代碼必須填寫，請在表中創建相關欄位");
            wffilesignprocess.setMakerdeptno(UserUtil.getCurrentUserMoreInfo().getDeptno());
            //自動填充沒有設置的審核人為自動審核
            flowService.setWfAutorBlank(wffilesignprocess, workFlowId);
            //設置流水號
            wffilesignprocess.setCreatetime(new Date());
            //保存和臨時保存
            if (flag.equals(Constant.RESULT.CODE_SAVE.getValue())) {
                if (StringUtils.isBlank(wffilesignprocess.getId())) {
                    wffilesignprocess.setSerialno(serviceUtil.createSerialno(wffilesignprocess.getMakerno()));
                    wffilesignprocess.setNewRecord(true);
                }
                wffilesignprocess.setWorkstatus(Constant.RESULT.CODE_SAVE.getValue());
                this.save(wffilesignprocess);

                //保存中間表信息
                TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(wffilesignprocess.getSerialno());
                if (relationEntity == null) {
                    relationEntity = new TQhAllRelationEntity();
                    relationEntity.setDtoName("WffilesignprocessEntity");
                    relationEntity.setSerialno(wffilesignprocess.getSerialno());
                    relationEntity.setWfName(conifgService.findUnique(workFlowId).getWorkflowname());
                    relationEntity.setWorkstatus(Constant.RESULT.CODE_SAVE.getValue());
                    if (infoService.whetherAppSign(UserUtil.getCurrentUser().getLoginName())) {
                        relationEntity.setWhetherApp("Y");
                    }
                }
                relationEntity.setYnFixedPosition(wffilesignprocess.getYnFixedPosition());
                Dict dict = dictService.getDictByTypeAndVlaue("workflow_code", workFlowId);
                relationEntity.setWorkflowid(workFlowId);
                relationEntity.setUrgencyLevel(wffilesignprocess.getUrgencyLevel());
                relationEntity.setVersion(dict.getLabel());
                allRelationService.save(relationEntity);

            } else if (flag.equals(Constant.RESULT.CODE_RUNNING.getValue())) {
                /**
                 * 方法描述: 提交
                 * @Author: S6114648
                 * @CreateDate: 2018/10/23  下午 04:01
                 * @Return
                 **/

                if (StringUtils.isEmpty(wffilesignprocess.getSerialno()) || wffilesignprocess.getSerialno() == null) {

                    wffilesignprocess.setNewRecord(true);
                    wffilesignprocess.setSerialno(serviceUtil.createSerialno(wffilesignprocess.getMakerno()));
                } else {
                    wffilesignprocess.setNewRecord(false);
                }
                wffilesignprocess.setWorkstatus(Constant.RESULT.CODE_RUNNING.getValue());
                if (StringUtils.isNotEmpty(wffilesignprocess.getFilepassword())) {
                    wffilesignprocess.setFilepassword(AESForJuhuiUtil.encrypt(wffilesignprocess.getFilepassword()));
                }
                this.save(wffilesignprocess);
                //流程參數
                WorkFlowEntity entity = new WorkFlowEntity();
                entity.setWorkflowId(workFlowId);
                entity.setSerialNo(wffilesignprocess.getSerialno());

                /**
                 * 方法描述: 獲取普通用戶參數
                 * @Author: S6114648
                 **/

                List<WfNodeinfoEntity> infoList = wfNodeinfoService.findForUser(workFlowId);
                String taskUser = "";
                for (WfNodeinfoEntity nodeinfoEntity : infoList) {
                    taskUser += nodeinfoEntity.getNodename() + ":" + Reflections.getFieldValue(wffilesignprocess, nodeinfoEntity.getColname()) + ";";
                }
                entity.setTaskUsers(taskUser);

                //獲取會簽節點參數
                List<WfNodeinfoEntity> infoLists = wfNodeinfoService.findForHuiqian(workFlowId);
                String huiqian = "";
                WfNoderuleEntity noderuleEntity = null;
                for (WfNodeinfoEntity list : infoLists) {
                    noderuleEntity = noderuleService.findUniqByNodeId(list.getNodeid(), workFlowId);
                    huiqian += noderuleEntity.getNodeparamname() + ":" + Reflections.getFieldValue(wffilesignprocess, list.getColname()) + ";";
                }
                entity.setHuiqian(huiqian);

                String processId = flowService.processStart(entity, wffilesignprocess);
                //創建會簽節點
//                flowService.createHuiqianTaskInfor(entity, processId, tQhWfbuildprojectrocess);
                //防止第一個節點為空  自動推動任務
//                flowService.AutoCompleteTask(processId);
                //保存中間表信息
                TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(wffilesignprocess.getSerialno());
                if (relationEntity == null) {
                    relationEntity = new TQhAllRelationEntity();
                    relationEntity.setDtoName("WffilesignprocessEntity");
                    relationEntity.setSerialno(wffilesignprocess.getSerialno());
                    relationEntity.setWfName(conifgService.findUnique(workFlowId).getWorkflowname());
                    if (infoService.whetherAppSign(UserUtil.getCurrentUser().getLoginName())||"1".equals(wffilesignprocess.getYnFixedPosition())) {
                        relationEntity.setWhetherApp("Y");
                    }
                }
                relationEntity.setWorkflowid(workFlowId);
                relationEntity.setYnFixedPosition(wffilesignprocess.getYnFixedPosition());
                Dict dict = dictService.getDictByTypeAndVlaue("workflow_code", workFlowId);
                relationEntity.setVersion(dict.getLabel());
                relationEntity.setProcessid(processId);
                relationEntity.setWorkstatus(Constant.RESULT.CODE_RUNNING.getValue());
                //添加代理人信息
                ProxTaskInfo taskInfo = tProxyUserinfoService.findNodeName(relationEntity);
                relationEntity.setNodename(taskInfo.getTaskName());
                relationEntity.setDeptNo(wffilesignprocess.getApplydeptno());
                relationEntity.setUrgencyLevel(wffilesignprocess.getUrgencyLevel());
                allRelationService.save(relationEntity);
            }

                List<SignModelPositionEntity> signPosition = signModelPositionList;
                if(signModelPositionList!=null&&signModelPositionList.size()>0){ //保存簽核檔位置
                    if("".equals(signModelPositionList.get(0).getApplyId())){ //申請單編號為空時
                        signPosition = new ArrayList<SignModelPositionEntity>();
                        for(SignModelPositionEntity item:signModelPositionList){
                            item.setApplyId(wffilesignprocess.getSerialno());
                            item.setNewRecord(true);
                            signPosition.add(item);
                        }
                    }else{ //保存後 提交 或駁回後提交
                        signModelPositionService.batchDelete(wffilesignprocess.getSerialno());
                    }
                    signModelPositionService.saveList(signPosition);
                }

        } catch (Exception e) {
            this.logger.info(e.getMessage(), e);
            return Constant.RESULT.CODE_NO.getValue();
        }
        return Constant.RESULT.CODE_YES.getValue();
    }



    /**
     * 方法描述: 啟動流程
     *
     * @Author: S6114299
     * @CreateDate: 2023-11-03
     * @Return
     **/

    @Transactional
    public Map<String,String> startOtherSysProcess(WffilesignprocessEntity wffilesignprocess, String workFlowId, List<SignModelPositionEntity> signModelPositionList, String sysCode, User currentUser) {
        Map<String,String> map = new HashMap<>();
        try {
            //檢驗填單人部門欄位必須存在，數據權限控制，切記必須有
            Assert.isTrue(MyBeanUtils.checkIfExitMakerdeptno(WffilesignprocessEntity.class, "makerdeptno"), "填單人部門代碼必須填寫，請在表中創建相關欄位");
            //自動填充沒有設置的審核人為自動審核
            flowService.setWfAutorBlank(wffilesignprocess, workFlowId);
            wffilesignprocess.setCreatetime(new Date());

            if(StringUtils.isEmpty(wffilesignprocess.getId()) || wffilesignprocess.getId() == null) {
                    wffilesignprocess.setNewRecord(true);
            } else {
                    wffilesignprocess.setNewRecord(false);
            }
            wffilesignprocess.setWorkstatus(Constant.RESULT.CODE_RUNNING.getValue());
            this.saveForOtherSys(wffilesignprocess,currentUser);
            map.put("mainId",wffilesignprocess.getId());
            //流程參數
            WorkFlowEntity entity = new WorkFlowEntity();
            entity.setWorkflowId(workFlowId);
            entity.setSerialNo(wffilesignprocess.getSerialno());

            List<WfNodeinfoEntity> infoList = wfNodeinfoService.findForUser(workFlowId);
            String taskUser = "";
            for (WfNodeinfoEntity nodeinfoEntity : infoList) {
                    taskUser += nodeinfoEntity.getNodename() + ":" + Reflections.getFieldValue(wffilesignprocess, nodeinfoEntity.getColname()) + ";";
            }
            entity.setTaskUsers(taskUser);

            //獲取會簽節點參數
            List<WfNodeinfoEntity> infoLists = wfNodeinfoService.findForHuiqian(workFlowId);
            String huiqian = "";
            WfNoderuleEntity noderuleEntity = null;
            for (WfNodeinfoEntity list : infoLists) {
                noderuleEntity = noderuleService.findUniqByNodeId(list.getNodeid(), workFlowId);
                huiqian += noderuleEntity.getNodeparamname() + ":" + Reflections.getFieldValue(wffilesignprocess, list.getColname()) + ";";
            }
            entity.setHuiqian(huiqian);

            String processId = flowService.otherSysProcessStart(entity, wffilesignprocess,sysCode,currentUser);

            //保存中間表信息
            TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(wffilesignprocess.getSerialno());
            if (relationEntity == null) {
                    relationEntity = new TQhAllRelationEntity();
                    relationEntity.setDtoName("WffilesignprocessEntity");
                    relationEntity.setSerialno(wffilesignprocess.getSerialno());
                    relationEntity.setWfName(conifgService.findUnique(workFlowId).getWorkflowname());
                    relationEntity.setWorkflowid(workFlowId);
                    relationEntity.setFormFrom("ODSMS");
                    relationEntity.setWhetherApp("Y");
            }
                Dict dict = dictService.getDictByTypeAndVlaue("workflow_code", workFlowId);
                relationEntity.setVersion(dict.getLabel());
                relationEntity.setProcessid(processId);
                relationEntity.setWorkstatus(Constant.RESULT.CODE_RUNNING.getValue());
                //添加代理人信息
                ProxTaskInfo taskInfo = tProxyUserinfoService.findNodeName(relationEntity);
                relationEntity.setNodename(taskInfo.getTaskName());
                relationEntity.setDeptNo(wffilesignprocess.getMakerdeptno());
                relationEntity.setUrgencyLevel(wffilesignprocess.getUrgencyLevel());
                allRelationService.saveForOtherSys(relationEntity,currentUser);

            List<SignModelPositionEntity> signPosition = signModelPositionList;
            //保存簽核檔位置
            if(signModelPositionList!=null&&signModelPositionList.size()>0){
                if("".equals(signModelPositionList.get(0).getApplyId())){ //申請單編號為空時
                    signPosition = new ArrayList<SignModelPositionEntity>();
                    for(SignModelPositionEntity item:signModelPositionList){
                        item.setApplyId(wffilesignprocess.getSerialno());
                        item.setNewRecord(true);
                        signPosition.add(item);
                    }
                }else{ //保存後 提交 或駁回後提交
                    signModelPositionService.batchDelete(wffilesignprocess.getSerialno());
                }
                signModelPositionService.saveListForOtherSys(signPosition,currentUser);
            }
        } catch (Exception e) {
            this.logger.info(e.getMessage(), e);
            map.put("result",Constant.RESULT.CODE_NO.getValue());
            return map;
        }
        map.put("result",Constant.RESULT.CODE_YES.getValue());
        return map;
    }

    /*手機審核更新附件id，service中繼承的前置執行方法中含有session信息無法使用，新增保存方法*/
    @Transactional
    public void saveForMobile(WffilesignprocessEntity entity,String empno) {
        if(entity.getId()==null) {
            entity.setId(IdGen.uuid());
            entity.setCreateBy(empno);
            entity.setCreateDate(new Date());
        }
        entity.setUpdateBy(empno);
        entity.setUpdateDate(new Date());
        wffilesignprocessDao.save(entity);
    }

    //發送郵件並記錄
    @Transactional
    public void  sendMails(List<Object[]> mails,String formNo,String workflowName,String remark, WfConifgEntity wfConifgEntity ){
        String freeloginurl = dictService.get(558).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" ;
        String freeloginurlip = dictService.get(559).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=";
        String url = dictService.get(560).getValue();
        String urlip = dictService.get(561).getValue();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String chargermantime = sf.format(new Date());
        out:
        for(Object[] item:mails){
            if(item[0]==null||item[1]==null||item[2]==null){
                  continue out;
            }
            Mail mail = new Mail();
            String validStr = UUID.randomUUID().toString().replace("-", "");
            freeloginurl = freeloginurl + item[0].toString() + "&loginType=1&utoken=" + validStr + "&url=/" + wfConifgEntity.getDetailaction() + "/" + formNo;
            freeloginurlip = freeloginurlip + item[0].toString() + "&loginType=1&utoken=" + validStr + "&url=/" + wfConifgEntity.getDetailaction() + "/" + formNo;

            mail.setSystemname(dictService.getDictByTypeAndVlaue("sys_property", "sys_name").getLabel());
            mail.setUsermail(item[2].toString());
            mail.setUsername(item[1].toString());

            mail.setChargerman(UserUtil.getCurrentUser().getName());
            mail.setChargermanremark(remark);
            mail.setChargermantime(chargermantime);
            mail.setDusername(item[1].toString());
            mail.setOrdertype(workflowName);
            mail.setSerialno(formNo);
            mail.setOrderstatus("9");

            mail.setUrl(url);
            mail.setUrlip(urlip);
            mail.setFreeloginurl(freeloginurl);
            mail.setFreeloginurlip(freeloginurlip);

            //保存發送記錄
            TPubMailrecordEntity mailrecordEntity = new TPubMailrecordEntity();
            mailrecordEntity.setChargerman(mail.getChargerman());
            mailrecordEntity.setDusername(mail.getDusername());
            mailrecordEntity.setOrderstatus(mail.getOrderstatus());
            mailrecordEntity.setOrdertype(mail.getOrdertype());
            mailrecordEntity.setSerialno(mail.getSerialno());
            mailrecordEntity.setSendStatus("0");
            mailrecordEntity.setEmpno(UserUtil.getCurrentUser().getName());
            mailrecordEntity.setUrl(mail.getUrl());
            mailrecordEntity.setUrlip(mail.getUrlip());
            mailrecordEntity.setFreeloginurl(mail.getFreeloginurl());
            mailrecordEntity.setFreeloginurlip(mail.getFreeloginurlip());
            mailrecordEntity.setValidStr(validStr);
            mailrecordEntity.setUsername(mail.getUsername());
            mailrecordEntity.setUsermail(mail.getUsermail());
            mailrecordService.save(mailrecordEntity);
            String sendResult = new SendMailUtil().sendMail(mail);
                if ("0".equals(sendResult)) {
                    //發送成功，更新標誌
                    mailrecordEntity.setSendStatus("1");
                    mailrecordService.save(mailrecordEntity);
                }

            mail.setAppAuditUrl(StrUtil.concat(true, wfConifgEntity.getAppDetailUrl(), "?serialno=", mail.getSerialno()));
            String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
            String JuihuiUrl = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
            SendMailUtilProx sendMailUtil = new SendMailUtilProx();
            sendMailUtil.sendJuihui(mail, type, JuihuiUrl, item[0].toString());

            String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
            //url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
            sendMailUtil = new SendMailUtilProx();
            sendMailUtil.sendJuihuiApi(mail, typeApp, JuihuiUrl, item[0].toString());
        }
    }

    public List findPastEmails(String formNo,String auditNo){
        String sql = " select su.login_name,su.name,su.email " +
                     "   from (select tqc.chargeno," +
                     "                 case when c.createDate is null then '0' when tqc.create_date>c.createDate then '0' else '1' end as flag" +
                     "           from T_QH_CHARGELOG tqc , (select max(create_date) createDate " +
                     "                                        from T_QH_CHARGELOG " +
                     "                                       where serialno = :formNo " +
                     "                                         and ispass = '駁回') c" +
                     "          where serialno = :formNo" +
                     "  union select '"+auditNo+"' as  chargeno, '0' as flag) m" +
                     "  left join sys_user su" +
                     "    on m.chargeno = su.login_name" +
                     " where flag = '0'" +
                     " group by su.login_name,su.name,su.email";
        return wffilesignprocessDao.findPastEmails(sql,formNo);
    }
    public List findListBySerialno(String serialno) {
        String  sql = "select id,workstatus from T_QH_WFFILESIGNPROCESS where serialno =?0";
        return this.wffilesignprocessDao.createSQLQuery(sql,serialno).list();
    }



    public int findMaxLength(String serialno, TaskInfo nodeInfo,String workflowid){
        int maxLength = 10;
            String nodeName = nodeInfo.getTaskName();
            String signer = nodeInfo.getAssignee();
            String sql = " select case when sum(case when smp.img_show_type = '3' then 0 else 1 end) = 0 then 20 else 10 end as maxLength" +
                         " from sign_model_position smp " +
                         " left join WF_NODEINFO wn " +
                         " on smp.sign_point = wn.colname " +
                         " where wn.workflowid = :workflowid " +
                         " and apply_id = :serialno " +
                         " and sign_no = :signer " +
                         " and wn.nodename = :nodeName";
        SQLQuery query =  this.wffilesignprocessDao.createSQLQuery(sql);
        query.setParameter("workflowid", workflowid);
        query.setParameter("serialno", serialno);
        query.setParameter("signer", signer);
        query.setParameter("nodeName", nodeName);
        List result = query.list();
        if (result != null && result.size() > 0) {
            maxLength = ((Integer) result.get(0)).intValue();
        }
        return maxLength;
    }

    @Transactional(readOnly = false)
    public void sendManagerInfo(FileSignParams fileSignParams) {
        WffilesignprocessEntity wffilesignprocess = this.wffilesignprocessDao.findUniqueBy("serialno", fileSignParams.getSerialno());
        if(StrUtil.equals("TRACEDOC", wffilesignprocess.getSource())||StrUtil.equals("itportal", wffilesignprocess.getSource())){
            return;
        }
        String stauts = wffilesignprocess.getWorkstatus();
        String curEmpNo = "", curEmpName = "",curTaskName="";
        if (fileSignParams.getCurrentTaskInfo() != null) {
            curEmpNo = fileSignParams.getCurrentTaskInfo().getAssignee();
            curEmpName = tQhUserformhsService.findByEmpno(fileSignParams.getCurrentTaskInfo().getAssignee()).getEmpname();
            curTaskName = fileSignParams.getCurrentTaskInfo().getTaskName();
        }
        String pdfUrl = wffilesignprocess.getAttachids2();
        try {
            if (StrUtil.equals("itportal", wffilesignprocess.getSource())) {
                String address = dictService.getUniqueDictByTypeAndCode("dict_fileAddress", "dict_fileAddress1").getValue();
                String result = HttpUtil.post(address, JSONUtil.createObj().put("serialno", fileSignParams.getSerialno()).put("stauts", stauts)
                        .put("curEmpNo", curEmpNo).put("curEmpName", curEmpName).put("pdfUrl", pdfUrl), 10000);
                logger.info("AI系統項目管理平台測試報告審核後狀態更新接口返回結果[{}],表單編號【{}】", result, fileSignParams.getSerialno());
            }else if(StrUtil.equals("TRACEDOC", wffilesignprocess.getSource())){
                TQhAllRelationEntity tqhAllRelationEntity = allRelationService.queryByEntity(fileSignParams.getSerialno());
                List<WfNodeinfoEntity> entityList = wfNodeinfoService.findByFilters(tqhAllRelationEntity.getWorkflowid());
                String chargePath = "";
                List chargeList = new ArrayList();
                for (WfNodeinfoEntity entity : entityList) {
                    if ("makerno".equals(entity.getColname())) {
                        continue;
                    }
                    chargeList.add(StrUtil.concat(true,entity.getNodealain(),"&",(String)Reflections.getFieldValue(wffilesignprocess, entity.getColname()),"&",(String)Reflections.getFieldValue(wffilesignprocess, entity.getColname().replace("no", "name"))));
                }
                String address = dictService.getUniqueDictByTypeAndCode("dict_fileAddress", "dict_fileAddress2").getValue();
                String result = HttpUtil.post(address, JSONUtil.createObj().put("serialno", fileSignParams.getSerialno()).put("stauts", stauts)
                        .put("curTaskInfo", StrUtil.concat(true,curTaskName,"&",curEmpNo,"&",curEmpName))
                        .put("chargePath", CollectionUtil.join(chargeList,"/")).put("pdfUrl", pdfUrl)
                        .put("ipAddress",fileSignParams.getIpAddress()).put("discrib",fileSignParams.getDiscrib())
                        .put("chargeDate",new Date()), 10000);
                logger.info("AI系統項目管理平台測試報告審核後狀態更新接口返回結果[{}],表單編號【{}】", result, fileSignParams.getSerialno());
            }
        } catch (Exception e) {
            logger.info(e.getMessage(), e);
        }
    }
}
