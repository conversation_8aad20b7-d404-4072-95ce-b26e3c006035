package com.foxconn.ipebg.buessness.etrfrm.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.foxconn.ipebg.buessness.common.entity.*;
import com.foxconn.ipebg.buessness.common.service.*;
import com.foxconn.ipebg.buessness.etrfrm.dto.ByTypeTaskInfo;
import com.foxconn.ipebg.buessness.etrfrm.dto.RetractTaskInfo;
import com.foxconn.ipebg.buessness.information.dto.FileSignParams;
import com.foxconn.ipebg.buessness.information.dto.WffilesignprocesspathDto;
import com.foxconn.ipebg.buessness.information.entity.WfsystemprocessEntity;
import com.foxconn.ipebg.buessness.information.service.NotProcessingNowService;
import com.foxconn.ipebg.buessness.information.service.SignModelPositionService;
import com.foxconn.ipebg.buessness.information.service.WffilesignprocessService;
import com.foxconn.ipebg.buessness.workflow.entity.*;
import com.foxconn.ipebg.buessness.workflow.service.*;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.utils.*;
import com.foxconn.ipebg.system.dto.EntityNewAndOld;
import com.foxconn.ipebg.system.entity.*;
import com.foxconn.ipebg.system.service.*;
import com.foxconn.ipebg.system.utils.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Company foxconn 重新提交
 * Copyright (C) 2004-2018 All Rights Reserved.
 */
@Service
@Transactional(readOnly = true)
public class WorkFlowForMobileService {
    private static final Logger logger = LoggerFactory.getLogger(WorkFlowForMobileService.class);
    @Autowired
    private ProcessLocalService processService;
    @Autowired
    private TQhAllRelationForMobileService allRelationService;
    @Autowired
    private WfConifgService conifgService;
    @Autowired
    private TQhChargelogService chargelogService;
    @Autowired
    private WfNodeinfoService wfNodeinfoService;
    @Autowired
    private WfConfigparamService wfConfigparamService;
    @Autowired
    private WfNoderuleService wfNoderuleService;
    @Autowired
    private TQhChargelogService tQhChargelogService;
    @Autowired
    private DictService dictService;
    @Autowired
    private WfNodeparamService nodeparamService;
    @Autowired
    private TQhUserformhsService tQhUserformhsService;
    @Autowired
    private TPubMailrecordService mailrecordService;
    /*@Autowired
    private TPubJuhuiApprecordService juhuiApprecordService;*/
    @Autowired
    private UserService userService;
    @Autowired
    private TQhFactoryidconfigService tQhFactoryidconfigService;
    @Autowired
    private TPubAreabaseinfoService areabaseinfoService;
    @Autowired
    private SendSmsService sendSmsService;
    @Autowired
    private ScheduleJobService scheduleJobService;
    @Autowired
    private TossAndTurnFhwService2 tossAndTurnFhwService;
    @Autowired
    private TProxyUserinfoService proxyUserinfoService;
    @Autowired
    private TPubDynamicTableService dynamicTableService;
    @Autowired
    private TQhUserformhsService userformhsService;
    @Autowired
    private FtpInfoService ftpInfoService;
    @Autowired
    private TPubFileobjectService fileobjectService;
    @Autowired
    @Lazy
    private ChargLogService chargLogService;
    @Autowired
    private SignModelPositionService signModelPositionService;

    @Autowired
    private NotProcessingNowService notProcessingNowService;
    @Autowired
    @Lazy
    private WfsystemprocessForMobileService wfsystemprocessService;
    @Autowired
    @Lazy
    private WffilesignprocessService wffilesignprocessService;
    @Autowired
    @Lazy
    private TProxyFormUserinfoService formUserinfoService;

    /**
     * 方法描述: 獲取待辦總數
     **/
    @Transactional(readOnly = false)
    public List<MyTaskCount> myTask(String empno, String workFlowId) {
        User user = null;
        user = new User();
        user.setLoginName(empno);

        List<MyTaskCount> entityList = new ArrayList<MyTaskCount>();
        List<MyTaskCount> entityBackList = new ArrayList<MyTaskCount>();
        //--------查詢代理相關信息---------------
        List<String> processIds = proxyUserinfoService.findUserAllProcessId(user.getLoginName());
        //--------查詢代理相關信息 ----------------
        //查詢中間表
        if (processIds.size() > 0) {
            int subSize = 1000;
            int subCount = processIds.size();
            int subPageTotal = (subCount / subSize) + ((subCount % subSize > 0) ? 1 : 0);
            for (int i = 0, len = subPageTotal - 1; i <= len; i++) {
                int fromIndex = i * subSize;
                int toIndex = ((i == len) ? subCount : ((i + 1) * subSize));
                entityList.addAll(allRelationService.queryIndexTask(processIds.subList(fromIndex, toIndex), workFlowId));
            }
            //小網頁只看審核中的，駁回的不看
/*            for (int i = 0, len = subPageTotal - 1; i <= len; i++) {
                int fromIndex = i * subSize;
                int toIndex = ((i == len) ? subCount : ((i + 1) * subSize));
                entityList.addAll(allRelationService.queryIndexBackTask(processIds.subList(fromIndex, toIndex),workFlowId));
            }*/
            logger.info(String.format("user %s falcon found %s,system found %s", user.getLoginName(), processIds.size(), entityList.size()));
        }
        return entityList;
    }

    /**
     * 方法描述: 文檔簽核查詢專用
     *
     * @Author: S6114648
     * @CreateDate: 2023/4/3  上午 10:49
     * @Return
     **/
    @Transactional(readOnly = false)
    public List<MyTaskCount> myFileEsignTask(String empno, String workFlowId) {
        User user = null;
        user = new User();
        user.setLoginName(empno);

        List<MyTaskCount> entityList = new ArrayList<MyTaskCount>();
        List<MyTaskCount> entityBackList = new ArrayList<MyTaskCount>();
        //--------查詢代理相關信息---------------
        List<String> processIds = proxyUserinfoService.findUserAllProcessId(UserUtil.getCurrentUser().getLoginName());
        //--------查詢代理相關信息 ----------------
        //查詢中間表
        if (processIds.size() > 0) {
            int subSize = 1000;
            int subCount = processIds.size();
            int subPageTotal = (subCount / subSize) + ((subCount % subSize > 0) ? 1 : 0);
            for (int i = 0, len = subPageTotal - 1; i <= len; i++) {
                int fromIndex = i * subSize;
                int toIndex = ((i == len) ? subCount : ((i + 1) * subSize));
                entityList.addAll(allRelationService.queryFileEsignIndexTask(processIds.subList(fromIndex, toIndex), workFlowId));
            }
            //小網頁只看審核中的，駁回的不看
/*            for (int i = 0, len = subPageTotal - 1; i <= len; i++) {
                int fromIndex = i * subSize;
                int toIndex = ((i == len) ? subCount : ((i + 1) * subSize));
                entityList.addAll(allRelationService.queryIndexBackTask(processIds.subList(fromIndex, toIndex),workFlowId));
            }*/
            logger.info(String.format("user %s falcon found %s,system found %s", user.getLoginName(), processIds.size(), entityList.size()));
        }
        return entityList;
    }

    /**
     * 方法描述: 獲取所有待辦任務 有分頁
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/13  上午 08:14
     * @Return
     **/

    @Transactional(readOnly = false)
    public Page<Object> getAllMyTask(String workFlowId, String status, Page page, List<PropertyFilter> filters, String loginName, Boolean isNeedsBuness) {
        try {
            List<Object> gtasksList = new ArrayList<Object>();
            Gtasks gtasks = null;
            //--------查詢代理相關信息---------------
            List<String> processIds = proxyUserinfoService.findUserAllProcessId(loginName);
            //--------查詢代理相關信息----------------
            if (processIds.size() > 0) {
                PropertyFilter filter = new PropertyFilter("INS_processid", processIds);
                filters.add(filter);
                WfConifgEntity conifgEntity = null;
                List<TQhAllRelationEntity> dataList = null;
                if (StringUtils.isNotBlank(workFlowId)) {
                    PropertyFilter filter_status = new PropertyFilter("EQS_workstatus", status);
                    PropertyFilter filter_workFlowId = new PropertyFilter("EQS_workflowid", workFlowId);
                    filters.add(filter_status);
                    filters.add(filter_workFlowId);
                    dataList = allRelationService.search(page, filters).getResult();
                } else if (StrUtil.isNotEmpty(status)) {
                    PropertyFilter filter_status = new PropertyFilter("EQS_workstatus", status);
                    filters.add(filter_status);
                    dataList = allRelationService.search(page, filters).getResult();
                } else {
                    dataList = allRelationService.search(page, filters).getResult();
                }
                Dict dict2 = new Dict();
                dict2.setType("entfrmIpebgIP_app");
                dict2.setCodeUniq("entfrmIpebgIP_app01");
                String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
                List<String> notProcessingNowList = notProcessingNowService.findListByEmpNo(loginName);
                List<Dict> dtList = dictService.getDictByType("audit_status");
                for (TQhAllRelationEntity entity : dataList) {
                    gtasks = allRelationService.queryMyGtasks(entity.getSerialno(), entity.getDtoName());
                    gtasks.setLevel(entity.getUrgencyLevel() == null ? "" : entity.getUrgencyLevel().replace("0", "特急").replace("1", "緊急").replace("2", "一般"));
                    if (gtasks != null) {
                        if (entity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                            Object obj = allRelationService.findByDto(entity.getDtoName(), entity.getSerialno());
                            gtasks.setWfName(StrUtil.concat(true, entity.getWfName(), "--", Reflections.getFieldValue(obj, "applytablename") + ""));
                        } else {
                            gtasks.setWfName(entity.getWfName());
                        }
                        conifgEntity = conifgService.findUnique(entity.getWorkflowid());
                        if ("4".equals(entity.getWorkstatus())) {
                            gtasks.setAuditAction(conifgEntity.getModaction());
                        } else if ("2".equals(entity.getWorkstatus())) {
                            gtasks.setAuditAction(conifgEntity.getAction());
                        }
                        gtasks.setType(conifgEntity.getAppType());
                        if ("1".equals(conifgEntity.getAppType())) {
                            gtasks.setAppAuditUrl(StrUtil.concat(true, fronUrl, conifgEntity.getAppAuditUrl(), "?serialno=", entity.getSerialno()));
                        } else {
//                            gtasks.setAppDetailUrl(StrUtil.concat(true, conifgEntity.getAppDetailUrl(), "?serialno=", entity.getSerialno()));
                            gtasks.setAppAuditUrl(StrUtil.concat(true, conifgEntity.getAppAuditUrl(), "?serialno=", entity.getSerialno()));
                        }
                        gtasks.setSerialno(entity.getSerialno());
                        InterConfig config = new InterConfig();
                        config.setProcessId(entity.getProcessid());
                        //獲取流程當前節點處理人
//                        InterResult taskInfo = processService.currentTaskInfo(config);
//                        TaskInfo info = taskInfo.getTaskInfoList().get(0);
//                        WfNodeinfoEntity nodeInfoEntity = wfNodeinfoService.findByTaskName(info.getTaskName(), entity.getWorkflowid(), entity.getVersion());
//                        gtasks.setCanBatch(nodeInfoEntity.getCanbatch());
//                        gtasks.setTaskName(nodeInfoEntity.getNodealain());
                        //合併業務表和中間表
                        Map<String, Object> taskMap = BeanUtil.beanToMap(gtasks, false, false);
                        if (isNeedsBuness) {
                            Map<String, Object> bunessMap = BeanUtil.beanToMap(allRelationService.findByDto(entity.getDtoName(), entity.getSerialno()), false, false);
                            if (bunessMap == null) {
                                page.setTotalCount(page.getTotalCount() - 1);
                                continue;
                            }
                            for (String key : bunessMap.keySet()) {
                                if ("workstatus".equals(key)) {
                                    for (Dict dict : dtList) {
                                        if (taskMap.get(key).equals(dict.getValue())) {
                                            taskMap.put(key, dict.getLabel());
                                        }
                                    }
                                }
                                if (!taskMap.containsKey(key)) {
                                    //處理違紀嘉獎的單子
                                    taskMap.put(key, bunessMap.get(key));
                                }
                            }
                        } else {
                            for (String key : taskMap.keySet()) {
                                if ("workstatus".equals(key)) {
                                    for (Dict dict : dtList) {
                                        if (taskMap.get(key).equals(dict.getValue())) {
                                            taskMap.put(key, dict.getLabel());
                                        }
                                    }
                                }
                            }
                        }
                        //是否有子表
                        if (StringUtils.isNotEmpty(conifgEntity.getDynfield02())) {
                            List<Object> dataChildList = allRelationService.findByListDto(conifgEntity.getDynfield02(), entity.getSerialno(), conifgEntity.getDynfield03());
                            Assert.notEmpty(dataChildList, String.format("任務編號：%s,實體：%s中不存在子數據", entity.getSerialno(), conifgEntity.getDynfield02()));
                            if (dataChildList != null && dataChildList.size() > 0) {
                                Map<String, Object> bunessChildMap = BeanUtil.beanToMap(dataChildList.get(0), false, false);
                                taskMap.putAll(bunessChildMap);
                                taskMap.put("childsNum", dataChildList.size());
                            }
                        }
                        List<TPubDynamicTableEntity> entityList = dynamicTableService.findByWorkflowId(workFlowId);
                        for (TPubDynamicTableEntity tableEntity : entityList) {
                            //是否需要字典替換
                            if ("1".equals(tableEntity.getIsTrans())) {
                                Assert.notNull(tableEntity.getDictCode(), "字典替換類型不可為空，請在T_PUB_DYNAMIC_TABLE配置【DICT_CODE】");
                                dtList = dictService.getDictByType(tableEntity.getDictCode());
                                for (Dict dict : dtList) {
                                    if (taskMap.get(tableEntity.getGridField()).equals(dict.getValue())) {
                                        taskMap.put(tableEntity.getGridField(), dict.getLabel());
                                    }
                                }
                            }
                        }
                        if(notProcessingNowList!=null&&notProcessingNowList.indexOf(entity.getSerialno())>=0){
                            taskMap.put("ynStop","Y");
                        }else{
                            taskMap.put("ynStop","N");
                        }
                        gtasksList.add(taskMap);
                    }
                }
            }
            page.setResult(gtasksList);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return page;
    }

    /**
     * 方法描述: 縮略待辦列表信息
     *
     * @Author: S6114299
     * @CreateDate: 2023/10/24
     * @Return
     **/

    @Transactional(readOnly = false)
    public List getRetractMyTask(String loginName, String applyName, String workstatus) {
        List result = new ArrayList(); //返回結果
        try {
            //--------查詢代理相關信息---------------
            List<String> processIds = proxyUserinfoService.findUserAllProcessId(loginName);
            //--------查詢代理相關信息----------------
            if (processIds.size() > 0) {
                List<RetractTaskInfo> dataList = allRelationService.findRetractMyTask(processIds, workstatus, applyName);
                Dict dict2 = new Dict();
                dict2.setType("entfrmIpebgIP_app");
                dict2.setCodeUniq("entfrmIpebgIP_app01");
                String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();


                Map<String,List> allType = new LinkedHashMap();
                for (RetractTaskInfo entity : dataList) {
                    if ("1".equals(entity.getAppType())) {
                        entity.setUrl(StrUtil.concat(true, fronUrl, entity.getUrl(), "?serialno=", entity.getApplyNo()));
                    } else {
                        entity.setUrl(StrUtil.concat(true, entity.getUrl(), "?serialno=", entity.getApplyNo()));
                    }
                    if (entity.getApplyType().contains("dzqh_wendangqianheshenqingdan")) {
                        Object obj = allRelationService.findByDto(entity.getDtoName(), entity.getApplyNo());
                        entity.setApplyName(StrUtil.concat(true, entity.getApplyName(), "--", Reflections.getFieldValue(obj, "applytablename") + ""));
                        Map map = new HashMap();
                        map.put("typeName", entity.getTypeName());
                        map.put("applyCount", entity.getApplyCount());
                        map.put("applyType", entity.getApplyType());
                        entity.setApplyNo(null);
                        entity.setApplyCount(0);
                        List item = new ArrayList();
                        item.add(entity);
                        map.put("list", item);
                        result.add(map);
                    } else {
                           List items = allType.getOrDefault(entity.getTypeName(),new ArrayList());
                           items.add(entity);
                           allType.put(entity.getTypeName(), items);
                    }
                }
                for(String key : allType.keySet()){
                    Map map = new HashMap();
                    map.put("typeName",key);
                    map.put("list",allType.get(key));
                    result.add(map);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }


    /**
     * 方法描述: 按表單類型查詢待辦列表信息
     *
     * @Author: S6114299
     * @CreateDate: 2023/10/24
     * @Return
     **/

    @Transactional(readOnly = false)
    public Page<Object> listMyTaskByType(String loginName, Page page, List<PropertyFilter> filters, String applyName,String ynStop) {
        try {
            List<Object> gtasksList = new ArrayList<Object>();
            //--------查詢代理相關信息---------------
            List<String> processIds = proxyUserinfoService.findUserAllProcessId(loginName);
            //--------查詢代理相關信息----------------
            if (processIds.size() > 0) {
                PropertyFilter filter = new PropertyFilter("INS_processid", processIds);
                filters.add(filter);
                WfConifgEntity conifgEntity = null;
                List<TQhAllRelationEntity> dataList = allRelationService.search(page, filters).getResult();
                List<String> notProcessingNowList = notProcessingNowService.findListByEmpNo(loginName);

                Dict dict2 = new Dict();
                dict2.setType("entfrmIpebgIP_app");
                dict2.setCodeUniq("entfrmIpebgIP_app01");
                String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
                out:
                for (TQhAllRelationEntity entity : dataList) {
                    ByTypeTaskInfo gtasks = new ByTypeTaskInfo();
                        conifgEntity = conifgService.findUnique(entity.getWorkflowid());
                        if ("1".equals(conifgEntity.getAppType())) {
                            gtasks.setUrl(StrUtil.concat(true, fronUrl, conifgEntity.getAppAuditUrl(), "?serialno=", entity.getSerialno()));
                        } else {
                            gtasks.setUrl(StrUtil.concat(true, conifgEntity.getAppAuditUrl(), "?serialno=", entity.getSerialno()));
                        }
                        if (entity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                            Object obj = allRelationService.findByDto(entity.getDtoName(), entity.getSerialno());
                            gtasks.setApplyName((String) Reflections.getFieldValue(obj, "applytablename"));
                            if(StringUtils.isNotBlank(applyName)&&gtasks.getApplyName().indexOf(applyName)<0){
                                continue out;
                            }
                        } else {
                            gtasks.setApplyName(entity.getSerialno());
                            if(StringUtils.isNotBlank(applyName)&&gtasks.getApplyName().indexOf(applyName)<0){
                                continue out;
                            }
                        }
                        gtasks.setSerialno(entity.getSerialno());
                        InterConfig config = new InterConfig();
                        config.setWorkFlowId(entity.getWorkflowid());
                        config.setProcessId(entity.getProcessid());
                        //獲取流程當前節點處理人
                        InterResult taskInfo = processService.currentTaskInfo(config);
                        TaskInfo info = taskInfo.getTaskInfoList().get(0);
                        WfNodeinfoEntity nodeInfoEntity = wfNodeinfoService.findByTaskName(info.getTaskName(), entity.getWorkflowid(), entity.getVersion());
                        gtasks.setCanBatch(nodeInfoEntity.getCanbatch());

                    long passMinute = (new Date().getTime() - entity.getUpdateDate().getTime()) / (1000 * 60); //歷時分鐘
                    SimpleDateFormat sf = new SimpleDateFormat("MM月dd日 HH:mm");
                    gtasks.setArriveTimeStr("於" + sf.format(entity.getUpdateDate()) + "到達");
                    gtasks.setLevel(entity.getUrgencyLevel() == null ? "" : entity.getUrgencyLevel().replace("0", "特急").replace("1", "緊急").replace("2", "一般"));
                    gtasks.setPassTimeStr("歷時" + ((passMinute - passMinute % 60) / 60) + "小時" + (passMinute % 60) + "分");

                    if(notProcessingNowList!=null&&notProcessingNowList.indexOf(entity.getSerialno())>=0){
                        gtasks.setYnStop("Y");
                    }else{
                        gtasks.setYnStop("N");
                    }
                    if(StringUtils.isNotBlank(ynStop)){
                          if(notProcessingNowList!=null&&notProcessingNowList.indexOf(entity.getSerialno())>=0)gtasksList.add(gtasks);
                    }else{
                        gtasksList.add(gtasks);
                    }

                }
            }
            page.setResult(gtasksList);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return page;
    }

    @Transactional(readOnly = false)
    public Map<String,Integer> listMyTaskByTypeCount(String loginName, List<PropertyFilter> filters, String applyName) {
        Map<String,Integer> countNumber = new HashMap<>();
        try {
            List<Object> gtasksList = new ArrayList<Object>();
            //--------查詢代理相關信息---------------
            List<String> processIds = proxyUserinfoService.findUserAllProcessId(loginName);
            //--------查詢代理相關信息----------------
            if (processIds.size() > 0) {
                PropertyFilter filter = new PropertyFilter("INS_processid", processIds);
                filters.add(filter);
                WfConifgEntity conifgEntity = null;
                List<TQhAllRelationEntity> dataList = allRelationService.search(filters);
                List<String> notProcessingNowList = notProcessingNowService.findListByEmpNo(loginName);
                int noProcess = 0;
                for(TQhAllRelationEntity item:dataList){
                    if(notProcessingNowList!=null&&notProcessingNowList.indexOf(item.getSerialno())>=0)noProcess++;
                }


                Map<String,List<TQhAllRelationEntity>> pathsGroupByCode = dataList.stream().collect(Collectors.groupingBy(e -> e.getUrgencyLevel()));

                countNumber.put("veryurgent",pathsGroupByCode.getOrDefault("0",new ArrayList()).size());
                countNumber.put("emergency",pathsGroupByCode.getOrDefault("1",new ArrayList()).size());
                countNumber.put("general",pathsGroupByCode.getOrDefault("2",new ArrayList()).size());
                countNumber.put("stop",noProcess);
                countNumber.put("all",dataList==null?0:dataList.size());
                /*Dict dict2 = new Dict();
                dict2.setType("entfrmIpebgIP_app");
                dict2.setCodeUniq("entfrmIpebgIP_app01");
                String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
                out:
                for (TQhAllRelationEntity entity : dataList) {
                    ByTypeTaskInfo gtasks = new ByTypeTaskInfo();
                    conifgEntity = conifgService.findUnique(entity.getWorkflowid());
                    if ("1".equals(conifgEntity.getAppType())) {
                        gtasks.setUrl(StrUtil.concat(true, fronUrl, conifgEntity.getAppAuditUrl(), "?serialno=", entity.getSerialno()));
                    } else {
                        gtasks.setUrl(StrUtil.concat(true, conifgEntity.getAppAuditUrl(), "?serialno=", entity.getSerialno()));
                    }
                    if (entity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                        Object obj = allRelationService.findByDto(entity.getDtoName(), entity.getSerialno());
                        gtasks.setApplyName((String) Reflections.getFieldValue(obj, "applytablename"));
                        if (StringUtils.isNotBlank(applyName) && gtasks.getApplyName().indexOf(applyName) < 0) {
                            continue out;
                        }
                    } else {
                        gtasks.setApplyName(entity.getSerialno());
                        if (StringUtils.isNotBlank(applyName) && gtasks.getApplyName().indexOf(applyName) < 0) {
                            continue out;
                        }
                    }
                    gtasks.setSerialno(entity.getSerialno());
                    InterConfig config = new InterConfig();
                    config.setWorkFlowId(entity.getWorkflowid());
                    config.setProcessId(entity.getProcessid());
                    //獲取流程當前節點處理人
                    InterResult taskInfo = processService.currentTaskInfo(config);
                    TaskInfo info = taskInfo.getTaskInfoList().get(0);
                    WfNodeinfoEntity nodeInfoEntity = wfNodeinfoService.findByTaskName(info.getTaskName(), entity.getWorkflowid(), entity.getVersion());
                    gtasks.setCanBatch(nodeInfoEntity.getCanbatch());

                    long passMinute = (new Date().getTime() - entity.getUpdateDate().getTime()) / (1000 * 60); //歷時分鐘
                    SimpleDateFormat sf = new SimpleDateFormat("MM月dd日 HH:mm");
                    gtasks.setArriveTimeStr("於" + sf.format(entity.getUpdateDate()) + "到達");
                    gtasks.setLevel(entity.getUrgencyLevel() == null ? "" : entity.getUrgencyLevel().replace("0", "特急").replace("1", "緊急").replace("2", "一般"));
                    gtasks.setPassTimeStr("歷時" + ((passMinute - passMinute % 60) / 60) + "小時" + (passMinute % 60) + "分");
                    gtasksList.add(gtasks);
                }*/
                /*if (gtasksList.size() > 0) {
                    countNumber = gtasksList.size();
                }*/
            }

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return countNumber;
    }

    /**
     * 方法描述: 獲取所有待辦任務 有分頁
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/13  上午 08:14
     * @Return
     **/

    @Transactional(readOnly = false)
    public Map<String, String> getNextMyTask(String serialno, List<PropertyFilter> filters, String loginName) {
        Map<String, String> resultStr = new HashMap<>();
        try {
            //--------查詢代理相關信息---------------
            List<String> processIds = proxyUserinfoService.findUserAllProcessId(loginName);
            //--------查詢代理相關信息----------------
            if (processIds.size() > 0) {
                List<String> notProcessingNowList = notProcessingNowService.findListByEmpNo(loginName);

                PropertyFilter filter = new PropertyFilter("INS_processid", processIds);
                filters.add(filter);
                if(notProcessingNowList!=null&&notProcessingNowList.size()>0){
                    PropertyFilter filter2 = new PropertyFilter("NINS_serialno", notProcessingNowList);
                    filters.add(filter2);
                }
                WfConifgEntity conifgEntity = null;
                List<TQhAllRelationEntity> dataList = null;
                dataList = allRelationService.searchOrder(filters, "createDate", true);
                String type = "";
                if (dataList != null && dataList.size() > 0) { //存在待審核單據
                    TQhAllRelationEntity resultBean = null;  //默認一下待審核單據
                    Map<String, Integer> map = new HashMap<>();
                    if (!"".equals(serialno)) {
                        TQhAllRelationEntity passForm = allRelationService.queryByEntity(serialno); //當前審核單據信息
                        type = passForm.getWorkflowid();
                        boolean ynFirst = false; //是否是當前簽核單據之後
                        boolean ynOk = false; //是否最終結果
                        for (TQhAllRelationEntity entity : dataList) {  //如存在與當前審核單據類型相同單據則 下一審核單據為第一個類型相同的單據\
                            Integer value = map.get(entity.getWorkflowid());
                            if (value == null) {
                                map.put(entity.getWorkflowid(), 1);
                            } else {
                                map.put(entity.getWorkflowid(), value + 1);
                            }
                            if (serialno.equals(entity.getSerialno())) {
                                ynFirst = true;
                            }
                            //設置默認審核單據
                            if (resultBean == null && !serialno.equals(entity.getSerialno())) {
                                resultBean = entity;
                            }
                            //默認單據resultBean與待審核單據類型不相同 當前entity 與審核單據類型相同且不是同一單據時 覆蓋原單據成為默認下一待審核單據
                            if (!ynOk && resultBean != null && !type.equals(resultBean.getWorkflowid()) && type.equals(entity.getWorkflowid()) && !serialno.equals(entity.getSerialno())) {
                                resultBean = entity;
                                if (ynFirst) {  //如當前entity 在待審核單據之後 則是下一待審核單據
                                    ynOk = true;
                                }
                            }
                            //在待審核單據之後 如類型相同 則為下一待審核單據
                            if (!ynOk && ynFirst && type.equals(entity.getWorkflowid()) && !serialno.equals(entity.getSerialno())) {
                                resultBean = entity;
                                ynOk = true;
                            }
                        }
                        if (resultBean.getWorkflowid().equals(type)) {
                            map.put(resultBean.getWorkflowid(), map.get(resultBean.getWorkflowid()) - 1);
                        }
                    }
                    Object obj = allRelationService.findByDto(resultBean.getDtoName(), resultBean.getSerialno());
                    if (obj != null) {
                        conifgEntity = conifgService.findUnique(resultBean.getWorkflowid());
                        Dict dict2 = new Dict();
                        dict2.setType("entfrmIpebgIP_app");
                        dict2.setCodeUniq("entfrmIpebgIP_app01");
                        String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
                        if ("1".equals(conifgEntity.getAppType())) {
                            resultStr.put("url", StrUtil.concat(true, fronUrl, conifgEntity.getAppAuditUrl(), "?serialno=", resultBean.getSerialno()));
                        } else {
                            resultStr.put("url", StrUtil.concat(true, conifgEntity.getAppAuditUrl(), "?serialno=", resultBean.getSerialno()));
                        }
                        String WfName = resultBean.getWfName();
                        if ("dzqh_wendangqianheshenqingdan".equals(resultBean.getWorkflowid())) {
                            WfName = StrUtil.concat(true, WfName, "--", Reflections.getFieldValue(obj, "applytablename") + "");
                        }
                        String applyType = resultBean.getWorkflowid();
                        String applyTypeName = resultBean.getWfName();
                        String applyCount = map.get(applyType).toString();
                        String backUrl = conifgEntity.getAppBackUrl();
                        resultStr.put("backUrl", backUrl + "?applyType=" + applyType + "&applyTypeName=" + applyTypeName + "&applyCount=" + applyCount);
                        resultStr.put("title", WfName);
                    }
                } else {  //不存在待審核單據
                    return resultStr;
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return resultStr;
    }

    /**
     * 方法描述: 獲取所有待辦任務 無分頁
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/13  上午 08:14
     * @Return
     **/

    @Transactional(readOnly = false)
    public List getAllMyTask(String workFlowId, String status, List<PropertyFilter> filters, String loginName) {
        List<Object> gtasksList = new ArrayList<Object>();
        try {
            Gtasks gtasks = null;
            //--------查詢代理相關信息---------------
            List<String> processIds = proxyUserinfoService.findUserAllProcessId(loginName);
            //--------查詢代理相關信息----------------
            if (processIds.size() > 0) {
                PropertyFilter filter = new PropertyFilter("INS_processid", processIds);
                filters.add(filter);
                WfConifgEntity conifgEntity = null;
                List<TQhAllRelationEntity> dataList = null;
                if (StringUtils.isNotBlank(workFlowId)) {
                    PropertyFilter filter_status = new PropertyFilter("EQS_workstatus", status);
                    PropertyFilter filter_workFlowId = new PropertyFilter("EQS_workflowid", workFlowId);
                    filters.add(filter_status);
                    filters.add(filter_workFlowId);
                    dataList = allRelationService.searchOrder(filters, "createDate", true);
                } else {
                    dataList = allRelationService.searchOrder(filters, "createDate", true);
                }
                for (TQhAllRelationEntity entity : dataList) {
                    gtasks = allRelationService.queryMyGtasks(entity.getSerialno(), entity.getDtoName());
                    if (gtasks != null) {
                        if (entity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                            Object obj = allRelationService.findByDto(entity.getDtoName(), entity.getSerialno());
                            gtasks.setWfName(StrUtil.concat(true, entity.getWfName(), "--", Reflections.getFieldValue(obj, "applytablename") + ""));
                        } else {
                            gtasks.setWfName(entity.getWfName());
                        }
                        conifgEntity = conifgService.findUnique(entity.getWorkflowid());
                        if ("4".equals(entity.getWorkstatus())) {
                            gtasks.setAuditAction(conifgEntity.getModaction());
                        } else if ("2".equals(entity.getWorkstatus())) {
                            gtasks.setAuditAction(conifgEntity.getAction());
                        }
                        gtasks.setSerialno(entity.getSerialno());
                        InterConfig config = new InterConfig();
                        config.setWorkFlowId(entity.getWorkflowid());
                        config.setProcessId(entity.getProcessid());
                        //獲取流程當前節點處理人
                        InterResult taskInfo = processService.currentTaskInfo(config);
                        TaskInfo info = taskInfo.getTaskInfoList().get(0);
                        WfNodeinfoEntity nodeInfoEntity = wfNodeinfoService.findByTaskName(info.getTaskName(), entity.getWorkflowid(), entity.getVersion());
                        gtasks.setCanBatch(nodeInfoEntity.getCanbatch());
                        gtasks.setTaskName(nodeInfoEntity.getNodealain());
                        //合併業務表和中間表
                        Map<String, Object> taskMap = BeanUtil.beanToMap(gtasks, false, false);
                        Map<String, Object> bunessMap = BeanUtil.beanToMap(allRelationService.findByDto(entity.getDtoName(), entity.getSerialno()), false, false);
                        /*if (bunessMap == null) {
                            page.setTotalCount(page.getTotalCount() - 1);
                            continue;
                        }*/
                        for (String key : bunessMap.keySet()) {
                            if ("workstatus".equals(key)) {
                                List<Dict> dtList = dictService.getDictByType("audit_status");
                                for (Dict dict : dtList) {
                                    if (taskMap.get(key).equals(dict.getValue())) {
                                        taskMap.put(key, dict.getLabel());
                                    }
                                }
                            }
                            if (!taskMap.containsKey(key)) {
                                //處理違紀嘉獎的單子
                                taskMap.put(key, bunessMap.get(key));
                            }
                        }
                        //是否有子表
                        if (StringUtils.isNotEmpty(conifgEntity.getDynfield02())) {
                            List<Object> dataChildList = allRelationService.findByListDto(conifgEntity.getDynfield02(), entity.getSerialno(), conifgEntity.getDynfield03());
                            Assert.notEmpty(dataChildList, String.format("任務編號：%s,實體：%s中不存在子數據", entity.getSerialno(), conifgEntity.getDynfield02()));
                            if (dataChildList != null && dataChildList.size() > 0) {
                                Map<String, Object> bunessChildMap = BeanUtil.beanToMap(dataChildList.get(0), false, false);
                                taskMap.putAll(bunessChildMap);
                                taskMap.put("childsNum", dataChildList.size());
                            }
                        }
                        List<TPubDynamicTableEntity> entityList = dynamicTableService.findByWorkflowId(workFlowId);
                        for (TPubDynamicTableEntity tableEntity : entityList) {
                            //是否需要字典替換
                            if ("1".equals(tableEntity.getIsTrans())) {
                                Assert.notNull(tableEntity.getDictCode(), "字典替換類型不可為空，請在T_PUB_DYNAMIC_TABLE配置【DICT_CODE】");
                                List<Dict> dtList = dictService.getDictByType(tableEntity.getDictCode());
                                for (Dict dict : dtList) {
                                    if (taskMap.get(tableEntity.getGridField()).equals(dict.getValue())) {
                                        taskMap.put(tableEntity.getGridField(), dict.getLabel());
                                    }
                                }
                            }
                        }
                        gtasksList.add(taskMap);
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return gtasksList;
    }

    @Transactional(readOnly = false)
    public Page<Gtasks> getAllMyDownTask(String empno, Page page, String serialno, String workstatus, String startDate, String endDate, String workFlowId, Boolean isNeedsBuness) {
        try {
            List<Object> gtasksList = new ArrayList<Object>();
            Gtasks gtasks = null;
            WfConifgEntity conifgEntity = null;
            List<TQhAllRelationEntity> dataList = new ArrayList<>();
//        List<TQhAllRelationEntity>   = allRelationService.getMyDownTask(UserUtil.getCurrentUser().getLoginName(), page);
            /**新舊工號兼容優化**/
            EntityNewAndOld newAndOld = OldJobNoToNewNo.getNewNo(empno);
            if (!(newAndOld.getNewEmpno().equals(newAndOld.getOldEmpno()))) {
                dataList.addAll(allRelationService.getMyFileSignDownTask(newAndOld.getNewEmpno() + "','" + newAndOld.getOldEmpno(), page, serialno, workstatus, startDate, endDate, workFlowId));
            } else {
                dataList = allRelationService.getMyFileSignDownTask(empno, page, serialno, workstatus, startDate, endDate, workFlowId);
            }
            /**新舊工號兼容優化 **/

            for (TQhAllRelationEntity entity : dataList) {
                gtasks = allRelationService.queryMyGtasks(entity.getSerialno(), entity.getDtoName());
                if (gtasks == null) {
                    continue;
                }
                if (entity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                    Object obj = allRelationService.findByDto(entity.getDtoName(), entity.getSerialno());
                    gtasks.setWfName(StrUtil.concat(true, entity.getWfName(), "--", Reflections.getFieldValue(obj, "applytablename") + ""));
                } else {
                    gtasks.setWfName(entity.getWfName());
                }
                conifgEntity = conifgService.findUnique(entity.getWorkflowid());
                gtasks.setAuditAction(conifgEntity.getDetailaction());
                gtasks.setSerialno(entity.getSerialno());
                gtasks.setType(conifgEntity.getAppType());
                if ("1".equals(conifgEntity.getAppType())) {
                    Dict dict2 = new Dict();
                    dict2.setType("entfrmIpebgIP_app");
                    dict2.setCodeUniq("entfrmIpebgIP_app01");
                    String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
                    gtasks.setAppAuditUrl(StrUtil.concat(true, fronUrl, conifgEntity.getAppDetailUrl(), "?serialno=", entity.getSerialno()));
                } else {
                    gtasks.setAppAuditUrl(StrUtil.concat(true, conifgEntity.getAppDetailUrl(), "?serialno=", entity.getSerialno()));
//                    gtasks.setAppDetailUrl(StrUtil.concat(true, conifgEntity.getAppDetailUrl(), "?serialno=", entity.getSerialno()));
                }
                InterConfig config = new InterConfig();
                gtasks.setUpdateDate(entity.getUpdateDate());
                config.setProcessId(entity.getProcessid());
                config.setWorkFlowId(entity.getWorkflowid());
                //獲取流程當前節點處理人
                InterResult taskInfo = processService.currentTaskInfo(config);
                gtasks.setTaskName(taskInfo.getTaskInfoList() == null ? "" : taskInfo.getTaskInfoList().get(0).getTaskName());
                /*List<Dict> dtList = dictService.getDictByType("audit_status");
                for (Dict dict : dtList) {
                    if (gtasks.getWorkstatus().equals(dict.getValue())) {
                        gtasks.setWorkstatus(dict.getLabel());
                    }
                }*/
                gtasksList.add(BeanUtil.beanToMap(gtasks));
            }
            page.setResult(gtasksList);
            page.setTotalCount(allRelationService.getMyFileSignDownTaskCount(empno, serialno, workstatus, startDate, endDate, workFlowId));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return page;
    }

    @Transactional(readOnly = false)
    public int getAllMyDownTaskCount(String empno, String serialno, String workstatus, String startDate, String endDate, String workFlowId) {
        int count = allRelationService.getMyFileSignDownTaskCount(empno, serialno, workstatus, startDate, endDate, workFlowId);
        return count;
    }

    /**
     * 方法描述: 通過流程標識獲取對應的任務
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/13  上午 08:09
     * @Return
     **/

    public List<Gtasks> getMyTaskByWorkId(String workFlowId, String status) throws Exception {
        List<Gtasks> gtasksList = new ArrayList<Gtasks>();
        Gtasks gtasks = null;
        List<String> processIds = this.getProcessIds(UserUtil.getCurrentUser().getLoginName());
//        processIds.add("101789953");
//        workFlowId="dzqh_xinzengxiangmuhuanbaoshouxubanlishenqingdan";
        WfConifgEntity conifgEntity = null;
        List<TQhAllRelationEntity> dataList = allRelationService.getMyTaskByWorkId(workFlowId, processIds, status);
        for (TQhAllRelationEntity entity : dataList) {
            gtasks = allRelationService.queryMyGtasks(entity.getProcessid(), entity.getDtoName());
            gtasks.setWfName(entity.getWfName());
            gtasks.setSerialno(entity.getSerialno());
            InterConfig config = new InterConfig();
            config.setProcessId(entity.getProcessid());
            config.setWorkFlowId(entity.getWorkflowid());
            conifgEntity = conifgService.findUnique(entity.getWorkflowid());
            gtasks.setAuditAction(conifgEntity.getAction());
            //獲取流程當前節點處理人
            InterResult taskInfo = processService.currentTaskInfo(config);
            gtasks.setTaskName(taskInfo.getTaskInfoList().get(0).getTaskName());
            gtasksList.add(gtasks);
        }
        return gtasksList;
    }

    private List<String> getProcessIds(String empno) {
        List<String> processIds = new ArrayList<String>();
        try {
            InterConfig config = new InterConfig();
            config.setUserId(empno);
            //查詢流程引擎的個人待辦
            InterResult interResult = processService.queryTaskList(config);
            List<WfConifgEntity> conifgEntityList = conifgService.getAll();
            if (interResult != null) {
                List<TaskInfo> taskList = interResult.getTaskInfoList();
                if (taskList != null && taskList.size() > 0) {
                    for (TaskInfo t : taskList) {
                        for (WfConifgEntity w : conifgEntityList) {
                            if (t.getPdId().startsWith(w.getWorkflowid())) {
                                processIds.add(t.getProcessId());
                                break;
                            }
                        }
                    }
                } else {
                    logger.info("not found user task");
                }
            }
            //查詢個人會簽待辦任務
            config = new InterConfig();
            config.setUserId(empno);
            interResult = processService.groupTasks(config);
            if (interResult != null) {
                List<TaskInfo> taskList = interResult.getTaskInfoList();
                if (taskList != null && taskList.size() > 0) {
                    for (TaskInfo t : taskList) {
                        for (WfConifgEntity w : conifgEntityList) {
                            if (t.getPdId().startsWith(w.getWorkflowid())) {
                                processIds.add(t.getProcessId());
                                break;
                            }
                        }
                    }
                } else {
                    logger.info("not found user group task");
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return processIds;
    }

    /**
     * 方法描述: 批量審合
     *
     * @Author: S6114648
     * @CreateDate: 2019/8/22  下午 04:24
     * @Return
     **/

    @Transactional(readOnly = false)
    public String completeTasks(List<String> serialno, String ip, String status, String attachidsremark, String reattachids, User user) {
        String reStr = Constant.RESULT.CODE_NO.getValue();
        for (int i = 0; i < serialno.size(); i++) {
            Map res = completeTask(serialno.get(i), ip, status, attachidsremark, reattachids, user, "");
            reStr = (String) res.get("result");
        }
        return reStr;
    }


    /**
     * 方法描述: 完成任務
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/10  下午 01:20
     * @Return
     **/
    @Transactional(readOnly = false)
    public Map completeTask(String serialno, String ip, String status, String attachidsremark, String reattachids, User userInfo, String remarkShow) {
        //status 2 重新提交  3 取消申請  0 通過 1 駁回
        Map map = new HashMap();
        String nodeName = "";
        String signNo = "";
        try {
            TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialno);
            Assert.notNull(relationEntity);
            CompleteTaskParameter parameter = new CompleteTaskParameter();
            parameter.setSerialno(serialno);
            parameter.setProcessId(relationEntity.getProcessid());
            parameter.setWorkFlowId(relationEntity.getWorkflowid());
            parameter.setIp(ip);
            parameter.setAuto(false);
            parameter.setAttachidsremark(attachidsremark);

            InterConfig config = new InterConfig();
            config.setProcessId(parameter.getProcessId());
            config.setWorkFlowId(relationEntity.getWorkflowid());
            //獲取流程當前節點處理人
            InterResult taskInfo = processService.currentTaskInfo(config);
            if ("SUCCESS".equals(taskInfo.getStatus())) {
                TaskInfo info = taskInfo.getTaskInfoList().get(0);
                signNo = info.getAssignee();
                WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(info.getTaskName(), parameter.getWorkFlowId(), relationEntity.getVersion());
                nodeName = entity.getColname();
                String result = "";
                List<String> proxUser = new ArrayList<>();
                //防止不是自己的單子審核通過
                //TODO 防止不是自己的單子審核通過
                List<TProxyUserinfoEntity> entity_prox = proxyUserinfoService.findProxUserByEmpno(userInfo.getLoginName());
                for (TProxyUserinfoEntity entityProx : entity_prox) {
                    proxUser.add(entityProx.getSuppliedempno());
                }
                /**新舊工號兼容優化**/
                EntityNewAndOld newAndOld = OldJobNoToNewNo.getNewNo(userInfo.getLoginName());
                if (!(newAndOld.getNewEmpno().equals(newAndOld.getOldEmpno()))) {
                    proxUser.add(newAndOld.getOldEmpno());
                    proxUser.add(newAndOld.getNewEmpno());
                } else {
                    proxUser.add(newAndOld.getNewEmpno());
                }
                /**新舊工號兼容優化**/
                if (proxUser.contains(info.getAssignee())) {
                    if ("0".equals(entity.getSigntype())) {
                        result = chargLogService.completeTaskForMobile(parameter, status, userInfo);
                    } else if ("1".equals(entity.getSigntype())) {
                        result = chargLogService.completeHqTaskForMobile(parameter, status, userInfo);
                    }
                    if (result.equals(Constant.RESULT.CODE_NO.getValue())) {
                        map.put("result", Constant.RESULT.CODE_NO.getValue());
                        return map;
                    } else {
                        try {
                            if (remarkShow != null && !"".equals(remarkShow)) {//審核成功 remarkShow不為空則當前表單為 文檔簽核申請單
                                signModelPositionService.updateRemarkShowType(serialno, entity.getColname(), info.getAssignee(), remarkShow);
                            }
                            //重新獲取當前工作流審核信息
                            InterResult taskInfoAuto = processService.currentTaskInfo(config);
                            TaskInfo infoAuto = null;
                            if (taskInfoAuto.getTaskInfoList() != null && "SUCCESS".equals(taskInfoAuto.getStatus())) {
                                infoAuto = taskInfoAuto.getTaskInfoList().get(0);
                            }
                            /*系統需求申請單審核調用專案管理系統*/
                            if (StringUtils.isNotEmpty(relationEntity.getDtoName()) && "WfsystemprocessEntity".equals(relationEntity.getDtoName()) && !"3".equals(result)) {
                                WfsystemprocessEntity wfsystemprocess = wfsystemprocessService.findBySerialno(parameter.getSerialno());
                                if (StringUtils.isNotEmpty(wfsystemprocess.getDockingSystem()) && wfsystemprocess.getDockingSystem().contains("zagl")) {
                                    wfsystemprocessService.sendManagerInfo(parameter.getSerialno(), status, info, userInfo);
                                }
                            }
                            /*文檔簽核申請單嵌入AI系統項目管理平台審核處理*/
                           wffilesignprocessService.sendManagerInfo(new FileSignParams(relationEntity.getSerialno(), infoAuto,status,attachidsremark,ip,relationEntity));
                            /*給智信發當前審核人信息*/
                            WfConifgEntity conifgEntity = conifgService.findUnique(relationEntity.getWorkflowid());
                            if ("Y".equals(conifgEntity.getWhetherApp())) {
                                String signStagus = "";
                                if ("0".equals(status)) {
                                    SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                                    Mail mail = new Mail();
                                    mail.setSerialno(parameter.getSerialno());
                                    signStagus = "signed";
                                    mail.setOrderstatus("2");
                                    mail.setAppAuditUrl("");
                                    mail.setDusername("");
                                    mail.setOrdertype("");
                                    User user = userService.getUser(signNo);
                                    //啟動流程時便易簽裡面有的單子發智信
                                    Dict except = dictService.getUniqueDictByType("dict_exceptType" + mail.getOrderstatus());
                                    sendMailUtil.sendZhiXing(mail, except, signNo, user, "signed");
                                } else if ("1".equals(status)) {
                                    SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                                    Mail mail = new Mail();
                                    mail.setSerialno(parameter.getSerialno());
                                    signStagus = "reject";
                                    mail.setOrderstatus("4");
                                    mail.setAppAuditUrl("");
                                    mail.setDusername("");
                                    mail.setOrdertype("");
                                    User user = userService.getUser(signNo);
                                    //啟動流程時便易簽裡面有的單子發智信
                                    Dict except = dictService.getUniqueDictByType("dict_exceptType" + mail.getOrderstatus());
                                    sendMailUtil.sendZhiXing(mail, except, signNo, user, "signed");
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                } else {  //移動端超時時會重複調用審核方法 如當前審核人不是登錄人 則單據已審核到下一節點，當前節點已審核成功
                    map.put("result", Constant.RESULT.CODE_COMPLETE.getValue());
                    map.put("nodeName", nodeName);
                    return map;
                }
                //對應窗口審核上傳的附件
                if (StringUtils.isNotEmpty(reattachids)) {
                    allRelationService.updateFormReattachids(serialno, reattachids, relationEntity.getDtoName());
                }
                //如果是完成則更新表單狀態及中間表狀態
                if (result == Constant.RESULT.CODE_COMPLETE.getValue()) {
                    allRelationService.updateFormStaticComplete(serialno, result, relationEntity.getDtoName());
                    relationEntity.setWorkstatus(result);
                    /*allRelationService.update(relationEntity);*/
                    allRelationService.updateEntityForMobile(relationEntity, userInfo.getLoginName());

                    WfConifgEntity conifgEntity = conifgService.findUnique(relationEntity.getWorkflowid());
                    String userEmpno = allRelationService.findUserEmpno(relationEntity.getDtoName(), serialno);

                    /**新舊工號兼容優化**/
                    newAndOld = OldJobNoToNewNo.getNewNo(userEmpno);
                    /**新舊工號兼容優化**/
                    User user = userService.getUser(userEmpno);
                    //查詢是否有代理人
                    List<TProxyUserinfoEntity> audit_prox = proxyUserinfoService.findProxUserByEmpnoNew(userEmpno);
                    if (audit_prox != null && audit_prox.size() > 0) {
                        user = userService.getUser(audit_prox.get(0).getSupplyempno());
                    }
                    Gtasks gtasks = allRelationService.queryMyGtasks(relationEntity.getSerialno(), relationEntity.getDtoName());
                    Mail mail = new Mail();
                    mail.setUsername(gtasks.getMakername());
                    mail.setSystemname(dictService.getDictByTypeAndVlaue("sys_property", "sys_name").getLabel());
                    mail.setDusername(gtasks.getMakername());
                    mail.setChargerman(userInfo.getName());
                    mail.setOrdertype(relationEntity.getWfName());
                    mail.setSerialno(serialno);
                    mail.setOrderstatus(result);
                    mail.setUrl(dictService.get(560).getValue());
                    mail.setUrlip(dictService.get(561).getValue());
                    if ("ODSMS".equals(relationEntity.getFormFrom())) {
                        mail.setSystemname(dictService.getUniqueDictByTypeAndCode("other_sys_name", "ODSMS").getValue());
                        mail.setUrl(dictService.getUniqueDictByTypeAndCode("other_sys_url", "ODSMS").getValue());
                        mail.setUrlip(dictService.getUniqueDictByTypeAndCode("other_sys_url_ip_through", "ODSMS").getValue());
                    }
                    //保存發送記錄
                    TPubMailrecordEntity mailrecordEntity = new TPubMailrecordEntity();
                    mailrecordEntity.setChargerman(mail.getChargerman());
                    mailrecordEntity.setDusername(mail.getDusername());
                    mailrecordEntity.setOrderstatus(mail.getOrderstatus());
                    mailrecordEntity.setOrdertype(mail.getOrdertype());
                    mailrecordEntity.setSerialno(mail.getSerialno());
                    mailrecordEntity.setUsername(mail.getUsername());
                    mailrecordEntity.setUsermail(mail.getUsermail());
                    mailrecordEntity.setSendStatus("0");
                    mailrecordEntity.setEmpno(user.getLoginName());
                    mailrecordEntity.setUrl(mail.getUrl());
                    mailrecordEntity.setUrlip(mail.getUrlip());
                    mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                    if (user != null && user.getEmail() != null) {
                        mail.setUsermail(user.getEmail());
                        mailrecordEntity.setUsermail(mail.getUsermail());
                        mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                        //判斷是否及時發送
//                        if (!"0".equals(user.getEmailset())) {
                        String sendResult = "";
                        if (relationEntity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                            try {
                                Object obj = allRelationService.findByDto(relationEntity.getDtoName(), relationEntity.getSerialno());
                                TPubFileobjectEntity fileobjectEntity = fileobjectService.findByIdIgnoreComma(Reflections.getFieldValue(obj, "attachids2") + "");
                                //由於郵箱接收附件有8M限制，大於限制不發送附件
                                if (ObjectUtil.isNotNull(fileobjectEntity)) {
                                    if (fileobjectEntity.getSizez() > 8388608) {
                                        sendResult = new SendMailUtil().sendMail(mail);
                                    } else {
                                        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//                                        HttpUtil.download("http://localhost:8081/newEsign/entrfrm/fileSignDownloadPdfAddImg222/" + fileobjectEntity.getId() + "?empNo=" + fileobjectEntity.getCreateBy(), outputStream, true);
                                        HttpUtil.download(dictService.get(558).getValue() + Global.getConfig("sendMailAuditPath") + "/entrfrm/fileSignDownloadPdfAddImg222/" + fileobjectEntity.getId() + "?empNo=" + fileobjectEntity.getCreateBy(), outputStream, true);
                                        mail.setFile(FileUtil.readBytes(new ByteArrayInputStream(outputStream.toByteArray()), fileobjectEntity.getName()));
                                        sendResult = SendDocMailUtil.sendMail(mail);
                                    }
                                } else {
                                    sendResult = new SendMailUtil().sendMail(mail);
                                }
                            } catch (Exception e) {
                                logger.info("發送附件文檔出錯,serialno:" + relationEntity.getSerialno(), e);
                            }
                        } else {
                            sendResult = new SendMailUtil().sendMail(mail);
                        }
                        if ("0".equals(sendResult)) {
                            //發送成功，更新標誌
                            mailrecordEntity.setSendStatus("1");
                            mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                        }
                        //聚會推送消息
                        SendMailUtil sendMailUtil = new SendMailUtil();
                        String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                        String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                        String empNos = user.getLoginName();
                        String sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos);
                        if ("0".equals(sendJuhuiResult)) {
                            mailrecordEntity.setSendJuhuiStatus("1");
                            mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                        }
                        if ("Y".equals(conifgEntity.getWhetherApp())) {
                            String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                            if ("1".equals(conifgEntity.getAppType())) {
                                Dict dict2 = new Dict();
                                dict2.setType("entfrmIpebgIP_app");
                                dict2.setCodeUniq("entfrmIpebgIP_app01");
                                String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
                                mail.setAppAuditUrl(StrUtil.concat(true, fronUrl, conifgEntity.getAppDetailUrl(), "?serialno=", mail.getSerialno()));
                            } else {
                                mail.setAppAuditUrl(StrUtil.concat(true, conifgEntity.getAppDetailUrl(), "?serialno=", mail.getSerialno()));
                            }
                            if (relationEntity.getWorkflowid().contains("dzqh_gongnengchengshifabushenqingdan")) {
                                sendMailUtil.sendJuihuiMobileApi(mail, "Smack", url, empNos);
                            } else {
                                sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos);
                                if (relationEntity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                                    Object obj = allRelationService.findByDto(relationEntity.getDtoName(), relationEntity.getSerialno());
                                    String applytablename = (String) Reflections.getFieldValue(obj, "applytablename");
                                    mail.setOrdertype(applytablename);
                                }
                                //表單審核完成的消息不需推送智信
                               /* Dict except=dictService.getUniqueDictByType("dict_exceptType"+mail.getOrderstatus());
                                sendMailUtil.sendZhiXing(mail,except,empNos,user);*/
                                if ("Y".equals(user.getNotification())) {
                                    sendMailUtil.sendAppMessageBar(mail, empNos, user);
                                }
                            }
                        }
//                        }

                    }
//                    if (user != null && relationEntity.getWorkflowid().contains("dzqh_gongnengchengshifabushenqingdan")) {
//                        String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
//                        String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
//                        String empNos = user.getLoginName();
//                        String sendJuhuiResult = new SendMailUtil().sendJuihui(mail, typeApp, url, empNos);
//                        if ("0".equals(sendJuhuiResult)) {
//                            mailrecordEntity.setSendJuhuiStatus("1");
//                            mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
//                        }
//                        mail.setUsermail(user.getEmail());
//                        String sendResult = new SendMailUtil().sendMail(mail);
//                        mailrecordEntity.setSendStatus("2"); //發送失敗
//                        if ("0".equals(sendResult)) {
//                            //發送成功，更新標誌
//                            mailrecordEntity.setSendStatus("1");
//                        }
//                        mailrecordEntity.setUsermail(mail.getUsermail());
//                        mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
//                    }
                    /*系統需求申請單審核調用專案管理系統*/
                    if (StringUtils.isNotEmpty(relationEntity.getDtoName()) && "WfsystemprocessEntity".equals(relationEntity.getDtoName())) {
                        WfsystemprocessEntity wfsystemprocess = wfsystemprocessService.findBySerialno(parameter.getSerialno());
                        if (StringUtils.isNotEmpty(wfsystemprocess.getDockingSystem()) && wfsystemprocess.getDockingSystem().contains("zagl")) {
                            wfsystemprocessService.sendManagerInfo(parameter.getSerialno(), status, info, userInfo);
                        }
                        if (StringUtils.isNotEmpty(wfsystemprocess.getDockingSystem()) && wfsystemprocess.getDockingSystem().contains("xmgl")) {
                            wfsystemprocessService.sendProjectInfo(wfsystemprocess, userInfo);
                        }
                    }
                    /*文檔簽核申請單嵌入AI系統項目管理平台審核處理*/
                    wffilesignprocessService.sendManagerInfo(new FileSignParams(relationEntity.getSerialno(), info,status,attachidsremark,ip,relationEntity));
                    //系統需求申請單簽核完成郵件/聚會消息提醒至規劃工程師
                    if (relationEntity.getWorkflowid().contains("dzqh_xitongxuqiushenqing")) {
                        wfsystemprocessService.sendMailToGuiHua(relationEntity, serialno, userInfo);
                    }


                } else {
                    //獲取審核人員郵箱，發送郵件信息用
                    taskInfo = processService.currentTaskInfo(config);
                    info = taskInfo.getTaskInfoList().get(0);

                    /**新舊工號兼容優化**/
                    newAndOld = OldJobNoToNewNo.getNewNo(info.getAssignee());
                    /**新舊工號兼容優化**/
                    User user = userService.getUser(newAndOld.getNewEmpno());
                    //查詢是否有代理人
                    List<TProxyUserinfoEntity> audit_prox = proxyUserinfoService.findProxUserByEmpnoNew(newAndOld.getNewEmpno());
                    if (audit_prox != null && audit_prox.size() > 0) {
                        user = userService.getUser(audit_prox.get(0).getSupplyempno());
                    }
//                    WfNodeinfoEntity nodeinfo = wfNodeinfoService.findProxTaskName(info.getTaskName(), relationEntity.getWorkflowid(), relationEntity.getVersion());
//                    relationEntity.setNodename(nodeinfo.getNodealain());
                    allRelationService.updateEntityForMobile(relationEntity, user.getLoginName());

                    WfConifgEntity conifgEntity = conifgService.findUnique(relationEntity.getWorkflowid());
                    String validStr = UUID.randomUUID().toString().replace("-", "");

                    Gtasks gtasks = allRelationService.queryMyGtasks(relationEntity.getSerialno(), relationEntity.getDtoName());

                    //查詢用戶信息
                    TQhUserformhsEntity userformhsEntity = tQhUserformhsService.findByEmpnoIgnoreIdStatus(user.getLoginName());
                    Mail mail = new Mail();
                    mail.setChargerman(userInfo.getName());
                    mail.setSystemname(dictService.getDictByTypeAndVlaue("sys_property", "sys_name").getLabel());
                    mail.setDusername(gtasks.getMakername());
                    mail.setOrdertype(relationEntity.getWfName());
                    mail.setSerialno(serialno);
                    mail.setUrl(dictService.get(560).getValue());
                    mail.setUrlip(dictService.get(561).getValue());
                    if (user != null && user.getEmail() != null) {
                        mail.setUsermail(user.getEmail());
                    }
                    if ("0".equals(status)) {
                        //通過
                        mail.setOrderstatus("2");
                        mail.setUsername(userformhsEntity == null ? "" : userformhsEntity.getEmpname());
                        mail.setFreeloginurl(dictService.get(558).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getAction() + "/" + serialno);
                        mail.setFreeloginurlip(dictService.get(559).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getAction() + "/" + serialno);

                    } else {
                        //駁回
                        mail.setOrderstatus("4");
                        mail.setUsername(gtasks.getMakername() != null ? gtasks.getMakername() : userformhsEntity.getEmpname());
                        mail.setFreeloginurl(dictService.get(558).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getModaction() + "/" + serialno);
                        mail.setFreeloginurlip(dictService.get(559).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getModaction() + "/" + serialno);

                    }
                    if ("ODSMS".equals(relationEntity.getFormFrom())) {
                        mail.setSystemname(dictService.getUniqueDictByTypeAndCode("other_sys_name", "ODSMS").getValue());
                        mail.setUrl(dictService.getUniqueDictByTypeAndCode("other_sys_url", "ODSMS").getValue());
                        mail.setUrlip(dictService.getUniqueDictByTypeAndCode("other_sys_url_ip_through", "ODSMS").getValue());
                        mail.setFreeloginurl(dictService.getUniqueDictByTypeAndCode("other_sys_url_login", "ODSMS").getValue() + user.getLoginName());
                        mail.setFreeloginurlip(dictService.getUniqueDictByTypeAndCode("other_sys_url_ip", "ODSMS").getValue() + user.getLoginName());
                    }
                    //保存發送記錄
                    TPubMailrecordEntity mailrecordEntity = new TPubMailrecordEntity();
                    mailrecordEntity.setChargerman(mail.getChargerman());
                    mailrecordEntity.setDusername(mail.getDusername());
                    mailrecordEntity.setOrderstatus(mail.getOrderstatus());
                    mailrecordEntity.setOrdertype(mail.getOrdertype());
                    mailrecordEntity.setSerialno(mail.getSerialno());
                    mailrecordEntity.setUsername(mail.getUsername());
                    mailrecordEntity.setSendStatus("0");
                    mailrecordEntity.setEmpno(user.getLoginName());
                    mailrecordEntity.setUrl(mail.getUrl());
                    mailrecordEntity.setUrlip(mail.getUrlip());
                    mailrecordEntity.setFreeloginurl(mail.getFreeloginurl());
                    mailrecordEntity.setFreeloginurlip(mail.getFreeloginurlip());
                    mailrecordEntity.setValidStr(validStr);
                    mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());

                    //保存聚會APP發送記錄
                    /*TPubJuhuiApprecordEntity appRecord = new TPubJuhuiApprecordEntity();
                    appRecord.setChargerman(mail.getChargerman());
                    appRecord.setDusername(mail.getDusername());
                    appRecord.setOrderstatus(mail.getOrderstatus());
                    appRecord.setOrdertype(mail.getOrdertype());
                    appRecord.setSerialno(mail.getSerialno());
                    appRecord.setUsername(mail.getUsername());
                    appRecord.setSendStatus("0");
                    appRecord.setEmpno(user.getLoginName());
                    appRecord.setUrl(mail.getUrl());
                    appRecord.setUrlip(mail.getUrlip());
                    appRecord.setFreeloginurl(mail.getFreeloginurl());
                    appRecord.setFreeloginurlip(mail.getFreeloginurlip());
                    appRecord.setValidStr(validStr);
                    juhuiApprecordService.saveForMobile(appRecord,userInfo.getLoginName());*/
                    if ("1".equals(conifgEntity.getAppType())) {
                        Dict dict2 = new Dict();
                        dict2.setType("entfrmIpebgIP_app");
                        dict2.setCodeUniq("entfrmIpebgIP_app01");
                        String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
                        mail.setAppAuditUrl(StrUtil.concat(true, fronUrl, conifgEntity.getAppAuditUrl(), "?serialno=", mail.getSerialno()));
                    } else {
                        mail.setAppAuditUrl(StrUtil.concat(true, conifgEntity.getAppAuditUrl(), "?serialno=", mail.getSerialno()));
                    }
                    if (user != null && user.getEmail() != null && (!relationEntity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan"))) {
                        mail.setUsermail(user.getEmail());
                        mailrecordEntity.setUsermail(mail.getUsermail());
                        mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                        String sendResult = new SendMailUtilProx().sendMail(mail, user);
                        if ("0".equals(sendResult)) {
                            //發送成功，更新標誌
                            mailrecordEntity.setSendStatus("1");
                            mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                        }
                        //聚會推送消息
                        String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                        String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                        String empNos = user.getLoginName();
                        SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                        String sendJuhuiResult = null;
//                            if (relationEntity.getWorkflowid().contains("dzqh_gongnengchengshifabushenqingdan") && "0".equals(status)) {
//                                sendMailUtil.sendJuihuiMobileApi(mail, "Smack", url, empNos);
//                            }
                        if ("0".equals(status)) {//只有通過時發送手機聚會消息，駁回只發送郵件通知用戶
                            sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos, user);
                            if ("0".equals(sendJuhuiResult)) {
                                mailrecordEntity.setSendJuhuiStatus("1");
                                mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                            }
                            if ("Y".equals(conifgEntity.getWhetherApp())) {
                                String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                                if (relationEntity.getWorkflowid().contains("dzqh_gongnengchengshifabushenqingdan")) {
                                    sendMailUtil.sendJuihuiMobileApi(mail, "Smack", url, empNos, user);
                                    sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos, user);
                                    if (relationEntity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                                        Object obj = allRelationService.findByDto(relationEntity.getDtoName(), relationEntity.getSerialno());
                                        String applytablename = (String) Reflections.getFieldValue(obj, "applytablename");
                                        mail.setOrdertype(applytablename);
                                    }
                                } else {
                                    sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos, user);
                                    if (relationEntity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                                        Object obj = allRelationService.findByDto(relationEntity.getDtoName(), relationEntity.getSerialno());
                                        String applytablename = (String) Reflections.getFieldValue(obj, "applytablename");
                                        mail.setOrdertype(applytablename);
                                    }
                                }
                                Dict except = dictService.getUniqueDictByType("dict_exceptType" + mail.getOrderstatus());
                                sendMailUtil.sendZhiXing(mail, except, empNos, user, "signing");
                                if ("Y".equals(user.getNotification())) {
                                    sendMailUtil.sendAppMessageBar(mail, empNos, user);
                                }
                            }
                        }
                        //聚會APP推送消息
                            /*String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                            String sendJuhuiResultApp=new SendMailUtil().sendJuihui(mail,typeApp,url,empNos);
                            if("0".equals(sendJuhuiResultApp)){
                                appRecord.setSendJuhuiStatus("1");
                                juhuiApprecordService.saveForMobile(appRecord,userInfo.getLoginName());
                            }*/

                    } else {
                        Object obj = allRelationService.findByDto(relationEntity.getDtoName(), relationEntity.getSerialno());
                        Boolean isApp = ftpInfoService.whetherAppSign(relationEntity.getCreateBy());

                        if (isApp && "0".equals(status)) {
                            String sendResult = new SendMailUtilProx().sendMail(mail, user);
                            if ("0".equals(sendResult)) {
                                //發送成功，更新標誌
                                mailrecordEntity.setSendStatus("1");
                                mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                            }
                            //聚會推送消息
                            String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                            String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                            String empNos = user.getLoginName();
                            SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                            String sendJuhuiResult = null;
                            sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos, user);
                            if ("0".equals(sendJuhuiResult)) {
                                mailrecordEntity.setSendJuhuiStatus("1");
                                mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                            }
                            String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                            url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                            sendMailUtil = new SendMailUtilProx();
                            empNos = user.getLoginName();
                            sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos, user);
                            if (relationEntity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                                String applytablename = (String) Reflections.getFieldValue(obj, "applytablename");
                                mail.setOrdertype(applytablename);
                            }
                            //文檔簽核的單子不管是否isApp都發智信
                            Dict except = dictService.getUniqueDictByType("dict_exceptType" + mail.getOrderstatus());
                            sendMailUtil.sendZhiXing(mail, except, empNos, user, "signing");
                            if ("Y".equals(user.getNotification())) {
                                sendMailUtil.sendAppMessageBar(mail, empNos, user);
                            }
                        } else {
                            String sendResult = new SendMailUtilProx().sendMail(mail, user);
                            if ("0".equals(sendResult)) {
                                //發送成功，更新標誌
                                mailrecordEntity.setSendStatus("1");
                                mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                            }
                            //聚會推送消息
                            String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                            String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                            String empNos = user.getLoginName();
                            SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                            String sendJuhuiResult = null;
                            sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos, user);
                            if ("0".equals(sendJuhuiResult)) {
                                mailrecordEntity.setSendJuhuiStatus("1");
                                mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                            }
                            //文檔簽核的單子只要通過，不管是否isApp都發智信
                            if ("0".equals(status)) {
                                if (relationEntity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                                    String applytablename = (String) Reflections.getFieldValue(obj, "applytablename");
                                    mail.setOrdertype(applytablename);
                                }
                                Dict except = dictService.getUniqueDictByType("dict_exceptType" + mail.getOrderstatus());
                                sendMailUtil.sendZhiXing(mail, except, empNos, user, "signing");
                                if ("Y".equals(user.getNotification())) {
                                    sendMailUtil.sendAppMessageBar(mail, empNos, user);
                                }
                            }

                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            map.put("result", Constant.RESULT.CODE_NO.getValue());
            return map;
        }
        map.put("result", Constant.RESULT.CODE_YES.getValue());
        map.put("nodeName", nodeName);
        return map;
    }

    @Transactional(readOnly = false)
    public String processStart(String serialno) {
        TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialno);
//        List<Map<String, Object>> mapList= allRelationService.findToMapBySql(" from "+relationEntity.getDtoName()+" where serialno=:serialno",serialno);
        List<Map<String, Object>> mapList = allRelationService.findToMapBySql(serialno);
        return "";
    }

    /**
     * 方法描述: 啟動流程
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/8  上午 10:32
     * @Return
     **/
    @Transactional(readOnly = false)
    public String processStart(WorkFlowEntity obj, Object entity) throws Exception {
        try {
            InterConfig config = new InterConfig();
            //設置工單發啟人
//            config.setApplyUserId("admin");
            config.setApplyUserId(Reflections.getFieldValue(entity, "makerno") == null ? UserUtil.getCurrentUser().getLoginName() : Reflections.getFieldValue(entity, "makerno").toString());
            //設置流程id
            config.setWorkFlowId(obj.getWorkflowId());
            //初始化工單各節點處理人
            config.setTaskUsers(obj.getTaskUsers());
            //設置會簽簽核人信息
            config.setHuiqian(obj.getHuiqian());
            //設置流程整體參數
            String varStr = this.getWfVariables(obj.getWorkflowId());
//            String userGrade = conifgService.callProcWithResult("{CALL p_qh_getUserGrade('?0')}", obj.getEmpNo()).get(0).toString();
            varStr = varStr.replace("$USERGRADE$", "1");
            logger.info(varStr);
            config.setVariables(varStr);
            InterResult interResult = processService.processStart(config);
            //創建會簽節點
            this.createHuiqianTaskInfor(obj, interResult.getProcessId(), entity);

            AutoCompleteTask(interResult.getProcessId(),obj.getWorkflowId());
            //發送郵件

            config = new InterConfig();
            config.setProcessId(interResult.getProcessId());
            config.setWorkFlowId(obj.getWorkflowId());
            InterResult taskInfo = processService.currentTaskInfo(config);
            if ("SUCCESS".equals(taskInfo.getStatus())) {
                TaskInfo info = taskInfo.getTaskInfoList().get(0);

                /**新舊工號兼容優化**/
                EntityNewAndOld newAndOld = OldJobNoToNewNo.getNewNo(info.getAssignee());
                /**新舊工號兼容優化**/
                User user = userService.getUser(newAndOld.getNewEmpno());
                //查詢是否有代理人
                List<TProxyUserinfoEntity> audit_prox = proxyUserinfoService.findProxUserByEmpnoNew(newAndOld.getNewEmpno());
                if (audit_prox != null && audit_prox.size() > 0) {
                    user = userService.getUser(audit_prox.get(0).getSupplyempno());
                }
                WfConifgEntity conifgEntity = conifgService.findUnique(obj.getWorkflowId());
                String validStr = UUID.randomUUID().toString().replace("-", "");
                Gtasks gtasks = allRelationService.queryMyGtasks(obj.getSerialNo(), entity.getClass().getName());
                Mail mail = new Mail();
                mail.setChargerman(UserUtil.getCurrentUser().getName());
                mail.setSystemname(dictService.getDictByTypeAndVlaue("sys_property", "sys_name").getLabel());
                mail.setDusername(gtasks.getMakername());
                mail.setOrdertype(conifgService.findUnique(obj.getWorkflowId()).getWorkflowname());
                mail.setSerialno(obj.getSerialNo());
                mail.setOrderstatus("2");
                mail.setFreeloginurl(dictService.get(558).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getAction() + "/" + obj.getSerialNo());
                mail.setFreeloginurlip(dictService.get(559).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getAction() + "/" + obj.getSerialNo());
                mail.setUrl(dictService.get(560).getValue());
                mail.setUrlip(dictService.get(561).getValue());
                //保存發送記錄
                TPubMailrecordEntity mailrecordEntity = new TPubMailrecordEntity();
                mailrecordEntity.setChargerman(mail.getChargerman());
                mailrecordEntity.setDusername(mail.getDusername());
                mailrecordEntity.setOrderstatus(mail.getOrderstatus());
                mailrecordEntity.setOrdertype(mail.getOrdertype());
                mailrecordEntity.setSerialno(mail.getSerialno());
                mailrecordEntity.setSendStatus("0");
                mailrecordEntity.setEmpno(user.getLoginName());
                mailrecordEntity.setUrl(mail.getUrl());
                mailrecordEntity.setUrlip(mail.getUrlip());
                mailrecordEntity.setFreeloginurl(mail.getFreeloginurl());
                mailrecordEntity.setFreeloginurlip(mail.getFreeloginurlip());
                mailrecordEntity.setValidStr(validStr);
                mailrecordService.saveEntity(mailrecordEntity);
                if (user != null && user.getEmail() != null) {
                    mail.setUsermail(user.getEmail());
                    mail.setUsername(user.getName());
                    mailrecordEntity.setUsername(mail.getUsername());
                    mailrecordEntity.setUsermail(mail.getUsermail());
                    mailrecordService.saveEntity(mailrecordEntity);
                    if ("1".equals(conifgEntity.getAppType())) {
                        Dict dict2 = new Dict();
                        dict2.setType("entfrmIpebgIP_app");
                        dict2.setCodeUniq("entfrmIpebgIP_app01");
                        String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
                        mail.setAppAuditUrl(StrUtil.concat(true, fronUrl, conifgEntity.getAppAuditUrl(), "?serialno=", mail.getSerialno()));
                    } else {
                        mail.setAppAuditUrl(StrUtil.concat(true, conifgEntity.getAppAuditUrl(), "?serialno=", mail.getSerialno()));
                    }
                    if (!obj.getWorkflowId().contains("dzqh_wendangqianheshenqingdan")) {
                        String sendResult = new SendMailUtilProx().sendMail(mail, user);
                        if ("0".equals(sendResult)) {
                            //發送成功，更新標誌
                            mailrecordEntity.setSendStatus("1");
                            mailrecordService.saveEntity(mailrecordEntity);
                        }
                        //聚會推送消息
                        String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                        String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                        String empNos = user.getLoginName();
                        SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                        String sendJuhuiResult = null;
//                                if (obj.getWorkflowId().contains("dzqh_gongnengchengshifabushenqingdan")) {
//                                    sendMailUtil.sendJuihuiMobileApi(mail, "Smack", url, empNos);
//                                }

                        sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos, user);
                        if ("0".equals(sendJuhuiResult)) {
                            mailrecordEntity.setSendJuhuiStatus("1");
                            mailrecordService.saveEntity(mailrecordEntity);
                        }
                        if ("Y".equals(conifgEntity.getWhetherApp())) {
                            String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                            if (obj.getWorkflowId().contains("dzqh_gongnengchengshifabushenqingdan")) {
                                sendMailUtil.sendJuihuiMobileApi(mail, "Smack", url, empNos, user);
                            } else {
                                sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos, user);
                            }
                            //啟動流程時便易簽裡面有的單子發智信
                            Dict except = dictService.getUniqueDictByType("dict_exceptType" + mail.getOrderstatus());
                            sendMailUtil.sendZhiXing(mail, except, empNos, user, "signing");
                            if ("Y".equals(user.getNotification())) {
                                sendMailUtil.sendAppMessageBar(mail, empNos, user);
                            }
                        }
                    } else {
                        if (obj.getWorkflowId().contains("dzqh_wendangqianheshenqingdan")) {
                            Boolean isApp = ftpInfoService.whetherAppSign(UserUtil.getCurrentUser().getLoginName());

                            String ynFixedPosition = (String) Reflections.getFieldValue(entity, "ynFixedPosition");
                            //2是  1否
                            if (isApp && "2".equals(ynFixedPosition)) {
                                //2是  1否
                                String sendResult = new SendMailUtilProx().sendMail(mail, user);
                                if ("0".equals(sendResult)) {
                                    //發送成功，更新標誌
                                    mailrecordEntity.setSendStatus("1");
                                    mailrecordService.saveEntity(mailrecordEntity);
                                }
                                //聚會推送消息
                                String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                                String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                String empNos = user.getLoginName();
                                SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                                String sendJuhuiResult = null;
                                sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos, user);
                                if ("0".equals(sendJuhuiResult)) {
                                    mailrecordEntity.setSendJuhuiStatus("1");
                                    mailrecordService.saveEntity(mailrecordEntity);
                                }
                                String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                                url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                sendMailUtil = new SendMailUtilProx();
                                empNos = user.getLoginName();
                                sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos, user);
                                //不管isApp,文檔簽核的都發智信
                                if (obj.getWorkflowId().contains("dzqh_wendangqianheshenqingdan")) {
                                    Object obj1 = allRelationService.findByDto(entity.getClass().getName(), obj.getSerialNo());
                                    String applytablename = (String) Reflections.getFieldValue(obj1, "applytablename");
                                    mail.setOrdertype(applytablename);
                                }
                                Dict except = dictService.getUniqueDictByType("dict_exceptType" + mail.getOrderstatus());
                                sendMailUtil.sendZhiXing(mail, except, empNos, user, "signing");
                                if ("Y".equals(user.getNotification())) {
                                    sendMailUtil.sendAppMessageBar(mail, empNos, user);
                                }
                            } else if (isApp && "1".equals(ynFixedPosition)) {
                                String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                                String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                                String empNos = user.getLoginName();
                                sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos, user);
                                if (obj.getWorkflowId().contains("dzqh_wendangqianheshenqingdan")) {
                                    Object obj1 = allRelationService.findByDto(entity.getClass().getName(), obj.getSerialNo());
                                    String applytablename = (String) Reflections.getFieldValue(obj1, "applytablename");
                                    mail.setOrdertype(applytablename);
                                }
                                Dict except = dictService.getUniqueDictByType("dict_exceptType" + mail.getOrderstatus());
                                sendMailUtil.sendZhiXing(mail, except, empNos, user, "signing");
                                if ("Y".equals(user.getNotification())) {
                                    sendMailUtil.sendAppMessageBar(mail, empNos, user);
                                }
                            } else {
                                String sendResult = new SendMailUtilProx().sendMail(mail, user);
                                if ("0".equals(sendResult)) {
                                    //發送成功，更新標誌
                                    mailrecordEntity.setSendStatus("1");
                                    mailrecordService.saveEntity(mailrecordEntity);
                                }
                                //聚會推送消息
                                String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                                String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                String empNos = user.getLoginName();
                                SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                                String sendJuhuiResult = null;
                                sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos, user);
                                if ("0".equals(sendJuhuiResult)) {
                                    mailrecordEntity.setSendJuhuiStatus("1");
                                    mailrecordService.saveEntity(mailrecordEntity);
                                }
                                if (obj.getWorkflowId().contains("dzqh_wendangqianheshenqingdan")) {
                                    Object obj1 = allRelationService.findByDto(entity.getClass().getName(), obj.getSerialNo());
                                    String applytablename = (String) Reflections.getFieldValue(obj1, "applytablename");
                                    mail.setOrdertype(applytablename);
                                }
                                Dict except = dictService.getUniqueDictByType("dict_exceptType" + mail.getOrderstatus());
                                sendMailUtil.sendZhiXing(mail, except, empNos, user, "signing");
                                if ("Y".equals(user.getNotification())) {
                                    sendMailUtil.sendAppMessageBar(mail, empNos, user);
                                }
                            }
                        } else {
                            if ("Y".equals(conifgEntity.getWhetherApp())) {
                                String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                                String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                String empNos = user.getLoginName();
                                SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                                sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos, user);
                                if (obj.getWorkflowId().contains("dzqh_wendangqianheshenqingdan")) {
                                    Object obj1 = allRelationService.findByDto(entity.getClass().getName(), obj.getSerialNo());
                                    String applytablename = (String) Reflections.getFieldValue(obj1, "applytablename");
                                    mail.setOrdertype(applytablename);
                                }
                                Dict except = dictService.getUniqueDictByType("dict_exceptType" + mail.getOrderstatus());
                                sendMailUtil.sendZhiXing(mail, except, empNos, user, "signing");
                                if ("Y".equals(user.getNotification())) {
                                    sendMailUtil.sendAppMessageBar(mail, empNos, user);
                                }
                            }
                        }
                    }
                }
            }
//            createHuiqianTaskInfor(obj, interResult.getProcessId());
            return interResult.getProcessId();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 方法描述: 創建會簽節點
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/8  上午 10:32
     * @Return
     **/
    @Transactional(readOnly = false)
    public void createHuiqianTaskInfor(WorkFlowEntity obj, String processId, Object o) {
        try {
            List<WfNodeinfoEntity> entityList = wfNodeinfoService.findForHuiqian(obj.getWorkflowId());
            if (entityList != null && entityList.size() > 0) {
                InterConfig config = null;
                for (WfNodeinfoEntity n : entityList) {
                    if ("1".equals(n.getSigntype())) {
                        config = new InterConfig();
                        config.setProcessId(processId);
                        config.setTaskName(n.getNodename());
                        String hQRule = getHuiQianRule(obj.getWorkflowId(), n.getNodeid());
                        Object cstr = Reflections.getFieldValue(o, n.getColname());
                        String hChargeStr = (cstr == null ? "" : cstr.toString());
                        if (StringUtils.isNotEmpty(hChargeStr)) {
                            String[] cs = hChargeStr.split(",");
                            hQRule = hQRule.replace("$PERSONNUM$", cs.length + "");
                        }
                        logger.info(hQRule);
                        config.setOtherParam(hQRule);
                        config.setWorkFlowId(n.getWorkflowid());
                        processService.createHuiqianTaskInfor(config);
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 方法描述: 獲取流程級配置參數
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/8  上午 11:51
     * @Return
     **/

    public String getWfVariables(String workFlowId) {
        StringBuffer sb = new StringBuffer();
        try {
            WfConifgEntity wf = conifgService.findUnique(workFlowId);
            Assert.notNull(wf);
            List<WfConfigparamEntity> ps = wfConfigparamService.findByFilters(workFlowId);
            Assert.notEmpty(ps);
            //組織流程級別的參數
            for (WfConfigparamEntity p : ps) {
                sb.append(p.getParamename() + ",")
                        .append(p.getParamvalue() == null ? "" : p.getParamvalue() + ",")
                        .append(p.getParamtype() == null ? "" : p.getParamtype() + ";");
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return sb.toString();
    }

    /**
     * 方法描述:
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/10  上午 10:42
     * @Return
     **/

    public String getHuiQianRule(String workflowId, String nodeId) {
        StringBuffer sb = new StringBuffer();
        try {
            WfNoderuleEntity ne = wfNoderuleService.findUniqByNodeId(nodeId, workflowId);
            Assert.notNull(ne);
            sb.append(ne.getTotalpeople()).append("&")
                    .append(ne.getPassrate()).append("&")
                    .append(ne.getVoterule()).append("&")
                    .append(ne.getTaskstatusvariable()).append("&")
                    .append(ne.getTaskcompletevariable()).append("&");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return sb.toString();
    }

    /**
     * 方法描述: 自動推動任務進行
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/9  下午 06:15
     * @Return
     **/

    public String AutoCompleteTask(String processId,String workFlowId) {
        try {
            String auto = dictService.get(6).getValue();
            //查詢流程狀態
            InterConfig config = new InterConfig();
            config.setProcessId(processId);
            config.setWorkFlowId(workFlowId);
            InterResult interResult = processService.processStatus(config);
            //如果是運行狀態
            if ("SUCCESS".equals(interResult.getStatus()) && interResult.getRunning().equals("true")) {
                config = new InterConfig();
                config.setProcessId(processId);
                config.setWorkFlowId(workFlowId);
                InterResult taskInfo = processService.currentTaskInfo(config);
                while (auto.equals(taskInfo.getTaskInfoList().get(0).getAssignee())) {
                    config = new InterConfig();
                    config.setUserId(auto);
                    config.setTaskId(taskInfo.getTaskInfoList().get(0).getTaskId());
                    config.setWorkFlowId(workFlowId);
                    processService.completeAutoTask(config);
                    //再查詢下個節點
                    config = new InterConfig();
                    config.setProcessId(processId);
                    config.setWorkFlowId(workFlowId);
                    taskInfo = processService.currentTaskInfo(config);
                }
            } else {
                //返回完成標識
                return Constant.RESULT.CODE_COMPLETE.getValue();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return Constant.RESULT.CODE_YES.getValue();
    }

    public void setBeanAutorBlank(Object obj, String workFlowId) {
        try {
            String auto = dictService.get(6).getValue();
            List<WfNodeinfoEntity> entityList = wfNodeinfoService.findByFilters(workFlowId);
            for (WfNodeinfoEntity entity : entityList) {
                String value = (String) Reflections.getFieldValue(obj, entity.getColname());
                if (auto.equals(value)) {
                    Reflections.setFieldValue(obj, entity.getColname(), "");
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 方法描述: 取消表單
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/10  上午 08:27
     * @Return
     **/
    @Transactional(readOnly = false)
    public String cancelTask(String serialNo, String ip) {
        try {
            TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialNo);
//        return Constant.RESULT.CODE_YES.getValue();
            InterConfig config = new InterConfig();
            config.setProcessId(relationEntity.getProcessid());
            config.setWorkFlowId(relationEntity.getWorkflowid());
            InterResult interResult = processService.processEnd(config);
            if ("SUCCESS".equals(interResult.getStatus())) {

                //添加簽核記錄
                //獲取流程當前節點處理人
                TQhChargelogEntity entity = new TQhChargelogEntity();
                entity.setChargename(UserUtil.getCurrentUser().getName());
//                entity.setChargename("S6112942");
                entity.setChargenode("填單人修改");
                entity.setSerialno(serialNo);
                entity.setChargeno(UserUtil.getCurrentUser().getLoginName());
                entity.setWorkflowid(relationEntity.getWorkflowid());
                entity.setOperateip(ip);
                entity.setIspass("取消申請");
                tQhChargelogService.save(entity);

                relationEntity.setWorkstatus(Constant.RESULT.CODE_CANCLE.getValue());
                allRelationService.update(relationEntity);
                allRelationService.updateFormStatic(relationEntity.getSerialno(), Constant.RESULT.CODE_CANCLE.getValue(), relationEntity.getDtoName());
                return Constant.RESULT.CODE_YES.getValue();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return Constant.RESULT.CODE_NO.getValue();
        }
        return Constant.RESULT.CODE_NO.getValue();
    }

    /**
     * 方法描述: 自動設置為空的審核人為AUTOFIN_DZQH
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/10  上午 08:36
     * @Return
     **/

    public void setWfAutorBlank(Object obj, String workFlowId) {
        try {
            String auto = dictService.get(6).getValue();
            List<WfNodeinfoEntity> entityList = wfNodeinfoService.findByFilters(workFlowId);
            for (WfNodeinfoEntity entity : entityList) {
                String value = (String) Reflections.getFieldValue(obj, entity.getColname());
                if (StringUtils.isBlank(value)) {
                    Reflections.setFieldValue(obj, entity.getColname(), auto);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 方法描述: 獲取簽核路徑
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/10  上午 09:35
     * @Return
     **/

    public String getChargeNodeInfo(Object obj, String workFlowId, String processId) {
        String result = "";
        StringBuffer stringBuffer = null;
        Boolean flag = true;
        try {
            String auto = dictService.get(6).getValue();
            stringBuffer = new StringBuffer();
            List<WfNodeinfoEntity> entityList = wfNodeinfoService.findByFilters(workFlowId);
            InterConfig config = new InterConfig();
            config.setProcessId(processId);
            config.setWorkFlowId(workFlowId);
            InterResult taskInfo = processService.currentTaskInfo(config);
            if ("SUCCESS".equals(taskInfo.getStatus())) {
                for (WfNodeinfoEntity entity : entityList) {
                    if ("makerno".equals(entity.getColname())) {
                        continue;
                    }
                    String value = (String) Reflections.getFieldValue(obj, entity.getColname());
                    if (taskInfo.getTaskInfoList() != null && taskInfo.getTaskInfoList().get(0) != null && taskInfo.getTaskInfoList().get(0).getTaskName().equals(entity.getNodename()) && flag) {
                        stringBuffer.append("<strong><font size=\"3\" color=red>" + entity.getNodealain() + "(");
                        if (auto.equals(value)) {
                            stringBuffer.append("/" + ")->");
                        } else {
                            String valueName = (String) Reflections.getFieldValue(obj, entity.getColname().replace("no", "name"));
                            stringBuffer.append(value + "/" + valueName + ")</font></strong>->");
                        }
                        flag = false;
                    } else {
                        stringBuffer.append(entity.getNodealain() + "(");
                        if (auto.equals(value)) {
                            stringBuffer.append("/" + ")->");
                        } else {
                            String valueName = (String) Reflections.getFieldValue(obj, entity.getColname().replace("no", "name"));
                            stringBuffer.append(value + "/" + valueName + ")->");
                        }
                    }
                }
                Assert.hasText(stringBuffer.toString());
                result = stringBuffer.toString().substring(0, stringBuffer.length() - 2);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 方法描述: 獲取簽核路徑,新舊流程兼容的方法
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/10  上午 09:35
     * @Return
     **/

    public String getChargeNodeInfoByVersion(Object obj, TQhAllRelationEntity rentity) {
        String result = "";
        StringBuffer stringBuffer = null;
        Boolean flag = true;
        try {
            String auto = dictService.get(6).getValue();
            stringBuffer = new StringBuffer();
            List<WfNodeinfoEntity> entityList = wfNodeinfoService.findByFiltersAndVersion(rentity);
            InterConfig config = new InterConfig();
            config.setProcessId(rentity.getProcessid());
            config.setWorkFlowId(rentity.getWorkflowid());
            InterResult taskInfo = processService.currentTaskInfo(config);
            if ("SUCCESS".equals(taskInfo.getStatus())) {
                for (WfNodeinfoEntity entity : entityList) {
                    if ("makerno".equals(entity.getColname())) {
                        continue;
                    }
                    String value = (String) Reflections.getFieldValue(obj, entity.getColname());
                    if (taskInfo.getTaskInfoList() != null && taskInfo.getTaskInfoList().get(0) != null && taskInfo.getTaskInfoList().get(0).getTaskName().equals(entity.getNodename()) && flag) {
                        stringBuffer.append("<strong><font size=\"3\" color=red>" + entity.getNodealain() + "(");
                        if (auto.equals(value)) {
                            stringBuffer.append("/" + ")->");
                        } else {
                            String valueName = (String) Reflections.getFieldValue(obj, entity.getColname().replace("no", "name"));
                            stringBuffer.append(value + "/" + valueName + ")</font></strong>->");
                        }
                        flag = false;
                    } else {
                        stringBuffer.append(entity.getNodealain() + "(");
                        if (auto.equals(value)) {
                            stringBuffer.append("/" + ")->");
                        } else {
                            String valueName = (String) Reflections.getFieldValue(obj, entity.getColname().replace("no", "name"));
                            stringBuffer.append(value + "/" + valueName + ")->");
                        }
                    }
                }
                Assert.hasText(stringBuffer.toString());
                result = stringBuffer.toString().substring(0, stringBuffer.length() - 2);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 方法描述: 獲取上線單小網頁簽核路徑
     *
     * @Author: S7198867
     * @CreateDate: 2021/2/1 上午 09:35
     * @Return
     **/

    public String getChargeNodeInfoByVersionForOnline(Object obj, TQhAllRelationEntity rentity) {
        String result = "";
        StringBuffer stringBuffer = null;
        Boolean flag = true;
        try {
            String auto = dictService.get(6).getValue();
            stringBuffer = new StringBuffer();
            List<WfNodeinfoEntity> entityList = wfNodeinfoService.findByFiltersAndVersion(rentity);
            InterConfig config = new InterConfig();
            config.setProcessId(rentity.getProcessid());
            config.setWorkFlowId(rentity.getWorkflowid());
            if (rentity.getProcessid() != null && rentity.getProcessid() != "") {
                InterResult taskInfo = processService.currentTaskInfo(config);
                if ("SUCCESS".equals(taskInfo.getStatus())) {
                    for (WfNodeinfoEntity entity : entityList) {
                        if ("makerno".equals(entity.getColname())) {
                            continue;
                        }
                        String value = (String) Reflections.getFieldValue(obj, entity.getColname());
                        String valueName = (String) Reflections.getFieldValue(obj, entity.getColname().replace("no", "name"));
                        List<String> workflowIds = Arrays.asList(Constant.WORKFLOWID_PROX.split(","));
                        if (workflowIds.stream().anyMatch(workflowId -> rentity.getWorkflowid().contains(workflowId))) {
                            List<TProxyFormUserinfoEntity> proxyFormUserinfoEntities = formUserinfoService.findProxUserByLogCreateDate(rentity.getCreateDate(), entity.getNodealain(), rentity.getWorkflowid());
                            if (CollectionUtil.isNotEmpty(proxyFormUserinfoEntities)) {
                                for (TProxyFormUserinfoEntity entity1 : proxyFormUserinfoEntities) {
                                    if (value.contains(entity1.getSupplyempno())) {
                                        value = value.replace(entity1.getSupplyempno(), entity1.getSuppliedempno());
                                        valueName = valueName.replace(entity1.getSupplyusername(), entity1.getSuppliedusername());
                                    }
                                }
                            }
                        }
                        if (taskInfo.getTaskInfoList() != null && taskInfo.getTaskInfoList().get(0) != null && taskInfo.getTaskInfoList().get(0).getTaskName().equals(entity.getNodename()) && flag) {
                            stringBuffer.append("<font size=\"3\" color=\"red\">");
                            if (auto.equals(value)) {
                                stringBuffer.append("(" + ")->");
                            } else {
                                //String valueName = (String) Reflections.getFieldValue(obj, entity.getColname().replace("no", "name"));
                                stringBuffer.append(value + "(" + valueName + ")</font>->");
                            }
                            flag = false;
                        } else {
                            if (auto.equals(value)) {
                                stringBuffer.append("(" + ")->");
                            } else {
                                //String valueName = (String) Reflections.getFieldValue(obj, entity.getColname().replace("no", "name"));
                                stringBuffer.append(value + "(" + valueName + ")->");
                            }
                        }
                    }
                    Assert.hasText(stringBuffer.toString());
                    result = stringBuffer.toString().substring(0, stringBuffer.length() - 2);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 方法描述: 獲取簽核路徑安卓
     *
     * @Author: S7198867
     * @CreateDate: 2018/10/10  上午 09:35
     * @Return
     **/

    public WffilesignprocesspathDto getChargeNodeInfoByVersionForFileEsign(Object obj, TQhAllRelationEntity rentity) {
        String result = "";
        StringBuffer stringBuffer = null;
        WffilesignprocesspathDto wffilesignprocesspathDto = new WffilesignprocesspathDto();
        int temp = 0;
        try {
            String auto = dictService.get(6).getValue();
            stringBuffer = new StringBuffer();
            List<WfNodeinfoEntity> entityList = wfNodeinfoService.findByFiltersAndVersion(rentity);
            InterConfig config = new InterConfig();
            config.setProcessId(rentity.getProcessid());
            config.setWorkFlowId(rentity.getWorkflowid());
            InterResult taskInfo = processService.currentTaskInfo(config);
            if ("SUCCESS".equals(taskInfo.getStatus())) {
                for (WfNodeinfoEntity entity : entityList) {
                    if ("makerno".equals(entity.getColname())) {
                        continue;
                    }
                    String value = (String) Reflections.getFieldValue(obj, entity.getColname());
                    if (!auto.equals(value)) {
                        String valueName = (String) Reflections.getFieldValue(obj, entity.getColname().replace("no", "name"));
                        String valueArray[] = value.split(",");
                        String valueNameArray[] = valueName.split(",");
                        for (int i = 0; i < valueArray.length; i++) {
                            if (taskInfo.getTaskInfoList() != null && taskInfo.getTaskInfoList().get(0) != null && taskInfo.getTaskInfoList().get(0).getTaskName().equals(entity.getNodename()) && taskInfo.getTaskInfoList().get(0).getAssignee().equals(valueArray[i])) {
                                wffilesignprocesspathDto.setAuditEmpNoNow(valueArray[i]);
                                wffilesignprocesspathDto.setAuditNode(temp);
                            }
                            stringBuffer.append(valueArray[i] + "(" + valueNameArray[i] + "),");
                            temp = temp + 1;
                        }
                    }
                }
                Assert.hasText(stringBuffer.toString());
                result = stringBuffer.toString().substring(0, stringBuffer.length() - 1);
                wffilesignprocesspathDto.setAuditPath(result);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return wffilesignprocesspathDto;
    }

    /**
     * 方法描述: 返回流程圖片
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/13  下午 01:20
     * @Return
     **/

    public String getImgUrl(String processId) {
        String imgPath = "";
        imgPath = dictService.get(301005).getValue() + "?processId=" + processId + "&t=" + UUID.randomUUID();
        return imgPath;
    }

    /**
     * 方法描述: 查詢簽核節點參數信息，顯示按鈕
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/13  下午 04:53
     * @Return
     **/

    public List<WfNodeparamEntity> queryNodeparam(String serialNo) {
        List<WfNodeparamEntity> entityList = new ArrayList<>();
        try {
            TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialNo);
            InterConfig config = new InterConfig();
            config.setProcessId(relationEntity.getProcessid());
            config.setWorkFlowId(relationEntity.getWorkflowid());
            //獲取流程當前節點處理人
            InterResult taskInfo = processService.currentTaskInfo(config);
            if ("SUCCESS".equals(taskInfo.getStatus())) {
                if (taskInfo.getTaskInfoList() == null) {
                    return entityList;
                }
                TaskInfo info = taskInfo.getTaskInfoList().get(0);
                WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(info.getTaskName(), relationEntity.getWorkflowid(), relationEntity.getVersion());
                entityList = nodeparamService.queryNodeInfos(entity.getNodeid(), relationEntity.getVersion());
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return entityList;
    }

    /**
     * 方法描述: 獲取簽核記錄
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/16  上午 08:20
     * @Return
     **/

    public Page<TQhChargelogEntity> queryChargeLog(Page<TQhChargelogEntity> page, List<PropertyFilter> filters) {
        Page<TQhChargelogEntity> pageResult = chargelogService.search(page, filters);
        return pageResult;
    }

    /**
     * 方法描述: 獲取簽核記錄 通過單號
     * S7198867
     **/

    public List<TQhChargelogEntity> queryChargeLogForMobile(String serialno) {
        List<TQhChargelogEntity> list = chargelogService.queryChargeLogByData(serialno);
        return list;
    }

    /**
     * 方法描述: 獲取當前記錄的簽核節點和簽核人
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/19  下午 04:58
     * @Return
     **/

    public TaskNode getNodeInfo(String serialNo) {
        TaskNode node = new TaskNode();
        TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialNo);
        //只獲取簽核中和駁回的
        try {
            if (relationEntity != null) {
                int nodeSize = wfNodeinfoService.findByFilters(relationEntity.getWorkflowid()).size();
                if (relationEntity != null && ("2".equals(relationEntity.getWorkstatus()) || "4".equals(relationEntity.getWorkstatus()))) {
                    InterConfig config = new InterConfig();
                    config.setProcessId(relationEntity.getProcessid());
                    config.setWorkFlowId(relationEntity.getWorkflowid());
                    //獲取流程當前節點處理人
                    InterResult taskInfo = processService.currentTaskInfo(config);
                    if (taskInfo != null && "SUCCESS".equals(taskInfo.getStatus()) && taskInfo.getTaskInfoList() != null && taskInfo.getTaskInfoList().size() > 0) {
                        TaskInfo info = taskInfo.getTaskInfoList().get(0);
                        if (info != null) {
                            WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(info.getTaskName(), relationEntity.getWorkflowid(), relationEntity.getVersion());
                            if (entity != null) {
                                node.setNodeName(entity.getNodealain());
                                node.setAuditProgress((Math.ceil(entity.getOrderby() * 100 / nodeSize)) + "");
                            }
                            TQhUserformhsEntity tentity = tQhUserformhsService.findByEmpno(info.getAssignee());
                            node.setAuditUser(info.getAssignee());
                            User user = userService.getUser(info.getAssignee());
                            if (user != null && user.getEmail() != null) {
                                node.setUserEmail(user.getEmail());
                            }
                            if (user != null && user.getPhone() != null) {
                                node.setUserPhone(user.getPhone());
                            }
                        } else {
                            node.setAuditUser("");
                            node.setNodeName("");
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return node;
    }

    /**
     * 方法描述: 通過workflowid查詢配置信息
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/22  下午 04:40
     * @Return
     **/

    public WfConifgEntity findByWorkFlowId(String workflowid) {
        return conifgService.findUnique(workflowid);
    }

    /**
     * 方法描述:  獲取當前節點名稱
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/29  下午 03:32
     * @Return
     **/

    public String getNodeName(String processId,String workflowId) {
        try {
            InterConfig config = new InterConfig();
            config.setProcessId(processId);
            config.setWorkFlowId(workflowId);
            //獲取流程當前節點處理人
            InterResult taskInfo = processService.currentTaskInfo(config);
            if ("SUCCESS".equals(taskInfo.getStatus()) && taskInfo.getTaskInfoList() != null) {
                TaskInfo info = taskInfo.getTaskInfoList().get(0);
                return info.getTaskName();
            } else {
                return null;
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 方法描述: 獲取當前簽核人
     *
     * @Author: S6114648
     * @CreateDate: 2018/11/9  上午 10:28
     * @Return
     **/

    public String getAssigneeInfo(String serialNo) {
        try {
            TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialNo);
            //只獲取簽核中和駁回的
            if ("2".equals(relationEntity.getWorkstatus()) || "4".equals(relationEntity.getWorkstatus())) {
                InterConfig config = new InterConfig();
                config.setProcessId(relationEntity.getProcessid());
                config.setWorkFlowId(relationEntity.getWorkflowid());
                //獲取流程當前節點處理人
                InterResult taskInfo = processService.currentTaskInfo(config);
                if ("SUCCESS".equals(taskInfo.getStatus())) {
                    if (taskInfo.getTaskInfoList() != null && taskInfo.getTaskInfoList().size() > 0) {
                        TaskInfo info = taskInfo.getTaskInfoList().get(0);
                        if (info != null) {
                            WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(info.getTaskName(), relationEntity.getWorkflowid(), relationEntity.getVersion());
                            return info.getAssignee();
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 方法描述: 獲取當前節點排序
     *
     * @Author: S6114648
     * @CreateDate: 上午 09:08
     * @Return
     **/

    public String getNodeOrder(String serialno) {
        try {
            TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialno);
            InterConfig config = new InterConfig();
            config.setProcessId(relationEntity.getProcessid());
            config.setWorkFlowId(relationEntity.getWorkflowid());
            //獲取流程當前節點處理人
            InterResult taskInfo = processService.currentTaskInfo(config);
            WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(taskInfo.getTaskInfoList().get(0).getTaskName(), relationEntity.getWorkflowid(), relationEntity.getVersion());
            return entity.getOrderby() + "";
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return "";
        }
    }

    /**
     * 方法描述: 獲取當前節點審核主管工號
     *
     * @Author: S6114648
     * @CreateDate: 上午 09:08
     * @Return
     **/

    public String getNodeUserNo(String serialno) {
        try {
            TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialno);
            InterConfig config = new InterConfig();
            config.setProcessId(relationEntity.getProcessid());
            config.setWorkFlowId(relationEntity.getWorkflowid());
            //獲取流程當前節點處理人
            InterResult taskInfo = processService.currentTaskInfo(config);
            return taskInfo.getTaskInfoList().get(0).getAssignee() + "";
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return "";
        }
    }

    public WfNodeinfoEntity getCurrentNode(String serialno) {
        try {
            TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialno);
            InterConfig config = new InterConfig();
            config.setProcessId(relationEntity.getProcessid());
            config.setWorkFlowId(relationEntity.getWorkflowid());
            //獲取流程當前節點處理人
            InterResult taskInfo = processService.currentTaskInfo(config);
            if (taskInfo != null && "SUCCESS".equals(taskInfo.getStatus()) && taskInfo.getTaskInfoList() != null && taskInfo.getTaskInfoList().size() > 0) {
                WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(taskInfo.getTaskInfoList().get(0).getTaskName(), relationEntity.getWorkflowid(), relationEntity.getVersion());
                entity.setDynfield01(taskInfo.getTaskInfoList().get(0).getAssignee());
                entity.setDynfield02(relationEntity.getWorkstatus());
                return entity;
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 方法描述: 獲取我的承辦
     *
     * @Author: S6073061
     * @CreateDate: 2023/11/9
     * @Return
     **/
    @Transactional(readOnly = false)
    public Page<Gtasks> getAllMyMakeTask(String empno, Page page, String serialno, String workstatus, String startDate, String endDate, String workFlowId, Boolean isNeedsBuness) {
        try {
            List<Object> gtasksList = new ArrayList<Object>();
            Gtasks gtasks = null;
            WfConifgEntity conifgEntity = null;
            List<TQhAllRelationEntity> dataList = new ArrayList<>();
//        List<TQhAllRelationEntity>   = allRelationService.getMyDownTask(UserUtil.getCurrentUser().getLoginName(), page);
            /**新舊工號兼容優化**/
//            EntityNewAndOld newAndOld = OldJobNoToNewNo.getNewNo(empno);
//            if (!(newAndOld.getNewEmpno().equals(newAndOld.getOldEmpno()))) {
//                dataList.addAll(allRelationService.getMyFileMakeTask(newAndOld.getNewEmpno() + "','" + newAndOld.getOldEmpno(), page, serialno, workstatus, startDate, endDate, workFlowId));
//            } else {
            dataList = allRelationService.getMyFileMakeTask(empno, page, serialno, workstatus, startDate, endDate, workFlowId);
//            }
            /**新舊工號兼容優化 **/

            for (TQhAllRelationEntity entity : dataList) {
                gtasks = allRelationService.queryMyGtasks(entity.getSerialno(), entity.getDtoName());
                if (gtasks == null) {
                    continue;
                }
                if (entity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                    Object obj = allRelationService.findByDto(entity.getDtoName(), entity.getSerialno());
                    gtasks.setWfName(StrUtil.concat(true, entity.getWfName(), "--", Reflections.getFieldValue(obj, "applytablename") + ""));
                } else {
                    gtasks.setWfName(entity.getWfName());
                }
                conifgEntity = conifgService.findUnique(entity.getWorkflowid());
                gtasks.setAuditAction(conifgEntity.getDetailaction());
                gtasks.setSerialno(entity.getSerialno());
                gtasks.setType(conifgEntity.getAppType());
                if ("1".equals(conifgEntity.getAppType())) {
                    Dict dict2 = new Dict();
                    dict2.setType("entfrmIpebgIP_app");
                    dict2.setCodeUniq("entfrmIpebgIP_app01");
                    String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
                    gtasks.setAppAuditUrl(StrUtil.concat(true, fronUrl, conifgEntity.getAppDetailUrl(), "?serialno=", entity.getSerialno()));
                } else {
                    gtasks.setAppAuditUrl(StrUtil.concat(true, conifgEntity.getAppDetailUrl(), "?serialno=", entity.getSerialno()));
//                    gtasks.setAppDetailUrl(StrUtil.concat(true, conifgEntity.getAppDetailUrl(), "?serialno=", entity.getSerialno()));
                }
                InterConfig config = new InterConfig();
                gtasks.setUpdateDate(entity.getUpdateDate());
                config.setProcessId(entity.getProcessid());
                config.setWorkFlowId(entity.getWorkflowid());
                //獲取流程當前節點處理人
                if (entity.getProcessid() != null && !"".equals(entity.getProcessid())) {
                    InterResult taskInfo = processService.currentTaskInfo(config);
                    gtasks.setTaskName(taskInfo.getTaskInfoList() == null ? "" : taskInfo.getTaskInfoList().get(0).getTaskName());
                    if (taskInfo.getTaskInfoList() != null && taskInfo.getTaskInfoList().size() > 0 && "SUCCESS".equals(taskInfo.getStatus())) {
                        gtasks.setChargeNo(taskInfo.getTaskInfoList().get(0).getAssignee());
                        gtasks.setChargeName(userformhsService.findByEmpno(taskInfo.getTaskInfoList().get(0).getAssignee()).getEmpname());
                    }
                }
                if ("4".equals(gtasks.getWorkstatus())) {
                    List<TQhChargelogEntity> chargelogs = tQhChargelogService.findLastRejectByDataDesc(entity.getSerialno());
                    TQhChargelogEntity chargelog = new TQhChargelogEntity();
                    if (chargelogs != null && chargelogs.size() > 0) {
                        chargelog = chargelogs.get(0);
                        gtasks.setChargeNo(chargelog.getChargeno());
                        gtasks.setChargeName(chargelog.getChargename());
                    }
                }
                /*List<Dict> dtList = dictService.getDictByType("audit_status");
                for (Dict dict : dtList) {
                    if (gtasks.getWorkstatus().equals(dict.getValue())) {
                        gtasks.setWorkstatus(dict.getLabel());
                    }
                }*/
                gtasksList.add(BeanUtil.beanToMap(gtasks));
            }
            page.setResult(gtasksList);
            page.setTotalCount(allRelationService.getMyFileMakeTaskCount(empno, serialno, workstatus, startDate, endDate, workFlowId));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return page;
    }

    @Transactional(readOnly = false)
    public int getAllMyMakeTaskCount(String empno, String serialno, String workstatus, String startDate, String endDate, String workFlowId) {
        int count = allRelationService.getMyFileMakeTaskCount(empno, serialno, workstatus, startDate, endDate, workFlowId);
        return count;
    }
}
