package com.foxconn.ipebg.buessness.workflow.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.foxconn.ipebg.buessness.common.entity.*;
import com.foxconn.ipebg.buessness.common.service.*;
import com.foxconn.ipebg.buessness.generalAffairs.entity.WfreceiptandreturnEntity;
import com.foxconn.ipebg.buessness.generalAffairs.service.WfreceiptandreturnService;
import com.foxconn.ipebg.buessness.humanCapital.entity.*;
import com.foxconn.ipebg.buessness.humanCapital.service.*;
import com.foxconn.ipebg.buessness.information.dto.FileSignParams;
import com.foxconn.ipebg.buessness.information.entity.*;
import com.foxconn.ipebg.buessness.information.service.*;
import com.foxconn.ipebg.buessness.workflow.entity.*;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.utils.*;
import com.foxconn.ipebg.system.dto.EntityNewAndOld;
import com.foxconn.ipebg.system.entity.*;
import com.foxconn.ipebg.system.service.*;
import com.foxconn.ipebg.system.utils.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;


import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Company foxconn 重新提交(合併失敗，重新提交)
 * Copyright (C) 2004-2018 All Rights Reserved.
 */
@Service
@Transactional(readOnly = true)
public class WorkFlowService {
    private static final Logger logger = LoggerFactory.getLogger(WorkFlowService.class);
    @Autowired
    private ProcessLocalService processService;
    @Autowired
    private TQhAllRelationService allRelationService;
    @Autowired
    private WfConifgService conifgService;
    @Autowired
    private TQhChargelogService chargelogService;
    @Autowired
    private WfNodeinfoService wfNodeinfoService;
    @Autowired
    private WfConfigparamService wfConfigparamService;
    @Autowired
    private WfNoderuleService wfNoderuleService;
    @Autowired
    private TQhChargelogService tQhChargelogService;
    @Autowired
    private DictService dictService;
    @Autowired
    private WfNodeparamService nodeparamService;
    @Autowired
    private TQhUserformhsService tQhUserformhsService;
    @Autowired
    private TPubMailrecordService mailrecordService;
    @Autowired
    private UserService userService;
    @Autowired
    private TQhFactoryidconfigService tQhFactoryidconfigService;
    @Autowired
    private TPubAreabaseinfoService areabaseinfoService;
    @Autowired
    private SendSmsService sendSmsService;
    @Autowired
    private ScheduleJobService scheduleJobService;
    @Autowired
    private TossAndTurnFhwService2 tossAndTurnFhwService;
    @Autowired
    private TProxyUserinfoService proxyUserinfoService;
    @Lazy
    @Autowired
    private WfreceiptandreturnService wfreceiptandreturnService;
    @Autowired
    private TPubDynamicTableService dynamicTableService;
    @Lazy
    @Autowired
    private TiptopProcessService tiptopProcessService;
    @Lazy
    @Autowired
    private WfptalkprocessService wfptalkprocessService;
    @Lazy
    @Autowired
    private WfinternetemailprocessService wfinternetemailprocessService;
    @Lazy
    @Autowired
    private WfspecialnetprocessService wfspecialnetprocessService;
    @Lazy
    @Autowired
    private WfSupernotesService wfSupernotesService;
    @Lazy
    @Autowired
    private WfwwwprocessService wfwwwprocessService;
    @Lazy
    @Autowired
    private WfsslvpnprocessService wfsslvpnprocessService;
    @Lazy
    @Autowired
    private WfpcprivilegeprocessService wfpcprivilegeprocessService;
    @Lazy
    @Autowired
    private WfSoftinstallProcessService wfSoftinstallProcessService;
    @Lazy
    @Autowired
    private WfvlanprocessService wfvlanprocessService;
    @Lazy
    @Autowired
    private WfIlegalProcessesService wfIlegalProcessesService;
    @Lazy
    @Autowired
    private WfCommendProcessesService wfCommendProcessesService;
    @Lazy
    @Autowired
    private WfvlanitemService wfvlanitemService;
    @Lazy
    @Autowired
    private WfpcprivilegeitemsService wfpcprivilegeitemsService;
    @Lazy
    @Autowired
    private WfadaccountprocessService wfadaccountprocessService;
    @Lazy
    @Autowired
    private WfadaccountitemService wfadaccountitemService;
    @Lazy
    @Autowired
    private WfprintauthinfoprocessService wfprintauthinfoprocessService;
    @Lazy
    @Autowired
    private WfprintauthitemsService wfprintauthitemsService;
    @Lazy
    @Autowired
    private WfmarryprocessesService wfmarryprocessesService;
    @Lazy
    @Autowired
    private WfbornprocessesService wfbornprocessesService;
    @Lazy
    @Autowired
    private WffuzhiprocessService wffuzhiprocessService;
    @Autowired
    private RequisitionListService requisitionListService;
    @Autowired
    private FtpInfoService ftpInfoService;
    @Autowired
    private TPubFileobjectService fileobjectService;
    @Autowired
    @Lazy
    private ChargLogService chargLogService;
    @Autowired
    private SignModelPositionService signModelPositionService;
    @Autowired
    @Lazy
    private WfsystemprocessService wfsystemprocessService;

    @Autowired
    private NotProcessingNowService notProcessingNowService;
    @Autowired
    @Lazy
    private WffilesignprocessService wffilesignprocessService;
    @Autowired
    @Lazy
    private TProxyFormUserinfoService formUserinfoService;

    /**
     * 方法描述: 獲取待辦
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/8  下午 03:09
     * @Return
     **/
    @Transactional(readOnly = false)
    public List<MyTaskCount> myTask(String empno) {
        User user = null;
        if (StringUtils.isNotEmpty(empno)) {
            user = new User();
            user.setLoginName(empno);
        } else {
            user = UserUtil.getCurrentUser();
        }
        List<MyTaskCount> entityList = new ArrayList<MyTaskCount>();
        List<MyTaskCount> entityBackList = new ArrayList<MyTaskCount>();
        //--------查詢代理相關信息---------------
        List<String> processIds =  proxyUserinfoService.findUserAllProcessId(user.getLoginName());
        //--------查詢代理相關信息 ----------------
      /*  String urlString = dictService.getUniqueDictByType("entfrmtodotask").getValue();
        String result = HttpUtil.get(urlString, JSONUtil.createObj().put("assignee", UserUtil.getCurrentUser().getLoginName()));
        JSONArray array = JSONUtil.parseArray(result);
        List<String> listProcessIds = array.toList(String.class);
        processIds.addAll(listProcessIds);*/
        //查詢中間表
        if (processIds.size() > 0) {
            int subSize = 1000;
            int subCount = processIds.size();
            int subPageTotal = (subCount / subSize) + ((subCount % subSize > 0) ? 1 : 0);
            for (int i = 0, len = subPageTotal - 1; i <= len; i++) {
                int fromIndex = i * subSize;
                int toIndex = ((i == len) ? subCount : ((i + 1) * subSize));
                entityList.addAll(allRelationService.queryIndexTask(processIds.subList(fromIndex, toIndex)));
            }
            for (int i = 0, len = subPageTotal - 1; i <= len; i++) {
                int fromIndex = i * subSize;
                int toIndex = ((i == len) ? subCount : ((i + 1) * subSize));
                entityList.addAll(allRelationService.queryIndexBackTask(processIds.subList(fromIndex, toIndex)));
            }
            logger.info(String.format("user %s falcon found %s,system found %s", user.getLoginName(), processIds.size(), entityList.size()));
        }
        return entityList;
    }

    /**
     * 方法描述: 獲取所有待辦任務
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/13  上午 08:14
     * @Return
     **/

    @Transactional(readOnly = false)
    public Page<Object> getAllMyTask(String workFlowId, String status, Page page, List<PropertyFilter> filters) {
        try {
            List<Object> gtasksList = new ArrayList<Object>();
            Gtasks gtasks = null;
            List<String> processIds = proxyUserinfoService.findUserAllProcessId(UserUtil.getCurrentUser().getLoginName());
            //--------查詢代理相關信息----------------
            if (processIds.size() > 0) {
//        processIds.add("101789953");
                PropertyFilter filter = new PropertyFilter("INS_processid", processIds);
                filters.add(filter);
                WfConifgEntity conifgEntity = null;
                List<TQhAllRelationEntity> dataList = null;
                PropertyFilter filter_neq_workflowid = new PropertyFilter("WDQHS_workflowid", "dzqh_wendangqianheshenqingdan");
//                filters.add(filter_neq_workflowid);
                //非app端簽核過濾
                PropertyFilter filter_app = new PropertyFilter("NOTAPPS_ynFixedPosition", "1");
                if ("1".equals(status)) {
                    filters.add(filter_app);
                }
                if (StringUtils.isNotBlank(workFlowId)) {
                    PropertyFilter filter_status = new PropertyFilter("EQS_workstatus", status);
                    PropertyFilter filter_workFlowId = new PropertyFilter("EQS_workflowid", workFlowId);
                    filters.add(filter_status);
                    filters.add(filter_workFlowId);
                    dataList = allRelationService.search(page, filters).getResult();
                } else {
                    dataList = allRelationService.search(page, filters).getResult();
                }

                Dict dict2 = new Dict();
                dict2.setType("entfrmIpebgIP");
                dict2.setCodeUniq("entfrmIpebgIP01");
                String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
                String entfrmAppId = dictService.getUniqueDictByType("entfrmAppId").getValue();
                String entfrmSecretKey = dictService.getUniqueDictByType("entfrmSecretKey").getValue();


                String urlStringNodeName = dictService.getUniqueDictByType("entfrmquerytaskname").getValue();
                Dict dict1 = new Dict();
                dict1.setType("entfrmIpebgIP");
                dict1.setCodeUniq("entfrmIpebgIP02");
                urlStringNodeName = dictService.getUniqueDictByTypeAndCode(dict1).getValue() + urlStringNodeName;

                for (TQhAllRelationEntity entity : dataList) {
                    gtasks = allRelationService.queryMyGtasks(entity.getSerialno(), entity.getDtoName());
                   /* else{
                      gtasks = allRelationService.queryMyGtasks(entity.getSerialno(), entity.getDtoName());
                    }*/
                    if (gtasks != null) {
                        if (entity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                            Object obj = allRelationService.findByDto(entity.getDtoName(), entity.getSerialno());
                            gtasks.setWfName(StrUtil.concat(true, entity.getWfName(), "--", Reflections.getFieldValue(obj, "applytablename") + ""));
                        } else {
                            gtasks.setWfName(entity.getWfName());
                        }
                        conifgEntity = conifgService.findUnique(entity.getWorkflowid());
                        if ("4".equals(entity.getWorkstatus())) {
                            if ("E".equals(entity.getFormFrom())) {
                                gtasks.setFormFrom("E");
                                gtasks.setAuditAction(StrUtil.concat(true, fronUrl, conifgEntity.getModaction(), "?appId=", entfrmAppId, "&secretKey=", entfrmSecretKey, "&serialno=", entity.getSerialno()));
                            } else {
                                gtasks.setAuditAction(conifgEntity.getModaction());
                            }
                        } else if ("2".equals(entity.getWorkstatus())) {
                            if ("E".equals(entity.getFormFrom())) {
                                gtasks.setFormFrom("E");
                                gtasks.setAuditAction(StrUtil.concat(true, fronUrl, conifgEntity.getAction(), "?appId=", entfrmAppId, "&secretKey=", entfrmSecretKey, "&serialno=", entity.getSerialno()));
                            } else {
                                gtasks.setAuditAction(conifgEntity.getAction());
                            }
                        }
                        gtasks.setSerialno(entity.getSerialno());
                        List<NotProcessingNowEntity> notProcessingNowEntity = notProcessingNowService.findBySerialnoAndEmpno(entity.getSerialno(), UserUtil.getCurrentUser().getLoginName());
                        if (notProcessingNowEntity != null && notProcessingNowEntity.size() > 0) {
                            gtasks.setSkipOrNot("Y");
                        }
                        InterConfig config = new InterConfig();
                        config.setProcessId(entity.getProcessid());
//                        判斷表單來源   是否為entfrm  或者  IPEBG電子簽核
                        if ("E".equals(entity.getFormFrom())) {
                            String taskName = HttpUtil.get(urlStringNodeName, JSONUtil.createObj().put("processId", entity.getProcessid()), 10000);
                            JSONObject jsonObject = JSONUtil.parseObj(taskName);
                            gtasks.setTaskName(jsonObject.get("taskName") + "");
                            gtasks.setCanBatch(jsonObject.get("canBatch") + "");
                        } else {
                            //獲取流程當前節點處理人
                            InterResult taskInfo = processService.currentTaskInfo(config);
                            if (CollectionUtil.isNotEmpty(taskInfo.getTaskInfoList())) {
                                TaskInfo info = taskInfo.getTaskInfoList().get(0);
                                WfNodeinfoEntity nodeInfoEntity = wfNodeinfoService.findByTaskName(info.getTaskName(), entity.getWorkflowid(), entity.getVersion());
                                gtasks.setCanBatch(nodeInfoEntity.getCanbatch());
                                gtasks.setTaskName(nodeInfoEntity.getNodealain());
                            }
                        }
                        //合併業務表和中間表
//                    Object obj = MyBeanUtils.merge(gtasks, allRelationService.findByDto(entity.getDtoName(), entity.getSerialno()));
                        Map<String, Object> taskMap = BeanUtil.beanToMap(gtasks, false, false);
                        Map<String, Object> bunessMap = BeanUtil.beanToMap(allRelationService.findByDto(entity.getDtoName(), entity.getSerialno()), false, false);
                        if (bunessMap == null) {
                            page.setTotalCount(page.getTotalCount() - 1);
                            continue;
                        }
                        for (String key : bunessMap.keySet()) {
                            if ("workstatus".equals(key)) {

                                List<Dict> dtList = dictService.getDictByType("audit_status");
                                for (Dict dict : dtList) {
                                    if (taskMap.get(key).equals(dict.getValue())) {
                                        taskMap.put(key, dict.getLabel());
                                    }
                                }
                                /*if (taskMap.get(key).equals("簽核中")) {
                                    NotProcessingNowEntity notProcessingNowEntity = notProcessingNowService.findBySerialno(entity.getSerialno());
                                    if (notProcessingNowEntity != null) {
                                        taskMap.put("workstatus", "簽核中(暫不處理)");
                                    }
                                }*/
                            }
                            if (!taskMap.containsKey(key)) {
                                //處理違紀嘉獎的單子
                                taskMap.put(key, bunessMap.get(key));
                                if (workFlowId.contains("dzqh_yuangongweiji")) {
                                    if ("1".equals(taskMap.get("sendpolice"))) {
                                        taskMap.put("daeltype", "送警");
                                    } else if ("2".equals(taskMap.get("fire"))) {
                                        taskMap.put("daeltype", "開除");
                                    } else if ("3".equals(taskMap.get("bigguo"))) {
                                        if ("1".equals(taskMap.get("bigguonum"))) {
                                            taskMap.put("daeltype", "記大過一次");
                                        }
                                        if ("2".equals(taskMap.get("bigguonum"))) {
                                            taskMap.put("daeltype", "記大過兩次");
                                        }
                                    } else if ("4".equals(taskMap.get("smallguo"))) {
                                        if ("1".equals(taskMap.get("smallguonum"))) {
                                            taskMap.put("daeltype", "記小過一次");
                                        }
                                        if ("2".equals(taskMap.get("smallguonum"))) {
                                            taskMap.put("daeltype", "記小過兩次");
                                        }
                                    } else if ("5".equals(taskMap.get("alarm"))) {
                                        if ("1".equals(taskMap.get("alarmnum"))) {
                                            taskMap.put("daeltype", "警告一次");
                                        }
                                        if ("2".equals(taskMap.get("alarmnum"))) {
                                            taskMap.put("daeltype", "警告兩次");
                                        }
                                    }
                                }
                                if ("dzqh_yuangongjiajiang_v1".equals(workFlowId)) {
                                    if ("1".equals(taskMap.get("commend"))) {
                                        if ("1".equals(taskMap.get("commendcount"))) {
                                            taskMap.put("daeltype", "嘉奖一次");
                                        }
                                        if ("2".equals(taskMap.get("commendcount"))) {
                                            taskMap.put("daeltype", "嘉奖兩次");
                                        }
                                    } else if ("2".equals(taskMap.get("smallgain"))) {
                                        if ("1".equals(taskMap.get("smallgaincount"))) {
                                            taskMap.put("daeltype", "記小功一次");
                                        }
                                        if ("2".equals(taskMap.get("smallgaincount"))) {
                                            taskMap.put("daeltype", "記小功兩次");
                                        }
                                    } else if ("3".equals(taskMap.get("biggain"))) {
                                        if ("1".equals(taskMap.get("biggaincount"))) {
                                            taskMap.put("daeltype", "記大功一次");
                                        }
                                        if ("2".equals(taskMap.get("biggaincount"))) {
                                            taskMap.put("daeltype", "記大功兩次");
                                        }
                                    }
                                }
                            }
                        }
                        //是否有子表
                        if (StringUtils.isNotEmpty(conifgEntity.getDynfield02())) {
                            List<Object> dataChildList = allRelationService.findByListDto(conifgEntity.getDynfield02(), entity.getSerialno(), conifgEntity.getDynfield03());
                            Assert.notEmpty(dataChildList, String.format("任務編號：%s,實體：%s中不存在子數據", entity.getSerialno(), conifgEntity.getDynfield02()));
                            if (dataChildList != null && dataChildList.size() > 0) {
                                Map<String, Object> bunessChildMap = BeanUtil.beanToMap(dataChildList.get(0), false, false);
                                taskMap.putAll(bunessChildMap);
                                taskMap.put("childsNum", dataChildList.size());
                            }
                        }
                        List<TPubDynamicTableEntity> entityList = dynamicTableService.findByWorkflowId(workFlowId);
                        for (TPubDynamicTableEntity tableEntity : entityList) {
                            //是否需要字典替換
                            if ("1".equals(tableEntity.getIsTrans())) {
                                Assert.notNull(tableEntity.getDictCode(), "字典替換類型不可為空，請在T_PUB_DYNAMIC_TABLE配置【DICT_CODE】");
                                List<Dict> dtList = dictService.getDictByType(tableEntity.getDictCode());
                                for (Dict dict : dtList) {
                                    if (taskMap.get(tableEntity.getGridField()).equals(dict.getValue())) {
                                        taskMap.put(tableEntity.getGridField(), dict.getLabel());
                                    }
                                }
                            }
                        }
//                    DynaBean bean = BeanUtil.createDynaBean(taskMap);
                        gtasksList.add(taskMap);
                    }
                }
            }
            page.setResult(gtasksList);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return page;
    }

    /**
     * 方法描述: 獲取下一待辦任務
     *
     * @Author: S6114299
     * @CreateDate: 2023/10/16
     * @Return
     **/

    @Transactional(readOnly = false)
    public Map<String, String> getMyNextTask(String workFlowId, String status) {
        Map<String, String> resultStr = new HashMap<>();
        List<PropertyFilter> filters = new ArrayList();
        try {
            Gtasks gtasks = null;

            //--------查詢代理相關信息---------------
            List<String> processIds = proxyUserinfoService.findUserAllProcessId(UserUtil.getCurrentUser().getLoginName());
            //--------查詢代理相關信息----------------
            if (processIds.size() > 0) {
                PropertyFilter filter = new PropertyFilter("INS_processid", processIds);
                filters.add(filter);
                List<TQhAllRelationEntity> dataList = null;
                //非app端簽核過濾
                PropertyFilter filter_app = new PropertyFilter("NOTAPPS_ynFixedPosition", "1");
                if ("1".equals(status)) {
                    filters.add(filter_app);
                }
                PropertyFilter filter_status = new PropertyFilter("EQS_workstatus", "2");
                filters.add(filter_status);
                PropertyFilter filter_fromForm = new PropertyFilter("NOTOTHS_fromForm", "2");
                filters.add(filter_fromForm);


                List<String> notProcessingNowList = notProcessingNowService.findListByEmpNo(UserUtil.getCurrentUser().getLoginName());
                if(notProcessingNowList!=null&&notProcessingNowList.size()>0){
                    PropertyFilter filter2 = new PropertyFilter("NINS_serialno", notProcessingNowList);
                    filters.add(filter2);
                }
                dataList = allRelationService.searchOrder(filters, "createDate", true);
                String type = "";
                if (dataList != null && dataList.size() > 0) { //存在待審核單據
                    TQhAllRelationEntity resultBean = dataList.get(0); //待審核單據默認為所有帶審核單據中 創建時間最早的單據
                    if (!"".equals(workFlowId)) {
                        TQhAllRelationEntity passForm = allRelationService.queryByEntity(workFlowId);
                        type = passForm.getWorkflowid();
                        for (TQhAllRelationEntity entity : dataList) {  //如存在與當前審核單據類型相同單據則 下一審核單據為第一個類型相同的單據
                            if (!type.equals(resultBean.getWorkflowid()) && type.equals(entity.getWorkflowid()) && !workFlowId.equals(entity.getSerialno())) {
                                resultBean = entity;
                                //審核單據創建時間小於 當前單據創建時間
                                if (passForm.getCreateDate().getTime() <= entity.getCreateDate().getTime()) {
                                    break;
                                }
                            }
                            //與審核單據類型相同且不是審核單據  且當前單據在審核單據之後創建
                            if (type.equals(entity.getWorkflowid()) && !workFlowId.equals(entity.getSerialno()) && passForm.getCreateDate().getTime() <= entity.getCreateDate().getTime()) {
                                resultBean = entity;
                                break;
                            }
                        }
                    }
                    gtasks = allRelationService.queryMyGtasks(resultBean.getSerialno(), resultBean.getDtoName());
                    if (gtasks != null) {
                        WfConifgEntity conifgEntity = conifgService.findUnique(resultBean.getWorkflowid());
                        if ("E".equals(resultBean.getFormFrom())) { //單據來自entfrm時
                            Dict dict2 = new Dict();
                            dict2.setType("entfrmIpebgIP");
                            dict2.setCodeUniq("entfrmIpebgIP01");
                            String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
                            String entfrmAppId = dictService.getUniqueDictByType("entfrmAppId").getValue();
                            String entfrmSecretKey = dictService.getUniqueDictByType("entfrmSecretKey").getValue();
                            String url = StrUtil.concat(true, fronUrl, conifgEntity.getAction(), "?appId=", entfrmAppId, "&secretKey=", entfrmSecretKey, "&serialno=", resultBean.getSerialno());
                            resultStr.put("url", url + "&id=" + gtasks.getId() + "&empNo=" + UserUtil.getCurrentUser().getLoginName() + "&turnNewform=1");
                            resultStr.put("title", resultBean.getWfName());
                        } else {
                            resultStr.put("url", conifgEntity.getAction() + "/" + resultBean.getSerialno() + "?turnNewform=2");
                            resultStr.put("title", resultBean.getWfName());
                        }
                    }
                } else {  //不存在待審核單據
                    return resultStr;
                }

            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return resultStr;
    }


    @Transactional(readOnly = false)
    public Page<Gtasks> getAllMyDownTask(Page page, String serialno, String workstatus, String startDate, String endDate) {
        try {
            List<Gtasks> gtasksList = new ArrayList<Gtasks>();
            Gtasks gtasks = null;
            WfConifgEntity conifgEntity = null;
            List<TQhAllRelationEntity> dataList = new ArrayList<>();
//        List<TQhAllRelationEntity>   = allRelationService.getMyDownTask(UserUtil.getCurrentUser().getLoginName(), page);
            /**新舊工號兼容優化**/
            EntityNewAndOld newAndOld = OldJobNoToNewNo.getNewNo(UserUtil.getCurrentUser().getLoginName());
            if (!(newAndOld.getNewEmpno().equals(newAndOld.getOldEmpno()))) {
                dataList.addAll(allRelationService.getMyDownTask(newAndOld.getNewEmpno() + "','" + newAndOld.getOldEmpno(), page, serialno, workstatus, startDate, endDate));
            } else {
                dataList = allRelationService.getMyDownTask(UserUtil.getCurrentUser().getLoginName(), page, serialno, workstatus, startDate, endDate);
            }
            /**新舊工號兼容優化 **/
            String urlStringNodeName = dictService.getUniqueDictByType("entfrmquerytaskname").getValue();
            Dict dict = new Dict();
            dict.setType("entfrmIpebgIP");
            dict.setCodeUniq("entfrmIpebgIP02");
            urlStringNodeName = dictService.getUniqueDictByTypeAndCode(dict).getValue() + urlStringNodeName;

            Dict dict2 = new Dict();
            dict2.setType("entfrmIpebgIP");
            dict2.setCodeUniq("entfrmIpebgIP01");
            String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
            String entfrmAppId = dictService.getUniqueDictByType("entfrmAppId").getValue();
            String entfrmSecretKey = dictService.getUniqueDictByType("entfrmSecretKey").getValue();

            for (TQhAllRelationEntity entity : dataList) {
                gtasks = allRelationService.queryMyGtasks(entity.getSerialno(), entity.getDtoName());
                if (gtasks == null) {
                    continue;
                }
                if (entity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                    Object obj = allRelationService.findByDto(entity.getDtoName(), entity.getSerialno());
                    gtasks.setWfName(StrUtil.concat(true, entity.getWfName(), "--", Reflections.getFieldValue(obj, "applytablename") + ""));
                    String filepassword = Reflections.getFieldValue(obj, "filepassword") + "";
                    if (!"".equals(filepassword) && !"null".equals(filepassword)) {
                        gtasks.setType(filepassword);
                    }
                } else {
                    gtasks.setWfName(entity.getWfName());
                }
                conifgEntity = conifgService.findUnique(entity.getWorkflowid());
//                gtasks.setAuditAction(conifgEntity.getDetailaction());
                gtasks.setSerialno(entity.getSerialno());
                InterConfig config = new InterConfig();
                gtasks.setUpdateDate(entity.getUpdateDate());
                config.setProcessId(entity.getProcessid());
                gtasks.setAuditAction(conifgEntity.getDetailaction());
//                判斷表單來源   是否為entfrm  或者  IPEBG電子簽核
                if ("E".equals(entity.getFormFrom())) {

                    String taskName = HttpUtil.get(urlStringNodeName, JSONUtil.createObj().put("processId", entity.getProcessid()), 10000);
                    JSONObject jsonObject = JSONUtil.parseObj(taskName);
                    gtasks.setTaskName(jsonObject.get("taskName") + "");
                    gtasks.setCanBatch(jsonObject.get("canBatch") + "");
                    gtasks.setFormFrom("E");
                    gtasks.setAuditAction(StrUtil.concat(true, fronUrl, conifgEntity.getDetailaction(), "?appId=", entfrmAppId, "&secretKey=", entfrmSecretKey));
                } else {
                    gtasks.setAuditAction(conifgEntity.getDetailaction());
                    //獲取流程當前節點處理人
                    InterResult taskInfo = processService.currentTaskInfo(config);
                    List<TaskInfo> infoList = taskInfo.getTaskInfoList();
                    if (ObjectUtil.isNotNull(infoList) && infoList.size() > 0 && ObjectUtil.isNotNull(infoList.get(0))) {
                        TaskInfo info = infoList.get(0);
                        WfNodeinfoEntity nodeInfoEntity = wfNodeinfoService.findByTaskName(info.getTaskName(), entity.getWorkflowid(), entity.getVersion());
                        if (ObjectUtil.isNotNull(nodeInfoEntity)) {
                            gtasks.setCanBatch(nodeInfoEntity.getCanbatch());
                            gtasks.setTaskName(nodeInfoEntity.getNodealain());
                        }
                    }
//					gtasks.setTaskName(taskInfo.getTaskInfoList() == null ? "" : taskInfo.getTaskInfoList().get(0).getTaskName());
                }
                gtasksList.add(gtasks);
            }
            page.setResult(gtasksList);
            page.setTotalCount(allRelationService.getMyDownTaskCount(UserUtil.getCurrentUser().getLoginName(), serialno, workstatus, startDate, endDate));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return page;
    }

    /**
     * 方法描述: 通過流程標識獲取對應的任務
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/13  上午 08:09
     * @Return
     **/

    public List<Gtasks> getMyTaskByWorkId(String workFlowId, String status) throws Exception {
        List<Gtasks> gtasksList = new ArrayList<Gtasks>();
        Gtasks gtasks = null;
        List<String> processIds = this.getProcessIds(UserUtil.getCurrentUser().getLoginName());
//        processIds.add("101789953");
//        workFlowId="dzqh_xinzengxiangmuhuanbaoshouxubanlishenqingdan";
        WfConifgEntity conifgEntity = null;
        List<TQhAllRelationEntity> dataList = allRelationService.getMyTaskByWorkId(workFlowId, processIds, status);
        for (TQhAllRelationEntity entity : dataList) {
            gtasks = allRelationService.queryMyGtasks(entity.getProcessid(), entity.getDtoName());
            gtasks.setWfName(entity.getWfName());
            gtasks.setSerialno(entity.getSerialno());
            InterConfig config = new InterConfig();
            config.setProcessId(entity.getProcessid());
            conifgEntity = conifgService.findUnique(entity.getWorkflowid());
            gtasks.setAuditAction(conifgEntity.getAction());
            //獲取流程當前節點處理人
            InterResult taskInfo = processService.currentTaskInfo(config);
            gtasks.setTaskName(taskInfo.getTaskInfoList().get(0).getTaskName());
            gtasksList.add(gtasks);
        }
        return gtasksList;
    }

    public List<String> getProcessIds(String empno) {
        List<String> processIds = new ArrayList<String>();
        try {
            InterConfig config = new InterConfig();
            config.setUserId(empno);
            //查詢流程引擎的個人待辦
            InterResult interResult = processService.queryTaskList(config);
            List<WfConifgEntity> conifgEntityList = conifgService.getAll();
            if (interResult != null) {
                List<TaskInfo> taskList = interResult.getTaskInfoList();
                if (taskList != null && taskList.size() > 0) {
                    for (TaskInfo t : taskList) {
                        for (WfConifgEntity w : conifgEntityList) {
                            if (t.getPdId().startsWith(w.getWorkflowid())) {
                                processIds.add(t.getProcessId());
                                break;
                            }
                        }
                    }
                } else {
                    logger.info("not found user task");
                }
            }
            //查詢個人會簽待辦任務
            config = new InterConfig();
            config.setUserId(empno);
            interResult = processService.groupTasks(config);
            if (interResult != null) {
                List<TaskInfo> taskList = interResult.getTaskInfoList();
                if (taskList != null && taskList.size() > 0) {
                    for (TaskInfo t : taskList) {
                        for (WfConifgEntity w : conifgEntityList) {
                            if (t.getPdId().startsWith(w.getWorkflowid())) {
                                processIds.add(t.getProcessId());
                                break;
                            }
                        }
                    }
                } else {
                    logger.info("not found user group task");
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return processIds;
    }

    /**
     * 方法描述: 批量審合
     *
     * @Author: S6114648
     * @CreateDate: 2019/8/22  下午 04:24
     * @Return
     **/

    @Transactional(readOnly = false)
    public String completeTasks(List<String> serialno, String ip, String status, String attachidsremark, String reattachids) {
        String reStr = Constant.RESULT.CODE_NO.getValue();
        for (String s : serialno) {
            Map<String, String> reMap = completeTask(s, ip, status, attachidsremark, reattachids, "");
            reStr = reMap.get("result");
            if (Constant.RESULT.CODE_NO.getValue().equals(reStr)) {
                reStr = s;
                break;
            }
        }
        return reStr;
    }

    public String entfrmBatchTasks(List<String> processIds, String ip, String status, String attachidsremark) {
        Dict dict = new Dict();
        dict.setType("entfrmIpebgIP");
        dict.setCodeUniq("entfrmIpebgIP02");
        String entfrmbatchtask = dictService.getUniqueDictByTypeAndCode(dict).getValue() + dictService.getUniqueDictByType("entfrmbatchtask").getValue();
        Map params = new HashMap();
        params.put("processIds", processIds);
        params.put("ip", ip);
        params.put("status", status);
        params.put("chargeNo", UserUtil.getCurrentUser().getLoginName());
        params.put("attachidsremark", attachidsremark);
        String result = HttpUtil.post(entfrmbatchtask, params, 10000);
        return result;
    }

    /**
     * 方法描述: 完成任務
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/10  下午 01:20
     * @Return
     **/
    @Transactional(readOnly = false)
    public Map<String, String> completeTask(String serialno, String ip, String status, String attachidsremark, String reattachids, String remarkShow) {
        //status 2 重新提交  3 取消申請  0 通過 1 駁回
        String nodeName = "";
        String signNo = "";
        String workflowid = "";
        Map<String, String> resultMap = new HashMap();
        try {
            TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialno);
            Assert.notNull(relationEntity);
            CompleteTaskParameter parameter = new CompleteTaskParameter();
            parameter.setSerialno(serialno);
            parameter.setProcessId(relationEntity.getProcessid());
            parameter.setWorkFlowId(relationEntity.getWorkflowid());
            parameter.setIp(ip);
            parameter.setAuto(false);
            parameter.setAttachidsremark(attachidsremark);

            workflowid = relationEntity.getWorkflowid();
            InterConfig config = new InterConfig();
            config.setProcessId(parameter.getProcessId());
            //獲取流程當前節點處理人
            InterResult taskInfo = processService.currentTaskInfo(config);
            if ("SUCCESS".equals(taskInfo.getStatus())) {
                TaskInfo info = taskInfo.getTaskInfoList().get(0);

                WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(info.getTaskName(), parameter.getWorkFlowId(), relationEntity.getVersion());
                signNo = info.getAssignee();
                nodeName = entity.getColname();
//            //如果是自動審核節點
//            if("Y".equals(entity.getDynfield02())){
//                Object obj = allRelationService.findByDto(relationEntity.getDtoName(), relationEntity.getSerialno());
//                ScrapJobEntity jobEntity = new ScrapJobEntity();
//                jobEntity.setEntity(relationEntity);
//                jobEntity.setGroup(relationEntity.getWorkflowid()+"-"+relationEntity.getVersion());
//                jobEntity.setName(relationEntity.getSerialno());
//                jobEntity.setDescription("此節點為自動審核節點，如到期未審核會自動通過");
//                jobEntity.setCronExpression(DateUtil.getCron(DateUtil.dateAddDay(new Date(),-3)));
//                jobEntity.setStatus("1");
//                scheduleJobService.add(jobEntity);
//            }

                String result = "";
                List<String> proxUser = new ArrayList<>();
                //防止不是自己的單子審核通過
                //TODO 防止不是自己的單子審核通過
                List<TProxyUserinfoEntity> entity_prox = proxyUserinfoService.findProxUserByEmpno(UserUtil.getCurrentUser().getLoginName());
                for (TProxyUserinfoEntity entityProx : entity_prox) {
                    proxUser.add(entityProx.getSuppliedempno());
                }
//                proxUser.add(UserUtil.getCurrentUser().getLoginName());
                /**新舊工號兼容優化**/
                EntityNewAndOld newAndOld = OldJobNoToNewNo.getNewNo(UserUtil.getCurrentUser().getLoginName());
                if (!(newAndOld.getNewEmpno().equals(newAndOld.getOldEmpno()))) {
                    proxUser.add(newAndOld.getOldEmpno());
                    proxUser.add(newAndOld.getNewEmpno());
                } else {
                    proxUser.add(newAndOld.getNewEmpno());
                }
                /**新舊工號兼容優化**/
                if (proxUser.contains(info.getAssignee())) {
                    if ("0".equals(entity.getSigntype())) {
                        result = chargLogService.completeTask(parameter, status);
                    } else if ("1".equals(entity.getSigntype())) {
                        result = chargLogService.completeHqTask(parameter, status);
                    }
                    if (result.equals(Constant.RESULT.CODE_NO.getValue())) {
                        resultMap.put("result", Constant.RESULT.CODE_NO.getValue());
                        return resultMap;
                    } else {
                        if (remarkShow != null && !"".equals(remarkShow)) {//審核成功 remarkShow不為空則當前表單為 文檔簽核申請單
                            signModelPositionService.updateRemarkShowType(serialno, entity.getColname(), info.getAssignee(), remarkShow);
                        }
                        try {
                            //重新獲取當前工作流審核信息
                            InterResult taskInfoAuto = processService.currentTaskInfo(config);
                            TaskInfo infoAuto = null;
                            if (taskInfoAuto.getTaskInfoList() != null && "SUCCESS".equals(taskInfoAuto.getStatus())) {
                                infoAuto = taskInfoAuto.getTaskInfoList().get(0);
                                WfNodeinfoEntity entityAuto = wfNodeinfoService.findByTaskName(infoAuto.getTaskName(), parameter.getWorkFlowId(), relationEntity.getVersion());
                                /**
                                 * 方法描述: 更新品管三張表單審核中數據，改善導出數據慢的問題
                                 * @Author: S6114648
                                 * @CreateDate: 2022/6/22  10:30
                                 * @Return
                                 **/

                                try {
                                    if (relationEntity.getWorkflowid().equals("dzqh_gongchengbaofeishenqingdan") || relationEntity.getWorkflowid().contains("dzqh_gongchengbaofeidan")
                                            || relationEntity.getWorkflowid().contains("dzqh_zhichengbaofeishenqingdan") || relationEntity.getWorkflowid().contains("dzqh_zhichengbaofeidan")) {
                                        int nodeSize = wfNodeinfoService.findByFilters(relationEntity.getWorkflowid()).size();
                                        TQhUserformhsEntity tentity = tQhUserformhsService.findByEmpno(infoAuto.getAssignee());
                                        User user = userService.getUser(infoAuto.getAssignee());
                                        allRelationService.updateScrapData(relationEntity.getDtoName(), serialno, StrUtil.concat(false, infoAuto.getAssignee(), "/", tentity == null ? infoAuto.getAssignee() + "(該人員在職狀態異常)" : tentity.getEmpname()), entityAuto.getNodealain(), (Math.ceil(entityAuto.getOrderby() * 100 / nodeSize)) + "", user != null ? user.getEmail() : "", user != null ? user.getPhone() : "");

                                    }
                                } catch (Exception e) {
                                    logger.info(e.getMessage(), e);
                                }
                                //如果是自動審核節點
                                if ("Y".equals(entityAuto.getDynfield02())) {
                                    Object obj = allRelationService.findByDto(relationEntity.getDtoName(), relationEntity.getSerialno());
                                    ScrapJobEntity jobEntity = new ScrapJobEntity();
                                    jobEntity.setEntity(relationEntity);
                                    jobEntity.setGroup(relationEntity.getWorkflowid() + "-" + relationEntity.getSerialno());
                                    jobEntity.setName(relationEntity.getSerialno() + "-" + UUID.randomUUID());
                                    jobEntity.setStatus("1");
                                    jobEntity.setChargeNodeName(infoAuto.getTaskName());
                                    if (entityAuto.getWorkflowid().equals("dzqh_feiyongxinglingtuiliaodan")) {
                                        WfreceiptandreturnEntity wfreceiptandreturnEntity = wfreceiptandreturnService.findBySerialno(relationEntity.getSerialno());
                                        jobEntity.setDescription("此節點為自動審核節點，如到期未審核會自動駁回并刪除任務【" + relationEntity.getWfName() + "】");
                                        jobEntity.setCronExpression(DateUtil.getCrons(DateUtil.dateAddDay(wfreceiptandreturnEntity.getBasedate2(), Double.parseDouble(entityAuto.getDynfield03() == null ? "10" : entityAuto.getDynfield03()))));
                                        jobEntity.setClassName("com.foxconn.ipebg.buessness.common.service.AutoCompleteReceipt");
                                    } else {
                                        jobEntity.setDescription("此節點為自動審核節點，如到期未審核會自動通過并刪除任務【" + relationEntity.getWfName() + "】");
                                        jobEntity.setCronExpression(DateUtil.getCrons(DateUtil.dateAddDay(new Date(), Double.parseDouble(entityAuto.getDynfield03() == null ? "3" : entityAuto.getDynfield03()))));
                                        jobEntity.setClassName("com.foxconn.ipebg.buessness.common.service.AutoCompleteScrap");
                                    }
                                    scheduleJobService.add(jobEntity);
                                }
                            }
                            //查詢是否需要發送短信
                            WfNodeparamEntity nodeparamEntity = nodeparamService.getInfoById(entity, status);
                            if (StringUtils.isNotEmpty(nodeparamEntity.getIsNeedSms()) && "1".equals(nodeparamEntity.getIsNeedSms())) {
                                String userName = dictService.getUniqueDictByType(nodeparamEntity.getSmsTemplete() + "_username").getValue();
                                String password = dictService.getUniqueDictByType(nodeparamEntity.getSmsTemplete() + "_password").getValue();
                                String content = dictService.getUniqueDictByType(nodeparamEntity.getSmsTemplete() + "_content").getValue();
                                Object obj = allRelationService.findByDto(relationEntity.getDtoName(), relationEntity.getSerialno());
                                String yongyinfactoryid = Reflections.getFieldValue(obj, "factoryid").toString();
                                if (StringUtils.isNotEmpty(relationEntity.getWorkflowid()) && "dzqh_xingzhengzhuanyongzhangyongyinshenqingdan_v1".equals(relationEntity.getWorkflowid())) {
                                    if (StringUtils.isNotEmpty(yongyinfactoryid) && "IPEZBQ".equals(yongyinfactoryid)) {
                                        sendSmsService.sendFormatSms(userName, password, Reflections.getFieldValue(obj, nodeparamEntity.getSmsCol()) + "", 4755, 0, "");
                                    }
                                } else {
                                    sendSmsService.sendSms(userName, password, Reflections.getFieldValue(obj, nodeparamEntity.getSmsCol()) + "", content);
                                }
                            }
                            /*S7198867  員工復職申請單 住宿分配節點短信發送開始    發動固定短信*/
                            if (StringUtils.isNotEmpty(relationEntity.getDtoName()) && "WffuzhiprocessEntity".equals(relationEntity.getDtoName())) {
                                Object obj = allRelationService.findByDto(relationEntity.getDtoName(), relationEntity.getSerialno());
                                String dealfactoryid = Reflections.getFieldValue(obj, "dealfactoryid").toString();
                                if (StringUtils.isNotEmpty(dealfactoryid) && "IPEZBQ".equals(dealfactoryid)) {
                                    if (StringUtils.isNotEmpty(nodeparamEntity.getNodeid()) && "dzqh_yuangongfuzhishenqing_v207".equals(nodeparamEntity.getNodeid())) {
                                        String userName = dictService.getUniqueDictByType(nodeparamEntity.getSmsTemplete() + "_username").getValue();
                                        String password = dictService.getUniqueDictByType(nodeparamEntity.getSmsTemplete() + "_password").getValue();
                                        int formatID = 0, spaceNum = 0;
                                        String content = "";
                                        String element = (String) Reflections.getFieldValue(obj, "element");
                                        String apartment = (String) Reflections.getFieldValue(obj, "apartment");
                                        if (StringUtils.isNotBlank(element) && StringUtils.isNotBlank(apartment) && !"".equals(element) && !"".equals(apartment)) {
                                            formatID = Integer.parseInt(dictService.getUniqueDictByType(nodeparamEntity.getSmsTemplete() + "_zc_formatID").getValue());
                                            spaceNum = 2;
                                            content = element + "," + apartment;
                                            sendSmsService.sendFormatSms(userName, password, Reflections.getFieldValue(obj, nodeparamEntity.getSmsCol()) + "", formatID, spaceNum, content);
                                        } else {
                                            formatID = Integer.parseInt(dictService.getUniqueDictByType(nodeparamEntity.getSmsTemplete() + "_yc_formatID").getValue());
                                            spaceNum = 0;
                                            sendSmsService.sendFormatSms(userName, password, Reflections.getFieldValue(obj, nodeparamEntity.getSmsCol()) + "", formatID, spaceNum, content);
                                        }
                                    }
                                } else if (StringUtils.isNotEmpty(dealfactoryid) && "IPEJY".equals(dealfactoryid)) {
                                    if (StringUtils.isNotEmpty(nodeparamEntity.getNodeid()) && "dzqh_yuangongfuzhishenqing_v202".equals(nodeparamEntity.getNodeid())) {
                                        String userName = dictService.getUniqueDictByType(nodeparamEntity.getSmsTemplete() + "_username").getValue();
                                        String password = dictService.getUniqueDictByType(nodeparamEntity.getSmsTemplete() + "_password").getValue();
                                        int formatID = 0, spaceNum = 0;
                                        String content = "";
                                        String reason = Reflections.getFieldValue(obj, "reason").toString();
                                        //if(StringUtils.isNotEmpty(nodeparamEntity.getDescrib())&&"可以體檢".equals(nodeparamEntity.getDescrib())){
                                        if (StringUtils.isNotEmpty(nodeparamEntity.getIspass()) && "0".equals(nodeparamEntity.getIspass())) {
                                            if (StringUtils.isNotEmpty(reason) && ("0".equals(reason) || "3".equals(reason))) {
                                                formatID = Integer.parseInt(dictService.getUniqueDictByType(nodeparamEntity.getSmsTemplete() + "_cjss_formatID").getValue());
                                                spaceNum = 0;
                                                sendSmsService.sendFormatSms(userName, password, Reflections.getFieldValue(obj, nodeparamEntity.getSmsCol()) + "", formatID, spaceNum, content);
                                            }
                                            if (StringUtils.isNotEmpty(reason) && ("1".equals(reason) || "2".equals(reason))) {
                                                formatID = Integer.parseInt(dictService.getUniqueDictByType(nodeparamEntity.getSmsTemplete() + "_gsyl_formatID").getValue());
                                                spaceNum = 0;
                                                sendSmsService.sendFormatSms(userName, password, Reflections.getFieldValue(obj, nodeparamEntity.getSmsCol()) + "", formatID, spaceNum, content);
                                            }
                                        }
                                    }
                                }

                            }
                            /*S7198867  員工復職申請單 住宿分配節點短信發送結束*/

                            /*系統需求申請單審核調用專案管理系統*/
                            if (StringUtils.isNotEmpty(relationEntity.getDtoName()) && "WfsystemprocessEntity".equals(relationEntity.getDtoName())) {
                                WfsystemprocessEntity wfsystemprocess = wfsystemprocessService.findBySerialno(parameter.getSerialno());
                                if (StringUtils.isNotEmpty(wfsystemprocess.getDockingSystem()) && wfsystemprocess.getDockingSystem().contains("zagl")) {
                                    wfsystemprocessService.sendManagerInfo(parameter.getSerialno(), status, info);
                                }
                                if (StringUtils.isNotEmpty(wfsystemprocess.getDockingSystem()) && wfsystemprocess.getDockingSystem().contains("xmgl") && "3".equals(wfsystemprocess.getWorkstatus())) {
                                    wfsystemprocessService.sendProjectInfo(wfsystemprocess);
                                }
                            }
                            /*文檔簽核申請單嵌入AI系統項目管理平台審核處理*/
                            wffilesignprocessService.sendManagerInfo(new FileSignParams(relationEntity.getSerialno(), infoAuto,status,attachidsremark,ip,relationEntity));
                            /*違紀表單驳回要發e通關系統*/
                            if (StringUtils.isNotEmpty(relationEntity.getDtoName()) && "WfIlegalProcessesEntity".equals(relationEntity.getDtoName())) {
                                if ("1".equals(status)) {
                                    wfIlegalProcessesService.sendToETongGuan(parameter.getSerialno());
                                }
                            }

                            /*給智信發當前審核人信息*/
                            WfConifgEntity conifgEntity = conifgService.findUnique(relationEntity.getWorkflowid());
                            if ("Y".equals(conifgEntity.getWhetherApp())) {
                                String signStagus = "";
                                if ("0".equals(status)) {
                                    SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                                    Mail mail = new Mail();
                                    signStagus = "signed";
                                    mail.setOrderstatus("2");
                                    mail.setSerialno(parameter.getSerialno());
                                    mail.setAppAuditUrl("");
                                    mail.setDusername("");
                                    mail.setOrdertype("");
                                    User user = userService.getUser(signNo);
                                    //啟動流程時便易簽裡面有的單子發智信
                                    Dict except = dictService.getUniqueDictByType("dict_exceptType" + mail.getOrderstatus());
                                    sendMailUtil.sendZhiXing(mail, except, signNo, user, signStagus);
                                } else if ("1".equals(status)) {
                                    SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                                    Mail mail = new Mail();
                                    signStagus = "reject";
                                    mail.setOrderstatus("4");
                                    mail.setSerialno(parameter.getSerialno());
                                    mail.setAppAuditUrl("");
                                    mail.setDusername("");
                                    mail.setOrdertype("");
                                    User user = userService.getUser(signNo);
                                    //啟動流程時便易簽裡面有的單子發智信
                                    Dict except = dictService.getUniqueDictByType("dict_exceptType" + mail.getOrderstatus());
                                    sendMailUtil.sendZhiXing(mail, except, signNo, user, signStagus);
                                }


                            }


                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                } else {
                    resultMap.put("result", Constant.RESULT.CODE_NO.getValue());
                    return resultMap;
                }
                //對應窗口審核上傳的附件
                if (StringUtils.isNotEmpty(reattachids)) {
                    allRelationService.updateFormReattachids(serialno, reattachids, relationEntity.getDtoName());
                }
                //如果是完成則更新表單狀態及中間表狀態
                if (result == Constant.RESULT.CODE_COMPLETE.getValue()) {
                    allRelationService.updateFormStaticComplete(serialno, result, relationEntity.getDtoName());
                    relationEntity.setWorkstatus(result);
                    allRelationService.update(relationEntity);

                    /**
                     * 方法描述: 審核完成的單子拋轉富鴻網
                     * @Author: S6114648
                     * @CreateDate: 2019/3/18  下午 01:11
                     * @Return
                     **/
                    TQhAllRelationEntity relationEntitya = allRelationService.queryByEntity(serialno);

                    tossAndTurnFhwService.tossAndTurnFhw(relationEntitya);

                    tossAndTurnFhwService.callPtalkAPI(relationEntity);
                    /**
                     * 方法描述: 審核完成清空品管表單三張審核中信息
                     * @Author: S6114648
                     * @CreateDate: 2022/6/22  13:35
                     * @Return
                     **/
                    if (relationEntitya.getWorkflowid() != null && (relationEntitya.getWorkflowid().contains("dzqh_gongchengbaofeishenqingdan") || relationEntitya.getWorkflowid().contains("dzqh_gongchengbaofeidan")
                            || relationEntitya.getWorkflowid().contains("dzqh_zhichengbaofeishenqingdan") || relationEntitya.getWorkflowid().contains("dzqh_zhichengbaofeidan"))) {
                        allRelationService.updateScrapData(relationEntity.getDtoName(), serialno, "", "", "", "", "");
                    }
                    String userEmpno = allRelationService.findUserEmpno(relationEntity.getDtoName(), serialno);

                    /**新舊工號兼容優化**/
                    newAndOld = OldJobNoToNewNo.getNewNo(userEmpno);
                    /**新舊工號兼容優化**/

                    User user = userService.getUser(userEmpno);
                    //查詢是否有代理人
                    List<TProxyUserinfoEntity> audit_prox = proxyUserinfoService.findProxUserByEmpnoNew(userEmpno);
                    if (audit_prox != null && audit_prox.size() > 0) {
                        user = userService.getUser(audit_prox.get(0).getSupplyempno());
                    }
                    Gtasks gtasks = allRelationService.queryMyGtasks(relationEntity.getSerialno(), relationEntity.getDtoName());
                    Mail mail = new Mail();
                    mail.setUsername(gtasks.getMakername());
                    mail.setSystemname(dictService.getDictByTypeAndVlaue("sys_property", "sys_name").getLabel());
                    mail.setDusername(gtasks.getMakername());
                    mail.setChargerman(UserUtil.getCurrentUser().getName());
                    mail.setOrdertype(relationEntity.getWfName());
                    mail.setSerialno(serialno);
                    mail.setOrderstatus(result);
                    mail.setUrl(dictService.get(560).getValue());
                    mail.setUrlip(dictService.get(561).getValue());
//                    mail.setFreeloginurl("");
                    //保存發送記錄
                    TPubMailrecordEntity mailrecordEntity = new TPubMailrecordEntity();
                    mailrecordEntity.setChargerman(mail.getChargerman());
                    mailrecordEntity.setDusername(mail.getDusername());
                    mailrecordEntity.setOrderstatus(mail.getOrderstatus());
                    mailrecordEntity.setOrdertype(mail.getOrdertype());
                    mailrecordEntity.setSerialno(mail.getSerialno());
                    mailrecordEntity.setUsername(mail.getUsername());
                    mailrecordEntity.setUsermail(mail.getUsermail());
                    mailrecordEntity.setSendStatus("0");
                    mailrecordEntity.setEmpno(user.getLoginName());
                    mailrecordEntity.setUrl(mail.getUrl());
                    mailrecordEntity.setUrlip(mail.getUrlip());
//                    mailrecordEntity.setValidStr(validStr);
                    mailrecordService.save(mailrecordEntity);
                    if (user != null && user.getEmail() != null) {
                        WfConifgEntity conifgEntity = conifgService.findUnique(relationEntity.getWorkflowid());
//                    String validStr = UUID.randomUUID().toString().replace("-","");
                        mail.setUsermail(user.getEmail());
                        mailrecordEntity.setUsermail(mail.getUsermail());
                        mailrecordService.save(mailrecordEntity);
                        //判斷是否及時發送
//                        if (!"0".equals(user.getEmailset())) {
                        String sendResult = "";
                        /**
                         * 方法描述: 如果是文檔簽核完成狀態需要發送附件到用戶郵箱，調用新的方法
                         * @Author: S6114648
                         * @CreateDate: 2023/5/11  上午 10:35
                         * @Return
                         **/

                        if (relationEntitya.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                            try {
                                Object obj = allRelationService.findByDto(relationEntitya.getDtoName(), relationEntitya.getSerialno());
                                TPubFileobjectEntity fileobjectEntity = fileobjectService.findByIdIgnoreComma(Reflections.getFieldValue(obj, "attachids2") + "");
                                //由於郵箱接收附件有8M限制，大於限制不發送附件
                                if (ObjectUtil.isNotNull(fileobjectEntity)) {
                                    if (fileobjectEntity.getSizez() > 8388608) {
                                        sendResult = new SendMailUtil().sendMail(mail);
                                    } else {
                                        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//                                        HttpUtil.download("http://localhost:8081/newEsign/entrfrm/fileSignDownloadPdfAddImg222/" + fileobjectEntity.getId() + "?empNo=" + fileobjectEntity.getCreateBy(), outputStream, true);
                                        HttpUtil.download(dictService.get(558).getValue() + Global.getConfig("sendMailAuditPath") + "/entrfrm/fileSignDownloadPdfAddImg222/" + fileobjectEntity.getId() + "?empNo=" + fileobjectEntity.getCreateBy(), outputStream, true);
                                        mail.setFile(FileUtil.readBytes(new ByteArrayInputStream(outputStream.toByteArray()), fileobjectEntity.getName()));
                                        sendResult = SendDocMailUtil.sendMail(mail);
                                    }
                                } else {
                                    sendResult = new SendMailUtil().sendMail(mail);
                                }
                            } catch (Exception e) {
                                logger.info("發送附件文檔出錯,serialno:" + relationEntitya.getSerialno(), e);
                            }
                        } else {
                            sendResult = new SendMailUtil().sendMail(mail);
                        }
                        if ("0".equals(sendResult)) {
                            //發送成功，更新標誌
                            mailrecordEntity.setSendStatus("1");
                            mailrecordService.save(mailrecordEntity);
                        }
                        SendMailUtil sendMailUtil = new SendMailUtil();
                        //聚會推送消息
                        String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                        String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                        String empNos = user.getLoginName();
                        String sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos);
                        if ("0".equals(sendJuhuiResult)) {
                            mailrecordEntity.setSendJuhuiStatus("1");
                            mailrecordService.save(mailrecordEntity);
                        }
                        if ("Y".equals(conifgEntity.getWhetherApp())) {
                            String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                            if ("1".equals(conifgEntity.getAppType())) {
                                Dict dict2 = new Dict();
                                dict2.setType("entfrmIpebgIP_app");
                                dict2.setCodeUniq("entfrmIpebgIP_app01");
                                String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
                                mail.setAppAuditUrl(StrUtil.concat(true, fronUrl, conifgEntity.getAppDetailUrl(), "?serialno=", mail.getSerialno()));
                            } else {
                                mail.setAppAuditUrl(StrUtil.concat(true, conifgEntity.getAppDetailUrl(), "?serialno=", mail.getSerialno()));
                            }
                            if (relationEntity.getWorkflowid().contains("dzqh_gongnengchengshifabushenqingdan")) {
                                sendMailUtil.sendJuihuiMobileApi(mail, "Smack", url, empNos);
                            } else {
                                sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos);
                                if (relationEntity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                                    Object obj = allRelationService.findByDto(relationEntity.getDtoName(), relationEntity.getSerialno());
                                    String applytablename = (String) Reflections.getFieldValue(obj, "applytablename");
                                    mail.setOrdertype(applytablename);
                                }
                                //表單審核完成的消息不需推送智信
                                /*Dict except=dictService.getUniqueDictByType("dict_exceptType"+mail.getOrderstatus());
                                sendMailUtil.sendZhiXing(mail,except,empNos,user);*/
                                if ("Y".equals(user.getNotification())) {
                                    sendMailUtil.sendAppMessageBar(mail, empNos, user);
                                }
                            }
                        }
//                        }

                    }

                    //環工表單審核完成后，郵件通知‘環保科技處對應窗口’‘總務處對應窗口’
                    //【iPEBG電子簽核平台】你好！XXX申請的xxxxxxxx表單已簽核完成，請知悉，謝謝！
                    if ("dzqh_fusheanquanxukezhengbiangengshenqingdan_v1".equals(relationEntity.getWorkflowid()) || "dzqh_shexianzhuangzhibaotingbaofeishenqingdan_v1".equals(relationEntity.getWorkflowid())
                            || "dzqh_baotingshexianzhuangzhihuifusyshenqingdan_v1".equals(relationEntity.getWorkflowid())
                            || "dzqh_jianshexiangmuhuanbaobiangengshenqingdan-v1".equals(relationEntity.getWorkflowid())
                            || "dzqh_xinzengxiangmuhuanbaoshouxubanlisqd_v3".equals(relationEntity.getWorkflowid())
                            || "dzqh_jianshexiangmuhuanbaobiangengshenqingdan_v3".equals(relationEntity.getWorkflowid()) || "dzqh_fusheanquanxukeweituoshenqingdan_v3".equals(relationEntity.getWorkflowid()) || "dzqh_fusheanquanxukeweituoshenqingdan_v3".equals(relationEntity.getWorkflowid())
                            || "dzqh_shexianzhuangzhihuanbaoweituoshenqingdan_v3".equals(relationEntity.getWorkflowid())) {
                        String hbEmpno = allRelationService.findHbEmpno(relationEntity.getDtoName(), serialno);
                        String zwEmpno = "";
                        if ("TQhWfbuildprojectrocessEntity".equals(relationEntity.getDtoName()) || "TQhWfbuildprojectchangeEntity".equals(relationEntity.getDtoName())
                                || "TQhWfraydevicehbprocessEntity".equals(relationEntity.getDtoName()) || "TQhWfraysafetyhbprocessEntity".equals(relationEntity.getDtoName())) {
                            zwEmpno = allRelationService.findZwEmpno(relationEntity.getDtoName(), serialno);
                        }
                        if (StringUtils.isNotEmpty(hbEmpno)) {
                            User hbuser = userService.getUser(hbEmpno);
                            //查詢是否有代理人
                            List<TProxyUserinfoEntity> audit_prox_hb = proxyUserinfoService.findProxUserByEmpnoNew(hbEmpno);
                            if (audit_prox_hb != null && audit_prox_hb.size() > 0) {
                                hbuser = userService.getUser(audit_prox_hb.get(0).getSupplyempno());
                            }
                            Mail mail_hb = new Mail();
                            mail_hb.setUsername(gtasks.getMakername());
                            mail_hb.setSystemname(dictService.getDictByTypeAndVlaue("sys_property", "sys_name").getLabel());
                            mail_hb.setDusername(hbuser.getName());
                            mail_hb.setChargerman("環保科技處對應窗口");
                            mail_hb.setOrdertype(relationEntity.getWfName());
                            mail_hb.setSerialno(serialno);
                            mail_hb.setOrderstatus(result);
                            mail_hb.setUrl(dictService.get(560).getValue());
                            mail_hb.setUrlip(dictService.get(561).getValue());
                            //保存發送記錄
                            TPubMailrecordEntity mailrecordEntity_hb = new TPubMailrecordEntity();
                            mailrecordEntity_hb.setChargerman(mail_hb.getChargerman());
                            mailrecordEntity_hb.setDusername(mail_hb.getDusername());
                            mailrecordEntity_hb.setOrderstatus(mail_hb.getOrderstatus());
                            mailrecordEntity_hb.setOrdertype(mail_hb.getOrdertype());
                            mailrecordEntity_hb.setSerialno(mail_hb.getSerialno());
                            mailrecordEntity_hb.setUsername(mail_hb.getUsername());
                            mailrecordEntity_hb.setUsermail(mail_hb.getUsermail());
                            mailrecordEntity_hb.setSendStatus("0");
                            mailrecordEntity_hb.setEmpno(hbuser.getLoginName());
                            mailrecordEntity_hb.setUrl(mail_hb.getUrl());
                            mailrecordEntity_hb.setUrlip(mail_hb.getUrlip());
                            mailrecordService.save(mailrecordEntity_hb);
                            if (hbuser != null && hbuser.getEmail() != null) {
                                mail_hb.setUsermail(hbuser.getEmail());
                                mailrecordEntity_hb.setUsermail(mail_hb.getUsermail());
                                mailrecordService.save(mailrecordEntity_hb);
                                //判斷是否及時發送
                                if (!"0".equals(hbuser.getEmailset())) {
                                    String sendResult = new SendMailUtil().sendMail(mail_hb);
                                    if ("0".equals(sendResult)) {
                                        //發送成功，更新標誌
                                        mailrecordEntity_hb.setSendStatus("1");
                                        mailrecordService.save(mailrecordEntity_hb);
                                    }
                                }

                            }
                        }
                        if (StringUtils.isNotEmpty(zwEmpno)) {
                            User zwuser = userService.getUser(zwEmpno);
                            //查詢是否有代理人
                            List<TProxyUserinfoEntity> audit_prox_zw = proxyUserinfoService.findProxUserByEmpnoNew(zwEmpno);
                            if (audit_prox_zw != null && audit_prox_zw.size() > 0) {
                                zwuser = userService.getUser(audit_prox_zw.get(0).getSupplyempno());
                            }
                            Mail mail_zw = new Mail();
                            mail_zw.setUsername(gtasks.getMakername());
                            mail_zw.setSystemname(dictService.getDictByTypeAndVlaue("sys_property", "sys_name").getLabel());
                            mail_zw.setDusername(zwuser.getName());
                            mail_zw.setChargerman("總務處對應窗口");
                            mail_zw.setOrdertype(relationEntity.getWfName());
                            mail_zw.setSerialno(serialno);
                            mail_zw.setOrderstatus(result);
                            mail_zw.setUrl(dictService.get(560).getValue());
                            mail_zw.setUrlip(dictService.get(561).getValue());
                            //保存發送記錄
                            TPubMailrecordEntity mailrecordEntity_zw = new TPubMailrecordEntity();
                            mailrecordEntity_zw.setChargerman(mail_zw.getChargerman());
                            mailrecordEntity_zw.setDusername(mail_zw.getDusername());
                            mailrecordEntity_zw.setOrderstatus(mail_zw.getOrderstatus());
                            mailrecordEntity_zw.setOrdertype(mail_zw.getOrdertype());
                            mailrecordEntity_zw.setSerialno(mail_zw.getSerialno());
                            mailrecordEntity_zw.setUsername(mail_zw.getUsername());
                            mailrecordEntity_zw.setUsermail(mail_zw.getUsermail());
                            mailrecordEntity_zw.setSendStatus("0");
                            mailrecordEntity_zw.setEmpno(zwuser.getLoginName());
                            mailrecordEntity_zw.setUrl(mail_zw.getUrl());
                            mailrecordEntity_zw.setUrlip(mail_zw.getUrlip());
                            mailrecordService.save(mailrecordEntity_zw);
                            if (zwuser != null && zwuser.getEmail() != null) {
                                mail_zw.setUsermail(zwuser.getEmail());
                                mailrecordEntity_zw.setUsermail(mail_zw.getUsermail());
                                mailrecordService.save(mailrecordEntity_zw);
                                //判斷是否及時發送
                                if (!"0".equals(zwuser.getEmailset())) {
                                    String sendResult = new SendMailUtil().sendMail(mail_zw);
                                    if ("0".equals(sendResult)) {
                                        //發送成功，更新標誌
                                        mailrecordEntity_zw.setSendStatus("1");
                                        mailrecordService.save(mailrecordEntity_zw);
                                    }
                                }

                            }
                        }

                    }
                    //系統需求申請單簽核完成郵件/聚會消息提醒至規劃工程師
                    if (relationEntity.getWorkflowid().contains("dzqh_xitongxuqiushenqing")) {
                        wfsystemprocessService.sendMailToGuiHua(relationEntity, serialno);
                    }
                    /*if (relationEntity.getWorkflowid().contains("dzqh_yuangongweiji")) {
                        WfIlegalProcessesEntity wfIlegalProcessesEntity = wfIlegalProcessesService.findBySerialno(serialno);
                        List<SaveRewardsInfoDto> saveRewardsInfos = new ArrayList();
                        SaveRewardsInfoDto saveRewardsInfoDto = null, saveRewardsInfoDto2 = null, saveRewardsInfoDto3 = null;
                        String rewardsLevel = "", rewardsLevel2 = "", rewardsLevel3 = "";
                        //記大過一次 記大過兩次 記小過一次 記小過兩次 記警告一次 記警告兩次	嘉獎一次 嘉獎兩次 記小功一次 記小功兩次 記大功一次 記大功兩次
                        if ("3".equals(wfIlegalProcessesEntity.getEndbigguo())) {
                            if ("1".equals(wfIlegalProcessesEntity.getEndbigguonum())) {
                                rewardsLevel = "記大過一次";
                            }
                            if ("2".equals(wfIlegalProcessesEntity.getEndbigguonum())) {
                                rewardsLevel = "記大過兩次";
                            }
                        }
                        if ("4".equals(wfIlegalProcessesEntity.getEndsmallguo())) {
                            if ("1".equals(wfIlegalProcessesEntity.getEndsmallguonum())) {
                                rewardsLevel2 = "記小過一次";
                            }
                            if ("2".equals(wfIlegalProcessesEntity.getEndsmallguonum())) {
                                rewardsLevel2 = "記小過兩次";
                            }
                        }
                        if ("5".equals(wfIlegalProcessesEntity.getEndalarm())) {
                            if ("1".equals(wfIlegalProcessesEntity.getEndalarmnum())) {
                                rewardsLevel3 = "記警告一次";
                            }
                            if ("2".equals(wfIlegalProcessesEntity.getEndalarmnum())) {
                                rewardsLevel3 = "記警告兩次";
                            }
                        }
                        for (WfIlegalItemprocessesEntity itemsEntity : wfIlegalProcessesEntity.getIlegalItemprocessesEntityList()) {
                            TQhUserformhsEntity userformhsEntity = tQhUserformhsService.findByEmpnoIgnoreIdStatus(itemsEntity.getApplyno());
                            if (rewardsLevel != "") {
                                saveRewardsInfoDto = new SaveRewardsInfoDto();
                                saveRewardsInfoDto.setEmpNo(itemsEntity.getApplyno());
                                saveRewardsInfoDto.setEmpName(itemsEntity.getApplyname());
                                saveRewardsInfoDto.setDept_no(itemsEntity.getDeptno());
                                saveRewardsInfoDto.setDept_name(userformhsEntity == null ? "無" : userformhsEntity.getDeptname());
                                String rewardsDate = new SimpleDateFormat("yyyy/MM/dd").format(wfIlegalProcessesEntity.getValidtime());
                                saveRewardsInfoDto.setRewardsDate(rewardsDate);
                                saveRewardsInfoDto.setRewardsType("2");
                                saveRewardsInfoDto.setRewardsLevel(rewardsLevel);
                                saveRewardsInfoDto.setRewardsDesc(wfIlegalProcessesEntity.getLaytype());
                                saveRewardsInfos.add(saveRewardsInfoDto);
                            }
                            if (rewardsLevel2 != "") {
                                saveRewardsInfoDto2 = new SaveRewardsInfoDto();
                                saveRewardsInfoDto2.setEmpNo(itemsEntity.getApplyno());
                                saveRewardsInfoDto2.setEmpName(itemsEntity.getApplyname());
                                saveRewardsInfoDto2.setDept_no(itemsEntity.getDeptno());
                                saveRewardsInfoDto2.setDept_name(userformhsEntity == null ? "無" : userformhsEntity.getDeptname());
                                String rewardsDate = new SimpleDateFormat("yyyy/MM/dd").format(wfIlegalProcessesEntity.getValidtime());
                                saveRewardsInfoDto2.setRewardsDate(rewardsDate);
                                saveRewardsInfoDto2.setRewardsType("2");
                                saveRewardsInfoDto2.setRewardsLevel(rewardsLevel2);
                                saveRewardsInfoDto2.setRewardsDesc(wfIlegalProcessesEntity.getLaytype());
                                saveRewardsInfos.add(saveRewardsInfoDto2);
                            }
                            if (rewardsLevel3 != "") {
                                saveRewardsInfoDto3 = new SaveRewardsInfoDto();
                                saveRewardsInfoDto3.setEmpNo(itemsEntity.getApplyno());
                                saveRewardsInfoDto3.setEmpName(itemsEntity.getApplyname());
                                saveRewardsInfoDto3.setDept_no(itemsEntity.getDeptno());
                                saveRewardsInfoDto3.setDept_name(userformhsEntity == null ? "無" : userformhsEntity.getDeptname());
                                String rewardsDate = new SimpleDateFormat("yyyy/MM/dd").format(wfIlegalProcessesEntity.getValidtime());
                                saveRewardsInfoDto3.setRewardsDate(rewardsDate);
                                saveRewardsInfoDto3.setRewardsType("2");
                                saveRewardsInfoDto3.setRewardsLevel(rewardsLevel3);
                                saveRewardsInfoDto3.setRewardsDesc(wfIlegalProcessesEntity.getLaytype());
                                saveRewardsInfos.add(saveRewardsInfoDto3);
                            }

                        }
                        if (saveRewardsInfos != null && saveRewardsInfos.size() != 0) {
                            String insertData = JSONUtil.toJsonStr(saveRewardsInfos);
                            String CodeUniq = "";
                            if ("IPEZBQ".equals(wfIlegalProcessesEntity.getMakerfactoryid()) || "CAAZZK".equals(wfIlegalProcessesEntity.getMakerfactoryid()) || "IPEZK".equals(wfIlegalProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_01";
                            } else if ("IPESZ".equals(wfIlegalProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_02";
                            } else if ("IPEJGQ".equals(wfIlegalProcessesEntity.getMakerfactoryid()) || "CAAZZC".equals(wfIlegalProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_03";
                            } else if ("YZSZ".equals(wfIlegalProcessesEntity.getMakerfactoryid()) || "IPEGZ".equals(wfIlegalProcessesEntity.getMakerfactoryid())) {//裕展深圳
                                CodeUniq = "ilegal_commend_info_04";
                            } else if ("IPETY".equals(wfIlegalProcessesEntity.getMakerfactoryid())) {//太原
                                CodeUniq = "ilegal_commend_info_05";
                            } else if ("YZZZ".equals(wfIlegalProcessesEntity.getMakerfactoryid())) {//裕展鄭州
                                CodeUniq = "ilegal_commend_info_06";
                            } else if ("IPEJY".equals(wfIlegalProcessesEntity.getMakerfactoryid()) || "CAAJY".equals(wfIlegalProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_07";
                            } else if ("ILVGWH".equals(wfIlegalProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_08";
                            } else if ("IPEJC".equals(wfIlegalProcessesEntity.getMakerfactoryid()) || "CAAJC".equals(wfIlegalProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_09";
                            } else if ("IPEHB".equals(wfIlegalProcessesEntity.getMakerfactoryid()) || "CAAHB".equals(wfIlegalProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_10";
                            } else if ("IPELK".equals(wfIlegalProcessesEntity.getMakerfactoryid()) || "CAALK".equals(wfIlegalProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_11";
                            } else if ("CAATY".equals(wfIlegalProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_12";
                            } else if ("IPEHY".equals(wfIlegalProcessesEntity.getMakerfactoryid()) || "IPEWH".equals(wfIlegalProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_13";
                            } else if ("CAA-ST".equals(wfIlegalProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_14";
                            } else if ("CAA-ZDH".equals(wfIlegalProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_15";
                            }
                            if (StringUtils.isNotEmpty(CodeUniq)) {
                                try {
                                    Dict dict = new Dict();
                                    dict.setCodeUniq(CodeUniq);
                                    dict.setType("ilegal_commend_info");
                                    String urlString = dictService.getUniqueDictByTypeAndCode(dict).getValue();
                                    String t1 = HttpUtil.post(urlString, insertData, 10000);
                                    logger.error(wfIlegalProcessesEntity.getSerialno() + "ilegal_commend_info-----" + t1);
                                    System.out.println(wfIlegalProcessesEntity.getSerialno() + "----拋轉狀態" + t1);
                                } catch (Exception e) {
                                    logger.info(e.getMessage(), e);
                                }
                            }
                        }
                    }*/
                    //審核完成要拋e通關系統
                    if (relationEntity.getWorkflowid().contains("dzqh_yuangongweiji")) {
                        wfIlegalProcessesService.sendToETongGuan(serialno);
                    }
                    /*if ("dzqh_yuangongjiajiang_v1".equals(relationEntity.getWorkflowid())) {
                        WfCommendProcessesEntity wfCommendProcessesEntity = wfCommendProcessesService.findBySerialno(serialno);
                        List<SaveRewardsInfoDto> saveRewardsInfos = new ArrayList();
                        SaveRewardsInfoDto saveRewardsInfoDto = null, saveRewardsInfoDto2 = null, saveRewardsInfoDto3 = null;
                        String rewardsLevel = "", rewardsLevel2 = "", rewardsLevel3 = "";
                        if ("1".equals(wfCommendProcessesEntity.getEndcommend())) {
                            if ("1".equals(wfCommendProcessesEntity.getEndcommendnum())) {
                                rewardsLevel = "嘉獎一次";
                            }
                            if ("2".equals(wfCommendProcessesEntity.getEndcommendnum())) {
                                rewardsLevel = "嘉獎兩次";
                            }
                        }
                        if ("2".equals(wfCommendProcessesEntity.getEndsmallgain())) {
                            if ("1".equals(wfCommendProcessesEntity.getEndsmallgainnum())) {
                                rewardsLevel2 = "記小功一次";
                            }
                            if ("2".equals(wfCommendProcessesEntity.getEndsmallgainnum())) {
                                rewardsLevel2 = "記小功兩次";
                            }
                        }
                        if ("3".equals(wfCommendProcessesEntity.getEndbiggain())) {
                            if ("1".equals(wfCommendProcessesEntity.getEndbiggainnum())) {
                                rewardsLevel3 = "記大功一次";
                            }
                            if ("2".equals(wfCommendProcessesEntity.getEndbiggainnum())) {
                                rewardsLevel3 = "記大功兩次";
                            }
                        }
                        for (WfCommendItemEntity itemsEntity : wfCommendProcessesEntity.getCommendItemEntityList()) {
                            TQhUserformhsEntity userformhsEntity = tQhUserformhsService.findByEmpnoIgnoreIdStatus(itemsEntity.getApplyno());
                            if (rewardsLevel != "") {
                                saveRewardsInfoDto = new SaveRewardsInfoDto();
                                saveRewardsInfoDto.setEmpNo(itemsEntity.getApplyno());
                                saveRewardsInfoDto.setEmpName(itemsEntity.getApplyname());
                                saveRewardsInfoDto.setDept_no(itemsEntity.getDeptno());
                                saveRewardsInfoDto.setDept_name(userformhsEntity == null ? "無" : userformhsEntity.getDeptname());
                                String rewardsDate = new SimpleDateFormat("yyyy/MM/dd").format(wfCommendProcessesEntity.getValidtime());
                                saveRewardsInfoDto.setRewardsDate(rewardsDate);
                                saveRewardsInfoDto.setRewardsType("1");
                                saveRewardsInfoDto.setRewardsLevel(rewardsLevel);
                                saveRewardsInfoDto.setRewardsDesc(wfCommendProcessesEntity.getReason());
                                saveRewardsInfos.add(saveRewardsInfoDto);
                            }
                            if (rewardsLevel2 != "") {
                                saveRewardsInfoDto2 = new SaveRewardsInfoDto();
                                saveRewardsInfoDto2.setEmpNo(itemsEntity.getApplyno());
                                saveRewardsInfoDto2.setEmpName(itemsEntity.getApplyname());
                                saveRewardsInfoDto2.setDept_no(itemsEntity.getDeptno());
                                saveRewardsInfoDto2.setDept_name(userformhsEntity == null ? "無" : userformhsEntity.getDeptname());
                                String rewardsDate = new SimpleDateFormat("yyyy/MM/dd").format(wfCommendProcessesEntity.getValidtime());
                                saveRewardsInfoDto2.setRewardsDate(rewardsDate);
                                saveRewardsInfoDto2.setRewardsType("1");
                                saveRewardsInfoDto2.setRewardsLevel(rewardsLevel2);
                                saveRewardsInfoDto2.setRewardsDesc(wfCommendProcessesEntity.getReason());
                                saveRewardsInfos.add(saveRewardsInfoDto2);
                            }
                            if (rewardsLevel3 != "") {
                                saveRewardsInfoDto3 = new SaveRewardsInfoDto();
                                saveRewardsInfoDto3.setEmpNo(itemsEntity.getApplyno());
                                saveRewardsInfoDto3.setEmpName(itemsEntity.getApplyname());
                                saveRewardsInfoDto3.setDept_no(itemsEntity.getDeptno());
                                saveRewardsInfoDto3.setDept_name(userformhsEntity == null ? "無" : userformhsEntity.getDeptname());
                                String rewardsDate = new SimpleDateFormat("yyyy/MM/dd").format(wfCommendProcessesEntity.getValidtime());
                                saveRewardsInfoDto3.setRewardsDate(rewardsDate);
                                saveRewardsInfoDto3.setRewardsType("1");
                                saveRewardsInfoDto3.setRewardsLevel(rewardsLevel3);
                                saveRewardsInfoDto3.setRewardsDesc(wfCommendProcessesEntity.getReason());
                                saveRewardsInfos.add(saveRewardsInfoDto3);
                            }
                        }
                        if (saveRewardsInfos != null && saveRewardsInfos.size() != 0) {
                            String insertData = JSONUtil.toJsonStr(saveRewardsInfos);
                            String CodeUniq = "";
                            if ("IPEZBQ".equals(wfCommendProcessesEntity.getMakerfactoryid()) || "CAAZZK".equals(wfCommendProcessesEntity.getMakerfactoryid()) || "IPEZK".equals(wfCommendProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_01";
                            } else if ("IPESZ".equals(wfCommendProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_02";
                            } else if ("IPEJGQ".equals(wfCommendProcessesEntity.getMakerfactoryid()) || "CAAZZC".equals(wfCommendProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_03";
                            } else if ("YZSZ".equals(wfCommendProcessesEntity.getMakerfactoryid()) || "IPEGZ".equals(wfCommendProcessesEntity.getMakerfactoryid())) {//裕展深圳
                                CodeUniq = "ilegal_commend_info_04";
                            } else if ("IPETY".equals(wfCommendProcessesEntity.getMakerfactoryid())) {//太原
                                CodeUniq = "ilegal_commend_info_05";
                            } else if ("YZZZ".equals(wfCommendProcessesEntity.getMakerfactoryid())) {//裕展鄭州
                                CodeUniq = "ilegal_commend_info_06";
                            } else if ("IPEJY".equals(wfCommendProcessesEntity.getMakerfactoryid()) || "CAAJY".equals(wfCommendProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_07";
                            } else if ("ILVGWH".equals(wfCommendProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_08";
                            } else if ("IPEJC".equals(wfCommendProcessesEntity.getMakerfactoryid()) || "CAAJC".equals(wfCommendProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_09";
                            } else if ("IPEHB".equals(wfCommendProcessesEntity.getMakerfactoryid()) || "CAAHB".equals(wfCommendProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_10";
                            } else if ("IPELK".equals(wfCommendProcessesEntity.getMakerfactoryid()) || "CAALK".equals(wfCommendProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_11";
                            } else if ("CAATY".equals(wfCommendProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_12";
                            } else if ("IPEHY".equals(wfCommendProcessesEntity.getMakerfactoryid()) || "IPEWH".equals(wfCommendProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_13";
                            } else if ("CAA-ST".equals(wfCommendProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_14";
                            } else if ("CAA-ZDH".equals(wfCommendProcessesEntity.getMakerfactoryid())) {
                                CodeUniq = "ilegal_commend_info_15";
                            }
                            if (StringUtils.isNotEmpty(CodeUniq)) {
                                try {
                                    Dict dict = new Dict();
                                    dict.setCodeUniq(CodeUniq);
                                    dict.setType("ilegal_commend_info");
                                    String urlString = dictService.getUniqueDictByTypeAndCode(dict).getValue();
                                    String t1 = HttpUtil.post(urlString, insertData, 10000);
                                    logger.error(wfCommendProcessesEntity.getSerialno() + "ilegal_commend_info-----" + t1);
                                    System.out.println(wfCommendProcessesEntity.getSerialno() + "----拋轉狀態" + t1);
                                } catch (Exception e) {
                                    logger.info(e.getMessage(), e);
                                }
                            }
                        }
                    }*/
                    //初婚/初育/員工複職表單數據拋轉薪資
                    if (relationEntity.getWorkflowid().contains("dzqh_chuhunlijinshenqing")) {
                        WfmarryprocessesEntity wfmarryprocesses = wfmarryprocessesService.findBySerialno(serialno);
                        wfmarryprocessesService.sendMarryToSalary(wfmarryprocesses);
                    }
                    if (relationEntity.getWorkflowid().contains("dzqh_shengyulijinshenqing")) {
                        WfbornprocessesEntity wfbornprocesses = wfbornprocessesService.findBySerialno(serialno);
                        wfbornprocessesService.sendBornToSalary(wfbornprocesses);
                    }
                    if (relationEntity.getWorkflowid().contains("dzqh_yuangongfuzhishenqing")) {
                        WffuzhiprocessEntity wffuzhiprocess = wffuzhiprocessService.findBySerialno(serialno);
                        wffuzhiprocessService.sendFuzhiToSalary(wffuzhiprocess);
                    }
                    //軟體安裝申請單數據拋運維
                    if (relationEntity.getWorkflowid().contains("dzqh_ruantianzhuangshenqingdan")) {
                        WfSoftinstallProcessEntity wfSoftinstall = wfSoftinstallProcessService.findBySerialno(serialno);
                        wfSoftinstallProcessService.sendSoftinstallToInfo(wfSoftinstall);
                    }
                    if (relationEntity.getWorkflowid().contains("dzqh_IEMailzhanghaoshenqing")) {
                        WfinternetemailprocessEntity wfinternetemail = wfinternetemailprocessService.findBySerialno(serialno);
                        String FormNumber = wfinternetemail.getSerialno();//系統單號
                        String OrderTime = DateUtil.getNowTime("yyyy-MM-dd HH:mm:ss");//拋單時間
                        String UndertakeID = wfinternetemail.getDealno();//承辦人工號
                        String UndertakerName = wfinternetemail.getDealname();//承辦人
                        String UnitCode1 = wfinternetemail.getDealno();//單位代碼
                        String Location1 = tQhFactoryidconfigService.findByFactiryid(wfinternetemail.getDealfactoryid()).getFactoryname();//廠區
                        String UndertakerPhone = wfinternetemail.getDealtel();//聯繫電話
                        String Unitname1 = wfinternetemail.getDealdeptname();//單位
                        String ContactEmail = wfinternetemail.getDealemail();//聯繫郵箱
                        String areaName = areabaseinfoService.getInfoByEntity("2", wfinternetemail.getApplyarea()).getName();
                        String buildingName = areabaseinfoService.getBuildingInfoEntity("3", wfinternetemail.getApplybuilding(), wfinternetemail.getApplyarea()).getName();
                        String Zones = areaName + buildingName;//區域樓棟
                        String NPIornot = dictService.getDictByTypeAndVlaue("security_area", wfinternetemail.getSecurityarea()).getLabel();//安保區域
                        //拋資安
                        Dict dict = new Dict();
                        dict.setCodeUniq("zi_an_type_01");
                        dict.setType("zi_an_type");
                        String url = dictService.getUniqueDictByTypeAndCode(dict).getValue();
                        //拋運維
                        Dict dictInfo = new Dict();
                        dictInfo.setCodeUniq("zi_xun_type_02");
                        dictInfo.setType("zi_xun_type");
                        String urlInfo = dictService.getUniqueDictByTypeAndCode(dictInfo).getValue();
                        List<WfinternetemailitemsEntity> items = wfinternetemail.getItemsEntitys();
                        for (int i = 0; i < items.size(); i++) {
                            String ApplicantID = items.get(i).getApplyno();//申請人工號
                            String Applicant = items.get(i).getApplyname();//申請人
                            String UnitCode2 = items.get(i).getApplydeptno();//單位代碼
                            String CostCode = items.get(i).getApplycostno();//費用代碼
                            String Location2 = tQhFactoryidconfigService.findByFactiryid(items.get(i).getApplyfactoryid()).getFactoryname();//所在廠區
                            String SalaryPosition = items.get(i).getApplyleveltype();//資位
                            String managementPosition = items.get(i).getApplymanager();//管理職
                            String UserEmail = items.get(i).getApplyemail();//聯繫郵箱
                            String Telephone = items.get(i).getApplyphone();//聯繫分機
                            String UnitName2 = items.get(i).getApplydeptname();//單位
                            String InternetEmailname = items.get(i).getIntermail() + items.get(i).getMailsuffix();//Internet E-Mail賬號
                            String Emailtype = items.get(i).getIntermailtype() + "賬號";//申請類型
                            String DemandType = items.get(i).getDemandtype();//需求類型
                            String InnerEmailadress = items.get(i).getOldintermail() == null ? "無" : items.get(i).getOldintermail();//原Email賬號名userformhsEntity == null ? "無" : userformhsEntity.getDeptname()
                            String InnerEmailname = items.get(i).getInternalmail() + "/" + items.get(i).getInnerchecknotesname();//內部郵箱
                            String Detail = items.get(i).getDescribtion();//需求說明
                            String starttime = "", endtime = "";
                            starttime = cn.hutool.core.date.DateUtil.format(items.get(i).getStarttime(), "yyyy-MM-dd");
                            endtime = cn.hutool.core.date.DateUtil.format(items.get(i).getEndtime(), "yyyy-MM-dd");
                            try {
                                String result1 = HttpUtil.post(url, JSONUtil.createObj().put("FormNumber", FormNumber).put("OrderTime", OrderTime)
                                        .put("UndertakeID", UndertakeID).put("UndertakerName", UndertakerName).put("UnitCode1", UnitCode1).put("Location1", Location1)
                                        .put("UndertakerPhone", UndertakerPhone).put("Unitname1", Unitname1).put("ContactEmail", ContactEmail).put("ApplicantID", ApplicantID)
                                        .put("Applicant", Applicant).put("UnitCode2", UnitCode2).put("CostCode", CostCode).put("Location2", Location2).put("SalaryPosition", SalaryPosition)
                                        .put("ManagementPosition", managementPosition).put("UserEmail", UserEmail).put("Zones", Zones).put("Telephone", Telephone).put("UnitName2", UnitName2)
                                        .put("NPIornot", NPIornot).put("InternetEmailname", InternetEmailname).put("Emailtype", Emailtype).put("DemandType", DemandType)
                                        .put("InnerEmailadress", InnerEmailadress).put("InnerEmailname", InnerEmailname).put("Detail", Detail), 10000);
                                logger.error(wfinternetemail.getSerialno() + "zi_an_type-----" + result1);
                                System.out.println(wfinternetemail.getSerialno() + "----拋轉狀態" + result1);
                            } catch (Exception e) {
                                logger.info(e.getMessage(), e);
                            }
                            try {
                                JSONObject jsonObject1 = JSONUtil.createObj().put("formnumber", FormNumber).put("ordertime", OrderTime)
                                        .put("undertakeid", UndertakeID).put("undertakername", UndertakerName).put("unitcode1", UnitCode1).put("location1", Location1)
                                        .put("undertakerphone", UndertakerPhone).put("unitname1", Unitname1).put("contactemail", ContactEmail).put("applicantid", ApplicantID)
                                        .put("applicant", Applicant).put("unitcode2", UnitCode2).put("costcode", CostCode).put("location2", Location2).put("salaryposition", SalaryPosition)
                                        .put("managementposition", managementPosition).put("useremail", UserEmail).put("zones", Zones).put("telephone", Telephone).put("unitname2", UnitName2)
                                        .put("npiornot", NPIornot).put("internetemailname", InternetEmailname).put("emailtype", Emailtype).put("demandtype", DemandType)
                                        .put("inneremailadress", InnerEmailadress).put("inneremailname", InnerEmailname).put("detail", Detail).put("starttime", starttime).put("endtime", endtime);
                                HttpRequest httpRequest = HttpRequest.post(urlInfo).body(JSONUtil.toJsonStr(jsonObject1));
                                System.out.println(httpRequest.execute());
                            } catch (Exception e) {
                                logger.info(e.getMessage(), e);
                            }
                        }
                    }
                    if (relationEntity.getWorkflowid().contains("dzqh_teshuwangluoshenqing")) {
                        WfspecialnetprocessEntity specialnet = wfspecialnetprocessService.findBySerialno(serialno);
                        String FormNumber = specialnet.getSerialno();//系統單號
                        String OrderTime = DateUtil.getNowTime("yyyy-MM-dd HH:mm:ss");//拋單時間
                        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        String updatetime = formatter.format(specialnet.getUpdateDate());
                        String UndertakeID = specialnet.getDealno();//承辦人工號
                        String UndertakerName = specialnet.getDealname();//承辦人
                        String UnitCode1 = specialnet.getDealdeptno();//單位代碼
                        String Location1 = tQhFactoryidconfigService.findByFactiryid(specialnet.getDealfactoryid()).getFactoryname();
                        String UndertakerPhone = specialnet.getDealtel();//聯繫分機
                        String Unitname1 = specialnet.getDealdeptname();//單位
                        String ContactEmail = specialnet.getDealemail();//聯繫郵箱
                        String ApplicantID = specialnet.getApplyno();//申請人工號
                        String Applicant = specialnet.getApplyname();//申請人
                        String UnitCode2 = specialnet.getApplydeptno();//單位代碼
                        String CostCode = specialnet.getApplycostno();//費用代碼
                        String Location2 = tQhFactoryidconfigService.findByFactiryid(specialnet.getApplyfactoryid()).getFactoryname();//所在廠區
                        String SalaryPosition = specialnet.getApplyleveltype();
                        String ManagementPosition = specialnet.getApplymanager();
                        String UserEmail = specialnet.getApplyemail();
                        String areaName = areabaseinfoService.getInfoByEntity("2", specialnet.getApplyarea()).getName();
                        String buildingName = areabaseinfoService.getBuildingInfoEntity("3", specialnet.getApplybuilding(), specialnet.getApplyarea()).getName();
                        String Zones = areaName + buildingName;//區域樓棟
                        String Telephone = specialnet.getApplyphone();
                        String UnitName2 = specialnet.getApplydeptname();
                        String NPIornot = dictService.getDictByTypeAndVlaue("security_area", specialnet.getSecurityarea()).getLabel();//安保區域
                        String StartTime = new SimpleDateFormat("yyyy-MM-dd").format(specialnet.getSpecialnetstartdate());
                        String CloseTime = new SimpleDateFormat("yyyy-MM-dd").format(specialnet.getSpecialnetenddate());
                        String Detail = specialnet.getDescribtion();
                        String BusinessType = dictService.getDictByTypeAndVlaue("dict_businessType", specialnet.getBusinesstype()).getLabel();//業務類型
                        String ApplyAction = dictService.getDictByTypeAndVlaue("dict_applyAction", specialnet.getApplyaction()).getLabel();//申請動作
                        //拋資安
                        try {
                            Dict dict = new Dict();
                            dict.setCodeUniq("zi_an_type_02");
                            dict.setType("zi_an_type");
                            String url = dictService.getUniqueDictByTypeAndCode(dict).getValue();
                            String result1 = HttpUtil.post(url, JSONUtil.createObj().put("FormNumber", FormNumber).put("OrderTime", OrderTime)
                                    .put("UndertakeID", UndertakeID).put("UndertakerName", UndertakerName).put("UnitCode1", UnitCode1).put("Location1", Location1)
                                    .put("UndertakerPhone", UndertakerPhone).put("Unitname1", Unitname1).put("ContactEmail", ContactEmail).put("ApplicantID", ApplicantID)
                                    .put("Applicant", Applicant).put("UnitCode2", UnitCode2).put("CostCode", CostCode).put("Location2", Location2).put("SalaryPosition", SalaryPosition)
                                    .put("ManagementPosition", ManagementPosition).put("UserEmail", UserEmail).put("Zones", Zones).put("Telephone", Telephone).put("UnitName2", UnitName2)
                                    .put("NPIornot", NPIornot).put("StartTime", StartTime).put("CloseTime", CloseTime).put("Detail", Detail), 10000);
                            logger.error(specialnet.getSerialno() + "zi_an_type-----" + result1);
                            System.out.println(specialnet.getSerialno() + "----拋轉狀態" + result1);
                        } catch (Exception e) {
                            logger.info(e.getMessage(), e);
                        }
                        //拋運維
                        try {
                            Dict dictInfo = new Dict();
                            dictInfo.setCodeUniq("zi_xun_type_06");
                            dictInfo.setType("zi_xun_type");
                            String urlInfo = dictService.getUniqueDictByTypeAndCode(dictInfo).getValue();
                            JSONObject jsonObject1 = JSONUtil.createObj().put("formnumber", FormNumber).put("ordertime", OrderTime).put("updatetime", updatetime)
                                    .put("undertakeid", UndertakeID).put("undertakername", UndertakerName).put("unitcode1", UnitCode1).put("location1", Location1)
                                    .put("undertakerphone", UndertakerPhone).put("unitname1", Unitname1).put("contactemail", ContactEmail).put("applicantid", ApplicantID)
                                    .put("applicant", Applicant).put("unitcode2", UnitCode2).put("costcode", CostCode).put("location2", Location2).put("salaryposition", SalaryPosition)
                                    .put("managementposition", ManagementPosition).put("useremail", UserEmail).put("zones", Zones).put("telephone", Telephone).put("unitname2", UnitName2)
                                    .put("npiornot", NPIornot).put("starttime", StartTime).put("closetime", CloseTime).put("detail", Detail).put("isipinternet", BusinessType).put("operationofapplication", ApplyAction);
                            HttpRequest httpRequest = HttpRequest.post(urlInfo).body(JSONUtil.toJsonStr(jsonObject1));
                            System.out.println(httpRequest.execute());
                        } catch (Exception e) {
                            logger.info(e.getMessage(), e);
                        }
                    }
                    if (relationEntity.getWorkflowid().contains("dzqh_wwwswzhshenqing")) {
                        WfwwwprocessEntity wfwwwprocessEntity = wfwwwprocessService.findBySerialno(serialno);
                        String FormNumber = wfwwwprocessEntity.getSerialno();//系統單號
                        String OrderTime = DateUtil.getNowTime("yyyy-MM-dd HH:mm:ss");//拋單時間
                        String UndertakeID = wfwwwprocessEntity.getDealno();//承辦人工號
                        String UndertakerName = wfwwwprocessEntity.getDealname();//承辦人
                        String UnitCode1 = wfwwwprocessEntity.getDealdeptno();//單位代碼
                        String Location1 = tQhFactoryidconfigService.findByFactiryid(wfwwwprocessEntity.getDealfactoryid()).getFactoryname();//廠區
                        String UndertakerPhone = wfwwwprocessEntity.getDealtel();//聯繫分機
                        String Unitname1 = wfwwwprocessEntity.getDealdeptname();//單位
                        String ContactEmail = wfwwwprocessEntity.getDealemail();//聯繫郵箱
                        String ApplicantID = wfwwwprocessEntity.getApplyno();//申請人工號
                        String Applicant = wfwwwprocessEntity.getApplyname();//申請人
                        String UnitCode2 = wfwwwprocessEntity.getApplydeptno();//單位代碼
                        String CostCode = wfwwwprocessEntity.getApplycostno();//費用代碼
                        String Location2 = tQhFactoryidconfigService.findByFactiryid(wfwwwprocessEntity.getApplyfactoryid()).getFactoryname();//所在廠區
                        String SalaryPosition = wfwwwprocessEntity.getApplyleveltype();
                        String ManagementPosition = wfwwwprocessEntity.getApplymanager();
                        String UserEmail = wfwwwprocessEntity.getApplyemail();
                        String areaName = areabaseinfoService.getInfoByEntity("2", wfwwwprocessEntity.getApplyarea()).getName();
                        String buildingName = areabaseinfoService.getBuildingInfoEntity("3", wfwwwprocessEntity.getApplybuilding(), wfwwwprocessEntity.getApplyarea()).getName();
                        String Zones = areaName + buildingName;//區域樓棟
                        String Telephone = wfwwwprocessEntity.getApplyphone();
                        String UnitName2 = wfwwwprocessEntity.getApplydeptname();
                        String NPIornot = dictService.getDictByTypeAndVlaue("security_area", wfwwwprocessEntity.getSecurityarea()).getLabel();//安保區域
                        String PhoneNumber = wfwwwprocessEntity.getApplymobile();
                        String UserName2 = wfwwwprocessEntity.getWwwip();//用戶名
                        String DemandType = dictService.getDictByTypeAndVlaue("dict_wwwType", wfwwwprocessEntity.getWwwtype()).getLabel();//申請類型
                        String StartTime = new SimpleDateFormat("yyyy-MM-dd").format(wfwwwprocessEntity.getWwwstarttime());
                        String CloseTime = new SimpleDateFormat("yyyy-MM-dd").format(wfwwwprocessEntity.getWwwendtime());
                        String DemandProjects = "", DemandProject = "";
                        if (wfwwwprocessEntity.getWwwitem().contains(",")) {
                            String param[] = wfwwwprocessEntity.getWwwitem().split(",");
                            for (int i = 0; i < param.length; i++) {
                                DemandProjects += dictService.getDictByTypeAndVlaue("dict_applyProject", param[i]).getLabel() + ",";
                            }
                            DemandProject = DemandProjects.substring(0, DemandProjects.length() - 1);
                        } else {
                            DemandProject = dictService.getDictByTypeAndVlaue("dict_applyProject", wfwwwprocessEntity.getWwwitem()).getLabel();
                        }
                        //
                        String Detail = wfwwwprocessEntity.getDescribtion();
                        //拋資安
                        try {
                            Dict dict = new Dict();
                            dict.setCodeUniq("zi_an_type_03");
                            dict.setType("zi_an_type");
                            String url = dictService.getUniqueDictByTypeAndCode(dict).getValue();
                            String result1 = HttpUtil.post(url, JSONUtil.createObj().put("FormNumber", FormNumber).put("OrderTime", OrderTime)
                                    .put("UndertakeID", UndertakeID).put("UndertakerName", UndertakerName).put("UnitCode1", UnitCode1).put("Location1", Location1)
                                    .put("UndertakerPhone", UndertakerPhone).put("Unitname1", Unitname1).put("ContactEmail", ContactEmail).put("ApplicantID", ApplicantID)
                                    .put("Applicant", Applicant).put("UnitCode2", UnitCode2).put("CostCode", CostCode).put("Location2", Location2).put("SalaryPosition", SalaryPosition)
                                    .put("ManagementPosition", ManagementPosition).put("UserEmail", UserEmail).put("Zones", Zones).put("Telephone", Telephone).put("UnitName2", UnitName2)
                                    .put("NPIornot", NPIornot).put("PhoneNumber", PhoneNumber).put("UserName2", UserName2).put("DemandType", DemandType).put("StartTime", StartTime)
                                    .put("CloseTime", CloseTime).put("DemandProject", DemandProject).put("Detail", Detail), 10000);

                            logger.error(wfwwwprocessEntity.getSerialno() + "zi_an_type-----" + result1);
                            System.out.println(wfwwwprocessEntity.getSerialno() + "----拋轉狀態" + result1);
                        } catch (Exception e) {
                            logger.info(e.getMessage(), e);
                        }
                        //拋運維
                        Dict dictInfo = new Dict();
                        dictInfo.setCodeUniq("zi_xun_type_01");
                        dictInfo.setType("zi_xun_type");
                        String urlInfo = dictService.getUniqueDictByTypeAndCode(dictInfo).getValue();
                       /* String result2 = HttpUtil.post(urlInfo, JSONUtil.createObj().put("formnumber", FormNumber).put("ordertime", OrderTime)
                                .put("undertakeid", UndertakeID).put("undertakername",UndertakerName).put("unitcode1",UnitCode1).put("location1",Location1)
                                .put("undertakerphone",UndertakerPhone).put("unitname1",Unitname1).put("contactemail",ContactEmail).put("applicantid",ApplicantID)
                                .put("applicant",Applicant).put("unitcode2",UnitCode2).put("costcode",CostCode).put("location2",Location2).put("salaryposition",SalaryPosition)
                                .put("managementposition",ManagementPosition).put("useremail",UserEmail).put("zones",Zones).put("telephone",Telephone).put("unitname2",UnitName2)
                                .put("npiornot",NPIornot).put("phonenumber",PhoneNumber).put("username2",UserName2).put("demandtype",DemandType).put("starttime",StartTime)
                                .put("closetime",CloseTime).put("demandproject",DemandProject).put("detail",Detail));*/

                        try {
                            JSONObject jsonObject1 = JSONUtil.createObj().put("formnumber", FormNumber).put("ordertime", OrderTime)
                                    .put("undertakeid", UndertakeID).put("undertakername", UndertakerName).put("unitcode1", UnitCode1).put("location1", Location1)
                                    .put("undertakerphone", UndertakerPhone).put("unitname1", Unitname1).put("contactemail", ContactEmail).put("applicantid", ApplicantID)
                                    .put("applicant", Applicant).put("unitcode2", UnitCode2).put("costcode", CostCode).put("location2", Location2).put("salaryposition", SalaryPosition)
                                    .put("managementposition", ManagementPosition).put("useremail", UserEmail).put("zones", Zones).put("telephone", Telephone).put("unitname2", UnitName2)
                                    .put("npiornot", NPIornot).put("phonenumber", PhoneNumber).put("username2", UserName2).put("demandtype", DemandType).put("starttime", StartTime)
                                    .put("closetime", CloseTime).put("demandproject", DemandProject).put("detail", Detail);
                            HttpRequest httpRequest = HttpRequest.post(urlInfo).body(JSONUtil.toJsonStr(jsonObject1));
                            System.out.println(httpRequest.execute());
                        } catch (Exception e) {
                            logger.info(e.getMessage(), e);
                        }


                      /*  logger.error(wfwwwprocessEntity.getSerialno()+"zi_xun_type-----"+result2);
                        System.out.println(wfwwwprocessEntity.getSerialno()+"----拋轉狀態"+result2);*/
                    }
                    if (relationEntity.getWorkflowid().contains("dzqh_sslvpnfuwushenqing")) {
                        WfsslvpnprocessEntity wfsslvpnprocess = wfsslvpnprocessService.findBySerialno(serialno);
                        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        String updatetime = formatter.format(wfsslvpnprocess.getUpdateDate());
                        String OrderTime = DateUtil.getNowTime("yyyy-MM-dd HH:mm:ss");//拋單時間
                        String UndertakeID = wfsslvpnprocess.getDealno();//承辦人工號
                        String UndertakerName = wfsslvpnprocess.getDealname();//承辦人
                        String UnitCode1 = wfsslvpnprocess.getDealdeptno();//單位代碼
                        String Location1 = tQhFactoryidconfigService.findByFactiryid(wfsslvpnprocess.getDealfactoryid()).getFactoryname();
                        String UndertakerPhone = wfsslvpnprocess.getDealtel();//聯繫分機
                        String Unitname1 = wfsslvpnprocess.getDealdeptname();//單位
                        String ContactEmail = wfsslvpnprocess.getDealemail();//聯繫郵箱
                        String areaName = areabaseinfoService.getInfoByEntity("2", wfsslvpnprocess.getApplyarea()).getName();
                        String buildingName = areabaseinfoService.getBuildingInfoEntity("3", wfsslvpnprocess.getApplybuilding(), wfsslvpnprocess.getApplyarea()).getName();
                        String Zones = areaName + buildingName;//區域樓棟
                        String NPIornot = dictService.getDictByTypeAndVlaue("security_area", wfsslvpnprocess.getSecurityarea()).getLabel();//安保區域
                        String ApplyWay = dictService.getDictByTypeAndVlaue("dic_applytype", wfsslvpnprocess.getDealapplytype()).getLabel();
                        //拋資安
                        Dict dict = new Dict();
                        dict.setCodeUniq("zi_an_type_04");
                        dict.setType("zi_an_type");
                        String url = dictService.getUniqueDictByTypeAndCode(dict).getValue();
                        //拋運維
                        Dict dictInfo = new Dict();
                        dictInfo.setCodeUniq("zi_xun_type_04");
                        dictInfo.setType("zi_xun_type");
                        String urlInfo = dictService.getUniqueDictByTypeAndCode(dictInfo).getValue();
                        List<WfsslvpnpitemsEntity> wfsslvpnpitemsEntities = wfsslvpnprocess.getItemsEntitys();
                        String FormNumber = "";
                        for (int i = 0; i < wfsslvpnpitemsEntities.size(); i++) {
                            if (wfsslvpnpitemsEntities.size() > 1) {
                                FormNumber = wfsslvpnprocess.getSerialno() + "-" + i;
                            } else {
                                FormNumber = wfsslvpnprocess.getSerialno();
                            }
                            String ApplicantID = wfsslvpnpitemsEntities.get(i).getApplyno();//申請人工號
                            String Applicant = wfsslvpnpitemsEntities.get(i).getApplyname();//申請人
                            String UnitCode2 = wfsslvpnpitemsEntities.get(i).getApplydeptno();//單位代碼
                            String CostCode = wfsslvpnpitemsEntities.get(i).getApplycostno();//費用代碼
                            String SalaryPosition = wfsslvpnpitemsEntities.get(i).getApplyleveltype();//資位
                            String ManagementPosition = wfsslvpnpitemsEntities.get(i).getApplymanager();//管理職
                            String UserEmail = wfsslvpnpitemsEntities.get(i).getApplyemail();//聯繫郵箱
                            String UserFactory = tQhFactoryidconfigService.findByFactiryid(wfsslvpnpitemsEntities.get(i).getApplyfactoryid()).getFactoryname();//所在廠區
                            String Telephone = wfsslvpnpitemsEntities.get(i).getApplyphone();//聯繫分機
                            String UnitName2 = wfsslvpnpitemsEntities.get(i).getApplydeptname();//單位
                            String UserName = wfsslvpnpitemsEntities.get(i).getVpnname();//用戶名
                            String ApplicantType = dictService.getDictByTypeAndVlaue("dict_applyManType", wfsslvpnpitemsEntities.get(i).getVpnapplytype()).getLabel();//申請人類型
                            String DemandType = dictService.getDictByTypeAndVlaue("dict_vpnType", wfsslvpnpitemsEntities.get(i).getVpntype()).getLabel();//申請類型
                            String OS = wfsslvpnpitemsEntities.get(i).getVpnrequiretype();//操作系統
                            String PhoneNumber = wfsslvpnpitemsEntities.get(i).getVpnhostip();//手機號碼
                            String TimeLimited = dictService.getDictByTypeAndVlaue("dict_vpnExpire", wfsslvpnpitemsEntities.get(i).getVpnexpire()).getLabel();//使用期限
                            String StartTime = new SimpleDateFormat("yyyy-MM-dd").format(wfsslvpnpitemsEntities.get(i).getVpnstarttime());//開始時間
                            String CloseTime = new SimpleDateFormat("yyyy-MM-dd").format(wfsslvpnpitemsEntities.get(i).getVpnendtime());//結束日期
                            String Detail = wfsslvpnpitemsEntities.get(i).getDescribtion();
                            //拋資安
                            try {
                                String result1 = HttpUtil.post(url, JSONUtil.createObj().put("FormNumber", FormNumber).put("OrderTime", OrderTime)
                                        .put("UndertakeID", UndertakeID).put("UndertakerName", UndertakerName).put("UnitCode1", UnitCode1).put("Location1", Location1)
                                        .put("UndertakerPhone", UndertakerPhone).put("Unitname1", Unitname1).put("ContactEmail", ContactEmail).put("Zones", Zones)
                                        .put("NPIornot", NPIornot).put("ApplyWay", ApplyWay).put("ApplicantID", ApplicantID).put("Applicant", Applicant).put("UnitCode2", UnitCode2)
                                        .put("CostCode", CostCode).put("SalaryPosition", SalaryPosition).put("ManagementPosition", ManagementPosition).put("UserEmail", UserEmail).put("UserFactory", UserFactory)
                                        .put("Telephone", Telephone).put("UnitName2", UnitName2).put("UserName", UserName).put("ApplicantType", ApplicantType).put("DemandType", DemandType)
                                        .put("OS", OS).put("PhoneNumber", PhoneNumber).put("TimeLimited", TimeLimited).put("StartTime", StartTime).put("CloseTime", CloseTime).put("Detail", Detail), 10000);
                                logger.error(wfsslvpnprocess.getSerialno() + "zi_an_type-----" + result1);
                                System.out.println(wfsslvpnprocess.getSerialno() + "----拋轉狀態" + result1);
                            } catch (Exception e) {
                                logger.info(e.getMessage(), e);
                            }
                            //拋運維
                            try {
                                JSONObject jsonObject1 = JSONUtil.createObj().put("formnumber", FormNumber).put("ordertime", OrderTime).put("updatetime", updatetime)
                                        .put("undertakeid", UndertakeID).put("undertakername", UndertakerName).put("unitcode1", UnitCode1).put("location1", Location1)
                                        .put("undertakerphone", UndertakerPhone).put("unitname1", Unitname1).put("contactemail", ContactEmail).put("zones", Zones)
                                        .put("npiornot", NPIornot).put("applyway", ApplyWay).put("applicantid", ApplicantID).put("applicant", Applicant).put("unitcode2", UnitCode2)
                                        .put("costcode", CostCode).put("salaryposition", SalaryPosition).put("managementposition", ManagementPosition).put("useremail", UserEmail).put("userfactory", UserFactory)
                                        .put("telephone", Telephone).put("unitname2", UnitName2).put("username", UserName).put("applicanttype", ApplicantType).put("demandtype", DemandType)
                                        .put("os", OS).put("phonenumber", PhoneNumber).put("timelimited", TimeLimited).put("starttime", StartTime).put("closetime", CloseTime).put("detail", Detail);
                                HttpRequest httpRequest = HttpRequest.post(urlInfo).body(JSONUtil.toJsonStr(jsonObject1));
                                System.out.println(httpRequest.execute());
                            } catch (Exception e) {
                                logger.info(e.getMessage(), e);
                            }
                        }
                    }
                    if (relationEntity.getWorkflowid().contains("dzqh_wuxianwangluoshenqing")) {
                        WfvlanprocessEntity wfvlanprocess = wfvlanprocessService.findBySerialno(serialno);
                        String OrderTime = DateUtil.getNowTime("yyyy-MM-dd HH:mm:ss");//拋單時間
                        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        String updatetime = formatter.format(wfvlanprocess.getUpdateDate());
                        String UndertakeID = wfvlanprocess.getDealno();//承辦人工號
                        String UndertakerName = wfvlanprocess.getDealname();//承辦人
                        String UnitCode1 = wfvlanprocess.getDealdeptno();//單位代碼
                        String Location1 = tQhFactoryidconfigService.findByFactiryid(wfvlanprocess.getDealfactoryid()).getFactoryname();
                        String UndertakerPhone = wfvlanprocess.getDealtel();//聯繫分機
                        String areaName = areabaseinfoService.getInfoByEntity("2", wfvlanprocess.getApplyarea()).getName();
                        String buildingName = areabaseinfoService.getBuildingInfoEntity("3", wfvlanprocess.getApplybuilding(), wfvlanprocess.getApplyarea()).getName();
                        String UseZone = areaName + buildingName;//區域樓棟
                        String NPIorNot = dictService.getDictByTypeAndVlaue("security_area", wfvlanprocess.getSecurityarea()).getLabel();//安保區域
                        String UseWay1 = dictService.getDictByTypeAndVlaue("dic_applytype", wfvlanprocess.getApplytype()).getLabel();//申请方式
                        //拋資安
                        Dict dict = new Dict();
                        dict.setCodeUniq("zi_an_type_05");
                        dict.setType("zi_an_type");
                        String url = dictService.getUniqueDictByTypeAndCode(dict).getValue();
                        //拋運維
                        Dict dictInfo = new Dict();
                        dictInfo.setCodeUniq("zi_xun_type_05");
                        dictInfo.setType("zi_xun_type");
                        String urlInfo = dictService.getUniqueDictByTypeAndCode(dictInfo).getValue();
                        List<WfvlanitemEntity> wfvlanitemEntities = wfvlanitemService.findItemEntityes(serialno);
                        String FormNumber = "";
                        for (int i = 0; i < wfvlanitemEntities.size(); i++) {
                            if (wfvlanitemEntities.size() > 1) {
                                FormNumber = wfvlanprocess.getSerialno() + "-" + i;
                            } else {
                                FormNumber = wfvlanprocess.getSerialno();
                            }
                            String ApplicantID = wfvlanitemEntities.get(i).getApplyno();//申請人工號
                            String Applicant = wfvlanitemEntities.get(i).getApplyname();//申請人
                            String UnitCode2 = wfvlanitemEntities.get(i).getApplydeptno();//單位代碼
                            String SalaryPosition = wfvlanitemEntities.get(i).getApplyleveltype();//資位
                            String ManagementPosition = wfvlanitemEntities.get(i).getApplymanager();//管理職
                            String ApplicantEmail = wfvlanitemEntities.get(i).getApplyemail();//聯繫郵箱
                            String ApplicantPhone = wfvlanitemEntities.get(i).getApplyphone();//聯繫分機
                            String UnitName = wfvlanitemEntities.get(i).getApplydeptname();//單位
                            String PhoneNumber = wfvlanitemEntities.get(i).getVlanphone();//手機號碼
                            String Location2 = "", ServerWay = "", UseWay2 = "", Terminal = "", SSID = "";
                            String ssidelse = wfvlanitemEntities.get(i).getSsidelse() == null ? "" : wfvlanitemEntities.get(i).getSsidelse();
                            if ("單筆申請".equals(UseWay1)) {
                                Location2 = tQhFactoryidconfigService.findByFactiryid(wfvlanitemEntities.get(i).getApplyfactoryid()).getFactoryname();//所在廠區
                                /*ServerWay = dictService.getDictByTypeAndVlaue("dic_servertype", wfvlanitemEntities.get(i).getVlanapplytype()).getLabel();//服务类型
                                UseWay2 = dictService.getDictByTypeAndVlaue("dic_usertype", wfvlanitemEntities.get(i).getVlanusertype()).getLabel();//用戶类型
                                Terminal = dictService.getDictByTypeAndVlaue("dic_zhongduanleixing", wfvlanitemEntities.get(i).getVlanterminaltype()).getLabel();//終端類型
                                SSID = dictService.getDictByTypeAndVlaue("dic_ssid", wfvlanitemEntities.get(i).getSsid()).getLabel() + ssidelse;//SSID*/
                                ServerWay = wfvlanitemEntities.get(i).getVlanapplytype();//服务类型
                                UseWay2 = wfvlanitemEntities.get(i).getVlanusertype();//用戶类型
                                Terminal = wfvlanitemEntities.get(i).getVlanterminaltype();//終端類型
                                SSID = wfvlanitemEntities.get(i).getSsid() + ssidelse;//SSID
                            } else {
                                Location2 = wfvlanitemEntities.get(i).getApplyfactoryid();
                                ServerWay = wfvlanitemEntities.get(i).getVlanapplytype();//服务类型
                                UseWay2 = wfvlanitemEntities.get(i).getVlanusertype();//用戶类型
                                Terminal = wfvlanitemEntities.get(i).getVlanterminaltype();//終端類型
                                SSID = wfvlanitemEntities.get(i).getSsid() + ssidelse;//SSID
                            }
                            String UserName = wfvlanitemEntities.get(i).getVlanname();//用戶名
                            String StartTime = new SimpleDateFormat("yyyy-MM-dd").format(wfvlanitemEntities.get(i).getVlanstarttime());//開始時間
                            String CloseTime = new SimpleDateFormat("yyyy-MM-dd").format(wfvlanitemEntities.get(i).getVlanendtime());//結束日期
                            String TimeLimited = StartTime + "至" + CloseTime;
                            String MacAdress = wfvlanitemEntities.get(i).getVlanterminalmac();//MAC地址
                            String Detail = wfvlanitemEntities.get(i).getDescribtion();//需求詳細說明
                            //拋資安
                            try {
                                String result1 = HttpUtil.post(url, JSONUtil.createObj().put("FormNumber", FormNumber).put("OrderTime", OrderTime)
                                        .put("UndertakeID", UndertakeID).put("UndertakerName", UndertakerName).put("UnitCode1", UnitCode1).put("Location1", Location1)
                                        .put("UndertakerPhone", UndertakerPhone).put("UseZone", UseZone).put("NPIorNot", NPIorNot).put("UseWay1", UseWay1)
                                        .put("ApplicantID", ApplicantID).put("Applicant", Applicant).put("UnitCode2", UnitCode2).put("SalaryPosition", SalaryPosition).put("ManagementPosition", ManagementPosition)
                                        .put("ApplicantEmail", ApplicantEmail).put("ApplicantPhone", ApplicantPhone).put("UnitName", UnitName).put("PhoneNumber", PhoneNumber).put("Location2", Location2)
                                        .put("UserName", UserName).put("TimeLimited", TimeLimited).put("ServerWay", ServerWay).put("UseWay2", UseWay2).put("Terminal", Terminal)
                                        .put("SSID", SSID).put("MacAdress", MacAdress).put("Detail", Detail), 10000);
                                logger.error(wfvlanprocess.getSerialno() + "zi_an_type-----" + result1);
                                System.out.println(wfvlanprocess.getSerialno() + "----拋轉狀態" + result1);
                            } catch (Exception e) {
                                logger.info(e.getMessage(), e);
                            }
                            //拋運維
                            try {
                                JSONObject jsonObject1 = JSONUtil.createObj().put("formnumber", FormNumber).put("ordertime", OrderTime).put("updatetime", updatetime)
                                        .put("undertakeid", UndertakeID).put("undertakername", UndertakerName).put("unitcode1", UnitCode1).put("location1", Location1)
                                        .put("undertakerphone", UndertakerPhone).put("usezone", UseZone).put("npiornot", NPIorNot).put("useway1", UseWay1)
                                        .put("applicantid", ApplicantID).put("applicant", Applicant).put("unitcode2", UnitCode2).put("salaryposition", SalaryPosition).put("managementposition", ManagementPosition)
                                        .put("applicantemail", ApplicantEmail).put("applicantphone", ApplicantPhone).put("unitname", UnitName).put("phonenumber", PhoneNumber).put("location2", Location2)
                                        .put("username", UserName).put("timelimited", TimeLimited).put("serverway", ServerWay).put("useway2", UseWay2).put("terminal", Terminal)
                                        .put("ssid", SSID).put("macadress", MacAdress).put("detail", Detail);
                                HttpRequest httpRequest = HttpRequest.post(urlInfo).body(JSONUtil.toJsonStr(jsonObject1));
                                System.out.println(httpRequest.execute());
                            } catch (Exception e) {
                                logger.info(e.getMessage(), e);
                            }
                        }
                    }
                    /*if (relationEntity.getWorkflowid().contains("dzqh_ruantianzhuangshenqingdan")) {
                        WfSoftinstallProcessEntity wfSoftinstall = wfSoftinstallProcessService.findBySerialno(serialno);
                        String OrderTime = DateUtil.getNowTime("yyyy-MM-dd HH:mm:ss");//拋單時間
                        String UndertakeID = wfSoftinstall.getDealno();
                        String UndertakerName = wfSoftinstall.getDealname();
                        String UnitCode = wfSoftinstall.getDealdeptno();
                        String UndertakerEmail = wfSoftinstall.getDealemail();
                        String UndertakerPhone = wfSoftinstall.getDealtel();
                        String ApplicantID = wfSoftinstall.getApplyno();
                        String Applicant = wfSoftinstall.getApplyname();
                        String Location = tQhFactoryidconfigService.findByFactiryid(wfSoftinstall.getApplyfactoryid()).getFactoryname();
                        String areaName = areabaseinfoService.getInfoByEntity("2", wfSoftinstall.getApplyarea()).getName();
                        String buildingName = areabaseinfoService.getBuildingInfoEntity("3", wfSoftinstall.getApplybuilding(), wfSoftinstall.getApplyarea()).getName();
                        String AppliedArea = areaName + buildingName;//區域樓棟
                        String ApplicantEmail = wfSoftinstall.getApplyemail();
                        String ApplicantPhone = wfSoftinstall.getApplyphone();
                        String UnitName = wfSoftinstall.getApplydeptname();
                        Dict dict = new Dict();
                        dict.setCodeUniq("zi_an_type_06");
                        dict.setType("zi_an_type");
                        String url = dictService.getUniqueDictByTypeAndCode(dict).getValue();
                        List<WfSoftinstallItemsEntity> wfSoftinstallItemsEntities = wfSoftinstall.getItemsEntitys();
                        String FormNumber = "";
                        for (int i = 0; i < wfSoftinstallItemsEntities.size(); i++) {
                            if (wfSoftinstallItemsEntities.size() > 1) {
                                FormNumber = wfSoftinstall.getSerialno() + "-" + i;
                            } else {
                                FormNumber = wfSoftinstall.getSerialno();
                            }
                            String UserID = wfSoftinstallItemsEntities.get(i).getUserno();
                            String UserName = wfSoftinstallItemsEntities.get(i).getUsername();
                            String CampuseName = tQhFactoryidconfigService.findByFactiryid(wfSoftinstallItemsEntities.get(i).getUserfactoryid()).getFactoryname();
                            String userareaName = areabaseinfoService.getInfoByEntity("2", wfSoftinstallItemsEntities.get(i).getArealist()).getName();
                            String userbuildingName = areabaseinfoService.getBuildingInfoEntity("3", wfSoftinstallItemsEntities.get(i).getBuildinglist(), wfSoftinstallItemsEntities.get(i).getArealist()).getName();
                            String Zones = userareaName + userbuildingName;
                            String ComputerName = wfSoftinstallItemsEntities.get(i).getPcname();
                            String IPadress = wfSoftinstallItemsEntities.get(i).getPcip();
                            String SoftwareName = wfSoftinstallItemsEntities.get(i).getSoftname();
                            String SoftwareVersion = wfSoftinstallItemsEntities.get(i).getSoftversion();
                            try {
                                String result1 = HttpUtil.post(url, JSONUtil.createObj().put("FormNumber", FormNumber).put("OrderTime", OrderTime)
                                        .put("UndertakeID", UndertakeID).put("UndertakerName", UndertakerName).put("UnitCode", UnitCode).put("UndertakerEmail", UndertakerEmail)
                                        .put("UndertakerPhone", UndertakerPhone).put("ApplicantID", ApplicantID).put("Applicant", Applicant).put("Location", Location)
                                        .put("AppliedArea", AppliedArea).put("ApplicantEmail", ApplicantEmail).put("ApplicantPhone", ApplicantPhone).put("UnitName", UnitName)
                                        .put("softwareInstallList", wfSoftinstallItemsEntities.size()).put("UserID", UserID).put("UserName", UserName).put("CampuseName", CampuseName).put("Zones", Zones).put("ComputerName", ComputerName)
                                        .put("IPadress", IPadress).put("SoftwareName", SoftwareName).put("SoftwareVersion", SoftwareVersion), 10000);
                                logger.error(wfSoftinstall.getSerialno() + "zi_an_type-----" + result1);
                                System.out.println(wfSoftinstall.getSerialno() + "----拋轉狀態" + result1);
                            } catch (Exception e) {
                                logger.info(e.getMessage(), e);
                            }
                        }
                    }*/
                    if (relationEntity.getWorkflowid().contains("dzqh_gerendiannaoteshuquanxianshenqing")) {
                        WfpcprivilegeprocessEntity wfpcprivilege = wfpcprivilegeprocessService.findBySerialno(serialno);
                        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        String OrderTime = DateUtil.getNowTime("yyyy-MM-dd HH:mm:ss");//拋單時間
                        String updatetime = formatter.format(wfpcprivilege.getUpdateDate());
                        String UndertakeID = wfpcprivilege.getDealno();
                        String UndertakerName = wfpcprivilege.getDealname();
                        String UnitCode = wfpcprivilege.getDealdeptno();
                        String Location1 = tQhFactoryidconfigService.findByFactiryid(wfpcprivilege.getApplyfactoryid()).getFactoryname();
                        String SalaryPosition = wfpcprivilege.getApplyleveltype();
                        String ManagementPosition = wfpcprivilege.getApplymanager();
                        String ContactEmail = wfpcprivilege.getDealemail();
                        String areaName = areabaseinfoService.getInfoByEntity("2", wfpcprivilege.getApplyarea()).getName();
                        String buildingName = areabaseinfoService.getBuildingInfoEntity("3", wfpcprivilege.getApplybuilding(), wfpcprivilege.getApplyarea()).getName();
                        String UseArea = areaName + buildingName;//區域樓棟
                        String TEL = wfpcprivilege.getDealtel();
                        String DEPT = wfpcprivilege.getDealdeptname();
                        String SecurityArea = dictService.getDictByTypeAndVlaue("security_area", wfpcprivilege.getSecurityarea()).getLabel();//安保區域
                        String OperationOfApplication = dictService.getDictByTypeAndVlaue("dict_applyOperate", wfpcprivilege.getApplyoperate()).getLabel();//申請操作

                        //申請類型
                        String usbapplytype = wfpcprivilege.getUsbapplytype() == null ? "" : "USB";
                        String shellapplytype = wfpcprivilege.getShellapplytype() == null ? "" : "外設";
                        String specialprivilege = wfpcprivilege.getSpecialprivilege() == null ? "" : "特殊權限";
                        String ApplicationType = usbapplytype + " " + shellapplytype + " " + specialprivilege;

                        //申請項目
                        String applyproject = wfpcprivilege.getApplyproject() == null ? "" : wfpcprivilege.getApplyproject();
                        String applyProject = "";
                        if ("1".equals(applyproject)) {
                            applyProject = "連接設備";
                        } else if ("2".equals(applyproject)) {
                            applyProject = "只讀權限";
                        } else if ("3".equals(applyproject)) {
                            applyProject = "存儲權限";
                        }
                        String cdrom = wfpcprivilege.getCdrom() == null ? "" : "CD-ROM";
                        String floppydrive = wfpcprivilege.getFloppydrive() == null ? "" : "軟驅";
                        String soundcard = wfpcprivilege.getSoundcard() == null ? "" : "聲卡";
                        //新添加欄位  設備屬性，設備ID，DL管控
                        String devicedcategory = null;
                        String deviceid = null;
                        String dlcontrol = null;
                        if ("1".equals(wfpcprivilege.getApplyproject())) {
                            devicedcategory = wfpcprivilege.getConnectdevicetype();
                            deviceid = wfpcprivilege.getConnectdeviceno();
                            dlcontrol = wfpcprivilege.getConnectdevicedl();
                        } else if ("2".equals(wfpcprivilege.getApplyproject())) {
                            devicedcategory = wfpcprivilege.getReaddevicetype();
                            deviceid = wfpcprivilege.getReaddeviceno();
                            dlcontrol = wfpcprivilege.getReaddevicedl();
                        } else if ("3".equals(wfpcprivilege.getApplyproject())) {
                            devicedcategory = wfpcprivilege.getSavedevicetype();
                            deviceid = wfpcprivilege.getSavedeviceno();
                            dlcontrol = wfpcprivilege.getSavedevicedl();
                        }
                        String accountprivilege = wfpcprivilege.getAccountprivilege() == null ? "" : dictService.getDictByTypeAndVlaue("dict_account", wfpcprivilege.getAccountprivilege()).getLabel();
                        String shareprivilege = wfpcprivilege.getShareprivilege() == null ? "" : "共享";
                        String ApplicationProject = applyProject + " " + cdrom + " " + floppydrive + " " + soundcard + " " + accountprivilege + " " + shareprivilege;
                        //需求說明
                        String usbdesc = wfpcprivilege.getUsbdesc() == null ? "" : wfpcprivilege.getUsbdesc();
                        String cdromdesc = wfpcprivilege.getCdromdesc() == null ? "" : wfpcprivilege.getCdromdesc();
                        String floppydrivedesc = wfpcprivilege.getFloppydrivedesc() == null ? "" : wfpcprivilege.getFloppydrivedesc();
                        String soundcarddesc = wfpcprivilege.getSoundcarddesc() == null ? "" : wfpcprivilege.getSoundcarddesc();
                        String accountspecialdesc = wfpcprivilege.getAccountspecialdesc() == null ? "" : wfpcprivilege.getAccountspecialdesc();
                        String sharespecialdesc = wfpcprivilege.getSharespecialdesc() == null ? "" : wfpcprivilege.getSharespecialdesc();
                        String StatementOfNeeds = usbdesc + " " + cdromdesc + " " + floppydrivedesc + " " + soundcarddesc + " " + accountspecialdesc + " " + sharespecialdesc;
                        //拋資安
                        Dict dict = new Dict();
                        dict.setCodeUniq("zi_an_type_07");
                        dict.setType("zi_an_type");
                        String url = dictService.getUniqueDictByTypeAndCode(dict).getValue();
                        //拋運維
                        Dict dictInfo = new Dict();
                        dictInfo.setCodeUniq("zi_xun_type_03");
                        dictInfo.setType("zi_xun_type");
                        String urlInfo = dictService.getUniqueDictByTypeAndCode(dictInfo).getValue();
                        List<WfpcprivilegeitemsEntity> wfpcprivilegeitems = wfpcprivilegeitemsService.findItemEntityes(serialno);
                        String FormNumber = "";
                        for (int i = 0; i < wfpcprivilegeitems.size(); i++) {
                            if (wfpcprivilegeitems.size() > 1) {
                                FormNumber = wfpcprivilege.getSerialno() + "-" + i;
                            } else {
                                FormNumber = wfpcprivilege.getSerialno();
                            }
                            String ApplicantID = wfpcprivilegeitems.get(i).getApplyno();
                            String Applicant = wfpcprivilegeitems.get(i).getApplyname();
                            String Location2 = tQhFactoryidconfigService.findByFactiryid(wfpcprivilegeitems.get(i).getApplyfactoryid()).getFactoryname();
                            String pcpareaName = areabaseinfoService.getInfoByEntity("2", wfpcprivilegeitems.get(i).getApplyarea()).getName();
                            String pcpbuildingName = areabaseinfoService.getBuildingInfoEntity("3", wfpcprivilegeitems.get(i).getApplybuilding(), wfpcprivilegeitems.get(i).getApplyarea()).getName();
                            String AppliedArea = pcpareaName + pcpbuildingName;
                            String ComputerName = wfpcprivilegeitems.get(i).getPcname();
                            String IPaddress = wfpcprivilegeitems.get(i).getApplyip();
                            /*try {
                                //拋資安
                                JSONObject jsonObject = JSONUtil.createObj().put("FormNumber", FormNumber).put("OrderTime", OrderTime)
                                        .put("UndertakeID", UndertakeID).put("UndertakerName", UndertakerName).put("UnitCode", UnitCode).put("Location", Location1)
                                        .put("SalaryPosition", SalaryPosition).put("ManagementPosition", ManagementPosition).put("ContactEmail", ContactEmail).put("UseArea", UseArea)
                                        .put("TEL", TEL).put("DEPT", DEPT).put("SecurityArea", SecurityArea).put("ApplicantID", ApplicantID).put("Applicant", Applicant)
                                        .put("Location2", Location2).put("AppliedArea", AppliedArea).put("ComputerName", ComputerName).put("IPaddress", IPaddress)
                                        .put("OperationOfApplication", OperationOfApplication).put("ApplicationType", ApplicationType).put("ApplicationProject", ApplicationProject)
                                        .put("StatementOfNeeds", StatementOfNeeds);
                                if (StrUtil.isNotEmpty(devicedcategory) && StrUtil.isNotEmpty(deviceid) && StrUtil.isNotEmpty(dlcontrol)) {
                                    jsonObject.put("devicedcategory", devicedcategory).put("deviceid", deviceid).put("dlcontrol", dlcontrol);
                                }
                                String result1 = HttpUtil.post(url, jsonObject, 10000);
                                logger.error(wfpcprivilege.getSerialno() + "zi_an_type-----" + result1);
                                System.out.println(wfpcprivilege.getSerialno() + "----拋轉狀態" + result1);
                            } catch (Exception e) {
                                logger.info(e.getMessage(), e);
                            }*/
                            try {
                                //拋運維
                                JSONObject jsonObject1 = JSONUtil.createObj().put("formnumber", FormNumber).put("ordertime", OrderTime).put("updatetime", updatetime)
                                        .put("undertakeid", UndertakeID).put("undertakername", UndertakerName).put("unitcode", UnitCode).put("location", Location1)
                                        .put("salaryposition", SalaryPosition).put("managementposition", ManagementPosition).put("contactemail", ContactEmail).put("usearea", UseArea)
                                        .put("tel", TEL).put("dept", DEPT).put("securityarea", SecurityArea).put("applicantid", ApplicantID).put("applicant", Applicant)
                                        .put("location2", Location2).put("appliedarea", AppliedArea).put("computername", ComputerName).put("ipaddress", IPaddress)
                                        .put("operationofapplication", OperationOfApplication).put("applicationtype", ApplicationType).put("applicationproject", ApplicationProject)
                                        .put("statementofneeds", StatementOfNeeds);
                                if (StrUtil.isNotEmpty(devicedcategory) && StrUtil.isNotEmpty(deviceid) && StrUtil.isNotEmpty(dlcontrol)) {
                                    jsonObject1.put("devicedcategory", devicedcategory).put("deviceid", deviceid).put("dlcontrol", dlcontrol);
                                }
                                HttpRequest httpRequest = HttpRequest.post(urlInfo).body(JSONUtil.toJsonStr(jsonObject1));
                                System.out.println(httpRequest.execute());
                            } catch (Exception e) {
                                logger.info(e.getMessage(), e);
                            }
                        }
                    }
                    if (relationEntity.getWorkflowid().contains("dzqh_adyuzhanghaoshenqing")) {
                        WfadaccountprocessEntity wfadaccountprocessEntity = wfadaccountprocessService.findBySerialno(serialno);
                        String ADAREA = areabaseinfoService.getInfoByEntity("2", wfadaccountprocessEntity.getApplyarea()).getName();
                        String ADBUILDING = areabaseinfoService.getBuildingInfoEntity("3", wfadaccountprocessEntity.getApplybuilding(), wfadaccountprocessEntity.getApplyarea()).getName();
                        Dict dict = new Dict();
                        dict.setCodeUniq("zi_an_type_08");
                        dict.setType("zi_an_type");
                        String url = dictService.getUniqueDictByTypeAndCode(dict).getValue();
                        List<WfadaccountitemEntity> wfadaccountitems = wfadaccountitemService.findItemEntityes(serialno);
                        String SERIALNO = "";
                        for (int i = 0; i < wfadaccountitems.size(); i++) {
                            String ID = wfadaccountitems.get(i).getId();
                            if (wfadaccountitems.size() > 1) {
                                SERIALNO = wfadaccountprocessEntity.getSerialno() + "-" + i;
                            } else {
                                SERIALNO = wfadaccountprocessEntity.getSerialno();
                            }
                            String USERNO = wfadaccountitems.get(i).getUserno();
                            String USERNAME = wfadaccountitems.get(i).getUsername();
                            String ACCOUNT = wfadaccountitems.get(i).getAccount();
                            String PCNAME = wfadaccountitems.get(i).getPcname();
                            String APPLYIP = wfadaccountitems.get(i).getApplyip();
                            String APPLYACTION = dictService.getDictByTypeAndVlaue("dict_adAddAction", wfadaccountitems.get(i).getApplyaction()).getLabel();
                            ;
                            String SHUNXU = wfadaccountitems.get(i).getShunxu();
                            String ADFACTORYID = wfadaccountitems.get(i).getAdfactoryid();
                            String WYNAME = wfadaccountitems.get(i).getWyname();
                            String ISNP = wfadaccountitems.get(i).getIsnpi();
                            String CREATE_BY = wfadaccountitems.get(i).getCreateBy();
                            Date CREATE_DATE = wfadaccountitems.get(i).getCreateDate();
                            String UPDATE_BY = wfadaccountitems.get(i).getUpdateBy();
                            Date UPDATE_DATE = wfadaccountitems.get(i).getUpdateDate();
                            String DEL_FLAG = wfadaccountitems.get(i).getDelFlag();
                            try {
                                String result1 = HttpUtil.post(url, JSONUtil.createObj().put("ID", ID).put("SERIALNO", SERIALNO).put("USERNO", USERNO).put("USERNAME", USERNAME)
                                        .put("ADAREA", ADAREA).put("ADBUILDING", ADBUILDING).put("ACCOUNT", ACCOUNT).put("PCNAME", PCNAME).put("APPLYIP", APPLYIP).put("APPLYACTION", APPLYACTION)
                                        .put("CREATE_BY", CREATE_BY).put("CREATE_DATE", CREATE_DATE).put("UPDATE_BY", UPDATE_BY).put("UPDATE_DATE", UPDATE_DATE).put("DEL_FLAG", DEL_FLAG)
                                        .put("SHUNXU", SHUNXU).put("ADFACTORYID", ADFACTORYID).put("WYNAME", WYNAME).put("ISNP", ISNP), 10000);
                                logger.error(wfadaccountprocessEntity.getSerialno() + "zi_an_type-----" + result1);
                                System.out.println(wfadaccountprocessEntity.getSerialno() + "----拋轉狀態" + result1);
                            } catch (Exception e) {
                                logger.info(e.getMessage(), e);
                            }
                        }
                    }
                    if (relationEntity.getWorkflowid().contains("dzqh_lieyinquanxianshenqing")) {
                        WfprintauthinfoprocessEntity wfprintauthinfoprocess = wfprintauthinfoprocessService.findBySerialno(serialno);
                        String SERIALNO = wfprintauthinfoprocess.getSerialno();
                        String MAKERNO = wfprintauthinfoprocess.getMakerno();
                        String MAKERNAME = wfprintauthinfoprocess.getMakername();
                        String MAKERIP = wfprintauthinfoprocess.getMakerip() == null ? "" : wfprintauthinfoprocess.getMakerip();
                        String PRINTERPOSITION = wfprintauthinfoprocess.getPrinterposition();
                        String PRINTERNAME = wfprintauthinfoprocess.getPrintername();
                        String PRINTERMANAGER = wfprintauthinfoprocess.getPrintermanager();
                        String PRINTSERVERADDRESS = wfprintauthinfoprocess.getPrintserveraddress();
                        String PRINTDEPTNO = wfprintauthinfoprocess.getPrintdeptno();
                        String PRINTDEPTNAME = wfprintauthinfoprocess.getPrintdeptname();
                        String PRINTIP = wfprintauthinfoprocess.getPrintip();
                        Date COMPLETTIME = ObjectUtil.isNull(wfprintauthinfoprocess.getComplettime()) ? new Date() : wfprintauthinfoprocess.getComplettime();
                        String CREATE_BY = wfprintauthinfoprocess.getCreateBy();
                        Date CREATE_DATE = wfprintauthinfoprocess.getCreateDate();
                        String UPDATE_BY = wfprintauthinfoprocess.getUpdateBy();
                        Date UPDATE_DATE = wfprintauthinfoprocess.getUpdateDate();
                        String DEL_FLAG = wfprintauthinfoprocess.getDelFlag();
                        String PRINTCONTROL = wfprintauthinfoprocess.getPrintcontrol();
                        String APPLYFACTORYID = tQhFactoryidconfigService.findByFactiryid(wfprintauthinfoprocess.getApplyfactoryid()).getFactoryname();
                        String MAKERFACTORYID = tQhFactoryidconfigService.findByFactiryid(wfprintauthinfoprocess.getMakerfactoryid()).getFactoryname();
                        String APPLYCHOOSEFACTORYID = tQhFactoryidconfigService.findByFactiryid(wfprintauthinfoprocess.getApplychoosefactoryid()).getFactoryname();
                        Dict dict = new Dict();
                        dict.setCodeUniq("zi_an_type_09");
                        dict.setType("zi_an_type");
                        String url = dictService.getUniqueDictByTypeAndCode(dict).getValue();
                        try {
                            String result1 = HttpUtil.post(url, JSONUtil.createObj().put("SERIALNO", SERIALNO).put("MAKERNO", MAKERNO)
                                    .put("MAKERNAME", MAKERNAME).put("MAKERIP", MAKERIP).put("PRINTERPOSITION", PRINTERPOSITION).put("COMPLETTIME", COMPLETTIME)
                                    .put("PRINTERNAME", PRINTERNAME).put("PRINTERMANAGER", PRINTERMANAGER).put("PRINTSERVERADDRESS", PRINTSERVERADDRESS).put("PRINTDEPTNO", PRINTDEPTNO)
                                    .put("CREATE_BY", CREATE_BY).put("CREATETIME", CREATE_DATE).put("UPDATE_BY", UPDATE_BY).put("UPDATE_DATE", UPDATE_DATE).put("DEL_FLAG", DEL_FLAG)
                                    .put("PRINTDEPTNAME", PRINTDEPTNAME).put("PRINTIP", PRINTIP).put("PRINTCONTROL", PRINTCONTROL).put("APPLYFACTORYID", APPLYFACTORYID).put("MAKERFACTORYID", MAKERFACTORYID)
                                    .put("APPLYCHOOSEFACTORYID", APPLYCHOOSEFACTORYID), 10000);
                            logger.error(wfprintauthinfoprocess.getSerialno() + "zi_an_type-----" + result1);
                            System.out.println(wfprintauthinfoprocess.getSerialno() + "----拋轉狀態" + result1);
                        } catch (Exception e) {
                            logger.info(e.getMessage(), e);
                        }
                    }
                    if (relationEntity.getWorkflowid().contains("dzqh_lieyinquanxianshenqing")) {
                        WfprintauthinfoprocessEntity wfprintauthinfoprocess = wfprintauthinfoprocessService.findBySerialno(serialno);
                        List<WfprintauthitemsEntity> wfprintauthitems = wfprintauthitemsService.findItemEntityes(serialno);
                        String SERIALNO = "";
                        for (int i = 0; i < wfprintauthitems.size(); i++) {
                            if (wfprintauthitems.size() > 1) {
                                SERIALNO = wfprintauthitems.get(i).getSerialno() + "-" + i;
                            } else {
                                SERIALNO = wfprintauthitems.get(i).getSerialno();
                            }
                            String APPLYNO = wfprintauthitems.get(i).getApplyno();
                            String APPLYNAME = wfprintauthitems.get(i).getApplyname();
                            String PCNO = wfprintauthitems.get(i).getPcno();
                            String PCIP = wfprintauthitems.get(i).getPcip();
                            String APPLYTYPE = dictService.getDictByTypeAndVlaue("dict_lieYinApplyType", wfprintauthitems.get(i).getApplytype()).getLabel();
                            String IMPORTID = wfprintauthitems.get(i).getImportid() == null ? "" : wfprintauthitems.get(i).getImportid();
                            String ISPRINTCHECK = wfprintauthitems.get(i).getIsprintcheck();
                            String CHECKNO = wfprintauthitems.get(i).getCheckno();
                            String CHECKNAME = wfprintauthitems.get(i).getCheckname();
                            String SECURITYAREA = dictService.getDictByTypeAndVlaue("security_area", wfprintauthitems.get(i).getSecurityarea()).getLabel();
                            String APPLYACTION = "";
                            String CREATE_BY = wfprintauthitems.get(i).getCreateBy();
                            Date CREATE_DATE = wfprintauthitems.get(i).getCreateDate();
                            String UPDATE_BY = wfprintauthitems.get(i).getUpdateBy();
                            Date UPDATE_DATE = wfprintauthitems.get(i).getUpdateDate();
                            String DEL_FLAG = wfprintauthitems.get(i).getDelFlag();
                            String SHUNX = wfprintauthitems.get(i).getShunxu().toString();
                            Dict dict = new Dict();
                            dict.setCodeUniq("zi_an_type_10");
                            dict.setType("zi_an_type");
                            String url = dictService.getUniqueDictByTypeAndCode(dict).getValue();
                            try {
                                String result1 = HttpUtil.post(url, JSONUtil.createObj().put("SERIALNO", SERIALNO).put("APPLYNO", APPLYNO)
                                        .put("APPLYNAME", APPLYNAME).put("PCNO", PCNO).put("PCIP", PCIP).put("SHUNXU", wfprintauthitems.get(i).getShunxu()).put("ID", wfprintauthitems.get(i).getId())
                                        .put("APPLYTYPE", APPLYTYPE).put("IMPORTID", IMPORTID).put("ISPRINTCHECK", ISPRINTCHECK).put("CHECKNO", CHECKNO).put("REQUIRRCONTENT", wfprintauthitems.get(i).getRequirecontent())
                                        .put("CREATE_BY", CREATE_BY).put("CREATE_DATE", CREATE_DATE).put("UPDATE_BY", UPDATE_BY).put("UPDATE_DATE", UPDATE_DATE).put("DEL_FLAG", DEL_FLAG)
                                        .put("CHECKNAME", CHECKNAME).put("SECURITYAREA", SECURITYAREA).put("APPLYACTION", APPLYACTION).put("SHUNX", SHUNX), 10000);
                                logger.error(wfprintauthinfoprocess.getSerialno() + "zi_an_type-----" + result1);
                                System.out.println(wfprintauthinfoprocess.getSerialno() + "----拋轉狀態" + result1);
                            } catch (Exception e) {
                                logger.info(e.getMessage(), e);
                            }
                        }
                    }
                    if (relationEntity.getWorkflowid().contains("dzqh_SuperNotesfuwushenqing")) {
                        WfSupernotesEntity supernotesEntity = wfSupernotesService.findBySerialno(serialno);
                        Dict dict = new Dict();
                        dict.setCodeUniq("zi_an_type_11");
                        dict.setType("zi_an_type");
                        String url = dictService.getUniqueDictByTypeAndCode(dict).getValue();
                        String form_no = supernotesEntity.getSerialno();
                        String mail_addr = supernotesEntity.getApplyemail();
                        Date app_time = supernotesEntity.getCreatetime();
                        Date fin_time = ObjectUtil.isNull(supernotesEntity.getComplettime()) ? new Date() : supernotesEntity.getComplettime();
                        ;
                        String yn_supernotes = supernotesEntity.getApplynotestype();
                        try {
                            if ("add".equals(supernotesEntity.getApplytype())) {
                                List<WfSupernotesItemsAddEntity> itemsEntityList = supernotesEntity.getItemsEntityList();
                                for (WfSupernotesItemsAddEntity addEntity : itemsEntityList) {
                                    String yn_npi = dictService.getDictByTypeAndVlaue("dict_supernotesSet", addEntity.getIsnpi()).getLabel();
                                    String yn_manager = dictService.getDictByTypeAndVlaue("dict_supernotesSet", addEntity.getIsmailboxperson()).getLabel();
                                    String user_id = addEntity.getNotesapplyno();
                                    String user_name = addEntity.getNotesapplyname();
                                    String ip_addr = addEntity.getIpbind();
                                    String job_type = "新增";
                                    JSONObject jsonObject = JSONUtil.createObj().put("form_no", form_no).put("mail_addr", mail_addr).put("yn_npi", yn_npi)
                                            .put("yn_manager", yn_manager).put("yn_supernotes", yn_supernotes).put("user_id", user_id).put("user_name", user_name).put("ip_addr", ip_addr).put("job_type", job_type)
                                            .put("oper_type", "").put("app_time", app_time).put("fin_time", fin_time);
                                    result = HttpUtil.post(url, JSONUtil.toJsonStr(jsonObject), 10000);
                                    logger.error(serialno + "------zi_an_type-----" + result);
                                    System.out.println(serialno + "----拋轉狀態" + result);
                                }
                            } else if ("update".equals(supernotesEntity.getApplytype())) {
                                List<WfSupernotesItemsChaEntity> itemsEntityChaList = supernotesEntity.getItemsEntityChaList();
                                for (WfSupernotesItemsChaEntity chaEntity : itemsEntityChaList) {
                                    String yn_npi = dictService.getDictByTypeAndVlaue("dict_supernotesSet", chaEntity.getIsnpi()).getLabel();
                                    String yn_manager = dictService.getDictByTypeAndVlaue("dict_supernotesSet", chaEntity.getIsmailboxperson()).getLabel();
                                    String user_id = chaEntity.getNotesapplyno();
                                    String user_name = chaEntity.getNotesapplyname();
                                    String ip_addr = chaEntity.getIpbind();
                                    String job_type = "變更";
                                    String oper_type = dictService.getDictByTypeAndVlaue("dict_ismate", chaEntity.getExchangetype()).getLabel();
                                    JSONObject jsonObject = JSONUtil.createObj().put("form_no", form_no).put("mail_addr", mail_addr).put("yn_npi", yn_npi)
                                            .put("yn_manager", yn_manager).put("yn_supernotes", yn_supernotes).put("user_id", user_id).put("user_name", user_name).put("ip_addr", ip_addr).put("job_type", job_type)
                                            .put("oper_type", oper_type).put("app_time", app_time).put("fin_time", fin_time);
                                    result = HttpUtil.post(url, JSONUtil.toJsonStr(jsonObject), 10000);
                                    logger.error(serialno + "------zi_an_type-----" + result);
                                    System.out.println(serialno + "----拋轉狀態" + result);
                                }
                            }
                        } catch (Exception e) {
                            logger.info(e.getMessage(), e);
                        }
                    }

                } else {

                    //獲取審核人員郵箱，發送郵件信息用
                    taskInfo = processService.currentTaskInfo(config);
                    info = taskInfo.getTaskInfoList().get(0);


                    /**新舊工號兼容優化**/
                    newAndOld = OldJobNoToNewNo.getNewNo(info.getAssignee());
                    /**新舊工號兼容優化**/
                    User user = userService.getUser(newAndOld.getNewEmpno());
                    //查詢是否有代理人
                    List<TProxyUserinfoEntity> audit_prox = proxyUserinfoService.findProxUserByEmpnoNew(newAndOld.getNewEmpno());
                    if (audit_prox != null && audit_prox.size() > 0) {
                        user = userService.getUser(audit_prox.get(0).getSupplyempno());
                    }

                    WfConifgEntity conifgEntity = conifgService.findUnique(relationEntity.getWorkflowid());
                    String validStr = UUID.randomUUID().toString().replace("-", "");

                    relationEntity.setUpdateBy(user.getLoginName());  //審核成功更新審核時間 與審核人信息
                    relationEntity.setUpdateDate(new Date());
                    allRelationService.update(relationEntity);

                    Gtasks gtasks = allRelationService.queryMyGtasks(relationEntity.getSerialno(), relationEntity.getDtoName());

                    //查詢用戶信息
                    if (user != null) { //用户信息为空则不发 邮件及聚会
                        TQhUserformhsEntity userformhsEntity = tQhUserformhsService.findByEmpnoIgnoreIdStatus(user.getLoginName());
                        Mail mail = new Mail();
                        mail.setChargerman(UserUtil.getCurrentUser().getName());
                        mail.setSystemname(dictService.getDictByTypeAndVlaue("sys_property", "sys_name").getLabel());
                        mail.setDusername(gtasks.getMakername());
                        mail.setOrdertype(relationEntity.getWfName());
                        mail.setSerialno(serialno);
                        mail.setUrl(dictService.get(560).getValue());
                        mail.setUrlip(dictService.get(561).getValue());
                        if (user != null && user.getEmail() != null) {
                            mail.setUsermail(user.getEmail());
                        }
                        if ("0".equals(status)) {
                            //通過
                            mail.setOrderstatus("2");
                            mail.setUsername(userformhsEntity == null ? "" : userformhsEntity.getEmpname());
                            mail.setFreeloginurl(dictService.get(558).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getAction() + "/" + serialno);
                            mail.setFreeloginurlip(dictService.get(559).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getAction() + "/" + serialno);

                        } else {
                            //駁回
                            mail.setOrderstatus("4");
                            mail.setUsername(gtasks.getMakername() != null ? gtasks.getMakername() : userformhsEntity.getEmpname());
                            mail.setFreeloginurl(dictService.get(558).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getModaction() + "/" + serialno);
                            mail.setFreeloginurlip(dictService.get(559).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getModaction() + "/" + serialno);

                        }
                        //保存發送記錄
                        TPubMailrecordEntity mailrecordEntity = new TPubMailrecordEntity();
                        mailrecordEntity.setChargerman(mail.getChargerman());
                        mailrecordEntity.setDusername(mail.getDusername());
                        mailrecordEntity.setOrderstatus(mail.getOrderstatus());
                        mailrecordEntity.setOrdertype(mail.getOrdertype());
                        mailrecordEntity.setSerialno(mail.getSerialno());
                        mailrecordEntity.setUsername(mail.getUsername());
                        mailrecordEntity.setSendStatus("0");
                        mailrecordEntity.setEmpno(user.getLoginName());
                        mailrecordEntity.setUrl(mail.getUrl());
                        mailrecordEntity.setUrlip(mail.getUrlip());
                        mailrecordEntity.setFreeloginurl(mail.getFreeloginurl());
                        mailrecordEntity.setFreeloginurlip(mail.getFreeloginurlip());
                        mailrecordEntity.setValidStr(validStr);
                        mailrecordService.save(mailrecordEntity);
                        if (user != null && user.getEmail() != null) {
                            mail.setUsermail(user.getEmail());
                            mailrecordEntity.setUsermail(mail.getUsermail());
                            mailrecordService.save(mailrecordEntity);
                            if ("1".equals(conifgEntity.getAppType())) {
                                Dict dict2 = new Dict();
                                dict2.setType("entfrmIpebgIP_app");
                                dict2.setCodeUniq("entfrmIpebgIP_app01");
                                String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
                                mail.setAppAuditUrl(StrUtil.concat(true, fronUrl, conifgEntity.getAppAuditUrl(), "?serialno=", mail.getSerialno()));
                            } else {
                                mail.setAppAuditUrl(StrUtil.concat(true, conifgEntity.getAppAuditUrl(), "?serialno=", mail.getSerialno()));
                            }
                            if (!relationEntity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                                String sendResult = new SendMailUtilProx().sendMail(mail, user);
                                if ("0".equals(sendResult)) {
                                    //發送成功，更新標誌
                                    mailrecordEntity.setSendStatus("1");
                                    mailrecordService.save(mailrecordEntity);
                                }
                                //聚會推送消息
                                String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                                String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                String empNos = user.getLoginName();
                                SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                                String sendJuhuiResult = null;
//                                if (relationEntity.getWorkflowid().contains("dzqh_gongnengchengshifabushenqingdan") && "0".equals(status)) {
//                                    sendMailUtil.sendJuihuiMobileApi(mail, "Smack", url, empNos);
//                                }
                                if ("0".equals(status)) {//只有通過時發送手機聚會消息，駁回只發送郵件通知用戶
                                    if ("Y".equals(conifgEntity.getWhetherApp())) {
                                        String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                                        if (relationEntity.getWorkflowid().contains("dzqh_gongnengchengshifabushenqingdan")) {
                                            //給聚慧手机端消息發送小網頁鏈接
                                            sendMailUtil.sendJuihuiMobileApi(mail, "Smack", url, empNos, user);
                                        } else {
                                            sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos, user);
                                            if (relationEntity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                                                Object obj = allRelationService.findByDto(relationEntity.getDtoName(), relationEntity.getSerialno());
                                                String applytablename = (String) Reflections.getFieldValue(obj, "applytablename");
                                                mail.setOrdertype(applytablename);
                                            }
                                        }
                                        //只要便易簽裡面有的單子都要發智信（不管網頁/APP審核）
                                        Dict except = dictService.getUniqueDictByType("dict_exceptType" + mail.getOrderstatus());
                                        sendMailUtil.sendZhiXing(mail, except, empNos, user, "signing");
                                        if ("Y".equals(user.getNotification())) {
                                            sendMailUtil.sendAppMessageBar(mail, empNos, user);
                                        }
                                    }
                                    sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos, user);
                                    if ("0".equals(sendJuhuiResult)) {
                                        mailrecordEntity.setSendJuhuiStatus("1");
                                        mailrecordService.save(mailrecordEntity);
                                    }
                                }
                            } else {
                                Object obj = allRelationService.findByDto(relationEntity.getDtoName(), relationEntity.getSerialno());
                                Boolean isApp = ftpInfoService.whetherAppSign(relationEntity.getCreateBy());


                                //2是  1否
                                if (isApp && "0".equals(status)) {
                                    //2是  1否
                                    String sendResult = new SendMailUtilProx().sendMail(mail, user);
                                    if ("0".equals(sendResult)) {
                                        //發送成功，更新標誌
                                        mailrecordEntity.setSendStatus("1");
                                        mailrecordService.save(mailrecordEntity);
                                    }
                                    //聚會推送消息
                                    String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                                    String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                    String empNos = user.getLoginName();
                                    SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                                    String sendJuhuiResult = null;
                                    sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos, user);
                                    if ("0".equals(sendJuhuiResult)) {
                                        mailrecordEntity.setSendJuhuiStatus("1");
                                        mailrecordService.save(mailrecordEntity);
                                    }
                                    String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                                    url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                    sendMailUtil = new SendMailUtilProx();
                                    empNos = user.getLoginName();
                                    sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos, user);
                                    if (relationEntity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                                        String applytablename = (String) Reflections.getFieldValue(obj, "applytablename");
                                        mail.setOrdertype(applytablename);
                                    }
                                    //文檔簽核的單子不管是否isApp都發智信
                                    Dict except = dictService.getUniqueDictByType("dict_exceptType" + mail.getOrderstatus());
                                    sendMailUtil.sendZhiXing(mail, except, empNos, user, "signing");
                                    if ("Y".equals(user.getNotification())) {
                                        sendMailUtil.sendAppMessageBar(mail, empNos, user);
                                    }
                                } else {
                                    String sendResult = new SendMailUtilProx().sendMail(mail, user);
                                    if ("0".equals(sendResult)) {
                                        //發送成功，更新標誌
                                        mailrecordEntity.setSendStatus("1");
                                        mailrecordService.save(mailrecordEntity);
                                    }
                                    //聚會推送消息
                                    String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                                    String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                    String empNos = user.getLoginName();
                                    SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                                    String sendJuhuiResult = null;
                                    sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos, user);
                                    if ("0".equals(sendJuhuiResult)) {
                                        mailrecordEntity.setSendJuhuiStatus("1");
                                        mailrecordService.save(mailrecordEntity);
                                    }
                                    //文檔簽核的單子只要通過，不管是否isApp都發智信
                                    if ("0".equals(status)) {
                                        if (relationEntity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                                            String applytablename = (String) Reflections.getFieldValue(obj, "applytablename");
                                            mail.setOrdertype(applytablename);
                                        }
                                        Dict except = dictService.getUniqueDictByType("dict_exceptType" + mail.getOrderstatus());
                                        sendMailUtil.sendZhiXing(mail, except, empNos, user, "signing");
                                        if ("Y".equals(user.getNotification())) {
                                            sendMailUtil.sendAppMessageBar(mail, empNos, user);
                                        }
                                    }
                                }

                            }
                        }
                    }
                    //員工違紀嘉獎表單駁回，郵件通知人資獎懲作業視窗
                    if ((relationEntity.getWorkflowid().contains("dzqh_yuangongweiji") || "dzqh_yuangongjiajiang_v1".equals(relationEntity.getWorkflowid())) && "1".equals(status)) {
                        String weijiaEmpno = allRelationService.findWeijiaEmpno(relationEntity.getDtoName(), serialno);
                        if (StringUtils.isNotEmpty(weijiaEmpno)) {
                            User weijiauser = userService.getUser(weijiaEmpno);
                            //查詢是否有代理人
                            List<TProxyUserinfoEntity> audit_prox_hb = proxyUserinfoService.findProxUserByEmpnoNew(weijiaEmpno);
                            if (audit_prox_hb != null && audit_prox_hb.size() > 0) {
                                weijiauser = userService.getUser(audit_prox_hb.get(0).getSupplyempno());
                            }
                            //查詢用戶信息
                            TQhUserformhsEntity wjuserformhsEntity = tQhUserformhsService.findByEmpnoIgnoreIdStatus(weijiauser.getLoginName());
                            String validStr2 = UUID.randomUUID().toString().replace("-", "");
                            Mail mail_weijia = new Mail();
                            mail_weijia.setChargerman(UserUtil.getCurrentUser().getName());
                            mail_weijia.setSystemname(dictService.getDictByTypeAndVlaue("sys_property", "sys_name").getLabel());
                            mail_weijia.setDusername(gtasks.getMakername());
                            mail_weijia.setOrdertype(relationEntity.getWfName());
                            mail_weijia.setSerialno(serialno);
                            mail_weijia.setUrl(dictService.get(560).getValue());
                            mail_weijia.setUrlip(dictService.get(561).getValue());
                            mail_weijia.setTypename("人資獎懲窗口");
                            if (weijiauser != null && weijiauser.getEmail() != null) {
                                mail_weijia.setUsermail(weijiauser.getEmail());
                            }
                            //駁回
                            mail_weijia.setOrderstatus("4");
                            mail_weijia.setUsername(weijiauser.getName() != null ? weijiauser.getName() : wjuserformhsEntity.getEmpname());
                            mail_weijia.setFreeloginurl(dictService.get(558).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + weijiauser.getLoginName() + "&loginType=1&utoken=" + validStr2 + "&url=/" + conifgEntity.getModaction() + "/" + serialno);
                            mail_weijia.setFreeloginurlip(dictService.get(559).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + weijiauser.getLoginName() + "&loginType=1&utoken=" + validStr2 + "&url=/" + conifgEntity.getModaction() + "/" + serialno);

                            //保存發送記錄
                            TPubMailrecordEntity mailrecordEntity_weijia = new TPubMailrecordEntity();
                            mailrecordEntity_weijia.setChargerman(mail_weijia.getChargerman());
                            mailrecordEntity_weijia.setDusername(mail_weijia.getDusername());
                            mailrecordEntity_weijia.setOrderstatus(mail_weijia.getOrderstatus());
                            mailrecordEntity_weijia.setOrdertype(mail_weijia.getOrdertype());
                            mailrecordEntity_weijia.setSerialno(mail_weijia.getSerialno());
                            mailrecordEntity_weijia.setUsername(mail_weijia.getUsername());
                            mailrecordEntity_weijia.setSendStatus("0");
                            mailrecordEntity_weijia.setEmpno(user.getLoginName());
                            mailrecordEntity_weijia.setUrl(mail_weijia.getUrl());
                            mailrecordEntity_weijia.setUrlip(mail_weijia.getUrlip());
                            mailrecordEntity_weijia.setFreeloginurl(mail_weijia.getFreeloginurl());
                            mailrecordEntity_weijia.setFreeloginurlip(mail_weijia.getFreeloginurlip());
                            mailrecordEntity_weijia.setValidStr(validStr2);
                            mailrecordService.save(mailrecordEntity_weijia);
                            if (weijiauser != null && weijiauser.getEmail() != null) {
                                mail_weijia.setUsermail(weijiauser.getEmail());
                                mailrecordEntity_weijia.setUsermail(mail_weijia.getUsermail());
                                mailrecordService.save(mailrecordEntity_weijia);
                                //判斷是否及時發送
                                if (!"0".equals(weijiauser.getEmailset())) {
                                    String sendResult = new SendMailUtil().sendMail(mail_weijia);
                                    if ("0".equals(sendResult)) {
                                        //發送成功，更新標誌
                                        mailrecordEntity_weijia.setSendStatus("1");
                                        mailrecordService.save(mailrecordEntity_weijia);
                                    }
                                }

                            }
                        }
                    }

                    //資訊類表單駁回，郵件通知審核過人員
                    if ((relationEntity.getWorkflowid().contains("dzqh_IEMailzhanghaoshenqing") || relationEntity.getWorkflowid().contains("dzqh_teshuwangluoshenqing") || relationEntity.getWorkflowid().contains("dzqh_wwwswzhshenqing")
                            || relationEntity.getWorkflowid().contains("dzqh_sslvpnfuwushenqing") || relationEntity.getWorkflowid().contains("dzqh_SuperNotesfuwushenqing") || relationEntity.getWorkflowid().contains("dzqh_tongxunwangluoshenqing")
                            || relationEntity.getWorkflowid().contains("dzqh_wuxianwangluoshenqing") || relationEntity.getWorkflowid().contains("dzqh_ruantianzhuangshenqingdan") || relationEntity.getWorkflowid().contains("dzqh_gerendiannaoteshuquanxianshenqing")) && "1".equals(status)) {
                        InterConfig configCurrent = new InterConfig();
                        configCurrent.setProcessId(parameter.getProcessId());
                        InterResult taskInfoCurrent = processService.workflowTasksDuration(configCurrent);
                        List<TaskInfo> taskInfoList = taskInfoCurrent.getTaskInfoList();
                        if (taskInfoList != null && taskInfoList.size() > 0) {
                            String chargermantime = DateUtil.getNowTime("yyyy-MM-dd HH:mm:ss");//拋單時間
                            for (int i = 0; i < taskInfoList.size() - 1; i++) {
                                String chargeno = taskInfoList.get(i).getAssignee();
                                if (!"AUTOFIN_DZQH".equals(chargeno)) {
                                    User chargeUser = userService.getUser(chargeno);
                                    //查詢是否有代理人
                                    List<TProxyUserinfoEntity> audit_prox_hb = proxyUserinfoService.findProxUserByEmpnoNew(chargeno);
                                    if (audit_prox_hb != null && audit_prox_hb.size() > 0) {
                                        chargeUser = userService.getUser(audit_prox_hb.get(0).getSupplyempno());
                                    }
                                    //查詢用戶信息
                                    TQhUserformhsEntity wjuserformhsEntity = tQhUserformhsService.findByEmpnoIgnoreIdStatus(chargeUser.getLoginName());
                                    String validStr2 = UUID.randomUUID().toString().replace("-", "");
                                    Mail mail_zixun = new Mail();
                                    mail_zixun.setChargerman(UserUtil.getCurrentUser().getName());
                                    mail_zixun.setSystemname(dictService.getDictByTypeAndVlaue("sys_property", "sys_name").getLabel());
                                    mail_zixun.setDusername(gtasks.getMakername());
                                    mail_zixun.setOrdertype(relationEntity.getWfName());
                                    mail_zixun.setSerialno(serialno);
                                    mail_zixun.setUrl(dictService.get(560).getValue());
                                    mail_zixun.setUrlip(dictService.get(561).getValue());
                                    mail_zixun.setTypename("資訊表單");
                                    mail_zixun.setChargermanremark(attachidsremark);
                                    mail_zixun.setChargermantime(chargermantime);
                                    if (chargeUser != null && chargeUser.getEmail() != null) {
                                        mail_zixun.setUsermail(chargeUser.getEmail());
                                    }
                                    //駁回
                                    mail_zixun.setOrderstatus("4");
                                    mail_zixun.setUsername(chargeUser.getName() != null ? chargeUser.getName() : wjuserformhsEntity.getEmpname());
                                    mail_zixun.setFreeloginurl(dictService.get(558).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + chargeUser.getLoginName() + "&loginType=1&utoken=" + validStr2 + "&url=/" + conifgEntity.getModaction() + "/" + serialno);
                                    mail_zixun.setFreeloginurlip(dictService.get(559).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + chargeUser.getLoginName() + "&loginType=1&utoken=" + validStr2 + "&url=/" + conifgEntity.getModaction() + "/" + serialno);

                                    //保存發送記錄
                                    TPubMailrecordEntity mailrecordEntity_zixun = new TPubMailrecordEntity();
                                    mailrecordEntity_zixun.setChargerman(mail_zixun.getChargerman());
                                    mailrecordEntity_zixun.setDusername(mail_zixun.getDusername());
                                    mailrecordEntity_zixun.setOrderstatus(mail_zixun.getOrderstatus());
                                    mailrecordEntity_zixun.setOrdertype(mail_zixun.getOrdertype());
                                    mailrecordEntity_zixun.setSerialno(mail_zixun.getSerialno());
                                    mailrecordEntity_zixun.setUsername(mail_zixun.getUsername());
                                    mailrecordEntity_zixun.setSendStatus("0");
                                    mailrecordEntity_zixun.setEmpno(user.getLoginName());
                                    mailrecordEntity_zixun.setUrl(mail_zixun.getUrl());
                                    mailrecordEntity_zixun.setUrlip(mail_zixun.getUrlip());
                                    mailrecordEntity_zixun.setFreeloginurl(mail_zixun.getFreeloginurl());
                                    mailrecordEntity_zixun.setFreeloginurlip(mail_zixun.getFreeloginurlip());
                                    mailrecordEntity_zixun.setValidStr(validStr2);
                                    mailrecordService.save(mailrecordEntity_zixun);
                                    if (chargeUser != null && chargeUser.getEmail() != null) {
                                        mail_zixun.setUsermail(chargeUser.getEmail());
                                        mailrecordEntity_zixun.setUsermail(mail_zixun.getUsermail());
                                        mailrecordService.save(mailrecordEntity_zixun);
                                        //判斷是否及時發送
                                        if (!"0".equals(chargeUser.getEmailset())) {
                                            String sendResult = new SendMailUtil().sendMail(mail_zixun);
                                            if ("0".equals(sendResult)) {
                                                //發送成功，更新標誌
                                                mailrecordEntity_zixun.setSendStatus("1");
                                                mailrecordService.save(mailrecordEntity_zixun);
                                            }
                                            //聚會推送消息
                                            String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                                            String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                            String empNos = chargeUser.getLoginName();
                                            String sendJuhuiResult = new SendMailUtil().sendJuihui(mail_zixun, type, url, empNos);
                                            if ("0".equals(sendJuhuiResult)) {
                                                mailrecordEntity_zixun.setSendJuhuiStatus("1");
                                                mailrecordService.save(mailrecordEntity_zixun);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            resultMap.put("result", Constant.RESULT.CODE_NO.getValue());
            return resultMap;
        }
        resultMap.put("workflowid", workflowid);
        resultMap.put("nodeName", nodeName);
        resultMap.put("signNo", signNo);
        resultMap.put("result", Constant.RESULT.CODE_YES.getValue());
        return resultMap;
    }

    @Transactional(readOnly = false)
    public String processStart(String serialno) {
        TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialno);
//        List<Map<String, Object>> mapList= allRelationService.findToMapBySql(" from "+relationEntity.getDtoName()+" where serialno=:serialno",serialno);
        List<Map<String, Object>> mapList = allRelationService.findToMapBySql(serialno);
        return "";
    }

    /**
     * 方法描述: 啟動流程
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/8  上午 10:32
     * @Return
     **/
    @Transactional(readOnly = false)
    public String processStart(WorkFlowEntity obj, Object entity) throws Exception {
        try {
            InterConfig config = new InterConfig();
            //設置工單發啟人
//            config.setApplyUserId("admin");
            config.setApplyUserId(Reflections.getFieldValue(entity, "makerno") == null ? UserUtil.getCurrentUser().getLoginName() : Reflections.getFieldValue(entity, "makerno").toString());
            //設置流程id
            config.setWorkFlowId(obj.getWorkflowId());
            //初始化工單各節點處理人
            config.setTaskUsers(obj.getTaskUsers());
            //設置會簽簽核人信息
            config.setHuiqian(obj.getHuiqian());
            //設置流程整體參數
            String varStr = this.getWfVariables(obj.getWorkflowId());
//            String userGrade = conifgService.callProcWithResult("{CALL p_qh_getUserGrade('?0')}", obj.getEmpNo()).get(0).toString();
            varStr = varStr.replace("$USERGRADE$", "1");
            logger.info(varStr);
            config.setVariables(varStr);
            InterResult interResult = processService.processStart(config);
            //創建會簽節點
            this.createHuiqianTaskInfor(obj, interResult.getProcessId(), entity);

            AutoCompleteTask(interResult.getProcessId(),obj.getWorkflowId());
            //發送郵件

            config = new InterConfig();
            config.setProcessId(interResult.getProcessId());
            config.setWorkFlowId(obj.getWorkflowId());
            InterResult taskInfo = processService.currentTaskInfo(config);
            if ("SUCCESS".equals(taskInfo.getStatus())) {
                TaskInfo info = taskInfo.getTaskInfoList().get(0);
                /**新舊工號兼容優化**/
                EntityNewAndOld newAndOld = OldJobNoToNewNo.getNewNo(info.getAssignee());
                /**新舊工號兼容優化**/
                User user = userService.getUser(newAndOld.getNewEmpno());
                //查詢是否有代理人
                List<TProxyUserinfoEntity> audit_prox = proxyUserinfoService.findProxUserByEmpnoNew(newAndOld.getNewEmpno());
                if (audit_prox != null && audit_prox.size() > 0) {
                    user = userService.getUser(audit_prox.get(0).getSupplyempno());
                }
                WfConifgEntity conifgEntity = conifgService.findUnique(obj.getWorkflowId());
                String validStr = UUID.randomUUID().toString().replace("-", "");
                Gtasks gtasks = allRelationService.queryMyGtasks(obj.getSerialNo(), entity.getClass().getName());
                try {
                    if (user != null && !"".equals(user.getLoginName())) { //无用户信息则不发送邮件
                        Mail mail = new Mail();
                        mail.setChargerman(UserUtil.getCurrentUser().getName());
                        mail.setSystemname(dictService.getDictByTypeAndVlaue("sys_property", "sys_name").getLabel());
                        mail.setDusername(gtasks.getMakername());
                        mail.setOrdertype(conifgService.findUnique(obj.getWorkflowId()).getWorkflowname());
                        mail.setSerialno(obj.getSerialNo());
                        mail.setOrderstatus("2");
                        mail.setFreeloginurl(dictService.get(558).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getAction() + "/" + obj.getSerialNo());
                        mail.setFreeloginurlip(dictService.get(559).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getAction() + "/" + obj.getSerialNo());
                        mail.setUrl(dictService.get(560).getValue());
                        mail.setUrlip(dictService.get(561).getValue());
                        //保存發送記錄
                        TPubMailrecordEntity mailrecordEntity = new TPubMailrecordEntity();
                        mailrecordEntity.setChargerman(mail.getChargerman());
                        mailrecordEntity.setDusername(mail.getDusername());
                        mailrecordEntity.setOrderstatus(mail.getOrderstatus());
                        mailrecordEntity.setOrdertype(mail.getOrdertype());
                        mailrecordEntity.setSerialno(mail.getSerialno());
                        mailrecordEntity.setSendStatus("0");
                        mailrecordEntity.setEmpno(user.getLoginName());
                        mailrecordEntity.setUrl(mail.getUrl());
                        mailrecordEntity.setUrlip(mail.getUrlip());
                        mailrecordEntity.setFreeloginurl(mail.getFreeloginurl());
                        mailrecordEntity.setFreeloginurlip(mail.getFreeloginurlip());
                        mailrecordEntity.setValidStr(validStr);
                        mailrecordService.save(mailrecordEntity);
                        if (user != null && user.getEmail() != null) {
                            mail.setUsermail(user.getEmail());
                            mail.setUsername(user.getName());
                            mailrecordEntity.setUsername(mail.getUsername());
                            mailrecordEntity.setUsermail(mail.getUsermail());
                            mailrecordService.save(mailrecordEntity);
                            if ("1".equals(conifgEntity.getAppType())) {
                                Dict dict2 = new Dict();
                                dict2.setType("entfrmIpebgIP_app");
                                dict2.setCodeUniq("entfrmIpebgIP_app01");
                                String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
                                mail.setAppAuditUrl(StrUtil.concat(true, fronUrl, conifgEntity.getAppAuditUrl(), "?serialno=", mail.getSerialno()));
                            } else {
                                mail.setAppAuditUrl(StrUtil.concat(true, conifgEntity.getAppAuditUrl(), "?serialno=", mail.getSerialno()));
                            }
                            if (!obj.getWorkflowId().contains("dzqh_wendangqianheshenqingdan")) {
                                String sendResult = new SendMailUtilProx().sendMail(mail, user);
                                if ("0".equals(sendResult)) {
                                    //發送成功，更新標誌
                                    mailrecordEntity.setSendStatus("1");
                                    mailrecordService.save(mailrecordEntity);
                                }
                                //聚會推送消息
                                String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                                String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                String empNos = user.getLoginName();
                                SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                                String sendJuhuiResult = null;
//                                if (obj.getWorkflowId().contains("dzqh_gongnengchengshifabushenqingdan")) {
//                                    sendMailUtil.sendJuihuiMobileApi(mail, "Smack", url, empNos);
//                                }

                                sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos, user);
                                if ("0".equals(sendJuhuiResult)) {
                                    mailrecordEntity.setSendJuhuiStatus("1");
                                    mailrecordService.save(mailrecordEntity);
                                }
                                if ("Y".equals(conifgEntity.getWhetherApp())) {
                                    String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                                    if (obj.getWorkflowId().contains("dzqh_gongnengchengshifabushenqingdan")) {
                                        sendMailUtil.sendJuihuiMobileApi(mail, "Smack", url, empNos, user);
                                    } else {
                                        sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos, user);
                                    }
                                    //啟動流程時便易簽裡面有的單子發智信
                                    Dict except = dictService.getUniqueDictByType("dict_exceptType" + mail.getOrderstatus());
                                    sendMailUtil.sendZhiXing(mail, except, empNos, user, "signing");
                                    if ("Y".equals(user.getNotification())) {
                                        sendMailUtil.sendAppMessageBar(mail, empNos, user);
                                    }
                                }
                                //發起時違紀表單發起要發e通關系統
                                if (obj.getWorkflowId().contains("dzqh_yuangongweiji")) {
                                    wfIlegalProcessesService.sendToETongGuan(obj.getSerialNo());
                                }
                            } else {
                                Boolean isApp = ftpInfoService.whetherAppSign(UserUtil.getCurrentUser().getLoginName());


                                //2是  1否
                                if (isApp) {
                                    //2是  1否
                                    String sendResult = new SendMailUtilProx().sendMail(mail, user);
                                    if ("0".equals(sendResult)) {
                                        //發送成功，更新標誌
                                        mailrecordEntity.setSendStatus("1");
                                        mailrecordService.save(mailrecordEntity);
                                    }
                                    //聚會推送消息
                                    String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                                    String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                    String empNos = user.getLoginName();
                                    SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                                    String sendJuhuiResult = null;
                                    sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos, user);
                                    if ("0".equals(sendJuhuiResult)) {
                                        mailrecordEntity.setSendJuhuiStatus("1");
                                        mailrecordService.save(mailrecordEntity);
                                    }
                                    String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                                    url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                    sendMailUtil = new SendMailUtilProx();
                                    empNos = user.getLoginName();
                                    sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos, user);
                                    //不管isApp,文檔簽核的都發智信
                                    if (obj.getWorkflowId().contains("dzqh_wendangqianheshenqingdan")) {
                                        Object obj1 = allRelationService.findByDto(entity.getClass().getName(), obj.getSerialNo());
                                        String applytablename = (String) Reflections.getFieldValue(obj1, "applytablename");
                                        mail.setOrdertype(applytablename);
                                    }
                                    Dict except = dictService.getUniqueDictByType("dict_exceptType" + mail.getOrderstatus());
                                    sendMailUtil.sendZhiXing(mail, except, empNos, user, "signing");
                                    if ("Y".equals(user.getNotification())) {
                                        sendMailUtil.sendAppMessageBar(mail, empNos, user);
                                    }
                                } else {
                                    String sendResult = new SendMailUtilProx().sendMail(mail, user);
                                    if ("0".equals(sendResult)) {
                                        //發送成功，更新標誌
                                        mailrecordEntity.setSendStatus("1");
                                        mailrecordService.save(mailrecordEntity);
                                    }
                                    //聚會推送消息
                                    String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                                    String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                    String empNos = user.getLoginName();
                                    SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                                    String sendJuhuiResult = null;
                                    sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos, user);
                                    if ("0".equals(sendJuhuiResult)) {
                                        mailrecordEntity.setSendJuhuiStatus("1");
                                        mailrecordService.save(mailrecordEntity);
                                    }
                                    //不管isApp,文檔簽核的都發智信
                                    if (obj.getWorkflowId().contains("dzqh_wendangqianheshenqingdan")) {
                                        Object obj1 = allRelationService.findByDto(entity.getClass().getName(), obj.getSerialNo());
                                        String applytablename = (String) Reflections.getFieldValue(obj1, "applytablename");
                                        mail.setOrdertype(applytablename);
                                    }
                                    Dict except = dictService.getUniqueDictByType("dict_exceptType" + mail.getOrderstatus());
                                    sendMailUtil.sendZhiXing(mail, except, empNos, user, "signing");
                                    if ("Y".equals(user.getNotification())) {
                                        sendMailUtil.sendAppMessageBar(mail, empNos, user);
                                    }
                                }
                            }

                        }
                    }
//                    if (user != null && "dzqh_wendangqianheshenqingdan".equals(obj.getWorkflowId())) {
//                        mail.setUsername(user.getName());
//                        mailrecordEntity.setUsername(mail.getUsername());
//                        mailrecordService.save(mailrecordEntity);
//                        String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
//                        String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
//                        String empNos = user.getLoginName();
//                        String sendJuhuiResult = new SendMailUtil().sendJuihuiApi(mail, typeApp, url, empNos);
//                        if ("0".equals(sendJuhuiResult)) {
//                            mailrecordEntity.setSendJuhuiStatus("1");
//                            mailrecordService.save(mailrecordEntity);
//                        }
//                    }
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }

            }
//            createHuiqianTaskInfor(obj, interResult.getProcessId());
            return interResult.getProcessId();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 方法描述: 創建會簽節點
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/8  上午 10:32
     * @Return
     **/
    @Transactional(readOnly = false)
    public void createHuiqianTaskInfor(WorkFlowEntity obj, String processId, Object o) {
        try {
            List<WfNodeinfoEntity> entityList = wfNodeinfoService.findForHuiqian(obj.getWorkflowId());
            if (entityList != null && entityList.size() > 0) {
                InterConfig config = null;
                for (WfNodeinfoEntity n : entityList) {
                    if ("1".equals(n.getSigntype())) {
                        config = new InterConfig();
                        config.setProcessId(processId);
                        config.setTaskName(n.getNodename());
                        String hQRule = getHuiQianRule(obj.getWorkflowId(), n.getNodeid());
                        Object cstr = Reflections.getFieldValue(o, n.getColname());
                        String hChargeStr = (cstr == null ? "" : cstr.toString());
                        if (StringUtils.isNotEmpty(hChargeStr)) {
                            String[] cs = hChargeStr.split(",");
                            hQRule = hQRule.replace("$PERSONNUM$", cs.length + "");
                        }
                        logger.info(hQRule);
                        config.setOtherParam(hQRule);
                        config.setWorkFlowId(n.getWorkflowid());
                        processService.createHuiqianTaskInfor(config);
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 方法描述: 獲取流程級配置參數
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/8  上午 11:51
     * @Return
     **/

    public String getWfVariables(String workFlowId) {
        StringBuffer sb = new StringBuffer();
        try {
            WfConifgEntity wf = conifgService.findUnique(workFlowId);
            Assert.notNull(wf);
            List<WfConfigparamEntity> ps = wfConfigparamService.findByFilters(workFlowId);
            Assert.notEmpty(ps);
            //組織流程級別的參數
            for (WfConfigparamEntity p : ps) {
                sb.append(p.getParamename() + ",")
                        .append(p.getParamvalue() == null ? "" : p.getParamvalue() + ",")
                        .append(p.getParamtype() == null ? "" : p.getParamtype() + ";");
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return sb.toString();
    }

    /**
     * 方法描述:
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/10  上午 10:42
     * @Return
     **/

    public String getHuiQianRule(String workflowId, String nodeId) {
        StringBuffer sb = new StringBuffer();
        try {
            WfNoderuleEntity ne = wfNoderuleService.findUniqByNodeId(nodeId, workflowId);
            Assert.notNull(ne);
            sb.append(ne.getTotalpeople()).append("&")
                    .append(ne.getPassrate()).append("&")
                    .append(ne.getVoterule()).append("&")
                    .append(ne.getTaskstatusvariable()).append("&")
                    .append(ne.getTaskcompletevariable()).append("&");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return sb.toString();
    }

    /**
     * 方法描述: 完成任務
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/9  上午 10:21
     * @Return
     **/
//    @Transactional(propagation = Propagation.REQUIRES_NEW)
//    public String completeTask(CompleteTaskParameter parameter, String status) {
//        try {
//            InterConfig config = new InterConfig();
//            config.setProcessId(parameter.getProcessId());
//            //獲取流程當前節點處理人
//            InterResult taskInfo = processService.currentTaskInfo(config);
//            config = new InterConfig();
////        config.setTaskId(parameter.getTaskId());
////        InterResult taskInfoTmp=((ProcessService) SpringContextUtil.getBean("processService")).queryTaskById(config);
//            config = new InterConfig();
//            TaskInfo info = taskInfo.getTaskInfoList().get(0);
//            config.setTaskId(info.getTaskId());
//            TQhAllRelationEntity entityR = allRelationService.queryByEntity(parameter.getSerialno());
//            config.setVariables(this.getNodeTaskVarivles(parameter.getWorkFlowId(), info.getTaskName(), status, entityR.getVersion()));
//            InterResult interResult = processService.comleteTask(config);
//            if ("SUCCESS".equals(interResult.getStatus())) {
//                //添加簽核記錄
//                TQhChargelogEntity entity = new TQhChargelogEntity();
//                User user = userService.getUser(info.getAssignee());
//                entity.setChargename(user.getName());
////                entity.setChargename("S6112942");
//                WfNodeinfoEntity entity_nodeinfo = wfNodeinfoService.findByTaskName(info.getTaskName(), entityR.getWorkflowid(), entityR.getVersion());
//                entity.setChargenode(entity_nodeinfo.getNodealain());
//                entity.setSerialno(parameter.getSerialno());
//                entity.setChargeno(info.getAssignee());
//                entity.setWorkflowid(parameter.getWorkFlowId());
//                entity.setOperateip(parameter.getIp());
//                if (parameter.getAuto() || UserUtil.getCurrentUser().getLoginName().equals(OldJobNoToNewNo.getNewNo(info.getAssignee()).getNewEmpno())) {
//                    entity.setDecrib(parameter.getAttachidsremark());
//                } else {
//                    entity.setDecrib("(" + UserUtil.getCurrentUser().getLoginName() + "/" + UserUtil.getCurrentUser().getName() + "代簽)" + parameter.getAttachidsremark());
//                }
//                /*S7198867 針對員工復職申請單-衛生部主管審核-簽核意見及主表中體檢結果單獨處理*/
//                if ("0".equals(status)) {
//                    if ("WffuzhiprocessEntity".equals(entityR.getDtoName())) {
//                        if ("衛生部主管審核窗口".equals(info.getTaskName())) {
//                            entity.setIspass("體檢合格");
//                            allRelationService.updateWffuzhiprocessEntity(entityR.getSerialno(), "體檢合格", entityR.getDtoName());
//                        } else if ("宿舍分配窗口".equals(info.getTaskName())) {
//                            Object obj = allRelationService.findByDto(entityR.getDtoName(), entityR.getSerialno());
//                            String element = (String) Reflections.getFieldValue(obj, "element");
//                            String apartment = (String) Reflections.getFieldValue(obj, "apartment");
//                            if ("".equals(element) || "".equals(apartment) || null == element || null == apartment) {
//                                entity.setIspass("辦理異常");
//                            } else {
//                                entity.setIspass("已辦理");
//                            }
//                        } else {
//                            entity.setIspass("通過");
//                        }
//
//                    } else if (entityR.getWorkflowid().contains("dzqh_yuangongweiji") || entityR.getWorkflowid().contains("dzqh_yuangongjiajiang")) {
//                        //處理違紀和嘉獎的生效日期
//                        try {
//                            Object obj = allRelationService.findByDto(entityR.getDtoName(), entityR.getSerialno());
//                            if (IlegAndCommdeValidTime.getAuditor(obj, info.getAssignee(), entity_nodeinfo.getNodealain())) {
//                                allRelationService.updateIlegAndCommdValidTime(entityR.getDtoName(), entityR.getSerialno());
//                            }
//                        } catch (Exception e) {
//
//                        }
//                        entity.setIspass("通過");
//                    } else {
//                        entity.setIspass("通過");
//                    }
//                    //entity.setIspass("通過");
//                } else if ("1".equals(status)) {
//                    if ("WffuzhiprocessEntity".equals(entityR.getDtoName()) && "衛生部主管審核窗口".equals(info.getTaskName())) {
//                        entity.setIspass("體檢不合格");
//                        allRelationService.updateWffuzhiprocessEntity(entityR.getSerialno(), "體檢不合格", entityR.getDtoName());
//                    } else {
//                        entity.setIspass("駁回");
//                    }
//                } else if ("2".equals(status)) {
//                    entity.setIspass("重新提交");
//                } else if ("3".equals(status)) {
//                    entity.setIspass("取消申請");
//                }
//                if (parameter.getAuto() != null && parameter.getAuto() == true) {
//                    tQhChargelogService.saveEntity(entity);
//                } else {
//                    tQhChargelogService.save(entity);
//                }
//                //如果是駁回更新中間表狀態
//                if ("1".equals(status)) {
//                    entityR.setWorkstatus(Constant.RESULT.CODE_REJECT.getValue());
//                    if (parameter.getAuto() != null && parameter.getAuto() == true) {
//                        allRelationService.updateReceipt(entityR);
//                    } else {
//                        allRelationService.update(entityR);
//                    }
//                    allRelationService.updateFormStatic(parameter.getSerialno(), Constant.RESULT.CODE_REJECT.getValue(), entityR.getDtoName());
//                }
//                String result = AutoCompleteTask(parameter.getProcessId());
//                //如果是完成，返回完成標識
//                if (result.equals(Constant.RESULT.CODE_COMPLETE.getValue())) {
//                    allRelationService.updateFormStaticComplete(parameter.getSerialno(), Constant.RESULT.CODE_COMPLETE.getValue(), entityR.getDtoName());
//                    return result;
//                }
//                return Constant.RESULT.CODE_YES.getValue();
//            }
//        } catch (Exception e) {
//            logger.error(e.getMessage(), e);
//        }
//        return Constant.RESULT.CODE_NO.getValue();
//    }
//
//    /**
//     * 方法描述: 會簽任務完成
//     *
//     * @Author: S6114648
//     * @CreateDate: 2018/10/15  下午 03:25
//     * @Return
//     **/
//    @Transactional(propagation = Propagation.REQUIRES_NEW)
//    public String completeHqTask(CompleteTaskParameter parameter, String status) {
//        try {
//            InterConfig config = new InterConfig();
//            config.setProcessId(parameter.getProcessId());
//            //獲取流程當前節點處理人
//            InterResult taskInfo = processService.currentTaskInfo(config);
//            TaskInfo info = taskInfo.getTaskInfoList().get(0);
//            config = new InterConfig();
//            config.setTaskId(info.getTaskId());
//            config.setTaskName(info.getTaskName());
//            TQhAllRelationEntity entityR = allRelationService.queryByEntity(parameter.getSerialno());
//            config.setVoteResult(this.getNodeHqTaskVarivles(parameter.getWorkFlowId(), info.getTaskName(), status, entityR.getVersion()));
//            config.setAssignee(info.getAssignee());
//            config.setProcessId(parameter.getProcessId());
//            InterResult interResult = processService.completeHuiqianTask(config);
//            if ("SUCCESS".equals(interResult.getStatus())) {
//                //添加簽核記錄
//                TQhChargelogEntity entity = new TQhChargelogEntity();
//                User user = userService.getUser(info.getAssignee());
//                entity.setChargename(user.getName());
////                entity.setChargename("S6112942");
//                WfNodeinfoEntity entity_nodeinfo = wfNodeinfoService.findByTaskName(info.getTaskName(), entityR.getWorkflowid(), entityR.getVersion());
//                entity.setChargenode(entity_nodeinfo.getNodealain());
//                entity.setSerialno(parameter.getSerialno());
//                entity.setChargeno(info.getAssignee());
//                entity.setWorkflowid(parameter.getWorkFlowId());
//                entity.setOperateip(parameter.getIp());
//                if (parameter.getAuto() || UserUtil.getCurrentUser().getLoginName().equals(OldJobNoToNewNo.getNewNo(info.getAssignee()).getNewEmpno())) {
//                    entity.setDecrib(parameter.getAttachidsremark());
//                } else {
//                    entity.setDecrib("(" + UserUtil.getCurrentUser().getLoginName() + "/" + UserUtil.getCurrentUser().getName() + "代簽)" + parameter.getAttachidsremark());
//                }
//                if ("0".equals(status)) {
//                    if (entityR.getWorkflowid().contains("dzqh_yuangongweiji") || entityR.getWorkflowid().contains("dzqh_yuangongjiajiang")) {
//                        //處理違紀和嘉獎的生效日期
//                        try {
//                            Object obj = allRelationService.findByDto(entityR.getDtoName(), entityR.getSerialno());
//                            if (IlegAndCommdeValidTime.getAuditor(obj, info.getAssignee(), entity_nodeinfo.getNodealain())) {
//                                allRelationService.updateIlegAndCommdValidTime(entityR.getDtoName(), entityR.getSerialno());
//                            }
//                        } catch (Exception e) {
//
//                        }
//                    }
//                    entity.setIspass("通過");
//                } else if ("1".equals(status)) {
//                    entity.setIspass("駁回");
//                } else if ("2".equals(status)) {
//                    entity.setIspass("重新提交");
//                } else if ("3".equals(status)) {
//                    entity.setIspass("取消申請");
//                }
//                if (parameter.getAuto() != null && parameter.getAuto() == true) {
//                    tQhChargelogService.saveEntity(entity);
//                } else {
//                    tQhChargelogService.save(entity);
//                }
//                String result = AutoCompleteTask(parameter.getProcessId());
//                //Internet E-Mail賬號申請單(t_qh_wfinternetemailprocess)，Super Notes帳號服務申請(T_QH_WFSUPERNOTESPROCESS)，無線區域網絡使用申請單（T_QH_WFVLANPROCESS） 廠區為觀瀾和惠州，咨詢運維部級和咨詢運維處級主管同為劉經理，跳過部級
//                try {
//                    String workflowId = entityR.getWorkflowid();
//                    if ((workflowId.contains("dzqh_IEMailzhanghaoshenqing") || workflowId.contains("dzqh_SuperNotesfuwushenqing") || workflowId.contains("dzqh_wuxianwangluoshenqing")) && "0".equals(status)) {
//                        Object obj = allRelationService.findByDto(entityR.getDtoName(), entityR.getSerialno());
//                        String ylno3 = (String) Reflections.getFieldValue(obj, "ylno3");
//                        String ylno6 = (String) Reflections.getFieldValue(obj, "ylno6");
//                        String applyFactroyId = null;
//                        if (workflowId.contains("dzqh_wuxianwangluoshenqing")) {
//                            //dealfactoryid
//                            applyFactroyId = (String) Reflections.getFieldValue(obj, "dealchoosefactoryid");
//                        } else {
//                            applyFactroyId = (String) Reflections.getFieldValue(obj, "applyfactoryid");
//                        }
//                        if (ylno6.equals(ylno3) && ylno3.equals("011266")) {
//                            if ("HZGL".equals(applyFactroyId) || "IPEHZ".equals(applyFactroyId)) {
//                                InterConfig config_auto = new InterConfig();
//                                config_auto.setProcessId(entityR.getProcessid());
//                                InterResult interResult1 = processService.currentTaskInfo(config_auto);
//                                if (interResult1.getTaskInfoList() != null && interResult1.getTaskInfoList().size() > 0) {
//                                    if ("資訊運維部級主管".equals(interResult1.getTaskInfoList().get(0).getTaskName())) {//兼容未改的流程
//                                        config = new InterConfig();
//                                        config.setTaskId(interResult1.getTaskInfoList().get(0).getTaskId());
//                                        config.setVoteResult(this.getNodeHqTaskVarivles(parameter.getWorkFlowId(), interResult1.getTaskInfoList().get(0).getTaskName(), status, entityR.getVersion()));
//                                        config.setTaskName("資訊運維部級主管");
//                                        config.setProcessId(entityR.getProcessid());
//                                        config.setAssignee("011266");
//                                        InterResult interResult_auto = processService.completeHuiqianTask(config);
//                                    }
//                                    if ("資訊運維廠區主管".equals(interResult1.getTaskInfoList().get(0).getTaskName())) {
//                                        config = new InterConfig();
//                                        config.setTaskId(interResult1.getTaskInfoList().get(0).getTaskId());
//                                        config.setVoteResult(this.getNodeHqTaskVarivles(parameter.getWorkFlowId(), interResult1.getTaskInfoList().get(0).getTaskName(), status, entityR.getVersion()));
//                                        config.setTaskName("資訊運維廠區主管");
//                                        config.setProcessId(entityR.getProcessid());
//                                        config.setAssignee("011266");
//                                        InterResult interResult_auto = processService.completeHuiqianTask(config);
//                                    }
//                                }
//                            }
//                        }
//                    }
//                    //tiptop申請單回寫數據
//                    if (workflowId.contains("dzqh_tiptopzhanghaoshenqingdan") && "0".equals(status) && "系統開發部級主管".equals(info.getTaskName())) {
//                        TiptopProcessEntity entity1 = tiptopProcessService.findBySerialno(parameter.getSerialno());
//                        TipTopDataDto dto = null;
//                        for (TiptopItemsEntity itemsEntity : entity1.getItemsEntity()) {
//                            dto = new TipTopDataDto();
//                            dto.setApply_type(entity1.getApplyContent());
//                            dto.setApply_user(itemsEntity.getAccountNumber());
//                            dto.setApply_name(itemsEntity.getAccountName());
//                            dto.setApply_depart(itemsEntity.getAccountDept());
//                            dto.setApply_sign(parameter.getSerialno());
//                            dto.setUser_mail(entity1.getApplyemail());
//                            if (StringUtils.isNotEmpty(entity1.getComputerAddress())) {
//                                dto.setApply_ip(dictService.getDictByTypeAndVlaue("tiptop_computer_address", entity1.getComputerAddress()).getLabel());
//                            } else {
//                                dto.setApply_ip("");
//                            }
//                            if (StringUtils.isNotEmpty(itemsEntity.getAccountType())) {
//                                dto.setApply_permission(dictService.getDictByTypeAndVlaue("tiptop_account_type", itemsEntity.getAccountType()).getLabel());
//                                dto.setPermission_desc(itemsEntity.getAccountType());
//                            } else {
//                                dto.setApply_permission("");
//                                dto.setPermission_desc("");
//                            }
//                            if (StringUtils.isNotEmpty(itemsEntity.getFactroyCode())) {
//                                dto.setApply_plant(dictService.getDictByTypeAndVlaue("tiptop_factroy_code", itemsEntity.getFactroyCode()).getLabel());
//                                dto.setPlant_desc(itemsEntity.getFactroyCode());
//                            } else {
//                                dto.setApply_plant("");
//                                dto.setPlant_desc("");
//                            }
//                            String insertData = JSONUtil.toJsonStr(dto);
//                            Dict dict = new Dict();
//                            dict.setCodeUniq("tiptop_check_address_04");
//                            dict.setType("tiptop_check_address");
//                            String urlString = dictService.getUniqueDictByTypeAndCode(dict).getValue();
//                            String t1 = HttpUtil.post(urlString, JSONUtil.createObj().put("serverID", "USERMANAGER").put("insertData", insertData), 10000);
//                            logger.error(entity1.getSerialno() + "tiptop_check_address_04-----" + t1);
//                            System.out.println(entity1.getSerialno() + "----拋轉狀態" + t1);
//                        }
//                    }
//                    /*1.黨“資訊運維中心級主管”與“系統開發處級主管”節點為同一主管簽核時， 只需簽核一次，可顯示2條簽核記錄；
//                    2.黨審核到“系統開發處級主管”時，判斷是否有代理，如有並且代理人為“資訊運維中心級主管 ”節點對應人員，該節點自動審核；
//                    3.黨審核到“資訊運維中心級主管 ”時，判斷是否有代理，如有並且代理人為“系統開發處級主管 ”節點對應人員，該節點自動審核；
//                    */
//                    if (workflowId.contains("dzqh_gongnengchengshifabushenqingdan")) {
//                        //嘗試更新自動審核后沒有ip的情況
//                        if ("系統開發處級主管".equals(info.getTaskName())) {
//                            tQhChargelogService.updateAutoAuditAndNoIp(parameter.getSerialno(), parameter.getIp(), workflowId.contains("_v3") ? "資訊運維中心級主管" : "資訊運維處級主管");//v3版本改為資訊運維中心級主管，兼容以前版本
//                        }
//                        if ("0".equals(status)) {
//                            InterConfig config_auto = new InterConfig();
//                            config_auto.setProcessId(entityR.getProcessid());
//                            Boolean flag = false;
//                            InterResult interResult1 = processService.currentTaskInfo(config_auto);
//                            Object obj = allRelationService.findByDto(entityR.getDtoName(), entityR.getSerialno());
//                            String zxywcjzgno = (String) Reflections.getFieldValue(obj, "zxywcjzgno");
//                            String xtkfcjzgno = (String) Reflections.getFieldValue(obj, "xtkfcjzgno");
//                            if (interResult1.getTaskInfoList() != null && interResult1.getTaskInfoList().size() > 0) {
//                                TaskInfo taskInfo1 = interResult1.getTaskInfoList().get(0);
//                                if ("系統開發處級主管".equals(taskInfo1.getTaskName())) {
//                                    List<TProxyUserinfoEntity> xtkfcjzgno_prox = proxyUserinfoService.findProxUserByEmpnoNew(xtkfcjzgno);
//                                    if (xtkfcjzgno_prox != null && xtkfcjzgno_prox.size() > 0) {
//                                        for (TProxyUserinfoEntity xtkfcjzgnoProx : xtkfcjzgno_prox) {
//                                            if (xtkfcjzgnoProx.getSupplyempno().equals(zxywcjzgno)) {
//                                                flag = true;
//                                                break;
//                                            }
//                                        }
//                                    } else if (zxywcjzgno.equals(xtkfcjzgno)) {
//                                        flag = true;
//                                    }
//                                    if (flag) {
//                                        config = new InterConfig();
//                                        config.setTaskId(taskInfo1.getTaskId());
//                                        config.setVoteResult(this.getNodeHqTaskVarivles(parameter.getWorkFlowId(), taskInfo1.getTaskName(), status, entityR.getVersion()));
//                                        config.setTaskName("系統開發處級主管");
//                                        config.setProcessId(entityR.getProcessid());
//                                        config.setAssignee(xtkfcjzgno);
//                                        InterResult interResult_auto = processService.completeHuiqianTask(config);
//                                        if ("SUCCESS".equals(interResult_auto.getStatus())) {
//                                            //添加簽核記錄
//                                            TQhChargelogEntity entity1 = new TQhChargelogEntity();
//                                            User user1 = userService.getUser(taskInfo1.getAssignee());
//                                            entity1.setChargename(user1.getName());
//                                            WfNodeinfoEntity entity_nodeinfo1 = wfNodeinfoService.findByTaskName(taskInfo1.getTaskName(), entityR.getWorkflowid(), entityR.getVersion());
//                                            entity1.setChargenode(entity_nodeinfo1.getNodealain());
//                                            entity1.setSerialno(parameter.getSerialno());
//                                            entity1.setChargeno(taskInfo1.getAssignee());
//                                            entity1.setWorkflowid(parameter.getWorkFlowId());
//                                            TQhChargelogEntity entity_log = tQhChargelogService.queryByNodenameAndSerialNo(parameter.getSerialno(), workflowId.contains("_v3") ? "資訊運維中心級主管" : "資訊運維處級主管");//v3版本改為資訊運維中心級主管，兼容以前版本
//                                            entity1.setOperateip(entity_log.getOperateip());
//                                            entity1.setIspass("通過");
//                                            User user2 = userService.getUser(zxywcjzgno);
//                                            if (zxywcjzgno.equals(xtkfcjzgno)) {
//                                                entity1.setDecrib(entity_log.getDecrib());
//                                            } else {
//                                                entity1.setDecrib("(" + user2.getLoginName() + "/" + user2.getName() + "代簽)");
//                                            }
//                                            tQhChargelogService.saveEntity(entity1);
//                                        }
//                                    }
//                                } else if ("資訊運維處級主管".equals(interResult1.getTaskInfoList().get(0).getTaskName())) {
//                                    List<TProxyUserinfoEntity> zxywcjzgno_prox = proxyUserinfoService.findProxUserByEmpnoNew(zxywcjzgno);
//                                    if (zxywcjzgno_prox != null && zxywcjzgno_prox.size() > 0) {
//                                        for (TProxyUserinfoEntity xtkfcjzgnoProx : zxywcjzgno_prox) {
//                                            if (xtkfcjzgnoProx.getSupplyempno().equals(xtkfcjzgno)) {
//                                                flag = true;
//                                                break;
//                                            }
//                                        }
//                                    }
//                                    if (flag) {
//                                        config = new InterConfig();
//                                        config.setTaskId(taskInfo1.getTaskId());
//                                        config.setVoteResult(this.getNodeHqTaskVarivles(parameter.getWorkFlowId(), taskInfo1.getTaskName(), status, entityR.getVersion()));
//                                        config.setTaskName("資訊運維處級主管");
//                                        config.setProcessId(entityR.getProcessid());
//                                        config.setAssignee(zxywcjzgno);
//                                        InterResult interResult_auto = processService.completeHuiqianTask(config);
//                                        if ("SUCCESS".equals(interResult_auto.getStatus())) {
//                                            //添加簽核記錄
//                                            TQhChargelogEntity entity2 = new TQhChargelogEntity();
//                                            User user2 = userService.getUser(taskInfo1.getAssignee());
//                                            entity2.setChargename(user2.getName());
//                                            WfNodeinfoEntity nodeinfoEntity = wfNodeinfoService.findByTaskName(taskInfo1.getTaskName(), entityR.getWorkflowid(), entityR.getVersion());
//                                            entity2.setChargenode(nodeinfoEntity.getNodealain());
//                                            entity2.setSerialno(parameter.getSerialno());
//                                            entity2.setChargeno(interResult1.getTaskInfoList().get(0).getAssignee());
//                                            entity2.setWorkflowid(parameter.getWorkFlowId());
//                                            entity2.setOperateip("");
//                                            entity2.setIspass("通過");
//                                            User user3 = userService.getUser(xtkfcjzgno);
//                                            entity2.setDecrib("(" + user3.getLoginName() + "/" + user3.getName() + "代簽)");
//                                            tQhChargelogService.saveEntity(entity2);
//                                        }
//                                    }
//                                } else if ("資訊運維中心級主管".equals(interResult1.getTaskInfoList().get(0).getTaskName())) {//v3版本"資訊運維處級主管"改為"資訊運維中心級主管"
//                                    List<TProxyUserinfoEntity> zxywcjzgno_prox = proxyUserinfoService.findProxUserByEmpnoNew(zxywcjzgno);
//                                    if (zxywcjzgno_prox != null && zxywcjzgno_prox.size() > 0) {
//                                        for (TProxyUserinfoEntity xtkfcjzgnoProx : zxywcjzgno_prox) {
//                                            if (xtkfcjzgnoProx.getSupplyempno().equals(xtkfcjzgno)) {
//                                                flag = true;
//                                                break;
//                                            }
//                                        }
//                                    }
//                                    if (flag) {
//                                        config = new InterConfig();
//                                        config.setTaskId(taskInfo1.getTaskId());
//                                        config.setVoteResult(this.getNodeHqTaskVarivles(parameter.getWorkFlowId(), taskInfo1.getTaskName(), status, entityR.getVersion()));
//                                        config.setTaskName("資訊運維中心級主管");
//                                        config.setProcessId(entityR.getProcessid());
//                                        config.setAssignee(zxywcjzgno);
//                                        InterResult interResult_auto = processService.completeHuiqianTask(config);
//                                        if ("SUCCESS".equals(interResult_auto.getStatus())) {
//                                            //添加簽核記錄
//                                            TQhChargelogEntity entity2 = new TQhChargelogEntity();
//                                            User user2 = userService.getUser(taskInfo1.getAssignee());
//                                            entity2.setChargename(user2.getName());
//                                            WfNodeinfoEntity nodeinfoEntity = wfNodeinfoService.findByTaskName(taskInfo1.getTaskName(), entityR.getWorkflowid(), entityR.getVersion());
//                                            entity2.setChargenode(nodeinfoEntity.getNodealain());
//                                            entity2.setSerialno(parameter.getSerialno());
//                                            entity2.setChargeno(interResult1.getTaskInfoList().get(0).getAssignee());
//                                            entity2.setWorkflowid(parameter.getWorkFlowId());
//                                            entity2.setOperateip("");
//                                            entity2.setIspass("通過");
//                                            User user3 = userService.getUser(xtkfcjzgno);
//                                            entity2.setDecrib("(" + user3.getLoginName() + "/" + user3.getName() + "代簽)");
//                                            tQhChargelogService.saveEntity(entity2);
//                                        }
//                                    }
//                                }
//                            }
//                        }
//                    }
//                    /*1.黨“資訊運維中心級主管”與“資訊處級主管”節點為同一主管簽核時， 只需簽核一次，可顯示2條簽核記錄；
//                    2.黨審核到“資訊處級主管”時，判斷是否有代理，如有並且代理人為“資訊運維中心級主管 ”節點對應人員，該節點自動審核；
//                    3.黨審核到“資訊運維中心級主管 ”時，判斷是否有代理，如有並且代理人為“資訊處級主管 ”節點對應人員，該節點自動審核；*/
//                    ArrayList<String> arrayList = new ArrayList<String>();
//                    arrayList.add("dzqh_wuxianwangluoshenqing");
//                    arrayList.add("dzqh_IEMailzhanghaoshenqing");
//                    arrayList.add("dzqh_teshuwangluoshenqing");
//                    arrayList.add("dzqh_wwwswzhshenqing");
//                    arrayList.add("dzqh_sslvpnfuwushenqing");
//                    arrayList.add("dzqh_gerendiannaoteshuquanxianshenqing");
//                    for (String o : arrayList) {
//                        if (workflowId.contains(o)) {
//                            //嘗試更新自動審核后沒有ip的情況
//                            String nodeName = "運維服務處級主管";//以前版本，新版本改為"資訊運維中心級主管"
//                            if ("dzqh_wuxianwangluoshenqing_v3".equals(workflowId) || "dzqh_IEMailzhanghaoshenqing_v4".equals(workflowId) || "dzqh_teshuwangluoshenqing_v5".equals(workflowId) ||
//                                    "dzqh_wwwswzhshenqing_v4".equals(workflowId) || "dzqh_sslvpnfuwushenqing_v4".equals(workflowId) || "dzqh_gerendiannaoteshuquanxianshenqing_v6".equals(workflowId)) {
//                                nodeName = "資訊運維中心級主管";
//                            }
//                            if ("資訊處級主管".equals(info.getTaskName())) {
//                                tQhChargelogService.updateAutoAuditAndNoIp(parameter.getSerialno(), parameter.getIp(), nodeName);
//                            }
//                            if ("0".equals(status)) {
//                                InterConfig config_auto = new InterConfig();
//                                config_auto.setProcessId(entityR.getProcessid());
//                                Boolean flag = false;
//                                InterResult interResult2 = processService.currentTaskInfo(config_auto);
//                                Object obj = allRelationService.findByDto(entityR.getDtoName(), entityR.getSerialno());
//                                String ylno3 = "";
//                                if (workflowId.contains("dzqh_gerendiannaoteshuquanxianshenqing")) {
//                                    ylno3 = (String) Reflections.getFieldValue(obj, "ylno10");
//                                } else {
//                                    ylno3 = (String) Reflections.getFieldValue(obj, "ylno3");
//                                }
//                                String zxcchargeno = (String) Reflections.getFieldValue(obj, "zxcchargeno");
//                                if (interResult2.getTaskInfoList() != null && interResult2.getTaskInfoList().size() > 0) {
//                                    TaskInfo taskInfo1 = interResult2.getTaskInfoList().get(0);
//                                    if (nodeName.equals(taskInfo1.getTaskName())) {
//                                        List<TProxyUserinfoEntity> zxywcjzgno_prox = null;
//                                        zxywcjzgno_prox = proxyUserinfoService.findProxUserByEmpnoNew(ylno3);
//                                        if (zxywcjzgno_prox != null && zxywcjzgno_prox.size() > 0) {
//                                            for (TProxyUserinfoEntity xtkfcjzgnoProx : zxywcjzgno_prox) {
//                                                if (xtkfcjzgnoProx.getSupplyempno().equals(zxcchargeno)) {
//                                                    flag = true;
//                                                    break;
//                                                }
//                                            }
//                                        }
//                                        if (flag) {
//                                            config = new InterConfig();
//                                            config.setTaskId(taskInfo1.getTaskId());
//                                            config.setVoteResult(this.getNodeHqTaskVarivles(parameter.getWorkFlowId(), taskInfo1.getTaskName(), status, entityR.getVersion()));
//                                            config.setTaskName(nodeName);
//                                            config.setProcessId(entityR.getProcessid());
//                                            config.setAssignee(ylno3);
//                                            InterResult interResult_auto = processService.completeHuiqianTask(config);
//                                            if ("SUCCESS".equals(interResult_auto.getStatus())) {
//                                                //添加簽核記錄
//                                                TQhChargelogEntity entity2 = new TQhChargelogEntity();
//                                                User user2 = userService.getUser(taskInfo1.getAssignee());
//                                                entity2.setChargename(user2.getName());
//                                                WfNodeinfoEntity nodeinfoEntity = wfNodeinfoService.findByTaskName(taskInfo1.getTaskName(), entityR.getWorkflowid(), entityR.getVersion());
//                                                entity2.setChargenode(nodeinfoEntity.getNodealain());
//                                                entity2.setSerialno(parameter.getSerialno());
//                                                entity2.setChargeno(interResult2.getTaskInfoList().get(0).getAssignee());
//                                                entity2.setWorkflowid(parameter.getWorkFlowId());
//                                                entity2.setOperateip("");
//                                                entity2.setIspass("通過");
//                                                User user3 = userService.getUser(zxcchargeno);
//                                                entity2.setDecrib("(" + user3.getLoginName() + "/" + user3.getName() + "代簽)");
//                                                tQhChargelogService.saveEntity(entity2);
//                                            }
//                                        }
//                                    } else if ("資訊處級主管".equals(taskInfo1.getTaskName())) {
//                                        List<TProxyUserinfoEntity> zxywcjzgno_prox = proxyUserinfoService.findProxUserByEmpnoNew(zxcchargeno);
//                                        if (zxywcjzgno_prox != null && zxywcjzgno_prox.size() > 0) {
//                                            for (TProxyUserinfoEntity xtkfcjzgnoProx : zxywcjzgno_prox) {
//                                                if (xtkfcjzgnoProx.getSupplyempno().equals(ylno3)) {
//                                                    flag = true;
//                                                    break;
//                                                }
//                                            }
//                                        } else if (ylno3.equals(zxcchargeno)) {
//                                            flag = true;
//                                        }
//                                        if (flag) {
//                                            config = new InterConfig();
//                                            config.setTaskId(taskInfo1.getTaskId());
//                                            config.setVoteResult(this.getNodeHqTaskVarivles(parameter.getWorkFlowId(), taskInfo1.getTaskName(), status, entityR.getVersion()));
//                                            config.setTaskName("資訊處級主管");
//                                            config.setProcessId(entityR.getProcessid());
//                                            config.setAssignee(zxcchargeno);
//                                            InterResult interResult_auto = processService.completeHuiqianTask(config);
//                                            if ("SUCCESS".equals(interResult_auto.getStatus())) {
//                                                //添加簽核記錄
//                                                TQhChargelogEntity entity2 = new TQhChargelogEntity();
//                                                User user2 = userService.getUser(taskInfo1.getAssignee());
//                                                entity2.setChargename(user2.getName());
//                                                WfNodeinfoEntity nodeinfoEntity = wfNodeinfoService.findByTaskName(taskInfo1.getTaskName(), entityR.getWorkflowid(), entityR.getVersion());
//                                                entity2.setChargenode(nodeinfoEntity.getNodealain());
//                                                entity2.setSerialno(parameter.getSerialno());
//                                                entity2.setChargeno(interResult2.getTaskInfoList().get(0).getAssignee());
//                                                entity2.setWorkflowid(parameter.getWorkFlowId());
//                                                TQhChargelogEntity qhChargelog = tQhChargelogService.queryByNodenameAndSerialNo(parameter.getSerialno(), nodeName);
//                                                entity2.setOperateip(qhChargelog.getOperateip());
//                                                entity2.setIspass("通過");
//                                                User user3 = userService.getUser(ylno3);
//                                                if (ylno3.equals(zxcchargeno)) {
//                                                    entity2.setDecrib(qhChargelog.getDecrib());
//                                                } else {
//                                                    entity2.setDecrib("(" + user3.getLoginName() + "/" + user3.getName() + "代簽)");
//                                                }
//                                                tQhChargelogService.saveEntity(entity2);
//                                            }
//                                        }
//                                    }
//                                }
//                            }
//                        }
//                    }
//                } catch (Exception e) {
//                    //記錄錯誤日誌，待測試
//                    logger.error("自動跳過部級主管審核失敗", e);
//                }
//                //如果是駁回更新中間表狀態
//                if ("1".equals(status)) {
//                    entityR.setWorkstatus(Constant.RESULT.CODE_REJECT.getValue());
//                    if (parameter.getAuto() != null && parameter.getAuto() == true) {
//                        allRelationService.updateEntity(entityR);
//                    } else {
//                        allRelationService.update(entityR);
//                    }
//                    allRelationService.updateFormStatic(parameter.getSerialno(), Constant.RESULT.CODE_REJECT.getValue(), entityR.getDtoName());
//
//
//                }
//                result = AutoCompleteTask(parameter.getProcessId());
////                InterConfig configStatus = new InterConfig();
////                configStatus.setProcessId(parameter.getProcessId());
////                InterResult interResults = processService.processStatus(configStatus);
////                if (interResults.getRunning().equals("false")) {
////                    return Constant.RESULT.CODE_COMPLETE.getValue();
////                }
//                //如果是完成，返回完成標識
//                if (result.equals(Constant.RESULT.CODE_COMPLETE.getValue())) {
//                    allRelationService.updateFormStaticComplete(parameter.getSerialno(), Constant.RESULT.CODE_COMPLETE.getValue(), entityR.getDtoName());
//                    return result;
//                }
//                return Constant.RESULT.CODE_YES.getValue();
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return Constant.RESULT.CODE_NO.getValue();
//    }
//
//    private String getNodeTaskVarivles(String workflowid, String taskName, String status, String version) {
//        WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(taskName, workflowid, version);
//        WfNodeparamEntity nodeparamEntity = nodeparamService.getInfoById(entity, status);
//        return nodeparamEntity.getParamename() + "," + nodeparamEntity.getParamvalue() + "," + nodeparamEntity.getParamtype() + ";";
//    }
//
//    private String getNodeHqTaskVarivles(String workflowid, String taskName, String status, String version) {
//        WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(taskName, workflowid, version);
//        WfNodeparamEntity nodeparamEntity = nodeparamService.getInfoById(entity, status);
//        return nodeparamEntity.getParamvalue();
//    }

    /**
     * 方法描述: 自動推動任務進行
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/9  下午 06:15
     * @Return
     **/

    public String AutoCompleteTask(String processId,String workFlowId) {
        try {
            String auto = dictService.get(6).getValue();
            //查詢流程狀態
            InterConfig config = new InterConfig();
            config.setProcessId(processId);
            config.setWorkFlowId(workFlowId);
            InterResult interResult = processService.processStatus(config);
            //如果是運行狀態
            if ("SUCCESS".equals(interResult.getStatus()) && interResult.getRunning().equals("true")) {
                config = new InterConfig();
                config.setProcessId(processId);
                config.setWorkFlowId(workFlowId);
                InterResult taskInfo = processService.currentTaskInfo(config);
                while (taskInfo.getTaskInfoList() != null && taskInfo.getTaskInfoList().size() > 0 && auto.equals(taskInfo.getTaskInfoList().get(0).getAssignee())) {
                    config = new InterConfig();
                    config.setUserId(auto);
                    config.setTaskId(taskInfo.getTaskInfoList().get(0).getTaskId());
                    config.setProcessId(processId);
                    config.setWorkFlowId(workFlowId);
                    processService.completeAutoTask(config);
                    //更新中間表代理人信息，代理人功能優化新增
//                    TQhAllRelationEntity entityProx = allRelationService.queryEntityByProcess(processId);
//                    WfNodeinfoEntity nodeinfo = wfNodeinfoService.findProxTaskName(taskInfo.getTaskInfoList().get(0).getTaskName(), entityProx.getWorkflowid(), entityProx.getVersion());
//                    entityProx.setNodename(nodeinfo.getNodealain());
//                    allRelationService.update(entityProx);
                    //九張資訊表單優化資訊運維部級主管與資訊運營中心主管為同一人時 只需簽核一次，但要補簽核記錄
                    if ("資訊運營中心主管".equals(taskInfo.getTaskInfoList().get(0).getTaskName())) {
                        TQhAllRelationEntity entityR = allRelationService.queryEntityByProcess(processId);
                        TQhChargelogEntity entity = null;
                        if (entityR.getWorkflowid().contains("dzqh_ptalkzhanghaoshenqingdan")) {
                            WfptalkprocessEntity wfptalkprocess = wfptalkprocessService.findBySerialno(entityR.getSerialno());
                            if (StringUtils.isNotEmpty(wfptalkprocess.getZzyyzxno()) && wfptalkprocess.getZzyyzxno().equals(wfptalkprocess.getYwbchargeno())) {
                                entity = tQhChargelogService.queryByNodenameAndSerialNo(entityR.getSerialno(), "資訊運維部級主管");
                            }
                        } else if (entityR.getWorkflowid().contains("dzqh_IEMailzhanghaoshenqing")) {
                            WfinternetemailprocessEntity wfinternetemail = wfinternetemailprocessService.findBySerialno(entityR.getSerialno());
                            if (StringUtils.isNotEmpty(wfinternetemail.getZzyyzxno()) && wfinternetemail.getZzyyzxno().equals(wfinternetemail.getYlno6())) {
                                entity = tQhChargelogService.queryByNodenameAndSerialNo(entityR.getSerialno(), "資訊運維部級主管");
                            }
                        } else if (entityR.getWorkflowid().contains("dzqh_teshuwangluoshenqing")) {
                            WfspecialnetprocessEntity wfspecialnet = wfspecialnetprocessService.findBySerialno(entityR.getSerialno());
                            if (StringUtils.isNotEmpty(wfspecialnet.getZzyyzxno()) && wfspecialnet.getZzyyzxno().equals(wfspecialnet.getYlno6())) {
                                entity = tQhChargelogService.queryByNodenameAndSerialNo(entityR.getSerialno(), "資訊運維部級主管");
                            }
                        } else if (entityR.getWorkflowid().contains("dzqh_SuperNotesfuwushenqing")) {
                            WfSupernotesEntity wfSupernotes = wfSupernotesService.findBySerialno(entityR.getSerialno());
                            if (StringUtils.isNotEmpty(wfSupernotes.getZzyyzxno()) && wfSupernotes.getZzyyzxno().equals(wfSupernotes.getYlno6())) {
                                entity = tQhChargelogService.queryByNodenameAndSerialNo(entityR.getSerialno(), "資訊運維部級主管");
                            }
                        } else if (entityR.getWorkflowid().contains("dzqh_wwwswzhshenqing")) {
                            WfwwwprocessEntity wfwwwprocess = wfwwwprocessService.findBySerialno(entityR.getSerialno());
                            if (StringUtils.isNotEmpty(wfwwwprocess.getZzyyzxno()) && wfwwwprocess.getZzyyzxno().equals(wfwwwprocess.getYlno6())) {
                                entity = tQhChargelogService.queryByNodenameAndSerialNo(entityR.getSerialno(), "資訊運維部級主管");
                            }
                        } else if (entityR.getWorkflowid().contains("dzqh_sslvpnfuwushenqing")) {
                            WfsslvpnprocessEntity wfsslvpn = wfsslvpnprocessService.findBySerialno(entityR.getSerialno());
                            if (StringUtils.isNotEmpty(wfsslvpn.getZzyyzxno()) && wfsslvpn.getZzyyzxno().equals(wfsslvpn.getYlno6())) {
                                entity = tQhChargelogService.queryByNodenameAndSerialNo(entityR.getSerialno(), "資訊運維部級主管");
                            }
                        } else if (entityR.getWorkflowid().contains("dzqh_gerendiannaoteshuquanxianshenqing")) {
                            WfpcprivilegeprocessEntity wfpcprivilege = wfpcprivilegeprocessService.findBySerialno(entityR.getSerialno());
                            if (StringUtils.isNotEmpty(wfpcprivilege.getZzyyzxno()) && wfpcprivilege.getZzyyzxno().equals(wfpcprivilege.getYlno15())) {
                                entity = tQhChargelogService.queryByNodenameAndSerialNo(entityR.getSerialno(), "資訊運維部級主管");
                            }
                        } else if (entityR.getWorkflowid().contains("dzqh_ruantianzhuangshenqingdan")) {
                            WfSoftinstallProcessEntity wfSoftinstall = wfSoftinstallProcessService.findBySerialno(entityR.getSerialno());
                            if (StringUtils.isNotEmpty(wfSoftinstall.getZzyyzxno()) && wfSoftinstall.getZzyyzxno().equals(wfSoftinstall.getYlno15())) {
                                entity = tQhChargelogService.queryByNodenameAndSerialNo(entityR.getSerialno(), "資訊運維部級主管");
                            }
                        } else if (entityR.getWorkflowid().contains("dzqh_wuxianwangluoshenqing")) {
                            WfvlanprocessEntity wfvlan = wfvlanprocessService.findBySerialno(entityR.getSerialno());
                            if (StringUtils.isNotEmpty(wfvlan.getZzyyzxno()) && wfvlan.getZzyyzxno().equals(wfvlan.getYlno6())) {
                                entity = tQhChargelogService.queryByNodenameAndSerialNo(entityR.getSerialno(), "資訊運維部級主管");
                            }
                        }

                        if (entity != null) {
                            TQhChargelogEntity entitynew = new TQhChargelogEntity();
                            entitynew.setChargename(entity.getChargename());
                            entitynew.setChargenode("資訊營運中心主管");
                            entitynew.setSerialno(entity.getSerialno());
                            entitynew.setChargeno(entity.getChargeno());
                            entitynew.setWorkflowid(entity.getWorkflowid());
                            entitynew.setOperateip(entity.getOperateip());
                            entitynew.setIspass(entity.getIspass());
                            tQhChargelogService.saveEntity(entitynew);
                        }
                    }

                    //再查詢下個節點
                    config = new InterConfig();
                    config.setProcessId(processId);
                    config.setWorkFlowId(workFlowId);
                    taskInfo = processService.currentTaskInfo(config);
                }
            } else {
                //返回完成標識
                return Constant.RESULT.CODE_COMPLETE.getValue();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return Constant.RESULT.CODE_YES.getValue();
    }

    public void setBeanAutorBlank(Object obj, String workFlowId) {
        try {
            String auto = dictService.get(6).getValue();
            List<WfNodeinfoEntity> entityList = wfNodeinfoService.findByFilters(workFlowId);
            for (WfNodeinfoEntity entity : entityList) {
                String value = (String) Reflections.getFieldValue(obj, entity.getColname());
                if (auto.equals(value)) {
                    Reflections.setFieldValue(obj, entity.getColname(), "");
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 方法描述: 取消表單
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/10  上午 08:27
     * @Return
     **/
    @Transactional(readOnly = false)
    public String cancelTask(String serialNo, String ip) {
        try {
            TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialNo);
//        return Constant.RESULT.CODE_YES.getValue();
            InterConfig config = new InterConfig();
            config.setProcessId(relationEntity.getProcessid());
            InterResult interResult = processService.processEnd(config);
            if ("SUCCESS".equals(interResult.getStatus())) {

                //添加簽核記錄
                //獲取流程當前節點處理人
                TQhChargelogEntity entity = new TQhChargelogEntity();
                entity.setChargename(UserUtil.getCurrentUser().getName());
//                entity.setChargename("S6112942");
                entity.setChargenode("填單人修改");
                entity.setSerialno(serialNo);
                entity.setChargeno(UserUtil.getCurrentUser().getLoginName());
                entity.setWorkflowid(relationEntity.getWorkflowid());
                entity.setOperateip(ip);
                entity.setIspass("取消申請");
                tQhChargelogService.save(entity);

                relationEntity.setWorkstatus(Constant.RESULT.CODE_CANCLE.getValue());
                allRelationService.update(relationEntity);
                allRelationService.updateFormStatic(relationEntity.getSerialno(), Constant.RESULT.CODE_CANCLE.getValue(), relationEntity.getDtoName());
                /**
                 * 方法描述: 取消申請清空品管表單三張審核中信息
                 * @Author: S6114648
                 * @CreateDate: 2022/6/22  13:35
                 * @Return
                 **/
                if (relationEntity.getWorkflowid() != null && (relationEntity.getWorkflowid().contains("dzqh_gongchengbaofeishenqingdan") || relationEntity.getWorkflowid().contains("dzqh_gongchengbaofeidan")
                        || relationEntity.getWorkflowid().contains("dzqh_zhichengbaofeishenqingdan") || relationEntity.getWorkflowid().contains("dzqh_zhichengbaofeidan"))) {
                    allRelationService.updateScrapCancelData(relationEntity.getDtoName(), serialNo, "", "", "", "", "");
                }
                //違紀表單取消要發e通關系統
                if (relationEntity.getWorkflowid() != null && relationEntity.getWorkflowid().contains("dzqh_yuangongweiji")) {
                    wfIlegalProcessesService.sendToETongGuan(serialNo);
                }
                return Constant.RESULT.CODE_YES.getValue();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return Constant.RESULT.CODE_NO.getValue();
        }
        return Constant.RESULT.CODE_NO.getValue();
    }

    /**
     * 方法描述: 自動設置為空的審核人為AUTOFIN_DZQH
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/10  上午 08:36
     * @Return
     **/

    public void setWfAutorBlank(Object obj, String workFlowId) {
        try {
            String auto = dictService.get(6).getValue();
            List<WfNodeinfoEntity> entityList = wfNodeinfoService.findByFilters(workFlowId);
            for (WfNodeinfoEntity entity : entityList) {
                String value = (String) Reflections.getFieldValue(obj, entity.getColname());
                if (StringUtils.isBlank(value)) {
                    Reflections.setFieldValue(obj, entity.getColname(), auto);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 方法描述: 獲取簽核路徑
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/10  上午 09:35
     * @Return
     **/

    public String getChargeNodeInfo(Object obj, String workFlowId, String processId) {
        String result = "";
        StringBuffer stringBuffer = null;
        Boolean flag = true;
        try {
            String auto = dictService.get(6).getValue();
            stringBuffer = new StringBuffer();
            List<WfNodeinfoEntity> entityList = wfNodeinfoService.findByFilters(workFlowId);
            InterConfig config = new InterConfig();
            config.setProcessId(processId);
            if (processId != null && processId != "") {
                boolean ifAuto= "Y".equals(conifgService.findUnique(workFlowId).getSignPathTemp());
                InterResult taskInfo = processService.currentTaskInfo(config);
                if ("SUCCESS".equals(taskInfo.getStatus())) {
                    for (WfNodeinfoEntity entity : entityList) {
                        if ("makerno".equals(entity.getColname())) {
                            continue;
                        }
                        String value = (String) Reflections.getFieldValue(obj, entity.getColname());
                        if (ifAuto) {
                            if (StrUtil.isNotEmpty(value) && !auto.equals(value)) {
                                if (taskInfo.getTaskInfoList() != null && taskInfo.getTaskInfoList().get(0) != null && taskInfo.getTaskInfoList().get(0).getTaskName().equals(entity.getNodename()) && flag) {
                                    stringBuffer.append("<strong><font size=\"3\" color=red>" + entity.getNodealain() + "(");
                                    String valueName = (String) Reflections.getFieldValue(obj, entity.getColname().replace("no", "name"));
                                    stringBuffer.append(value + "/" + valueName + ")</font></strong>->");
                                    flag = false;
                                } else {
                                    stringBuffer.append(entity.getNodealain() + "(");
                                    String valueName = (String) Reflections.getFieldValue(obj, entity.getColname().replace("no", "name"));
                                    stringBuffer.append(value + "/" + valueName + ")->");
                                }
                            }
                        } else {
                            if (taskInfo.getTaskInfoList() != null && taskInfo.getTaskInfoList().get(0) != null && taskInfo.getTaskInfoList().get(0).getTaskName().equals(entity.getNodename()) && flag) {
                                stringBuffer.append("<strong><font size=\"3\" color=red>" + entity.getNodealain() + "(");
                                if (auto.equals(value)) {
                                    stringBuffer.append("/" + ")->");
                                } else {
                                    String valueName = (String) Reflections.getFieldValue(obj, entity.getColname().replace("no", "name"));
                                    stringBuffer.append(value + "/" + valueName + ")</font></strong>->");
                                }
                                flag = false;
                            } else {
                                stringBuffer.append(entity.getNodealain() + "(");
                                if (auto.equals(value)) {
                                    stringBuffer.append("/" + ")->");
                                } else {
                                    String valueName = (String) Reflections.getFieldValue(obj, entity.getColname().replace("no", "name"));
                                    stringBuffer.append(value + "/" + valueName + ")->");
                                }
                            }
                        }
                    }
                    Assert.hasText(stringBuffer.toString());
                    result = stringBuffer.toString().substring(0, stringBuffer.length() - 2);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 方法描述: 獲取簽核路徑,新舊流程兼容的方法
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/10  上午 09:35
     * @Return
     **/

    public String getChargeNodeInfoByVersion(Object obj, TQhAllRelationEntity rentity) {
        String result = "";
        StringBuffer stringBuffer = null;
        Boolean flag = true;
        try {
            String auto = dictService.get(6).getValue();
            stringBuffer = new StringBuffer();
            String prox_user = "";
            List<WfNodeinfoEntity> entityList = wfNodeinfoService.findByFiltersAndVersion(rentity);
            InterConfig config = new InterConfig();
            config.setProcessId(rentity.getProcessid());
            if (rentity.getProcessid() != null && rentity.getProcessid() != "") {
                boolean ifAuto= "Y".equals(conifgService.findUnique(rentity.getWorkflowid()).getSignPathTemp());
                InterResult taskInfo = processService.currentTaskInfo(config);
                if ("SUCCESS".equals(taskInfo.getStatus())) {
                    for (WfNodeinfoEntity entity : entityList) {
                        if ("makerno".equals(entity.getColname())) {
                            continue;
                        }
                        String value = (String) Reflections.getFieldValue(obj, entity.getColname());
                        String valueName = (String) Reflections.getFieldValue(obj, entity.getColname().replace("no", "name"));
                        TQhChargelogEntity tQhChargelogEntity = tQhChargelogService.queryByNodenameAndSerialNo(rentity.getSerialno(), entity.getNodealain());
                        List<String> workflowIds = Arrays.asList(Constant.WORKFLOWID_PROX.split(","));
                        if (workflowIds.stream().anyMatch(workflowId->rentity.getWorkflowid().contains(workflowId))) {
                            List<TProxyFormUserinfoEntity> proxyFormUserinfoEntities = formUserinfoService.findProxUserByLogCreateDate(rentity.getCreateDate(),entity.getNodealain(),rentity.getWorkflowid());
                            if (CollectionUtil.isNotEmpty(proxyFormUserinfoEntities)) {
                                for (TProxyFormUserinfoEntity entity1 : proxyFormUserinfoEntities) {
                                    if (value.contains(entity1.getSupplyempno())) {
                                        value = value.replace(entity1.getSupplyempno(), entity1.getSuppliedempno());
                                        valueName = valueName.replace(entity1.getSupplyusername(), entity1.getSuppliedusername());
                                    }
                                }
                            }
                        }
                        if (ifAuto) {
                            if (StrUtil.isNotEmpty(value) && !auto.equals(value)) {
                                if (taskInfo.getTaskInfoList() != null && taskInfo.getTaskInfoList().get(0) != null && taskInfo.getTaskInfoList().get(0).getTaskName().equals(entity.getNodename()) && flag) {
                                    stringBuffer.append("<strong><font size=\"3\" color=red>" + entity.getNodealain() + "(");
//                                    String valueName = (String) Reflections.getFieldValue(obj, entity.getColname().replace("no", "name"));
                                    stringBuffer.append(value + "/" + valueName + ")</font></strong>->");
                                    flag = false;
                                } else {
                                    stringBuffer.append(entity.getNodealain() + "(");
//                                    String valueName = (String) Reflections.getFieldValue(obj, entity.getColname().replace("no", "name"));
                                    stringBuffer.append(value + "/" + valueName + ")->");
                                }
                            }
                        } else {
                            if (taskInfo.getTaskInfoList() != null && taskInfo.getTaskInfoList().get(0) != null && taskInfo.getTaskInfoList().get(0).getTaskName().equals(entity.getNodename()) && flag) {
                                stringBuffer.append("<strong><font size=\"3\" color=red>" + entity.getNodealain() + "(");
                                if (auto.equals(value)) {
                                    stringBuffer.append("/" + ")->");
                                } else {
//                                    String valueName = (String) Reflections.getFieldValue(obj, entity.getColname().replace("no", "name"));
                                    stringBuffer.append(value + "/" + valueName + ")</font></strong>->");
                                }
                                flag = false;
                            } else {
                                stringBuffer.append(entity.getNodealain() + "(");
                                if (auto.equals(value)) {
                                    stringBuffer.append("/" + ")->");
                                } else {
//                                    String valueName = (String) Reflections.getFieldValue(obj, entity.getColname().replace("no", "name"));
                                    stringBuffer.append(value + "/" + valueName + ")->");
                                }
                            }
                        }
                    }
                    Assert.hasText(stringBuffer.toString());
                    result = stringBuffer.toString().substring(0, stringBuffer.length() - 2);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 方法描述: 返回流程圖片
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/13  下午 01:20
     * @Return
     **/

    public String getImgUrl(String processId) {
        TQhAllRelationEntity allRelationEntity = allRelationService.queryEntityByProcess(processId);
        String imgPath = "";
        if (ObjectUtil.isNotNull(allRelationEntity)&&StrUtil.equals("L", allRelationEntity.getFormFrom())) {
            imgPath = dictService.get(3010051).getValue() + "?sysCode=esign&processId=" + processId + "&t=" + UUID.randomUUID();
        }else{
            imgPath = dictService.get(301005).getValue() + "?processId=" + processId + "&t=" + UUID.randomUUID();
        }
        return imgPath;
    }

    /**
     * 方法描述: 查詢簽核節點參數信息，顯示按鈕
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/13  下午 04:53
     * @Return
     **/

    public List<WfNodeparamEntity> queryNodeparam(String serialNo) {
        List<WfNodeparamEntity> entityList = new ArrayList<>();
        try {
            TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialNo);
            InterConfig config = new InterConfig();
            config.setProcessId(relationEntity.getProcessid());
            //獲取流程當前節點處理人
            InterResult taskInfo = processService.currentTaskInfo(config);
            if ("SUCCESS".equals(taskInfo.getStatus())) {
                if (taskInfo.getTaskInfoList() == null) {
                    return entityList;
                }
                TaskInfo info = taskInfo.getTaskInfoList().get(0);
                WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(info.getTaskName(), relationEntity.getWorkflowid(), relationEntity.getVersion());
                entityList = nodeparamService.queryNodeInfos(entity.getNodeid(), relationEntity.getVersion());
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return entityList;
    }

    /**
     * 方法描述: 獲取簽核記錄
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/16  上午 08:20
     * @Return
     **/

    public Page<TQhChargelogEntity> queryChargeLog(Page<TQhChargelogEntity> page, List<PropertyFilter> filters) {
        Page<TQhChargelogEntity> pageResult = chargelogService.search(page, filters);
        return pageResult;
    }

    /**
     * 方法描述: 獲取當前記錄的簽核節點和簽核人
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/19  下午 04:58
     * @Return
     **/

    public TaskNode getNodeInfo(String serialNo) {
        TaskNode node = new TaskNode();
        TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialNo);
        //只獲取簽核中和駁回的
        try {
            if (relationEntity != null) {
                int nodeSize = wfNodeinfoService.findByFilters(relationEntity.getWorkflowid()).size();
                if (relationEntity != null && ("2".equals(relationEntity.getWorkstatus()) || "4".equals(relationEntity.getWorkstatus()))) {
                    InterConfig config = new InterConfig();
                    config.setProcessId(relationEntity.getProcessid());
                    //獲取流程當前節點處理人
                    InterResult taskInfo = processService.currentTaskInfo(config);
                    if (taskInfo != null && "SUCCESS".equals(taskInfo.getStatus()) && taskInfo.getTaskInfoList() != null && taskInfo.getTaskInfoList().size() > 0) {
                        TaskInfo info = taskInfo.getTaskInfoList().get(0);
                        if (info != null) {
                            WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(info.getTaskName(), relationEntity.getWorkflowid(), relationEntity.getVersion());
                            if (entity != null) {
                                node.setNodeName(entity.getNodealain());
                                node.setAuditProgress((Math.ceil(entity.getOrderby() * 100 / nodeSize)) + "");
                            }
                            TQhUserformhsEntity tentity = tQhUserformhsService.findByEmpno(info.getAssignee());
                            node.setAuditUser(info.getAssignee() + "/" + (tentity == null ? info.getAssignee() + "(該人員在職狀態異常)" : tentity.getEmpname()));
                            User user = userService.getUser(info.getAssignee());
                            if (user != null && user.getEmail() != null) {
                                node.setUserEmail(user.getEmail());
                            }
                            if (user != null && user.getPhone() != null) {
                                node.setUserPhone(user.getPhone());
                            }
                        } else {
                            node.setAuditUser("");
                            node.setNodeName("");
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return node;
    }

    /**
     * 方法描述: 通過workflowid查詢配置信息
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/22  下午 04:40
     * @Return
     **/

    public WfConifgEntity findByWorkFlowId(String workflowid) {
        return conifgService.findUnique(workflowid);
    }

    /**
     * 方法描述:  獲取當前節點名稱
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/29  下午 03:32
     * @Return
     **/
    public String getNodeName(String processId, String workflowId) {
        try {
            InterConfig config = new InterConfig();
            config.setProcessId(processId);
            config.setWorkFlowId(workflowId);
            //獲取流程當前節點處理人
            InterResult taskInfo = processService.currentTaskInfo(config);
            if ("SUCCESS".equals(taskInfo.getStatus()) && taskInfo.getTaskInfoList() != null) {
                TaskInfo info = taskInfo.getTaskInfoList().get(0);
                return info.getTaskName();
            } else {
                return null;
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }


    /**
     * 方法描述:  獲取當前節點名稱
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/29  下午 03:32
     * @Return
     **/

    public TaskInfo getNodeInfoName(String processId) {
        try {
            InterConfig config = new InterConfig();
            config.setProcessId(processId);
            //獲取流程當前節點處理人
            InterResult taskInfo = processService.currentTaskInfo(config);
            if ("SUCCESS".equals(taskInfo.getStatus()) && taskInfo.getTaskInfoList() != null) {
                TaskInfo info = taskInfo.getTaskInfoList().get(0);
                return info;
            } else {
                return null;
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 方法描述: 獲取當前簽核人
     *
     * @Author: S6114648
     * @CreateDate: 2018/11/9  上午 10:28
     * @Return
     **/

    public String getAssigneeInfo(String serialNo) {
        try {
            TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialNo);
            //只獲取簽核中和駁回的
            if ("2".equals(relationEntity.getWorkstatus()) || "4".equals(relationEntity.getWorkstatus())) {
                InterConfig config = new InterConfig();
                config.setProcessId(relationEntity.getProcessid());
                //獲取流程當前節點處理人
                InterResult taskInfo = processService.currentTaskInfo(config);
                if ("SUCCESS".equals(taskInfo.getStatus())) {
                    if (taskInfo.getTaskInfoList() != null && taskInfo.getTaskInfoList().size() > 0) {
                        TaskInfo info = taskInfo.getTaskInfoList().get(0);
                        if (info != null) {
                            WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(info.getTaskName(), relationEntity.getWorkflowid(), relationEntity.getVersion());
                            return info.getAssignee();
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }


    /**
     * 方法描述: 完成任務
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/10  下午 01:20
     * @Return
     **/
    @Transactional(readOnly = false)
    public String autoCompleteTask(String serialno, String ip, String status, String attachidsremark, String reattachids, String chargeNodeName) {
        //status 2 重新提交  3 取消申請  0 通過 1 駁回
        try {
            TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialno);
            Assert.notNull(relationEntity);
            CompleteTaskParameter parameter = new CompleteTaskParameter();
            parameter.setSerialno(serialno);
            parameter.setProcessId(relationEntity.getProcessid());
            parameter.setWorkFlowId(relationEntity.getWorkflowid());
            parameter.setIp(ip);
            parameter.setAuto(true);
            parameter.setAttachidsremark(attachidsremark);

            InterConfig config = new InterConfig();
            config.setProcessId(parameter.getProcessId());
            //獲取流程當前節點處理人
            InterResult taskInfo = processService.currentTaskInfo(config);
            if ("SUCCESS".equals(taskInfo.getStatus())) {
                //如果流程已經完成，刪除定時
                if (taskInfo.getTaskInfoList() == null) {
                    scheduleJobService.delJobsByKey(relationEntity.getSerialno());
                    return Constant.RESULT.CODE_AUDIT_BY_PEOPLE.getValue();
                }
                TaskInfo info = taskInfo.getTaskInfoList().get(0);
                WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(info.getTaskName(), parameter.getWorkFlowId(), relationEntity.getVersion());
                //查詢是否自動審核
//                Object obj1 = allRelationService.findByDto(relationEntity.getDtoName(), relationEntity.getSerialno());
                //如果是自動審核，但是工作流引擎中已經是下一個節點，刪除定時任務并返回已被人工審核的標識
                if (StringUtils.isNotEmpty(chargeNodeName) && !chargeNodeName.equals(entity.getNodename())) {
                    scheduleJobService.delJobsByKey(relationEntity.getSerialno());
                    return Constant.RESULT.CODE_AUDIT_BY_PEOPLE.getValue();
                }


                String result = "";
                String signtype = entity.getSigntype();

                if ("0".equals(entity.getSigntype())) {
                    result = chargLogService.completeTask(parameter, status);
                } else if ("1".equals(entity.getSigntype())) {
                    result = chargLogService.completeHqTask(parameter, status);
                }
                if (result.equals(Constant.RESULT.CODE_NO.getValue())) {
                    return Constant.RESULT.CODE_NO.getValue();
                } else {
                    //刪除定時
                    scheduleJobService.delJobsByKey(relationEntity.getSerialno());
                    //查詢是否需要發送短信
                    try {
                        WfNodeparamEntity nodeparamEntity = nodeparamService.getInfoById(entity, status);
                        if (StringUtils.isNotEmpty(nodeparamEntity.getIsNeedSms()) && "1".equals(nodeparamEntity.getIsNeedSms())) {
                            String userName = dictService.getUniqueDictByType(nodeparamEntity.getSmsTemplete() + "_username").getValue();
                            String password = dictService.getUniqueDictByType(nodeparamEntity.getSmsTemplete() + "_password").getValue();
                            String content = dictService.getUniqueDictByType(nodeparamEntity.getSmsTemplete() + "_content").getValue();
                            Object obj = allRelationService.findByDto(relationEntity.getDtoName(), relationEntity.getSerialno());
                            sendSmsService.sendSms(userName, password, Reflections.getFieldValue(obj, nodeparamEntity.getSmsCol()) + "", content);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                //對應窗口審核上傳的附件
                if (StringUtils.isNotEmpty(reattachids)) {
                    allRelationService.updateFormReattachids(serialno, reattachids, relationEntity.getDtoName());
                }
                //如果是完成則更新表單狀態及中間表狀態
                if (result == Constant.RESULT.CODE_COMPLETE.getValue()) {
                    allRelationService.updateFormStaticComplete(serialno, result, relationEntity.getDtoName());
                    relationEntity.setWorkstatus(result);
                    allRelationService.updateEntity(relationEntity);
                    //獲取填單人
                    String userEmpno = allRelationService.findUserEmpno(relationEntity.getDtoName(), serialno);
                    /**新舊工號兼容優化**/
                    EntityNewAndOld newAndOld = OldJobNoToNewNo.getNewNo(userEmpno);
                    /**新舊工號兼容優化**/
                    User user = userService.getUser(newAndOld.getNewEmpno());
//查詢是否有代理人
                    List<TProxyUserinfoEntity> audit_prox = proxyUserinfoService.findProxUserByEmpnoNew(newAndOld.getNewEmpno());
                    if (audit_prox != null && audit_prox.size() > 0) {
                        user = userService.getUser(audit_prox.get(0).getSupplyempno());
                    }
                    Gtasks gtasks = allRelationService.queryMyGtasks(relationEntity.getSerialno(), relationEntity.getDtoName());
                    Mail mail = new Mail();
                    mail.setUsername(gtasks.getMakername());
                    mail.setSystemname(dictService.getDictByTypeAndVlaue("sys_property", "sys_name").getLabel());
                    mail.setDusername(gtasks.getMakername());
                    mail.setChargerman(userService.getUser(info.getAssignee()).getName());
                    mail.setOrdertype(relationEntity.getWfName());
                    mail.setSerialno(serialno);
                    mail.setOrderstatus(result);
                    mail.setUrl(dictService.get(560).getValue());
                    mail.setUrlip(dictService.get(561).getValue());
//                    mail.setFreeloginurl("");
                    //保存發送記錄
                    TPubMailrecordEntity mailrecordEntity = new TPubMailrecordEntity();
                    mailrecordEntity.setChargerman(mail.getChargerman());
                    mailrecordEntity.setDusername(mail.getDusername());
                    mailrecordEntity.setOrderstatus(mail.getOrderstatus());
                    mailrecordEntity.setOrdertype(mail.getOrdertype());
                    mailrecordEntity.setSerialno(mail.getSerialno());
                    mailrecordEntity.setUsername(mail.getUsername());
                    mailrecordEntity.setUsermail(mail.getUsermail());
                    mailrecordEntity.setSendStatus("0");
                    mailrecordEntity.setEmpno(user.getLoginName());
                    mailrecordEntity.setUrl(mail.getUrl());
                    mailrecordEntity.setUrlip(mail.getUrlip());
//                    mailrecordEntity.setValidStr(validStr);
                    mailrecordService.saveEntity(mailrecordEntity);
                    if (user != null && user.getEmail() != null) {
                        WfConifgEntity conifgEntity = conifgService.findUnique(relationEntity.getWorkflowid());
//                    String validStr = UUID.randomUUID().toString().replace("-","");
                        mail.setUsermail(user.getEmail());
                        mailrecordEntity.setUsermail(mail.getUsermail());
                        mailrecordService.saveEntity(mailrecordEntity);
                        if ("1".equals(conifgEntity.getAppType())) {
                            Dict dict2 = new Dict();
                            dict2.setType("entfrmIpebgIP_app");
                            dict2.setCodeUniq("entfrmIpebgIP_app01");
                            String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
                            mail.setAppAuditUrl(StrUtil.concat(true, fronUrl, conifgEntity.getAppAuditUrl(), "?serialno=", mail.getSerialno()));
                        } else {
                            mail.setAppAuditUrl(StrUtil.concat(true, conifgEntity.getAppAuditUrl(), "?serialno=", mail.getSerialno()));
                        }
                        if (!relationEntity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                            if (!"0".equals(user.getEmailset())) {
                                String sendResult = new SendMailUtil().sendMail(mail);
                                if ("0".equals(sendResult)) {
                                    //發送成功，更新標誌
                                    mailrecordEntity.setSendStatus("1");
                                    mailrecordService.save(mailrecordEntity);
                                }
                                //聚會推送消息
                                String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                                String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                String empNos = user.getLoginName();
                                SendMailUtil sendMailUtil = new SendMailUtil();
                                String sendJuhuiResult = null;
//                                if (relationEntity.getWorkflowid().contains("dzqh_gongnengchengshifabushenqingdan") && "0".equals(status)) {
//                                    sendMailUtil.sendJuihuiMobileApi(mail, "Smack", url, empNos);
//                                }
                                sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos);
                                if ("0".equals(sendJuhuiResult)) {
                                    mailrecordEntity.setSendJuhuiStatus("1");
                                    mailrecordService.save(mailrecordEntity);
                                }
                                if ("Y".equals(conifgEntity.getWhetherApp())) {
                                    String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                                    if (relationEntity.getWorkflowid().contains("dzqh_gongnengchengshifabushenqingdan")) {
                                        sendMailUtil.sendJuihuiMobileApi(mail, "Smack", url, empNos);
                                    } else {
                                        sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos);
                                        if (relationEntity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                                            Object obj = allRelationService.findByDto(relationEntity.getDtoName(), relationEntity.getSerialno());
                                            String applytablename = (String) Reflections.getFieldValue(obj, "applytablename");
                                            mail.setOrdertype(applytablename);
                                        }
                                        /*Dict except=dictService.getUniqueDictByType("dict_exceptType"+mail.getOrderstatus());
                                        sendMailUtil.sendZhiXing(mail,except,empNos,user);*/
                                        if ("Y".equals(user.getNotification())) {
                                            sendMailUtil.sendAppMessageBar(mail, empNos, user);
                                        }
                                    }
                                }
                            }
                        } else {
                            Object obj = allRelationService.findByDto(relationEntity.getDtoName(), relationEntity.getSerialno());
                            Boolean isApp = ftpInfoService.whetherAppSign(relationEntity.getCreateBy());


                            //2是  1否
                            if (isApp && "0".equals(status)) {
                                //2是  1否
                                String sendResult = new SendMailUtil().sendMail(mail);
                                if ("0".equals(sendResult)) {
                                    //發送成功，更新標誌
                                    mailrecordEntity.setSendStatus("1");
                                    mailrecordService.save(mailrecordEntity);
                                }
                                //聚會推送消息
                                String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                                String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                String empNos = user.getLoginName();
                                SendMailUtil sendMailUtil = new SendMailUtil();
                                String sendJuhuiResult = null;
                                sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos);
                                if ("0".equals(sendJuhuiResult)) {
                                    mailrecordEntity.setSendJuhuiStatus("1");
                                    mailrecordService.save(mailrecordEntity);
                                }
                                String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                                url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                sendMailUtil = new SendMailUtil();
                                empNos = user.getLoginName();
                                sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos);
                                if (relationEntity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                                    String applytablename = (String) Reflections.getFieldValue(obj, "applytablename");
                                    mail.setOrdertype(applytablename);
                                }
                                /*Dict except=dictService.getUniqueDictByType("dict_exceptType"+mail.getOrderstatus());
                                sendMailUtil.sendZhiXing(mail,except,empNos,user);*/
                                if ("Y".equals(user.getNotification())) {
                                    sendMailUtil.sendAppMessageBar(mail, empNos, user);
                                }
                            } else {
                                String sendResult = new SendMailUtil().sendMail(mail);
                                if ("0".equals(sendResult)) {
                                    //發送成功，更新標誌
                                    mailrecordEntity.setSendStatus("1");
                                    mailrecordService.save(mailrecordEntity);
                                }
                                //聚會推送消息
                                String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                                String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                String empNos = user.getLoginName();
                                SendMailUtil sendMailUtil = new SendMailUtil();
                                String sendJuhuiResult = null;
                                sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos);
                                if ("0".equals(sendJuhuiResult)) {
                                    mailrecordEntity.setSendJuhuiStatus("1");
                                    mailrecordService.save(mailrecordEntity);
                                }
                            }
                        }
                    }
                } else {
                    //獲取審核人員郵箱，發送郵件信息用
                    taskInfo = processService.currentTaskInfo(config);
                    TaskInfo infoNext = taskInfo.getTaskInfoList().get(0);
                    /**新舊工號兼容優化**/
                    EntityNewAndOld newAndOld = OldJobNoToNewNo.getNewNo(infoNext.getAssignee());
                    /**新舊工號兼容優化**/
                    User user = userService.getUser(newAndOld.getNewEmpno());
                    //查詢是否有代理人
                    List<TProxyUserinfoEntity> audit_prox = proxyUserinfoService.findProxUserByEmpnoNew(newAndOld.getNewEmpno());
                    if (audit_prox != null && audit_prox.size() > 0) {
                        user = userService.getUser(audit_prox.get(0).getSupplyempno());
                    }
//                    WfNodeinfoEntity nodeinfo = wfNodeinfoService.findProxTaskName(info.getTaskName(), relationEntity.getWorkflowid(), relationEntity.getVersion());
//                    relationEntity.setNodename(nodeinfo.getNodealain());
                    allRelationService.update(relationEntity);
                    WfConifgEntity conifgEntity = conifgService.findUnique(relationEntity.getWorkflowid());
                    String validStr = UUID.randomUUID().toString().replace("-", "");

                    Gtasks gtasks = allRelationService.queryMyGtasks(relationEntity.getSerialno(), relationEntity.getDtoName());
                    Mail mail = new Mail();
                    mail.setUsername(gtasks.getMakername());
                    mail.setChargerman(userService.getUser(info.getAssignee()).getName());
                    mail.setSystemname(dictService.getDictByTypeAndVlaue("sys_property", "sys_name").getLabel());
                    mail.setDusername(gtasks.getMakername());
                    mail.setOrdertype(relationEntity.getWfName());
                    mail.setSerialno(serialno);
                    mail.setUrl(dictService.get(560).getValue());
                    mail.setUrlip(dictService.get(561).getValue());
                    if ("0".equals(status)) {
                        //通過
                        mail.setOrderstatus("2");
                        mail.setFreeloginurl(dictService.get(558).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getAction() + "/" + serialno);
                        mail.setFreeloginurlip(dictService.get(559).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getAction() + "/" + serialno);

                    } else {
                        //駁回
                        mail.setOrderstatus("4");
                        mail.setUsername(gtasks.getMakername());
                        mail.setFreeloginurl(dictService.get(558).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getModaction() + "/" + serialno);
                        mail.setFreeloginurlip(dictService.get(559).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getModaction() + "/" + serialno);

                    }
                    //保存發送記錄
                    TPubMailrecordEntity mailrecordEntity = new TPubMailrecordEntity();
                    mailrecordEntity.setChargerman(mail.getChargerman());
                    mailrecordEntity.setDusername(mail.getDusername());
                    mailrecordEntity.setOrderstatus(mail.getOrderstatus());
                    mailrecordEntity.setOrdertype(mail.getOrdertype());
                    mailrecordEntity.setSerialno(mail.getSerialno());
                    mailrecordEntity.setUsername(mail.getUsername());
                    mailrecordEntity.setSendStatus("0");
                    mailrecordEntity.setEmpno(user.getLoginName());
                    mailrecordEntity.setUrl(mail.getUrl());
                    mailrecordEntity.setUrlip(mail.getUrlip());
                    mailrecordEntity.setFreeloginurl(mail.getFreeloginurl());
                    mailrecordEntity.setFreeloginurlip(mail.getFreeloginurlip());
                    mailrecordEntity.setValidStr(validStr);
                    mailrecordService.saveEntity(mailrecordEntity);
                    if (user != null && user.getEmail() != null) {
                        mail.setUsermail(user.getEmail());
                        mailrecordEntity.setUsermail(mail.getUsermail());
                        mailrecordService.saveEntity(mailrecordEntity);
                        String sendResult = new SendMailUtilProx().sendMail(mail, user);
                        if ("0".equals(sendResult)) {
                            //發送成功，更新標誌
                            mailrecordEntity.setSendStatus("1");
                            mailrecordService.saveEntity(mailrecordEntity);
                        }
                        //聚會推送消息
                        String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                        String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                        String empNos = user.getLoginName();
                        SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                        String sendJuhuiResult = null;
//                        if (relationEntity.getWorkflowid().contains("dzqh_gongnengchengshifabushenqingdan")) {
//                            sendMailUtil.sendJuihuiMobileApi(mail, "Smack", url, empNos);
//                        }
                        if ("0".equals(status)) {//只有通過時發送手機聚會消息，駁回只發送郵件通知用戶
                            if ("Y".equals(conifgEntity.getWhetherApp())) {
                                String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                                if ("1".equals(conifgEntity.getAppType())) {
                                    Dict dict2 = new Dict();
                                    dict2.setType("entfrmIpebgIP_app");
                                    dict2.setCodeUniq("entfrmIpebgIP_app01");
                                    String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
                                    mail.setAppAuditUrl(StrUtil.concat(true, fronUrl, conifgEntity.getAppAuditUrl(), "?serialno=", mail.getSerialno()));
                                } else {
                                    mail.setAppAuditUrl(StrUtil.concat(true, conifgEntity.getAppAuditUrl(), "?serialno=", mail.getSerialno()));
                                }
                                if (relationEntity.getWorkflowid().contains("dzqh_gongnengchengshifabushenqingdan")) {
                                    sendMailUtil.sendJuihuiMobileApi(mail, "Smack", url, empNos, user);
                                } else {
                                    sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos, user);
                                    if (relationEntity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                                        Object obj = allRelationService.findByDto(relationEntity.getDtoName(), relationEntity.getSerialno());
                                        String applytablename = (String) Reflections.getFieldValue(obj, "applytablename");
                                        mail.setOrdertype(applytablename);
                                    }
                                }
                                //便易簽裡面的單子通過都發智信
                                Dict except = dictService.getUniqueDictByType("dict_exceptType" + mail.getOrderstatus());
                                sendMailUtil.sendZhiXing(mail, except, empNos, user, "signing");
                                if ("Y".equals(user.getNotification())) {
                                    sendMailUtil.sendAppMessageBar(mail, empNos, user);
                                }
                            }
                            sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos, user);
                            if ("0".equals(sendJuhuiResult)) {
                                mailrecordEntity.setSendJuhuiStatus("1");
                                mailrecordService.saveEntity(mailrecordEntity);
                            }
                        }
                    }
                }
            }
            this.AutoCompleteTask(relationEntity.getProcessid(),relationEntity.getWorkflowid());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return Constant.RESULT.CODE_NO.getValue();
        }
        return Constant.RESULT.CODE_YES.getValue();
    }

    /**
     * 方法描述: 獲取當前節點排序
     *
     * @Author: S6114648
     * @CreateDate: 上午 09:08
     * @Return
     **/

    public String getNodeOrder(String serialno) {
        try {
            TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialno);
            InterConfig config = new InterConfig();
            config.setProcessId(relationEntity.getProcessid());
            //獲取流程當前節點處理人
            InterResult taskInfo = processService.currentTaskInfo(config);
            WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(taskInfo.getTaskInfoList().get(0).getTaskName(), relationEntity.getWorkflowid(), relationEntity.getVersion());
            return entity.getOrderby() + "";
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return "";
        }
    }

    /**
     * 方法描述: 獲取當前簽核節點
     *
     * @Author: S6114648
     * @CreateDate: 2023/5/12  上午 10:16
     * @Return
     **/

    public WfNodeinfoEntity getCurrentNode(String serialno) {
        try {
            TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialno);
            InterConfig config = new InterConfig();
            config.setProcessId(relationEntity.getProcessid());
            //獲取流程當前節點處理人
            InterResult taskInfo = processService.currentTaskInfo(config);
            if (taskInfo != null && "SUCCESS".equals(taskInfo.getStatus()) && taskInfo.getTaskInfoList() != null && taskInfo.getTaskInfoList().size() > 0) {
                WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(taskInfo.getTaskInfoList().get(0).getTaskName(), relationEntity.getWorkflowid(), relationEntity.getVersion());
                entity.setDynfield01(taskInfo.getTaskInfoList().get(0).getAssignee());
                return entity;
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 方法描述: 獲取當前節點審核主管工號
     *
     * @Author: S6114648
     * @CreateDate: 上午 09:08
     * @Return
     **/

    public String getNodeUserNo(String serialno) {
        try {
            TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialno);
            InterConfig config = new InterConfig();
            config.setProcessId(relationEntity.getProcessid());
            //獲取流程當前節點處理人
            InterResult taskInfo = processService.currentTaskInfo(config);
            return taskInfo.getTaskInfoList().get(0).getAssignee() + "";
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return "";
        }
    }


    /**
     * 方法描述: 啟動流程
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/8  上午 10:32
     * @Return
     **/
    @Transactional(readOnly = false)
    public String otherSysProcessStart(WorkFlowEntity obj, Object entity, String sysCode, User currentUser) throws Exception {
        try {
            InterConfig config = new InterConfig();
            //設置工單發啟人
//            config.setApplyUserId("admin");
            config.setApplyUserId(Reflections.getFieldValue(entity, "makerno") == null ? currentUser.getLoginName() : Reflections.getFieldValue(entity, "makerno").toString());
            //設置流程id
            config.setWorkFlowId(obj.getWorkflowId());
            //初始化工單各節點處理人
            config.setTaskUsers(obj.getTaskUsers());
            //設置會簽簽核人信息
            config.setHuiqian(obj.getHuiqian());
            //設置流程整體參數
            String varStr = this.getWfVariables(obj.getWorkflowId());
//            String userGrade = conifgService.callProcWithResult("{CALL p_qh_getUserGrade('?0')}", obj.getEmpNo()).get(0).toString();
            varStr = varStr.replace("$USERGRADE$", "1");
            logger.info(varStr);
            config.setVariables(varStr);
            InterResult interResult = processService.processStart(config);
            //創建會簽節點
            this.createHuiqianTaskInfor(obj, interResult.getProcessId(), entity);

            AutoCompleteTask(interResult.getProcessId(),obj.getWorkflowId());
            //發送郵件
            config = new InterConfig();
            config.setProcessId(interResult.getProcessId());
            config.setWorkFlowId(obj.getWorkflowId());
            InterResult taskInfo = processService.currentTaskInfo(config);
            if ("SUCCESS".equals(taskInfo.getStatus())) {
                TaskInfo info = taskInfo.getTaskInfoList().get(0);
                /**新舊工號兼容優化**/
                EntityNewAndOld newAndOld = OldJobNoToNewNo.getNewNo(info.getAssignee());
                /**新舊工號兼容優化**/
                User user = userService.getUser(newAndOld.getNewEmpno());
                //查詢是否有代理人
                List<TProxyUserinfoEntity> audit_prox = proxyUserinfoService.findProxUserByEmpnoNew(newAndOld.getNewEmpno());
                if (audit_prox != null && audit_prox.size() > 0) {
                    user = userService.getUser(audit_prox.get(0).getSupplyempno());
                }
                WfConifgEntity conifgEntity = conifgService.findUnique(obj.getWorkflowId());
                String validStr = UUID.randomUUID().toString().replace("-", "");
                Gtasks gtasks = allRelationService.queryMyGtasks(obj.getSerialNo(), entity.getClass().getName());
                try {
                    if (user != null && !"".equals(user.getLoginName())) { //无用户信息则不发送邮件
                        Mail mail = new Mail();
                        mail.setChargerman(currentUser.getName());
                        mail.setSystemname(dictService.getUniqueDictByTypeAndCode("other_sys_name", sysCode).getValue());
                        mail.setDusername(gtasks.getMakername());
                        mail.setOrdertype(conifgService.findUnique(obj.getWorkflowId()).getWorkflowname());
                        mail.setSerialno(obj.getSerialNo());
                        mail.setOrderstatus("2");
                        mail.setSystemname(dictService.getUniqueDictByTypeAndCode("other_sys_name", "ODSMS").getValue());
                        mail.setUrl(dictService.getUniqueDictByTypeAndCode("other_sys_url", "ODSMS").getValue());
                        mail.setUrlip(dictService.getUniqueDictByTypeAndCode("other_sys_url_ip_through", "ODSMS").getValue());
                        mail.setFreeloginurl(dictService.getUniqueDictByTypeAndCode("other_sys_url_login", "ODSMS").getValue() + user.getLoginName());
                        mail.setFreeloginurlip(dictService.getUniqueDictByTypeAndCode("other_sys_url_ip", "ODSMS").getValue() + user.getLoginName());
                        //保存發送記錄
                        TPubMailrecordEntity mailrecordEntity = new TPubMailrecordEntity();
                        mailrecordEntity.setChargerman(mail.getChargerman());
                        mailrecordEntity.setDusername(mail.getDusername());
                        mailrecordEntity.setOrderstatus(mail.getOrderstatus());
                        mailrecordEntity.setOrdertype(mail.getOrdertype());
                        mailrecordEntity.setSerialno(mail.getSerialno());
                        mailrecordEntity.setSendStatus("0");
                        mailrecordEntity.setEmpno(user.getLoginName());
                        mailrecordEntity.setUrl(mail.getUrl());
                        mailrecordEntity.setUrlip(mail.getUrlip());
                        mailrecordEntity.setFreeloginurl(mail.getFreeloginurl());
                        mailrecordEntity.setFreeloginurlip(mail.getFreeloginurlip());
                        mailrecordEntity.setValidStr(validStr);
                        mailrecordService.saveForOtherSys(mailrecordEntity, currentUser);
                        if (user != null && user.getEmail() != null) {
                            mail.setUsermail(user.getEmail());
                            mail.setUsername(user.getName());
                            mailrecordEntity.setUsername(mail.getUsername());
                            mailrecordEntity.setUsermail(mail.getUsermail());
                            mailrecordService.saveForOtherSys(mailrecordEntity, currentUser);
                            if ("1".equals(conifgEntity.getAppType())) {
                                Dict dict2 = new Dict();
                                dict2.setType("entfrmIpebgIP_app");
                                dict2.setCodeUniq("entfrmIpebgIP_app01");
                                String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
                                mail.setAppAuditUrl(StrUtil.concat(true, fronUrl, conifgEntity.getAppAuditUrl(), "?serialno=", mail.getSerialno()));
                            } else {
                                mail.setAppAuditUrl(StrUtil.concat(true, conifgEntity.getAppAuditUrl(), "?serialno=", mail.getSerialno()));
                            }
                            Boolean isApp = ftpInfoService.whetherAppSign(currentUser.getLoginName());
                            //2是  1否
                            if (isApp) {
                                //2是  1否
                                String sendResult = new SendMailUtilProx().sendMail(mail, user);
                                if ("0".equals(sendResult)) {
                                    //發送成功，更新標誌
                                    mailrecordEntity.setSendStatus("1");
                                    mailrecordService.saveForOtherSys(mailrecordEntity, currentUser);
                                }
                                //聚會推送消息
                                String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                                String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                String empNos = user.getLoginName();
                                SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                                String sendJuhuiResult = null;
                                sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos, user);
                                if ("0".equals(sendJuhuiResult)) {
                                    mailrecordEntity.setSendJuhuiStatus("1");
                                    mailrecordService.saveForOtherSys(mailrecordEntity, currentUser);
                                }
                                String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                                url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                sendMailUtil = new SendMailUtilProx();
                                empNos = user.getLoginName();
                                sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos, user);
                            } else {
                                String sendResult = new SendMailUtilProx().sendMail(mail, user);
                                if ("0".equals(sendResult)) {
                                    //發送成功，更新標誌
                                    mailrecordEntity.setSendStatus("1");
                                    mailrecordService.saveForOtherSys(mailrecordEntity, currentUser);
                                }
                                //聚會推送消息
                                String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                                String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                                String empNos = user.getLoginName();
                                SendMailUtilProx sendMailUtil = new SendMailUtilProx();
                                String sendJuhuiResult = null;
                                sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos, user);
                                if ("0".equals(sendJuhuiResult)) {
                                    mailrecordEntity.setSendJuhuiStatus("1");
                                    mailrecordService.saveForOtherSys(mailrecordEntity, currentUser);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }
            }
            return interResult.getProcessId();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw e;
        }
    }


    /**
     * 方法描述: 審核
     *
     * @Author: S6114299
     * @CreateDate: 2023/11/07
     * @Return
     **/
    @Transactional(readOnly = false)
    public String completeTaskForODSMS(String serialno, String ip, String status, String attachidsremark, String sysCode, User userInfo, String remarkShow) {
        //status 2 重新提交  3 取消申請  0 通過 1 駁回
        try {
            TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialno);
            Assert.notNull(relationEntity);
            CompleteTaskParameter parameter = new CompleteTaskParameter();
            parameter.setSerialno(serialno);
            parameter.setProcessId(relationEntity.getProcessid());
            parameter.setWorkFlowId(relationEntity.getWorkflowid());
            parameter.setIp(ip);
            parameter.setAuto(false);
            parameter.setAttachidsremark(attachidsremark);

            InterConfig config = new InterConfig();
            config.setProcessId(parameter.getProcessId());
            //獲取流程當前節點處理人
            InterResult taskInfo = processService.currentTaskInfo(config);
            if ("SUCCESS".equals(taskInfo.getStatus())) {
                TaskInfo info = taskInfo.getTaskInfoList().get(0);
                WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(info.getTaskName(), parameter.getWorkFlowId(), relationEntity.getVersion());
                String result = "";
                List<String> proxUser = new ArrayList<>();
                //防止不是自己的單子審核通過
                //TODO 防止不是自己的單子審核通過
                List<TProxyUserinfoEntity> entity_prox = proxyUserinfoService.findProxUserByEmpno(userInfo.getLoginName());
                for (TProxyUserinfoEntity entityProx : entity_prox) {
                    proxUser.add(entityProx.getSuppliedempno());
                }
                /**新舊工號兼容優化**/
                EntityNewAndOld newAndOld = OldJobNoToNewNo.getNewNo(userInfo.getLoginName());
                if (!(newAndOld.getNewEmpno().equals(newAndOld.getOldEmpno()))) {
                    proxUser.add(newAndOld.getOldEmpno());
                    proxUser.add(newAndOld.getNewEmpno());
                } else {
                    proxUser.add(newAndOld.getNewEmpno());
                }
                /**新舊工號兼容優化**/
                if (proxUser.contains(info.getAssignee())) {
                    if ("0".equals(entity.getSigntype())) {
                        result = chargLogService.completeTaskForMobile(parameter, status, userInfo);
                    } else if ("1".equals(entity.getSigntype())) {
                        result = chargLogService.completeHqTaskForMobile(parameter, status, userInfo);
                    }
                    if (result.equals(Constant.RESULT.CODE_NO.getValue())) {
                        return Constant.RESULT.CODE_NO.getValue();
                    } else {
                        try {
                            if (remarkShow != null && !"".equals(remarkShow)) {//審核成功 remarkShow不為空則當前表單為 文檔簽核申請單
                                signModelPositionService.updateRemarkShowType(serialno, entity.getColname(), info.getAssignee(), remarkShow);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                } else {  //移動端超時時會重複調用審核方法 如當前審核人不是登錄人 則單據已審核到下一節點，當前節點已審核成功
                    return Constant.RESULT.CODE_COMPLETE.getValue();
                }
                //如果是完成則更新表單狀態及中間表狀態
                if (result == Constant.RESULT.CODE_COMPLETE.getValue()) {
                    allRelationService.updateFormStaticComplete(serialno, result, relationEntity.getDtoName());
                    relationEntity.setWorkstatus(result);
                    allRelationService.updateEntityForOtherSys(relationEntity, userInfo.getLoginName());

                    WfConifgEntity conifgEntity = conifgService.findUnique(relationEntity.getWorkflowid());
                    String userEmpno = allRelationService.findUserEmpno(relationEntity.getDtoName(), serialno);

                    /**新舊工號兼容優化**/
                    newAndOld = OldJobNoToNewNo.getNewNo(userEmpno);
                    /**新舊工號兼容優化**/
                    User user = userService.getUser(userEmpno);
                    //查詢是否有代理人
                    List<TProxyUserinfoEntity> audit_prox = proxyUserinfoService.findProxUserByEmpnoNew(userEmpno);
                    if (audit_prox != null && audit_prox.size() > 0) {
                        user = userService.getUser(audit_prox.get(0).getSupplyempno());
                    }
                    Gtasks gtasks = allRelationService.queryMyGtasks(relationEntity.getSerialno(), relationEntity.getDtoName());
                    Mail mail = new Mail();
                    mail.setUsername(gtasks.getMakername());
                    mail.setSystemname(dictService.getUniqueDictByTypeAndCode("other_sys_name", sysCode).getValue());
                    mail.setDusername(gtasks.getMakername());
                    mail.setChargerman(userInfo.getName());
                    mail.setOrdertype(relationEntity.getWfName());
                    mail.setSerialno(serialno);
                    mail.setOrderstatus(result);
                    mail.setUrl(dictService.getUniqueDictByTypeAndCode("other_sys_url_login", sysCode).getValue());
                    mail.setUrlip(dictService.getUniqueDictByTypeAndCode("other_sys_url_ip", sysCode).getValue());
                    //保存發送記錄
                    TPubMailrecordEntity mailrecordEntity = new TPubMailrecordEntity();
                    mailrecordEntity.setChargerman(mail.getChargerman());
                    mailrecordEntity.setDusername(mail.getDusername());
                    mailrecordEntity.setOrderstatus(mail.getOrderstatus());
                    mailrecordEntity.setOrdertype(mail.getOrdertype());
                    mailrecordEntity.setSerialno(mail.getSerialno());
                    mailrecordEntity.setUsername(mail.getUsername());
                    mailrecordEntity.setUsermail(mail.getUsermail());
                    mailrecordEntity.setSendStatus("0");
                    mailrecordEntity.setEmpno(user.getLoginName());
                    mailrecordEntity.setUrl(mail.getUrl());
                    mailrecordEntity.setUrlip(mail.getUrlip());
                    mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                    if (user != null && user.getEmail() != null) {
                        mail.setUsermail(user.getEmail());
                        mailrecordEntity.setUsermail(mail.getUsermail());
                        mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                        String sendResult = "";
                        if (relationEntity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan")) {
                            try {
                                Object obj = allRelationService.findByDto(relationEntity.getDtoName(), relationEntity.getSerialno());
                                TPubFileobjectEntity fileobjectEntity = fileobjectService.findByIdIgnoreComma(Reflections.getFieldValue(obj, "attachids2") + "");
                                //由於郵箱接收附件有8M限制，大於限制不發送附件
                                if (ObjectUtil.isNotNull(fileobjectEntity)) {
                                    if (fileobjectEntity.getSizez() > 8388608) {
                                        sendResult = new SendMailUtil().sendMail(mail);
                                    } else {
                                        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//                                        HttpUtil.download("http://localhost:8081/newEsign/entrfrm/fileSignDownloadPdfAddImg222/" + fileobjectEntity.getId() + "?empNo=" + fileobjectEntity.getCreateBy(), outputStream, true);
                                        HttpUtil.download(dictService.getUniqueDictByTypeAndCode("other_sys_url", sysCode).getValue() + Global.getConfig("sendMailAuditPath") + "/entrfrm/fileSignDownloadPdfAddImg222/" + fileobjectEntity.getId() + "?empNo=" + fileobjectEntity.getCreateBy(), outputStream, true);
                                        mail.setFile(FileUtil.readBytes(new ByteArrayInputStream(outputStream.toByteArray()), fileobjectEntity.getName()));
                                        sendResult = SendDocMailUtil.sendMail(mail);
                                    }
                                } else {
                                    sendResult = new SendMailUtil().sendMail(mail);
                                }
                            } catch (Exception e) {
                                logger.info("發送附件文檔出錯,serialno:" + relationEntity.getSerialno(), e);
                            }
                        } else {
                            sendResult = new SendMailUtil().sendMail(mail);
                        }
                        if ("0".equals(sendResult)) {
                            //發送成功，更新標誌
                            mailrecordEntity.setSendStatus("1");
                            mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                        }
                        //聚會推送消息
                        SendMailUtil sendMailUtil = new SendMailUtil();
                        String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                        String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                        String empNos = user.getLoginName();
                        String sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos);
                        if ("0".equals(sendJuhuiResult)) {
                            mailrecordEntity.setSendJuhuiStatus("1");
                            mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                        }
                        if ("Y".equals(conifgEntity.getWhetherApp())) {
                            String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                            if ("1".equals(conifgEntity.getAppType())) {
                                Dict dict2 = new Dict();
                                dict2.setType("entfrmIpebgIP_app");
                                dict2.setCodeUniq("entfrmIpebgIP_app01");
                                String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
                                mail.setAppAuditUrl(StrUtil.concat(true, fronUrl, conifgEntity.getAppDetailUrl(), "?serialno=", mail.getSerialno()));
                            } else {
                                mail.setAppAuditUrl(StrUtil.concat(true, conifgEntity.getAppDetailUrl(), "?serialno=", mail.getSerialno()));
                            }
                            if (relationEntity.getWorkflowid().contains("dzqh_gongnengchengshifabushenqingdan")) {
                                sendMailUtil.sendJuihuiMobileApi(mail, "Smack", url, empNos);
                            } else {
                                sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos);
                            }
                        }
                    }
                } else {
                    //獲取審核人員郵箱，發送郵件信息用
                    taskInfo = processService.currentTaskInfo(config);
                    info = taskInfo.getTaskInfoList().get(0);

                    /**新舊工號兼容優化**/
                    newAndOld = OldJobNoToNewNo.getNewNo(info.getAssignee());
                    /**新舊工號兼容優化**/
                    User user = userService.getUser(newAndOld.getNewEmpno());
                    //查詢是否有代理人
                    List<TProxyUserinfoEntity> audit_prox = proxyUserinfoService.findProxUserByEmpnoNew(newAndOld.getNewEmpno());
                    if (audit_prox != null && audit_prox.size() > 0) {
                        user = userService.getUser(audit_prox.get(0).getSupplyempno());
                    }
//                    WfNodeinfoEntity nodeinfo = wfNodeinfoService.findProxTaskName(info.getTaskName(), relationEntity.getWorkflowid(), relationEntity.getVersion());
//                    relationEntity.setNodename(nodeinfo.getNodealain());
                    allRelationService.updateEntityForOtherSys(relationEntity, user.getLoginName());
                    WfConifgEntity conifgEntity = conifgService.findUnique(relationEntity.getWorkflowid());
                    String validStr = UUID.randomUUID().toString().replace("-", "");
                    Gtasks gtasks = allRelationService.queryMyGtasks(relationEntity.getSerialno(), relationEntity.getDtoName());
                    //查詢用戶信息
                    TQhUserformhsEntity userformhsEntity = tQhUserformhsService.findByEmpnoIgnoreIdStatus(user.getLoginName());
                    Mail mail = new Mail();
                    mail.setChargerman(userInfo.getName());
                    mail.setSystemname(dictService.getUniqueDictByTypeAndCode("other_sys_name", sysCode).getValue());
                    mail.setDusername(gtasks.getMakername());
                    mail.setOrdertype(relationEntity.getWfName());
                    mail.setSerialno(serialno);
                    mail.setUrl(dictService.getUniqueDictByTypeAndCode("other_sys_url_login", sysCode).getValue());
                    mail.setUrlip(dictService.getUniqueDictByTypeAndCode("other_sys_url_ip", sysCode).getValue());
                    if (user != null && user.getEmail() != null) {
                        mail.setUsermail(user.getEmail());
                    }
                    if ("0".equals(status)) {
                        //通過
                        mail.setOrderstatus("2");
                        mail.setUsername(userformhsEntity == null ? "" : userformhsEntity.getEmpname());
                        mail.setFreeloginurl(dictService.getUniqueDictByTypeAndCode("other_sys_url", sysCode).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getAction() + "/" + serialno);
                        mail.setFreeloginurlip(dictService.getUniqueDictByTypeAndCode("other_sys_url_ip_through", sysCode).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getAction() + "/" + serialno);
                    } else {
                        //駁回
                        mail.setOrderstatus("4");
                        mail.setUsername(gtasks.getMakername() != null ? gtasks.getMakername() : userformhsEntity.getEmpname());
                        mail.setFreeloginurl(dictService.getUniqueDictByTypeAndCode("other_sys_url", sysCode).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getModaction() + "/" + serialno);
                        mail.setFreeloginurlip(dictService.getUniqueDictByTypeAndCode("other_sys_url_ip_through", sysCode).getValue() + Global.getConfig("sendMailAuditPath") + "/audit/login?username=" + user.getLoginName() + "&loginType=1&utoken=" + validStr + "&url=/" + conifgEntity.getModaction() + "/" + serialno);
                    }
                    //保存發送記錄
                    TPubMailrecordEntity mailrecordEntity = new TPubMailrecordEntity();
                    mailrecordEntity.setChargerman(mail.getChargerman());
                    mailrecordEntity.setDusername(mail.getDusername());
                    mailrecordEntity.setOrderstatus(mail.getOrderstatus());
                    mailrecordEntity.setOrdertype(mail.getOrdertype());
                    mailrecordEntity.setSerialno(mail.getSerialno());
                    mailrecordEntity.setUsername(mail.getUsername());
                    mailrecordEntity.setSendStatus("0");
                    mailrecordEntity.setEmpno(user.getLoginName());
                    mailrecordEntity.setUrl(mail.getUrl());
                    mailrecordEntity.setUrlip(mail.getUrlip());
                    mailrecordEntity.setFreeloginurl(mail.getFreeloginurl());
                    mailrecordEntity.setFreeloginurlip(mail.getFreeloginurlip());
                    mailrecordEntity.setValidStr(validStr);
                    mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                    if ("1".equals(conifgEntity.getAppType())) {
                        Dict dict2 = new Dict();
                        dict2.setType("entfrmIpebgIP_app");
                        dict2.setCodeUniq("entfrmIpebgIP_app01");
                        String fronUrl = dictService.getUniqueDictByTypeAndCode(dict2).getValue();
                        mail.setAppAuditUrl(StrUtil.concat(true, fronUrl, conifgEntity.getAppAuditUrl(), "?serialno=", mail.getSerialno()));
                    } else {
                        mail.setAppAuditUrl(StrUtil.concat(true, conifgEntity.getAppAuditUrl(), "?serialno=", mail.getSerialno()));
                    }
                    if (user != null && user.getEmail() != null && (!relationEntity.getWorkflowid().contains("dzqh_wendangqianheshenqingdan"))) {
                        mail.setUsermail(user.getEmail());
                        mailrecordEntity.setUsermail(mail.getUsermail());
                        mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                        if (!"0".equals(user.getEmailset())) {
                            String sendResult = new SendMailUtil().sendMail(mail);
                            if ("0".equals(sendResult)) {
                                //發送成功，更新標誌
                                mailrecordEntity.setSendStatus("1");
                                mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                            }
                            //聚會推送消息
                            String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                            String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                            String empNos = user.getLoginName();
                            SendMailUtil sendMailUtil = new SendMailUtil();
                            String sendJuhuiResult = null;
                            if ("0".equals(status)) {//只有通過時發送手機聚會消息，駁回只發送郵件通知用戶
                                sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos);
                                if ("0".equals(sendJuhuiResult)) {
                                    mailrecordEntity.setSendJuhuiStatus("1");
                                    mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                                }
                                if ("Y".equals(conifgEntity.getWhetherApp())) {
                                    String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                                    if (relationEntity.getWorkflowid().contains("dzqh_gongnengchengshifabushenqingdan")) {
                                        sendMailUtil.sendJuihuiMobileApi(mail, "Smack", url, empNos);
                                        sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos);
                                    } else {
                                        sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos);

                                    }
                                }
                            }
                        }
                    } else {
                        Boolean isApp = ftpInfoService.whetherAppSign(relationEntity.getCreateBy());
                        if (isApp && "0".equals(status)) {
                            String sendResult = new SendMailUtil().sendMail(mail);
                            if ("0".equals(sendResult)) {
                                //發送成功，更新標誌
                                mailrecordEntity.setSendStatus("1");
                                mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                            }
                            //聚會推送消息
                            String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                            String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                            String empNos = user.getLoginName();
                            SendMailUtil sendMailUtil = new SendMailUtil();
                            String sendJuhuiResult = null;
                            sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos);
                            if ("0".equals(sendJuhuiResult)) {
                                mailrecordEntity.setSendJuhuiStatus("1");
                                mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                            }
                            String typeApp = dictService.getUniqueDictByType("dict_sendJuhuiTypeOfApp").getValue();
                            url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                            sendMailUtil = new SendMailUtil();
                            empNos = user.getLoginName();
                            sendMailUtil.sendJuihuiApi(mail, typeApp, url, empNos);
                        } else {
                            String sendResult = new SendMailUtil().sendMail(mail);
                            if ("0".equals(sendResult)) {
                                //發送成功，更新標誌
                                mailrecordEntity.setSendStatus("1");
                                mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                            }
                            //聚會推送消息
                            String type = dictService.getUniqueDictByType("dict_sendJuhuiType").getValue();
                            String url = dictService.getUniqueDictByType("dict_sendJuhuiUrl").getValue();
                            String empNos = user.getLoginName();
                            SendMailUtil sendMailUtil = new SendMailUtil();
                            String sendJuhuiResult = null;
                            sendJuhuiResult = sendMailUtil.sendJuihui(mail, type, url, empNos);
                            if ("0".equals(sendJuhuiResult)) {
                                mailrecordEntity.setSendJuhuiStatus("1");
                                mailrecordService.saveForMobile(mailrecordEntity, userInfo.getLoginName());
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return Constant.RESULT.CODE_NO.getValue();
        }
        return Constant.RESULT.CODE_YES.getValue();
    }
}
