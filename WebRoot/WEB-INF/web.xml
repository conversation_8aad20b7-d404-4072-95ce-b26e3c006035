<?xml version="1.1" encoding="UTF-8"?>
<web-app version="3.0"
         xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd">
    <display-name>foxconn</display-name>

    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>
            classpath*:/applicationContext.xml,
            classpath*:/applicationContext-shiro.xml
        </param-value>
    </context-param>
    <context-param>
        <param-name>spring.profiles.default</param-name>
        <param-value>production</param-value>
    </context-param>
    <!--spring ApplicationContext 载入 -->
    <listener>
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
    </listener>
    <!-- 编码  filter -->
    <filter>
        <filter-name>encodingFilter</filter-name>
        <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
        <init-param>
            <param-name>forceEncoding</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>encodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <!-- finder集成 -->
    <filter>
        <filter-name>SessionFilter</filter-name>
        <filter-class>com.skin.finder.filter.SessionFilter</filter-class>
        <init-param>
            <param-name>loginUrl</param-name>
            <param-value>/finder?action=finder.login</param-value>
        </init-param>
    </filter>

    <filter-mapping>
        <filter-name>SessionFilter</filter-name>
        <url-pattern>/finder</url-pattern>
    </filter-mapping>
    <!-- finder集成 -->
    <!-- Filter 定义 -->
    <!-- session filter -->
    <filter>
        <filter-name>springSessionRepositoryFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>springSessionRepositoryFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <!-- Hibernate Open Session In View filter-->
    <filter>
        <filter-name>openSessionInViewFilter</filter-name>
        <filter-class>org.springframework.orm.hibernate4.support.OpenSessionInViewFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>openSessionInViewFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- ajax filter -->
    <filter>
        <filter-name>ajaxFilter</filter-name>
        <filter-class>com.foxconn.ipebg.system.utils.AjaxFilter</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>ajaxFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- shiro filter -->
    <filter>
        <filter-name>shiroFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
        <init-param>
            <param-name>targetFilterLifecycle</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>shiroFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <servlet>
        <servlet-name>DispatchServlet</servlet-name>
        <servlet-class>com.skin.finder.web.servlet.DispatchServlet</servlet-class>
        <load-on-startup>0</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>DispatchServlet</servlet-name>
        <url-pattern>/finder</url-pattern>
    </servlet-mapping>

    <!-- spring mvc Servlet -->
    <servlet>
        <servlet-name>springServlet</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>classpath*:/spring-mvc.xml</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>springServlet</servlet-name>
        <url-pattern>/</url-pattern>
    </servlet-mapping>

    <!-- druid 监控 -->
<!--    <servlet>-->
<!--        <servlet-name>DruidStatView</servlet-name>-->
<!--        <servlet-class>com.alibaba.druid.support.http.StatViewServlet</servlet-class>-->
<!--        <init-param>-->
<!--            &lt;!&ndash; 用户名 &ndash;&gt;-->
<!--            <param-name>loginUsername</param-name>-->
<!--            <param-value>druid</param-value>-->
<!--        </init-param>-->
<!--        <init-param>-->
<!--            &lt;!&ndash; 密码 &ndash;&gt;-->
<!--            <param-name>loginPassword</param-name>-->
<!--            <param-value>Druid1234</param-value>-->
<!--        </init-param>-->
<!--    </servlet>-->
<!--    <servlet-mapping>-->
<!--        <servlet-name>DruidStatView</servlet-name>-->
<!--        <url-pattern>/druid/*</url-pattern>-->
<!--    </servlet-mapping>-->

    <!--

    <servlet>
        <display-name>HighChartsUtil</display-name>
        <servlet-name>HighChartsUtil</servlet-name>
        <servlet-class>com.ty.tianyu.common.utils.HighChartsUtil</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>HighChartsUtil</servlet-name>
        <url-pattern>/HighChartsUtil</url-pattern>
    </servlet-mapping> -->

    <!-- kaptcha验证码 -->
    <servlet>
        <servlet-name>kaptcha</servlet-name>
        <servlet-class>com.google.code.kaptcha.servlet.KaptchaServlet</servlet-class>
        <init-param>
            <param-name>kaptcha.textproducer.char.string</param-name>
            <param-value>0123456789</param-value>
        </init-param>
        <init-param>
            <param-name>kaptcha.textproducer.char.length</param-name>
            <param-value>4</param-value>
        </init-param>
    </servlet>
    <servlet-mapping>
        <servlet-name>kaptcha</servlet-name>
        <url-pattern>/static/images/kaptcha.jpg</url-pattern>
    </servlet-mapping>

    <!-- session过期时间 -->
    <session-config>
        <session-timeout>20</session-timeout>
    </session-config>

    <!-- 错误页面设置 -->
    <error-page>
        <exception-type>java.lang.Throwable</exception-type>
        <location>/WEB-INF/views/error/500.jsp</location>
    </error-page>
    <error-page>
        <error-code>500</error-code>
        <location>/WEB-INF/views/error/500.jsp</location>
    </error-page>
    <error-page>
        <error-code>404</error-code>
        <location>/WEB-INF/views/error/404.jsp</location>
    </error-page>
    <error-page>
        <error-code>403</error-code>
        <location>/WEB-INF/views/error/403.jsp</location>
    </error-page>

    <!--&lt;!&ndash; welcome file list &ndash;&gt;-->
    <!--<welcome-file-list>-->
        <!--<welcome-file>index.html</welcome-file>-->
    <!--</welcome-file-list>-->
</web-app>
