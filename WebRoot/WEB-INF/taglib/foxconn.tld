<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<taglib xmlns="http://java.sun.com/xml/ns/j2ee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	version="2.0"
	xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-jsptaglibrary_2_0.xsd">
	<description><![CDATA["check user authment."]]></description>
	<display-name>Auto Tags</display-name>
	<tlib-version>1.0</tlib-version>
	<short-name>fox</short-name>
	<uri>/foxconn-tags</uri>

	<tag>
	   <name>action</name>
	   <tag-class>com.foxconn.ipebg.common.taglib.ActionTag</tag-class>
		<body-content>empty</body-content>
		<attribute>
			<name>perCall</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cssClass</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cssStyle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>blankNum</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>serialNo</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dataOptions</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>callback</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>systemName</name>
		<tag-class>com.foxconn.ipebg.common.taglib.SystemNameTag</tag-class>
		<body-content>empty</body-content>
		<attribute>
			<name>sysName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<name>copyRight</name>
		<tag-class>com.foxconn.ipebg.common.taglib.CopyRightTag</tag-class>
		<body-content>empty</body-content>
		<attribute>
			<name>copyRight</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<!-- 标签名 -->
		<name>hasPermssion</name>
		<!-- 标签对应的类所在路径 -->
		<tag-class>com.foxconn.ipebg.common.taglib.HasPermssionTag</tag-class>
		<body-content>JSP</body-content>
<!--		<description>has any permission is ok</description>-->
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
	<tag>
		<!-- 标签名 -->
		<name>hasNotPermssion</name>
		<!-- 标签对应的类所在路径 -->
		<tag-class>com.foxconn.ipebg.common.taglib.NotHasPermssionTag</tag-class>
		<body-content>JSP</body-content>
		<!--		<description>has any permission is ok</description>-->
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
	</tag>
</taglib>
