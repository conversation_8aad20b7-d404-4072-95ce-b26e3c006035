<%@ page contentType="text/html;charset=UTF-8" isErrorPage="true" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page import="org.slf4j.Logger,org.slf4j.LoggerFactory" %>
<%response.setStatus(200);%>

<%
    Throwable ex = null;
    if (exception != null)
        ex = exception;
    if (request.getAttribute("javax.servlet.error.exception") != null)
        ex = (Throwable) request.getAttribute("javax.servlet.error.exception");

    //记录日志
    Logger logger = LoggerFactory.getLogger("500.jsp");
    logger.error(ex.getMessage(), ex);
%>

<!DOCTYPE html>
<html>
<head>
    <title>500 - 系统内部错误</title>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>

<body>
<div align="center">
    <h3>系统发生内部错误，請聯繫管理員</h3>
    <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
       data-options="iconCls:'icon-cancel'"
       style="width: 100px;" onclick="closeCurrentTabNoRefesh();">關閉</a>
</div>
</body>
</html>
