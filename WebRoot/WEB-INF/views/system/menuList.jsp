<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title></title>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body style="font-family: '微软雅黑'">
<div id="tb" style="padding:5px;height:auto">
    <div>
        <shiro:hasPermission name="sys:perm:add">
            <a href="#" class="easyui-linkbutton" plain="true" iconCls="icon-add" onclick="add();">添加</a>
            <span class="toolbar-item dialog-tool-separator"></span>
        </shiro:hasPermission>
        <shiro:hasPermission name="sys:perm:delete">
            <a href="#" class="easyui-linkbutton" plain="true" iconCls="icon-remove" onclick="del()">删除</a>
            <span class="toolbar-item dialog-tool-separator"></span>
        </shiro:hasPermission>
        <shiro:hasPermission name="sys:perm:update">
            <a href="#" class="easyui-linkbutton" plain="true" iconCls="icon-edit" onclick="upd()">修改</a>
        </shiro:hasPermission>
    </div>
</div>
<table id="dg"></table>

<div id="dlg"></div>
<div id="icon_dlg"></div>
<div id="menu" class="easyui-menu" style="width: 30px; display: none;">
    <shiro:hasPermission name="sys:perm:add">
        <div id="btn_Add" data-options="iconCls:'icon-add'" onclick="add();">添加</div>
    </shiro:hasPermission>
    <shiro:hasPermission name="sys:perm:update">
        <div id="btn_Modify" data-options="iconCls:'icon-edit'" onclick="upd()">编辑</div>
    </shiro:hasPermission>
    <!--放置一个隐藏的菜单Div-->
    <shiro:hasPermission name="sys:perm:delete">
        <div id="btn_Delete" data-options="iconCls:'icon-remove'" οnclick="del()">删除</div>
    </shiro:hasPermission>
    <!--具体的菜单事件请自行添加，跟toolbar的方法是基本一样的-->
</div>
<script type="text/javascript">
    var dg;
    var d;
    var permissionDg;
    var parentPermId;
    $(function () {
        dg = $('#dg').treegrid({
            method: "get",
            url: '${ctx}/system/permission/menu/json',
            fit: true,
            fitColumns: true,
            cache: false,  //关闭AJAX相应的缓存
            border: false,
            idField: 'id',
            treeField: 'name',
            parentField: 'pid',
            iconCls: 'icon',
            checkbox:true,
//            animate: true,
            rownumbers: true,
            singleSelect: true,
//            striped: true,
            columns: [[
                {field: 'id', title: 'id', hidden: true, width: 100},
                {field: 'name', title: '名称', width: 100},
                {field: 'url', title: '资源路径', width: 100},
                {field: 'sort', title: '排序'},
                {field: 'description', title: '描述', width: 100}
            ]],
            contextMenu: function (e, rowIndex) { //右键时触发事件
                //三个参数：e里面的内容很多，真心不明白，rowIndex就是当前点击时所在行的索引，rowData当前行的数据
                e.preventDefault(); //阻止浏览器捕获右键事件
                $(this).datagrid("clearSelections"); //取消所有选中项
                $(this).datagrid("selectRow", rowIndex.id); //根据索引选中该行
                $('#menu').menu('show', {
                    //显示右键菜单
                    left: e.pageX,//在鼠标点击处显示菜单
                    top: e.pageY
                });
                e.preventDefault();  //阻止浏览器自带的右键菜单弹出
            },
            enableHeaderClickMenu: false,
            enableHeaderContextMenu: false,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });

    });
    //弹窗增加
    function add() {
        //父级权限
        var row = dg.treegrid('getSelected');
        if (row) {
            parentPermId = row.id;
        }
        d = $('#dlg').dialog({
            title: '添加菜单',
            width: 450,
            height: 320,
            closed: false,
            cache: false,
            maximizable: true,
            resizable: true,
            href: '${ctx}/system/permission/menu/create',
            modal: true,
            buttons: [{
                text: '确认',
                handler: function () {
                    $("#mainform").submit();
                }
            }, {
                text: '取消',
                handler: function () {
                    d.panel('close');
                }
            }]
        });
    }

    //删除
    function del() {
        var row = dg.treegrid('getSelected');
        if (rowIsNull(row)) return;
        parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
            if (data) {
                $.ajax({
                    type: 'get',
                    url: "${ctx}/system/permission/delete/" + row.id,
                    success: function (data) {
                        if (successTip(data, dg))
                            dg.treegrid('reload');
                    }
                });
                //dg.datagrid('reload'); //grid移除一行,不需要再刷新
            }
        });

    }

    //修改
    function upd() {
        var row = dg.treegrid('getSelected');
        if (rowIsNull(row)) return;
        //父级权限
        parentPermId = row.pid;
        d = $("#dlg").dialog({
            title: '修改菜单',
            width: 450,
            height: 320,
            href: '${ctx}/system/permission/menu/update/' + row.id,
            maximizable: true,
            modal: true,
            buttons: [{
                text: '确认',
                handler: function () {
                    $("#mainform").submit();
                }
            }, {
                text: '取消',
                handler: function () {
                    d.panel('close');
                }
            }]
        });

    }

    var nowIcon;
    var icon_dlg;
</script>
</body>
</html>