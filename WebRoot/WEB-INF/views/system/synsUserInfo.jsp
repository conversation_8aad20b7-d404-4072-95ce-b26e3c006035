<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title></title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .tabs {
            width: 100%;
        }
        .tabs-scroller-left, .tabs-scroller-right {
            width: 0px;
            display: none;
        }
    </style>
</head>
<body>
<div id="tt" class="easyui-tabs">
    <div id="tb1" style="height:auto" title="通用拋轉中央網通" data-options="refreshable: false">
        <input type="text" name="serialno" id="serialno" class="easyui-validatebox"
               data-options="width:150,prompt: '任務編號'"/>
        <a href="javascript(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true" onclick="pz()">拋轉</a>
    </div>
    <div id="tb" style="height:auto" title="同步人事信息" data-options="refreshable: false">
        <input type="text" name="empno" id="empno" class="easyui-validatebox"
               data-options="width:150,prompt: '工號'"/>
        <a href="javascript(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true" onclick="cx()">執行</a>

        <input type="text" name="dataStr" id="dataStr" class="easyui-validatebox Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"
               data-options="width:200,prompt: '只同步一天的數據'"/>
        <a href="javascript(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true" onclick="cxdatestr()">執行</a>
    </div>
	<div id="tb5" style="height:auto" title="同步台幹信息" data-options="refreshable: false">
        <input type="text" name="empno2" id="empno2" class="easyui-validatebox"
               data-options="width:150,prompt: '工號'"/>
        <a href="javascript(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true" onclick="tbtwg()">執行</a>
    </div>
    <div id="tb2" style="height:auto" title="同步組織信息" data-options="refreshable: false">
        <input type="text" name="deptno" id="deptno" class="easyui-validatebox"
               data-options="width:150,prompt: '部門代碼'"/>
        <input type="text" name="siteCode" id="siteCode" class="easyui-validatebox"
               data-options="width:150,prompt: 'siteID'"/>
        <a href="javascript(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true"
           onclick="tborg()">執行</a>
    </div>
    <div id="tb11" style="height:auto" title="拋轉tiptop表單" data-options="refreshable: false">
        <input type="text" name="empno11" id="empno11" class="easyui-validatebox"
               data-options="width:150,prompt: '表单编号'"/>
        <a href="javascript(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true" onclick="cx11()">執行</a>

    </div>
    <%--<div id="tb12" style="height:auto" title="拋轉獎懲表單" data-options="refreshable: false">
        <input type="text" name="empno12" id="empno12" class="easyui-validatebox"
               data-options="width:150,prompt: '表单编号'"/>
        <a href="javascript(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true" onclick="cx12()">執行</a>
    </div>--%>
    <div id="tb13" style="height:auto" title="拋轉資安表單" data-options="refreshable: false">
        <input type="text" name="empno12" id="empno13" class="easyui-validatebox"
               data-options="width:150,prompt: '表单编号'"/>
        <a href="javascript(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true" onclick="cx13()">執行</a>
    </div>
    <div id="tb14" style="height:auto" title="拋轉運維表單" data-options="refreshable: false">
        <input type="text" name="empno12" id="empno14" class="easyui-validatebox"
               data-options="width:150,prompt: '表单编号'"/>
        <a href="javascript(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true" onclick="cx14()">執行</a>
    </div>
    <div id="tb15" style="height:auto" title="拋轉禮金/複職表單" data-options="refreshable: false">
        <input type="text" name="empno15" id="empno15" class="easyui-validatebox"
               data-options="width:150,prompt: '表单编号'"/>
        <a href="javascript(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true" onclick="cx15()">執行</a>
    </div>
    <div id="tb16" style="height:auto" title="拋轉e通關" data-options="refreshable: false">
        <input type="text" name="empno16" id="empno16" class="easyui-validatebox"
               data-options="width:150,prompt: '表单编号'"/>
        <a href="javascript(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true" onclick="cx16()">執行</a>
    </div>
    <div id="tb17" style="height:auto" title="拋轉專案管理系統" data-options="refreshable: false">
        <input type="text" name="empno17" id="empno17" class="easyui-validatebox"
               data-options="width:150,prompt: '表单编号'"/>
        <a href="javascript(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true" onclick="cx17()">執行</a>
    </div>
    <div id="tb18" style="height:auto" title="拋轉項目管理系統" data-options="refreshable: false">
        <input type="text" name="empno18" id="empno18" class="easyui-validatebox"
               data-options="width:150,prompt: '表单编号'"/>
        <a href="javascript(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true" onclick="cx18()">執行</a>
    </div>
    <div id="tb3" style="height:auto" title="批量結束個人待辦" data-options="refreshable: false">
        <input type="text" name="empno_new" id="empno_new" class="easyui-validatebox"
               data-options="width:150,prompt: '工號'"/>
        <a href="javascript(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true"
           onclick="endByEmpno()">執行</a>
    </div>
    <div id="tb4" style="height:auto" title="補傳附件" data-options="refreshable: false">
        <input type="hidden" id="attachids" name="attachids" value=""/>
        <input type="text" name="serialno_new" id="serialno_new" class="easyui-validatebox"
               data-options="width:150,prompt: '任務編號'"/>
        <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile();"
               class="ui-input-file"/>
        <a href="javascript(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true"
           onclick="confire()">確認</a>
        <div id="dowloadUrl">
        </div>
    </div>
    <div id="tb41" style="height:auto" title="查詢是否一賬通帳號" data-options="refreshable: false">
        <input type="text" name="is_empno" id="is_empno" class="easyui-validatebox"
               data-options="width:150,prompt: '工號'"/>
        <a href="javascript(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true"
           onclick="is_empno()">確認</a>
    </div>
    <%--<div id="tb6" style="height:auto" title="P-Talk/賦能柜數據拋磚" data-options="refreshable: false">
        <input type="text" name="serialno_ptalk" id="serialno_ptalk" class="easyui-validatebox"
               data-options="width:150,prompt: '任務編號'"/>
        <a href="javascript(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true"
           onclick="pz_ptalk()">拋轉</a>
    </div>--%>
    <div id="tb19" style="height:auto" title="拋運維" data-options="refreshable: false">
        <input type="text" name="empno19" id="empno19" class="easyui-validatebox"
               data-options="width:150,prompt: '表单编号'"/>
        <a href="javascript(0)" class="easyui-linkbutton" iconCls="icon-search" plain="true" onclick="cx19()">執行</a>
    </div>
</div>
<table id="dg"></table>
<div id="dlg"></div>
<script type="text/javascript">
    //创建查询对象并查询
    function cx() {
        var empno = $("#empno").val();
        if (empno == null || empno == undefined || empno == 'undefined') {
            $.messager.alert("溫馨提示", "請填寫工號！", "info");
            return false;
        }
        $.ajax({
            type: 'GET',
            beforeSend: ajaxLoading,
            url: "${ctx}/system/scheduleJob/synsUserInfoByEmpno?empno=" + empno,
            success: function (data) {
                $("#empno").val('');
                ajaxLoadEnd();
                successTip(data);
                // window.parent.mainpage.mainTabs.refCurrentTab();
            },
            error:function(date){
                ajaxLoadEnd();
            }
        });
    }
    function cx11() {
        var empno = $("#empno11").val();
        if (empno == null || empno == undefined || empno == 'undefined') {
            $.messager.alert("溫馨提示", "請填寫表单编号！", "info");
            return false;
        }
        $.ajax({
            type: 'GET',
            beforeSend: ajaxLoading,
            url: "${ctx}/tiptopprocess/rePao?serialno=" + empno,
            success: function (data) {
                $("#empno11").val('');
                ajaxLoadEnd();
                successTip(data);
                // window.parent.mainpage.mainTabs.refCurrentTab();
            },
            error:function(date){
                ajaxLoadEnd();
            }
        });
    }
    function cx12() {
        var empno = $("#empno12").val();
        if (empno == null || empno == undefined || empno == 'undefined') {
            $.messager.alert("溫馨提示", "請填寫表单编号！", "info");
            return false;
        }
        $.ajax({
            type: 'GET',
            beforeSend: ajaxLoading,
            url: "${ctx}/wfilegalprocesses/rePao?serialno=" + empno,
            success: function (data) {
                $("#empno12").val('');
                ajaxLoadEnd();
                successTip(data);
                // window.parent.mainpage.mainTabs.refCurrentTab();
            },
            error:function(date){
                ajaxLoadEnd();
            }
        });
    }
    function cx13() {
        var empno = $("#empno13").val();
        var type = 'zian';
        if (empno == null || empno == undefined || empno == 'undefined') {
            $.messager.alert("溫馨提示", "請填寫表单编号！", "info");
            return false;
        }
        $.ajax({
            type: 'GET',
            beforeSend: ajaxLoading,
            url: "${ctx}/wfinternetemailprocess/rePao?serialno=" + empno+"&&type="+type,
            success: function (data) {
                $("#empno13").val('');
                ajaxLoadEnd();
                successTip(data);
                // window.parent.mainpage.mainTabs.refCurrentTab();
            },
            error:function(date){
                ajaxLoadEnd();
            }
        });
    }
    function cx14() {
        var empno = $("#empno14").val();
        var type="zixun";
        if (empno == "" ||empno == null || empno == undefined || empno == 'undefined') {
            $.messager.alert("溫馨提示", "請填寫表单编号！", "info");
            return false;
        }
        $.ajax({
            type: 'GET',
            beforeSend: ajaxLoading,
            url: "${ctx}/wfinternetemailprocess/rePao?serialno=" + empno+"&&type="+type,
            success: function (data) {
                $("#empno14").val('');
                ajaxLoadEnd();
                successTip(data);
                // window.parent.mainpage.mainTabs.refCurrentTab();
            },
            error:function(date){
                ajaxLoadEnd();
            }
        });
    }
    function cx15() {
        var empno = $("#empno15").val();
        if (empno == "" ||empno == null || empno == undefined || empno == 'undefined') {
            $.messager.alert("溫馨提示", "請填寫表单编号！", "info");
            return false;
        }
        $.ajax({
            type: 'GET',
            beforeSend: ajaxLoading,
            url: "${ctx}/wfmarryprocesses/rePao?serialno=" + empno,
            success: function (data) {
                $("#empno15").val('');
                ajaxLoadEnd();
                successTip(data);
                // window.parent.mainpage.mainTabs.refCurrentTab();
            },
            error: function (date) {
                ajaxLoadEnd();
            }
        });
    }

    function cx16() {
        var empno = $("#empno16").val();
        if (empno == "" || empno == null || empno == undefined || empno == 'undefined') {
            $.messager.alert("溫馨提示", "請填寫表单编号！", "info");
            return false;
        }
        $.ajax({
            type: 'GET',
            beforeSend: ajaxLoading,
            url: "${ctx}/wfilegalprocesses/rePaoToETongGuan?serialno=" + empno,
            success: function (data) {
                $("#empno16").val('');
                ajaxLoadEnd();
                successTip(data);
                // window.parent.mainpage.mainTabs.refCurrentTab();
            },
            error: function (date) {
                ajaxLoadEnd();
            }
        });
    }

    function cx17() {
        var empno = $("#empno17").val();
        if (empno == "" || empno == null || empno == undefined || empno == 'undefined') {
            $.messager.alert("溫馨提示", "請填寫表单编号！", "info");
            return false;
        }
        $.ajax({
            type: 'GET',
            beforeSend: ajaxLoading,
            url: "${ctx}/wfsystemprocess/rePao?serialno=" + empno,
            success: function (data) {
                $("#empno17").val('');
                ajaxLoadEnd();
                successTip(data);
                // window.parent.mainpage.mainTabs.refCurrentTab();
            },
            error: function (date) {
                ajaxLoadEnd();
            }
        });
    }

    function cx18() {
        var empno = $("#empno18").val();
        if (empno == "" || empno == null || empno == undefined || empno == 'undefined') {
            $.messager.alert("溫馨提示", "請填寫表单编号！", "info");
            return false;
        }
        $.ajax({
            type: 'GET',
            beforeSend: ajaxLoading,
            url: "${ctx}/wfsystemprocess/rePao1?serialno=" + empno,
            success: function (data) {
                $("#empno18").val('');
                ajaxLoadEnd();
                successTip(data);
                // window.parent.mainpage.mainTabs.refCurrentTab();
            },
            error: function (date) {
                ajaxLoadEnd();
            }
        });
    }

    function cx19() {
        var empno = $("#empno19").val();
        if (empno == "" || empno == null || empno == undefined || empno == 'undefined') {
            $.messager.alert("溫馨提示", "請填寫表单编号！", "info");
            return false;
        }
        $.ajax({
            type: 'GET',
            beforeSend: ajaxLoading,
            url: "${ctx}/wfsoftinstallprocess/rePao?serialno=" + empno,
            success: function (data) {
                $("#empno19").val('');
                ajaxLoadEnd();
                successTip(data);
                // window.parent.mainpage.mainTabs.refCurrentTab();
            },
            error: function (date) {
                ajaxLoadEnd();
            }
        });
    }

    function cxdatestr() {
        var empno = $("#dataStr").val();
        if (empno == null || empno == undefined || empno == 'undefined') {
            $.messager.alert("溫馨提示", "請選擇時間！", "info");
            return false;
        }
        $.ajax({
            type: 'GET',
            beforeSend: ajaxLoading,
            url: "${ctx}/system/scheduleJob/synsUserInfoByDateStr?dataStr=" + empno,
            success: function (data) {
                $("#dataStr").val('');
                ajaxLoadEnd();
                successTip(data);
                // window.parent.mainpage.mainTabs.refCurrentTab();
            },
            error:function(date){
                ajaxLoadEnd();
            }
        });
    }
    function isEmpty(value) {
        return value === null || value === undefined||value === 'undefined'||value === '';
    }
    function tborg() {
        var deptno = $("#deptno").val();
        var siteCode = $("#siteCode").val();
        if (isEmpty(deptno)&&isEmpty(siteCode)) {
            return false;
        }
        if (!isEmpty(deptno)&&!isEmpty(siteCode)) {
            parent.$.messager.alert('提示', "只能填寫一項");
            return false;
        }
        var para;
        if (!isEmpty(deptno)) {
            para = "?deptno=" + deptno;
        }
        if (!isEmpty(siteCode)) {
            para = "?siteCode=" + siteCode;
        }
        $.ajax({
            type: 'GET',
            beforeSend: ajaxLoading,
            url: "${ctx}/system/scheduleJob/synsOrgInfoByDeptno" + para,
            success: function (data) {
                $("#deptno").val('');
                $("#siteCode").val('');
                ajaxLoadEnd();
                successTip(data);
                // window.parent.mainpage.mainTabs.refCurrentTab();
            },
            error:function(date){
                ajaxLoadEnd();
            }
        });
    }

    function pz() {
        var serialno = $("#serialno").val();
        if (serialno == null || serialno == undefined || serialno == 'undefined') {
            return false;
        }
        $.ajax({
            type: 'GET',
            beforeSend: ajaxLoading,
            url: "${ctx}/requisitionlist/tossAndTurnFhw?serialno=" + serialno,
            success: function (data) {
                $("#serialno").val("");
                ajaxLoadEnd();
                successTip(data);
                // window.parent.mainpage.mainTabs.refCurrentTab();
            },
            error: function (date) {
                ajaxLoadEnd();
            }
        });
    }

    /*function pz_ptalk() {
        var serialno = $("#serialno_ptalk").val();
        if (serialno == null || serialno == undefined || serialno == 'undefined') {
            return false;
        }
        $.ajax({
            type: 'GET',
            beforeSend: ajaxLoading,
            url: "${ctx}/wfptalkprocess/tossAndTurnFhw?serialno=" + serialno,
            success: function (data) {
                $("#serialno_ptalk").val("");
                ajaxLoadEnd();
                successTip(data);
                // window.parent.mainpage.mainTabs.refCurrentTab();
            },
            error:function(date){
                ajaxLoadEnd();
            }
        });
    }*/

    function endByEmpno() {
        var empno = $("#empno_new").val();
        if (empno == null || empno == undefined || empno == 'undefined') {
            return false;
        }
        $.ajax({
            type: 'GET',
            beforeSend: ajaxLoading,
            url: "${ctx}/wfcontroller/batchEndTaskByEmpno/" + empno,
            success: function (data) {
                $("#empno_new").val("");
                ajaxLoadEnd();
                successTip(data);
                // window.parent.mainpage.mainTabs.refCurrentTab();
            },
            error:function(date){
                ajaxLoadEnd();
            }
        });
    }

    function confire() {
        var serialno = $("#serialno_new").val();
        if (serialno == null || serialno == undefined || serialno == 'undefined') {
            $.messager.alert("溫馨提示", "請填寫任務編號！", "info");
            return false;
        }
        $.ajax({
            type: 'GET',
            beforeSend: ajaxLoading,
            url: "${ctx}/tqhallrelation/reUploadFile?serialno=" + $("#serialno_new").val()+"&attachids="+$("#attachids").val(),
            success: function (data) {
                $("#serialno_new").val("");
                ajaxLoadEnd();
                successTip(data);
                // window.parent.mainpage.mainTabs.refCurrentTab();
            },
            error:function(date){
                ajaxLoadEnd();
            }
        });
    }

    function reUploadFile() {
        var serialno = $("#serialno_new").val();
        if (serialno == null || serialno == undefined || serialno == 'undefined') {
            $.messager.alert("溫馨提示", "請填寫任務編號！", "info");
            return false;
        }
//        var attachIds = $.trim($("#attachids").val());
//        var attachIdsL = attachIds.split(",").length;
//        if (attachIdsL >= 6) {
//            $.messager.alert("溫馨提示", "上傳附件個數不能超過五個！", "info");
//            return;
//        }
        var explorer = window.navigator.userAgent;
        if (!!window.ActiveXObject || "ActiveXObject" in window) {
            ajaxLoading();
            $.ajaxFileUpload({  //Jquery插件上传文件
                url: "${ctx}/admin/uploadCompatible",
                secureuri: false,//是否启用安全提交  默认为false
                fileElementId: "attachidsUpload", //type="file"的id
                dataType: "JSON",  //返回值类型
                success: function (msg, status) {
                    msg = jQuery.parseJSON(msg);
                    $("#attachids").val($("#attachids").val() + msg.id + ",");
                    $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href=' + ctx + '/admin/download/' + msg.id + '>' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delAtt(\'' + msg.id + '\')"></div></div>');
                    ajaxLoadEnd();
                },
                error: function (data, status, e) {
                    ajaxLoadEnd();
                }
            });
        } else {
// 创建
            var form_data = new FormData();
            // 获取文件
//        var file = document.getElementById('fileToUpload').files[0];
            var file_data = $("#attachidsUpload").prop("files")[0];
            form_data.append("file", file_data);
            form_data.append("path", "upload");
            // 把所以表单信息
            $.ajax({
                type: "POST",
                url: "${ctx}/admin/upload",
                beforeSend: ajaxLoading,
                dataType: "json",
                processData: false,  // 注意：让jQuery不要处理数据
                contentType: false,  // 注意：让jQuery不要设置contentType
                data: form_data
            }).success(function (msg) {
                ajaxLoadEnd();
                $("#attachids").val($("#attachids").val() + msg.id + ",");
                $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href=' + ctx + '/admin/download/' + msg.id + '>' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delAtt(\'' + msg.id + '\')"></div></div>');
                // $("#attachids").val(msg.id);
            }).fail(function (msg) {
                ajaxLoadEnd();
            });
        }
    }
    function delAtt(fileid) {
        $("#" + fileid).remove();
        var kk = $("#attachids").val();
        kk = kk.replace(fileid + ",", "");
        $("#attachids").val(kk);
        ajaxLoading();
        $.post('${ctx}/admin/delete/' + fileid, {}, function (data) {
            successTip(data);
            ajaxLoadEnd();
        }, 'text').error(function () {
            ajaxLoadEnd();
        });
    }

function tbtwg() {
        var empno = $("#empno2").val();
        if(empno==null||empno==undefined||empno=='undefined'){
            return false;
        }
        $.ajax({
            type:'GET',
            beforeSend: ajaxLoading,
            url:"${ctx}/system/scheduleJob/synsTwgUserInfoByEmpno?empno="+empno,
            success: function(data){
                $("#empno2").val('');
                ajaxLoadEnd();
                successTip(data);
                // window.parent.mainpage.mainTabs.refCurrentTab();
            },
            error:function(date){
                ajaxLoadEnd();
            }
        });
    }
    function is_empno() {
        var empno = $("#is_empno").val();
        if(empno==null||empno==undefined||empno=='undefined'){
            return false;
        }
        $.ajax({
            type:'GET',
            beforeSend: ajaxLoading,
            url:"${ctx}/system/user/queryUserfrmInfo?empno="+empno,
            success: function(data){
                $("#is_empno").val('');
                ajaxLoadEnd();
                successTip(data);
                // window.parent.mainpage.mainTabs.refCurrentTab();
            },
            error:function(date){
                ajaxLoadEnd();
            }
        });
    }
</script>
</body>
</html>
