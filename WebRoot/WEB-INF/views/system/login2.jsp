<%@ page contentType="text/html;charset=UTF-8" %>
<%@ page import="org.apache.shiro.web.filter.authc.FormAuthenticationFilter" %>
<%@ page import="org.apache.shiro.authc.ExcessiveAttemptsException" %>
<%@ page import="org.apache.shiro.authc.IncorrectCredentialsException" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fox" uri="/foxconn-tags" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<%
    String error = (String) request.getAttribute(FormAuthenticationFilter.DEFAULT_ERROR_KEY_ATTRIBUTE_NAME);
    request.setAttribute("error", error);
%>
<html>
<head>
    <title>iPEBG電子簽核平台</title>
    <meta http-equiv="X-UA-Compatible" content="IE=8"/>
    <%--<link rel="bookmark" href="${pageContext.request.contextPath}/favicon.ico"/>--%>
    <link rel="icon" href="${ctx}/favicon.ico" type="image/x-icon"/>
    <link rel="shortcut icon" href="${ctx}/favicon.ico" type="image/x-icon"/>
    <link rel="bookmark" href="${ctx}/favicon.ico" type="image/x-icon"/>
    <script src="${ctx}/static/plugins/easyui/jquery/jquery-3.6.1.min.js"></script>
    <link rel="stylesheet" type="text/css" href="${ctx}/static/css/bglogin_new2.css"/>
    <script>
        var captcha;

        function refreshCaptcha() {
            document.getElementById("img_captcha").src = "${ctx}/static/images/kaptcha.jpg?t=" + Math.random();
        }

        $(document).ready(function () {
            if (window != top) {
                top.location.href = location.href;
            }
        });
    </script>
</head>
<body>
    <form id="loginForm" action="${ctx}/admin/login" method="post">
        <div class="login_wrapper">
            <div class="login_title">
                <label>iPEBG電子簽核平臺</label>
            </div>
            <div class="login_main">
                <div class="login_info">
                    <div class="userAndPass">
                        <div class="inputUser">
                            <input type="text" class="inputstyle" id="username" name="username"  value="" autocomplete="off" onblur="toUperCaseString(this);" placeholder="用戶名為工號">
                            <span class="login_span">
                                    <img src="${ctx}/static/images/user1.png" class="login_img">
                                </span>
                        </div>
                        <div class="inputPass">
                            <input type="password" class="inputstyle" id="password" name="password"  value="" autocomplete="off"  placeholder="請輸入密碼"/>
                            <span class="login_span">
                                    <img src="${ctx}/static/images/pwd1.png" class="login_img">
                                </span>
                        </div>
                    </div>
                    <div class="codeOuter">
                        <div class="inputUser"></div>
                        <div class="inputPass">
                            <input type="text" class="codeinput" id="captcha" name="captcha" autocomplete="off"/>
                            <div style="border-radius:5px;display: inline-block;cursor: pointer;height: 40px;border:1px outset #D7D7D7;vertical-align: top;overflow: hidden;">
                                <img alt="验证码" src="${ctx}/static/images/kaptcha.jpg" title="点击更换" id="img_captcha" onclick="javascript:refreshCaptcha();" style="display: block;height:40px;width:85px;float:left;"/>
                            </div>
                            <button onclick="">
                                <%--<img src="${ctx}/static/images/btn2.png" width="46px" height="46px" onclick="">--%>
                            </button>
                        </div>
                    </div>
                    <div class="codeOuter">
                        <div class="inputUser"></div>
                        <div class="inputPass">
                            <div class="login_main_errortip">&nbsp;</div>
                        </div>
                    </div>
                    <div class="codeOuter">
                        <div class="inputUser">
                            <div class="login_link">
                                <a id="linka" target="_blank" href="http://itsm.ipebg.efoxconn.com/itsm/itsm_info_detail.jsp?recno=FPT2001150001" ><font id="fonta" color="#FF9B42">組織審核路徑新增/修改</font></a>
                            </div>
                        </div>
                        <div class="inputPass"></div>
                    </div>
                </div>
            </div>
            <div class="login_footer">
                <label><fox:copyRight></fox:copyRight></label>
            </div>
           <%-- <div style="text-align: center;">
                <img style="vertical-align:middle" src="${ctx}/admin/showImg/c97c66136dd14959a2c3f88a22344b89"/><span style="line-height: 40px;font-size: 18px;font-weight: bold"><a href="${ctx}/admin/downLoad">下載chrome瀏覽器</a></span>
            </div>--%>
        </div>
    </form>
<c:choose>
    <c:when test="${error eq 'com.foxconn.ipebg.system.utils.CaptchaException'}">
        <script>
            $(".login_main_errortip").html("验证码错误，请重试");
        </script>
    </c:when>
    <c:when test="${error eq 'org.apache.shiro.authc.IncorrectCredentialsException'}">
        <script>
            $(".login_main_errortip").html("帐号或密码错误，请重试");
        </script>
    </c:when>
    <%--<c:when test="${error eq 'org.apache.shiro.authc.IncorrectCredentialsException'}">
        <script>
            $(".login_main_errortip").html("用户名不存在，请重试");
        </script>
    </c:when>--%>
</c:choose>
<script>
    function toUperCaseString(obj) {
        $(obj).val(obj.value.toUpperCase());
    }
</script>
</body>
</html>
