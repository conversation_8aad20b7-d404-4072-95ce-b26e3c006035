<%@ page import="java.util.HashSet" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="fox" uri="/foxconn-tags" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title><fox:systemName></fox:systemName></title>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script type="text/javascript" src="${ctx }/static/plugins/artTemplate/dist/template.js"></script>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <!--导入首页启动时需要的相应资源文件(首页相应功能的 js 库、css样式以及渲染首页界面的 js 文件)-->
    <script src="${ctx}/static/plugins/easyui/common/index.js?v=20231026" type="text/javascript"></script>
    <link href="${ctx}/static/plugins/easyui/common/index.css" rel="stylesheet"/>
    <script src="${ctx}/static/plugins/easyui/common/index-startup.js"></script>
    <script src="${ctx}/static/plugins/jquery-marquee/lib/jquery.marquee.js"></script>
    <style>
        ul.marquee {
            display: block;
            line-height: 1;
            position: relative;
            overflow: hidden;
            width: 400px;
            height: 22px;
        }

        ul.marquee li {
            position: absolute;
            top: -999em;
            left: 0;
            display: block;
            white-space: nowrap;
            padding: 3px 5px;
            text-indent: 0.8em;
        }
    </style>
</head>
<body>
<!-- 容器遮罩 -->
<div id="maskContainer">
    <div class="datagrid-mask" style="display: block;"></div>
    <div class="datagrid-mask-msg" style="display: block; left: 50%; margin-left: -52.5px;">
        正在加载...
    </div>
</div>
<div id="mainLayout" class="easyui-layout hidden" data-options="fit: true">
    <div id="northPanel" data-options="region: 'north', border: false" style="height: 107px; overflow: hidden;">
        <div id="topbar" class="top-bar">
            <div class="top-bar-left">
                <h1 style="margin-left: 20px; margin-top: 20px;"><span
                        style="color: #3F4752;font-size: 26px"><fox:systemName></fox:systemName></span>
                </h1>
            </div>
            <div class="top-bar-right">
                <div id="timerSpan"></div>
                <div id="themeSpan">
                    <a id="btnHideNorth" class="easyui-linkbutton"
                       data-options="plain: true, iconCls: 'layout-button-up'"></a>
                </div>
            </div>
            <div class="top-bar-right" style="right: 300px;width:500px;height: 72px;line-height: 72px"
                 text-align="center">
                <ul id="marquee" class="marquee">

                </ul>
                <%--<marquee direction="up" scrollamount=1 scrolldelay=10 behavior=sroll onmouseover=this.stop() onmouseout=this.start() width="300"><a href="#" onclick="window.parent.mainpage.mainTabs.addModule('公告詳情','${ctx}/sysnotice/view/a359a3add0dc4be8a8fbfcd6a9d43ed6','icon-hamburg-basket')"><font style="font-size: large;color: #A60000">滚动文字</font></a></marquee>--%>
            </div>
        </div>
        <div id="toolbar" class="panel-header panel-header-noborder top-toolbar">
            <div id="infobar">
                    <span class="icon-hamburg-user" style="padding-left: 25px; background-position: left center;">
                       <shiro:principal property="name"/>，歡迎來到電子簽核平台
                    </span>
                <%--<span>&nbsp;&nbsp;<font color="red">當前在線人數：<% HashSet sessions = (HashSet)application.getAttribute("sessions");%><%=sessions.size()%></font></span>--%>
            </div>

            <div id="buttonbar">
                <%--               <span>更换皮肤：</span>--%>
                <%--                <select id="themeSelector"></select>--%>
                <a href="javascript:void(0);" class="easyui-menubutton" data-options="menu:'#layout_north_set'"
                   iconCls="icon-standard-cog">我的设置</a>
                <div id="layout_north_set">
                    <div id="updatePwd" data-options="iconCls:'key'">修改密碼</div>
                    <div id="syncUserInfo" data-options="iconCls:'key'">同步個人信息</div>
                    <div id="updateInfo" data-options="iconCls:'key'">修改個人信息</div>
                    <div id="btnFullScreen" data-options="iconCls:'key'">全屏切换</div>
                    <div id="btnExit" data-options="iconCls:'logout'">退出系统</div>
                </div>
                <a id="btnShowNorth" class="easyui-linkbutton" data-options="plain: true, iconCls: 'layout-button-down'"
                   style="display: none;"></a>
            </div>
        </div>
    </div>

    <div data-options="region: 'west', title: '菜单导航栏', iconCls: 'icon-standard-map', split: true, minWidth: 200, maxWidth: 400"
         style="width: 220px; padding: 1px;">
        <div id="RightAccordion" class="easyui-accordion" data-options="fit:true,border:false">
            <%--<ul id="tree"></ul>--%>
            <%--<script id="menu" type="text/html">
                {{each data as p_permission}}
                {{if (p_permission.pid==null&&p_permission.type=='F')}}
                <div title="{{p_permission.name }}" style="padding: 5px;"
                     data-options="border:false,iconCls:'{{p_permission.icon }}'">
                    <div>
                        <div id="myMenu1" class="easyui-accordion" data-options="fit:true,border:false">
                            {{each data as c_permission}}
                            {{if (c_permission.pid==p_permission.id&&c_permission.type=='F')}}
                            <div title="{{c_permission.name }}" style="padding: 5px;"
                                 data-options="border:false,iconCls:'{{c_permission.icon }}'">
                                <div>
                                    {{each data as d_permission}}
                                    {{if (d_permission.pid==c_permission.id&&d_permission.type=='F')}}
                                    <a id="btn" class="easyui-linkbutton"
                                       data-options="plain:true,iconCls:'{{d_permission.icon }}'"
                                       style="width:98%;margin-bottom:5px;"
                                       onclick="window.mainpage.mainTabs.addModule('{{d_permission.name}}','{{d_permission.url }}','{{d_permission.icon }}')">{{d_permission.name}}</a>
                                    {{/if}}
                                    {{/each}}
                                </div>
                            </div>
                            {{/if}}
                            {{/each}}
                        </div>
                    </div>
                </div>
                {{/if}}
                {{/each}}
            </script>--%>

        </div>
    </div>

    <div data-options="region: 'center'">
        <div id="mainTabs_tools" class="tabs-tool">
            <table>
                <tr>
                    <td><a id="mainTabs_jumpHome" class="easyui-linkbutton easyui-tooltip" title="跳转至主页选项卡"
                           data-options="plain: true, iconCls: 'icon-hamburg-home'"></a></td>
                    <td>
                        <div class="datagrid-btn-separator"></div>
                    </td>
                    <td><a id="mainTabs_toggleAll" class="easyui-linkbutton easyui-tooltip"
                           title="展开/折叠面板使选项卡最大化"
                           data-options="plain: true, iconCls: 'icon-standard-arrow-out'"></a></td>
                    <td>
                        <div class="datagrid-btn-separator"></div>
                    </td>
                    <td><a id="mainTabs_refTab" class="easyui-linkbutton easyui-tooltip" title="刷新当前选中的选项卡"
                           data-options="plain: true, iconCls: 'icon-standard-arrow-refresh'"></a></td>
                    <td>
                        <div class="datagrid-btn-separator"></div>
                    </td>
                    <td><a id="mainTabs_closeTab" class="easyui-linkbutton easyui-tooltip" title="关闭当前选中的选项卡"
                           data-options="plain: true, iconCls: 'icon-standard-application-form-delete'"></a></td>
                </tr>
            </table>
        </div>
        <div id="mm" class="easyui-menu" style="width:120px;">
            <div onclick="append()" data-options="iconCls:'icon-add'">添加到快捷菜單</div>
            <%--            <div onclick="remove()" data-options="iconCls:'icon-remove'">移除</div>--%>
        </div>
        <div id="mainTabs" class="easyui-tabs"
             data-options="fit: true, border: false, showOption: true,enableNewTabMenu: true, tools: '#mainTabs_tools', enableJumpTabMenu: true">
            <div id="homePanel" data-options="title: '主页', iconCls: 'icon-hamburg-home',refreshable: true">
                <div class="easyui-layout" data-options="fit: true">
                    <%--<div data-options="region: 'east', split: false, border: false" style="text-align: center;width: 50%;">
                        111
                    </div>--%>
                    <div data-options="region: 'center', border: false" style="overflow: hidden;">
                        <table id="myTask"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div data-options="region: 'east', title: '快捷菜單', iconCls: 'icon-standard-date', split: true,collapsed: true, minWidth: 160, maxWidth: 1000"
         style="width: 200px;">
        <div id="eastLayout" class="easyui-layout" data-options="fit: true">
            <div data-options="region: 'south', split: false, border: false" style="height: 220px;">
                <div class="easyui-calendar" data-options="fit: true, border: false"></div>
            </div>
            <div id="shortcut" data-options="region: 'center', border: false" style="overflow: hidden;">

            </div>
            <%--<div id="linkPanel"
                 data-options="region: 'center', border: false, title: '通知', iconCls: 'icon-hamburg-link', tools: [{ iconCls: 'icon-hamburg-refresh', handler: function () { window.link.reload(); } }]">

            </div>--%>
        </div>
    </div>
    <div id="deleteShortCard" class="easyui-menu" style="width:120px;">
        <div onclick="deleteShortCard()" data-options="iconCls:'icon-add'">刪除</div>
        <%--            <div onclick="remove()" data-options="iconCls:'icon-remove'">移除</div>--%>
    </div>
    <%--<div align="center" data-options="region: 'south'" border="false" class="panel-header panel-header-noborder top-toolbar"
         >
        <font color="#000" face="Microsoft JhengHei,Microsoft YaHei,tahoma, Arial, sans-serif" style="font-size:12px;">CopyRight
            &copy;
            2018 中原資訊總處&nbsp;&nbsp;版權所有</font>
    </div>--%>
    <div id="northPane2" data-options="region: 'south', border: false" style="overflow: hidden;">
        <div id="toolbar1" class="panel-header">
            <div id="infobar1" align="center">
                    <span style="padding-left: 25px; background-position: left center;font-size: 14px">
                       <%--CopyRight&copy;2018 中原資訊總處&nbsp;&nbsp;版權所有--%>
                       <fox:copyRight></fox:copyRight>
                    </span>
            </div>
        </div>
    </div>
</div>
<div id="dlg"></div>
<script>
    var dg;
    var dg1;
    $(function () {
        dg = $('#myTask').datagrid({
            method: "post",
            url: '${ctx}/wfcontroller/list',
            fit: true,
            async: false,
            fitColumns: true,
            border: false,
            striped: true,
            idField: 'id',
            pagination: false,
//            rownumbers: true,
            scrollbarSize: 0,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: true,
            columns: [[
                {field: 'wfName', title: '任務類別', width: 100},
                {field: 'workflowId', title: 'workflowId', width: 50, hidden: true},
                {
                    field: 'flag', title: '狀態', width: 20, formatter: flagStatus,
                    styler: function (value, row, index) {
                        if (value == '4') {
                            return 'color:red;font-weight:bold;';
                        }
                    }
                },
                {field: 'taskCount', title: '合計', width: 10, formatter: operation},
            ]],
            rowStyler: function (index, row) {
                if (row.flag == 4) {
                    //return 'color:red;font-weight:bold;';
                }
            },
            enableHeaderClickMenu: false,
            enableHeaderContextMenu: false,
            enableRowContextMenu: false
        });

        <%--        dg1 = $('#myTask1').datagrid({--%>
        <%--            method: "get",--%>
        <%--            url: '${ctx}/system/exchange/json',--%>
        <%--            fit: true,--%>
        <%--            async: false,--%>
        <%--            fitColumns: true,--%>
        <%--            border: false,--%>
        <%--            striped: true,--%>
        <%--            scrollbarSize: 0,--%>
        <%--            idField: 'id',--%>
        <%--            pagination: false,--%>
        <%--//            rownumbers: true,--%>
        <%--            pageNumber: 1,--%>
        <%--            pageSize: 20,--%>
        <%--            pageList: [10, 20, 30, 40, 50],--%>
        <%--            singleSelect: true,--%>
        <%--            columns: [[--%>
        <%--                {field: 'wfName', title: '任務類別', width: 100},--%>
        <%--                {field: 'workflowId', title: 'workflowId', width: 50, hidden: true},--%>
        <%--                {--%>
        <%--                    field: 'flag', title: '狀態', width: 20, formatter: flagStatus,--%>
        <%--                    styler: function (value, row, index) {--%>
        <%--                        if (value == '4') {--%>
        <%--                            return 'color:red;font-weight:bold;';--%>
        <%--                        }--%>
        <%--                    }--%>
        <%--                },--%>
        <%--                {field: 'taskCount', title: '合計', width: 10, formatter: operation1},--%>
        <%--            ]],--%>
        <%--            rowStyler: function (index, row) {--%>
        <%--                if (row.flag == 4) {--%>
        <%--                    //return 'color:red;font-weight:bold;';--%>
        <%--                }--%>
        <%--            },--%>
        <%--            onLoadSuccess: function (data) {--%>
        <%--                var info = $("#myTask1").datagrid("getData");--%>
        <%--                //这里举例获取某列所有数据的和，当然你也可以进行其它处理或遍历操作--%>
        <%--                if (info.rows.length > 0) {--%>
        <%--//                    $('#mainLayout').layout('expand', 'east');--%>
        <%--                }--%>
        <%--            },--%>
        <%--            enableHeaderClickMenu: false,--%>
        <%--            enableHeaderContextMenu: false,--%>
        <%--            enableRowContextMenu: false--%>
        <%--        });--%>
        //定義刷新主頁數據的方法
        window.top["reload_Abnormal_Monitor"] = function () {
            dg.datagrid("reload");
        }

        var urlFromOld = "${urlFromOld}";
        if (urlFromOld != null && urlFromOld != "") {
            var titleFromOld = decodeURI(decodeURI("${titleFromOld}"));
            window.parent.mainpage.mainTabs.addModule(titleFromOld, '${pageContext.request.contextPath}' + urlFromOld, 'icon-hamburg-basket');
        }
        <%--if (sureIsIEAndLower8()) {--%>
        <%--    $.messager.confirm("操作提示", "檢測到您正在使用ie8及以下版本瀏覽器，部分功能無法正常使用，請點擊確定跳轉谷歌瀏覽器！", function (data) {--%>
        <%--        if (data) {--%>
        <%--            var urlEmail = "${urlEmail}"--%>
        <%--            var objShell = new ActiveXObject("WScript.Shell");--%>
        <%--            try {--%>
        <%--                if (urlEmail != '') {--%>
        <%--                    objShell.Run('cmd.exe /c start chrome "' + urlEmail + '&random=<%= Math.random()%>"', 0, true);--%>
        <%--                } else {--%>
        <%--                    objShell.Run('cmd.exe /c start chrome "http://esign.ipebg.efoxconn.com/newEsign/admin/login&random=<%= Math.random()%>"', 0, true);--%>
        <%--                }--%>
        <%--            } catch (e) {--%>
        <%--                $.messager.alert('操作提示', '檢測到本機沒有安裝谷歌瀏覽器，請安裝並使用谷歌瀏覽器！', "warning");--%>
        <%--            }--%>
        <%--        }--%>
        <%--    });--%>
        <%--}--%>
        queryNotice();

        addListenor();
        if ('<shiro:principal property="whetherDefaultPassword"/>' == 'true') {
            $.messager.alert('操作提示', '為保障你的帳號安全，請先修改密碼！', "warning");
        }
    });

    function addListenor() {
        if (window.addEventListener) {
            window.addEventListener('message', function (e) {
                // console.log("接到消息!!!!addEventListener");
                // console.log(e);
                closeCurrentTabMessageHandler(e.data);
            });
        } else if (window.attachEvent) {
            //for ie
            window.attachEvent('message', function (e) {
                // console.log("接到消息!!!!attachEvent");
                // console.log(e);
                closeCurrentTabMessageHandler(e.data);
            });
        }
    }

    function closeCurrentTabMessageHandler(data) {
        if ('closeCurrentTabMessage' === data) {
            var url = window.mainpage.mainTabs.getUrl();
            var serialno = "";
            if (url != null && url.indexOf("&serialno=") >= 0) {
                serialno = url.split("&serialno=")[1];
                if (url.indexOf("&")) {
                    serialno = serialno.split("&")[0];
                }
            }
            $.ajax({
                type: "POST",
                async: false,
                url: "${ctx}/wfcontroller/getNextForm",
                data: {"serialno": serialno},
                success: function (data) {
                    if (data == "success") {//無待審單據時
                        window.mainpage.mainTabs.closeCurrentTab();
                    } else {
                        var url = data.split(";");
                        if (data.indexOf("turnNewform=1") > 0) { //entfrm
                            window.mainpage.mainTabs.openNewPageAtLocation(url[0] + "&random=" + Math.random(), url[1]);
                        }
                        if (data.indexOf("turnNewform=2") > 0) { //iPEBG
                            window.mainpage.mainTabs.openNewPageAtLocation("${ctx}" + "/" + url[0] + "&random=" + Math.random(), url[1]);
                        }
                    }
                }
            });
        }
        if ("returnCurrentTabMessage" === data) {
            window.parent.mainpage.mainTabs.closeCurrentTab();
        }
    }

    //查詢公告
    function queryNotice() {
        $.ajax({
            type: "GET",
            dataType: 'json',
            async: false,
            url: "${ctx}/sysnotice/infoList",
            data: {},
            success: function (data) {
                var html = '';
                $.each(data, function (i, v) {
                    html += "<li><a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('公告詳情','${ctx}/sysnotice/view/" + v.id + "','icon-hamburg-basket')\"><font style=\"font-size: large;color: #A60000\">【公告】" + v.title + "</font></a> [創建於" + v.createDate + "]</li>";
                })
                $("#marquee").html(html);
            }
        });
        $("#marquee").marquee({
            yScroll: "bottom",
            showSpeed: 850,        // 初始下拉速度         ,
            scrollSpeed: 30,       // 滚动速度         ,
            pauseSpeed: 1000,      // 滚动完到下一条的间隔时间         ,
            pauseOnHover: true,    // 鼠标滑向文字时是否停止滚动         ,
            loop: -1,             // 设置循环滚动次数 （-1为无限循环）         ,
            fxEasingShow: "swing", // 缓冲效果         ,
            fxEasingScroll: "linear",  // 缓冲效果         ,
            cssShowing: "marquee-showing"  //定义class

        });
    }

    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);//search,查询？后面的参数，并匹配正则
        if (r != null) return unescape(r[2]);
        return null;
    }

    function flagStatus(value, row, index) {
        if (value == '2') {
            return "待簽核";
        } else if (value == '4') {
            return "駁回";
        }
    }

    function operation(value, row, index) {
        if (row.flag == '2') {
            return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('我的待辦(待簽核)','${ctx}/wfcontroller/index?workFlowId=" + row.workflowId + "&status=" + row.flag + "&random=<%= Math.random()%>','icon-hamburg-basket')\">" + value + "</a>";
        } else if (row.flag == '4') {
            return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('我的待辦(駁回)','${ctx}/wfcontroller/index?workFlowId=" + row.workflowId + "&status=" + row.flag + "&random=<%= Math.random()%>','icon-hamburg-basket')\">" + value + "</a>";
        }
    };

    function operation1(value, row, index) {
        var old_url;
        $.ajax({
            async: false,
            type: 'get',
            url: "${ctx}/system/dict/getDictById",
            success: function (data) {
                old_url = data;
            }
        });
        return '<a href="' + old_url + '/frame/indexFrame.action" target="_blank">' + value + "</a>";
    };
    //創建菜單
    jQuery("#RightAccordion").accordion({ //初始化accordion
        fillSpace: true,
        fit: true,
        border: false,
        animate: false
    });
    $.ajax({
        async: false,
        type: 'get',
        url: "${ctx}/system/permission/i/json",
        dataType: "json",
        success: function (data) {
            $.each(data, function (i, e) {//循环创建手风琴的项
                var id = e.id;
                $('#RightAccordion').accordion('add', {
                    title: e.text,
                    content: "<ul id='tree" + id + "' ></ul>",
                    selected: true,
                    iconCls: e.iconCls,//e.Icon
                    id: id,
                    onCollapse: function () {
                        try {
                            var roots = $("#tree" + id).tree('getChildren');
                            for (var i = 0; i < roots.length; i++) {
                                var node = $("#tree" + id).tree('find', roots[i].id)
                                $("#tree" + id).tree('unselect', node.target);
                            }
                            $("#tree" + id).tree('collapseAll');
                        } catch (error) {

                        }

                    }
                });
                $.parser.parse();
                $.post("${ctx}/system/permission/i/json?pid=" + id, function (data) {//循环创建树的项
                    $("#tree" + id).tree({
                        enableContextMenu: false,
                        data: data,
                        formatter: function (node) {
                            return '<span title="' + node.text + '"  class="easyui-tooltip" >' + node.text + '</span>'
                        },
                        onClick: function (node) {
                            if (node.state == 'closed' && (!$("#tree").tree('isLeaf', node.target))) {  //状态为关闭而且非叶子节点
                                $(this).tree('expand', node.target);//点击文字展开菜单
                                if (node.attributes.url) {
                                    window.mainpage.mainTabs.addModule(node.text, node.attributes.url, node.attributes.icon)
                                }
                            } else {
                                if ($("#tree").tree('isLeaf', node.target)) {  //状态为打开而且为叶子节点
                                    if (node.attributes.url) {
                                        window.mainpage.mainTabs.addModule(node.text, node.attributes.url, node.attributes.icon)
                                    }
                                } else {
                                    $(this).tree('collapse', node.target);//点击文字关闭菜单  
                                }
                            }
                        },
                        onLoadSuccess: function (node, data) {
                            $("#tree" + id).tree("collapseAll");
                            $(".easyui-tooltip").tooltip({
                                onShow: function () {
                                    $(this).tooltip('tip').css({
                                        borderColor: '#000'
                                    });
                                }
                            });
                        },
                        onContextMenu: function (e, node) {
                            e.preventDefault();
                            // 查找节点
                            if ($("#tree").tree('isLeaf', node.target)) {
                                $("#tree" + id).tree('select', node.target);
                                // 显示快捷菜单
                                $('#mm').menu('show', {
                                    left: e.pageX,
                                    top: e.pageY
                                });
                            }
                        }
                    });
                }, 'json');
            });
        }
    });

    $('.easyui-linkbutton').on('click', function () {
        $('.easyui-linkbutton').linkbutton({selected: false});
        $(this).linkbutton({selected: true});
    });
    //修改密碼彈窗
    $("#updatePwd").click(function () {
        updatePwd();
    });
    var d;

    //修改密碼
    function updatePwd() {
        d = $("#dlg").dialog({
            title: '修改密碼',
            width: 380,
            height: 380,
            async: false,
            href: '${ctx}/system/user/updatePwd',
            maximizable: true,
            modal: true,
            buttons: [{
                text: '确认',
                handler: function () {
                    $("#mainform").submit();
                }
            }, {
                text: '取消',
                handler: function () {
                    d.panel('close');
                }
            }]
        });
    }

    $("#updateInfo").click(function () {
        updateInfo();
    });

    //弹窗修改
    function updateInfo() {
        d = $("#dlg").dialog({
            title: '修改個人信息',
            width: 550,
            height: 580,
            href: '${ctx}/system/user/updatePersion',
            maximizable: true,
            modal: true,
            buttons: [{
                text: '修改',
                handler: function () {
                    $('#mainform').submit();
                }
            }, {
                text: '取消',
                handler: function () {
                    d.panel('close');
                }
            }]
        });
    }

    $("#syncUserInfo").click(function () {
        $.ajax({
            type: 'GET',
            url: "${ctx}/system/scheduleJob/synsUserInfoByEmpno?empno=${user.loginName}",
            success: function (data) {
                successTip(data);
            }
        });
    });

    function IEVersion() {
        var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
        var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器
        var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器
        var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
        if (isIE) {
            var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
            reIE.test(userAgent);
            var fIEVersion = parseFloat(RegExp["$1"]);
            if (fIEVersion == 7) {
                return 7;
            } else if (fIEVersion == 8) {
                return 8;
            } else if (fIEVersion == 9) {
                return 9;
            } else if (fIEVersion == 10) {
                return 10;
            } else {
                return 6; //IE版本<=7
            }
        } else if (isEdge) {
            return 'edge'; //edge
        } else if (isIE11) {
            return 11; //IE11
        } else {
            return -1; //不是ie浏览器
        }
    }

    var sureIsIEAndLower8 = function () {
        var version = IEVersion();
        if (-1 == version) {
            return false;
        } else if (8 < version || "edge" == version) {
            return false;
        } else {
            return true;
        }
    }

    function append() {
        var panels = $('#RightAccordion').accordion('panels');
        for (var i = 0; i < panels.length; i++) {
            var id = panels[i].attr('id');
            var selectV = $('#tree' + id).tree('getSelected')
            if (!isEmpty(selectV)) {
                $.ajax({
                    type: 'GET',
                    url: "${ctx}/syspermissionshortcut/create/" + selectV.attributes.id + "/" + id,
                    success: function (data) {
                        console.log(data);
                        if (data == 'success') {
                            $("#shortcut").tree('reload');
                            parent.$.messager.show({title: "提示", msg: "操作成功！", position: "bottomRight"});
                        } else {
                            parent.$.messager.alert(data);
                        }
                    }
                });
            }
        }
        // parent.$.messager.alert("溫馨提示", "功能開發中", "warning");
    }

    $("#shortcut").tree({
        url: '${ctx}/syspermissionshortcut/i/json',
        enableContextMenu: false,
        formatter: function (node) {
            return '<span title="' + node.attributes.description + '"  class="easyui-tooltip" >' + node.text + '</span>'
        },
        onClick: function (node) {
            if (node.attributes.url) {
                window.mainpage.mainTabs.addModule(node.text, node.attributes.url, node.attributes.icon);
                p = $("#RightAccordion").accordion('getPanel', node.attributes.accordionName);
                p.panel('expand');
                var node1 = $('#tree' + node.attributes.accordionId).tree('find', node.attributes.pid);
                $('#tree' + node.attributes.accordionId).tree('expandTo', node1.target).tree('select', node1.target);
            }
            $('#mainLayout').layout('collapse', 'east');
        },
        onLoadSuccess: function (node, data) {
            $(".easyui-tooltip").tooltip({
                onShow: function () {
                    $(this).tooltip('tip').css({
                        borderColor: '#000'
                    });
                }
            });
        },
        onContextMenu: function (e, node) {
            e.preventDefault();
            // 查找节点
            if ($("#shortcut").tree('isLeaf', node.target)) {
                $("#shortcut").tree('select', node.target);
                // 显示快捷菜单
                $('#deleteShortCard').menu('show', {
                    left: e.pageX,
                    top: e.pageY
                });
            }
        }
    });

    function deleteShortCard() {
        var selectV = $('#shortcut').tree('getSelected')
        console.log(selectV.id);
        // var row = dg.treegrid('getSelected');
        if (rowIsNull(selectV)) return;
        $.ajax({
            type: 'get',
            url: "${ctx}/syspermissionshortcut/delete/" + selectV.id,
            success: function (data) {
                $("#shortcut").tree('reload');
                parent.$.messager.show({title: "提示", msg: "操作成功！", position: "bottomRight"});
            }
        });
    }
</script>
</body>
</html>
