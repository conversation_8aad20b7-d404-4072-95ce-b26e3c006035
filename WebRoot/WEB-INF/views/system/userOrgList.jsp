<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title></title>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<div id="cc" class="easyui-layout" style="width:100%;height:100%;">
    <div data-options="region:'center',title:'模塊',split:true">
        <table id="permissionDg1"></table>
    </div>
    <div data-options="region:'east',title:'機構',split:true" style="width:530px;">
        <table id="ur_dg" class="ztree"></table>
    </div>

</div>
<div id="dlg"></div>
<script type="text/javascript">
    var ur_dg;
    var permissionDg;
    var setting = {
        check: {
            enable: true,
            chkboxType: {"Y": "", "N": ""}
        },
        data: {
            simpleData: {
                enable: true,
                idKey: "id",
                pIdKey: "pid"
            },
            key: {
                name: "orgName"
            }

        }
    };
    $(function () {
        permissionDg = $('#permissionDg1').treegrid({
            method: "get",
            url: '${ctx}/system/permission/json/onlyf',
            fit: true,
            fitColumns: true,
            border: false,
            idField: 'id',
            treeField: 'name',
            parentField: 'pid',
            iconCls: 'icon',
            animate: true,
            rownumbers: true,
            striped: true,
            singleSelect: true,//需设置
            columns: [[
                {field: 'ck', checkbox: true, hidden: true, width: 100},
                {field: 'id', title: 'id', hidden: true, width: 100},
                {field: 'name', title: '名称', width: 100},
                {field: 'description', title: '描述', width: 100, tooltip: true}
            ]],
            onClickRow: function (row) {
                //级联选择
                // $(this).treegrid('cascadeCheck', {
                //     id: row.id, //节点ID
                //     deepCascade: true //深度级联
                // });
                lookP(row.id);
            }
        });
        //获取树
        $.ajax({
            async: false,
            url: '${ctx}/system/organization/json',
            type: "get",
            dataType: "json",
            success: function (datas) {
                ur_dg = $.fn.zTree.init($("#ur_dg"), setting, datas);
            },
            error: function (event, errors) {
                $.easyui.messager.alert("加载失败");
            }
        });
    })

    //保存用户机构
    function saveUserOrg() {
        flag = true;
        var row = permissionDg.datagrid('getSelected');
        if (row == null) {
            parent.$.messager.show({title: "提示", msg: "请选择模塊！", position: "bottomRight"});
            return false;
        }
        var newRoleList = [];
        var data = ur_dg.getCheckedNodes(true);
        //所选的机构列表
        for (var i = 0, j = data.length; i < j; i++) {
            console.info(data[i].id);
            newRoleList.push(data[i].id);
        }
        // if (newRoleList.length == 0) {
        //     parent.$.messager.show({title: "提示", msg: "请选择機構數據！", position: "bottomRight"});
        //     return false;
        // }
        $.ajax({
            async: false,
            type: 'POST',
            data: JSON.stringify(newRoleList),
            contentType: 'application/json;charset=utf-8', //必须
            url: "${ctx}/system/userFromOrg/${userId}/" + row.id + "/save",
            success: function (data) {
                data = JSON.parse(data);
                if (data.msg == 'success') {
                    parent.$.messager.show({
                        title: "提示",
                        msg: "操作成功！",
                        position: "bottomRight"
                    });
                } else {
                    flag = false;
                    $.easyui.messager.alert(data.msg);
                }
            }
        });
        return flag;
    }

    function lookP(formId) {
        var treeObj = $.fn.zTree.getZTreeObj("ur_dg");
        treeObj.checkAllNodes(false); // 取消勾选的所有节点
        // var row = permissionDg.datagrid('getSelected');
        // console.log(row.id);
        $.ajax({
            async: false,
            type: 'get',
            dataType: 'json',
            url: "${ctx}/system/userFromOrg/${userId}/" + formId + "/get",
            success: function (data) {
                if (data.data) {
                    data = data.data;
                    for (var i = 0, j = data.length; i < j; i++) {
                        var nodes = ur_dg.getNodesByParam("id", data[i], null);//根据org的ID值获取符合的节点
                        for (var d = 0, b = nodes.length; d < b; d++) {//对符合的节点进行遍历勾选
                            ur_dg.checkNode(nodes[d], true, false);
                        }
                    }
                }
            },
            error: function (event, errors) {
                $.easyui.messager.alert("加载失败");
            }
        });
    }
</script>
</body>
</html>