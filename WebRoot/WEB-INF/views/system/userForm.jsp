<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
	<title></title>
	<script type="text/javascript">
		var ctx = "${pageContext.request.contextPath}";
	</script>
	<%@ include file="/WEB-INF/views/include/easyui.jsp" %>
	<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div>
	<form id="mainform" action="${ctx }/system/user/${action}" method="post">
		<table class="formTable" style="width: 100%">
			<tr>
				<td>工號：</td>
				<td>
					<input type="hidden" name="id" value="${user.id }"/>
					<input id="loginName" name="loginName" class="easyui-validatebox" data-options="width: 200" value="${user.loginName }">
				</td>
			</tr>
			<c:if test="${action != 'update'}">
				<tr>
					<td>密码：</td>
					<td><input id="plainPassword" name="plainPassword" type="password" class="easyui-validatebox" data-options="width: 200,required:'required',validType:['minLength[8]','passwordVlid']"/></td>
				</tr>
				<tr>
					<td>确认密码：</td>
					<td><input id="confirmPassword" name="confirmPassword" type="password" class="easyui-validatebox" data-options="width: 200,required:'required',validType:'equals[$(\'#plainPassword\').val()]'"/></td>
				</tr>
			</c:if>
			<tr>
				<td>姓名：</td>
				<td><input name="name" type="text" value="${user.name }" class="easyui-validatebox" data-options="width: 200,required:'required',validType:'length[1,20]'"/></td>
			</tr>
			<tr>
				<td>部門代碼：</td>
				<td><input name="deptNo" type="text" value="${user.deptNo }" class="easyui-validatebox" data-options="width: 200,required:'required'"/></td>
			</tr>
			<tr>
				<td>出生日期：</td>
				<td><input name="birthday" type="text" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width: 200" value="<fmt:formatDate value="${user.birthday}" pattern='yyyy-MM-dd'/>"/></td>
			</tr>
			<tr>
				<td>性别：</td>
				<td>
					<input type="radio" id="man" name="gender" value="1"/><label for="man">男</label>
					<input type="radio" id="woman" name="gender" value="0"/><label for="woman">女</label>
				</td>
			</tr>
			<tr>
				<td>电话：</td>
				<td><input type="text" name="phone" value="${user.phone }" class="easyui-validatebox"
						   data-options="width: 200"/></td>
			</tr>
			<tr>
				<td>郵箱：</td>
				<td><input type="text" name="email" value="${user.email }" class="easyui-validatebox"
						   data-options="width: 200,validType:'email'"/></td>
			</tr>
			<tr>
				<td colspan="2" style="font-weight: bold;font-size: 14px;color:#2779AA;">PC端消息接收設置</td>
			</tr>
			<c:if test="${not empty action &&'update' eq action}">
				<tr>
					<td>及時郵件發送：</td>
					<td>
						<div class="emailsetDiv"></div>
						<input id="emailset" name="emailset"
							   type="hidden" class="easyui-validatebox" data-options="width: 200"
							   value="${user.emailset}"/>
					</td>
				</tr>
				<tr>
					<td>定時段發送：</td>
					<td>
						<table id="timingperiodTable">
							<tbody id="info_Body_ip2">
							<c:if test="${not empty user.timingperiod}">
								<c:forEach items="${user.timingperiod.split(',')}" var="itemsEntity" varStatus="status">
									<tr id="timingperiodItem${status.index+1}">
										<td>
											<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="addTimingperiod('0');"></a>
											<input class="easyui-timespinner" style="width:80px;" name="timingperiodStart" id="timingperiodStart${status.index+1}" onblur="chgTimingperiod(${status.index+1})" value="${itemsEntity.split('-')[0]}"/> — <input class="easyui-timespinner" style="width:80px;" name="timingperiodEnd" id="timingperiodEnd${status.index+1}" onblur="chgTimingperiod(${status.index+1})" value="${itemsEntity.split('-')[1]}"/>
											<input type="hidden" name="timingperiod" id="timingperiod${status.index+1}" value="${itemsEntity}" />
											<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" data-options="disabled:false" onclick="deleteTimingperiodRow(${status.index+1});return false;"></a>
										</td>
									</tr>
								</c:forEach>
							</c:if>
							<c:if test="${empty user.timingperiod}">
								<tr id="timingperiodItem1">
									<td>
										<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="addTimingperiod('0');"></a>
										<input class="easyui-timespinner" style="width:80px;" name="timingperiodStart" id="timingperiodStart1" onblur="chgTimingperiod(1)"/> — <input class="easyui-timespinner" style="width:80px;"  name="timingperiodEnd" id="timingperiodEnd1" onblur="chgTimingperiod(1)"/>
										<input type="hidden" name="timingperiod" id="timingperiod1" value="" />
										<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" data-options="disabled:false" onclick="deleteTimingperiodRow(1);return false;"></a>
									</td>
								</tr>
							</c:if>
							</tbody>
						</table>
					</td>
				</tr>
				<tr>
					<td>定點發送：</td>
					<td>
						<table>
							<tbody id="info_Body_ip">
							<c:if test="${not empty user.timingsend}">
								<c:forEach items="${user.timingsend.split(',')}" var="itemsEntity" varStatus="status">
									<tr id="timingsendItem${status.index+1}">
										<td>
											<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="addTimingsend('0');"></a>
											<input class="easyui-timespinner" style="width:80px;" name="timingsendpoint" id="timingsendpoint${status.index+1}" onblur="chgTimingsend(${status.index+1})" value="${itemsEntity}">
											<input type="hidden" name="timingsend" id="timingsend${status.index+1}"  value="${itemsEntity}">
											<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" data-options="disabled:false" onclick="deleteTimingsendRow(${status.index+1});return false;"></a>
										</td>
									</tr>
								</c:forEach>
							</c:if>
							<c:if test="${empty user.timingsend}">
								<tr id="timingsendItem1">
									<td>
										<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="addTimingsend('0');"></a>
										<input class="easyui-timespinner" style="width:80px;" name="timingsendpoint" id="timingsendpoint1" onblur="chgTimingsend(1)" value="">
										<input type="hidden" name="timingsend" id="timingsend1"  value="">
										<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" data-options="disabled:false" onclick="deleteTimingsendRow(1);return false;"></a>
									</td>
								</tr>
							</c:if>
							</tbody>
						</table>
					</td>
				</tr>
				<tr>
					<td colspan="2" style="font-weight: bold;font-size: 14px;color:#2779AA;">APP端消息接收設置(不設置默認與PC同步)</td>
				</tr>
				<tr>
					<td>及時郵件發送：</td>
					<td>
						<div class="emailsetappDiv"></div>
						<input id="emailsetapp" name="emailsetapp"
							   type="hidden" class="easyui-validatebox" data-options="width: 200"
							   value="${user.emailsetapp}"/>
					</td>
				</tr>
				<tr>
					<td>定時段發送：</td>
					<td>
						<table id="timingperiodappTable">
							<tbody id="info_Body_ipapp2">
							<c:if test="${not empty user.timingperiodapp}">
								<c:forEach items="${user.timingperiodapp.split(',')}" var="itemsEntity" varStatus="status">
									<tr id="timingperiodappItem${status.index+1}">
										<td>
											<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="addTimingperiodapp('0');"></a>
											<input class="easyui-timespinner" style="width:80px;" name="timingperiodappStart" id="timingperiodappStart${status.index+1}"  value="${itemsEntity.split('-')[0]}" onblur="chgTimingperiodapp(${status.index+1})"/> — <input class="easyui-timespinner" style="width:80px;" name="timingperiodappEnd" id="timingperiodappEnd${status.index+1}" value="${itemsEntity.split('-')[1]}" onblur="chgTimingperiodapp(${status.index+1})"/>
											<input type="hidden" name="timingperiodapp" id="timingperiodapp${status.index+1}" value="${itemsEntity}" />
											<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" data-options="disabled:false" onclick="deleteTimingperiodappRow(${status.index+1});return false;"></a>
										</td>
									</tr>
								</c:forEach>
							</c:if>
							<c:if test="${empty user.timingperiodapp}">
								<tr id="timingperiodappItem1">
									<td>
										<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="addTimingperiodapp('0');"></a>
										<input class="easyui-timespinner" style="width:80px;" name="timingperiodappStart" id="timingperiodappStart1" onblur="chgTimingperiodapp(1)"/> — <input class="easyui-timespinner" style="width:80px;"  name="timingperiodappEnd" id="timingperiodappEnd1" onblur="chgTimingperiodapp(1)"/>
										<input type="hidden" name="timingperiodapp" id="timingperiodapp1" value="" />
										<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" data-options="disabled:false" onclick="deleteTimingperiodappRow(1);return false;"></a>
									</td>
								</tr>
							</c:if>
							</tbody>
						</table>
					</td>
				</tr>
				<tr>
					<td>定點發送：</td>
					<td>
						<table>
							<tbody id="info_Body_ipapp">
							<c:if test="${not empty user.timingsendapp}">
								<c:forEach items="${user.timingsendapp.split(',')}" var="itemsEntity" varStatus="status">
									<tr id="timingsendappItem${status.index+1}">
										<td>
											<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="addTimingsendapp('0');"></a>
											<input class="easyui-timespinner" style="width:80px;" name="timingsendapppoint" id="timingsendapppoint${status.index+1}" onblur="chgTimingsendapp(${status.index+1})"  value="${itemsEntity}">
											<input type="hidden" name="timingsendapp" id="timingsendapp${status.index+1}"  value="${itemsEntity}">
											<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" data-options="disabled:false" onclick="deleteTimingsendappRow(${status.index+1});return false;"></a>
										</td>
									</tr>
								</c:forEach>
							</c:if>
							<c:if test="${empty user.timingsendapp}">
								<tr id="timingsendappItem1">
									<td>
										<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="addTimingsendapp('0');"></a>
										<input class="easyui-timespinner" style="width:80px;" name="timingsendapppoint" id="timingsendapppoint1" onblur="chgTimingsendapp(1)" value="">
										<input type="hidden" name="timingsendapp" id="timingsendapp1"  value="">
										<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" data-options="disabled:false" onclick="deleteTimingsendappRow(1);return false;"></a>
									</td>
								</tr>
							</c:if>
							</tbody>
						</table>
					</td>
				</tr>
			</c:if>
			<tr>
				<td>描述：</td>
				<td><textarea rows="3" cols="41" name="description" style="font-size: 12px;font-family: '微软雅黑'">${user.description}</textarea></td>
			</tr>


		</table>
	</form>
</div>
<input type="hidden" id="disOrEnabled"  value="disabled"/>
<script type="text/javascript">
	var action="${action}";
	//用户 添加修改区分
	if(action=='create'){
		$("input[name='gender'][value=1]").attr("checked",true);
		//用户名存在验证
		$('#loginName').validatebox({
			required: true,
			validType:{
				length:[2,20],
				remote:["${ctx}/system/user/checkLoginName","loginName"]
			},
			invalidMessage:'該用戶已經存在'
		});
	}else if(action=='update'){
		$("input[name='loginName']").attr('readonly','readonly');
		$("input[name='loginName']").css('background','#eee')
		$("input[name='gender'][value=${user.gender}]").attr("checked",true);
		$.ajax({
			type:"GET",
			/*async: false,*/
			dataType:'json',
			url:"${ctx}/system/dict/getDictByType/dict_emailSet",
			success: function (data) {
				var html = '';
				if ($("#emailset").val() == null || $("#emailset").val() == '') {
					$.each(data, function (i, v) {
						html += "<input type='radio' value='" + v.value + "'  name='emailsetName' onclick=changeEmailSetPc() />" + v.label + "";
					})
				}else {
					var str = $("#emailset").val();
					$.each(data, function (n, item) {
						var flag=false;
						if (str== item.value) {
							html += "<input type='radio'  checked='checked'  value='" + item.value + "' name='emailsetName' onclick=changeEmailSetPc() />" + item.label + "";
							flag=true;
						}
						if(!flag)
							html += "<input type='radio'  value='" + item.value + "' name='emailsetName' onclick=changeEmailSetPc() />" + item.label + "";
					});
				}
				$(".emailsetDiv").html(html);
				changeEmailSetPc();
			}
		});
		$.ajax({
			type:"GET",
			/*async: false,*/
			dataType:'json',
			url:"${ctx}/system/dict/getDictByType/dict_emailSet",
			success: function (data) {
				var html = '';
				if ($("#emailsetapp").val() == null || $("#emailsetapp").val() == '') {
					$.each(data, function (i, v) {
						html += "<input type='radio' value='" + v.value + "' name='emailsetappName' onclick=changeEmailSetApp() />" + v.label + "";
					})
				}else {
					var str = $("#emailsetapp").val();
					$.each(data, function (n, item) {
						var flag=false;
						if (str== item.value) {
							html += "<input type='radio'  checked='checked'  value='" + item.value + "' name='emailsetappName' onclick=changeEmailSetApp() />" + item.label + "";
							flag=true;
						}
						if(!flag)
							html += "<input type='radio'  value='" + item.value + "' name='emailsetappName' onclick=changeEmailSetApp() />" + item.label + "";
					});
				}
				$(".emailsetappDiv").html(html);
				changeEmailSetApp();
			}
		});

	}

	//提交表单
	$('#mainform').form({
		onSubmit: function(){
			var isValid = $(this).form('validate');
			if(isValid==true){
				isValid = validTimingSend();
				if(isValid==true){
					isValid = validTimingSendapp();
				}
			}
			if(isValid==true){
				if($('#emailsetapp').val()==null || $('#emailsetapp').val()==''){
					if($('#emailset').val()!=null && $('#emailset').val()!=''){
						$('#emailsetapp').val($('#emailset').val());
						$("input[name=timingsendapp]").val(timingsenddValue());
						$("input[name=timingperiodapp]").val(timingperioddValue());
					}
				}
			}
			return isValid;	// 返回false终止表单提交

		},
		success:function(data){
			successTip(data,dg,d);
		}
	});
	function setRadioValue(name,id){
		var chk_value = [];
		$('input[name="'+name+'"]:checked').each(function () {
			chk_value.push($(this).val());
		})
		var chk_values = '';
		$.each(chk_value, function (index, value) {
			chk_values += value + ',';
		})
		var chk_vals = chk_values.substring(0, chk_values.length - 1);
		$('#'+id).val(chk_vals);
	}
	function changeEmailSetPc(){
		setRadioValue('emailsetName','emailset');
		if($('#emailset').val()=='1'||$('#emailset').val()==''){
			addTimingsend('cut');
			deleteTimingsendAll();
			addTimingperiod('cut');
			deleteTimingperiodAll();
			$("input[name=timingsendpoint]").timespinner('readonly', true);
			$("input[name=timingperiodStart]").timespinner("readonly", true);
			$("input[name=timingperiodEnd]").timespinner("readonly", true);
		}else if($('#emailset').val()=='0'){
			$("input[name=timingsendpoint]").timespinner('readonly', false);
			$("input[name=timingperiodStart]").timespinner('readonly', false);
			$("input[name=timingperiodEnd]").timespinner('readonly', false);
		}
		/*changePcIsnotApp();*/
	}
	function changeEmailSetApp(){
		setRadioValue('emailsetappName','emailsetapp');
		if($('#emailsetapp').val()=='1'||$('#emailsetapp').val()==''){
			addTimingsendapp('cut');
			deleteTimingsendappAll();
			addTimingperiodapp('cut');
			deleteTimingperiodappAll();
			$("input[name=timingsendapppoint]").timespinner('readonly', true);
			$("input[name=timingperiodappStart]").timespinner("readonly", true);
			$("input[name=timingperiodappEnd]").timespinner("readonly", true);
		}else if($('#emailsetapp').val()=='0'){
			$("input[name=timingsendapppoint]").timespinner('readonly', false);
			$("input[name=timingperiodappStart]").timespinner('readonly', false);
			$("input[name=timingperiodappEnd]").timespinner('readonly', false);
		}
	}
	//首次設置時同步app
	/*function changePcIsnotApp(){
		if($('#emailsetapphidden').val()==''){
			$("input[name='emailsetappName'][value='"+$('#emailset').val()+"']").prop("checked",true);
		}
	}*/
	function validTimingSend() {
		var chk_value = [],chk_start_value=[],chk_end_value=[];
		var validflag=true;
		var emilset=$('#emailset').val();
		$('input[name="timingsendpoint"]').each(function () {
			if($(this).val()!='') {
				chk_value.push($(this).val());
			}
		})
		$('input[name="timingperiodStart"]').each(function () {
			if($(this).val()!='') {
				chk_start_value.push($(this).val());
			}
		})
		$('input[name="timingperiodEnd"]').each(function () {
			if($(this).val()!='') {
				chk_end_value.push($(this).val());
			}
		})
		if(emilset=='0'){
			if(chk_value.length == 0 && chk_start_value.length==0 && chk_end_value.length==0){
				$.messager.alert("溫馨提示", "PC端定時段/定點至少選擇一項", "info");
				validflag = false;
			}
			if(chk_start_value.length!=chk_end_value.length){
				$.messager.alert("溫馨提示", "PC端定時段請填寫完成", "info");
				validflag = false;
			}
			var cont=0;
			$('input[name="timingperiod"]').each(function () {
				var start=$(this).val().split('-')[0];
				var end=$(this).val().split('-')[1];
				if(end<start){
					cont++;
				}
			})
			if(cont>0){
				$.messager.alert("溫馨提示", "PC端定時段結束日期不能小於開始日期", "info");
				validflag = false;
			}
		}
		/*if (chk_value.length == 0 && emilset=='0') {
			$.messager.alert("溫馨提示", "PC端至少添加一項定點發送時間", "info");
			validflag = false;
		}else if(emilset=='0'){
			validflag = true;
		}*/
		return validflag;
	}
	function timingsenddValue() {
		var chk_value = [];
		$('input[name="timingsend"]').each(function() {
			if($(this).val()!=''){
				chk_value.push($(this).val());
			}
		});
		var chk_values='',chk_vals = '';
		if (chk_value.length == 0) {

		} else {
			$.each(chk_value, function(index, value) {
				chk_values += value + ',';
			});
			chk_vals = chk_values.substring(0, chk_values.length - 1);
		}
		return chk_vals;
	}
	function timingperioddValue() {
		var chk_value = [];
		$('input[name="timingperiod"]').each(function() {
			if($(this).val()!=''){
				chk_value.push($(this).val());
			}
		});
		var chk_values='',chk_vals = '';
		if (chk_value.length == 0) {

		} else {
			$.each(chk_value, function(index, value) {
				chk_values += value + ',';
			});
			chk_vals = chk_values.substring(0, chk_values.length - 1);
		}
		return chk_vals;
	}
	function validTimingSendapp() {
		var chk_valueapp = [],chk_start_valueapp=[],chk_end_valueapp=[];
		var validflagapp=true;
		var emilsetapp=$('#emailsetapp').val();
		$('input[name="timingsendapppoint"]').each(function () {
			if($(this).val()!=''){
				chk_valueapp.push($(this).val());
			}
		})
		$('input[name="timingperiodappStart"]').each(function () {
			if($(this).val()!='') {
				chk_start_valueapp.push($(this).val());
			}
		})
		$('input[name="timingperiodappEnd"]').each(function () {
			if($(this).val()!='') {
				chk_end_valueapp.push($(this).val());
			}
		})
		if(emilsetapp=='0'){
			if(chk_valueapp.length == 0 && chk_start_valueapp.length==0 && chk_end_valueapp.length==0){
				$.messager.alert("溫馨提示", "APP端定時段/定點至少選擇一項", "info");
				validflagapp = false;
			}
			if(chk_start_valueapp.length!=chk_end_valueapp.length){
				$.messager.alert("溫馨提示", "APP端定時段請填寫完成", "info");
				validflagapp = false;
			}
			var cont=0;
			$('input[name="timingperiodapp"]').each(function () {
				var start=$(this).val().split('-')[0];
				var end=$(this).val().split('-')[1];
				if(end<start){
					cont++;
				}
			})
			if(cont>0){
				$.messager.alert("溫馨提示", "APP端定時段結束日期不能小於開始日期", "info");
				validflagapp = false;
			}
		}
		/*if (chk_valueapp.length == 0 && emilsetapp=='0') {
			$.messager.alert("溫馨提示", "APP端至少添加一項定點發送時間", "info");
			validflagapp = false;
		}else if(emilsetapp=='0'){
			validflagapp = true;
		}*/
		return validflagapp;
	}

	function addTimingsend(cut) {
		var flag = true;
		if ($("#info_Body_ip").children().length == 8) {
			parent.$.messager.alert("最多添加8个！");
			return;
		}
		if(cut=="cut"){

		}else{
			$("input[name='timingsendpoint']").each(function () {
				if ($(this).val() == null || $(this).val() == '') {
					$.messager.alert("溫馨提示", "請填寫完本行後再添加", "info");
					flag = false;
					$(this).focus();
					return false;
				}
			});
		}

		if(flag){
			var len = parseInt($("#info_Body_ip tr:last").attr("id").replace("timingsendItem", "")) + 1;
			var strVar = "";
			strVar += "  <tr id=\"timingsendItem" + (len) + "\"><td>";
			strVar += "        <a href=\"javascript:void(0)\"";
			strVar += "           class=\"easyui-linkbutton\" iconCls=\"icon-add\" plain=\"true\" onclick=\"addTimingsend();\"";
			strVar += "         ><\/a>";
			strVar += "        <input id=\"timingsendpoint" + (len) + "\" name=\"timingsendpoint\"";
			strVar += "               class=\"easyui-timespinner\" onblur=\"chgTimingsend(" + (len) + ");\"";
			strVar += "               style=\"width:80px\"";
			strVar += "               value=\"\"\/>";
			strVar += "        <input type=\"hidden\" id=\"timingsend" + (len) + "\" name=\"timingsend\"";
			strVar += "               value=\"\"\/>";
			strVar += "        <a href=\"javascript:void(0)\"";
			strVar += "           class=\"easyui-linkbutton\" iconCls=\"icon-remove\" plain=\"true\" onclick=\"deleteTimingsendRow(" + (len) + ");return false;\"";
			strVar += "           data-options=\"disabled:false\"><\/a>";
			strVar += "  <\/td><\/tr>";
			$("#info_Body_ip").append(strVar);
			$.parser.parse($("#info_Body_ip"));
		}
	}
	function deleteTimingsendRow(index) {
		$.messager.confirm("操作提示", "您确定要删除该记录吗？", function (data) {
			if (data) {
				if ($("#info_Body_ip").children().length == 1) {
					parent.$.messager.alert("至少保留一筆！");
					return;
				}
				$("#timingsendItem" + index).remove();
			}
		});
	}
	//變更刪除全部
	function deleteTimingsendAll() {
		$("#info_Body_ip").find("tr:not(:last)").remove();
	}
	function addTimingperiod(cut) {
		var flag = true;
		if ($("#info_Body_ip2").children().length == 4) {
			parent.$.messager.alert("最多添加4个！");
			return;
		}
		if(cut=="cut"){

		}else{
			$("input[name='timingperiodStart']").each(function () {
				if ($(this).val() == null || $(this).val() == '') {
					$.messager.alert("溫馨提示", "請填寫完本行開始日期再添加", "info");
					flag = false;
					$(this).focus();
					return false;
				}
			});
			$("input[name='timingperiodEnd']").each(function () {
				if ($(this).val() == null || $(this).val() == '') {
					$.messager.alert("溫馨提示", "請填寫完本行結束日期再添加", "info");
					flag = false;
					$(this).focus();
					return false;
				}
			});
		}

		if(flag){
			var len = parseInt($("#info_Body_ip2 tr:last").attr("id").replace("timingperiodItem", "")) + 1;
			var strVar = "";
			strVar += "  <tr id=\"timingperiodItem" + (len) + "\"><td>";
			strVar += "        <a href=\"javascript:void(0)\"";
			strVar += "           class=\"easyui-linkbutton\" iconCls=\"icon-add\" plain=\"true\" onclick=\"addTimingperiod();\"";
			strVar += "         ><\/a>";
			strVar += "        <input id=\"timingperiodStart" + (len) + "\" name=\"timingperiodStart\"";
			strVar += "               class=\"easyui-timespinner\"";
			strVar += "               style=\"width:80px\" onblur=\"chgTimingperiod(" + (len) + ");\"";
			strVar += "               value=\"\"\/> — ";
			strVar += "        <input id=\"timingperiodEnd" + (len) + "\" name=\"timingperiodEnd\"";
			strVar += "               class=\"easyui-timespinner\"";
			strVar += "               style=\"width:80px\" onblur=\"chgTimingperiod(" + (len) + ");\"";
			strVar += "               value=\"\"\/>";
			strVar += "        <input type=\"hidden\" id=\"timingperiod" + (len) + "\" name=\"timingperiod\"";
			strVar += "               value=\"\"\/>";
			strVar += "        <a href=\"javascript:void(0)\"";
			strVar += "           class=\"easyui-linkbutton\" iconCls=\"icon-remove\" plain=\"true\" onclick=\"deleteTimingperiodRow(" + (len) + ");return false;\"";
			strVar += "           data-options=\"disabled:false\"><\/a>";
			strVar += "  <\/td><\/tr>";
			$("#info_Body_ip2").append(strVar);
			$.parser.parse($("#info_Body_ip2"));
		}
	}
	function deleteTimingperiodRow(index) {
		$.messager.confirm("操作提示", "您确定要删除该记录吗？", function (data) {
			if (data) {
				if ($("#info_Body_ip2").children().length == 1) {
					parent.$.messager.alert("至少保留一筆！");
					return;
				}
				$("#timingperiodItem" + index).remove();
			}
		});
	}
	//變更刪除全部
	function deleteTimingperiodAll() {
		$("#info_Body_ip2").find("tr:not(:last)").remove();
	}
	function chgTimingperiod(index) {
		var timingperiodStart = $('#timingperiodStart'+index).timespinner('getValue');
		var timingperiodEnd = $('#timingperiodEnd'+index).timespinner('getValue');
		$('#timingperiod'+index).val(timingperiodStart + "-" + timingperiodEnd)
	}
	function chgTimingsend(index){
		$('#timingsend'+index).val($('#timingsendpoint'+index).timespinner('getValue'));
	}
	function chgTimingsendapp(index){
		$('#timingsendapp'+index).val($('#timingsendapppoint'+index).timespinner('getValue'));
	}
	/*--------app----------*/
	function addTimingsendapp(cut) {
		var flag = true;
		if ($("#info_Body_ipapp").children().length == 8) {
			parent.$.messager.alert("最多添加8个！");
			return;
		}
		if(cut=="cut"){

		}else{
			$("input[name='timingsendapppoint']").each(function () {
				if ($(this).val() == null || $(this).val() == '') {
					$.messager.alert("溫馨提示", "請填寫完本行後再添加", "info");
					flag = false;
					$(this).focus();
					return false;
				}
			});
		}

		if(flag){
			var len = parseInt($("#info_Body_ipapp tr:last").attr("id").replace("timingsendappItem", "")) + 1;
			var strVar = "";
			strVar += "  <tr id=\"timingsendappItem" + (len) + "\"><td>";
			strVar += "        <a href=\"javascript:void(0)\"";
			strVar += "           class=\"easyui-linkbutton\" iconCls=\"icon-add\" plain=\"true\" onclick=\"addTimingsendapp();\"";
			strVar += "         ><\/a>";
			strVar += "        <input id=\"timingsendapppoint" + (len) + "\" name=\"timingsendapppoint\"";
			strVar += "               class=\"easyui-timespinner\" onblur=\"chgTimingsendapp(" + (len) + ");\"";
			strVar += "               style=\"width:80px\"";
			strVar += "               value=\"\"\/>";
			strVar += "        <input type=\"hidden\" id=\"timingsendapp" + (len) + "\" name=\"timingsendapp\"";
			strVar += "               value=\"\"\/>";
			strVar += "        <a href=\"javascript:void(0)\"";
			strVar += "           class=\"easyui-linkbutton\" iconCls=\"icon-remove\" plain=\"true\" onclick=\"deleteTimingsendappRow(" + (len) + ");return false;\"";
			strVar += "           data-options=\"disabled:false\"><\/a>";
			strVar += "  <\/td><\/tr>";
			$("#info_Body_ipapp").append(strVar);
			$.parser.parse($("#info_Body_ipapp"));
		}
	}
	function deleteTimingsendappRow(index) {
		$.messager.confirm("操作提示", "您确定要删除该记录吗？", function (data) {
			if (data) {
				if ($("#info_Body_ipapp").children().length == 1) {
					parent.$.messager.alert("至少保留一筆！");
					return;
				}
				$("#timingsendappItem" + index).remove();
			}
		});
	}
	//變更刪除全部
	function deleteTimingsendappAll() {
		$("#info_Body_ipapp").find("tr:not(:last)").remove();
	}
	function addTimingperiodapp(cut) {
		var flag = true;
		if ($("#info_Body_ipapp2").children().length == 4) {
			parent.$.messager.alert("最多添加4个！");
			return;
		}
		if(cut=="cut"){

		}else{
			$("input[name='timingperiodappStart']").each(function () {
				if ($(this).val() == null || $(this).val() == '') {
					$.messager.alert("溫馨提示", "請填寫完本行開始日期再添加", "info");
					flag = false;
					$(this).focus();
					return false;
				}
			});
			$("input[name='timingperiodappEnd']").each(function () {
				if ($(this).val() == null || $(this).val() == '') {
					$.messager.alert("溫馨提示", "請填寫完本行結束日期再添加", "info");
					flag = false;
					$(this).focus();
					return false;
				}
			});
		}

		if(flag){
			var len = parseInt($("#info_Body_ipapp2 tr:last").attr("id").replace("timingperiodappItem", "")) + 1;
			var strVar = "";
			strVar += "  <tr id=\"timingperiodappItem" + (len) + "\"><td>";
			strVar += "        <a href=\"javascript:void(0)\"";
			strVar += "           class=\"easyui-linkbutton\" iconCls=\"icon-add\" plain=\"true\" onclick=\"addTimingperiodapp();\"";
			strVar += "         ><\/a>";
			strVar += "        <input id=\"timingperiodappStart" + (len) + "\" name=\"timingperiodappStart\"";
			strVar += "               class=\"easyui-timespinner\"";
			strVar += "               style=\"width:80px\" onblur=\"chgTimingperiodapp(" + (len) + ");\"";
			strVar += "               value=\"\"\/> — ";
			strVar += "        <input id=\"timingperiodappEnd" + (len) + "\" name=\"timingperiodappEnd\"";
			strVar += "               class=\"easyui-timespinner\"";
			strVar += "               style=\"width:80px\" onblur=\"chgTimingperiodapp(" + (len) + ");\"";
			strVar += "               value=\"\"\/>";
			strVar += "        <input type=\"hidden\" id=\"timingperiodapp" + (len) + "\" name=\"timingperiodapp\"";
			strVar += "               value=\"\"\/>";
			strVar += "        <a href=\"javascript:void(0)\"";
			strVar += "           class=\"easyui-linkbutton\" iconCls=\"icon-remove\" plain=\"true\" onclick=\"deleteTimingperiodappRow(" + (len) + ");return false;\"";
			strVar += "           data-options=\"disabled:false\"><\/a>";
			strVar += "  <\/td><\/tr>";
			$("#info_Body_ipapp2").append(strVar);
			$.parser.parse($("#info_Body_ipapp2"));
		}
	}
	function deleteTimingperiodappRow(index) {
		$.messager.confirm("操作提示", "您确定要删除该记录吗？", function (data) {
			if (data) {
				if ($("#info_Body_ipapp2").children().length == 1) {
					parent.$.messager.alert("至少保留一筆！");
					return;
				}
				$("#timingperiodappItem" + index).remove();
			}
		});
	}
	//變更刪除全部
	function deleteTimingperiodappAll() {
		$("#info_Body_ipapp2").find("tr:not(:last)").remove();
	}
	function chgTimingperiodapp(index) {
		var timingperiodappStart = $('#timingperiodappStart'+index).timespinner('getValue');
		var timingperiodappEnd = $('#timingperiodappEnd'+index).timespinner('getValue');
		$('#timingperiodapp'+index).val(timingperiodappStart + "-" + timingperiodappEnd)
	}
</script>
</body>
</html>
