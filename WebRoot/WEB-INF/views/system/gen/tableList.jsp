<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>代碼生成</title>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="genweb();">web生成</a>
    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="genlocal();">本地生成</a>
</div>
<table id="dg"></table>
<div id="dlg"></div>
</body>
<script type="text/javascript">
    var dg;
    var d;
    $(function () {
        dg = $('#dg').datagrid({
            method: "get",
            url: '${ctx}/sys/generator/list',
            fit: true,
            fitColumns: true,
            border: false,
            striped: true,
            cache: false,  //关闭AJAX相应的缓存
            pagination: true,
            rownumbers: true,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: false,
            columns: [[
                {field: 'tableName', title: '表名', sortable: true, width: 100}
                ,
                {field: 'comments', title: '注釋', sortable: true, width: 100}
            ]],
            enableHeaderClickMenu: true,
            enableHeaderContextMenu: true,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });
    });

    function genweb() {
        var rowIsSelect = dg.datagrid('getSelected');
        if (rowIsNull(rowIsSelect)) return;
        var row = dg.datagrid('getSelections');
        var ids = new Array();
        for(var i=0;i<row.length;i++){
            ids.push(row[i].tableName);
        }
        window.location.href = "${ctx}/sys/generator/code?genType=1&tables=" + JSON.stringify(ids);
    }
    function genlocal() {
        var rowIsSelect = dg.datagrid('getSelected');
        if (rowIsNull(rowIsSelect)) return;
        var row = dg.datagrid('getSelections');
        var ids = new Array();
        for(var i=0;i<row.length;i++){
            ids.push(row[i].tableName);
        }
        $.ajax({
            type: 'get',
            url: "${ctx}/sys/generator/code?genType=0&tables=" + JSON.stringify(ids),
            success: function (data) {
                successTip(data, dg);
            }
        });
    }
</script>
</html>
