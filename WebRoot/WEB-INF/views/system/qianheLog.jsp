<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title></title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>

</head>
<body style="font-family: '微软雅黑'">
<table id="dg" style="width:auto"></table>
<script type="text/javascript">
    var dg;
    $(function () {
        dg = $('#dg').datagrid({
            method: "get",
            url: '${ctx}/wfcontroller/queryChargeLog?filter_EQS_serialno=${serialno}&rows=2000&random=<%= Math.random()%>',
            fit: false,
            fitColumns: true,
            border: false,
            striped: true,
            cache: false,  //关闭AJAX相应的缓存
            idField: 'id',
            scrollbarSize: 0,
            pagination: false,
            rownumbers: true,
            nowrap:false,
            singleSelect: true,
            columns: [[
                {field: 'id', title: 'id', hidden: true},
                {field: 'createDate', title: '簽核時間', sortable: true, width: 80},
                {field: 'chargenode', title: '簽核節點', sortable: true, width: 120},
                {field: 'chargename', title: '簽核主管', sortable: true, width: 50},
                {field: 'ispass', title: '簽核意見', sortable: true},
                {
                    field: 'decrib', title: '批註', sortable: true, width: 150, formatter: function (value, row, index) {
                    if (value != null || value != undefined && value != '') {
                        return '<font style="color:#297dd1;cursor:pointer;word-break:break-all;word-wrap:break-word;white-space:pre-wrap;"><span title="' + value + '">' + value.replace("(", "<strong><span style=\'color:#F00\'>(").replace(")", ")</span></strong>").replace("（", "<strong><span style=\'color:#F00\'>(").replace("）", ")</span></strong>").replace("《", "<strong><span style=\'color:#F00\'>(").replace("》", ")</span></strong>") + '</span></font>';
                    } else {
                        return value;
                    }
                }
                },
                {field: 'operateip', title: '簽核電腦IP', sortable: true, width: 80}
            ]],
            rowStyler: function (index, row) {
                if (row.ispass == '駁回'||row.ispass == '體檢不合格') {
                    return 'background-color:pink;color:blue;font-weight:bold;';
                }
            },
            enableHeaderClickMenu: false,
            enableHeaderContextMenu: false,
            enableRowContextMenu: false,
            onLoadSuccess: function (data) {
//                var heightPlus = 0;//datagrid的高度
//                heightPlus += data.rows.length * 25;
//                heightPlus += 1 * 42;//加表格标题36，加操作栏36
                var heightPlus = 0;
                var i= 0;
//datagrid的高度
                $(".datagrid-btable").each(function () {
                    if ($(this).index() == 0&&i==1) {
                        $(this).children().children("tr").each(function () {
                            heightPlus += $(this).height();
                        })
                    }
                    i++;
//                    return false;
                });
                heightPlus += 1 * 42;//加表格标题36，加操作栏36
                $(this).datagrid("clearSelections");
                reSetIframeHeight(heightPlus);
            }
        });
    });

    var reSetIframeHeight = function (heightPlus) {
        try {
            var oIframe = parent.document.getElementById('qianheLogFrame');
//            oIframe.height = 100;
            iframeLoaded(oIframe, heightPlus);
        }
        catch (err) {
            try {
                parent.document.getElementById(window.name).height = 1000;
            } catch (err2) {
            }
        }
    }

    function iframeLoaded(iframe, heightPlus) {
        if (iframe) {
            var iframeWin = iframe.contentWindow || iframe.contentDocument.parentWindow;
            if (iframeWin.document.body) {
//                iframe.height = (iframeWin.document.documentElement.scrollHeight || iframeWin.document.body.scrollHeight || heightPlus);
                iframe.height = heightPlus;
            }
        }
    };
</script>
</body>
</html>
