<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>${comments}</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/sysftpsipcontrol/${action}" method="post">
    <input type="hidden" name="id" value="${ftps.id }"/>
    <table class="formTable">
        <tr>
            <td>IP地址：</td>
            <td>
                <input name="ipAdress" id="ipAdress" data-options="width: 200,required:'required',prompt:'xx.xx.xx.xx',validType:'ip[\'ipAdress\']'" class="easyui-validatebox" value="${ftps.ipAdress }"/>
            </td>
        </tr>
        <tr>
            <td>工號：</td>
            <td>
                <input id="empno" name="empno" class="easyui-validatebox" onblur="queryUserInfo()"
                       data-options="width: 200,required:'required',prompt:'工號自動帶出姓名'" value="${ftps.empno }">
            </td>
        </tr>
        <tr>
            <td>姓名：</td>
            <td><input id="name" name="name" class="easyui-validatebox" readonly
                       data-options="width: 200" value="${ftps.name }"/></td>
        </tr>
        <tr>
            <td>角色名稱：</td>
            <td><input name="roleId" id="roleId" class="easyui-combobox"/></td>
        </tr>
        <tr>
            <td>備註：</td>
            <td><textarea rows="4" name="remark"
                          style="font-size: 12px;font-family: '微软雅黑';width: 270px">${ftps.remark}</textarea>
            </td>
        </tr>
    </table>
</form>
<script type="text/javascript">
    $(function () {
        $('#mainform').form({
            onSubmit: function () {
                var isValid = $(this).form('validate');
                return isValid;	// 返回false终止表单提交
            },
            success: function (data) {
                successTip(data, dg, d);
            }
        });
        $("#roleId").combobox({
            editable: true,
            required:true,
            width:200,
            method: "get",
            url: '${ctx}/system/role/allSlectData',
            valueField: 'id',
            textField: 'name',
            onLoadSuccess: function () {
                if (${ftps.roleId!=null}) {
                    $(this).combobox("select",${ftps.roleId});
                }else{
                    var data = $('#roleId').combobox('getData');
                    $(this).combobox('select', data[0].id);
                }
            },
            filter: function(q, row){
                var opts = $(this).combobox('options');
                return row[opts.textField].indexOf(q) > -1;
            }
        });
    });
    function queryUserInfo() {
        var empno = $.trim($("#empno").val().toUpperCase());
        if (empno != null && empno != "") {
            $.ajax({
                url: ctx+'/system/user/getUserInfo/',
                type: 'POST',
                dataType:'json',
                data: {empno: empno},
                success: function (data) {
                    if (!data) {
                        $.parent.messager.alert("溫馨提示", "工號不存在", "error");
                        $('#empno').val('');
                    } else {
                        $('#name').val(data.empname);
                    }
                }
            });
        }
    }
</script>
</body>
</html>