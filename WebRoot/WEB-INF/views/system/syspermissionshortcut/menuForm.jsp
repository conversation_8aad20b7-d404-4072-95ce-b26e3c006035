<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title></title>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<div>
	<form id="mainform" action="${ctx}/syspermissionshortcut/${action}" method="post">
	<table  class="formTable">
		<tr>
			<td>菜单名称：</td>
			<td>
				<input type="hidden" name="id" value="${permission.id }"/>
				<input type="hidden" name="type" value="F"/>
				<input id="name" name="name" type="text" value="${permission.name }" class="easyui-validatebox" data-options="width: 180,required:'required',validType:'length[2,20]'" readonly/>
			</td>
		</tr>
		<tr>
			<td>菜单路径：</td>
			<td><input id="url" name="url" type="text" value="${permission.url }" class="easyui-validatebox"  data-options="width: 180" class="easyui-validatebox" readonly/></td>
			<input id="icon" name="icon" value="${permission.icon }" type="hidden"/>
			<input id="pid" name="pid" value="${permission.pid }" type="hidden"/>
			<input id="userId" name="userId" value="${permission.userId }" type="hidden"/>
			<input id="accordionName" name="accordionName" value="${permission.accordionName }" type="hidden"/>
			<input id="accordionId" name="accordionId" value="${permission.accordionId }" type="hidden"/>
		</tr>
		<tr>
			<td>排序：</td>
			<td><input id="sort" type="text" name="sort" value="${permission.sort }" class="easyui-numberbox" data-options="width: 180" /></td>
		</tr>
		<tr>
			<td>描述：</td>
			<td><textarea rows="3" cols="41" name="description">${permission.description}</textarea></td>
		</tr>
	</table>
	</form>
</div> 
<script type="text/javascript">

$('#mainform').form({    
    onSubmit: function(){    
    	var isValid = $(this).form('validate');
		return isValid;	// 返回false终止表单提交
    },    
    success:function(data){   
    	if(successTip(data,dg,d)){
			parent.$("#shortcut").tree('reload');
			dg.treegrid('reload');
		}
    }
});   

</script>
</body>
</html>