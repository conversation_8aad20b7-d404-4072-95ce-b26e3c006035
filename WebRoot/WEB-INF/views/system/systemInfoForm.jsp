<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title></title>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>

</head>
<body>
<div>
	<form id="mainform" action="${ctx}/system/sysinfo/${action}" method="post">
	<table  class="formTable">
		<tr>
			<td>系統名称：</td>
			<td>
			<input type="hidden" name="id" value="${id }" data-options="required:false"/>
			<input name="name" type="text" value="${systemInfo.name}" class="easyui-validatebox"  data-options="required:'required',validType:['length[0,50]']" />
			</td>
		</tr>
		<tr>
			<td>系統分類：</td>
			<td><input id="superId" name="superId" type="text" value="${systemInfo.superId }" class="easyui-validatebox" data-options="required:'required',validType:['length[0,9]']" /></td>
		</tr>
	</table>
	</form>
</div>
<script type="text/javascript">
$(function(){
	
	//查詢類別
	$('#superId').combotree({
		width:180,
		method:'GET',
	    url: '${ctx}/system/sysinfo/json?filter_EQI_superId=-1',
	    idField : 'id',
	    textFiled : 'name',
		parentField : 'superId',
	    animate:true
	});  
	
	$('#mainform').form({    
	    onSubmit: function(){    
	    	/*var isValid = $(this).form('validate');
	    	console.log(isValid);
			return isValid;	// 返回false终止表单提交*/
	    	return true;
	    },    
	    success:function(data){   
	    	if(successTip(data,dg,d))
	    		dg.treegrid('reload');
	    }    
	}); 
});

</script>
</body>
</html>