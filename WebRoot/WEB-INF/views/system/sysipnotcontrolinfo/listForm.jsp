<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>ip不管控用戶信息</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/sysipnotcontrolinfo/${action}" method="post">
    <table class="formTable">
        <tr>
            <td>工號：</td>
            <td>
                <input type="hidden" name="id" value="${sysIpNotControlInfo.id }"/>
                <input name="empno" id="empno" type="text" style="width: 270px;" value="${sysIpNotControlInfo.empno }"
                       class="easyui-validatebox" required="required" onblur="getUserNameByEmpno(this);"/>
            </td>
        </tr>
        <tr>
            <td>ip地址：</td>
            <td><input name="ipAdress" type="text" style="width: 270px" value="${sysIpNotControlInfo.ipAdress }"
                       class="easyui-validatebox"/></td>
        </tr>
        <tr>
            <td>姓名：</td>
            <td><input name="name" id="name" type="text" style="width: 270px" value="${sysIpNotControlInfo.name}"
                       class="easyui-validatebox" readonly
                       required="required"/></td>
        </tr>
        <tr>
            <td>部門名稱：</td>
            <td><input name="deptname" type="text" style="width: 270px" value="${sysIpNotControlInfo.deptname}"
                       class="easyui-validatebox"
            /></td>
        </tr>
        <tr>
            <td>描述：</td>
            <td><textarea rows="4" name="describ"
                          style="font-size: 12px;font-family: '微软雅黑';width: 270px">${sysIpNotControlInfo.describ}</textarea>
            </td>
        </tr>
    </table>
</form>
</div>
<script type="text/javascript">
    $(function () {
        $('#mainform').form({
            onSubmit: function () {
                var isValid = $(this).form('validate');
                return isValid;	// 返回false终止表单提交
            },
            success: function (data) {
                successTip(data, dg, d);
            }
        });
    });
    function getUserNameByEmpno(obj) {
        if (obj.value != null && obj.value != "") {
            $.post(ctx + '/system/user/getUserInfo/', {
                empno: obj.value
            }, function (data) {
                if (!data) {
                    $("#empno").val("");
                    $("#name").val("");
                    $.messager.alert("溫馨提示", "工號不存在", "error");
                } else {
                    $("#name").val(data.empname);
                }
            }, 'json');
        }else{
            $("#empno").val("");
        }
    }
</script>
</body>
</html>