<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>ip不管控用戶信息</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/sysipnotcontrolinfo/${action}" method="post">
<!--
		        id 主鍵
    createBy 創建人
    createDate 創建時間
    updateBy 更新者
    updateDate 更新時間
    delFlag 刪除標識
    remark 備註
    empno 工號
    ipAdress ip地址
    name 姓名
    deptname 單位名稱
    describ 備註
		   -->
    <input id="ids" name="ids" type="hidden" value="${sysIpNotControlInfoEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${sysIpNotControlInfoEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">ip不管控用戶信息</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${sysIpNotControlInfoEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${sysIpNotControlInfoEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${sysIpNotControlInfoEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <fmt:formatDate value='${sysIpNotControlInfoEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：<span style="color:#999;">${sysIpNotControlInfoEntity.makerno}/${sysIpNotControlInfoEntity.makername}</span></div>
        <div class="clear"></div>
        <table  class="formList">
            <tr>
                <td>
                    <table class="formList">
                    </table>
                </td>
           </tr>
            <tr>
                <td>
                    <table class="formList">
                       <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${sysIpNotControlInfoEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/newEsign/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','ip不管控用戶信息');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${sysIpNotControlInfoEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                               <c:if test="${sysIpNotControlInfoEntity.workstatus!=null&&sysIpNotControlInfoEntity.workstatus==3}">
                                  <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                      style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                               </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/information/sysipnotcontrolinfo.js?random=<%= Math.random()%>'></script>
</body>
</html>