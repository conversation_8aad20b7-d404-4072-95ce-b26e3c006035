<%@ taglib prefix="shiro" uri="http://shiro.apache.org/tags" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title></title>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/highcharts/highcharts.js"></script>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body style="font-family: '微软雅黑'">
<div id="tb" style="padding:5px;height:auto">
    <label>表單名稱：</label>
    <select id="cc" class="easyui-combobox" style="width:200px;margin-top: 15px;">
        <option value="aa">津貼加減項申請單</option>
        <option>bitem2</option>
        <option>bitem3</option>
        <option>ditem4</option>
        <option>eitem5</option>
    </select>
    <input type="text" name="filter_GTD_complettime" class="easyui-my97" datefmt="yyyy"
           data-options="width:150,prompt: '年份'"/>
</div>
<div id="container" style="margin-top: 20px;"></div>
</body>
<script type="text/javascript">
    var chart = Highcharts.chart('container', {
        title: {
            text: '津貼加減項申請單'
        },
//        subtitle: {
//            text: '数据来源：foxconn.com'
//        },
        yAxis: {
            title: {
                text: '數量'
            }
        },
        legend: {
            layout: 'vertical',
            align: 'right',
            verticalAlign: 'middle'
        },
        xAxis: {
            type: 'datetime',
            dateTimeLabelFormats: {
                month: '%Y-%m'
            }
        },
        plotOptions: {
            series: {
                pointStart: Date.UTC(2019, 0, 1),
                pointIntervalUnit: 'month'
            }
        },
        series: [{
            name: '審核中',
            data: [43, 52, 57, 69, 97, 11, 13, 15,34,12,23,11]
        }, {
            name: '駁回',
            data: [24, 24, 29, 29, 32, 30, 38, 40,56,9,1,7]
        }, {
            name: '完成',
            data: [11, 17, 16, 19, 20, 24, 32, 39,1,2,3,19]
        }, {
            name: '取消',
            data: [null, null, 7, 12, 15, 2, 34, 3,7,8,9,24]
        }],
        responsive: {
            rules: [{
                condition: {
                    maxWidth: 500
                },
                chartOptions: {
                    legend: {
                        layout: 'horizontal',
                        align: 'center',
                        verticalAlign: 'bottom'
                    }
                }
            }]
        }
    });
</script>
</html>