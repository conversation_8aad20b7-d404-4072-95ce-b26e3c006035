<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title></title>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
	<style type="text/css">
		label.error{color: red;}
	</style>
</head>
<body>
	<div style="padding: 5px">
	<form id="mainform" action="${ctx }/system/user/updatePwd" method="post">
	<table>
		<tr>
			<td>原密码：</td>
			<td>
			<input type="hidden" name="id" value="${user.id }"/>
			<input id="oldPassword" name="oldPassword" type="password" class="required"/>
			</td>
		</tr>
		<tr>
			<td>密码：</td>
			<td><input id="plainPassword" name="plainPassword" type="password" class="easyui-validatebox"/></td>
		</tr>
		<tr>
			<td>确认密码：</td>
			<td><input id="confirmPassword" name="confirmPassword" type="password" class="easyui-validatebox" equalTo="#plainPassword"/></td>
		</tr>
		<tr>
			<td><input id="submit" type="submit" value="submit" style="display: none"/></td>
			<td></td>
		</tr>
	</table>
	</form>
</div>
<script>
$(function(){
	$("#oldPassword").focus();
	$.validator.addMethod("password111", function (value, element) {
		return /[0-9]+/.test(value) && /[a-zA-Z]+/.test(value) && /[~!@#￥%&*.?<>+_)(]+/.test(value);
	});
	$("#mainform").validate({
		rules: {
			oldPassword: {
				required: true,
				remote: "${ctx}/system/user/checkPwd"
			},
			plainPassword: {
				required: true,
				minlength: 8,
				password111: true
			},//required在此含义是必填
			confirmPassword: {
				required: true,
				minlength: 8,
				password111: true,
				equalTo: "#plainPassword"
			}
		},
		messages: {
			oldPassword: {
				required: "請輸入密碼",
				remote:"原密码错误"
			},
			plainPassword: {
				required: "請輸入密碼",
				minlength: "密碼長度不能小於8",
				password111: "密碼必須包含數字,字母和特殊字符【~!@#￥%&*.?<>+_)(】"
			},//required在此含义是必填
			confirmPassword: {
				required: "請輸入密碼",
				minlength: "密碼長度不能小於8",
				equalTo: "兩次密碼不一致",
				password111: "密碼必須包含數字,字母和特殊字符【~!@#￥%&*.?<>+_)(】"
			}
		},
		 submitHandler:function(form){
				$("#mainform").ajaxSubmit(function(data){
					 if(data=='success'){
//						 parent.$.messager.show({ title : "提示",msg: "操作成功！", position: "bottomRight" });
							parent.d.panel('close');
                         $.easyui.messager.confirm("操作提醒", "您已修改密碼成功，請點擊確定重新登錄？", function (c) {
                             if (c) {
                                 window.onbeforeunload = null;
                                 location.href='a/logout';
                             }else{
                                 window.onbeforeunload = null;
                                 location.href='a/logout';
							 }
                         });
						}
			   });
        }
	});

});
</script>
</body>
</html>
