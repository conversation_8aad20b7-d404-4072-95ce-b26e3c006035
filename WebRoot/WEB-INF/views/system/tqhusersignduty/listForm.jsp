<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>${comments}</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<div>
<form id="mainform" action="${ctx}/tqhusersignduty/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhUsersignduty.id }"/>
    <table class="formTable">
        <tr>
            <td>表單類型</td>
            <td>
                <input id="dutyid" name="dutyid" class="easyui-combobox" data-options="width: 150"
                       value="${tQhUsersignduty.dutyid }"/>
            </td>
        </tr>
        <tr>
            <td>廠區</td>
            <td>
                <input id="factoryid" name="factoryid" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhUsersignduty.factoryid }"/>
            </td>
        </tr>
        <tr>
            <td>工號：</td>
            <td>
                <input id="empno" name="empno" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhUsersignduty.empno }"/>
            </td>
        </tr>
        <tr>
            <td>備註：</td>
            <td>
                <input id="remarks" name="remarks" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhUsersignduty.remarks }"/>
            </td>
        </tr>
    </table>
</form>
</div>
<script src='${ctx}/static/js/system/tqhusersignduty.js?'+Math.random()></script>
<script type="text/javascript">
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            successTip(data, dg, d);
        }
    });
    $('#dutyid').combobox({
        prompt:'输入关键字自动检索',
        required:false,
        url:ctx+"/system/dict/getDictByType/user_sign_duty",
        editable:true,
        valueField: "value",
        textField: "label",
        hasDownArrow:true,
        filter: function(q, row){
            var opts = $(this).combobox('options');
            return row[opts.textField].toLowerCase().indexOf(q.toLowerCase()) != -1;
//            return row[opts.textField].indexOf(q) == 0;
        }
    });
</script>
</body>
</html>