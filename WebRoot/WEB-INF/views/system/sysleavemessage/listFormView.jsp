<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>用戶留言</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <%@ include file="/WEB-INF/views/include/kindeditor.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/sysleavemessage/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${sysLeaveMessageEntity.id }"/>
    <div class="commonW">
        <div class="headTitle">${sysLeaveMessageEntity.title}</div>
        <table class="formList">
            <tr align="center">
                <td width="20%">聯繫方式</td>
                <td width="80%" class="td_style1">${sysLeaveMessageEntity.contactWay}</td>
            </tr>
            <tr align="center">
                <td width="20%">附件</td>
                <td width="80%" class="td_style1">
                    <input type="hidden" id="attachids"
                           name="attachids" value="${sysLeaveMessageEntity.attachids }"/>
                    <div id="dowloadUrl">
                        <c:forEach items="${file}" varStatus="i" var="item">
                            <div id="${item.id}"
                                 style="line-height:30px;margin-left:5px;" class="float_L">
                                <div class="float_L">
                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                </div>
                            </div>
                        </c:forEach>
                    </div>
                </td>
            </tr>
            <tr align="center">
                <td width="20%">留言内容</td>
                <td width="80%" class="td_style1">
                    <div class="textarea" contenteditable="true" id="description_content"
                         style="max-width:1100px;overflow-x:scroll"/>
                    <input id="description" name="description" type="hidden"
                           value="${sysLeaveMessageEntity.description }"/>
                    <%--<textarea style="width:100%;" id="description" type="hidden"--%>
                    <%--name="description" class="dd">${sysLeaveMessageEntity.description}</textarea>--%>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<script type="text/javascript">
    //提交表单
    $(function () {
        $("#description_content").html($("#description").val());
    });
</script>
</body>
</html>