<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>用戶留言</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <style>
        .commonW {
            width: 100%;
        }

        .td_style1 {
            text-align: left;
        }

        .notice-type {
            padding-right: 10px;
        }
    </style>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <%--
<%@ include file="/WEB-INF/views/include/kindeditor.jsp" %>--%>
</head>
<body>
<form id="mainform" action="${ctx}/sysleavemessage/${action}" method="post">
    <!--
          createBy 創建人
createDate 創建時間
updateBy 更新者
updateDate 更新時間
delFlag 刪除標識
id 主鍵
title 标题
description 詳情
status 状态
startTime 有效開始時間
endTime 有效結束時間
type 公告類別
attachids 附件Id
      -->
    <input id="ids" name="ids" type="hidden" value="${sysLeaveMessageEntity.id }"/>
    <div class="commonW" style="width: 100%;">
        <div class="headTitle">系統留言</div>
        <table class="formList" align="center">
            <tr align="center">
                <td colspan="2">留言標題</td>
                <td colspan="8" class="td_style1">
                    <input id="title" style="width: 97%" name="title" class="easyui-validatebox"
                           value="${sysLeaveMessageEntity.title}"></input>
                </td>
            </tr>
            <tr align="center">
                <td colspan="2">聯繫方式</td>
                <td colspan="8" class="td_style1" style="text-align: left;">
                    <input id="contactWay" name="contactWay" class="easyui-validatebox"
                           style="width:300px;"
                           value="${sysLeaveMessageEntity.contactWay}"/>
                    <input id="channel" name="channel" type="hidden" value="PC"/>
                </td>
            </tr>
            <tr align="center">
                <td colspan="2">附件</td>
                <td colspan="8" class="td_style1" style="text-align: left;"><span
                        class="sl-custom-file"> <input type="button"
                                                       value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="oosUploadFile('','message');" class="ui-input-file"/>
								</span> <input type="hidden" id="attachids"
                                               name="attachids" value="${sysNoticeEntity.attachids }"/>
                    <div id="dowloadUrl">
                        <c:forEach items="${file}" varStatus="i" var="item">
                            <div id="${item.id}"
                                 style="line-height:30px;margin-left:5px;" class="float_L">
                                <div class="float_L">
                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                </div>
                                <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                            </div>
                        </c:forEach>
                    </div>
                </td>
            </tr>
            <tr align="center">
                <td colspan="2">留言内容</td>
                <td colspan="8" class="td_style1">
                <textarea style="width:100%;max-height:1000px;" id="description"
                          name="description" class="dd">${sysLeaveMessageEntity.description}</textarea>
                </td>
            </tr>
            <%-- <tr>
                 <td colspan="10" style="text-align:center;margin-top:10px">
                     <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                        data-options="iconCls:'icon-add'"
                        style="width: 100px;" onclick="add();">提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                 </td>
             </tr>--%>
        </table>
    </div>
	</form>
  </div>
<script src='${ctx}/static/js/system/sysleavemessage.js?random=<%= Math.random()%>'></script>
<script type="text/javascript">
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            successTip1(data, dg, d);
        }
    });
</script>
</body>
</html>