<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>APP端操作指南</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style>
        body {
            display: flex;
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }

        .video-player {
            flex: 2; /* 占据頁面寬度的2/3 */
            padding: 20px;
            text-align: center;
        }

        .video-list {
            flex: 1; /* 占据页面宽度的1/3 */
            padding: 20px;
            /*border-left: 1px solid #ccc;*/
            overflow-y: auto; /* 如果视频列表很长，允许垂直滚动 */
        }

        .video-list ul {
            padding-left: 0;
            margin-left: 0;
        }

        .video-item {
            margin-bottom: 10px;
            padding: 10px;
            border-bottom: 1px solid #eee; /* 添加分割线 */
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .video-item .title {
            flex: 3; /* 标题占据大部分空间 */
        }

        .video-item .duration {
            flex: 1; /* 时间占据小部分空间 */
            text-align: right;
        }

        .video-item a {
            text-decoration: none;
            color: #007BFF;
            display: block; /* 使整个<li>区域可点击 */
        }

        .video-item a:hover {
            text-decoration: underline;
        }

        .selected {
            color: #4FBCFE;
        }
    </style>
</head>
<body>
<div class="video-player">
    <h1 style="margin-bottom: 10px">視頻播放</h1>
    <h1 style="width:100%;border-bottom: 3px solid #4FBCFE;margin: 10px 0px"></h1>
    <video id="videoPlayer" width="100%" height="600px" controls>
        <source src="${ctx}/static/video/video1.mp4" type="video/mp4">
        您的瀏覽器不支持 video 標籤。
    </video>
    <!-- PDF查看器将插入到这里，如果点击的是PDF -->
    <div id="pdfViewerContainer" class="pdf-viewer" style="display:none;">
        <iframe id="pdfViewer" name="pdfiframe" width="100%" height="600px" frameborder="no" border="0" marginwidth="0"
                marginheight="0" scrolling="yes" allowtransparency="yes" style="display: inline;"
                src="${ctx}/static/plugins/PDFJSInNet/PDFJSInNet/web/viewer.html?file=https://fileiedu.foxconn.com/File/pdf/202208/7141212646812483.pdf">
        </iframe>
    </div>
</div>
<div class="video-list">
    <h1>&nbsp;</h1>
    <h1 style="margin-bottom: 10px">目錄</h1>
    <h1 style="width:80px;border-bottom: 3px solid #4FBCFE;margin: 0px;"></h1>
    <ul>
        <li class="video-item" data-type="video" data-src="${ctx}/static/video/video1.mp4">
            <span class="title">1.便易簽簡介</span>
            <span class="duration">00:43</span>
        </li>
        <li class="video-item" data-type="video" data-src="${ctx}/static/video/video2.mp4">
            <span class="title">2.便易簽安裝註冊</span>
            <span class="duration">01:47</span>
        </li>
        <li class="video-item" data-type="video" data-src="${ctx}/static/video/video3.mp4">
            <span class="title">3.便易簽常用設置</span>
            <span class="duration">01:33</span>
        </li>
        <li class="video-item" data-type="video" data-src="${ctx}/static/video/video4.mp4">
            <span class="title">4.表單呈簽</span>
            <span class="duration">01:34</span>
        </li>
        <li class="video-item" data-type="video" data-src="${ctx}/static/video/video5.mp4">
            <span class="title">5.表單簽核</span>
            <span class="duration">02:49</span>
        </li>
        <li class="video-item" data-type="video" data-src="${ctx}/static/video/video6.mp4">
            <span class="title">6.查詢下載</span>
            <span class="duration">01:18</span>
        </li>
        <li class="video-item" data-type="pdf" data-src="${ctx}/static/resources/download/manual.pdf">
            <span class="title">7.文檔教程</span>
        </li>
    </ul>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const videoPlayer = document.getElementById('videoPlayer');
        const pdfViewer = document.getElementById('pdfViewer');
        const pdfViewerContainer = document.getElementById('pdfViewerContainer');
        const videoItems = document.querySelectorAll('.video-item');
        // 移除所有已存在的 selected 类
        videoItems.forEach(item => item.classList.remove('selected'));

        videoItems.forEach(item => {
            item.addEventListener('click', function () {
                // 移除其他项的 selected 类
                videoItems.forEach(otherItem => otherItem.classList.remove('selected'));
                // 为当前项添加 selected 类
                this.classList.add('selected');
                const type = this.getAttribute('data-type');
                const src = this.getAttribute('data-src');
                if (type === 'video') {
                    videoPlayer.src = src;
                    videoPlayer.load();
                    videoPlayer.play();
                    pdfViewerContainer.style.display = 'none';
                    videoPlayer.style.display = 'block';
                } else if (type === 'pdf') {
                    videoPlayer.style.display = 'none';
                    pdfViewer.src = `${ctx}/static/plugins/PDFJSInNet/PDFJSInNet/web/viewer.html?file=` + src; // 假设服务器支持嵌入式PDF查看
                    pdfViewerContainer.style.display = 'block'; // 显示PDF查看器
                    videoPlayer.pause(); // 暂停可能正在播放的视频
                    videoPlayer.currentTime = 0; // 重置视频时间
                }
            });
        });
        if (videoItems.length > 0) {
            videoItems[0].click();
        }
    });
</script>
</body>
</html>