<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title></title>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script type="text/javascript" src="${ctx }/static/plugins/artTemplate/dist/template.js"></script>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <!--导入首页启动时需要的相应资源文件(首页相应功能的 js 库、css样式以及渲染首页界面的 js 文件)-->
    <script src="${ctx}/static/plugins/easyui/common/index.js" type="text/javascript"></script>
    <link href="${ctx}/static/plugins/easyui/common/index.css" rel="stylesheet"/>
    <script src="${ctx}/static/plugins/easyui/common/index-startup.js"></script>
    <style type="text/css">
        .btn-style-01 {
            border-style: none;
            padding: 4px 30px;
            line-height: 24px;
            color: #fff;
            font: 18px "Microsoft YaHei", Verdana, Geneva, sans-serif;
            cursor: pointer;
            border: 1px #ae7d0a solid;
            -webkit-box-shadow: inset 0px 0px 1px #fff;
            -moz-box-shadow: inset 0px 0px 1px #fff;
            box-shadow: inset 0px 0px 1px #fff; /*内发光效果*/
            -webkit-border-radius: 4px;
            -moz-border-radius: 4px;
            border-radius: 4px; /*边框圆角*/
            text-shadow: 1px 1px 0px #b67f01; /*字体阴影效果*/
            background-color: #feb100;
            background-image: -webkit-gradient(linear, 0 0%, 0 100%, from(#feb100), to(#e8a201));
            background-image: -webkit-linear-gradient(top, #feb100 0%, #e8a201 100%);
            background-image: -moz-linear-gradient(top, #feb100 0%, #e8a201 100%);
            background-image: -ms-linear-gradient(top, #feb100 0%, #e8a201 100%);
            background-image: -o-linear-gradient(top, #feb100 0%, #e8a201 100%);
            background-image: linear-gradient(top, #feb100 0%, #e8a201 100%); /*颜色渐变效果*/
        }

        .btn-style-01:hover {
            background-color: #e8a201;
            background-image: -webkit-gradient(linear, 0 0%, 0 100%, from(#e8a201), to(#feb100));
            background-image: -webkit-linear-gradient(top, #e8a201 0%, #feb100 100%);
            background-image: -moz-linear-gradient(top, #e8a201 0%, #feb100 100%);
            background-image: -ms-linear-gradient(top, #e8a201 0%, #feb100 100%);
            background-image: -o-linear-gradient(top, #e8a201 0%, #feb100 100%);
            background-image: linear-gradient(top, #e8a201 0%, #feb100 100%);
        }
        label.error{color: red;}
    </style>
</head>
<body>
<div id="mainLayout" class="easyui-layout" data-options="fit: true">
    <div id="northPanel" data-options="region: 'north', border: false" style="height: 107px; overflow: hidden;">
        <div id="topbar" class="top-bar">
            <div class="top-bar-left">
                <h1 style="margin-left: 20px; margin-top: 20px;"><span
                        style="color: #3F4752;font-size: 26px">iPEBG電子簽核平台</span>
                </h1>
            </div>
            <div class="top-bar-right">
                <div id="timerSpan"></div>
                <div id="themeSpan">
                    <a id="btnHideNorth" class="easyui-linkbutton"
                       data-options="plain: true, iconCls: 'layout-button-up'"></a>
                </div>
            </div>
            <div class="top-bar-right" style="right: 300px;width:500px;height: 72px;line-height: 72px"
                 text-align="center">
                <ul id="marquee" class="marquee">

                </ul>
                <%--<marquee direction="up" scrollamount=1 scrolldelay=10 behavior=sroll onmouseover=this.stop() onmouseout=this.start() width="300"><a href="#" onclick="window.parent.mainpage.mainTabs.addModule('公告詳情','${ctx}/sysnotice/view/a359a3add0dc4be8a8fbfcd6a9d43ed6','icon-hamburg-basket')"><font style="font-size: large;color: #A60000">滚动文字</font></a></marquee>--%>
            </div>
        </div>
        <div id="toolbar" class="panel-header panel-header-noborder top-toolbar">
            <div id="infobar">
                    <span class="icon-hamburg-user" style="padding-left: 25px; background-position: left center;">
                       ${user.name }，歡迎來到電子簽核平台
                    </span>
                <%--<span>&nbsp;&nbsp;<font color="red">當前在線人數：<% HashSet sessions = (HashSet)application.getAttribute("sessions");%><%=sessions.size()%></font></span>--%>
            </div>

            <div id="buttonbar">

            </div>
        </div>
    </div>
    <div data-options="region: 'west', title: '菜单导航栏', iconCls: 'icon-standard-map', split: true,collapsed: true, minWidth: 200, maxWidth: 400"
         style="width: 220px; padding: 1px;">
        <div id="RightAccordion" class="easyui-accordion" data-options="fit:true,border:false">

        </div>
    </div>
    <div data-options="region: 'center'">
        <div id="mainTabs_tools" class="tabs-tool">
            <table>
                <tr>
                    <td><a id="mainTabs_jumpHome" class="easyui-linkbutton easyui-tooltip" title="跳转至主页选项卡"
                           data-options="plain: true, iconCls: 'icon-hamburg-home'"></a></td>
                    <td>
                        <div class="datagrid-btn-separator"></div>
                    </td>
                    <td><a id="mainTabs_toggleAll" class="easyui-linkbutton easyui-tooltip" title="展开/折叠面板使选项卡最大化"
                           data-options="plain: true, iconCls: 'icon-standard-arrow-out'"></a></td>
                    <td>
                        <div class="datagrid-btn-separator"></div>
                    </td>
                    <td><a id="mainTabs_refTab" class="easyui-linkbutton easyui-tooltip" title="刷新当前选中的选项卡"
                           data-options="plain: true, iconCls: 'icon-standard-arrow-refresh'"></a></td>
                    <td>
                        <div class="datagrid-btn-separator"></div>
                    </td>
                    <td><a id="mainTabs_closeTab" class="easyui-linkbutton easyui-tooltip" title="关闭当前选中的选项卡"
                           data-options="plain: true, iconCls: 'icon-standard-application-form-delete'"></a></td>
                </tr>
            </table>
        </div>
        <div id="mainTabs" class="easyui-tabs"
             data-options="fit: true, border: false, showOption: true,enableNewTabMenu: true, tools: '#mainTabs_tools', enableJumpTabMenu: true">
            <div id="homePanel" data-options="title: '修改密碼', iconCls: 'icon-hamburg-home',refreshable: true">
                <div class="easyui-layout" data-options="fit: true">
                    <form id="mainform" action="${ctx }/system/user/updatePwdNew" method="post">
                        <table align="center" style="margin-top: 65px">
                            <tr>
                                <td colspan="2"><font color="#dc143c" size="3px"><strong>請修改默認密碼，否則無法使用系統，</br>修改後請牢記並使用新密碼登錄！</strong></font>
                                </td>
                            </tr>
                            <tr>
                                <td align="right" style="margin-top: 10px"><strong>密码：</strong></td>
                                <input type="hidden" name="id" value="${user.id }"/>
                                <td><input id="plainPassword" name="plainPassword" type="password" class="easyui-validatebox"/>
                                </td>
                            </tr>
                            <tr>
                                <td align="right"><strong>确认密码：</strong></td>
                                <td><input id="confirmPassword" name="confirmPassword" type="password"
                                           class="easyui-validatebox"/></td>
                            </tr>
                            <tr align="center">
                                <td colspan="2"><input class="btn-style-01" id="submit" type="submit" value="提   交" style="margin-top: 10px"/></td>
                            </tr>
                        </table>
                    </form>

                </div>
            </div>
        </div>
    </div>
    <div data-options="region: 'east', title: 'iPEBG電子簽核平台（舊）', iconCls: 'icon-standard-date', split: true,collapsed: true, minWidth: 160, maxWidth: 1000"
         style="width: 500px;">
        <div id="eastLayout" class="easyui-layout" data-options="fit: true">
            <div data-options="region: 'south', split: false, border: false" style="height: 220px;">
                <div class="easyui-calendar" data-options="fit: true, border: false"></div>
            </div>
            <div data-options="region: 'center', border: false" style="overflow: hidden;">
                <table id="myTask1"></table>
            </div>
        </div>
    </div>
    <div id="northPane2" data-options="region: 'south', border: false" style="overflow: hidden;">
        <div id="toolbar1" class="panel-header">
            <div id="infobar1" align="center">
                    <span style="padding-left: 25px; background-position: left center;font-size: 14px">
                       <%--CopyRight&copy;2018 中原資訊總處&nbsp;&nbsp;版權所有--%>
                       <fox:copyRight></fox:copyRight>
                    </span>
            </div>
        </div>
    </div>
</div>
<script>
    $(function () {
        $.validator.addMethod("password", function (value, element) {
            return /[0-9]+/.test(value) && /[a-zA-Z]+/.test(value) && /[~!@#￥%&*.?<>+_)(]+/.test(value);
        });
        $("#mainform").validate({
            rules: {
                plainPassword: {
                    required: true,
                    minlength: 8,
                    password: true
                },//required在此含义是必填
                confirmPassword: {
                    required: true,
                    minlength: 8,
                    password: true,
                    equalTo: "#plainPassword"
                }
            },
            messages: {
                plainPassword: {
                    required: "請輸入密碼",
                    minlength: "密碼長度不能小於8",
                    password: "密碼必須包含數字,字母和特殊字符【~!@#￥%&*.?<>+_)(】"
                },//required在此含义是必填
                confirmPassword: {
                    required: "請輸入密碼",
                    minlength: "密碼長度不能小於8",
                    equalTo: "兩次密碼不一致",
                    password: "密碼必須包含數字,字母和特殊字符【~!@#￥%&*.?<>+_)(】"
                }
            },
            submitHandler: function (form) {
                $("#mainform").ajaxSubmit(function (data) {
                    if (data == 'success') {
                        $.easyui.messager.confirm("操作提醒", "您已修改密碼成功，請點擊確定重新登錄？", function (c) {
                            if (c) {
                                location.href = '${ctx}/admin/login';
                            } else {
                                location.href = '${ctx}/admin/login';
                            }
                        });
                    }
                });
            }
        });
    });
</script>
</body>
</html>
