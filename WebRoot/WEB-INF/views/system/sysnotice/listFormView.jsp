<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>系統公告</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <%@ include file="/WEB-INF/views/include/kindeditor.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/sysnotice/${action}" method="post">
    <!--
          createBy 創建人
createDate 創建時間
updateBy 更新者
updateDate 更新時間
delFlag 刪除標識
id 主鍵
title 标题
description 詳情
status 状态
startTime 有效開始時間
endTime 有效結束時間
type 公告類別
      -->
    <input id="ids" name="ids" type="hidden" value="${sysNoticeEntity.id }"/>
    <div class="commonW">
        <div class="headTitle">${sysNoticeEntity.title}</div>
        <table class="formList">
            <tr align="center">
                <td colspan="2">開始時間：</td>
                <td colspan="3"><input name="startTime" id="startTime" type="text" class="easyui-validatebox Wdate"
                                       data-options="width: 250,required:true,disabled:true" disabled
                                       value="<fmt:formatDate value="${sysNoticeEntity.startTime}" pattern='yyyy-MM-dd HH:mm:ss'/>"
                                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'%y-%M-%d'})"
                                       placeholder="请选择开始时间"/></td>
                <td colspan="2">結束時間：</td>
                <td colspan="3"><input name="endTime" id="endTime" type="text" class="easyui-validatebox Wdate"
                                       data-options="width: 250,required:true,disabled:true" disabled
                                       value="<fmt:formatDate value="${sysNoticeEntity.endTime}" pattern='yyyy-MM-dd HH:mm:ss'/>"
                                       onclick="WdatePicker({minDate:'#F{$dp.$D(\'startTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})"
                                       placeholder="请选择结束时间"/></td>
            </tr>
            <tr align="center">
                <td colspan="2">附件</td>
                <td colspan="8" class="td_style1">
                    <input type="hidden" id="attachids"
                           name="attachids" value="${sysNoticeEntity.attachids }"/>
                    <div id="dowloadUrl">
                        <c:forEach items="${file}" varStatus="i" var="item">
                            <div id="${item.id}"
                                 style="line-height:30px;margin-left:5px;" class="float_L">
                                <div class="float_L">
                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                </div>
                            </div>
                        </c:forEach>
                    </div>
                </td>
            </tr>
            <tr align="center">
                <td colspan="2">公告内容</td>
                <td colspan="8" class="td_style1">
                <div class="textarea" contenteditable="true" id="description_content" style="max-width:1100px;overflow-x:scroll"/>
                    <input id="description" name="description" type="hidden" value="${sysNoticeEntity.description }"/>
                <%--<textarea style="width:100%;" id="description" type="hidden"--%>
                          <%--name="description" class="dd">${sysNoticeEntity.description}</textarea>--%>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<script type="text/javascript">
    //提交表单
    $(function () {
        $("#description_content").html($("#description").val());
    });
</script>
</body>
</html>