<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>系統公告</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <style>
        .commonW{
            width: 100%;
        }
        .td_style1{
            text-align: left;
        }
        .notice-type{
            padding-right: 10px;
        }
    </style>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>

</head>
<body>
<form id="mainform" action="${ctx}/sysnotice/${action}" method="post">
    <!--
          createBy 創建人
createDate 創建時間
updateBy 更新者
updateDate 更新時間
delFlag 刪除標識
id 主鍵
title 标题
description 詳情
status 状态
startTime 有效開始時間
endTime 有效結束時間
type 公告類別
      -->
    <input id="ids" name="ids" type="hidden" value="${sysNoticeEntity.id }"/>
    <div class="commonW" style="width: 100%;">
        <div class="headTitle">系統公告</div>
        <table class="formList" align="center">
            <tr align="center">
                <td colspan="2">公告標題：</td>
                <td colspan="8" class="td_style1" style="text-align: left;">
                    <input id="title" name="title" data-options="width: 640" class="easyui-validatebox" value="${sysNoticeEntity.title}"></input>
                </td>
            </tr>
            <tr align="center">
                <td colspan="2">開始時間：</td>
                <td colspan="3" style=" text-align: left;"><input name="startTime" id="startTime" type="text" readonly class="easyui-validatebox Wdate"
                                       data-options="width: 250,required:true,prompt:'请选择开始时间'"
                                       value="<fmt:formatDate value="${sysNoticeEntity.startTime}" pattern='yyyy-MM-dd HH:mm:ss'/>"
                                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'%y-%M-%d '})"
                /></td>
                <td colspan="2">結束時間：</td>
                <td colspan="3" style=" text-align: left;"><input name="endTime" id="endTime" type="text" readonly class="easyui-validatebox Wdate"
                                       data-options="width: 250,required:true,prompt:'请选择结束时间'"
                                       value="<fmt:formatDate value="${sysNoticeEntity.endTime}" pattern='yyyy-MM-dd HH:mm:ss'/>"
                                       onclick="WdatePicker({minDate:'#F{$dp.$D(\'startTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})"
                /></td>
            </tr>
            <tr align="center">
                <td colspan="2">附件：</td>
                <td colspan="8" class="td_style1"  style="text-align: left;"><span
                        class="sl-custom-file"> <input type="button"
                                                       value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="oosUploadFile('','notice');" class="ui-input-file"/>
								</span> <input type="hidden" id="attachids"
                                               name="attachids" value="${sysNoticeEntity.attachids }"/>
                    <div id="dowloadUrl">
                        <c:forEach items="${file}" varStatus="i" var="item">
                            <div id="${item.id}"
                                 style="line-height:30px;margin-left:5px;" class="float_L">
                                <div class="float_L">
                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                </div>
                                <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                            </div>
                        </c:forEach>
                    </div>
                </td>
            </tr>
            <tr>
                <td  colspan="2">公告類別：</td>
                <td colspan="8">
                    <label class="notice-type" style="padding-right: 10px;"><input type="radio" name="type" style="transform: translate(0, -3px);" value="0" <c:if test="${sysNoticeEntity.type==null}">checked</c:if><c:if test="${sysNoticeEntity.type=='0'}">checked</c:if>/>APP端</label>
                    <label class="notice-type" style="padding-right: 10px;"><input type="radio" name="type" style="transform: translate(0, -3px);" value="1" <c:if test="${sysNoticeEntity.type=='1'}">checked</c:if>/>PC端</label>
                    <label class="notice-type" style="padding-right: 10px;"><input type="radio" name="type" style="transform: translate(0, -3px);" value="2" <c:if test="${sysNoticeEntity.type=='2'}">checked</c:if>/>APP端+PC端</label>
                </td>
            </tr>
            <tr align="center">
                <td colspan="2">公告内容</td>
                <td colspan="8" class="td_style1"  style="text-align: left;">
                <textarea style="width:100%;max-height:1000px;" id="description"
                          name="description" class="dd">${sysNoticeEntity.description}</textarea>
                </td>
            </tr>

        </table>
    </div>
</form>
</div>
<script src='${ctx}/static/js/system/sysnotice.js?random=<%= Math.random()%>'></script>
<script type="text/javascript">
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            successTip(data, dg, d);
        }
    });
</script>
</body>
</html>