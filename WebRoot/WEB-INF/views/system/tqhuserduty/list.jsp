<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>${comments}</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input type="text" name="filter_EQS_dutyid" class="easyui-validatebox"
               data-options="width:150,prompt: '角色名稱'" id="dutyids"/>
        <input type="text" name="filter_LIKES_empno" class="easyui-validatebox"
               data-options="width:150,prompt: '工號'"/>
        <input type="text" name="filter_GTD_createDate" class="easyui-my97" datefmt="yyyy-MM-dd"
               data-options="width:150,prompt: '創建开始日期'"/>
        - <input type="text" name="filter_LTD_createDate" class="easyui-my97" datefmt="yyyy-MM-dd"
                 data-options="width:150,prompt: '創建结束日期'"/>
        <%--<input id="qysjzt" style="width:100px" class="easyui-validatebox" name="filter_EQS_workstatus"/>--%>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
    </form>
    <div>
        <shiro:hasPermission name="system:tqhuserduty:add">
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true"
               onclick="add();">添加</a>
            <span class="toolbar-item dialog-tool-separator"></span>
        </shiro:hasPermission>
        <shiro:hasPermission name="system:tqhuserduty:delete">
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true"
               data-options="disabled:false" onclick="del()">删除</a>
            <span class="toolbar-item dialog-tool-separator"></span>
        </shiro:hasPermission>
        <shiro:hasPermission name="system:tqhuserduty:update">
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true"
               onclick="upd()">修改</a>
            <span class="toolbar-item dialog-tool-separator"></span>
        </shiro:hasPermission>
    </div>

</div>
<table id="dg"></table>
<div id="dlg"></div>

<script src="${ctx}/static/js/system/tqhuserduty.js?" +Math.random()></script>
<script type="application/javascript">
    $(function () {
        dg = $('#dg').datagrid({
            method: "get",
            url: ctx+'/tqhuserduty/list',
            fit: true,
            fitColumns: true,
            border: false,
            idField: 'id',
            striped: true,
            cache: false,  //关闭AJAX相应的缓存
            pagination: true,
            rownumbers: true,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: true,
            columns: [[
                {field: 'id', title: '${column.comments}', hidden: true},
                {field: 'factoryid', title: '所在廠區', sortable: true, width: 100},
                {field: 'dutyid', title: '角色名稱', sortable: true, width: 150},
                {field: 'dutyids', title: '角色id', sortable: true, width: 100},
                {field: 'empno', title: '工號', sortable: true, width: 100},
                {field: 'sysId', title: '系統', sortable: true, width: 100},
                {field: 'area', title: '區域', sortable: true, width: 100}
                ,
                {field: 'building', title: '樓棟', sortable: true, width: 100}
                ,
                {field: 'remarks', title: '備註', sortable: true, width: 100,formatter:cellTextTip}
                ,
                {field: 'factorycode', title: '廠區編碼', sortable: true, width: 100}
                ,
                {field: 'applydeptno', title: '申請人單位代碼', sortable: true, width: 100}
                ,
                {field: 'createBy', title: '創建人', sortable: true, width: 100}
                ,
                {field: 'createDate', title: '創建時間', sortable: true, width: 100}
                ,
                {field: 'updateBy', title: '更新者', sortable: true, width: 100}
                ,
                {field: 'updateDate', title: '更新時間', sortable: true, width: 100}
            ]],
            onLoadSuccess: function(){
                $(".easyui-tooltip").tooltip({
                    onShow: function () {
                        $(this).tooltip('tip').css({
                            borderColor: '#000'
                        });
                    }
                });
                $(this).datagrid("fixRownumber");
            },
            enableHeaderClickMenu: true,
            enableHeaderContextMenu: true,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });
        $('#dutyids').combobox({
            prompt:'输入关键字自动检索',
            required:false,
            url:ctx+"/system/dict/getDictByType/user_duty",
            editable:true,
            valueField: "value",
            textField: "label",
            hasDownArrow:true,
            filter: function(q, row){
                var opts = $(this).combobox('options');
                return row[opts.textField].toLowerCase().indexOf(q.toLowerCase()) != -1;
                // return row[opts.textField].indexOf(q) == 0;
            }
        });
        // onchangeFactory();
        // onchangeArea();
    });
</script>
</body>
</html>