<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>${comments}</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<div>
<form id="mainform" action="${ctx}/tqhuserduty/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhUserduty.id }"/>
    <table class="formTable">
        <tr>
            <td>所屬角色</td>
            <td>
                <input id="dutyid" name="dutyid" class="easyui-combobox" data-options="width: 150"
                       value="${tQhUserduty.dutyid }"/>
            </td>
        </tr>
        <tr>
			<td>系統：</td>
			<td><input id="sysId" name="sysId" type="text" value="${tQhUserduty.sysId}" class="easyui-validatebox" data-options="width: 180" /></td>
		</tr>
        <tr>
            <td>廠區</td>
            <td>
                <input id="factoryid" name="factoryid" class="easyui-validatebox" data-options="width: 150,onSelect:function(){onchangeFactory();},onLoadSuccess: function () {getFacAreaBuild();}"
                       value="${tQhUserduty.factoryid }"/>
                <%--<input id="factoryid" name="factoryid" class="easyui-combobox" data-options="width: 150,onSelect:function(){onchangeFactory();}"
                       value="${tQhUserduty.factoryid}"/>--%>
            </td>
        </tr>
       <tr>
            <td>所屬區域：</td>
            <td>
                <%--<input id="area" name="area" class="easyui-validatebox" data-options="width: 150,onSelect:function(){onchangeArea();}"
                       value="${tQhUserduty.area }"/>--%>
                <input id="area" name="area" class="easyui-combobox" data-options="width: 150,onSelect:function(){onchangeArea();}"
                       value="${tQhUserduty.area}"/>
            </td>
        </tr>
        <tr>
            <td>樓棟信息：</td>
            <td>
                <%--<input id="building" name="building" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhUserduty.building }"/>--%>
                <input id="building" name="building" class="easyui-combobox" data-options="width: 150"
                       value="${tQhUserduty.building }"/>
            </td>
        </tr>
        <tr>
            <td>備註：</td>
            <td>
                <input id="remarks" name="remarks" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhUserduty.remarks }"/>
            </td>
        </tr>
        <tr>
            <td>責任製程廠級代碼：</td>
            <td>
                <input id="factorycode" name="factorycode" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhUserduty.factorycode }"/>
            </td>
        </tr>
        <tr>
            <td>工號：</td>
            <td>
                <input id="empno" name="empno" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhUserduty.empno }"/>
            </td>
        </tr>
        <tr>
            <td>申請人單位代碼：</td>
            <td>
                <input id="applydeptno" name="applydeptno" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhUserduty.applydeptno }"/>
            </td>
        </tr>
    </table>
</form>
</div>
<script src='${ctx}/static/js/system/tqhuserduty.js?'+Math.random()></script>
<script type="text/javascript">
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            successTip(data, dg, d);
        }
    });
    $('#dutyid').combobox({
        prompt:'输入关键字自动检索',
        required:false,
        url:ctx+"/system/dict/getDictByType/user_duty",
        editable:true,
        valueField: "value",
        textField: "label",
        hasDownArrow:true,
        filter: function(q, row){
            var opts = $(this).combobox('options');
            return row[opts.textField].toLowerCase().indexOf(q.toLowerCase()) != -1;
//            return row[opts.textField].indexOf(q) == 0;
        }
    });
    //加載系統列表
	/*$('#sysId').combotree({
		width:150,
		method:'GET',
	    url: '${ctx}/system/sysinfo/json',
	    idField : 'id',
	    textFiled : 'name',
		parentField : 'superId',
	    animate:true
	});*/ 
    
    
	$.ajax({
		type : 'GET',
		async : false,
		url :  ctx + '/system/sysinfo/json1',
		dataType : 'json',
		success : function(treedata) {
			treedata.unshift({"id":'',"text":"請選擇","sort":1});
			$('#sysId').combotree({
				width:250,
			    data: treedata
			});
		},
		error : function(data) {
			$.messager.alert('Info', '操作異常', 'info');
		}
	})
</script>
</body>
</html>