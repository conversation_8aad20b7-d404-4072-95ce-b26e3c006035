<%@ page contentType="text/html;charset=UTF-8" %>
<%@ page import="org.apache.shiro.web.filter.authc.FormAuthenticationFilter"%>
<%@ page import="org.apache.shiro.authc.ExcessiveAttemptsException"%>
<%@ page import="org.apache.shiro.authc.IncorrectCredentialsException"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>

<html>
<head>
    <title>iPEBG電子簽核平台</title>
    <meta http-equiv="X-UA-Compatible" content="IE=8"/>
    <%@ include file="/WEB-INF/views/include/kindeditor.jsp" %>
    <script src="${ctx}/static/plugins/easyui/jquery/jquery-3.6.1.min.js"></script>
</head>
<body>
<div>
    <form id="loginForm" action="${ctx}/admin/login" method="post">
        <textarea style="width:800px;height:300px;visibility:hidden;" id="content" name="content" class="dd"></textarea>
    </form>
</div>
<script type="text/javascript">
    var KE;
    var TT = TAOTAO = {
        // 编辑器参数
        kingEditorParams : {
            resizeType : 1,
            allowImageUpload : true,
            allowFileManager : true,
            uploadJson : '${ctx}/admin/fileUpload',
            <%--fileManagerJson : '${ctx}/static/plugins/kindeditor/file_manager_json.jsp',--%>
            afterUpload : function(url, data, name){
                this.sync();
                //对上传的图片宽度的控制
                if(name=="image" || name=="multiimage"){ //单个和批量上传图片时
                    var img = new Image(); img.src = url;
                    img.onload = function(){ //图片必须加载完成才能获取尺寸
                        if(img.width>600) KE.html(KE.html().replace('<img src="' + url + '"','<img src="' + url + '" width="600"'))
                    }
                }
            },
            afterBlur : function(){this.sync();},
            items : ['source', '|', 'undo', 'redo', '|', 'preview', 'template', 'cut', 'copy', 'paste',
                'plainpaste', 'wordpaste', '|', 'justifyleft', 'justifycenter', 'justifyright',
                'justifyfull', 'insertorderedlist', 'insertunorderedlist', 'indent', 'outdent', 'subscript',
                'superscript', 'clearhtml', 'quickformat', 'selectall', '|', 'fullscreen', '/',
                'formatblock', 'fontname', 'fontsize', '|', 'forecolor', 'hilitecolor', 'bold',
                'italic', 'underline', 'strikethrough', 'lineheight', 'removeformat', '|', 'image','multiimage',
                'flash', 'media', 'insertfile', 'table', 'hr', 'emoticons', 'baidumap', 'pagebreak',
                'anchor', 'link', 'unlink']

        },
        createEditor : function(select){
            KE = KindEditor.create(select, TT.kingEditorParams);
            return KE;
        }
    }

    var itemAddEditor ;
    //页面初始化完毕后执行此方法
    $(function(){
        var html = "我们在kindeditor编辑器中写入文字，设置其字体、颜色，保存到数据库中。由于编辑器是覆盖在textarea文本框上的，好像无法直接用编辑器打开之前的文字遇到一定困难，所以只有在纯文本中查看文字。但是，显示时纯文本中会连带出现一些html元素，<html>....</html>，中间的内容基本是html代码表示这些文字的字体、颜色。\n" +
            "\n" +
            "解决方案：\n" +
            " 由于没法在textarea下取得HTML内容，于是我们结合DIV和异步编辑成功添加了编辑器，在对于取得编辑器HTML内容上面，我们是这样处理的：\n" +
            "KindEditor的可视化操作在新创建的iframe上执行，代码模式下的textarea框也是新创建的，所以最后提交前需要将HTML数据同步到原来的textarea，KE.sync函数会完成这个动作。\n" +
            "KindEditor在默认情况下自动寻找textarea所属的form元素，找到form后onsubmit事件里添加KE.sync函数，所以用form方式提交数据，不需要手动执行KE.sync函数。\n" +
            "//取得HTML内容\n" +
            "html = KE.html('editor_id');\n" +
            "\n" +
            "//同步数据后可以直接取得textarea的value<br/>" +
            "KE.sync('editor_id');\n" +
            "html = document.getElementById('editor_id').value;\n" +
            "html = $('#editor_id').val(); //jQuery\n" +
            "\n" +
            "//设置HTML内容\n" +
            "KE.html('editor_id', 'HTML内容');\n" +
            "<a href='#'>而后在源代码中做了改进，结合ajax与异步加载实现了1111再编辑功能</a>\n" +
            "--------------------- \n" +
            "作者：lcbqqq李狗蛋 \n" +
            "来源：CSDN \n" +
            "原文：https://blog.csdn.net/u012203244/article/details/15681829 \n" +
            "版权声明：本文为博主原创文章，转载请附上博文链接！"
        //创建富文本编辑器
        $("#content").html(html);
        itemAddEditor = TAOTAO.createEditor("#loginForm [name=content]",TT.kingEditorParams);
    });
    //提交表单
    function submitForm(){
        //编辑器中数据同步描述进textarea
        itemAddEditor.sync();
        //ajax的post方式提交表单
        //提交过去的是json数据，返回回来的数据是json解析的
        //$("#itemAddForm").serialize()将表单序列号为key-value形式的字符串
        $.post("blogsave.action",$("#loginForm").serialize(), function(data){
            alert(data);
        },"json");
    }
</script>
</body>
</html>
