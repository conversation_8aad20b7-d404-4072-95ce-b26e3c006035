<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>ip管控用戶信息</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input type="text" name="filter_EQS_versionCode" class="easyui-validatebox"
               data-options="width:150,prompt: '當前APP版本號 '"/>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
           onclick="listSearchReset()">重置</a>
    </form>
    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="add();">添加</a>
    <span class="toolbar-item dialog-tool-separator"></span>
    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true"
       data-options="disabled:false" onclick="del()">删除</a>
    <span class="toolbar-item dialog-tool-separator"></span>
    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="upd()">修改</a>
</div>
<table id="dg"></table>
<div id="dlg"></div>

<script type="text/javascript">
    $(function () {
        dg = $('#dg').datagrid({
            method: "get",
            url: ctx + '/mobileappupdatehistory/list',
            fit: true,
            fitColumns: true,
            //fitColumns: false,  //如需要底部出現滾動條，修改爲false
            border: false,
            idField: 'id',
            striped: true,
            cache: false,  //关闭AJAX相应的缓存
            pagination: true,
            rownumbers: true,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: true,
            columns: [[
                {field: 'id', title: '主鍵', hidden: true},
                {field: 'versionCode', title: '當前APP版本號', sortable: true, width: 100},
                {field: 'appSource', title: '從哪個終端請求的', sortable: true, width: 100},
                {field: 'downloadUrl', title: 'APK下載地址', sortable: true, width: 100},
                {field: 'newVersion', title: '最新版本號', sortable: true, width: 100},
                {field: 'updateContent', title: '更新內容', sortable: true, width: 100},
                {field: 'updateType', title: '更新類型', sortable: true, width: 100},
                {field: 'channel', title: '渠道', sortable: true, width: 100},
                {field: 'remark', title: '備註', sortable: true, width: 100},
                {field: 'createBy', title: '創建人', sortable: true, width: 100},
                {field: 'createDate', title: '創建時間', sortable: true, width: 100},
                {field: 'updateBy', title: '更新者', sortable: true, width: 100},
                {field: 'updateDate', title: '更新時間', sortable: true, width: 100}
            ]],
            onLoadSuccess: function () {
                $(".easyui-tooltip").tooltip({
                    onShow: function () {
                        $(this).tooltip('tip').css({
                            borderColor: '#000'
                        });
                    }
                });
                //$(this).datagrid("fixRownumber");
            },
            rowStyler: rowStylerFun,
            enableHeaderClickMenu: true,
            enableHeaderContextMenu: true,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });
    });

    //创建查询对象并查询
    function cx() {
        var obj = $("#searchFrom").serializeObject();
        dg.datagrid('load', obj);
    }

    //表單進度查詢頁面查詢條件重置
    function listSearchReset() {
        $("#searchFrom").form("reset");
    }

    //导出excel
    function exportExcel() {
        var options = $('#dg').datagrid('getPager').data("pagination").options;
        var page = options.pageNumber;//当前页数  
        var total = options.total;
        var rows = options.pageSize;//每页的记录数（行数）  
        var form = document.getElementById("searchFrom");
        if (total == 0) {
            return
        }
        $('#page').val(page);
        $('#rows').val(total);
        var form = document.getElementById("searchFrom");
        searchFrom.action = ctx + '/mobileappupdatehistory/exportExcel';
        form.submit();
    }

    //弹窗增加
    function add() {
        d = $("#dlg").dialog({
            title: '添加App版本更新信息',
            width: 500,
            height: 400,
            href: '${ctx}/mobileappupdatehistory/create',
            maximizable: true,
            modal: true,
            buttons: [{
                text: '确认',
                handler: function () {
                    $("#mainform").submit();
                }
            }, {
                text: '取消',
                handler: function () {
                    d.panel('close');
                }
            }]
        });
    }

    function del() {
        var row = dg.datagrid('getSelected');
        if (rowIsNull(row)) return;
        parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
            if (data) {
                $.ajax({
                    type: 'get',
                    url: ctx + "/mobileappupdatehistory/delete/" + row.id,
                    success: function (data) {
                        successTip(data, dg);
                    }
                }, 'text');
            }
        });
    }

    //弹窗修改
    function upd() {
        var row = dg.datagrid('getSelected');
        if (rowIsNull(row)) return;
        d = $("#dlg").dialog({
            title: '修改App版本更新信息',
            width: 500,
            height: 400,
            href: ctx + '/mobileappupdatehistory/update/' + row.id,
            maximizable: true,
            modal: true,
            buttons: [{
                text: '修改',
                handler: function () {
                    $('#mainform').submit();
                }
            }, {
                text: '取消',
                handler: function () {
                    d.panel('close');
                }
            }]
        });
    }
</script>
</body>
</html>
