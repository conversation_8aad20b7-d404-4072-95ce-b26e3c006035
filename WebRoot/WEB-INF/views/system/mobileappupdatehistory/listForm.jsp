<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>ip管控用戶信息</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/mobileappupdatehistory/${action}" method="post">
    <table class="formTable">
        <tr>
            <td>當前APP版本號：</td>
            <td>
                <input type="hidden" name="id" value="${mobileAppUpdateHistoryEntity.id}"/>
                <input name="versionCode" type="text" style="width: 270px"
                       value="${mobileAppUpdateHistoryEntity.versionCode }"
                       class="easyui-validatebox"/></td>
        </tr>
        <tr>
            <td>從哪個終端請求的：</td>
            <td>
                <input name="appSource" type="text" style="width: 270px"
                       value="${mobileAppUpdateHistoryEntity.appSource }"
                       class="easyui-validatebox"/></td>
        </tr>
        <tr>
            <td>APK下載地址：</td>
            <td>
                <input name="downloadUrl" type="text" style="width: 270px"
                       value="${mobileAppUpdateHistoryEntity.downloadUrl }"
                       class="easyui-validatebox"/></td>
        </tr>
        <tr>
            <td>最新版本號：</td>
            <td>
                <input name="newVersion" type="text" style="width: 270px"
                       value="${mobileAppUpdateHistoryEntity.newVersion }"
                       class="easyui-validatebox"/></td>
        </tr>
        <tr>
            <td>更新內容：</td>
            <td>
                <input name="updateContent" type="text" style="width: 270px"
                       value="${mobileAppUpdateHistoryEntity.updateContent }"
                       class="easyui-validatebox"/></td>
        </tr>
        <tr>
            <td>更新類型：</td>
            <td>
                <input name="updateType" type="text" style="width: 270px"
                       value="${mobileAppUpdateHistoryEntity.updateType }"
                       class="easyui-validatebox"/></td>
        </tr>
        <tr>
            <td>渠道：</td>
            <td>
                <input name="channel" type="text" style="width: 270px" value="${mobileAppUpdateHistoryEntity.channel }"
                       class="easyui-validatebox"/></td>
        </tr>
        <tr>
            <td>描述：</td>
            <td><textarea rows="4" name="remarks"
                          style="font-size: 12px;font-family: '微软雅黑';width: 270px">${mobileAppUpdateHistoryEntity.remarks}</textarea>
            </td>
        </tr>
    </table>
</form>
</div>
<script type="text/javascript">
    $(function () {
        $('#mainform').form({
            onSubmit: function () {
                var isValid = $(this).form('validate');
                return isValid;	// 返回false终止表单提交
            },
            success: function (data) {
                successTip(data, dg, d);
            }
        });
    });
</script>
</body>
</html>