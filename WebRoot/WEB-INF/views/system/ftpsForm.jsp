<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>FTP信息維護</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/sysftpinfo/${action}" method="post">
    <input type="hidden" name="id" value="${ftps.id }"/>
    <table class="formTable">
        <tr>
            <td>
                類型
            </td>
            <td>
                <c:choose>
                    <c:when test="${action != 'update'}">
                        <input type="radio" name="type" value="2" class="easyui-radiobutton" onchange="checkType(this)"/>FTPS
                        <input type="radio" name="type" value="0" class="easyui-radiobutton" onchange="checkType(this)"/>IP管控
                        <input type="radio" name="type" value="1" class="easyui-radiobutton" onchange="checkType(this)"/>移動端簽核
                    </c:when>
                    <c:when test="${action == 'update'&&hasChild == false}">
                        <input type="radio" name="type" value="2" class="easyui-radiobutton" onchange="checkType(this)"/>FTPS
                        <input type="radio" name="type" value="0" class="easyui-radiobutton" onchange="checkType(this)"/>IP管控
                        <input type="radio" name="type" value="1" class="easyui-radiobutton" onchange="checkType(this)"/>移動端簽核
                    </c:when>
                    <c:otherwise>
                        <input type="radio" name="type" value="2" class="easyui-radiobutton" onchange="checkType(this)" disabled/>FTPS
                        <input type="radio" name="type" value="0" class="easyui-radiobutton" onchange="checkType(this)" disabled/>IP管控
                        <input type="hidden" name="type" value="${ftps.type }"/>
                        <input type="radio" name="type" value="1" class="easyui-radiobutton" onchange="checkType(this)" disabled/>移動端簽核
                    </c:otherwise>
                </c:choose>

            </td>
        </tr>
        <tr id="whetherIpLockTr">
            <td>是否IP管控：</td>
            <td>
                <input name="whetherIpLock" id="whetherIpLock" style="width:200px;" class="easyui-combobox"/>
            </td>
        </tr>
        <tr id="whetherAppTr">
            <td>是否APP簽核：</td>
            <td>
                <input name="whetherApp" id="whetherApp" style="width:200px;" class="easyui-combobox"/>
            </td>
        </tr>
        <tr class="FTPSTr">
            <td>FTPS賬號：</td>
            <td>
                <input id="ftpsName" name="ftpsName" class="easyui-validatebox"
                       data-options="width: 200,required:'required'" value="${ftps.ftpsName }">
            </td>
        </tr>
        <tr class="FTPSTr">
            <td>FTPS密码：</td>
            <td><input id="ftpsPass" name="ftpsPass" class="easyui-validatebox"
                       data-options="width: 200,required:'required'" value="${ftps.ftpsPass }"/></td>
        </tr>
        <tr class="FTPSTr">
            <td>FTPS IP：</td>
            <td><input name="ftpsIp" type="text" value="${ftps.ftpsIp }" class="easyui-validatebox"
                       data-options="width: 200,required:'required'"/></td>
        </tr>
        <tr class="FTPSTr">
            <td>FTPS端口：</td>
            <td><input name="ftpsPort" type="text" value="${ftps.ftpsPort }" class="easyui-validatebox"
                       data-options="width: 200,required:'required'"/></td>
        </tr>
        <tr class="FTPSTr">
            <td>對象存儲路徑：</td>
            <td><input name="oosFilePath" type="text" value="${ftps.oosFilePath }" class="easyui-validatebox"
                       data-options="width: 200,required:'required'"/></td>
        </tr>
        <tr>
            <td>備註：</td>
            <td><textarea rows="4" name="remark"
                          style="font-size: 12px;font-family: '微软雅黑';width: 270px">${ftps.remark}</textarea>
            </td>
        </tr>
    </table>
</form>
</div>
<script type="text/javascript">

    $(function () {

        $('#mainform').form({
            onSubmit: function () {
                var isValid = $(this).form('validate');
                return isValid;	// 返回false终止表单提交
            },
            success: function (data) {
                successTip(data, dg, d);
            }
        });
        $("#whetherIpLock").combobox({
            editable: false,
            valueField: 'itemValue',
            textField: 'itemName',
            data: [{itemValue: '1', itemName: '是'}, {itemValue: '2', itemName: '否'}],
            onLoadSuccess: function () {
                var data = $('#whetherIpLock').combobox('getData');
                var v = ${ftps.whetherIpLock}
                $(this).combobox("select", v==null?data[1].itemValue:v);
            }
        });
        $("#whetherApp").combobox({
            editable: false,
            valueField: 'itemValue',
            textField: 'itemName',
            data: [{itemValue: '1', itemName: '是'}, {itemValue: '2', itemName: '否'}],
            onLoadSuccess: function () {
                var data = $('#whetherApp').combobox('getData');
                var v = ${ftps.whetherApp}
                    $(this).combobox("select", v==null?data[1].itemValue:v);
            }
        });
        if (${ftps.type!=null}) {
            $("input[name='type'][value='${ftps.type}']").attr("checked",true);
        }else {
            $("input[name='type'][value='2']").attr("checked",true);
        }

        $('#whetherIpLockTr').hide();
        $('#whetherAppTr').hide();
        checkType();
    });

    function checkType() {
        var obj = $("input[name='type']:checked").val();
        if (obj == 0) {
            $('#whetherAppTr').hide();
            $("#whetherApp").combobox('setValue','2');
            $(".FTPSTr").hide();
            $('.FTPSTr input').validatebox({required:false});
            $('.FTPSTr input').val('');

            $('#whetherIpLockTr').show();
        }else if (obj == 1) {
            $('#whetherIpLockTr').hide();
            $("#whetherIpLock").combobox('setValue','2');
            $(".FTPSTr").hide();
            $('.FTPSTr input').validatebox({required:false});
            $('.FTPSTr input').val('');

            $('#whetherAppTr').show();
        }else if (obj == 2) {
            $('#whetherAppTr').hide();
            $("#whetherApp").combobox('setValue','2');
            $('#whetherIpLockTr').hide();
            $("#whetherIpLock").combobox('setValue','2');
            // $("#whetherIpLock").combobox('setValue','2');
            // $("#whetherApp").combobox('setValue','2');

            $(".FTPSTr").show();
            $('.FTPSTr input').validatebox({required:true});
        }
    }
</script>
</body>
</html>