<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>SQL 控制台</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .results-table th, .results-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .results-table th {
            background-color: #f2f2f2;
        }

        .results-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .error-message {
            color: red;
            margin-top: 10px;
        }

        /*.history-panel {*/
        /*    margin-top: 20px;*/
        /*    border: 1px solid #ddd;*/
        /*    padding: 10px;*/
        /*    border-radius: 4px;*/
        /*}*/
    </style>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <!-- 在head中添加 -->
    <link rel="stylesheet" href="${ctx}/static/css/codemirror.min.css">
    <style type="text/css">
        .CodeMirror{
            height: 500px !important;
            font-family: Consolas, "Courier New", monospace;
            font-weight: normal;
            font-size: 14px;
            line-height: 19px;
            letter-spacing: 0px;
        }
    </style>
    <link rel="stylesheet" href="${ctx}/static/css/dracula.min.css">
    <link rel="stylesheet" href="${ctx}/static/css/show-hint.min.css">
    <link rel="stylesheet" href="${ctx}/static/css/foldgutter.min.css">
    <link rel="stylesheet" href="${ctx}/static/css/fullscreen.min.css">
    <script src="${ctx}/static/js/common/clamp.min.js"></script>
    <script src="${ctx}/static/js/common/codemirror.min.js"></script>
    <script src="${ctx}/static/js/common/sql.min.js"></script>
    <script src="${ctx}/static/js/common/sql-formatter.min.js"></script>
    <script src="${ctx}/static/js/common/show-hint.min.js"></script>
    <script src="${ctx}/static/js/common/sql-hint.min.js"></script>
    <script src="${ctx}/static/js/common/brace-fold.min.js"></script>
    <script src="${ctx}/static/js/common/comment-fold.min.js"></script>
    <script src="${ctx}/static/js/common/foldcode.min.js"></script>
    <script src="${ctx}/static/js/common/foldgutter.min.js"></script>
    <script src="${ctx}/static/js/common/indent-fold.min.js"></script>
    <script src="${ctx}/static/js/common/xml-fold.min.js"></script>
    <script src="${ctx}/static/js/common/fullscreen.min.js"></script>
    <script src="${ctx}/static/js/common/matchbrackets.min.js"></script>
</head>
<body>
<div class="container">
    <h1>SQL 控制台</h1>
        <textarea data-options="required:true" id="sqlEditor" class="sql-editor" name="sqlQuery"
                  placeholder="输入SQL语句..."></textarea>
        <br>
        <a href="javascript(0)" class="easyui-linkbutton" data-options="iconCls:'icon-ok'" onclick="executeSql()">執 行</a>
        <a href="javascript(0)" class="easyui-linkbutton" data-options="iconCls:'icon-ok'" onclick="formatSql()">格式化SQL</a>
<%--        <a href="javascript(0)" class="easyui-linkbutton" data-options="iconCls:'icon-ok'" onclick="fullscreen()">全屏</a>--%>
    <div class="error-message" id="error-message">
        <strong>执行失敗的sql:</strong>
        <div id="error"></div>
    </div>

</div>
<!-- 在页面底部添加 -->
<script type="text/javascript">
    var editor = CodeMirror.fromTextArea(document.getElementById('sqlEditor'), {
        mode: 'text/x-sql',
        indentWithTabs: true,
        smartIndent: true,
        dragDrop:true,
        lineNumbers: true,//显示行号
        styleActiveLine: true,// 设置光标所在行高亮true/false
        matchBrackets: true,
        // fullScreen:true,
        foldGutter: true,        // 启用折叠边栏
        //括号匹配
        matchBrackets:true,
        gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter'],// 添加折叠边栏
        lineWrapping: 'wrap',
        theme: 'dracula',//设置主题
        extraKeys: {
            "Ctrl-Space": "autocomplete",  // 设置自动补全快捷键
            "Ctrl-Enter": function(cm) {
                executeSql(cm.getValue());  // 自定义执行SQL的功能
            }
        },
        hintOptions: {
            // SQL特定的补全配置
            tables: ${fn:replace(tables,"=",":")},
            <%--columns: ${columns},--%>
            <%--columnTypes: ${columnTypes},--%>
            // 内置SQL关键字补全
            <%--defaultTable: ${tables.length > 0 ? tables[0] : null},  // 默认表--%>
            completeSingle: false, // 不自动补全唯一匹配项
            disableKeywords: false  // 不禁用关键字补全
        }
    });
    // 将自动提示绑定到change事件上，这样输入的时候就可以看到联想的关键词
    this.editor.on('change', (instance, change) => {
        // 自动补全的时候，也会触发change事件，所有坐下判断，以免死循环，正则是为了不让空格，换行之类的也提示
        // 通过change对象你可以自定义一些规则去判断是否提示
        if (change.origin !== 'complete' && /\w|\./g.test(change.text[0])) {
            instance.showHint()
        }
    });
    function formatSql() {
        var sql = editor.getValue();
        var formatted = sqlFormatter.format(sql);
        editor.setValue(formatted);
    }
    function fullscreen(){
        editor.setOption("fullScreen", !editor.getOption("fullScreen"));
    }
    editor.on("dragover", (editor, event) => {
        event.preventDefault(); // 必须阻止默认行为
    });

    editor.on("drop", (editor, event) => {
        // 1. 检查是否有文件
        if (event.dataTransfer.files.length > 0) {
            const file = event.dataTransfer.files[0];
            // 处理文件（如读取文本）
            const reader = new FileReader();
            reader.onload = (e) => {
                const text = e.target.result;
                // 在光标位置插入文本
                const coords = editor.coordsChar({left: event.clientX, top: event.clientY});
                editor.replaceRange(text, coords);
            };
            reader.readAsText(file);
            event.preventDefault(); // 阻止浏览器默认打开文件行为
        }
        // 2. 处理纯文本拖放
        else if (event.dataTransfer.getData("text")) {
            const text = event.dataTransfer.getData("text");
            const coords = editor.coordsChar({left: event.clientX, top: event.clientY});
            editor.replaceRange(text, coords);
            event.preventDefault();
        }
    });
    editor.on("change", function(cm, change) {
        cm.scrollIntoView({line: cm.lastLine(), ch: 0});
        // 只有在最后添加内容时才滚动
        // if (change.from.line === cm.lastLine()) {
        //     var lastLine = cm.lastLine();
        //     cm.scrollIntoView({line: lastLine, ch: 0});
        // }
    });
    //创建查询对象并查询
    function executeSql() {
        var command = editor.getValue();
        // alert(command);
        $.ajax({
            type: 'POST',
            beforeSend: ajaxLoading,
            url: "${ctx}/system/sql/exeSqlCommond",
            data: {sqlCommond: command},
            dataType: 'json',
            success: function (data) {
                ajaxLoadEnd();
                // editor.setValue("");
                var html = '';
                $.each(data,function(index,item) {
                    console.log(item);
                    html += '<div class="content-box" id="content-'+index+'"><div class="clamp-text">'+item+'</div><span class="show-more" data-index="'+index+'"><u>显示更多</u></span></div></br>'
                });
                if(html==''){
                    $("#error").html("執行成功，沒有執行失敗的SQL");
                }else {
                    $("#error").html(html);
                    $('.clamp-text').each(function () {
                        $clamp(this, {clamp: 2});
                    });
                }
            },
            error: function (data) {
                $.messager.alert(data);
                ajaxLoadEnd();
            }
        });
    }
    $(document).on('click', '.show-more', function() {
        var index = $(this).data('index');
        var $content = $('#content-' + index + ' .clamp-text');
        var $btn = $(this);

        if ($btn.text() === '显示更多') {
            $content.css('-webkit-line-clamp', 'unset');
            $btn.text('收起');
        } else {
            $clamp($content[0], {clamp: 2});
            $btn.text('显示更多');
        }
    });
</script>
<style>

</style>
</body>
</html>