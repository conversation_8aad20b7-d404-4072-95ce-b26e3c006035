<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title></title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/ssh/system/command" method="post">
    <div id="tb1" style="height:auto" title="執行命令" data-options="refreshable: false">
        <textarea rows="4" cols="120" name="command" id="command" class="easyui-validatebox" data-options="required:true"></textarea><br/>
        <a href="javascript(0)" class="easyui-linkbutton" data-options="iconCls:'icon-ok'" onclick="exe()" >執    行</a>
    </div>
</form>
    <div id="tb2">

    </div>
<table id="dg"></table>
<div id="dlg"></div>
<script type="text/javascript">
    // $('#mainform').form({
    //     onSubmit: function(){
    //         var isValid = $(this).form('validate');
    //         return isValid;	// 返回false终止表单提交
    //     },
    //     success:function(data){
    //         if(successTip(data,dg,d))
    //             dg.treegrid('reload');
    //     }
    // });
    //创建查询对象并查询
    function exe() {
        var command = $("#command").val();
        if (!$("#mainform").form('validate')) {
            return false;
        }
        $.ajax({
            type: 'POST',
            beforeSend: ajaxLoading,
            url: "${ctx}/ssh/command",
            data:{command:command},
            success: function (data) {
                ajaxLoadEnd();
                $("#tb2").html(data);
            },
            error:function(data){
                $.messager.alert(data);
                ajaxLoadEnd();
            }
        });
    }
</script>
</body>
</html>
