<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>FTP信息維護</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body class="easyui-layout" style="font-family: '微软雅黑'">
<div data-options="region:'center',split:true,border:false,title:'FTPS列表'">
    <div id="tb" style="padding:5px;height:auto">
        <form id="searchFrom" action="">
            <input type="text" name="filter_LIKES_ftpsName" class="easyui-validatebox"
                   data-options="width:150,prompt: '賬號 '"/>
            <input style="width:100px" class="easyui-validatebox" data-options="width:150,prompt: 'IP地址'"
                   name="filter_LIKES_ftpsIp"/>
            <span class="toolbar-item dialog-tool-separator"></span>
            <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
            <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
               onclick="listSearchReset()">重置</a>
        </form>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true"
           onclick="add();">添加</a>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true"
           data-options="disabled:false" onclick="del()">删除</a>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true"
           onclick="upd()">修改</a>
    </div>
    <table id="dg"></table>
    <div id="dlg"></div>
</div>
<div data-options="region:'east',split:true,border:false,title:'角色列表'" style="width: 600px">
    <div id="tb1" style="padding:5px;height:auto">
        <div>
            <a href="#" class="easyui-linkbutton" iconCls="icon-save" plain="true" onclick="save();">保存授权</a>
        </div>

    </div>
    <table id="dg1"></table>
</div>
</body>
<script type="text/javascript">
    var dg;	//角色datagrid
    var d; //弹窗
    var permissionDg;	//权限datagrid
    var rolePerData;	//用户拥有的权限
    $(function () {
        dg = $('#dg').datagrid({
            method: "get",
            url: ctx + '/sysftpinfo/list',
            fit: true,
            fitColumns: true,
            //fitColumns: false,  //如需要底部出現滾動條，修改爲false
            border: false,
            idField: 'id',
            striped: true,
            cache: false,  //关闭AJAX相应的缓存
            pagination: true,
            rownumbers: true,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: true,
            columns: [[
                {field: 'id', title: '主鍵', hidden: true},
                {field: 'ftpsName', title: '賬號', sortable: true, width: 50},
                {field: 'ftpsPass', title: '密碼', sortable: true, width: 50},
                {field: 'ftpsIp', title: 'ip地址', sortable: true, width: 50},
                {field: 'ftpsPort', title: '端口', sortable: true, width: 30},
                {field: 'oosFilePath', title: '對象存儲路徑', sortable: true, width: 80},
                {field: 'whetherIpLock', title: '是否IP管控', sortable: true, width: 50,formatter:staus},
                {field: 'whetherApp', title: '是否APP簽核', sortable: true, width: 50,formatter:staus},
                {field: 'remark', title: '備註', sortable: true, width: 100, tooltip: true},
                {
                    field: 'action', title: '操作', width: 30,
                    formatter: function (value, row, index) {
                        return '<a href="javascript:lookP(' + row.id + ')"><div class="icon-hamburg-lock" style="width:16px;height:16px" title="查看角色"></div></a>';
                    }
                }
            ]],
            onLoadSuccess: function () {
                $(".easyui-tooltip").tooltip({
                    onShow: function () {
                        $(this).tooltip('tip').css({
                            borderColor: '#000'
                        });
                    }
                });
            },
            onClickRow: function (index, row) {
                lookP(row.id);
            },
            rowStyler: rowStylerFun,
            enableHeaderClickMenu: true,
            enableHeaderContextMenu: true,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });
        permissionDg = $('#dg1').datagrid({
            method: "get",
            url: '${ctx}/system/role/allData',
            fit: true,
            fitColumns: true,
            border: false,
            idField: 'id',
            pagination: false,
            rownumbers: true,
            pageNumber: 1,
            pageSize: 30,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: false,
            striped: true,
            cache: false,  //关闭AJAX相应的缓存
            columns: [[
                {field: 'id', title: 'id', hidden: true},
                {field: 'name', title: '角色名称', sortable: true, width: 100},
                {field: 'roleCode', title: '角色编码', sortable: true, width: 100},
                {field: 'description', title: '描述', sortable: true, width: 100, tooltip: true}
            ]],
            enableHeaderClickMenu: false,
            enableHeaderContextMenu: false,
            enableRowContextMenu: false,
            toolbar: '#tb1'
        });
    });

    function add() {
        $.ajaxSetup({type: 'GET'});
        d = $("#dlg").dialog({
            title: '添加FTPS信息',
            width: 400,
            height: 450,
            href: '${ctx}/sysftpinfo/create',
            maximizable: true,
            modal: true,
            buttons: [{
                text: '确认',
                handler: function () {
                    $("#mainform").submit();
                }
            }, {
                text: '取消',
                handler: function () {
                    d.panel('close');
                }
            }]
        });
    }
    function lookP(roleId) {
        //清空勾选的权限
        if (rolePerData) {
            permissionDg.datagrid('unselectAll');
            rolePerData = [];//清空
        }
        //获取角色拥有权限
        $.ajax({
            async: false,
            type: 'get',
            dataType:'json',
            url: "${ctx}/sysftpinfo/" + roleId + "/json",
            success: function (data) {
                if (typeof data == 'object') {
                    rolePerData = data;
                    for (var i = 0, j = data.length; i < j; i++) {
                        permissionDg.datagrid('selectRow', permissionDg.datagrid('getRowIndex', data[i]));
                    }
                } else {
                    $.easyui.messager.alert(data);
                }
            }
        });
    }

    function save() {
        var row = dg.datagrid('getSelected');
        var roleId = row.id;
        parent.$.messager.confirm('提示', '确认要保存修改？', function (data) {
            if (data) {
                var newPermissionList = [];
                var data = permissionDg.treegrid('getSelections');
                for (var i = 0, j = data.length; i < j; i++) {
                    newPermissionList.push(data[i].id);
                }

                if (roleId == null) {
                    parent.$.messager.show({title: "提示", msg: "请选择FTP！", position: "bottomRight"});
                    return;
                }
                $.ajax({
                    async: false,
                    type: 'POST',
                    data: JSON.stringify(newPermissionList),
                    contentType: 'application/json;charset=utf-8',
                    url: "${ctx}/sysftpinfo/" + roleId + "/updatePermission",
                    success: function (data) {
                        successTip(data);
                    }
                });
            }
        });
    }

    function del() {
        var row = dg.datagrid('getSelected');
        if (rowIsNull(row)) return;
        parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
            if (data) {
                $.ajax({
                    type: 'get',
                    url: "${ctx}/sysftpinfo/delete/" + row.id,
                    success: function (data) {
                        successTip(data, dg);
                    }
                });
                //dg.datagrid('reload'); //grid移除一行,不需要再刷新
            }
        });
    }
    //创建查询对象并查询
    function cx() {
        var obj = $("#searchFrom").serializeObject();
        dg.datagrid('load', obj);
    }
    function upd() {
        var row = dg.datagrid('getSelected');
        if (rowIsNull(row)) return;
        var rowIndex = row.id;
        $.ajaxSetup({type: 'GET'});
        d = $("#dlg").dialog({
            title: '修改FTPS信息',
            width: 400,
            height: 450,
            href: '${ctx}/sysftpinfo/update/' + rowIndex,
            maximizable: true,
            modal: true,
            buttons: [{
                text: '确认',
                handler: function () {
                    $("#mainform").submit();
                }
            }, {
                text: '取消',
                handler: function () {
                    d.panel('close');
                }
            }]
        });
    }
    //表單進度查詢頁面查詢條件重置
    function listSearchReset() {
        $("#searchFrom").form("reset");
    }

    function staus(value, row) {
        if (value == 1) {
            return "是";
        }else if (value == 2) {
            return "否";
        }
    }
</script>
</html>
