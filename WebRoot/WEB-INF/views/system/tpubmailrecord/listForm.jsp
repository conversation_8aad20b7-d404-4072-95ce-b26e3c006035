<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>郵件發送記錄表</title>
	<script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
	</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/tpubmailrecord/${action}" method="post">
		<table class="formTable">
		    			<tr>
				<td>${column.comments}：</td>
				<td>
				    															<input id="id" name="id" class="easyui-validatebox" data-options="width: 150" value="${tPubMailrecord.id }" />
									</td>
			</tr>
						<tr>
				<td>創建人：</td>
				<td>
				    										<input id="createBy" name="createBy" class="easyui-validatebox" data-options="width: 150" value="${tPubMailrecord.createBy }" />
									</td>
			</tr>
						<tr>
				<td>創建時間：</td>
				<td>
				    										<input id="createDate" name="createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 150" value="<fmt:formatDate value="${tPubMailrecord.createDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>更新者：</td>
				<td>
				    										<input id="updateBy" name="updateBy" class="easyui-validatebox" data-options="width: 150" value="${tPubMailrecord.updateBy }" />
									</td>
			</tr>
						<tr>
				<td>更新時間：</td>
				<td>
				    										<input id="updateDate" name="updateDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 150" value="<fmt:formatDate value="${tPubMailrecord.updateDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>刪除標識：</td>
				<td>
				    										<input id="delFlag" name="delFlag" class="easyui-validatebox" data-options="width: 150" value="${tPubMailrecord.delFlag }" />
									</td>
			</tr>
						<tr>
				<td>用戶：</td>
				<td>
				    										<input id="username" name="username" class="easyui-validatebox" data-options="width: 150" value="${tPubMailrecord.username }" />
									</td>
			</tr>
						<tr>
				<td>表單類型：</td>
				<td>
				    										<input id="ordertype" name="ordertype" class="easyui-validatebox" data-options="width: 150" value="${tPubMailrecord.ordertype }" />
									</td>
			</tr>
						<tr>
				<td>任務編號：</td>
				<td>
				    										<input id="serialno" name="serialno" class="easyui-validatebox" data-options="width: 150" value="${tPubMailrecord.serialno }" />
									</td>
			</tr>
						<tr>
				<td>駁回主管：</td>
				<td>
				    										<input id="chargerman" name="chargerman" class="easyui-validatebox" data-options="width: 150" value="${tPubMailrecord.chargerman }" />
									</td>
			</tr>
						<tr>
				<td>審核主管：</td>
				<td>
				    										<input id="dusername" name="dusername" class="easyui-validatebox" data-options="width: 150" value="${tPubMailrecord.dusername }" />
									</td>
			</tr>
						<tr>
				<td>表單狀態  2 審核中 3審核完成 4駁回：</td>
				<td>
				    										<input id="orderstatus" name="orderstatus" class="easyui-validatebox" data-options="width: 150" value="${tPubMailrecord.orderstatus }" />
									</td>
			</tr>
						<tr>
				<td>發送狀態  0 未發送   1 已發送：</td>
				<td>
				    										<input id="sendStatus" name="sendStatus" class="easyui-validatebox" data-options="width: 150" value="${tPubMailrecord.sendStatus }" />
									</td>
			</tr>
						<tr>
				<td>接收郵箱：</td>
				<td>
				    										<input id="usermail" name="usermail" class="easyui-validatebox" data-options="width: 150" value="${tPubMailrecord.usermail }" />
									</td>
			</tr>
						<tr>
				<td>驗證字符串：</td>
				<td>
				    										<input id="validStr" name="validStr" class="easyui-validatebox" data-options="width: 150" value="${tPubMailrecord.validStr }" />
									</td>
			</tr>
						<tr>
				<td>表單類型：</td>
				<td>
				    										<input id="url" name="url" class="easyui-validatebox" data-options="width: 150" value="${tPubMailrecord.url }" />
									</td>
			</tr>
						<tr>
				<td>表單類型：</td>
				<td>
				    										<input id="urlip" name="urlip" class="easyui-validatebox" data-options="width: 150" value="${tPubMailrecord.urlip }" />
									</td>
			</tr>
					</table>
	</form>
  </div>
<script src='${ctx}/static/js/system/tpubmailrecord.js?"+Math.random()"'></script>
<script type="text/javascript">
   //提交表单
$('#mainform').form({
    onSubmit: function(){
        var isValid = $(this).form('validate');
        return isValid;	// 返回false终止表单提交
    },
    success:function(data){
        successTip(data,dg,d);
    }
});
</script>
</body>
</html>