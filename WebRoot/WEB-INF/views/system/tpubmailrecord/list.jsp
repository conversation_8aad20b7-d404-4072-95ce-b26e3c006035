<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>郵件發送記錄表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input type="text" name="filter_EQS_serialno" class="easyui-validatebox"
               data-options="width:200,prompt: '任務編碼'"/>
        <input type="text" name="filter_GTD_createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
               data-options="width:150,prompt: '發送开始日期'"/>
        - <input type="text" name="filter_LTD_createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
                 data-options="width:150,prompt: '發送结束日期'"/>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
           onclick="listSearchReset()">重置</a>
    </form>
</div>
<table id="dg"></table>
<div id="dlg"></div>
<div id="menu" class="easyui-menu" style="width: 30px; display: none;">
    <div id="btn_Add" data-options="iconCls:'icon-redo'" onclick="sendMails()">重新發送</div>
    <!--具体的菜单事件请自行添加，跟toolbar的方法是基本一样的-->
</div>
<script src="${ctx}/static/js/system/tpubmailrecord.js?random=<%= Math.random()%>"></script>
</body>
</html>