<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title></title>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>

</head>
<body>
<div>
    <form id="mainform" action="${ctx}/system/dict/${action}" method="post">
        <table class="formTable">
            <tr>
                <td>标签名：</td>
                <td>
                    <input type="hidden" name="id" value="${id }"/>
                    <input name="label" type="text" style="width: 270px;" value="${dict.label }"
                           class="easyui-validatebox" required="required"/>
                </td>
            </tr>
            <tr>
                <td>值：</td>
                <td><input name="value" type="text" style="width: 270px" value="${dict.value }"
                           class="easyui-validatebox" required="required"/></td>
            </tr>
            <tr>
                <td>类型：</td>
                <td><input name="type" type="text" style="width: 270px" value="${dict.type}" class="easyui-validatebox"
                           required="required"/></td>
            </tr>
            <tr>
                <td>是否分組:</td>
                <td><input type="radio" value="1" name="isGroup" checked onclick="hiddenCode(this.value);"/>是<input
                        type="radio" value="0" name="isGroup" onclick="hiddenCode(this.value);"/>否
                    &nbsp;&nbsp;<font color="red">(通過類型只能查詢到一條的是不需要分組的)</font>
                </td>
            </tr>
            <tr>
                <td>唯一標識：</td>
                <td><input name="codeUniq" type="text" style="width: 270px" value="${dict.codeUniq}" class="easyui-validatebox"
                           required="required"/></td>
            </tr>
            <tr>
                <td>排序：</td>
                <td><input name="sort" type="text" style="width: 270px" value="${dict.sort}" class="easyui-validatebox"
                           required="required"/></td>
            </tr>
            <tr>
                <td>描述：</td>
                <td><textarea rows="4" name="description"
                              style="font-size: 12px;font-family: '微软雅黑';width: 270px">${dict.description}</textarea>
                </td>
            </tr>
        </table>
    </form>
</div>
<script type="text/javascript">
    $(function () {
        $('#mainform').form({
            onSubmit: function () {
                var isValid = $(this).form('validate');
                return isValid;	// 返回false终止表单提交
            },
            success: function (data) {
                successTip(data, dg, d);
            }
        });
//        var isGroup= $("input[type='radio']:checked").val();
        if("${dict.isGroup}"=='0'){
            $("input[type=radio][name='isGroup']").attr("disabled",'true');
        }
        $("input[type=radio][name='isGroup'][value='${dict.isGroup}']").attr("checked",'checked');
        hiddenCode(${dict.isGroup});
    });
    function hiddenCode(obj) {
        if (obj == '0') {
            $("input[name='codeUniq']").val("");
            $("input[name='codeUniq']").removeAttr("required");
            $("input[name='codeUniq']").removeAttr("class");
            $("input[name='codeUniq']").attr("disabled", "true");
        }else{
            $("input[name='codeUniq']").removeAttr("disabled");
            $("input[name='codeUniq']").attr("class","easyui-validatebox");
            $("input[name='codeUniq']").attr("required","true");
        }
    };
</script>
</body>
</html>