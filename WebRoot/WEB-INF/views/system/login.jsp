<%@ page contentType="text/html;charset=UTF-8" %>
<%@ page import="org.apache.shiro.web.filter.authc.FormAuthenticationFilter" %>
<%@ page import="org.apache.shiro.authc.ExcessiveAttemptsException" %>
<%@ page import="org.apache.shiro.authc.IncorrectCredentialsException" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fox" uri="/foxconn-tags" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<%
    String error = (String) request.getAttribute(FormAuthenticationFilter.DEFAULT_ERROR_KEY_ATTRIBUTE_NAME);
    request.setAttribute("error", error);
%>
<html>
<head>
    <title><fox:systemName></fox:systemName></title>
    <meta http-equiv="X-UA-Compatible" content="IE=8"/>
    <link rel="icon" href="${ctx}/favicon.ico" type="image/x-icon"/>
    <link rel="shortcut icon" href="${ctx}/favicon.ico" type="image/x-icon"/>
    <link rel="bookmark" href="${ctx}/favicon.ico" type="image/x-icon"/>
    <script src="${ctx}/static/plugins/easyui/jquery/jquery-3.6.1.min.js"></script>
    <script src="${ctx}/static/plugins/placeholder/placeholder2.js"></script>
    <link rel="stylesheet" type="text/css" href="${ctx}/static/css/bglogin_new.css?random=20231117"/>
    <script>
        var captcha;

        function refreshCaptcha() {
            document.getElementById("img_captcha").src = "${ctx}/static/images/kaptcha.jpg?t=" + Math.random();
        }

        $(document).ready(function () {
            if (window != top) {
                top.location.href = location.href;
            }
        });
    </script>
</head>
<body>
<form id="loginForm" action="${ctx}/admin/login" method="post">
    <div class="login_wrapper">
        <div class="login_title">
            <label><fox:systemName></fox:systemName></label>
        </div>
        <div class="login_main">
            <div class="login_info">
                <div class="login_main_errortip" style="text-align: center;font-size: 18px;font-weight: bold;">&nbsp;
                </div>
                <div class="userAndPass">
                    <div class="inputUser">
                        <input type="text" class="inputstyle" id="unam" name="unam" value="" autocomplete="off"
                               onblur="toUperCaseString(this);" placeholder="用戶名為工號">
                        <span class="login_span">
                                    <img src="${ctx}/static/images/user.png" class="login_img">
                            </span>
                    </div>
                    <div class="inputPass">
                        <input type="password" class="inputstyle" id="pwd" name="pwd" value=""
                               autocomplete="new-password" placeholder="請輸入密碼"/>
                        <span class="login_span">
                                    <img src="${ctx}/static/images/pwd.png" class="login_img">
                            </span>
                    </div>
                    <div class="inputButton">
                        <button onclick="">
                            <%--<img src="${ctx}/static/images/btn2.png" width="46px" height="46px" onclick="">--%>
                        </button>
                    </div>
                </div>
                <div class="codeOuter">
                    <div class="inputUser"></div>
                    <div class="inputPass">
                        <input type="text" class="codeinput" id="captcha" name="captcha" autocomplete="off"/>
                        <div class="codeInfo">
                            <img alt="验证码" src="${ctx}/static/images/kaptcha.jpg" title="点击更换" id="img_captcha"
                                 onclick="javascript:refreshCaptcha();"
                                 style="display: block;height:40px;width:85px;float:left;"/>
                        </div>
                        <div class="codeUpdate">
                            <a href="javascript:refreshCaptcha();" onclick="javascript:refreshCaptcha();">
                                <font color="white">看不清,換一張</font>
                            </a>
                        </div>
                    </div>
                </div>
                <%--<div class="codeOuter">
                    <div class="inputUser">
                        <div class="login_link">
                            <a id="linka" target="_blank"
                               href="http://itsm.ipebg.efoxconn.com/itsm_info_detail.jsp?recno=FPT2008060001"><font
                                    id="fonta" color="#fc3838">技術支持</font></a>
                        </div>
                    </div>
                </div>--%>
            </div>
        </div>
        <%--<div class="login_footer">
            <label><fox:copyRight></fox:copyRight></label>
        </div>--%>
        <div class="login_footer3 clearfix">
            <div class="div-item item1">
                <a target="_blank" href="${ctx}/systemvideo/videoList">APP操作指南</a>
            </div>
            <div class="div-item item2">
                <label><fox:copyRight></fox:copyRight></label>
            </div>
            <div class="div-item item3">
                <div class="item3-content">
                    <div class="item3-left">
                        <img src="${ctx}/static/images/download.png">
                    </div>
                    <div class="item3-right">
                        APP下載<br>
                        讓簽核更便易
                    </div>
                </div>
            </div>
        </div>
        <%-- <div style="text-align: center;">
             <img style="vertical-align:middle" src="${ctx}/admin/showImg/c97c66136dd14959a2c3f88a22344b89"/><span style="line-height: 40px;font-size: 18px;font-weight: bold"><a href="${ctx}/admin/downLoad">下載chrome瀏覽器</a></span>
         </div>--%>
    </div>
</form>
<c:choose>
    <c:when test="${error eq 'com.foxconn.ipebg.system.utils.CaptchaException'}">
        <script>
            $(".login_main_errortip").html("验证码错误");
        </script>
    </c:when>
    <c:when test="${error eq 'org.apache.shiro.authc.IncorrectCredentialsException'}">
        <script>
            $(".login_main_errortip").html("系統帐号或密码错误");
        </script>
    </c:when>
    <c:when test="${error eq 'com.foxconn.ipebg.common.exception.OneAccountPwdErrerException'}">
        <script>
            $(".login_main_errortip").html("一賬通帐号或密码错误");
        </script>
    </c:when>
    <c:when test="${error eq 'com.foxconn.ipebg.common.exception.BusinessException'}">
        <script>
            $(".login_main_errortip").html("請檢查用戶是否在職");
        </script>
    </c:when>
    <c:when test="${error eq 'com.foxconn.ipebg.system.utils.UserNameOrPasswordException'}">
        <script>
            $(".login_main_errortip").html("用戶名或密碼不能為空");
        </script>
    </c:when>
    <c:when test="${error eq 'com.foxconn.ipebg.common.exception.IpControlNotPassException'}">
        <script>
            $(".login_main_errortip").html("抱歉，您的IP未通過合法性驗證，請致電565+63620/63656");
        </script>
    </c:when>
</c:choose>
<script>
    function toUperCaseString(obj) {
        $(obj).val(obj.value.toUpperCase());
    }
</script>
</body>
</html>
