<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>簽核業務中間表，保存所有簽核業務主信息</title>
	<script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
	</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/tqhallrelation/${action}" method="post">
		<table class="formTable">
		    			<tr>
				<td>主鍵：</td>
				<td>
				    															<input id="id" name="id" class="easyui-validatebox" data-options="width: 150" value="${tQhAllRelation.id }" />
									</td>
			</tr>
						<tr>
				<td>創建人：</td>
				<td>
				    										<input id="createBy" name="createBy" class="easyui-validatebox" data-options="width: 150" value="${tQhAllRelation.createBy }" />
									</td>
			</tr>
						<tr>
				<td>創建時間：</td>
				<td>
				    										<input id="createDate" name="createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 150" value="<fmt:formatDate value="${tQhAllRelation.createDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>更新者：</td>
				<td>
				    										<input id="updateBy" name="updateBy" class="easyui-validatebox" data-options="width: 150" value="${tQhAllRelation.updateBy }" />
									</td>
			</tr>
						<tr>
				<td>更新時間：</td>
				<td>
				    										<input id="updateDate" name="updateDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 150" value="<fmt:formatDate value="${tQhAllRelation.updateDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>刪除標識：</td>
				<td>
				    										<input id="delFlag" name="delFlag" class="easyui-validatebox" data-options="width: 150" value="${tQhAllRelation.delFlag }" />
									</td>
			</tr>
						<tr>
				<td>流程編碼：</td>
				<td>
				    										<input id="workflowid" name="workflowid" class="easyui-validatebox" data-options="width: 150" value="${tQhAllRelation.workflowid }" />
									</td>
			</tr>
						<tr>
				<td>工單流水號：</td>
				<td>
				    										<input id="serialno" name="serialno" class="easyui-validatebox" data-options="width: 150" value="${tQhAllRelation.serialno }" />
									</td>
			</tr>
						<tr>
				<td>工單實例ID：</td>
				<td>
				    										<input id="processid" name="processid" class="easyui-validatebox" data-options="width: 150" value="${tQhAllRelation.processid }" />
									</td>
			</tr>
						<tr>
				<td>流程版本：</td>
				<td>
				    										<input id="version" name="version" class="easyui-validatebox" data-options="width: 150" value="${tQhAllRelation.version }" />
									</td>
			</tr>
						<tr>
				<td>表對應實體名稱：</td>
				<td>
				    										<input id="dtoName" name="dtoName" class="easyui-validatebox" data-options="width: 150" value="${tQhAllRelation.dtoName }" />
									</td>
			</tr>
						<tr>
				<td>字典：DICT_WORKSTATUS，對應表單狀態：</td>
				<td>
				    										<input id="workstatus" name="workstatus" class="easyui-validatebox" data-options="width: 150" value="${tQhAllRelation.workstatus }" />
									</td>
			</tr>
						<tr>
				<td>流程名稱：</td>
				<td>
				    										<input id="wfName" name="wfName" class="easyui-validatebox" data-options="width: 150" value="${tQhAllRelation.wfName }" />
									</td>
			</tr>
					</table>
	</form>
  </div>
<script src='${ctx}/static/js/system/tqhallrelation.js?random=<%= Math.random()%>'></script>
<script type="text/javascript">
   //提交表单
$('#mainform').form({
    onSubmit: function(){
        var isValid = $(this).form('validate');
        return isValid;	// 返回false终止表单提交
    },
    success:function(data){
        successTip(data,dg,d);
    }
});
</script>
</body>
</html>