<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>簽核路徑表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<div>
    <form id="mainform" action="${ctx}/tqhchargepath/${action}" method="post">
        <table class="formTable">
            <input id="ids" name="ids" type="hidden" value="${tQhChargepath.id }"/>
            <input id="isvalid" name="isvalid" type="hidden" value="${tQhChargepath.isvalid }"/>
            <tr>
                <td>廠區：</td>
                <td>
                    <c:if test="${ifAdmin==true}">
                        <input id="factoryid" name="factoryid" class="easyui-combobox"
                               panelHeight="auto" value="${tQhChargepath.factoryid }"
                               data-options="width: 150,validType:'comboxValidate[\'factoryid\',\'请選擇廠區\']'"/>
                    </c:if>
                    <c:if test="${ifAdmin==false}">
                        <input id="factoryid11" name="factoryid11" class="easyui-combobox"
                               panelHeight="auto" value="${userFactoryid }"
                               data-options="disabled:true,width: 150,validType:'comboxValidate[\'factoryid11\',\'请選擇廠區\']'"/>
                        <input id="factoryid" name="factoryid" value="${userFactoryid }" type="hidden"/>
                    </c:if>
                </td>
                <td></td>
            </tr>
            <tr>
                <td>單位代碼：</td>
                <td>
                    <c:if test="${ifAdmin==true}">
                        <input id="deptno" name="deptno" class="easyui-validatebox"
                               data-options="width: 150,required:true"
                               value="${tQhChargepath.deptno }"/>
                    </c:if>
                    <c:if test="${ifAdmin==false}">
                        <input id="deptno" name="deptno" class="easyui-validatebox"
                               data-options="width: 150,required:true" readonly
                               value="${userDeptno }"/>
                    </c:if>
                </td>
            </tr>
            <tr>
                <td>${nameMap['kchargeno_name']}：</td>
                <td>
                    <input id="kchargeno" name="kchargeno" class="easyui-validatebox managerNo"
                           data-options="width: 150"
                           value="${tQhChargepath.kchargeno }" onblur="getUserNameByEmpno(this);"/>
                </td>
                <td>姓名：</td>
                <td><input id="kchargename" name="kchargename" class="easyui-validatebox managerName"
                           data-options="width: 150"
                           readonly="true"
                           value="${tQhChargepath.kchargename }"/></td>
            </tr>
            <tr>
                <td>${nameMap['bchargeno_name']}：</td>
                <td>
                    <input id="bchargeno" name="bchargeno" class="easyui-validatebox managerNo"
                           data-options="width: 150"
                           value="${tQhChargepath.bchargeno }" onblur="getUserNameByEmpno(this);"/>
                </td>
                <td>姓名：</td>
                <td><input id="bchargename" name="bchargename" class="easyui-validatebox managerName"
                           data-options="width: 150"
                           readonly="true"
                           value="${tQhChargepath.bchargename }"/></td>
            </tr>
            <tr>
                <td>${nameMap['cchargeno_name']}：</td>
                <td>
                    <input id="cchargeno" name="cchargeno" class="easyui-validatebox managerNo"
                           data-options="width: 150"
                           value="${tQhChargepath.cchargeno }" onblur="getUserNameByEmpno(this);"/>
                </td>
                <td>姓名：</td>
                <td><input id="cchargename" name="cchargename" class="easyui-validatebox managerName"
                           data-options="width: 150"
                           readonly="true"
                           value="${tQhChargepath.cchargename }"/></td>
            </tr>
            <tr>
                <td>${nameMap['czchargeno_name']}：</td>
                <td>
                    <input id="czchargeno" name="czchargeno" class="easyui-validatebox managerNo"
                           data-options="width: 150"
                           value="${tQhChargepath.czchargeno }" onblur="getUserNameByEmpno(this);"/>
                </td>
                <td>姓名：</td>
                <td><input id="czchargename" name="czchargename" class="easyui-validatebox managerName"
                           data-options="width: 150"
                           readonly="true"
                           value="${tQhChargepath.czchargename }"/></td>
            </tr>
            <tr>
                <td>${nameMap['zchargeno_name']}：</td>
                <td>
                    <input id="zchargeno" name="zchargeno" class="easyui-validatebox managerNo"
                           data-options="width: 150"
                           value="${tQhChargepath.zchargeno }" onblur="getUserNameByEmpno(this);"/>
                </td>
                <td>姓名：</td>
                <td><input id="zchargename" name="zchargename" class="easyui-validatebox managerName"
                           data-options="width: 150"
                           readonly="true"
                           value="${tQhChargepath.zchargename }"/></td>
            </tr>
            <tr>
                <td>${nameMap['czcchargeno_name']}：</td>
                <td>
                    <input id="czcchargeno" name="czcchargeno" class="easyui-validatebox managerNo"
                           data-options="width: 150"
                           value="${tQhChargepath.czcchargeno }" onblur="getUserNameByEmpno(this);"/>
                </td>
                <td>姓名：</td>
                <td><input id="czcchargename" name="czcchargename" class="easyui-validatebox managerName"
                           data-options="width: 150"
                           readonly="true"
                           value="${tQhChargepath.czcchargename }"/></td>
            </tr>
            <tr>
                <td>${nameMap['zcchargeno_name']}：</td>
                <td>
                    <input id="zcchargeno" name="zcchargeno" class="easyui-validatebox managerNo"
                           data-options="width: 150"
                           value="${tQhChargepath.zcchargeno }" onblur="getUserNameByEmpno(this);"/>
                </td>
                <td>姓名：</td>
                <td><input id="zcchargename" name="zcchargename" class="easyui-validatebox managerName"
                           data-options="width: 150"
                           readonly="true"
                           value="${tQhChargepath.zcchargename }"/></td>
            </tr>
            <tr>
                <td>${nameMap['pcchargeno_name']}：</td>
                <td>
                    <input id="pcchargeno" name="pcchargeno" class="easyui-validatebox managerNo"
                           data-options="width: 150"
                           value="${tQhChargepath.pcchargeno }" onblur="getUserNameByEmpno(this);"/>
                </td>
                <td>姓名：</td>
                <td><input id="pcchargename" name="pcchargename" class="easyui-validatebox managerName"
                           data-options="width: 150"
                           readonly="true"
                           value="${tQhChargepath.pcchargename }"/></td>
            </tr>
            <tr>
                <td>${nameMap['cqchargeno_name']}：</td>
                <td>
                    <input id="cqchargeno" name="cqchargeno" class="easyui-validatebox managerNo"
                           data-options="width: 150"
                           value="${tQhChargepath.cqchargeno }" onblur="getUserNameByEmpno(this);"/>
                </td>
                <td>姓名：</td>
                <td><input id="cqchargename" name="cqchargename" class="easyui-validatebox managerName"
                           data-options="width: 150"
                           readonly="true"
                           value="${tQhChargepath.cqchargename }"/></td>
            </tr>
            <tr>
                <td>${nameMap['sychargeno_name']}：</td>
                <td>
                    <input id="sychargeno" name="sychargeno" class="easyui-validatebox managerNo"
                           data-options="width: 150"
                           value="${tQhChargepath.sychargeno }" onblur="getUserNameByEmpno(this);"/>
                </td>
                <td>姓名：</td>
                <td><input id="sychargename" name="sychargename" class="easyui-validatebox managerName"
                           data-options="width: 150"
                           readonly="true"
                           value="${tQhChargepath.sychargename }"/></td>
            </tr>
            <tr>
                <td>申請人工號：</td>
                <td>
                    <input id="applyempno" name="applyempno" class="easyui-validatebox" data-options="width: 150,required:true"
                           value="${tQhChargepath.applyempno }" onblur="getUserNameByEmpno(this);"/>
                </td>
                <td>姓名：</td>
                <td><input id="applyusername" name="applyusername" class="easyui-validatebox" data-options="width: 150,required:true"
                           readonly="true"
                           value="${tQhChargepath.applyusername }"/></td>
            </tr>
        </table>
    </form>
</div>
<script type="text/javascript">
    $(function(){
        $.get(ctx + '/tqhfactoryidconfig/allFactorys/', function(result) {
            $("#factoryid").combobox({
                data : result,
                valueField : "factoryid",
                textField : "factoryname",
                editable : false,
                panelHeight : 350,
                loadFilter : function(data) {
                    data.unshift({
                        factoryid : '',
                        factoryname : '請選擇廠區'
                    });
                    return data;
                }
            });
        },"json");
        $.get(ctx + '/tqhfactoryidconfig/allFactorys/', function(result) {
            $("#factoryid11").combobox({
                data : result,
                valueField : "factoryid",
                textField : "factoryname",
                editable : false,
                panelHeight : 350,
                loadFilter : function(data) {
                    data.unshift({
                        factoryid : '',
                        factoryname : '請選擇廠區'
                    });
                    return data;
                }
            });
        },"json");
    });
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid =true;
            var factoryid=$('#factoryid').combobox('getValue');
            var deptno=$("#deptno").val();
            if (!$(this).form('validate')) {
                isValid=false;
                return isValid;
            }
            if (!checkManager(deptno,factoryid)){
                isValid=false;
                return isValid;
            }
            return isValid;
        },
        success: function (data) {
            successTip(data, dg, d);
        }
    });
    //檢查是不是所有的主管都為空
    function checkManager(deptno,factoryid) {
        var emptyCount = 0;
        var empnoCount = 0;
        //var empdeptno = 0;
        $(".managerName").each(function () {
            if ($(this).val() == null || $(this).val() == '') {
                emptyCount++;
            }
        });
        $(".managerNo").each(function () {
            if ($(this).val() == null || $(this).val() == '') {
                empnoCount++;
            }
        });

        if (emptyCount < 10) {
            if (emptyCount != empnoCount) {
                alert("審核主管姓名不能為空");
                return false;
            } else {
                return true;
            }
        } else {
            alert("請至少填寫一位審核主管");
            $("#kchargeno").focus();
            return false;
        }
    }
    function getUserNameByEmpno(obj) {
        if (obj.value != null && obj.value != "") {
            $.post(ctx + '/system/user/getUserInfo/', {
                empno: obj.value
            }, function (data) {
                if (!data) {
                    $.messager.alert("溫馨提示", "工號不存在", "error");
                    if (obj.name == 'applyempno') {
                        $("#applyempno").val("");
                        $("#applyusername").val("");
                    }else{
                        $("#" + obj.name.replace("no", "no")).val("");
                        $("#" + obj.name.replace("no", "name")).val("");
                    }
                } else {
                    if (obj.name == 'applyempno') {
                        $("#applyusername").val(data.empname);
                    } else {
                        $("#" + obj.name.replace("no", "name")).val(data.empname);
                    }
                }
            }, 'json');
        }else{
            if (obj.name == 'applyempno') {
                $("#applyempno").val("");
                $("#applyusername").val("");
            }else{
                $("#" + obj.name.replace("no", "name")).val('');
            }
        }
    }
</script>
</body>
</html>