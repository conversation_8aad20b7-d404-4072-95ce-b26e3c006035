<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>簽核路徑表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input id="factoryids" name="filter_EQS_factoryid" class="easyui-combobox"
               data-options="width: 150,url:'${ctx}/tqhfactoryidconfig/allFactorys/',
  valueField:'factoryid',
  textField:'factoryname',onLoadSuccess:function(){
                                            $('#factoryids').combobox('setText','請選擇');
                                        }"/>
        <input type="text" name="filter_EQS_deptno" class="easyui-validatebox"
               data-options="width:150,prompt: '單位代碼'"/>
        <input type="text" name="filter_EQS_applyempno" class="easyui-validatebox"
               data-options="width:150,prompt: '申請人工號'"/>
        <input type="text" name="filter_GED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd"
               data-options="width:150,prompt: '創建开始日期'"/>
        - <input type="text" name="filter_LED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd"
                 data-options="width:150,prompt: '創建结束日期'"/>
        <%--<input id="qysjzt" style="width:100px" class="easyui-validatebox" name="filter_EQS_workstatus"/>--%>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel" onclick="downLoad();">模板下載</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel" onclick="importExcel();">批量导入</a>
    </form>
    <div>
        <shiro:hasPermission name="system:tqhchargepath:add">
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true"
               onclick="add();">添加</a>
            <span class="toolbar-item dialog-tool-separator"></span>
        </shiro:hasPermission>
        <shiro:hasPermission name="system:tqhchargepath:delete">
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true"
               data-options="disabled:false" onclick="batchDel()">批量删除</a>
            <span class="toolbar-item dialog-tool-separator"></span>
        </shiro:hasPermission>
        <shiro:hasPermission name="system:tqhchargepath:delete">
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true"
               data-options="disabled:false" onclick="del()">删除</a>
            <span class="toolbar-item dialog-tool-separator"></span>
        </shiro:hasPermission>
        <shiro:hasPermission name="system:tqhchargepath:update">
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true"
               onclick="upd()">修改</a>
            <span class="toolbar-item dialog-tool-separator"></span>
        </shiro:hasPermission>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true"
           onclick="setInvalid()">置為無效</a>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true"
           onclick="setValid()">置為有效</a>
        <span class="toolbar-item dialog-tool-separator"></span>
    </div>

</div>
<table id="dg"></table>
<div id="dlg"></div>
<div id="optionWin" class="easyui-window" title="簽核路徑批量導入" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%">
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="batchFile" type="file" style="width: 300px"
                           accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>

                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <span ID="labelListAddResult"></span><a href="${ctx}/tqhchargepath/downLoad/errorExcel" id="downloadError"
                                                            plain="true">查看錯誤信息</a>
                </td>
            </tr>
        </table>
    </form>
</div>
<script src="${ctx}/static/js/system/tqhchargepath.js?random=<%= Math.random()%>"></script>
</body>
</html>