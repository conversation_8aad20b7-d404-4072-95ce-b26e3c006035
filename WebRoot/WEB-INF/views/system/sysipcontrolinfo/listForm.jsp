<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>ip管控用戶信息</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/sysipcontrolinfo/${action}" method="post">
    <table class="formTable">
        <tr>
            <td>ip地址：</td>
            <td>
                <input type="hidden" name="id" value="${sysIpControlInfo.id}"/>
                <input name="ipAdress" type="text" style="width: 270px" value="${sysIpControlInfo.ipAdress }"
                       class="easyui-validatebox"/></td>
        </tr>
        <tr>
            <td>備註：</td>
            <td><textarea rows="4" name="remark"
                          style="font-size: 12px;font-family: '微软雅黑';width: 270px">${sysIpControlInfo.remark}</textarea>
            </td>
        </tr>
    </table>
</form>
</div>
<script type="text/javascript">
    $(function () {
        $('#mainform').form({
            onSubmit: function () {
                var isValid = $(this).form('validate');
                return isValid;	// 返回false终止表单提交
            },
            success: function (data) {
                successTip(data, dg, d);
            }
        });
    });
</script>
</body>
</html>