<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>進料檢料緊急放行單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/wfjinliaoprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfjinliaoprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfjinliaoprocessEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">進料檢料緊急放行單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfjinliaoprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfjinliaoprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfjinliaoprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfjinliaoprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfjinliaoprocessEntity.makerno}/${wfjinliaoprocessEntity.makername}</div>
        <div class="clear"></div>
        <table  class="formList">
            <tr>
                <td>
					<table class="formList">
							<tr align="center">
								<td>廠區</td>
								<td colspan="2"><input id="factory" name="factory" class="easyui-combobox"
									value="${wfjinliaoprocessEntity.factory }" disabled="disabled" data-options="width: 120,required:true" /></td>
								<td>進貨驗收單號</td>
								<td colspan="2"><input id="goodscheckid"
									name="goodscheckid" class="easyui-validatebox inputCss"
									data-options="width:150" readonly
									value="${wfjinliaoprocessEntity.goodscheckid }" /></td>
								<td>廠商名稱</td>
								<td><input id="facname" name="facname"
									class="easyui-validatebox inputCss"
									data-options="width:150,required:true" readonly
									value="${wfjinliaoprocessEntity.facname }" /></td>
								<td>進貨日期</td>
								<td><input id="billdate" name="billdate" disabled="disabled"
									class="Wdate" data-options="width:180,required:true"
									style="width:180px"
									value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${wfjinliaoprocessEntity.billdate}"/>"
									onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" /></td>
							</tr>
							<tr align="center">
								<td>料號</td>
								<td colspan="2"><input id="pnhonhai" name="pnhonhai"
									class="easyui-validatebox inputCss"
									data-options="width:150,required:true" readonly
									value="${wfjinliaoprocessEntity.pnhonhai }" /></td>
								<td>物料名稱</td>
								<td colspan="4"><input id="material" name="material"
									class="easyui-validatebox inputCss" data-options="width:250"
									readonly value="${wfjinliaoprocessEntity.material }" /></td>
								<td>收貨數量</td>
								<td><input id="billnum" name="billnum"
									class="easyui-validatebox inputCss"
									data-options="width:150,required:true" readonly
									value="${wfjinliaoprocessEntity.billnum }" /></td>
							</tr>
							<tr align="center">
								<td>緊急放行說明</td>
								<td align="left" colspan="9"><textarea id="passdesc"
										name="passdesc" data-options="required:true" maxlength="200" readonly
										class="easyui-validatebox" style="width:800px;height:80px;"
										rows="5" cols="6">${wfjinliaoprocessEntity.passdesc}</textarea></td>
							</tr> 
							
		
                       <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${wfjinliaoprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
										<td>進料檢料記錄表編號</td>
										<td colspan="9" align="left" ><input id="tableid" name="tableid"
											class="easyui-validatebox inputCss"
											data-options="width:300" readonly
											value="${wfjinliaoprocessEntity.tableid }" /></td>
						</tr>
                        <tr align="center">
									<td>檢驗結果(200字以內)</td>
									<td align="left" colspan="9"><textarea id="checkresult"
											name="checkresult" readonly oninput="return LessThan(this);"
											class="easyui-validatebox" style="width:800px;height:80px;"
											rows="5" cols="6">${wfjinliaoprocessEntity.checkresult}</textarea></td>
						</tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','進料檢料緊急放行單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfjinliaoprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                   style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>

                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
<div id="dlg"></div>
<script src='${ctx}/static/js/qihua/wfjinliaoprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>