<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>進料檢料緊急放行單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
	type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfjinliaoprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfjinliaoprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfjinliaoprocessEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfjinliaoprocessEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfjinliaoprocessEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfjinliaoprocessEntity.makerdeptno }"/>
    <div class="commonW">
    <div class="headTitle">進料檢料緊急放行單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfjinliaoprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfjinliaoprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfjinliaoprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfjinliaoprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfjinliaoprocessEntity.makerno}">
			  <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfjinliaoprocessEntity.makerno}">
                <div class="position_R margin_R">填單人：${wfjinliaoprocessEntity.makerno}/${wfjinliaoprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table  class="formList">
            <tr>
                <td>
                    <table class="formList">
                    <tr align="center">
                      <td>廠區&nbsp;<font color="red">*</font></td>
                      <td colspan="2">
                      <input id="factory" name="factory" class="easyui-combobox"  value="${wfjinliaoprocessEntity.factory }"  data-options="width: 120,required:true"/>
                      <!-- <input id="dealdeptname" name="dealdeptname" type="hidden"/> -->
                      </td>
                      <td>進貨驗收單號</td>
                      <td colspan="2"><input id="goodscheckid" name="goodscheckid" class="easyui-validatebox" data-options="width:150"
							value="${wfjinliaoprocessEntity.goodscheckid }"/></td>
                      <td>廠商名稱&nbsp;<font color="red">*</font></td>
                      <td><input id="facname" name="facname" class="easyui-validatebox" data-options="width:150,required:true"
							value="${wfjinliaoprocessEntity.facname }"/></td>
                      <td>進貨日期&nbsp;<font color="red">*</font></td>
                      <td><input id="billdate" name="billdate"
									class="Wdate" data-options="width:180,required:true" style="width:150px"
									value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${wfjinliaoprocessEntity.billdate}"/>"   
									onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" />		
					  </td>
                    </tr>
                    <tr align="center">
                      <td>料號&nbsp;<font color="red">*</font></td>
                      <td colspan="2"><input id="pnhonhai" name="pnhonhai" class="easyui-validatebox" data-options="width:150,required:true"
							value="${wfjinliaoprocessEntity.pnhonhai }"/></td>
                      <td>物料名稱</td>
                      <td colspan = "4"><input id="material" name="material" class="easyui-validatebox" data-options="width:250"
							value="${wfjinliaoprocessEntity.material }"/></td>
                      <td>收貨數量&nbsp;<font color="red">*</font></td>
                      <td><input id="billnum" name="billnum" class="easyui-validatebox" data-options="width:150,required:true" onblur = "valdNumber(this)"
							value="${wfjinliaoprocessEntity.billnum }"/></td>
                    </tr>
                    <tr align = "center">
                    <td>緊急放行說明(200字以內)&nbsp;<font color="red">*</font></td>
                    <td align="left" colspan="9"><textarea id="passdesc"
										name="passdesc" 
										oninput="return LessThan(this);"
                                        onchange="return LessThan(this);"
                                        onpropertychange="return LessThan(this);"
										data-options="required:true" maxlength="200"
										class="easyui-validatebox" style="width:800px;height:80px;"
										rows="5" cols="6">${wfjinliaoprocessEntity.passdesc}</textarea><span id="txtNum"></span></td>
                    </tr>
                                            <tr align="center">
                            <td>附件<font color="red">*</font></td>
                            <td colspan="9" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button"
                                                                   value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
								<input type="hidden" id="attachids" name="attachids" value="${wfjinliaoprocessEntity.attachids }"/>
								<c:choose>
									<c:when test="${file.size()>0}">
									  <input type="hidden" id="attachids" name="attachids" value="${wfjinliaoprocessEntity.attachids }" />
									</c:when>
									<c:otherwise>
										<input type="hidden" id="attachids" name="attachids" value="" />
									</c:otherwise>
								</c:choose>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
           <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','進料檢料緊急放行單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                <tr>
                <td>
                    <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                        <tr>
                            <td style="border:none">
                                <table width="18%" style="float: left;margin-left: 5px;"  id="applychargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">申請人單位主管</td>
                                                    <td style="border: none;">
                                                             <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(72,'applychargeTable','applychargeno','applychargename',$('#factory').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="applychargeno" name="applychargeno"
                                                   class="easyui-validatebox" data-options="width:80,required:true"
                                                   readonly
                                                   value="${wfjinliaoprocessEntity.applychargeno }"/><font color="red">*</font>
                                            /<input id="applychargename" name="applychargename"
                                                    readonly class="easyui-validatebox"
                                                    data-options="width:80,required:true"
                                                    value="${wfjinliaoprocessEntity.applychargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;"
                                       id="huoqianTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: center;">會簽<a href="#" onclick="addHq();">添加一位</a></td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="huoqianno" onblur="getUserNameByEmpno(this);"
                                                   name="huoqianno" class="easyui-validatebox" data-options="width: 80"
                                                   value="${wfjinliaoprocessEntity.huoqianno }"/>
                                            /<input id="huoqianname" name="huoqianname"
                                                    class="easyui-validatebox"
                                                    data-options="width: 80"  readonly  value="${wfjinliaoprocessEntity.huoqianname }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;"  id="facuserTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">廠部使用單位主管</td>
                                                    <td style="border: none;">
                                                             <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(73,'facuserTable','facuserno','facusername',$('#factory').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="facuserno" name="facuserno"
                                                   class="easyui-validatebox" data-options="width:80,required:true"
                                                   readonly
                                                   value="${wfjinliaoprocessEntity.facuserno }"/><font color="red">*</font>
                                            /<input id="facusername" name="facusername"
                                                    readonly class="easyui-validatebox"
                                                    data-options="width:80,required:true"
                                                    value="${wfjinliaoprocessEntity.facusername }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;"  id="jinliaodeptTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">進料檢驗單位主管</td>
                                                    <td style="border: none;">
                                                             <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(74,'jinliaodeptTable','jinliaodeptno','jinliaodeptname',$('#factory').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="jinliaodeptno" name="jinliaodeptno" class="easyui-validatebox" data-options="width:80,required:true"
                                                   readonly value="${wfjinliaoprocessEntity.jinliaodeptno }"/><font color="red">*</font>
                                            /<input id="jinliaodeptname" name="jinliaodeptname"  readonly class="easyui-validatebox"  data-options="width:80,required:true"
                                                    value="${wfjinliaoprocessEntity.jinliaodeptname }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;"  id="jianyanTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">檢驗人員</td>
                                                    <td style="border: none;">
                                                             <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(75,'jianyanTable','jianyanno','jianyanname',$('#factory').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="jianyanno" name="jianyanno" class="easyui-validatebox" data-options="width:80,required:true"
                                                   readonly value="${wfjinliaoprocessEntity.jianyanno }"/><font color="red">*</font>
                                            /<input id="jianyanname" name="jianyanname" readonly class="easyui-validatebox"
                                                    data-options="width:80,required:true"  value="${wfjinliaoprocessEntity.jianyanname }"/>
                                        </td>
                                    </tr>
                                </table>
                             </td>
                        </tr>          
                    </table>
                </td>
           </tr>
            <tr>
                <td colspan="10" style="text-align:left;">
                    <iframe id="qianheLogFrame" name="qianheLogFrame"
                            src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfjinliaoprocessEntity.serialno}"
                            width="100%"></iframe>
                </td>
            </tr>
            <tr>
                <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                    <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                       data-options="iconCls:'icon-add'"
                       style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                    <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                       style="width: 100px;" onclick="canelTask('${wfjinliaoprocessEntity.serialno }');">取消申請</a>
                </td>
            </tr>
        </table>
        </td>
            </tr>
        </table>
	</div>
	<input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
	</form>
  </div>
<script src='${ctx}/static/js/qihua/wfjinliaoprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>