<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>製程報廢表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfprocessscrap/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfProcessScrapEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfProcessScrapEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfProcessScrapEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfProcessScrapEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfProcessScrapEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${wfProcessScrapEntity.makerfactoryid }"/>
    <%--<input id="applycostno" name="applycostno" type="hidden" value="${wfProcessScrapEntity.applycostno }"/>--%>
    <div class="commonW">
        <div class="headTitle">製程報廢單一</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfProcessScrapEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfProcessScrapEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfProcessScrapEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfProcessScrapEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfProcessScrapEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfProcessScrapEntity.makerno}">
            <div class="position_R margin_R">填單人：${wfProcessScrapEntity.makerno}/${wfProcessScrapEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1" colspan="2">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 150,required:true"
                                       value="${wfProcessScrapEntity.applyno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="4%">申請人姓名</td>
                            <td width="6%" class="td_style1" colspan="2">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wfProcessScrapEntity.applyname }"/>
                            </td>
                            <td width="4%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1" colspan="3">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="400" value="${wfProcessScrapEntity.applyfactoryid }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applyfactoryid\',\'请选择所在廠區\']',onChange:function(){auditTableReset('auditTable');}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1" colspan="2">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfProcessScrapEntity.applydeptno }"/>
                            </td>
                            <td>申請單位&nbsp;<font color="red">*</font></td>
                            <td colspan="6" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox"
                                       style="width:90%;" data-options="required:true"
                                       value="${wfProcessScrapEntity.applydeptname }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="4%">聯繫方式&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1" colspan="2">
                                <input id="applyphone" name="applyphone"
                                       class="easyui-validatebox" data-options="width: 150,required:true"
                                       value="${wfProcessScrapEntity.applyphone }"/>
                            </td>
                            <td>申請人郵件&nbsp;<font color="red">*</font></td>
                            <td colspan="6" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       style="width:90%;" data-options="required:true"
                                       value="${wfProcessScrapEntity.applyemail }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">報廢信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">料號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1" colspan="2">
                                <input id="materialcode" name="materialcode" class="easyui-validatebox"
                                       data-options="width: 150,required:true" onblur="queryPNInfo(this);"
                                       value="${wfProcessScrapEntity.materialcode }"/>
                            </td>
                            <td width="4%">機種&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1" colspan="2">
                                <input id="machinetype" name="machinetype"
                                       class="easyui-validatebox"
                                       data-options="width:150,required:true"
                                       value="${wfProcessScrapEntity.machinetype }"/>
                            </td>
                            <td width="4%">品名&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1" colspan="3">
                                <input id="charactername" name="charactername" class="easyui-validatebox"
                                       data-options="width: 150,required:true"
                                       value="${wfProcessScrapEntity.charactername }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="4%">物料形態&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1" colspan="2">
                                <%--<input id="materialform" name="materialform" class="easyui-validatebox"--%>
                                <%--data-options="width: 150,required:true"--%>
                                <%--value="${wfProcessScrapEntity.materialform }"/>--%>
                                <input id="materialform"
                                       name="materialform"
                                       class="easyui-combobox"
                                       data-options="width: 150,panelHeight:400,valueField:'value',textField:'label',required:true,validType:'comboxValidate[\'materialform\',\'請選擇物料形態\']',
																              editable:false,onBeforeLoad:function(){
																              loadMaterialform();}"
                                       style="width:80px;" value="${wfProcessScrapEntity.materialform }"/>
                            </td>
                            <td width="4%">顏色&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1" colspan="2">
                                <input id="color" name="color"
                                       class="easyui-validatebox"
                                       data-options="width:150,required:true"
                                       value="${wfProcessScrapEntity.color }"/>
                            </td>
                            <td width="4%">報廢類別&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1" colspan="3">
                                <%--<input id="scraptype" name="scraptype" class="easyui-validatebox"--%>
                                <%--data-options="width: 150,required:true"--%>
                                <%--value="${wfProcessScrapEntity.scraptype }"/>--%>
                                <input id="scraptype"
                                       name="scraptype"
                                       class="easyui-combobox"
                                       data-options="width: 150,panelHeight:400,valueField:'value',textField:'label',required:true,validType:'comboxValidate[\'scraptype\',\'請選擇報廢類別\']',
																              editable:false,onBeforeLoad:function(){
																              loadScraptype();}"
                                       style="width:80px;" value="${wfProcessScrapEntity.scraptype }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="116px;" colspan="2">項目</td>
                            <td width="4%" colspan="2">廠級代碼</td>
                            <td width="4%" colspan="2">廠別</td>
                            <td width="4%" colspan="2">製程</td>
                            <td width="4%"colspan="2">樓層</td>
                        </tr>
                        <tr align="center">
                            <td width="4%" colspan="2">發生製程&nbsp;<font color="red">*</font></td>
                            <td width="6%" colspan="2">
                                <input id="factorycode" name="factorycode" class="easyui-validatebox"
                                       data-options="width: 150,required:true" onblur="queryFactoryNameInfo('');"
                                       value="${wfProcessScrapEntity.factorycode }"/>
                            </td>
                            <td width="6%" colspan="2">
                                <input id="factorytype" name="factorytype" class="easyui-validatebox"
                                       data-options="width: 220,required:true"
                                       value="${wfProcessScrapEntity.factorytype }"/>
                            </td>
                            <td width="6%" colspan="2">
                                <input id="process" name="process" class="easyui-validatebox"
                                       data-options="width: 150,required:true"
                                       value="${wfProcessScrapEntity.process }"/>
                            </td>
                            <td width="6%" colspan="2">
                                <input id="floor" name="floor" class="easyui-validatebox"
                                       data-options="width: 120,required:true"
                                       value="${wfProcessScrapEntity.floor }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="4%" colspan="2">責任製程&nbsp;<font color="red">*</font></td>
                            <td width="6%" colspan="2">
                                <input id="factorycode1" name="factorycode1" class="easyui-validatebox"
                                       data-options="width: 150,required:true" onblur="queryFactoryNameInfo('1');"
                                       value="${wfProcessScrapEntity.factorycode1 }"/>
                            </td>
                            <td width="6%" colspan="2">
                                <input id="factorytype1" name="factorytype1" class="easyui-validatebox"
                                       data-options="width: 220,required:true"
                                       value="${wfProcessScrapEntity.factorytype1 }"/>
                            </td>
                            <td width="6%" colspan="2">
                                <input id="process1" name="process1" class="easyui-validatebox"
                                       data-options="width: 150,required:true"
                                       value="${wfProcessScrapEntity.process1 }"/>
                            </td>
                            <td width="6%" colspan="2">
                                <input id="floor1" name="floor1" class="easyui-validatebox"
                                       data-options="width: 120,required:true"
                                       value="${wfProcessScrapEntity.floor1 }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">報廢明細</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="bondedgoodsItemTable" width="100%">
                                        <tr align="center">
                                            <td width="2%" colspan="2">序號</td>
                                            <td width="4%" colspan="2">不良區域&nbsp;<font color="red">*</font></td>
                                            <td width="4%" colspan="2">缺陷描述&nbsp;<font color="red">*</font></td>
                                            <td width="4%" colspan="2">報廢數量(單位:pcs/Kg)&nbsp;<font color="red">*</font></td>
                                            <td width="2%" colspan="2">操作</td>
                                        </tr>
                                        <tbody id="info_Body">
                                        <c:if test="${wfProcessScrapEntity.itemsEntity!=null&&wfProcessScrapEntity.itemsEntity.size()>0}">
                                            <c:forEach items="${wfProcessScrapEntity.itemsEntity}" var="itemsEntity"
                                                       varStatus="status">
                                                <tr align="center" id="bondedgoodsItem${status.index+1}">
                                                    <td width="4%" colspan="2">${status.index+1}</td>
                                                    <td width="6%" colspan="2">
                                                        <input id="badarea${status.index}"
                                                               name="itemsEntity[${status.index}].badarea"
                                                               class="easyui-combobox"
                                                               class="easyui-combobox"
                                                               data-options="width: 150,panelHeight:400,valueField:'value',textField:'label',required:true,validType:'comboxValidateSpecal[\'badarea${status.index}\',\'请选择不良區域\']',onBeforeLoad:function(){
																              editable:true,defectTypeList(${status.index});}"
                                                               value="${itemsEntity.badarea }"/>
                                                    </td>
                                                    <td width="6%" colspan="2">
                                                        <input id="bugdescribe${status.index}"
                                                               name="itemsEntity[${status.index}].bugdescribe"
                                                               class="easyui-combobox"
                                                               data-options="width: 150,panelHeight:400,valueField:'value',textField:'label',required:true,validType:'comboxValidateSpecal[\'bugdescribe${status.index}\',\'缺陷描述\']',onBeforeLoad:function(){
																              editable:true,descriptionList(${status.index});}"
                                                               value="${itemsEntity.bugdescribe }"/>
                                                    </td>
                                                    <td width="6%" colspan="2">
                                                        <input id="scrapnum${status.index}"
                                                               name="itemsEntity[${status.index}].scrapnum"
                                                               class="easyui-numberbox"
                                                               data-options="width: 150,required:true,precision:2,groupSeparator:','"
                                                               value="${itemsEntity.scrapnum }"/>
                                                    </td>
                                                    <td width="6%" colspan="2">
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               class="deleteBtnStr"
                                                               onclick="bondedgooddeltr(${status.index+1});return false;"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${wfProcessScrapEntity==null||wfProcessScrapEntity.itemsEntity.size()==0}">
                                            <tr align="center" id="bondedgoodsItem1">
                                                <td width="4%" colspan="2">1</td>
                                                <td width="6%" colspan="2">
                                                    <input id="badarea" name="itemsEntity[0].badarea"
                                                           class="easyui-combobox"
                                                           data-options="width: 150,panelHeight:400,valueField:'value',textField:'label',required:true,validType:'comboxValidateSpecal[\'badarea0\',\'请选择不良區域\']',onBeforeLoad:function(){
																              editable:true,defectTypeList(0);}"
                                                           value="${wfProcessScrapEntity.itemsEntity[0].badarea }"/>
                                                </td>
                                                <td width="6%" colspan="2">
                                                    <input id="bugdescribe" name="itemsEntity[0].bugdescribe"
                                                           class="easyui-validatebox"
                                                           class="easyui-combobox"
                                                           data-options="width: 150,panelHeight:400,valueField:'value',textField:'label',required:true,validType:'comboxValidateSpecal[\'bugdescribe0\',\'请选择缺陷描述\']',onBeforeLoad:function(){
																              editable:true,descriptionList(0);}"/>
                                                </td>
                                                <td width="6%" colspan="2">
                                                    <input id="scrapnum" name="itemsEntity[0].scrapnum"
                                                           class="easyui-numberbox"
                                                           data-options="width: 150,required:true,precision:2,groupSeparator:','"
                                                           value="${wfProcessScrapEntity.itemsEntity[0].scrapnum }"/>
                                                </td>
                                                <td width="6%" colspan="2">
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           class="deleteBtnStr"
                                                           onclick="bondedgooddeltr(1);return false;"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr">
                                            <td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="bondedgoodItemAdd"
                                                       style="width:100px;float:left;" value="添加一筆"/>
                                                <label id="totalNum111" style="float: right;padding-right:100px;color: red;font-size: 20px;font-weight: bold">報廢總數:&nbsp;<label id="totalNum">0</label></label>
                                                <input id="scraptotalnum" name="scraptotalnum" type="hidden"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1" colspan="9">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <c:choose>
                                    <c:when test="${file.size()>0}">
                                        <input type="hidden" id="attachids" name="attachids" value="${wfProcessScrapEntity.attachids }"/>
                                    </c:when>
                                    <c:otherwise>
                                        <input type="hidden" id="attachids" name="attachids" value=""/>
                                    </c:otherwise>
                                </c:choose>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">備註</td>
                            <td width="90%" class="td_style1" colspan="9">
						    <textarea id="describtion" name="describtion"
                                      class="easyui-validatebox"
                                      oninput="return LessThan(this);"
                                      onchange="return LessThan(this);"
                                      onpropertychange="return LessThan(this);"
                                      maxlength="200"
                                      style="width:85%;height:80px;"
                            <%--data-options="required:true,prompt:'請需求單位詳細說明，如欄位不夠，請附件說明'"--%>
                                      rows="5" cols="6"
                                      data-options="validType:'length[0,200]'">${wfProcessScrapEntity.describtion }</textarea><span
                                    id="txtNum"></span></td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','製程報廢表');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">申請人課級主管&nbsp;<a
                                                                        href="javascript:void(0);"
                                                                        onclick="addRowComm('kchargeno');">添加一位</a></td>
                                                                <%--<td style="border: none;">--%>
                                                                <%--<div class="float_L qhUserIcon"--%>
                                                                <%--onclick="selectRole2(100000,'kchargeTable','kchargeno','kchargename',$('#dealfactoryid').val(),null)"></div>--%>
                                                                <%--</td>--%>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="kchargeno"
                                                               class="easyui-validatebox"
                                                               onblur='getUserNameByEmpnoComm(this);'
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               value="${wfProcessScrapEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfProcessScrapEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">申請人部級主管&nbsp;<a
                                                                        href="javascript:void(0);"
                                                                        onclick="addRowComm('bchargeno');">添加一位</a></td>
                                                                <%--<td style="border: none;">--%>
                                                                <%--<div class="float_L qhUserIcon"--%>
                                                                <%--onclick="selectRole2(100000,'bchargeTable','bchargeno','bchargename',$('#dealfactoryid').val(),null)"></div>--%>
                                                                <%--</td>--%>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="bchargeno"
                                                               class="easyui-validatebox"
                                                               onblur='getUserNameByEmpnoComm(this);'
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               value="${wfProcessScrapEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfProcessScrapEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl1Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">品管負責人/主管&nbsp;<a
                                                                        href="javascript:void(0);"
                                                                        onclick="addRowComm('ylno1');">添加一位</a>
                                                                </td>
                                                                <%--<td style="border: none;">--%>
                                                                <%--<div class="float_L qhUserIcon"--%>
                                                                <%--onclick="selectRole2(100000,'yl1Table','ylno1','ylname1',$('#dealfactoryid').val(),null)"></div>--%>
                                                                <%--</td>--%>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno1" name="ylno1"
                                                               class="easyui-validatebox"
                                                               onblur='getUserNameByEmpnoComm(this);'
                                                               data-options="width:80,required:${requiredMap['ylno1']}"
                                                               value="${wfProcessScrapEntity.ylno1 }"/><c:if
                                                            test="${requiredMap['ylno1'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname1" name="ylname1"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno1']}"
                                                                value="${wfProcessScrapEntity.ylname1 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl3Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">制工負責人/主管&nbsp;<a
                                                                        href="javascript:void(0);"
                                                                        onclick="addRowComm('ylno3');">添加一位</a>
                                                                </td>
                                                                <%--<td style="border: none;">--%>
                                                                <%--<div class="float_L qhUserIcon"--%>
                                                                <%--onclick="selectRole2(100000,'yl3Table','ylno3','ylname3',$('#dealfactoryid').val(),null)"></div>--%>
                                                                <%--</td>--%>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno3" name="ylno3"
                                                               class="easyui-validatebox"
                                                               onblur='getUserNameByEmpnoComm(this);'
                                                               data-options="width:80,required:${requiredMap['ylno3']}"
                                                               value="${wfProcessScrapEntity.ylno3 }"/><c:if
                                                            test="${requiredMap['ylno3'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname3" name="ylname3"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno3']}"
                                                                value="${wfProcessScrapEntity.ylname3 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl5Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">
                                                                    SQE或PQE負責人/主管&nbsp;<a href="javascript:void(0);"
                                                                                          onclick="addRowComm('ylno5');">添加一位</a>
                                                                </td>
                                                                <%--<td style="border: none;">--%>
                                                                <%--<div class="float_L qhUserIcon"--%>
                                                                <%--onclick="selectRole2(100000,'yl5Table','ylno5','ylname5',$('#dealfactoryid').val(),null)"></div>--%>
                                                                <%--</td>--%>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno5" name="ylno5"
                                                               class="easyui-validatebox"
                                                               onblur='getUserNameByEmpnoComm(this);'
                                                               data-options="width:80,required:${requiredMap['ylno5']}"
                                                               value="${wfProcessScrapEntity.ylno5 }"/><c:if
                                                            test="${requiredMap['ylno5'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname5" name="ylname5"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno5']}"
                                                                value="${wfProcessScrapEntity.ylname5 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl7Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">責任單位負責人/主管&nbsp;<a
                                                                        href="javascript:void(0);"
                                                                        onclick="addRowComm('ylno7');">添加一位</a>
                                                                </td>
                                                                <%--<td style="border: none;">--%>
                                                                <%--<div class="float_L qhUserIcon"--%>
                                                                <%--onclick="selectRole2(100000,'yl7Table','ylno7','ylname7',$('#dealfactoryid').val(),null)"></div>--%>
                                                                <%--</td>--%>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno7" name="ylno7"
                                                               class="easyui-validatebox"
                                                               onblur='getUserNameByEmpnoComm(this);'
                                                               data-options="width:80,required:${requiredMap['ylno7']}"
                                                               value="${wfProcessScrapEntity.ylno7 }"/><c:if
                                                            test="${requiredMap['ylno7'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname7" name="ylname7"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno7']}"
                                                                value="${wfProcessScrapEntity.ylname7 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl9Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    廠部或工程成本負責人/主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(107,'yl9Table','ylno9','ylname9',$('input[name=\'applyfactoryid\']').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno9" name="ylno9"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno9']}"
                                                               readonly
                                                               value="${wfProcessScrapEntity.ylno9 }"/><c:if
                                                            test="${requiredMap['ylno9'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname9" name="ylname9"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno9']}"
                                                                value="${wfProcessScrapEntity.ylname9 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl11Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">責任單位廠級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(108,'yl11Table','ylno11','ylname11',$('input[name=\'applyfactoryid\']').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno11" name="ylno11"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno11']}"
                                                               readonly
                                                               value="${wfProcessScrapEntity.ylno11 }"/><c:if
                                                            test="${requiredMap['ylno11'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname11" name="ylname11"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno11']}"
                                                                value="${wfProcessScrapEntity.ylname11 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl10Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    運籌或資源管理部負責人/主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(109,'yl10Table','ylno10','ylname10',$('input[name=\'applyfactoryid\']').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno10" name="ylno10"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno10']}"
                                                               readonly
                                                               value="${wfProcessScrapEntity.ylno10 }"/><c:if
                                                            test="${requiredMap['ylno10'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname10" name="ylname10"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno10']}"
                                                                value="${wfProcessScrapEntity.ylname10 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl8Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽&nbsp;<a
                                                                        href="javascript:void(0);"
                                                                        onclick="addRowComm('ylno8');">添加一位</a></td>
                                                                <%--<td style="border: none;">--%>
                                                                <%--<div class="float_L qhUserIcon"--%>
                                                                <%--onclick="selectRole2(100000,'yl8Table','ylno8','ylname8',$('#dealfactoryid').val(),null)"></div>--%>
                                                                <%--</td>--%>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno8" name="ylno8"
                                                               class="easyui-validatebox"
                                                               onblur="getUserNameByEmpnoComm(this)"
                                                               data-options="width:80,required:${requiredMap['ylno8']}"
                                                               value="${wfProcessScrapEntity.ylno8 }"/><c:if
                                                            test="${requiredMap['ylno8'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname8" name="ylname8"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno8']}"
                                                                value="${wfProcessScrapEntity.ylname8 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl12Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">責任單位製造處級主管&nbsp;<a
                                                                        href="javascript:void(0);"
                                                                        onclick="addRowComm('ylno12');">添加一位</a>
                                                                </td>
                                                                <%--<td style="border: none;">--%>
                                                                <%--<div class="float_L qhUserIcon"--%>
                                                                <%--onclick="selectRole2(100000,'yl12Table','ylno12','ylname12',$('#dealfactoryid').val(),null)"></div>--%>
                                                                <%--</td>--%>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno12" name="ylno12"
                                                               class="easyui-validatebox"
                                                               onblur="getUserNameByEmpnoComm(this)"
                                                               data-options="width:80,required:${requiredMap['ylno12']}"
                                                               value="${wfProcessScrapEntity.ylno12 }"/><c:if
                                                            test="${requiredMap['ylno12'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname12" name="ylname12"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno12']}"
                                                                value="${wfProcessScrapEntity.ylname12 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl13Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">生管負責人</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(99,'yl13Table','ylno13','ylname13',$('input[name=\'applyfactoryid\']').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno13" name="ylno13"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno13']}"
                                                               readonly
                                                               value="${wfProcessScrapEntity.ylno13 }"/><c:if
                                                            test="${requiredMap['ylno13'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname13" name="ylname13"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno13']}"
                                                                value="${wfProcessScrapEntity.ylname13 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl15Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">PQA負責人</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(100,'yl15Table','ylno15','ylname15',$('input[name=\'applyfactoryid\']').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno15" name="ylno15"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno15']}"
                                                               readonly
                                                               value="${wfProcessScrapEntity.ylno15 }"/><c:if
                                                            test="${requiredMap['ylno15'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname15" name="ylname15"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno15']}"
                                                                value="${wfProcessScrapEntity.ylname15 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl16Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">PQA主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(111,'yl16Table','ylno16','ylname16',$('input[name=\'applyfactoryid\']').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno16" name="ylno16"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno16']}"
                                                               readonly
                                                               value="${wfProcessScrapEntity.ylno16 }"/><c:if
                                                            test="${requiredMap['ylno16'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname16" name="ylname16"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno16']}"
                                                                value="${wfProcessScrapEntity.ylname16 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl14Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">經管負責人</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(101,'yl14Table','ylno14','ylname14',$('input[name=\'applyfactoryid\']').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno14" name="ylno14"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno14']}"
                                                               readonly
                                                               value="${wfProcessScrapEntity.ylno14 }"/><c:if
                                                            test="${requiredMap['ylno14'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname14" name="ylname14"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno14']}"
                                                                value="${wfProcessScrapEntity.ylname14 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl17Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠部FF倉負責人
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(112,'yl17Table','ylno17','ylname17',$('input[name=\'applyfactoryid\']').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno17" name="ylno17"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno17']}"
                                                               readonly
                                                               value="${wfProcessScrapEntity.ylno17 }"/><c:if
                                                            test="${requiredMap['ylno17'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname17" name="ylname17"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno17']}"
                                                                value="${wfProcessScrapEntity.ylname17 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl18Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">中央FF倉負責人
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(102,'yl18Table','ylno18','ylname18',$('input[name=\'applyfactoryid\']').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno18" name="ylno18"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno18']}"
                                                               readonly
                                                               value="${wfProcessScrapEntity.ylno18 }"/><c:if
                                                            test="${requiredMap['ylno18'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname18" name="ylname18"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno18']}"
                                                                value="${wfProcessScrapEntity.ylname18 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfProcessScrapEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;"
                                   onclick="canelTask('${wfProcessScrapEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
</div>
<script src='${ctx}/static/js/shengchan/wfprocessscrap.js?random=<%= Math.random()%>'></script>
</body>
</html>
