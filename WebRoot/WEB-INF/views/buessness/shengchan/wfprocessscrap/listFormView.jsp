<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>製程報廢表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfprocessscrap/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfProcessScrapEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfProcessScrapEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">製程報廢單一</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfProcessScrapEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfProcessScrapEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfProcessScrapEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfProcessScrapEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfProcessScrapEntity.makerno}/${wfProcessScrapEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號</td>
                            <td width="6%" class="td_style1" colspan="2">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 150,required:true,disabled:true"
                                       value="${wfProcessScrapEntity.applyno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="4%">申請人姓名</td>
                            <td width="6%" class="td_style1" colspan="2">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wfProcessScrapEntity.applyname }"/>
                            </td>
                            <td width="4%">所在廠區</td>
                            <td width="7%" class="td_style1" colspan="3">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="400" value="${wfProcessScrapEntity.applyfactoryid }"
                                       data-options="width: 120,disabled:true,required:true,validType:'comboxValidate[\'applyfactoryid\',\'请选择所在廠區\']',onChange:function(){auditTableReset('auditTable');}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1" colspan="2">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfProcessScrapEntity.applydeptno }"/>
                            </td>
                            <td>申請單位</td>
                            <td colspan="6" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox"
                                       data-options="width: 600,required:true,disabled:true"
                                       value="${wfProcessScrapEntity.applydeptname }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="4%">聯繫方式</td>
                            <td width="6%" class="td_style1" colspan="2">
                                <input id="applyphone" name="applyphone"
                                       class="easyui-validatebox" data-options="width: 150,required:true,disabled:true"
                                       value="${wfProcessScrapEntity.applyphone }"/>
                            </td>
                            <td>申請人郵件</td>
                            <td colspan="6" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       data-options="width: 600,required:true,disabled:true"
                                       value="${wfProcessScrapEntity.applyemail }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">報廢信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">料號</td>
                            <td width="6%" class="td_style1" colspan="2">
                                <input id="materialcode" name="materialcode" class="easyui-validatebox"
                                       data-options="width: 150,required:true,disabled:true"
                                       value="${wfProcessScrapEntity.materialcode }"/>
                            </td>
                            <td width="4%">機種</td>
                            <td width="6%" class="td_style1" colspan="2">
                                <input id="machinetype" name="machinetype"
                                       class="easyui-validatebox"
                                       data-options="width:150,required:true,disabled:true"
                                       value="${wfProcessScrapEntity.machinetype }"/>
                            </td>
                            <td width="4%">品名</td>
                            <td width="7%" class="td_style1" colspan="3">
                                <input id="charactername" name="charactername" class="easyui-validatebox"
                                       data-options="width: 150,required:true,disabled:true"
                                       value="${wfProcessScrapEntity.charactername }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="4%">物料形態</td>
                            <td width="6%" class="td_style1" colspan="2">
                                <%--<input id="materialform" name="materialform" class="easyui-validatebox"--%>
                                <%--data-options="width: 150,required:true,disabled:true"--%>
                                <%--value="${wfProcessScrapEntity.materialform }"/>--%>
                                <input id="materialform"
                                       name="materialform"
                                       class="easyui-combobox"
                                       data-options="width: 150,panelHeight:400,disabled:true,valueField:'value',textField:'label',required:true,validType:'comboxValidate[\'materialform\',\'請選擇物料形態\']',
																              editable:false,onBeforeLoad:function(){
																              loadMaterialform();}"
                                       style="width:80px;" value="${wfProcessScrapEntity.materialform }"/>
                            </td>
                            <td width="4%">顏色</td>
                            <td width="6%" class="td_style1" colspan="2">
                                <input id="color" name="color"
                                       class="easyui-validatebox"
                                       data-options="width:150,required:true,disabled:true"
                                       value="${wfProcessScrapEntity.color }"/>
                            </td>
                            <td width="4%">報廢類別</td>
                            <td width="7%" class="td_style1" colspan="3">
                                <%--<input id="scraptype" name="scraptype" class="easyui-validatebox"--%>
                                <%--data-options="width: 150,required:true,disabled:true"--%>
                                <%--value="${wfProcessScrapEntity.scraptype }"/>--%>
                                <input id="scraptype"
                                       name="scraptype"
                                       class="easyui-combobox"
                                       data-options="width: 150,panelHeight:400,disabled:true,valueField:'value',textField:'label',required:true,validType:'comboxValidate[\'scraptype\',\'請選擇報廢類別\']',
																              editable:false,onBeforeLoad:function(){
																              loadScraptype();}"
                                       style="width:80px;" value="${wfProcessScrapEntity.scraptype }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="116px;" colspan="2">項目</td>
                            <td width="4%" colspan="2">廠級代碼</td>
                            <td width="4%" colspan="2">廠別</td>
                            <td width="4%" colspan="2">製程</td>
                            <td width="4%" colspan="2">樓層</td>
                        </tr>
                        <tr align="center">
                            <td width="4%" colspan="2">發生製程</td>
                            <td width="6%" colspan="2">
                                <input id="factorycode" name="factorycode" class="easyui-validatebox"
                                       data-options="width: 150,required:true,disabled:true"
                                       value="${wfProcessScrapEntity.factorycode }"/>
                            </td>
                            <td width="6%" colspan="2">
                                <input id="factorytype" name="factorytype" class="easyui-validatebox"
                                       data-options="width: 220,required:true,disabled:true"
                                       value="${wfProcessScrapEntity.factorytype }"/>
                            </td>
                            <td width="6%" colspan="2">
                                <input id="process" name="process" class="easyui-validatebox"
                                       data-options="width: 150,required:true,disabled:true"
                                       value="${wfProcessScrapEntity.process }"/>
                            </td>
                            <td width="6%" colspan="2">
                                <input id="floor" name="floor" class="easyui-validatebox"
                                       data-options="width: 120,required:true,disabled:true"
                                       value="${wfProcessScrapEntity.floor }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="4%" colspan="2">責任製程</td>
                            <td width="6%" colspan="2">
                                <input id="factorycode1" name="factorycode1" class="easyui-validatebox"
                                       data-options="width: 150,required:true,disabled:true"
                                       value="${wfProcessScrapEntity.factorycode1 }"/>
                            </td>
                            <td width="6%" colspan="2">
                                <input id="factorytype1" name="factorytype1" class="easyui-validatebox"
                                       data-options="width: 220,required:true,disabled:true"
                                       value="${wfProcessScrapEntity.factorytype1 }"/>
                            </td>
                            <td width="6%" colspan="2">
                                <input id="process1" name="process1" class="easyui-validatebox"
                                       data-options="width: 150,required:true,disabled:true"
                                       value="${wfProcessScrapEntity.process1 }"/>
                            </td>
                            <td width="6%" colspan="2">
                                <input id="floor1" name="floor1" class="easyui-validatebox"
                                       data-options="width: 120,required:true,disabled:true"
                                       value="${wfProcessScrapEntity.floor1 }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">報廢明細</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;">
                                    <table id="bondedgoodsItemTable" width="100%">
                                        <tr align="center">
                                            <td width="1%">序號</td>
                                            <td width="4%">不良區域</td>
                                            <td width="4%" colspan="2">缺陷描述</td>
                                            <td width="4%">報廢數量(單位:pcs/Kg)</td>
                                            <td width="4%">操作</td>
                                        </tr>
                                        <tbody id="info_Body">
                                        <c:if test="${wfProcessScrapEntity.itemsEntity!=null&&wfProcessScrapEntity.itemsEntity.size()>0}">
                                            <c:forEach items="${wfProcessScrapEntity.itemsEntity}" var="itemsEntity"
                                                       varStatus="status">
                                                <tr align="center" id="bondedgoodsItem${status.index+1}">
                                                    <td width="4%">${status.index+1}</td>
                                                    <td width="6%">
                                                        <input id="badarea${status.index}"
                                                               name="itemsEntity[${status.index}].badarea"
                                                               class="easyui-combobox"
                                                               data-options="width: 150,panelHeight:400,valueField:'value',textField:'label',required:true,validType:'comboxValidate[\'badarea${status.index}\',\'请选择不良區域\']',onBeforeLoad:function(){
																              editable:true,defectTypeList(${status.index});},disabled:true"
                                                               value="${itemsEntity.badarea }"/>
                                                    </td>
                                                    <td width="6%" colspan="2">
                                                        <input id="bugdescribe${status.index}"
                                                               name="itemsEntity[${status.index}].bugdescribe"
                                                               class="easyui-combobox"
                                                               data-options="width: 150,panelHeight:400,valueField:'value',textField:'label',required:true,validType:'comboxValidate[\'bugdescribe${status.index}\',\'缺陷描述\']',onBeforeLoad:function(){
																              editable:true,descriptionList(${status.index});},disabled:true"
                                                               value="${itemsEntity.bugdescribe }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input id="scrapnum${status.index}"
                                                               name="itemsEntity[${status.index}].scrapnum"
                                                               class="easyui-numberbox"
                                                               data-options="width: 150,required:true,disabled:true,precision:2,groupSeparator:','"
                                                               value="${itemsEntity.scrapnum }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               class="deleteBtnStr" disabled
                                                               onclick="bondedgooddeltr(${status.index+1});return false;"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr">
                                            <td colspan="6" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="bondedgoodItemAdd"
                                                       style="width:100px;float:left;" value="添加一筆" disabled/>
                                                <label id="totalNum111"
                                                       style="float: right;padding-right:100px;color: red;font-size: 20px;font-weight: bold">報廢總數:&nbsp;<label
                                                        id="totalNum">${wfProcessScrapEntity.scraptotalnum }</label></label>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%" colspan="2">附件</td>
                            <td width="90%" class="td_style1" colspan="8">
                                <input type="hidden" id="attachids" name="attachids" value="${wfProcessScrapEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%" colspan="2">備註</td>
                            <td width="90%" class="td_style1" colspan="8">
						    <textarea id="describtion" name="describtion"
                                      class="easyui-validatebox"
                                      oninput="return LessThan(this);"
                                      onchange="return LessThan(this);"
                                      onpropertychange="return LessThan(this);"
                                      maxlength="200"
                                      style="width:85%;height:80px;"
                            <%--data-options="required:true,prompt:'請需求單位詳細說明，如欄位不夠，請附件說明'"--%>
                                      rows="5" cols="6"
                                      data-options="validType:'length[0,200]',disabled:true">${wfProcessScrapEntity.describtion }</textarea><span
                                    id="txtNum"></span></td>
                        </tr>
                        <tr>
                            <td width="4%">費用代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applycostno" name="applycostno" class="easyui-validatebox"
                                       data-options="width: 150,disabled:true"
                                       value="${wfProcessScrapEntity.applycostno }"/>
                            </td>
                            <td width="4%">表單編號</td>
                            <td width="6%" class="td_style1" colspan="2">
                                <input id="formserialno" name="formserialno"
                                       class="easyui-validatebox"
                                       data-options="width:150,disabled:true"
                                       value="${wfProcessScrapEntity.formserialno }"/>
                            </td>
                            <td width="4%">入庫單號</td>
                            <td width="7%" class="td_style1" colspan="2">
                                <input id="warehousenum1" name="warehousenum" class="easyui-validatebox"
                                       data-options="width: 150,disabled:true"
                                       value="${wfProcessScrapEntity.warehousenum }"/>
                            </td>
                            <td width="4%">轉撥單號</td>
                            <td width="7%" class="td_style1">
                                <input id="transfernum1" name="transfernum" class="easyui-validatebox"
                                       data-options="width: 150,disabled:true"
                                       value="${wfProcessScrapEntity.transfernum }"/>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','製程報廢表');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfProcessScrapEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfProcessScrapEntity.workstatus!=null&&wfProcessScrapEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<div id="dlg"></div>
<script src='${ctx}/static/js/shengchan/wfprocessscrap.js?random=<%= Math.random()%>'></script>
</body>
</html>
