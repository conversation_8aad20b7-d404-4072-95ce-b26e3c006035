<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>品質嫌疑品物料鎖料需求申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input type="text" name="filter_EQS_dealno" class="easyui-validatebox"
               data-options="width:150,prompt: '申请人工號'"/>
        <input type="text" name="filter_EQS_dealdeptno" class="easyui-validatebox"
               data-options="width:150,prompt: '申請人單位代碼'"/>
        <input type="text" name="filter_EQS_serialno" class="easyui-validatebox"
               data-options="width:150,prompt: '任務編碼'"/>
        <input id="qysjzt"  style="width:100px" class="easyui-validatebox" name="filter_EQS_workstatus"/>
        <input id="changqu" style="width:100px" class="easyui-validatebox" name="filter_EQS_dealchoosefactoryid"/>
        <input id="jizhong" style="width:100px" class="easyui-validatebox" name="filter_EQS_machinetype"/>
        <%--<input id="zhicheng" style="width:100px" class="easyui-validatebox" name="filter_EQS_processprocedure"/>--%>
        <input id="wuliaoxingtai" style="width:100px" class="easyui-validatebox" name="filter_EQS_materialform"/>
        <input id="pinzhileibie" style="width:100px" class="easyui-validatebox" name="filter_EQS_qualitycategory"/>
        <input type="text" name="filter_GED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd"
               data-options="width:150,prompt: '填單开始日期'"/>
        - <input type="text" name="filter_LED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd"
                 data-options="width:150,prompt: '填單结束日期'"/>
        <input type="text" name="filter_GED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"
               data-options="width:150,prompt: '簽核完成开始日期'"/>
        - <input type="text" name="filter_LED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"
                 data-options="width:150,prompt: '簽核完成结束日期'"/>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
           onclick="listSearchReset()">重置</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
           onclick="exportExcel()">导出Excel</a>
        <!--導出用，請勿刪除-->
        <input id="page" name="page" type="hidden" value="1"/>
        <input id="rows" name="rows" type="hidden" value="30"/>
    </form>

</div>
<table id="dg"></table>
<div id="dlg"></div>

<script type="text/javascript">
    //創建下拉查詢條件
    //獲取廠區
    $.get(ctx + '/tqhfactoryidconfig/allFactorys/', function(result) {
        $("#changqu").combobox({
            data : result,
            valueField : "factoryid",
            textField : "factoryname",
            editable : false,
            panelHeight : 350,
            loadFilter : function(data) {
                data.unshift({
                    factoryid : '',
                    factoryname : '請選擇廠區'
                });
                return data;
            }
        });
    },"json");
    $.ajax({
        url: ctx + "/system/dict/getDictByType/audit_status",
        dataType: "json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#qysjzt").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇任務狀態'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
    $.ajax({
        url: ctx + "/system/dict/getDictByType/dic_mahinetype",
        dataType: "json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#jizhong").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇廠區'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
    /*$.ajax({
        url: ctx + "/system/dict/getDictByType/dic_processprocedure",
        dataType: "json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#zhicheng").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇製程'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });*/
    $.ajax({
        url: ctx + "/system/dict/getDictByType/dic_materialform",
        dataType: "json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#wuliaoxingtai").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇物料形態'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
    $.ajax({
        url: ctx + "/system/dict/getDictByType/dic_qualitycategory",
        dataType: "json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#pinzhileibie").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇品質類別'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
    var dg;
    $(function () {
        dg = $('#dg').datagrid({
            method: "get",
            url: ctx + '/tqhmateriallock/list',
            fit: true,
            fitColumns: true,
            //fitColumns: false,  //如需要底部出現滾動條，修改爲false
            border: false,
            idField: 'attachids',
            striped: true,
            cache: false,  //关闭AJAX相应的缓存
            pagination: true,
            rownumbers: true,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: true,
            columns: [[
                //{ field: 'id', title: '主鍵',sortable:true,width:100},
                {field: 'serialno', title: '任務编码', sortable: true, width: 150, formatter: operation},
                {field: 'dealno', title: '申請人工號', sortable: true, width: 100},
                {field: 'dealname', title: '申請人姓名',sortable:true,width:100},
                {field: 'dealdeptno', title: '單位代碼', sortable: true, width: 100},
                {field: 'dealdeptname', title: '部門名稱', sortable: true, width: 100, formatter: cellTextTip},
                {field: 'dealchoosefactoryid', title: '所在廠區',sortable:true,width:100},
                {field: 'machinetype', title: '機種',sortable:true,width:100},
                {field: 'color', title: '顏色',sortable:true,width:100},
                {field: 'stage', title: '階段',sortable:true,width:100},
                {field: 'processprocedure', title: '製程',sortable:true,width:100},
                {field: 'floor', title: '樓層',sortable:true,width:100},
                {field: 'materialform', title: '物料形態',sortable:true,width:100},
                {field: 'qualitycategory', title: '品質類別',sortable:true,width:100},
                {field: 'locknum', title: '鎖定數量',sortable:true,width:100},
                {field: 'createtime', title: '填單日期', sortable: true, width: 100},
                {field: 'workstatus', title: '任务狀態', sortable: true, width: 100},
                {field: 'nodeName', title: '當前審核節點', sortable: true, width: 150, formatter: formatProgress},
                {field: 'auditUser', title: '當前簽核人', sortable: true, width: 120},
                {field: 'complettime', title: '簽核完成時間', sortable: true, width: 100}
            ]],
            onLoadSuccess: function () {
                $(".easyui-tooltip").tooltip({
                    onShow: function () {
                        $(this).tooltip('tip').css({
                            borderColor: '#000'
                        });
                    }
                });
                //$(this).datagrid("fixRownumber");
            },
            rowStyler: rowStylerFun,
            enableHeaderClickMenu: true,
            enableHeaderContextMenu: true,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });
    });

    //任務編號查看頁面
    function operation(value, row, index) {
        return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('詳情',ctx+'/tqhmateriallock/view/"
            + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
    };

    //创建查询对象并查询
    function cx() {
        var obj = $("#searchFrom").serializeObject();
        dg.datagrid('load', obj);
    }

    //表單進度查詢頁面查詢條件重置
    function listSearchReset() {
        $("#searchFrom").form("reset");
    }

    //导出excel
    function exportExcel() {
        var options = $('#dg').datagrid('getPager').data("pagination").options;
        var page = options.pageNumber;//当前页数  
        var total = options.total;
        var rows = options.pageSize;//每页的记录数（行数）  
        var form = document.getElementById("searchFrom");
        $('#page').val(page);
        $('#rows').val(total);
        var form = document.getElementById("searchFrom");
        searchFrom.action = ctx + '/tqhmateriallock/exportExcel';
        form.submit();
    }
</script>
</body>
</html>