<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>品質嫌疑品物料鎖料需求申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/tqhmateriallock/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhMaterialLockEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${tQhMaterialLockEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">品質嫌疑品物料鎖料需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${tQhMaterialLockEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${tQhMaterialLockEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${tQhMaterialLockEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${tQhMaterialLockEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${tQhMaterialLockEntity.makerno}/${tQhMaterialLockEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td>申請人工號</td>
                            <td align="left">
                                ${tQhMaterialLockEntity.dealno}
                            </td>
                            <td>申請人</td>
                            <td align="left">${tQhMaterialLockEntity.dealname }
                            </td>
                            <td>單位代碼</td>
                            <td align="left">
                                ${tQhMaterialLockEntity.dealdeptno }
                            </td>
                            <td>聯繫方式</td>
                            <td align="left">
                                ${tQhMaterialLockEntity.dealphone }
                            </td>
                            <td>所在廠區</td>
                            <td class="td_style1">
                                <input id="dealchoosefactoryid" name="dealchoosefactoryid" disabled
                                       class="easyui-combobox" data-options="width:100,required:true"
                                       value="${tQhMaterialLockEntity.dealchoosefactoryid }"/>
                                <input id="dealfactoryid" name="dealfactoryid"
                                       value="${tQhMaterialLockEntity.dealfactoryid}" type="hidden"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱</td>
                            <td align="left" colspan="5">${tQhMaterialLockEntity.dealdeptname }
                            </td>
                            <td>聯繫郵箱</td>
                            <td align="left" colspan="3">
                                ${tQhMaterialLockEntity.dealmail}
                            </td>
                        </tr>
                    </table>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="9%">機種</td>
                            <td width="10%" align="left"><input id="machinetype" name="machinetype" class="easyui-combobox"
                                                         disabled
                                                         data-options="width:100"
                                                         value="${tQhMaterialLockEntity.machinetype}"/>
                            </td>
                            <td width="12%">顏色</td>
                            <td class="td_style1"><input id="color" name="color" class="easyui-combobox" disabled
                                                         data-options="width:150,prompt:'選擇顏色,可多選',required:true,validType:'comboxValidate[\'machinetype\',\'请選擇顏色\']'"
                                                         value="${tQhMaterialLockEntity.color}"/>
                            </td>
                            <td width="10%">階段</td>
                            <td class="td_style1"><input id="stage" name="stage" class="easyui-combobox" disabled
                                                         data-options="width:150,prompt:'選擇階段,可多選',required:true,validType:'comboxValidate[\'machinetype\',\'请選擇階段\']'"
                                                         value="${tQhMaterialLockEntity.stage}"/>
                            </td>
                            <td width="10%">製程</td>
                            <td width="12%" align="left">
                               <%-- <input id="processprocedure" name="processprocedure" disabled class="easyui-combobox"
                                       data-options="width:150" value="${tQhMaterialLockEntity.processprocedure}"/>--%>
                               ${tQhMaterialLockEntity.processprocedure}
                            </td>
                            <td width="10%">樓層</td>
                            <td width="11%" align="left"><input id="floor" name="floor" readonly data-options="width:150" class="easyui-validatebox inputCss" value="${tQhMaterialLockEntity.floor}"/></td>
                        </tr>
                        <tr align="center">
                            <td>物料形態</td>
                            <td  align="left"><input id="materialform" name="materialform" class="easyui-combobox"
                                                         disabled
                                                         data-options="width:100,required:true,validType:'comboxValidate[\'materialform\',\'请選擇物料形態\']'"
                                                         value="${tQhMaterialLockEntity.materialform}"/>
                            </td>
                            <td>品質類別</td>
                            <td colspan="3"  align="left"><input id="qualitycategory"
                                                                     name="qualitycategory"
                                                                     class="easyui-combobox" disabled
                                                                     data-options="width:280"
                                                                     value="${tQhMaterialLockEntity.qualitycategory}"/>
                            </td>
                            <td>鎖定數量</td>
                            <td colspan="3" align="left">${tQhMaterialLockEntity.locknum}</td>
                        </tr>
                        <tr align="center">
                            <td>物料類型</td>
                            <td colspan="9" align="left">
                                <div class="materialTypeDiv"></div>
                                <input id="materialType" name="materialType" disabled
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${tQhMaterialLockEntity.materialType }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>鎖料提示</td>
                            <td  align="left" colspan="9"><textarea style="width: 1000px" rows="5" cols="6"
                                                                        id="lockprompt" name="lockprompt"
                                                                        class="easyui-validatebox" readonly
                                                                        data-options="required:true">${tQhMaterialLockEntity.lockprompt}</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>原因說明</td>
                            <td align="left" colspan="9"><textarea style="width: 1000px" rows="5" cols="6"
                                                                        id="reason" name="reason" readonly
                                                                        class="easyui-validatebox">${tQhMaterialLockEntity.reason}</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>預處理方式</td>
                            <td colspan="5" class="td_style1">
                                <textarea oninput="return LessThanAuto(this,'txtNum2');"
                                          onchange="return LessThanAuto(this,'txtNum2');" readonly
                                          onpropertychange="return LessThanAuto(this,'txtNum2');"
                                          class="easyui-validatebox" maxlength="500" rows="5" name="preprocessingMethod"
                                          data-options="width:600,required:true">${tQhMaterialLockEntity.preprocessingMethod}</textarea><span id="txtNum2"></span>
                            </td>
                            <td>預處理日期</td>
                            <td colspan="3" class="td_style1">
                                <input name="preprocessingDate" class="Wdate" data-options="required:true"
                                       style="width:100px" disabled
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${tQhMaterialLockEntity.preprocessingDate}"/>"
                                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})">
                            </td>
                        </tr>
                        <tr align="center">
                            <td>作業系統/鎖定節點</td>
                            <td class="td_style2" colspan="9" align="left">
                                <div class="operatesystemDiv"></div>
                                <input id="operatesystem" name="operatesystem"
                                       type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${tQhMaterialLockEntity.operatesystem }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td colspan="9" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${tQhMaterialLockEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                    </table>
                    <c:choose>
                        <c:when test="${not empty nodeName&&'QS查詢結果' eq nodeName}">
                            <table class="formList">
                                <tr>
                                    <td colspan="10" class="td_style1">QS查询结果</td>
                                </tr>
                                <tr align="center">
                                    <td>系統鎖料數量</td>
                                    <td class="td_style1"><input id="systemlocknum" name="systemlocknum"
                                                                 class="easyui-validatebox"
                                                                 value="${tQhMaterialLockEntity.systemlocknum}"/></td>
                                    <td>關聯標簽/箱數</td>
                                    <td class="td_style1"><input id="relativenum" name="relativenum"
                                                                 class="easyui-validatebox"
                                                                 value="${tQhMaterialLockEntity.relativenum}"/></td>
                                    <td>倉庫影響總數</td>
                                    <td class="td_style1"><input id="hubeffecttotal" name="hubeffecttotal"
                                                                 class="easyui-validatebox"
                                                                 value="${tQhMaterialLockEntity.hubeffecttotal}"/></td>
                                </tr>
                            </table>
                        </c:when>
                        <c:when test="${not empty nodeName&&('會簽' eq nodeName ||'工程' eq nodeName ||'PQE' eq nodeName ||'OQC/QC' eq nodeName ||'CS課部級主管' eq nodeName ||'企劃生管' eq nodeName ||'企劃倉儲' eq nodeName ||'QS鎖料' eq nodeName)}">
                            <table class="formList">
                                <tr>
                                    <td colspan="10" class="td_style1">QS查询结果</td>
                                </tr>
                                <tr align="center">
                                    <td width="15%">系統鎖料數量</td>
                                    <td align="left">${tQhMaterialLockEntity.systemlocknum}</td>
                                    <td width="15%">關聯標簽/箱數</td>
                                    <td align="left">${tQhMaterialLockEntity.relativenum}</td>
                                    <td width="15%">倉庫影響總數</td>
                                    <td align="left">${tQhMaterialLockEntity.hubeffecttotal}</td>
                                </tr>
                            </table>
                        </c:when>
                    </c:choose>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%">
						<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                        <c:choose>
                            <c:when test="${not empty nodeName&&'QS查詢結果' eq nodeName}">
                                <tr align="center">
                                    <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" perCall="updateQs"
                                                    serialNo="${tQhMaterialLockEntity.serialno}"></fox:action>
                                    </td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <tr align="center">
                                    <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                                    serialNo="${tQhMaterialLockEntity.serialno}"></fox:action>
                                    </td>
                                </tr>
                            </c:otherwise>
                        </c:choose>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','品質嫌疑品物料鎖料需求申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhMaterialLockEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<input type="hidden" id="otherreason" value="${tQhMaterialLockEntity.otherreason}"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/shengchan/tqhmateriallock.js?random=<%= Math.random()%>'></script>
</body>
</html>