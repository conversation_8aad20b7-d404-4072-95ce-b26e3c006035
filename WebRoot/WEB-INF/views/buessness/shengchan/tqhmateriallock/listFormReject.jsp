<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>品質嫌疑品物料鎖料需求申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/tqhmateriallock/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhMaterialLockEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${tQhMaterialLockEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${tQhMaterialLockEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${tQhMaterialLockEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${tQhMaterialLockEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${tQhMaterialLockEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">品質嫌疑品物料鎖料需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${tQhMaterialLockEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${tQhMaterialLockEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${tQhMaterialLockEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${tQhMaterialLockEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty tQhMaterialLockEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty tQhMaterialLockEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${tQhMaterialLockEntity.makerno}/${tQhMaterialLockEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td>申請人工號&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealno" name="dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${tQhMaterialLockEntity.dealno}"
                                       onchange="queryUserInfo();"/>
                            </td>
                            <td>申請人</td>
                            <td class="td_style1">
                                <input id="dealname" name="dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${tQhMaterialLockEntity.dealname }"/>
                            </td>
                            <td>單位代碼</td>
                            <td class="td_style1">
                                <input id="dealdeptno" name="dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${tQhMaterialLockEntity.dealdeptno }" readonly/>
                            </td>
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealphone" name="dealphone"
                                       class="easyui-validatebox" data-options="width:100,required:true"
                                       value="${tQhMaterialLockEntity.dealphone }"/>
                            </td>
                            <td>所在廠區&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealchoosefactoryid" name="dealchoosefactoryid"
                                       class="easyui-combobox" data-options="width:100,required:true"
                                       value="${tQhMaterialLockEntity.dealchoosefactoryid }"/>
                                <input id="dealfactoryid" name="dealfactoryid"
                                       value="${tQhMaterialLockEntity.dealfactoryid}" type="hidden"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="dealdeptname" name="dealdeptname"
                                       class="easyui-validatebox" data-options="width:400,required:true"
                                       value="${tQhMaterialLockEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealmail" name="dealmail"
                                       class="easyui-validatebox"data-options="width: 250,required:true,validType:'email[\'dealmail\',\'郵箱的格式不正確\']'"
                                       value="${tQhMaterialLockEntity.dealmail }"/>
                            </td>
                        </tr>
                    </table>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="12%">機種&nbsp;<font color="red">*</font></td>
                            <td class="td_style1"><input id="machinetype" name="machinetype" class="easyui-combobox"
                                                         data-options="width:150,required:true,validType:'comboxValidate[\'machinetype\',\'请選擇機種\']'" value="${tQhMaterialLockEntity.machinetype}"/>
                            </td>
                            <td width="12%">顏色&nbsp;<font color="red">*</font></td>
                            <td class="td_style1"><input id="color" name="color" class="easyui-combobox"
                                                         data-options="width:150,prompt:'選擇顏色,可多選',required:true,validType:'comboxValidate[\'machinetype\',\'请選擇顏色\']'"
                                                         value="${tQhMaterialLockEntity.color}"/>
                            </td>
                            <td width="10%">階段&nbsp;<font color="red">*</font></td>
                            <td class="td_style1"><input id="stage" name="stage" class="easyui-combobox"
                                                         data-options="width:150,prompt:'選擇階段,可多選',required:true,validType:'comboxValidate[\'machinetype\',\'请選擇階段\']'"
                                                         value="${tQhMaterialLockEntity.stage}"/>
                            <td width="10%">製程&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <%--<input id="processprocedure" name="processprocedure" class="easyui-combobox"
                                       data-options="width:150,required:true,validType:'comboxValidate[\'processprocedure\',\'请選擇製程\']'" value="${tQhMaterialLockEntity.processprocedure}"/>--%>
                                <input id="processprocedure" name="processprocedure" class="easyui-validatebox"
                                       data-options="width:150,required:true" value="${tQhMaterialLockEntity.processprocedure}"/>
                            </td>
                            <td width="10%">樓層&nbsp;<font color="red">*</font></td>
                            <td class="td_style1"><input id="floor" name="floor" class="easyui-validatebox"  data-options="width:150,required:true"
                                                         value="${tQhMaterialLockEntity.floor}"/></td>
                        </tr>
                        <tr align="center">
                            <td>物料形態&nbsp;<font color="red">*</font></td>
                            <td class="td_style1"><input id="materialform" name="materialform" class="easyui-combobox"
                                                         data-options="width:150,required:true,validType:'comboxValidate[\'materialform\',\'请選擇物料形態\']'" value="${tQhMaterialLockEntity.materialform}"/>
                            </td>
                            <td>品質類別&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1"><input id="qualitycategory" name="qualitycategory" class="easyui-combobox"
                                                                     data-options="width:280,required:true,validType:'comboxValidate[\'qualitycategory\',\'请選擇品質類別\']',onChange:function(){queryXing();}" value="${tQhMaterialLockEntity.qualitycategory}"/>
                            </td>
                            <td>鎖定數量</td>
                            <td colspan="3" class="td_style1"><input id="locknum" name="locknum" class="easyui-validatebox"  data-options="width:150"
                                                                     value="${tQhMaterialLockEntity.locknum}"/></td>
                        </tr>
                        <tr align="center">
                            <td>物料類型&nbsp;<font color="red">*</font></td>
                            <td colspan="9" align="left">
                                <div class="materialTypeDiv"></div>
                                <input id="materialType" name="materialType"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${tQhMaterialLockEntity.materialType }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>鎖料提示&nbsp;<font color="red">*</font></td>
                            <td class="td_style1" colspan="9"><textarea style="width: 1000px" rows="5" cols="6" id="lockprompt" name="lockprompt"
                                                                        oninput="return LessThanAuto(this,'txtNum');"
                                                                        onchange="return LessThanAuto(this,'txtNum');"
                                                                        onpropertychange="return LessThanAuto(this,'txtNum');"
                                                                        maxlength="100" class="easyui-validatebox" data-options="required:true">${tQhMaterialLockEntity.lockprompt}</textarea><span id="txtNum"></span></td>
                        </tr>
                        <tr align="center">
                            <td>原因說明</td>
                            <td class="td_style1" colspan="9"><textarea style="width: 1000px" rows="7" cols="6" id="reason" name="reason"
                                                                        oninput="return LessThanAuto(this,'txtNum1');"
                                                                        onchange="return LessThanAuto(this,'txtNum1');"
                                                                        onpropertychange="return LessThanAuto(this,'txtNum1');" maxlength="300" class="easyui-validatebox">${tQhMaterialLockEntity.reason}</textarea><span id="txtNum1"></span></td>
                        </tr>
                        <tr align="center">
                            <td>
                                預處理方式&nbsp;<font color="red">*</font>
                            </td>
                            <td colspan="5" class="td_style1">
                                <textarea oninput="return LessThanAuto(this,'txtNum2');"
                                          onchange="return LessThanAuto(this,'txtNum2');"
                                          onpropertychange="return LessThanAuto(this,'txtNum2');"
                                          class="easyui-validatebox" maxlength="500" rows="5" name="preprocessingMethod"
                                          data-options="width:600,required:true">${tQhMaterialLockEntity.preprocessingMethod}</textarea><span id="txtNum2"></span>
                            </td>
                            <td>
                                預處理日期&nbsp;<font color="red">*</font>
                            </td>
                            <td colspan="3" class="td_style1">
                                <input name="preprocessingDate" class="Wdate" data-options="required:true"
                                       style="width:100px"
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${tQhMaterialLockEntity.preprocessingDate}"/>"
                                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})">
                            </td>
                        </tr>
                        <tr align="center">
                            <td>作業系統/鎖定節點&nbsp;<font color="red">*</font></td>
                            <td class="td_style2" colspan="9" align="left">
                                <div class="operatesystemDiv"></div>
                                <input id="operatesystem" name="operatesystem"
                                       type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${tQhMaterialLockEntity.operatesystem }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button"
                                                                   value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span> <input type="hidden" id="attachids"
                                               name="attachids" value="${tQhMaterialLockEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','品質嫌疑品物料鎖料需求申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="23%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${tQhMaterialLockEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${tQhMaterialLockEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="23%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${tQhMaterialLockEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${tQhMaterialLockEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="23%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${tQhMaterialLockEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${tQhMaterialLockEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="23%" style="float: left;margin-left: 5px;"
                                                   id="qsqueryresultTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['qsqueryresultno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(165,'qsqueryresultTable','qsqueryresultno','qsqueryresultname',$('#dealchoosefactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="qsqueryresultno" name="qsqueryresultno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['qsqueryresultno']}"
                                                               readonly
                                                               value="${tQhMaterialLockEntity.qsqueryresultno }"/><c:if
                                                            test="${requiredMap['qsqueryresultno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        <span id ="qscha"></span>
                                                        /<input id="qsqueryresultname" name="qsqueryresultname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['qsqueryresultno']}"
                                                                value="${tQhMaterialLockEntity.qsqueryresultname }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="23%" style="float: left;margin-left: 5px;" id="hqchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">${requiredMap['hqchargeno_name']}<a
                                                                        href="javascript:void(0);"
                                                                        onclick="addRowComm('hqchargeno');">添加一位</a>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hqchargeno" name="hqchargeno"
                                                               class="easyui-validatebox"
                                                               onblur="getUserNameByEmpnoComm(this)"
                                                               data-options="width:80,required:${requiredMap['hqchargeno']}"
                                                               value="${tQhMaterialLockEntity.hqchargeno }"/><c:if
                                                            test="${requiredMap['hqchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hqchargename" name="hqchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hqchargeno']}"
                                                                value="${tQhMaterialLockEntity.hqchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="23%" style="float: left;margin-left: 5px;" id="gcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['gcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(644,'gcchargeTable','gcchargeno','gcchargename',$('#dealchoosefactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="gcchargeno" name="gcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['gcchargeno']}"
                                                               readonly
                                                               value="${tQhMaterialLockEntity.gcchargeno }"/>
                                                        <c:if test="${requiredMap['gcchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        /<input id="gcchargename" name="gcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['gcchargeno']}"
                                                                value="${tQhMaterialLockEntity.gcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="23%" style="float: left;margin-left: 5px;"
                                                   id="pqechargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['pqechargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(645,'pqechargeTable','pqechargeno','pqechargename',$('#dealchoosefactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pqechargeno" name="pqechargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pqechargeno']}"
                                                               readonly
                                                               value="${tQhMaterialLockEntity.pqechargeno }"/>
                                                        <c:if test="${requiredMap['pqechargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        /<input id="pqechargename" name="pqechargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pqechargeno']}"
                                                                value="${tQhMaterialLockEntity.pqechargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="23%" style="float: left;margin-left: 5px;"
                                                   id="oqcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['oqcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(211,'oqcchargeTable','oqcchargeno','oqcchargename',$('#dealchoosefactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="oqcchargeno" name="oqcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['oqcchargeno']}"
                                                               readonly
                                                               value="${tQhMaterialLockEntity.oqcchargeno }"/><c:if
                                                            test="${requiredMap['oqcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="oqcchargename" name="oqcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['oqcchargeno']}"
                                                                value="${tQhMaterialLockEntity.oqcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <table width="23%" style="float: left;margin-left: 5px;"
                                                   id="qhsgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['qhsgchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(167,'qhsgchargeTable','qhsgchargeno','qhsgchargename',$('#dealchoosefactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="qhsgchargeno" name="qhsgchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['qhsgchargeno']}"
                                                               readonly
                                                               value="${tQhMaterialLockEntity.qhsgchargeno }"/><c:if
                                                            test="${requiredMap['qhsgchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        <span id ="qhsg"></span>
                                                        /<input id="qhsgchargename" name="qhsgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['qhsgchargeno']}"
                                                                value="${tQhMaterialLockEntity.qhsgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="23%" style="float: left;margin-left: 5px;"
                                                   id="qslockchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['qslockchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(168,'qslockchargeTable','qslockchargeno','qslockchargename',$('#dealchoosefactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="qslockchargeno" name="qslockchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['qslockchargeno']}"
                                                               readonly
                                                               value="${tQhMaterialLockEntity.qslockchargeno }"/><c:if
                                                            test="${requiredMap['qslockchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="qslockchargename" name="qslockchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['qslockchargeno']}"
                                                                value="${tQhMaterialLockEntity.qslockchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="23%" style="float: left;margin-left: 5px;"
                                                   id="qhhubchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['qhhubchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(169,'qhhubchargeTable','qhhubchargeno','qhhubchargename',$('#dealchoosefactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="qhhubchargeno" name="qhhubchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['qhhubchargeno']}"
                                                               readonly
                                                               value="${tQhMaterialLockEntity.qhhubchargeno }"/><c:if
                                                            test="${requiredMap['qhhubchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        <span id="qhhub"></span>
                                                        /<input id="qhhubchargename" name="qhhubchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['qhhubchargeno']}"
                                                                value="${tQhMaterialLockEntity.qhhubchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhMaterialLockEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;"
                                   onclick="canelTask('${tQhMaterialLockEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <input type="hidden" id="otherreason" value="${tQhMaterialLockEntity.otherreason}"/>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
    <div id="dlg"></div>
</form>
</div>
<script src='${ctx}/static/js/shengchan/tqhmateriallock.js?random=<%= Math.random()%>'></script>
</body>
</html>