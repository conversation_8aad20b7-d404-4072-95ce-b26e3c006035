<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>量試樣品驗證單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
	type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfsampletestprocess/${action}" method="post">
       <!--
		               serialno 任務編號
                   processid 工單實例ID
                   workflowid 流程編碼
                   makerno 填單人工號
                   makername 填單人名稱
                   makerip 填單人IP
                   createtime 創建時間
                   complettime 完成時間
                   workstatus 表單狀態
                   makerdeptno 填單人單位代碼
                   makerfactoryid 填單人廠區ID
                   attachids 附件ID
                   dealno 申請人工號
                   dealname 申請人姓名
                   dealdeptno 申請人單位代碼
                   dealdeptname 申請人單位名稱
                   costno 費用代碼
                   dealfactoryid 申請人廠區代碼(所在廠區)
                   dealemail 申請人郵箱
                   dealtel 申請人聯繫電話
                   testfloor 驗證樓層
                   joindept 參與單位
                   ylno1 企劃人員
                   ylname1 ${column.comments}
                   kchargeno 課級主管
                   kchargename ${column.comments}
                   bchargeno 部級主管
                   bchargename ${column.comments}
                   cchargeno 廠級主管
                   cchargename ${column.comments}
                   zchargeno 製造處級主管
                   zchargename ${column.comments}
                   zcchargeno 製造總處級主管
                   zcchargename ${column.comments}
                   ylno2 企劃主管
                   ylname2 ${column.comments}
                   ylno3 生產主管
                   ylname3 ${column.comments}
                   ylno5 生技主管
                   ylname5 ${column.comments}
                   ylno8 制工主管
                   ylname8 ${column.comments}
                   ylno4 品管主管
                   ylname4 ${column.comments}
                   testdept 量試單位
                   samplename 品名
                   sampleno 品號
                   rawno 原料號
                   mouldno 模號
                   rev REV
                   orderno 訂單
                   needno 需求數量
                   needstartdate 需求開始日期
                   reason 原因說明
                   testbak 驗證背景
                   testdetail 驗證明細
                   testeffect 驗證效果
                   testprocess 驗證流程
                   testdutytel 驗證負責人電話
                   prodstatus 生產狀況
                   needenddate 需求結束日期
                   describtion 備註
                   ylno6 刀具室主管
                   ylname6 ${column.comments}
                   ylno7 加工室主管
                   ylname7 ${column.comments}
                   ylno9 產工主管
                   ylname9 ${column.comments}
                   ylno10 其他主管
                   ylname10 ${column.comments}
                   other 參與單位-其它
                   proeffect 生產狀況-有影響生產排配預計降量補充
                   prodept 生產狀況-量試單位補充
                   joindept1 參與單位-其它
                   prodstatus1 生產狀況-量試單位
                   prodstatus2 生產狀況-有影響生產排配預計降量
                   testdutyname 驗證負責人姓名
                   id 主鍵
                   createBy 創建人
                   createDate 創建時間
                   updateBy 更新者
                   updateDate 更新時間
                   delFlag 刪除標識
        		   -->
    <input id="ids" name="ids" type="hidden" value="${wfsampletestprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfsampletestprocessEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">量試樣品驗證單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfsampletestprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfsampletestprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfsampletestprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfsampletestprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfsampletestprocessEntity.makerno}/${wfsampletestprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
           <tr>
                <td>
                    <table class="formList">
                    <tr align="center">
								<td>申請人工號</td>
								<td><input id="dealno" name="dealno" readonly
									class="easyui-validatebox inputCss"
									data-options="width: 150,required:true"
									value="${wfsampletestprocessEntity.dealno}" /></td>
								<td>申請人</td>
								<td><input id="dealname" name="dealname" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfsampletestprocessEntity.dealname}" /></td>
								<td>聯繫分機</td>
								<td><input id="dealtel" name="dealtel" readonly
									class="easyui-validatebox inputCss"
									data-options="width: 150,required:true"
									value="${wfsampletestprocessEntity.dealtel}" /></td>
								<td>所在廠區</td>
								<td><input id="dealfactoryid" disabled
									name="dealfactoryid" class="easyui-combobox"
									data-options="width: 150,required:true"
									value="${wfsampletestprocessEntity.dealfactoryid}" /></td>
								<td>單位代碼</td>
								<td><input id="dealdeptno" name="dealdeptno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfsampletestprocessEntity.dealdeptno}" /></td>
							</tr>
							<tr align="center">
								<td>單位名稱</td>
								<td colspan="4"><input id="dealdeptname"
									style="width:360px" name="dealdeptname"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 360,required:true"
									value="${wfsampletestprocessEntity.dealdeptname}" /></td>
								<td>聯繫郵箱</td>
								<td colspan="4"><input id="dealemail" name="dealemail"
									class="easyui-validatebox inputCss"readonly
									data-options="width: 250,required:true"
									value="${wfsampletestprocessEntity.dealemail}" /></td>

							</tr>
							<tr align="center">
								<td>參與單位</td>
								<td colspan="9" align="left">
									<div class="participatingunitsDiv"></div> <input id="joindept"
									name="joindept" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${wfsampletestprocessEntity.joindept}" /> <input
									id="joindept1" name="joindept1" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${wfsampletestprocessEntity.joindept1}" />
									<input id="other1" name="other1" readonly type="hidden"
									class="easyui-validatebox" data-options="width: 80"
									value="${wfsampletestprocessEntity.other}" />
									<input type="hidden" id="disOrEnabled" value="disabled"/>
								</td>
							</tr>
							<tr align="center">
								<td>量試單位</td>
								<td colspan="9"><input id="testdept" style="width:900px"
									name="testdept" class="easyui-validatebox inputCss" readonly
									data-options="width: 900,required:true"
									value="${wfsampletestprocessEntity.testdept}" /></td>
							</tr>
							<tr align="center">
								<td>品名</td>
								<td colspan="2"><input id="samplename" style="width:150px"
									name="samplename" class="easyui-validatebox inputCss"
									data-options="width: 150,required:true" readonly
									value="${wfsampletestprocessEntity.samplename}" /></td>
								<td>品(料)號</td>
								<td colspan="3"><input id="sampleno" style="width:200px"
									name="sampleno" class="easyui-validatebox inputCss"
									data-options="width: 200,required:true" readonly
									value="${wfsampletestprocessEntity.sampleno}" /></td>
								<td>原(物)料號</td>
								<td colspan="2"><input id="rawno" style="width:150px"
									name="rawno" class="easyui-validatebox inputCss"
									data-options="width: 150,required:true" readonly
									value="${wfsampletestprocessEntity.rawno}" /></td>
							</tr>
							<tr align="center">
								<td>模號</td>
								<td colspan="2"><input id="mouldno" style="width:150px"
									name="mouldno" class="easyui-validatebox inputCss"
									data-options="width: 150,required:true" readonly
									value="${wfsampletestprocessEntity.mouldno}" /></td>
								<td>REV</td>
								<td colspan="3"><input id="rev" style="width:200px"
									name="rev" class="easyui-validatebox inputCss"
									data-options="width: 200,required:true" readonly
									value="${wfsampletestprocessEntity.rev}" /></td>
								<td>訂單號</td>
								<td colspan="2"><input id="orderno" style="width:150px"
									name="orderno" class="easyui-validatebox inputCss"
									data-options="width: 150,required:true" readonly
									value="${wfsampletestprocessEntity.orderno}" /></td>
							</tr>
							<tr align="center">
								<td>需求數量</td>
								<td colspan="2"><input id="needno" style="width:150px"
									name="needno" class="easyui-validatebox inputCss" 
									data-options="width: 150,required:true" readonly
									value="${wfsampletestprocessEntity.needno}" /></td>
								<td>需求日期</td>
								<td colspan="3"><input id="needstartdate" name="needstartdate" class="Wdate"
									data-options="width:150,required:true" style="width:150px" disabled
									value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${wfsampletestprocessEntity.needstartdate}"/>"
									onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" />~ <input
									id="needenddate" name="needenddate" class="Wdate"
									data-options="width:150,required:true" style="width:150px" disabled
									value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${wfsampletestprocessEntity.needenddate}"/>"
									onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" /></td>
								<td>費用代碼</td>
								<td colspan="2"><input id="costno" style="width:150px"
									name="costno" class="easyui-validatebox inputCss"
									data-options="width: 150,required:true" readonly
									value="${wfsampletestprocessEntity.costno}" /></td>
							</tr>
							<tr align="center">
							    <td>驗證名稱</td>
							    <td colspan="9"><input id="testname" style="width:1000px"
									name="testname" class="easyui-validatebox inputCss"
									data-options="width: 1000,required:true" readonly
									value="${wfsampletestprocessEntity.testname}" /></td>
							</tr>
							<tr align="center">
								<td>驗證背景</td>
								<td align="left" colspan="9"><textarea id="testbak"
										name="testbak" oninput="return LessThan(this,'txtNum1');"
										onchange="return LessThan(this,'txtNum1');"
										onpropertychange="return LessThan(this,'txtNum1');"
										data-options="required:true" maxlength="500" readonly
										class="easyui-validatebox" style="width:1000px;height:120px;"
										rows="5" cols="6">${wfsampletestprocessEntity.testbak}</textarea><span
									id="txtNum1"></span></td>
							</tr>
							<tr align="center">
								<td>驗證明細</td>
								<td align="left" colspan="9"><textarea id="testdetail"
										name="testdetail" oninput="return LessThan(this,'txtNum2');"
										onchange="return LessThan(this,'txtNum2');"
										onpropertychange="return LessThan(this,'txtNum2');"
										data-options="required:true" maxlength="500" readonly
										class="easyui-validatebox" style="width:1000px;height:120px;"
										rows="5" cols="6">${wfsampletestprocessEntity.testdetail}</textarea><span
									id="txtNum2"></span></td>
							</tr>
							<tr align="center">
								<td>驗證樓層</td>
								<td align="left" colspan="9"><textarea id="testfloor"
										name="testfloor" oninput="return LessThan(this,'txtNum3');"
										onchange="return LessThan(this,'txtNum3');"
										onpropertychange="return LessThan(this,'txtNum3');"
										data-options="required:true" maxlength="200" readonly
										class="easyui-validatebox" style="width:1000px;height:55px;"
										rows="5" cols="6">${wfsampletestprocessEntity.testfloor}</textarea><span
									id="txtNum3"></span></td>
							</tr>
							<tr align="center">
								<td>驗證效果</td>
								<td align="left" colspan="9"><textarea id="testeffect"
										name="testeffect" oninput="return LessThan(this,'txtNum4');"
										onchange="return LessThan(this,'txtNum4');"
										onpropertychange="return LessThan(this,'txtNum4');"
										data-options="required:true" maxlength="200" readonly
										class="easyui-validatebox" style="width:1000px;height:55px;"
										rows="5" cols="6">${wfsampletestprocessEntity.testeffect}</textarea><span
									id="txtNum4"></span></td>
							</tr>
							<tr align="center">
								<td>驗證流程</td>
								<td align="left" colspan="9"><textarea id="testprocess"
										name="testprocess" oninput="return LessThan(this,'txtNum5');"
										onchange="return LessThan(this,'txtNum5');"
										onpropertychange="return LessThan(this,'txtNum5');"
										data-options="required:true" maxlength="500" readonly
										class="easyui-validatebox" style="width:1000px;height:80px;"
										rows="5" cols="6">${wfsampletestprocessEntity.testprocess}</textarea><span
									id="txtNum5"></span></td>
							</tr>
							<tr align="center">
								<td>驗證負責人</td>
								<td>姓名</td>
								<td colspan="3"><input id="testdutyname"
									style="width:150px" name="testdutyname"
									class="easyui-validatebox inputCss"
									data-options="width: 150,required:true" readonly
									value="${wfsampletestprocessEntity.testdutyname}" /></td>
								<td>電話</td>
								<td colspan="4"><input id="testdutytel" style="width:150px"
									name="testdutytel" class="easyui-validatebox inputCss"
									data-options="width: 150,required:true" readonly
									value="${wfsampletestprocessEntity.testdutytel}" /></td>
							</tr>
							<tr align="left">
								<td align="center">生產狀況</td>
								<td colspan="9">
									<div class="productionstatusDiv"></div> <input id="prodstatus"
									name="prodstatus" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${wfsampletestprocessEntity.prodstatus }" /> 
									<input id="prodstatus1" name="prodstatus1" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${wfsampletestprocessEntity.prodstatus1}" />
									<input id="prodstatus2" name="prodstatus2" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${wfsampletestprocessEntity.prodstatus2}" /> 
									<input id="proeffect1" name="proeffect1" disabled="disabled"
									type="hidden" class="easyui-validatebox"
									data-options="width: 30"
									value="${wfsampletestprocessEntity.proeffect}" /> 
									<input id="prodept1" name="prodept1" disabled="disabled" type="hidden"
									class="easyui-validatebox" data-options="width: 30"
									value="${wfsampletestprocessEntity.prodept}" />
								</td>
							</tr>
							<tr align="center">
								<td>備註</td>
								<td align="left" colspan="9"><textarea id="describtion"
										name="describtion" oninput="return LessThan(this,'txtNum');"
										onchange="return LessThan(this,'txtNum');"
										onpropertychange="return LessThan(this,'txtNum');" readonly
										maxlength="200" class="easyui-validatebox" style="width:1000px;height:65px;"
										rows="5" cols="6">${wfsampletestprocessEntity.describtion}</textarea><span
									id="txtNum"></span></td>
							</tr>
              <!--       </table>
                </td>
           </tr>
            <tr>
                <td>
                     <table class="formList"> -->
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${wfsampletestprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>批註</td>
                            <td colspan="9">
						<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wfsampletestprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','量試樣品驗證單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfsampletestprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  <div id="dlg"></div>
<script src='${ctx}/static/js/shengchan/wfsampletestprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>