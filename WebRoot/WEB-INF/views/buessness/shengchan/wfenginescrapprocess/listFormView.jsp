<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>工程報廢單業務表</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<form id="mainform" action="${ctx}/wfenginescrapprocess/${action}" method="post">
		<input id="ids" name="ids" type="hidden"
			value="${wfenginescrapprocessEntity.id }" /> <input id="serialno"
			name="serialno" type="hidden"
			value="${wfenginescrapprocessEntity.serialno }" />
		<div class="commonW">
			<div class="headTitle">工程報廢單業務表</div>
			<div class="position_L">
				任務編碼：<span style="color:#999;"> <c:choose>
						<c:when test="${wfenginescrapprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
						<c:otherwise>
                           ${wfenginescrapprocessEntity.serialno}
                       </c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_L1 margin_L">
				填單時間：<span style="color:#999;"> <c:choose>
						<c:when test="${wfenginescrapprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
						<c:otherwise>
							<input style="border:0px" style="width: 120px" readonly="true"
								value="<fmt:formatDate value='${wfenginescrapprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>" />
						</c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_R margin_R">填單人：${wfenginescrapprocessEntity.makerno}/${wfenginescrapprocessEntity.makername}</div>
			<div class="clear"></div>
			<table class="formList">
				<tr>
					<td>
						<table class="formList">
							<tr>
								<td>
									<table class="formList">
										<tr align="center">
											<td>申請人工號</td>
											<td><input id="dealno"
												name="wfenginescrapprocess.dealno"
												class="easyui-validatebox inputCss" readonly
												data-options="width: 150"
												value="${wfenginescrapprocessEntity.dealno}" /></td>
											<td>申請人姓名</td>
											<td><input id="dealname"
												name="wfenginescrapprocess.dealname" readonly
												class="easyui-validatebox inputCss"
												data-options="width: 150"
												value="${wfenginescrapprocessEntity.dealname}" /></td>
											<td>所在廠區</td>
											<td><input id="dealfactoryid"
												name="wfenginescrapprocess.dealfactoryid" disabled
												class="easyui-combobox" data-options="width: 100"
												value="${wfenginescrapprocessEntity.dealfactoryid}" /></td>
											<td>單位代碼</td>
											<td><input id="dealdeptno"
												name="wfenginescrapprocess.dealdeptno" readonly
												class="easyui-validatebox inputCss"
												data-options="width: 150"
												value="${wfenginescrapprocessEntity.dealdeptno}" /></td>
											<td>聯繫分機</td>
											<td><input id="dealtel"
												name="wfenginescrapprocess.dealtel" readonly
												class="easyui-validatebox inputCss"
												data-options="width: 150"
												value="${wfenginescrapprocessEntity.dealtel}" /></td>
										</tr>
										<tr align="center">
											<td>單位名稱</td>
											<td colspan="4"><input id="dealdeptname" readonly
												style="width:380px" name="wfenginescrapprocess.dealdeptname"
												class="easyui-validatebox inputCss"
												data-options="width: 380"
												value="${wfenginescrapprocessEntity.dealdeptname}" /></td>
											<td>聯繫郵箱</td>
											<td colspan="4"><input id="dealemail" readonly
												name="wfenginescrapprocess.dealemail"
												class="easyui-validatebox inputCss"
												data-options="width: 250,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"
												value="${wfenginescrapprocessEntity.dealemail}" /></td>
										</tr>
									</table>
								</td>
							</tr>
							<tr>
								<td>
									<table class="formList">
										<tr>
											<td colspan="10" class="td_style1">報廢信息</td>
										</tr>
										<tr align="center">
											<td colspan="10" width="100%">
												<div style="overflow-x: auto;">
													<table id="enginscrapItemTable" width="100%">
														<tr align="center">
															<td>&nbsp;序號&nbsp;</td>
															<td>料號</td>
															<td>機種</td>
															<td>品名</td>
															<td>顏色</td>
															<td>物料形態</td>
															<td>廠級代碼</td>
															<td>廠別</td>
															<td>制程</td>
															<td>樓層</td>
															<td>報廢數量</td>
															<td>報廢類別</td>
															<td>費用類別</td>
															<td>驗證類別</td>
														</tr>
														<tbody id="info_Body">
															<c:if test="${itemEntity!=null&&itemEntity.size()>0}">
																<c:forEach items="${itemEntity}" var="item"
																	varStatus="status">
																	<tr align="center" id="scrapItem${status.index+1}">
																		<td>${status.index+1}</td>
																		<td>${item.materialcode}</td>
																		<td>${item.machinetype}</td>
																		<td>${item.charactername}</td>
																		<td>${item.color}</td>
																		<td><input id="materialform${status.index+1}"
																			class="easyui-combobox" disabled
																			data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadMaterialform(${status.index+1});}"
																			style="width:100px;" value="${item.materialform}" /></td>
																		<td>${item.factorycode}</td>
																		<td>${item.factorytype}</td>
																		<td>${item.process}</td>
																		<td>${item.floor}</td>
																		<td>${item.scrapnum}</td>
																		<td><input id="scraptype${status.index+1}"
																			disabled class="easyui-combobox"
																			data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,onBeforeLoad:function(){
																              loadScraptype(${status.index+1});}"
																			style="width:80px;" value="${item.scraptype}" /></td>
																		<td><input id="feetype${status.index+1}" disabled
																			name="wfenginescrapiterms[${status.index}].feetype"
																			class="easyui-combobox"
																			data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,onBeforeLoad:function(){
																              loadFeeType(${status.index+1});}"
																			style="width:80px;" value="${item.feetype}" /></td>
																		<td>${item.verifytype}</td>
																	</tr>
																</c:forEach>
															</c:if>
															<c:if test="${itemEntity==null}">
																<tr align="center" id="scrapItem1">
																	<td>1</td>
																	<td></td>
																	<td></td>
																	<td></td>
																	<td></td>
																	<td></td>
																	<td></td>
																	<td></td>
																	<td></td>
																	<td></td>
																	<td></td>
																	<td></td>
																	<td></td>
																	<td></td>
																</tr>
															</c:if>
														</tbody>
														<tr class="nottr">
															<td colspan="10" style="text-align: right;">報廢總數:&nbsp;&nbsp;</td>
															<td>${wfenginescrapprocessEntity.scraptotalnum}</td>
															<td colspan="3">&nbsp;</td>
														</tr>
													</table>
												</div>
											</td>
										</tr>
									</table>
								</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td>附件</td>
								<td colspan="9" class="td_style1">
									<input type="hidden" id="attachids" name="attachids" value="${wfenginescrapprocessEntity.attachids }" />
									<div id="dowloadUrl">
										<c:forEach items="${file}" varStatus="i" var="item">
											<div id="${item.id}"
												style="line-height:30px;margin-left:5px;" class="float_L">
												<div class="float_L">
													<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
												</div>
											</div>
										</c:forEach>
									</div></td>
							</tr>
							<tr align="center">
								<td>費用代碼</td>
								<td><input id="applycostno" readonly
									name="wfenginescrapprocess.applycostno"
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfenginescrapprocessEntity.applycostno}" /></td>
								<td>表單編號</td>
								<td><input id="formserialno"
									name="wfenginescrapprocess.formserialno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfenginescrapprocessEntity.formserialno}" /></td>
								<td>入庫單號</td>
								<td><input id="warehousenum"
									name="wfenginescrapprocess.warehousenum" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfenginescrapprocessEntity.warehousenum}" /></td>
								<td>轉撥單號</td>
								<td><input id="transfernum"
									name="wfenginescrapprocess.transfernum" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfenginescrapprocessEntity.transfernum}" /></td>
							</tr>
							<tr align="center">
								<td>備註</td>
								<td align="left" colspan="9"><textarea id="remark" readonly
										name="wfenginescrapprocess.remark" class="easyui-validatebox"
										style="width:800px;height:80px;" rows="5" cols="6">${wfenginescrapprocessEntity.remark}</textarea></td>
							</tr>
							<tr>
								<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
									<a href="javascript:void(0)"
									onclick="showWfImag('${processId}','工程報廢單業務表');">點擊查看簽核流程圖</a>
								</th>
							</tr>
							<tr>
								<td colspan="10" style="text-align:left;">
									${chargeNodeInfo}</td>
							</tr>

							<tr>
								<td colspan="10" style="text-align:left;"><iframe
										id="qianheLogFrame" name="qianheLogFrame"
										src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfenginescrapprocessEntity.serialno}"
										width="100%"></iframe></td>
							</tr>
							<tr class="no-print">
								<td colspan="10" style="text-align:center;padding-left:10px;">
									<a href="javascript:;" id="btnClose" class="easyui-linkbutton"
									data-options="iconCls:'icon-cancel'" style="width: 100px;"
									onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp; <a
									href="#" id="btnPrint" class="easyui-linkbutton"
									data-options="iconCls:'icon-print'" style="width: 100px;"
									onclick="printWindow('btnClose,btnPrint');">列印</a>

								</td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</div>
	</form>
	</div>
	<div id="dlg"></div>
	<script
		src='${ctx}/static/js/shengchan/wfenginescrapprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>
