<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>工程報廢申請單</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<body>
	<form id="mainform" action="${ctx}/wfenginescrapprocess/${action}" method="post">
		<input id="ids" name="ids" type="hidden"
			value="${wfenginescrapprocessEntity.id }" /> <input id="serialno"
			name="wfenginescrapprocess.serialno" type="hidden"
			value="${wfenginescrapprocessEntity.serialno }" /> <input
			id="makerno" name="wfenginescrapprocess.makerno" type="hidden"
			value="${wfenginescrapprocessEntity.makerno }" /> <input
			id="makername" name="wfenginescrapprocess.makername" type="hidden"
			value="${wfenginescrapprocessEntity.makername }" /> <input
			id="makerdeptno" name="wfenginescrapprocess.makerdeptno" type="hidden"
			value="${wfenginescrapprocessEntity.makerdeptno }" />
		<div class="commonW">
			<div class="headTitle">工程報廢單業務表</div>
			<div class="position_L">
				任務編碼：<span style="color:#999;"> <c:choose>
						<c:when test="${wfenginescrapprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
						<c:otherwise>
                           ${wfenginescrapprocessEntity.serialno}
                       </c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_L1 margin_L">
				填單時間：<span style="color:#999;"> <c:choose>
						<c:when test="${wfenginescrapprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
						<c:otherwise>
							<input style="border:0px" style="width: 120px" readonly="true"
								value="<fmt:formatDate value='${wfenginescrapprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>" />
						</c:otherwise>
					</c:choose>
				</span>
			</div>
			<c:if test="${empty wfenginescrapprocessEntity.makerno}">
				<div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
			</c:if>
			<c:if test="${not empty wfenginescrapprocessEntity.makerno}">
				<div class="position_R margin_R">填單人：${wfenginescrapprocessEntity.makerno}/${wfenginescrapprocessEntity.makername}</div>
			</c:if>
			<div class="clear"></div>
			<table class="formList">
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td>申請人工號&nbsp;<font color="red">*</font></td>
								<td><input id="dealno" name="wfenginescrapprocess.dealno"
									onblur="queryUserInfo(this)" class="easyui-validatebox"
									data-options="width: 150,required:true"
									value="${wfenginescrapprocessEntity.dealno}" /></td>
								<td>申請人姓名</td>
								<td><input id="dealname"
									name="wfenginescrapprocess.dealname" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfenginescrapprocessEntity.dealname}" /></td>
								<td>所在廠區&nbsp;<font color="red">*</font></td>
								<td><input id="dealfactoryid"
									name="wfenginescrapprocess.dealfactoryid"
									class="easyui-combobox" data-options="width: 150,required:true"
									value="${wfenginescrapprocessEntity.dealfactoryid}" /></td>
								<td>單位代碼</td>
								<td><input id="dealdeptno"
									name="wfenginescrapprocess.dealdeptno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfenginescrapprocessEntity.dealdeptno}" /></td>
								<td>聯繫分機&nbsp;<font color="red">*</font></td>
								<td><input id="dealtel" name="wfenginescrapprocess.dealtel"
									onblur="validApplyTel('dealtel')"
									class="easyui-validatebox"
									data-options="width: 150,required:true"
									value="${wfenginescrapprocessEntity.dealtel}" /></td>
							</tr>
							<tr align="center">
								<td>單位名稱&nbsp;<font color="red">*</font></td>
								<td colspan="4"><input id="dealdeptname"
									style="width:380px" name="wfenginescrapprocess.dealdeptname"
									class="easyui-validatebox"
									data-options="width: 380,required:true"
									value="${wfenginescrapprocessEntity.dealdeptname}" /></td>
								<td>聯繫郵箱&nbsp;<font color="red">*</font></td>
								<td colspan="4"><input id="dealemail"
									name="wfenginescrapprocess.dealemail"
									class="easyui-validatebox"
									data-options="width: 250,required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"
									value="${wfenginescrapprocessEntity.dealemail}" /></td>

							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList">
							<tr>
								<td colspan="10" class="td_style1">報廢信息</td>
							</tr>
							<tr align="center">
								<td colspan="10" width="100%">
									<div style="overflow-x: auto;width: 1200px;">
										<input id="enginscrapItemTableIndex" type="hidden"
											value="<c:if test="${itemEntity!=null && itemEntity.size()>0}">${itemEntity.size() +1}</c:if>
                                        <c:if test="${itemEntity==null}">2</c:if>">
										</input>
										<table id="enginscrapItemTable" width="130%">
											<tr align="center">
												<td>&nbsp;序號&nbsp;</td>
												<td>料號&nbsp;<font color="red">*</font></td>
												<td>機種&nbsp;<font color="red">*</font></td>
												<td>品名&nbsp;<font color="red">*</font></td>
												<td>顏色&nbsp;<font color="red">*</font></td>
												<td>物料形態&nbsp;<font color="red">*</font></td>
												<td>廠級代碼&nbsp;<font color="red">*</font></td>
												<td>廠別&nbsp;<font color="red">*</font></td>
												<td>制程&nbsp;<font color="red">*</font></td>
												<td>樓層&nbsp;<font color="red">*</font></td>
												<td>報廢數量&nbsp;<font color="red">*</font></td>
												<td>報廢類別&nbsp;<font color="red">*</font></td>
												<td>費用類別&nbsp;<font color="red">*</font></td>
												<td>驗證類別&nbsp;<font color="red">*</font></td>
												<td>&nbsp;操作&nbsp;</td>
											</tr>
											<tbody id="info_Body">
												<c:if test="${itemEntity!=null&&itemEntity.size()>0}">
													<c:forEach items="${itemEntity}" var="item"
														varStatus="status">
														<tr align="center" id="scrapItem${status.index+1}">
															<td>${status.index+1}<input type="hidden"
																name="wfenginescrapiterms[${status.index}].id2"
																value="${status.index+1}" /></td>
															<td><input id="materialcode${status.index+1}" onblur="queryPNInfo(this)"
																name="wfenginescrapiterms[${status.index}].materialcode"
																class="easyui-validatebox" data-options="required:true"
																style="width: 120px;" value="${item.materialcode}" /></td>
															<td><input id="machinetype${status.index+1}"
																name="wfenginescrapiterms[${status.index}].machinetype"
																class="easyui-validatebox" data-options="required:true"
																style="width:50px;" value="${item.machinetype}" /></td>
															<td><input type="hidden" id ='charactername${status.index+1}'  name="wfenginescrapiterms[${status.index}].charactername"/>
															    <input id="charnamehide${status.index+1}"
																class="easyui-combobox" data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:true"
																style="width:180px;" value="${item.charactername}" /></td>
															<td><input id="color${status.index+1}"
																name="wfenginescrapiterms[${status.index}].color"
																class="easyui-validatebox" data-options="required:true"
																style="width:50px;" value="${item.color}" /></td>
															<td><input id="materialform${status.index+1}"
																name="wfenginescrapiterms[${status.index}].materialform"
																class="easyui-combobox"
																data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadMaterialform(${status.index+1});}"
																style="width:60px;" value="${item.materialform}" /></td>
															<td><input id="factorycode${status.index+1}"
																name="wfenginescrapiterms[${status.index}].factorycode"
																onblur="validFactorycode(this)"
																class="easyui-validatebox" data-options="required:true"
																style="width:90px;" value="${item.factorycode}" /></td>
															<td><input type="hidden" id ="factorytype${status.index+1}"  name="wfenginescrapiterms[${status.index}].factorytype"/>
															    <input id="factypehide${status.index+1}"
																class="easyui-combobox" data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:true"
																style="width:150px;" value="${item.factorytype}" /></td>
															<td><input type="hidden" id ="process${status.index+1}"  name="wfenginescrapiterms[${status.index}].process"/>
													            <input id="prohide${status.index+1}"
																class="easyui-combobox" data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:true"
																style="width:80px;" value="${item.process}" /></td>
															<%-- <td><input id="floor${status.index+1}"
																name="wfenginescrapiterms[${status.index}].floor"
																class="easyui-combobox" data-options="panelHeight:'auto',
																              valueField:'label',
																              textField:'label',
																              editable:true"
																style="width:80px;" value="${item.floor}" /></td> --%>

															<td><input type="hidden" id="floor${status.index+1}"  name="wfenginescrapiterms[${status.index}].floor"/>
															   <input id="flooorhide${status.index+1}"
																class="easyui-combobox" data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:true"
																style="width:80px;" value="${item.floor}" />
															</td>
															<td><input id="scrapnum${status.index+1}"
																name="wfenginescrapiterms[${status.index}].scrapnum" onblur="scrapnumChange(this);toThousandsCur(this)"
																class="easyui-validatebox" data-options="required:true"
																style="width:80px;" value="${item.scrapnum}" /></td>
															<td><input id="scraptype${status.index+1}"
																name="wfenginescrapiterms[${status.index}].scraptype"
																class="easyui-combobox"
																data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,onBeforeLoad:function(){
																              loadScraptype(${status.index+1});}"
																style="width:80px;" value="${item.scraptype}" /></td>
															<td><input id="feetype${status.index+1}"
																name="wfenginescrapiterms[${status.index}].feetype"
																class="easyui-combobox"
																data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,onBeforeLoad:function(){
																              loadFeeType(${status.index+1});}"
																style="width:80px;" value="${item.feetype}" /></td>
															<td><input id="verifytype${status.index+1}"
																name="wfenginescrapiterms[${status.index}].verifytype"
																class="easyui-validatebox" data-options="required:true"
																style="width:80px;" value="${item.verifytype}" /></td>
															<td><input type="image"
																src="${ctx}/static/images/deleteRow.png"
																onclick="deltr(${status.index+1});return false;" /></td>
														</tr>
													</c:forEach>
												</c:if>
												<c:if test="${itemEntity==null}">
													<tr align="center" id="scrapItem1">
														<td>1<input  type="hidden"
															name="wfenginescrapiterms[0].id2" value="1" /></td>
														<td><input id="materialcode1"
															name="wfenginescrapiterms[0].materialcode" onblur="queryPNInfo(this)"
															class="easyui-validatebox" data-options="required:true"
															style="width:120px;" value="" /></td>
														<td><input id="machinetype1"
															name="wfenginescrapiterms[0].machinetype"
															class="easyui-validatebox" data-options="required:true"
															style="width:50px;" value="" /></td>
														<td><input type="hidden" id ="charactername1"  name="wfenginescrapiterms[0].charactername"/>
														    <input id="charnamehide1"
															class="easyui-combobox" data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:true"
															style="width:180px;" value="" /></td>
														<td><input id="color1"
															name="wfenginescrapiterms[0].color"
															class="easyui-validatebox" data-options="required:true"
															style="width:50px;" value="" /></td>
														<td><input id="materialform1"
															name="wfenginescrapiterms[0].materialform"
															class="easyui-combobox"
														    data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              required:true,
																              onBeforeLoad:function(){
																              loadMaterialform(1);}"
															style="width:60px;" value="" /></td>
														<td><input id="factorycode1"
															name="wfenginescrapiterms[0].factorycode"
															onblur="validFactorycode(this)"
															class="easyui-validatebox" data-options="required:true"
															style="width:90px;" value="" /></td>
														<td><input type="hidden" id ='factorytype1'  name="wfenginescrapiterms[0].factorytype"/>
														    <input id="factypehide1"
															class="easyui-combobox" data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:true"
															style="width:150px;" value="" /></td>
														<td><input type="hidden" id ='process1'  name="wfenginescrapiterms[0].process"/>
													        <input id="prohide1"
															class="easyui-combobox" data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:true"
															style="width:80px;" value="" /></td>
														<!-- <td><input id="floor1"
															name="wfenginescrapiterms[0].floor"
															class="easyui-combobox" data-options="panelHeight:'auto',
																              valueField:'labels',
																              textField:'label',
																              editable:true"
															style="width:80px;" value="" /></td> -->
														<td><input type="hidden" id="floor1"  name="wfenginescrapiterms[0].floor"/>
															<input id="flooorhide1"
															class="easyui-combobox" data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:true"
															style="width:80px;" value="" />
													    </td>
														<td><input id="scrapnum1"
															name="wfenginescrapiterms[0].scrapnum" onblur="scrapnumChange(this);toThousandsCur(this)"
															class="easyui-validatebox" data-options="required:true"
															style="width:80px;" value="" /></td>
														<td><input id="scraptype1"
															name="wfenginescrapiterms[0].scraptype"
															class="easyui-combobox"
																data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,onBeforeLoad:function(){
																              loadScraptype(1);}"
															style="width:80px;" value="" /></td>
														<td><input id="feetype1"
															name="wfenginescrapiterms[0].feetype"
															class="easyui-combobox"
                                                            data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,onBeforeLoad:function(){
																              loadFeeType(1);}"
															style="width:80px;" value="" /></td>
														<td><input id="verifytype1"
															name="wfenginescrapiterms[0].verifytype"
															class="easyui-validatebox" data-options="required:true"
															style="width:80px;" value="" /></td>
														<td><input type="image"
															src="${ctx}/static/images/deleteRow.png"
															onclick="deltr(1);return false;" /></td>
													</tr>
												</c:if>
											</tbody>
											<tr class="nottr">
												<td colspan="10" style="text-align: right;">報廢總數:&nbsp;&nbsp;</td>
												<td><input id="scraptotalnum"
													name="wfenginescrapprocess.scraptotalnum"
													class="easyui-validatebox inputCss" style="color: #ff9000;"
													data-options="width:100"
													value="${wfenginescrapprocessEntity.scraptotalnum}" /></td>
												<td colspan="4">&nbsp;</td>
											</tr>
											<tr align="left" class="nottr">
												<td colspan="21" width="100%"
													style="text-align:left;padding-left:10px;"><input
													type="button" id="itemAdd" style="width:100px;float:left;"
													value="添加一筆" /></td>
											</tr>
										</table>
									</div>
								</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="2" >附件</td>
								<td colspan="8" class="td_style1">
									<span class="sl-custom-file">
										<input type="button" value="点击上传文件" class="btn-file" />
										<input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file" />
									</span>
									<input type="hidden" id="attachids" name="wfenginescrapprocess.attachids" value="${wfenginescrapprocessEntity.attachids }" />
									<div id="dowloadUrl">
										<c:forEach items="${file}" varStatus="i" var="item">
											<div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
												<div class="float_L">
													<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
												</div>
												<div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
											</div>
										</c:forEach>
									</div></td>
							</tr>
							<tr align="center">
								<td colspan="2" >備註</td>
								<td align="left" colspan="8"><textarea id="remark"
										name="wfenginescrapprocess.remark" oninput="return LessThanAuto(this,'txtNum');"
										onchange="return LessThanAuto(this,'txtNum');"
										onpropertychange="return LessThanAuto(this,'txtNum');"
										maxlength="200" class="easyui-validatebox"
										style="width:1000px;height:65px;" rows="5" cols="6">${wfenginescrapprocessEntity.remark}</textarea><span
									id="txtNum"></span></td>
							</tr>
							<tr>
								<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
									<a href="javascript:void(0)"
									onclick="showWfImag('dzqh_gongchengbaofeidan_v1','工程報廢單業務表','');">點擊查看簽核流程圖</a>
								</th>
							</tr>
							<tr>
								<td colspan="10" style="text-align:left;">
									<table class="flowList"
										style="margin-left:5px;margin-top:5px;width:99%">
										<tr>
											<td style="border:none">
												<table width="18%" style="float: left;margin-left: 5px;"
													id="kchargenoTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">申請人課級主管
																		<a href="#" onclick="addRowScrap('kchargeno')">添加一位</a>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="kchargeno"
															onblur="getUserNameByEmpno(this);" name="wfenginescrapprocess.kchargeno"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['kchargeno']}"
															value="${wfenginescrapprocessEntity.kchargeno}" /> <c:if
																test="${requiredMap['kchargeno'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="kchargename" name="wfenginescrapprocess.kchargename"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['kchargeno']}"
															readonly
															value="${wfenginescrapprocessEntity.kchargename}" /></td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="bchargenoTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">申请人部級主管<a
																		href="#" onclick="addRowScrap('bchargeno')">添加一位</a></td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="bchargeno" name="wfenginescrapprocess.bchargeno"
															class="easyui-validatebox"
															onblur="getUserNameByEmpno(this);"
															data-options="width:80,required:${requiredMap['bchargeno']}"
															value="${wfenginescrapprocessEntity.bchargeno}" /> <c:if
																test="${requiredMap['bchargeno'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="bchargename" name="wfenginescrapprocess.bchargename" readonly
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['bchargeno']}"
															value="${wfenginescrapprocessEntity.bchargename}" /></td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno1Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">責任單位工程師<a
																		href="#" onclick="addRowScrap('ylno1')">添加一位</a></td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno1"
															onblur="getUserNameByEmpno(this);" name="wfenginescrapprocess.ylno1"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno1']}"
															value="${wfenginescrapprocessEntity.ylno1}" /> <c:if
																test="${requiredMap['ylno1'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname1" name="wfenginescrapprocess.ylname1"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno1']}"
															readonly value="${wfenginescrapprocessEntity.ylname1}" />
														</td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno2Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">責任單位課級主管<a
																		href="#" onclick="addRowScrap('ylno2')">添加一位</a></td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno2"
															onblur="getUserNameByEmpno(this);" name="wfenginescrapprocess.ylno2"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno2']}"
															value="${wfenginescrapprocessEntity.ylno2}" /> <c:if
																test="${requiredMap['ylno2'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname2" name="wfenginescrapprocess.ylname2"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno2']}"
															readonly value="${wfenginescrapprocessEntity.ylname2}" />
														</td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno5Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">工程單位成本負責人/主管</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRoleForScrap(96,'ylno5Table','ylno5','ylname5',$('#dealfactoryid').combobox('getValue'),'wfenginescrapprocess',$('#factorycode1').val())"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno5"
															onblur="getUserNameByEmpno(this);" name="wfenginescrapprocess.ylno5"
															class="easyui-validatebox" readonly
															data-options="width: 80,required:${requiredMap['ylno5']}"
															value="${wfenginescrapprocessEntity.ylno5 }" /> <c:if
																test="${requiredMap['ylno5'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname5" name="wfenginescrapprocess.ylname5"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno5']}"
															readonly value="${wfenginescrapprocessEntity.ylname5}" />
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td style="border:none">
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno3Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">責任單位部級主管<a
																		href="#" onclick="addRowScrap('ylno3')">添加一位</a></td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno3"
															onblur="getUserNameByEmpno(this);" name="wfenginescrapprocess.ylno3"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno3']}"
															value="${wfenginescrapprocessEntity.ylno3}" /> <c:if
																test="${requiredMap['ylno3'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname3" name="wfenginescrapprocess.ylname3"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno3']}"
															readonly value="${wfenginescrapprocessEntity.ylname3}" />
														</td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno4Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">制工負責人<a
																		href="#" onclick="addRowScrap('ylno4')">添加一位</a></td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno4"
															onblur="getUserNameByEmpno(this);" name="wfenginescrapprocess.ylno4"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno4']}"
															value="${wfenginescrapprocessEntity.ylno4}" /> <c:if
																test="${requiredMap['ylno4'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname4" name="wfenginescrapprocess.ylname4"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno4']}"
															readonly value="${wfenginescrapprocessEntity.ylname4}" />
														</td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno6Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">DQE負責人<a
																		href="#" onclick="addRowScrap('ylno6')">添加一位</a></td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno6"
															onblur="getUserNameByEmpno(this);" name="wfenginescrapprocess.ylno6"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno6']}"
															value="${wfenginescrapprocessEntity.ylno6}" /> <c:if
																test="${requiredMap['ylno6'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname6" name="wfenginescrapprocess.ylname6"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno6']}"
															readonly value="${wfenginescrapprocessEntity.ylname6}" />
														</td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno7Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">市場負責人</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole2(97,'ylno7Table','ylno7','ylname7',$('#dealfactoryid').combobox('getValue'),'wfenginescrapprocess',$('factorycode').val())"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno7"
															onblur="getUserNameByEmpno(this);" name="wfenginescrapprocess.ylno7"
															class="easyui-validatebox" readonly
															data-options="width: 80,required:${requiredMap['ylno7']}"
															value="${wfenginescrapprocessEntity.ylno7}" /> <c:if
																test="${requiredMap['ylno7'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname7" name="wfenginescrapprocess.ylname7"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno7']}"
															readonly value="${wfenginescrapprocessEntity.ylname7}" />
														</td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno8Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">責任單位核準主管</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRoleForScrap(98,'ylno8Table','ylno8','ylname8',$('#dealfactoryid').combobox('getValue'),'wfenginescrapprocess',$('#factorycode1').val())"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno8"
															onblur="getUserNameByEmpno(this);" name="wfenginescrapprocess.ylno8"
															class="easyui-validatebox" readonly
															data-options="width: 80,required:${requiredMap['ylno8']}"
															value="${wfenginescrapprocessEntity.ylno8}" /> <c:if
																test="${requiredMap['ylno8'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname8" name="wfenginescrapprocess.ylname8"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno8']}"
															readonly value="${wfenginescrapprocessEntity.ylname8}" />
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td style="border:none">
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno13Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">生管負責人</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole2(99,'ylno13Table','ylno13','ylname13',$('#dealfactoryid').combobox('getValue'),'wfenginescrapprocess')"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno13"
															onblur="getUserNameByEmpno(this);" name="wfenginescrapprocess.ylno13"
															class="easyui-validatebox" readonly
															data-options="width: 80,required:${requiredMap['ylno13']}"
															value="${wfenginescrapprocessEntity.ylno13}" /> <c:if
																test="${requiredMap['ylno13'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname13" name="wfenginescrapprocess.ylname13"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno13']}"
															readonly value="${wfenginescrapprocessEntity.ylname13}" />
														</td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno15Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">PQA負責人</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole2(100,'ylno15Table','ylno15','ylname15',$('#dealfactoryid').combobox('getValue'),'wfenginescrapprocess')"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno15"
															onblur="getUserNameByEmpno(this);" name="wfenginescrapprocess.ylno15"
															class="easyui-validatebox" readonly
															data-options="width: 80,required:${requiredMap['ylno15']}"
															value="${wfenginescrapprocessEntity.ylno15}" /> <c:if
																test="${requiredMap['ylno15'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname15" name="wfenginescrapprocess.ylname15"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno15']}"
															readonly value="${wfenginescrapprocessEntity.ylname15}" />
														</td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno14Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">經管負責人</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole2(101,'ylno14Table','ylno14','ylname14',$('#dealfactoryid').combobox('getValue'),'wfenginescrapprocess')"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno14"
															onblur="getUserNameByEmpno(this);" name="wfenginescrapprocess.ylno14"
															class="easyui-validatebox" readonly
															data-options="width: 80,required:${requiredMap['ylno14']}"
															value="${wfenginescrapprocessEntity.ylno14}" /> <c:if
																test="${requiredMap['ylno14'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname14" name="wfenginescrapprocess.ylname14"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno14']}"
															readonly value="${wfenginescrapprocessEntity.ylname14}" />
														</td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno18Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">中央FF倉負責人</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole2(102,'ylno18Table','ylno18','ylname18',$('#dealfactoryid').combobox('getValue'),'wfenginescrapprocess')"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno18"
															onblur="getUserNameByEmpno(this);" name="wfenginescrapprocess.ylno18"
															class="easyui-validatebox" readonly
															data-options="width: 80,required:${requiredMap['ylno18']}"
															value="${wfenginescrapprocessEntity.ylno18}" /> <c:if
																test="${requiredMap['ylno18'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname18" name="wfenginescrapprocess.ylname18"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno18']}"
															readonly value="${wfenginescrapprocessEntity.ylname18}" />
														</td>
													</tr>
												</table>
											</td>
										</tr>

									</table>
								</td>
							</tr>

							<tr>
								<td colspan="10" style="text-align:left;">
									<table class="flowList"
										style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
										<tr>
											<td>簽核時間</td>
											<td>簽核節點</td>
											<td>簽核主管</td>
											<td>簽核意見</td>
											<td>批註</td>
											<td>簽核電腦IP</td>
										</tr>
									</table>
								</td>
							</tr>
							<tr>
								<td colspan="10"
									style="border:none;text-align:center;margin-top:10px"><a
									href="javascript:;" id="btnSave" class="easyui-linkbutton"
									data-options="iconCls:'icon-add'" style="width: 100px;"
									onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp; <a
									href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
									data-options="iconCls:'icon-ok'" style="width: 100px;"
									onclick="saveInfo(2);">提交</a></td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</div>
		<input type="hidden" id="deptNo" name="deptNo" value="" /> <input
			type="hidden" id="chargeNo" name="chargeNo" value="" /> <input
			type="hidden" id="chargeName" name="chargeName" value="" /> <input
			type="hidden" id="factoryId" name="factoryId" value="" /> <input
			type="hidden" id="dutyId" name="dutyId" value="" />
			<input type="hidden" id="factoryCode" name="factoryCode" value=""/>
		<div id="win"></div>
	</form>
	<script
		src='${ctx}/static/js/shengchan/wfenginescrapprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>
