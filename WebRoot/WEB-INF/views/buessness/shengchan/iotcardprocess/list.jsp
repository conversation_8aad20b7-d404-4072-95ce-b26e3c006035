<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>物聯網卡申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input type="text" name="filter_EQS_applyno" class="easyui-validatebox"
               data-options="width:150,prompt: '申請人工號'"/>
        <input type="text" name="filter_EQS_applydepartno" class="easyui-validatebox"
               data-options="width:150,prompt: '單位代碼單位代碼'"/>
        <input type="text" name="filter_EQS_makerno" class="easyui-validatebox"
               data-options="width:150,prompt: '填單人工號'"/>
        <input type="text" name="filter_EQS_serialno" class="easyui-validatebox"
               data-options="width:150,prompt: '任務編碼'"/>
        <input id="makerfactoryid" style="width:100px" class="easyui-validatebox" data-options="width:150,prompt: '請選擇承辦人所在廠區'" name="filter_EQS_makerfactoryid"/>
        <input type="text" name="filter_GED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
               data-options="width:150,prompt: '填單开始日期'"/>
        - <input type="text" name="filter_LED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
                 data-options="width:150,prompt: '填單结束日期'"/>
        <input type="text" name="filter_GED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
               data-options="width:150,prompt: '簽核完成开始日期'"/>
        - <input type="text" name="filter_LED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
                 data-options="width:150,prompt: '簽核完成结束日期'"/>
        <input id="qysjzt" style="width:100px" class="easyui-validatebox" data-options="width:150,prompt: '請選擇簽核狀態'" name="filter_EQS_workstatus"/>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
           onclick="listSearchReset()">重置</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
           onclick="exportExcel()">导出Excel</a>
        <!--導出用，請勿刪除-->
        <input id="page" name="page" type="hidden" value="1"/>
        <input id="rows" name="rows" type="hidden" value="30"/>
    </form>

</div>
<table id="dg"></table>
<div id="dlg"></div>

<script type="text/javascript">
    //創建下拉查詢條件
    $.ajax({
        url: ctx + "/system/dict/getDictByType/audit_status",
        dataType: "json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#qysjzt").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
    $.get(ctx+'/tqhfactoryidconfig/allFactorys/',
        function (result) {
            $("#makerfactoryid").combobox({
                data: result,
                valueField: "factoryid",
                textField: "factoryname",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    return data;
                }
            });
        },'json');
    $(function () {
        dg = $('#dg').datagrid({
            method: "get",
            url: ctx + '/iotcardprocess/list',
            fit: true,
            fitColumns: false,
            //fitColumns: false,  //如需要底部出現滾動條，修改爲false
            border: false,
            idField: 'id',
            striped: true,
            cache: false,  //关闭AJAX相应的缓存
            pagination: true,
            rownumbers: true,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: true,
            frozenColumns:[[
                {field: 'serialno', title: '表單編號', sortable: true, width: 200, formatter: operation},
                {field: 'applyno', title: '申請人工號', sortable: true, width: 100},
                {field: 'applyname', title: '申請人姓名', sortable: true, width: 100},
            ]],
            columns: [[
                //{ field: 'zakchargename', title: '資安課級主管',sortable:true,width:100},
                //{ field: 'kchargeno', title: '課級主管',sortable:true,width:100},
                //{ field: 'kchargename', title: '課級主管',sortable:true,width:100},
                //{ field: 'bchargeno', title: '部級主管',sortable:true,width:100},
                //{ field: 'bchargename', title: '部級主管',sortable:true,width:100},
                //{ field: 'cchargeno', title: '廠級主管',sortable:true,width:100},
                //{ field: 'cchargename', title: '廠級主管',sortable:true,width:100},
                //{ field: 'zchargeno', title: '製造處級主管',sortable:true,width:100},
                //{ field: 'zchargename', title: '製造處級主管',sortable:true,width:100},
                //{ field: 'zcchargeno', title: '製造總處級主管',sortable:true,width:100},
                //{ field: 'zcchargename', title: '製造總處級主管',sortable:true,width:100},
                //{ field: 'jgchargeno', title: '經管主管',sortable:true,width:100},
                //{ field: 'jgchargename', title: '經管主管',sortable:true,width:100},
                //{ field: 'zxywkjzgno', title: '資訊運維課級主管',sortable:true,width:100},
                //{ field: 'zxywkjzgname', title: '資訊運維課級主管',sortable:true,width:100},
                //{ field: 'zxywcjzgno', title: '資訊運維廠區主管',sortable:true,width:100},
                //{ field: 'zxywcjzgname', title: '資訊運維廠區主管',sortable:true,width:100},
                //{ field: 'ylno14', title: '資安部級主管',sortable:true,width:100},
                //{ field: 'ylname14', title: '資安部級主管',sortable:true,width:100},
                //{ field: 'zxywzgno', title: '資訊運維主管',sortable:true,width:100},
                //{ field: 'zxywzgname', title: '資訊運維主管',sortable:true,width:100},
                //{ field: 'glyzyno', title: '管理員作業',sortable:true,width:100},
                //{ field: 'glyzyname', title: '管理員作業',sortable:true,width:100},
                {field: 'id', title: '${column.comments}', hidden: true},
                { field: 'makerno', title: '填單人工號',sortable:true,width:100},
                { field: 'makername', title: '填單人姓名',sortable:true,width:100},
                {field: 'createDate', title: '填單時間', sortable: true, width: 150},
                {field: 'workstatus', title: '任務狀態', sortable: true, width: 100},
                {field: 'nodeName', title: '當前審核節點', sortable: true, width: 150, formatter: formatProgress},
                {field: 'auditUser', title: '當前簽核人', sortable: true, width: 120},
                {field: 'complettime', title: '簽核完成時間', sortable: true, width: 150},
                { field: 'applydepartno', title: '單位代碼',sortable:true,width:100},
                {field: 'applycostno', title: '費用代碼', sortable: true, width: 100},
                {
                    field: 'viewbagResult', title: '拋轉狀態', sortable: true, width: 500,
                    formatter: function (value, rec, index) {
                        var del = "";
                        if (rec.viewbagResult != null && rec.viewbagResult != ""&&rec.viewbagResult.indexOf("Y") != -1) {
                            del += "拋轉狀態【Y】，返回信息：";
                            $.each(rec.viewbagResult.replace('拋轉狀態【Y】', '').split(","),function(index, value) {
                                // 在这里进行你想要的操作
                                if (value != '') {
                                    if (index == 0) {
                                        del +='成功，單號：'+ '<a href="https://112.efoxconn.com/Network/Sign/' + value.replace(/[^A-Z0-9]/ig, "") + '?querytype=homeview" target="_blank" class="mybutton" data-options="plain:true,iconCls:\'icon-remove\'"><span title=\"點擊跳轉中央網通查詢\">' + value.replace(/[^A-Z0-9]/ig, "") + '</span></a>';
                                    }else{
                                        del +=',成功，單號：'+ '<a href="https://112.efoxconn.com/Network/Sign/' + value.replace(/[^A-Z0-9]/ig, "") + '?querytype=homeview" target="_blank" class="mybutton" data-options="plain:true,iconCls:\'icon-remove\'"><span title=\"點擊跳轉中央網通查詢\">' + value.replace(/[^A-Z0-9]/ig, "") + '</span></a>';
                                    }
                                }
                            });
                        }else {
                            if(rec.viewbagResult!=null)
                            del = '<span title=\"' + value + '\" class=\"easyui-tooltip\">' + value + '</span>';
                        }
                        return del;
                    }
                }

                //{ field: 'complettime', title: '簽核完成時間',sortable:true,width:100},

                // {field: 'dealno', title: '承辦人工號', sortable: true, width: 100},
                //{ field: 'dealname', title: '承辦人名稱',sortable:true,width:100},
                // {field: 'dealdeptno', title: '承辦人單位代碼', sortable: true, width: 100},
                // {field: 'dealdeptname', title: '承辦人單位名稱', sortable: true, width: 100, formatter: cellTextTip},
                //{ field: 'attachids', title: '附件Id',sortable:true,width:100},
                //{ field: 'createBy', title: '創建人',sortable:true,width:100},
                //{ field: 'createDate', title: '創建時間',sortable:true,width:100},
                //{ field: 'updateBy', title: '更新者',sortable:true,width:100},
                //{ field: 'updateDate', title: '更新時間',sortable:true,width:100},
                //{ field: 'delFlag', title: '刪除標識',sortable:true,width:100},
                //{ field: 'makerdeptno', title: '填單人所在部門',sortable:true,width:100},
                //{ field: 'applytel', title: '聯繫電話',sortable:true,width:100},
                //{ field: 'applyFactoryId', title: '所在廠區',sortable:true,width:100},
                //{ field: 'applydepartno', title: '單位代碼',sortable:true,width:100},
                //{ field: 'applydepartname', title: '單位',sortable:true,width:100},
                //{ field: 'applyemail', title: '聯繫郵箱',sortable:true,width:100},
                //{ field: 'applydate', title: '申請日期',sortable:true,width:100},
                //{ field: 'applyLevel', title: '資位',sortable:true,width:100},
                //{ field: 'applyPost', title: '管理職',sortable:true,width:100},
                //{ field: 'applyArea', title: '區域',sortable:true,width:100},
                //{ field: 'applyFloor', title: '樓棟',sortable:true,width:100},
                //{ field: 'isProtect', title: '安保區域',sortable:true,width:100},
                //{ field: 'applycompanycode', title: '法人代碼',sortable:true,width:100},
                //{ field: 'applycompanyname', title: '法人名稱',sortable:true,width:100},
                //{ field: 'zxywzrrqrno', title: '資訊運維責任人確認',sortable:true,width:100},
                //{ field: 'zxywzrrqrname', title: '資訊運維責任人確認',sortable:true,width:100},
                //{ field: 'zakchargeno', title: '資安課級主管',sortable:true,width:100},
            ]],
            onLoadSuccess: function () {
                $(".easyui-tooltip").tooltip({
                    position: 'letf',
                    onShow: function () {
                        $(this).tooltip('tip').css({
                            borderColor: '#000'
                        });
                    }
                });
                //$(this).datagrid("fixRownumber");
            },
            rowStyler: rowStylerFun,
            enableHeaderClickMenu: true,
            enableHeaderContextMenu: true,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });
    });

    //任務編號查看頁面
    function operation(value, row, index) {
        return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('詳情',ctx+'/iotcardprocess/view/"
            + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
    };

    //创建查询对象并查询
    function cx() {
        var obj = $("#searchFrom").serializeObject();
        dg.datagrid('load', obj);
    }

    //表單進度查詢頁面查詢條件重置
    function listSearchReset() {
        $("#searchFrom").form("reset");
    }

    //导出excel
    function exportExcel() {
        var options = $('#dg').datagrid('getPager').data("pagination").options;
        var page = options.pageNumber;//当前页数  
        var total = options.total;
        var rows = options.pageSize;//每页的记录数（行数）  
        var form = document.getElementById("searchFrom");
        if (total == 0) {
            return
        }
        $('#page').val(page);
        $('#rows').val(total);
        var form = document.getElementById("searchFrom");
        searchFrom.action = ctx + '/iotcardprocess/exportExcel';
        form.submit();
    }
</script>
</body>
</html>
