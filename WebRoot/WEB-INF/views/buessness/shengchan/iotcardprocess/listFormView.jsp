<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>物聯網卡申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/iotcardprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${iotCardProcessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${iotCardProcessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">物聯網卡申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${iotCardProcessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${iotCardProcessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${iotCardProcessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${iotCardProcessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty iotCardProcessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty iotCardProcessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${iotCardProcessEntity.makerno}/${iotCardProcessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td colspan="10" class="td_style1">承辦人基本信息</td>
            </tr>
            <c:choose>
                <c:when test="${not empty iotCardProcessEntity.makerno}">
                    <input type="hidden" id="isNew" value="0"/>
                    <tr align="center">
                        <td width="6%">承辦人工號</td>
                        <td width="6%" class="td_style1">
                            <input id="dealno" name="dealno" class="easyui-validatebox"
                                   data-options="width: 80,required:true,disabled:true"
                                   value="${iotCardProcessEntity.dealno}" onblur="loadDealUserInfo();"/>
                            
                        </td>
                        <td width="4%">承辦人</td>
                        <td width="6%" class="td_style1">
                            <input id="dealname" name="dealname"
                                   class="easyui-validatebox inputCss"
                                   data-options="width:80,disabled:true"
                                   value="${iotCardProcessEntity.dealname}"/>
                        </td>
                        <td width="4%">單位代碼</td>
                        <td width="6%" class="td_style1">
                            <input id="makerdeptno" name="makerdeptno"
                                   class="easyui-validatebox inputCss"
                                   data-options="width:80,disabled:true"
                                   value="${iotCardProcessEntity.makerdeptno}"/>
                        </td>
                        <td width="4%">所在廠區</td>
                        <td width="7%" class="td_style1">
                            <input id="makerfactoryid" name="makerfactoryid" class="easyui-combobox"
                                   panelHeight="auto" value="${iotCardProcessEntity.applyFactoryId }"
                                   data-options="width: 120,disabled:true,required:true,validType:'comboxValidate[\'applyFactoryId\',\'请选择所在廠區\']'"/>
                        </td>
                        <td width="4%">聯繫分機</td>
                        <td width="10%" class="td_style1">
                            <input id="makertel" name="makertel" class="easyui-validatebox"
                                   style="width:90px;"
                                   value="${iotCardProcessEntity.makertel}" data-options="required:true,disabled:true"
                                   onblur="valdApplyTel(this)"/>
                            
                        </td>
                    </tr>
                    <tr align="center">
                        <td width="6%">單位</td>
                        <td colspan="5" class="td_style1">
                            <input id="makerdeptname" name="makerdeptname" class="easyui-validatebox"
                                   data-options="width: 450,required:true,disabled:true"
                                   value="${iotCardProcessEntity.makerdeptname}"/>
                            
                        </td>
                        <td width="4%">聯繫郵箱</td>
                        <td colspan="3" class="td_style1">
                            <input id="makeremail" name="makeremail" class="easyui-validatebox"
                                   data-options="width: 300,required:true,disabled:true"
                                   value="${iotCardProcessEntity.makeremail}"/>
                            
                        </td>
                    </tr>
                </c:when>
                <c:otherwise>
                    <input type="hidden" id="isNew" value="1"/>
                    <tr align="center">
                        <td width="6%">承辦人工號</td>
                        <td width="6%" class="td_style1">
                            <input id="dealno" name="dealno" class="easyui-validatebox"
                                   data-options="width: 80,required:true,disabled:true"
                                   value="${user.loginName}" onblur="loadDealUserInfo();"/>
                        </td>
                        <td width="4%">承辦人</td>
                        <td width="6%" class="td_style1">
                            <input id="dealname" name="dealname"
                                   class="easyui-validatebox inputCss"
                                   data-options="width:80,disabled:true" value="${user.name}"/>
                        </td>
                        <td width="4%">單位代碼</td>
                        <td width="6%" class="td_style1">
                            <input id="makerdeptno" name="makerdeptno"
                                   class="easyui-validatebox inputCss"
                                   data-options="width:80,disabled:true" value=""/>
                        </td>
                        <td width="4%">所在廠區</td>
                        <td width="7%" class="td_style1">
                            <input id="makerfactoryid" name="makerfactoryid" class="easyui-combobox"
                                   panelHeight="auto" value=""
                                   data-options="width: 120,disabled:true,required:true,validType:'comboxValidate[\'applyFactoryId\',\'请选择所在廠區\']'"/>
                        </td>
                        <td width="4%">聯繫分機</td>
                        <td width="10%" class="td_style1">
                            <input id="makertel" name="makertel" class="easyui-validatebox"
                                   style="width:90px;"
                                   value="${user.phone}" data-options="required:true,disabled:true"/>
                        </td>
                    </tr>
                    <tr align="center">
                        <td width="6%">單位</td>
                        <td colspan="5" class="td_style1">
                            <input id="makerdeptname" name="makerdeptname" class="easyui-validatebox"
                                   data-options="width: 450,required:true,disabled:true"
                                   value=""/>
                        </td>
                        <td width="4%">聯繫郵箱</td>
                        <td colspan="3" class="td_style1">
                            <input id="makeremail" name="makeremail" class="easyui-validatebox"
                                   data-options="width: 300,required:true,disabled:true"
                                   value="${user.email}"/>
                        </td>
                    </tr>
                </c:otherwise>
            </c:choose>
            <tr>
                <td colspan="10" class="td_style1">申請人基本信息</td>
            </tr>
            <tr align="center">
                <td width="6%">申請人工號</td>
                <td width="6%" class="td_style1">
                    <input id="applyno" name="applyno" class="easyui-validatebox"
                           data-options="width: 80,required:true,disabled:true"
                           value="${iotCardProcessEntity.applyno}" onblur="queryUserInfo(this);"/>
                </td>
                <td width="4%">申請人</td>
                <td width="6%" class="td_style1">
                    <input id="applyname" name="applyname"
                           class="easyui-validatebox inputCss"
                           data-options="width:80,disabled:true" value="${iotCardProcessEntity.applyname}"/>
                </td>
                <td width="4%">單位代碼</td>
                <td width="6%" class="td_style1">
                    <input id="applydepartno" name="applydepartno"
                           class="easyui-validatebox inputCss" data-options="width: 90,disabled:true"
                           value="${iotCardProcessEntity.applydepartno}"/>
                </td>
                <td width="4%">費用代碼</td>
                <td width="6%" class="td_style1">
                    <input id="applycostno" name="applycostno"
                           class="easyui-validatebox" data-options="width: 90,required:true,disabled:true"
                           value="${iotCardProcessEntity.applycostno}"/>
                </td>
                <td width="4%">所在廠區&nbsp;</td>
                <td width="10%" class="td_style1">
                    <input id="applyFactoryId" name="applyFactoryId" class="easyui-combobox"
                           panelHeight="auto" value="${iotCardProcessEntity.applyFactoryId }"
                           data-options="width: 120,disabled:true,required:true,onSelect:function(){onchangeFactory();},validType:'comboxValidate[\'applyFactoryId\',\'请选择所在廠區\']'"/>
                </td>
            </tr>
            <tr align="center">
                <td width="6%">資位</td>
                <td width="6%" class="td_style1">
                    <input id="applyLevel" name="applyLevel" class="easyui-validatebox"
                           style="width:90px;"
                           value="${iotCardProcessEntity.applyLevel}" data-options="required:true,disabled:true"
                    />
                </td>
                <td width="4%">管理職</td>
                <td width="6%" class="td_style1">
                    <input id="applyPost" name="applyPost" class="easyui-validatebox"
                           style="width:90px;"
                           value="${iotCardProcessEntity.applyPost}" data-options="required:true,disabled:true"
                    />
                </td>
                <td width="4%">聯繫郵箱</td>
                <td colspan="3" class="td_style1">
                    <input id="applyemail" name="applyemail" class="easyui-validatebox"
                           style="width:350px;"
                           value="${iotCardProcessEntity.applyemail}" data-options="required:true,disabled:true"
                    />
                </td>
                <td width="4%">使用區域</td>
                <td width="10%" class="td_style1">
                    <input id="applyArea" name="applyArea" class="easyui-combobox"
                           data-options="width: 90,required:true,disabled:true,onSelect:function(){onchangeArea();},validType:'comboxValidate[\'applyArea\',\'请选择區域\']'"
                           value="${iotCardProcessEntity.applyArea}" panelHeight="auto"/>&nbsp;/
                    <input id="applyFloor" name="applyFloor"
                           class="easyui-combobox"
                           data-options="width: 60,required:true,disabled:true,validType:'comboxValidate[\'applyFloor\',\'请选择樓層\']'"
                           value="${iotCardProcessEntity.applyFloor}" panelHeight="auto"/>
                </td>
            </tr>
            <tr align="center">
                <td width="6%">聯繫分機</td>
                <td width="6%" class="td_style1">
                    <input id="applytel" name="applytel" class="easyui-validatebox"
                           style="width:90px;"
                           value="${iotCardProcessEntity.applytel}" data-options="required:true,disabled:true"
                    />
                </td>
                <td width="4%">單位</td>
                <td colspan="5" class="td_style1">
                    <input id="applydepartname" name="applydepartname" class="easyui-validatebox"
                           style="width:500px;"
                           value="${iotCardProcessEntity.applydepartname}" data-options="required:true,disabled:true"/>
                </td>
                <td width="4%">安保區域</td>
                <td class="td_style2">
                    <div class="isProtectDiv"></div>
                    <input id="isProtect" name="isProtect"
                           type="hidden" class="easyui-validatebox" data-options="width: 100"
                           value="${iotCardProcessEntity.isProtect}"/>
                </td>
            </tr>
            <tr align="center">
                <td>法人代碼</td>
                <td class="td_style1">
                    <input id="applycompanycode" name="applycompanycode"
                           class="easyui-validatebox"
                           data-options="width:80,required:true,disabled:true"
                           value="${iotCardProcessEntity.applycompanycode}"/>
                </td>
                <td>法人名稱</td>
                <td class="td_style1" colspan="7">
                    <input id="applycompanyname" name="applycompanyname"
                           class="easyui-validatebox"
                           data-options="width:300,required:true,disabled:true"
                           value="${iotCardProcessEntity.applycompanyname}"/>
                </td>
            </tr>
            <tr>
                <td colspan="10" class="td_style1">申請詳細信息</td>
            </tr>
            <tr align="center">
                <td>申請數量</td>
                <td class="td_style1" colspan="2">
                    <input id="applyQuantity" name="applyQuantity"
                           class="easyui-validatebox"
                           data-options="width:80,required:true,disabled:true"
                           value="${iotCardProcessEntity.applyQuantity}"/> <span style="color: #0d3349;font-size: 12px">張</span>
                </td>
                <td>申請規格</td>
                <td class="td_style1" colspan="3">
                    <input id="applySpecifications" name="applySpecifications"
                           class="easyui-validatebox"
                           data-options="width:300,required:true,disabled:true"
                           value="${iotCardProcessEntity.applySpecifications}"/>
                </td>
                <td>申請動作</td>
                <td colspan="2">
                    <div class="applyActionDiv"></div>
                    <input id="applyAction" name="applyAction"
                           type="hidden" class="easyui-validatebox" data-options="width: 100"
                           value="${iotCardProcessEntity.applyAction}"/>
                </td>
            </tr>
            <tr>
                <td colspan="10" class="td_style1">使用設備信息</td>
            </tr>
            <tr align="center">
                <td colspan="10" width="100%">
                    <div style="overflow-x: auto;width: 1200px;">
                        <table id="IotCardItemsTable" width="100%">
                            <tr align="center">
                                <td width="50px;">序號</td>
                                <td width="200px;">SN</td>
                                <td width="200px;">MAC</td>
                                <td width="100px;">綁定人員工號</td>
                                <td width="100px;" id="qxlb">綁定人員姓名</td>
                                <td width="100px;">使用地點（精確至樓層）</td>
                                <td width="200px;">備註</td>
                                <td width="50px;">&nbsp;APN卡CCID&nbsp;</td>
                                <tbody id="info_Body">
                                <c:if test="${iotCardProcessEntity.itemsEntity!=null&&iotCardProcessEntity.itemsEntity.size()>0}">
                                <c:forEach items="${iotCardProcessEntity.itemsEntity}"
                                           var="itemsEntity"
                                           varStatus="status">
                            <tr align="center" id="itemsEntity${status.index}">
                                <td>${status.index+1}</td>
                                <td>
                                    <input id="equipmentSn${status.index}"
                                           onblur="queryEquipmentInformation(this,'${status.index}');"
                                           name="itemsEntity[${status.index}].equipmentSn"
                                           class="easyui-validatebox" style="width:150px;"
                                           data-options="required:true,disabled:true"
                                           value="${itemsEntity.equipmentSn}"/>
                                </td>
                                <td>
                                    <input id="equipmentMac${status.index}"
                                           name="itemsEntity[${status.index}].equipmentMac"
                                           class="easyui-validatebox" style="width:150px;"
                                           data-options="required:true,disabled:true"
                                           value="${itemsEntity.equipmentMac}"/>
                                </td>
                                <td>
                                    <input id="bondedNumber${status.index}"
                                           name="itemsEntity[${status.index}].bondedNumber"
                                        <%--                                                               data-options="panelHeight:300,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'userfactoryid${status.index}\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(${status.index});},onSelect:function(){pcOnchangeFactory('userfactoryid${status.index}',${status.index});}"--%>
                                           style="width:100px;" class="easyui-validatebox"
                                           data-options="required:true,disabled:true"
                                           value="${itemsEntity.bondedNumber}"/>
                                </td>
                                <td>
                                    <input id="bondedName${status.index}"
                                           name="itemsEntity[${status.index}].bondedName"
                                           class="easyui-validatebox" style="width:150px"
                                           value="${itemsEntity.bondedName}"
                                           data-options="required:true,disabled:true"/>
                                </td>
                                <td>
                                    <input id="location${status.index}"
                                           name="itemsEntity[${status.index}].location"
                                           class="easyui-validatebox" style="width:150px"
                                           value="${itemsEntity.location}"
                                           data-options="required:true,disabled:true"/>
                                </td>
                                <td>
                                    <input id="remark${status.index}"
                                           name="itemsEntity[${status.index}].remark"
                                           class="easyui-validatebox" style="width:150px" data-options="disabled:true"
                                           value="${itemsEntity.remark}"/>
                                </td>
                                <td>
                                    <input id="apnCcid${status.index}" data-options="required:true,disabled:true"
                                           name="itemsEntity[${status.index}].apnCcid"
                                           class="easyui-validatebox" style="width:150px"
                                           value="${itemsEntity.apnCcid}"/>
                                </td>
                            </tr>
                            </c:forEach>
                            </c:if>
                            <c:if test="${iotCardProcessEntity.itemsEntity.size()==0 || iotCardProcessEntity.itemsEntity==null}">
                                <tr align="center" id="itemsEntity0">
                                    <td>1</td>
                                    <td>
                                        <input id="equipmentSn0" onblur="queryEquipmentInformation(this,'0');"
                                               name="itemsEntity[0].equipmentSn"
                                               class="easyui-validatebox" style="width:80px;"
                                               data-options="required:true,disabled:true"
                                               value="${itemsEntity.equipmentSn}"/>
                                    </td>
                                    <td>
                                        <input id="equipmentMac0"
                                               name="itemsEntity[0].equipmentMac"
                                               class="easyui-validatebox" style="width:80px;"
                                               data-options="required:true,disabled:true
                                               value="${itemsEntity.equipmentMac}"/>
                                    </td>
                                    <td>
                                        <input id="bondedNumber0"
                                               name="itemsEntity[0].bondedNumber"
                                            <%--                                                               data-options="panelHeight:300,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'userfactoryid${status.index}\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(${status.index});},onSelect:function(){pcOnchangeFactory('userfactoryid${status.index}',${status.index});}"--%>
                                               style="width:100px;" class="easyui-validatebox"
                                               data-options="required:true,disabled:true"
                                               value="${itemsEntity.bondedNumber}"/>
                                    </td>
                                    <td>
                                        <input id="bondedName0"
                                               name="itemsEntity[0].bondedName"
                                               class="easyui-validatebox" style="width:150px"
                                               value="${itemsEntity.bondedName}"
                                               data-options="required:true,disabled:true"/>
                                    </td>
                                    <td>
                                        <input id="location0"
                                               name="itemsEntity[0].location"
                                               class="easyui-validatebox" style="width:150px"
                                               value="${itemsEntity.location}"
                                               data-options="required:true,disabled:true"/>
                                    </td>
                                    <td>
                                        <input id="remark0" data-options="disabled:true"
                                               name="itemsEntity[0].remark"
                                               class="easyui-validatebox" style="width:150px"
                                               value="${itemsEntity.remark}"/>
                                    </td>
                                    <td>
                                        <input id="apnCcid0" data-options="required:true,disabled:true"
                                               name="itemsEntity[0].apnCcid"
                                               class="easyui-validatebox" style="width:150px"
                                               value="${itemsEntity.apnCcid}"/>
                                    </td>
                                </tr>
                            </c:if>
                            </tbody>
                            <tr align="left" class="nottr">
                                <td colspan="10" width="100%"
                                    style="text-align:left;padding-left:10px;">
                                    <input type="button" id="pcItemAdd" style="width:100px;" disabled
                                           value="添加一筆"/>&nbsp;
                                    <input type="button" style="width:100px;" disabled
                                           onclick="openBatchImportWin();"
                                           value="批量導入"/>&nbsp;
                                    <a href="${ctx}/ossAdmin/download/f336a5d60c074be680ec3e08e63fa5ac"
                                       id="btnBatchImportTpl">模板下載</a>
                                </td>
                            </tr>
                        </table>
                    </div>
                </td>
            </tr>
            <tr align="center">
                <td width="10%">附件</td>
                <td colspan="9" class="td_style1">
                    <input type="hidden" id="attachids"
                           name="attachids" value="${iotCardProcessEntity.attachids }"/>
                    <div id="dowloadUrl">
                        <c:forEach items="${file}" varStatus="i" var="item">
                            <div id="${item.id}"
                                 style="line-height:30px;margin-left:5px;" class="float_L">
                                <div class="float_L">
                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                </div>
                            </div>
                        </c:forEach>
                    </div>
                </td>
            </tr>
            <tr align="center">
                <td>需求說明</td>
                <td class="td_style1" colspan="9">
                                <textarea id="requireDetail" name="requireDetail"
                                          oninput="return LessThanAuto(this,'txtNum');"
                                          onchange="return LessThanAuto(this,'txtNum');"
                                          onpropertychange="return LessThanAuto(this,'txtNum');"
                                          maxlength="300" class="easyui-validatebox" style="width:85%;height:80px;"
                                          data-options="required:true,disabled:true"
                                          rows="5" cols="6">${iotCardProcessEntity.requireDetail}</textarea><span
                        id="txtNum"></span>
                </td>
            </tr>
            <tr align="center" style="height: 70px">
                <td>備註/要求</td>
                <td align="left" colspan="9">
                    1、本單適用於申請訪問六流應用特殊管制網段之物聯網卡使用；<br/>
                    2、物聯網卡費用結算默認至申請人所在單位費用代碼下。<br/>
                </td>
            </tr>
            <tr>
                <td align="left" colspan="10" style="color: red">
                    &nbsp;&nbsp;&nbsp;溫馨提示：如果您在填單過程中有任何疑問，請聯繫物聯網卡管理作業人員579+30655
                </td>
            </tr>
            <tr>
                <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                    <a href="javascript:void(0)"
                       onclick="showWfImag('${processId}','物聯網卡申請單');">點擊查看簽核流程圖</a>
                </th>
            </tr>
            <tr>
                <td colspan="10" style="text-align:left;">
                    ${chargeNodeInfo}
                </td>
            </tr>

            <tr>
                <td colspan="10" style="text-align:left;">
                    <iframe id="qianheLogFrame" name="qianheLogFrame"
                            src="${ctx}/wfcontroller/goChargeLog?serialNo=${iotCardProcessEntity.serialno}"
                            width="100%"></iframe>
                </td>
            </tr>
            <tr class="no-print">
                <td colspan="10" style="text-align:center;padding-left:10px;">
                    <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                       data-options="iconCls:'icon-cancel'"
                       style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                    <c:if test="${iotCardProcessEntity.workstatus!=null&&iotCardProcessEntity.workstatus==3}">
                        <a href="#" id="btnPrint" class="easyui-linkbutton"
                           data-options="iconCls:'icon-print'"
                           style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                    </c:if>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/shengchan/iotcardprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>