<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>品質嫌疑品物料解鎖需求申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/tqhmaterialunlocked/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhMaterialUnlockedEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${tQhMaterialUnlockedEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${tQhMaterialUnlockedEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${tQhMaterialUnlockedEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${tQhMaterialUnlockedEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${tQhMaterialUnlockedEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">品質嫌疑品物料解鎖需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${tQhMaterialUnlockedEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${tQhMaterialUnlockedEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${tQhMaterialUnlockedEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${tQhMaterialUnlockedEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty tQhMaterialUnlockedEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty tQhMaterialUnlockedEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${tQhMaterialUnlockedEntity.makerno}/${tQhMaterialUnlockedEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="12%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox" data-options="width: 80,required:true"
                                       value="${tQhMaterialUnlockedEntity.applyno}" onchange="queryUserInfo();"/>
                            </td>
                            <td width="8%">申請人</td>
                            <td width="12%" class="td_style1">
                                <input id="applyname" name="applyname" class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${tQhMaterialUnlockedEntity.applyname }"/>
                            </td>
                            <td width="8%">單位代碼</td>
                            <td width="12%" class="td_style1">
                                <input id="applydeptno" name="applydeptno" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${tQhMaterialUnlockedEntity.applydeptno }" readonly/>
                            </td>
                            <td width="8%">聯繫方式&nbsp;<font color="red">*</font></td>
                            <td width="12%" class="td_style1">
                                <input id="applytel" name="applytel" class="easyui-validatebox" data-options="width:100,required:true"
                                       value="${tQhMaterialUnlockedEntity.applytel }"/>
                            </td>
                            <td width="8%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="12%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox" data-options="width:100,required:true"
                                       value="${tQhMaterialUnlockedEntity.applyfactoryid }"/>
                                <input id="applynofactoryid" name="applynofactoryid"
                                       value="${tQhMaterialUnlockedEntity.applynofactoryid}" type="hidden"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox" data-options="width:400,required:true"
                                       value="${tQhMaterialUnlockedEntity.applydeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"data-options="width: 250,required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"
                                       value="${tQhMaterialUnlockedEntity.applyemail }"/>
                            </td>
                        </tr>
                    </table>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">機種&nbsp;<font color="red">*</font></td>
                            <td width="12%" class="td_style1">
                                <input id="machinetype" name="machinetype" class="easyui-combobox"
                                       data-options="width:100,required:true,validType:'comboxValidate[\'machinetype\',\'请選擇機種\']'"
                                       value="${tQhMaterialUnlockedEntity.machinetype}"/>
                            </td>
                            <td width="8%">顏色</td>
                            <td width="12%" class="td_style1">
                                <input id="color" name="color" class="easyui-combobox"
                                       data-options="width:150,prompt:'選擇顏色,可多選',required:true,validType:'comboxValidate[\'color\',\'请選擇顏色\']'"
                                       value="${tQhMaterialUnlockedEntity.color}"/>
                            </td>
                            <td width="8%">階段</td>
                            <td width="12%" class="td_style1">
                                <input id="stage" name="stage" class="easyui-combobox"
                                       data-options="width:150,prompt:'選擇階段,可多選',required:true,validType:'comboxValidate[\'stage\',\'请選擇階段\']'"
                                       value="${tQhMaterialUnlockedEntity.stage}"/>
                            </td>
                            <td width="8%">製程&nbsp;<font color="red">*</font></td>
                            <td width="12%" class="td_style1">
                                <%--<input id="processprocedure" name="processprocedure" class="easyui-combobox"
                                       data-options="width:100,required:true,validType:'comboxValidate[\'processprocedure\',\'请選擇製程\']'"
                                       value="${tQhMaterialUnlockedEntity.processprocedure}"/>--%>
                                <input id="processprocedure" name="processprocedure" class="easyui-validatebox"
                                       data-options="width:100,required:true"
                                       value="${tQhMaterialUnlockedEntity.processprocedure}"/>
                            </td>
                            <td width="8%">樓層</td>
                            <td width="12%" class="td_style1">
                                <input id="floor" name="floor" class="easyui-validatebox"  data-options="width:100" value="${tQhMaterialUnlockedEntity.floor}"/></td>
                        </tr>
                        <tr align="center">
                            <td>物料形態&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="materialform" name="materialform" class="easyui-combobox"
                                       data-options="width:100,required:true,validType:'comboxValidate[\'materialform\',\'请選擇物料形態\']'"
                                       value="${tQhMaterialUnlockedEntity.materialform}"/>
                            </td>
                            <td>品質類別&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="qualitycategory" name="qualitycategory" class="easyui-combobox"
                                       data-options="width:400,required:true,validType:'comboxValidate[\'qualitycategory\',\'请選擇品質類別\']',onChange:function(){queryXing2();}" value="${tQhMaterialUnlockedEntity.qualitycategory}"/>
                            </td>
                            <td>解鎖數量</td>
                            <td class="td_style1">
                                <input id="unlockednum" name="unlockednum" class="easyui-validatebox"  data-options="width:100" value="${tQhMaterialUnlockedEntity.unlockednum}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>出貨地</td>
                            <td class="td_style1" colspan="3">
                                <input id="placeofshipment" name="placeofshipment" class="easyui-validatebox"  data-options="width:300" value="${tQhMaterialUnlockedEntity.placeofshipment}"/></td>
                            </td>
                            <td>出貨日期</td>
                            <td class="td_style1">
                                <input id="shipmentdate" name="shipmentdate" class="Wdate"
                                       style="width:100px"  value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${tQhMaterialUnlockedEntity.shipmentdate}"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                            </td>
                            <td>是否CR</td>
                            <td class="td_style2" colspan="3">
                                <div class="whethercrDiv"></div>
                                <input id="whethercr" name="whethercr" type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${tQhMaterialUnlockedEntity.whethercr}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>鎖料提示&nbsp;<font color="red">*</font></td>
                            <td class="td_style1" colspan="9">
                                <textarea style="width:99%;" rows="5" cols="6" id="unlockedprompt" name="unlockedprompt"
                                          oninput="return LessThanAuto(this,'txtNum');"
                                          onchange="return LessThanAuto(this,'txtNum');"
                                          onpropertychange="return LessThanAuto(this,'txtNum');"
                                          maxlength="100" class="easyui-validatebox" data-options="required:true">${tQhMaterialUnlockedEntity.unlockedprompt}</textarea><span id="txtNum"></span></td>
                        </tr>
                        <tr align="center">
                            <td>緊急程度&nbsp;<font color="red">*</font></td>
                            <td class="td_style2" colspan="5">
                                <div class="urgentdegreeDiv"></div>
                                <input id="urgentdegree" name="urgentdegree"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${tQhMaterialUnlockedEntity.urgentdegree }"/>
                            </td>
                            <td>剩餘解鎖數量</td>
                            <td class="td_style2">
                                <input id="surplusunlockednum" name="surplusunlockednum" class="easyui-validatebox"  data-options="width:100" value="${tQhMaterialUnlockedEntity.surplusunlockednum}"/>
                            </td>
                            <td>期望完成日期</td>
                            <td class="td_style2">
                                <input id="expectcompletedate" name="expectcompletedate" class="Wdate"
                                       style="width:100px"  value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${tQhMaterialUnlockedEntity.expectcompletedate}"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                            </td>
                        </tr>
                        <tr align="center">
                            <td>作業系統&nbsp;<font color="red">*</font></td>
                            <td class="td_style2" colspan="9">
                                <div class="operatesystemDiv"></div>
                                <input id="operatesystem" name="operatesystem"
                                       type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${tQhMaterialUnlockedEntity.operatesystem }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>原因說明&nbsp;<font color="red">*</font></td>
                            <td class="td_style1" colspan="9">
                                <textarea style="width:99%;" rows="7" cols="6" id="reason" name="reason"
                                          oninput="return LessThanAuto(this,'txtNum1');"
                                          onchange="return LessThanAuto(this,'txtNum1');"
                                          onpropertychange="return LessThanAuto(this,'txtNum1');" maxlength="300" class="easyui-validatebox">${tQhMaterialUnlockedEntity.reason}</textarea><span id="txtNum1"></span></td>
                        </tr>
                        <tr align="center">
                            <td>附件&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <c:choose>
                                    <c:when test="${file.size()>0}">
                                        <input type="hidden" id="attachids" name="attachids"
                                               value="${tQhMaterialUnlockedEntity.attachids }"/>
                                    </c:when>
                                    <c:otherwise>
                                        <input type="hidden" id="attachids" name="attachids" value=""/>
                                    </c:otherwise>
                                </c:choose>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','品質嫌疑品物料解鎖需求申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                        <td colspan="10" style="text-align:left;">
                            <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                <tr>
                                    <td style="border:none">
                                        <table width="23%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                            <tr>
                                                <td>
                                                    <table width="100%">
                                                        <tr>
                                                            <td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
                                                            <td style="border: none;">
                                                                <div class="float_L qhUserIcon"
                                                                     onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#applynofactoryid').val())"></div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><input id="kchargeno" name="kchargeno"
                                                           class="easyui-validatebox"
                                                           data-options="width:80,required:${requiredMap['kchargeno']}"
                                                           readonly
                                                           value="${tQhMaterialUnlockedEntity.kchargeno }"/><c:if
                                                        test="${requiredMap['kchargeno'].equals('true')}"><font
                                                        color="red">*</font></c:if>
                                                    /<input id="kchargename" name="kchargename"
                                                            readonly class="easyui-validatebox"
                                                            data-options="width:80,required:${requiredMap['kchargeno']}"
                                                            value="${tQhMaterialUnlockedEntity.kchargename }"/>
                                                </td>
                                            </tr>
                                        </table>
                                        <table width="23%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                            <tr>
                                                <td>
                                                    <table width="100%">
                                                        <tr>
                                                            <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                            <td style="border: none;">
                                                                <div class="float_L qhUserIcon"
                                                                     onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#applynofactoryid').val())"></div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><input id="bchargeno" name="bchargeno"
                                                           class="easyui-validatebox"
                                                           data-options="width:80,required:${requiredMap['bchargeno']}"
                                                           readonly
                                                           value="${tQhMaterialUnlockedEntity.bchargeno }"/><c:if
                                                        test="${requiredMap['bchargeno'].equals('true')}"><font
                                                        color="red">*</font></c:if>
                                                    /<input id="bchargename" name="bchargename"
                                                            readonly class="easyui-validatebox"
                                                            data-options="width:80,required:${requiredMap['bchargeno']}"
                                                            value="${tQhMaterialUnlockedEntity.bchargename }"/>
                                                </td>
                                            </tr>
                                        </table>
                                        <table width="23%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                            <tr>
                                                <td>
                                                    <table width="100%">
                                                        <tr>
                                                            <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                            <td style="border: none;">
                                                                <div class="float_L qhUserIcon"
                                                                     onclick="selectRole($('#applydeptno').val(),'cchargeno','cchargename',$('#applynofactoryid').val())"></div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><input id="cchargeno" name="cchargeno"
                                                           class="easyui-validatebox"
                                                           data-options="width:80,required:${requiredMap['cchargeno']}"
                                                           readonly
                                                           value="${tQhMaterialUnlockedEntity.cchargeno }"/><c:if
                                                        test="${requiredMap['cchargeno'].equals('true')}"><font
                                                        color="red">*</font></c:if>
                                                    /<input id="cchargename" name="cchargename"
                                                            readonly class="easyui-validatebox"
                                                            data-options="width:80,required:${requiredMap['cchargeno']}"
                                                            value="${tQhMaterialUnlockedEntity.cchargename }"/>
                                                </td>
                                            </tr>
                                        </table>
                                        <table width="23%" style="float: left;margin-left: 5px;" id="hqchargeTable">
                                            <tr>
                                                <td>
                                                    <table width="100%">
                                                        <tr>
                                                            <td style="border: none;text-align: center;">${requiredMap['hqchargeno_name']}<a
                                                                    href="javascript:void(0);"
                                                                    onclick="addRowComm('hqchargeno');">添加一位</a></td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><input id="hqchargeno" name="hqchargeno"
                                                           class="easyui-validatebox"
                                                           onblur="getUserNameByEmpnoComm(this)"
                                                           data-options="width:80,required:${requiredMap['hqchargeno']}"
                                                           value="${tQhMaterialUnlockedEntity.hqchargeno }"/><c:if
                                                        test="${requiredMap['hqchargeno'].equals('true')}"><font
                                                        color="red">*</font></c:if>
                                                    /<input id="hqchargename" name="hqchargename"
                                                            readonly class="easyui-validatebox"
                                                            data-options="width:80,required:${requiredMap['hqchargeno']}"
                                                            value="${tQhMaterialUnlockedEntity.hqchargename }"/>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="border:none">
                                        <table width="23%" style="float: left;margin-left: 5px;" id="gcchargeTable">
                                            <tr>
                                                <td>
                                                    <table width="100%">
                                                        <tr>
                                                            <td style="border: none;text-align: right;">${requiredMap['gcchargeno_name']}</td>
                                                            <td style="border: none;">
                                                                <div class="float_L qhUserIcon"
                                                                     onclick="selectRole2(644,'gcchargeTable','gcchargeno','gcchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><input id="gcchargeno" name="gcchargeno" class="easyui-validatebox" data-options="width:80,required:${requiredMap['gcchargeno']}"
                                                           readonly value="${tQhMaterialUnlockedEntity.gcchargeno }"/>
                                                    <c:if test="${requiredMap['gcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                    /<input id="gcchargename" name="gcchargename"
                                                            readonly class="easyui-validatebox"
                                                            data-options="width:80,required:${requiredMap['gcchargeno']}"
                                                            value="${tQhMaterialUnlockedEntity.gcchargename }"/>
                                                </td>
                                            </tr>
                                        </table>
                                        <table width="23%" style="float: left;margin-left: 5px;" id="pqechargeTable">
                                            <tr>
                                                <td>
                                                    <table width="100%">
                                                        <tr>
                                                            <td style="border: none;text-align: right;">${requiredMap['pqechargeno_name']}</td>
                                                            <td style="border: none;">
                                                                <div class="float_L qhUserIcon"
                                                                     onclick="selectRole2(645,'pqechargeTable','pqechargeno','pqechargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><input id="pqechargeno" name="pqechargeno" class="easyui-validatebox" data-options="width:80,required:${requiredMap['pqechargeno']}"
                                                           readonly value="${tQhMaterialUnlockedEntity.pqechargeno }"/>
                                                    <c:if test="${requiredMap['pqechargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                    /<input id="pqechargename" name="pqechargename"
                                                            readonly class="easyui-validatebox"
                                                            data-options="width:80,required:${requiredMap['pqechargeno']}"
                                                            value="${tQhMaterialUnlockedEntity.pqechargename }"/>
                                                </td>
                                            </tr>
                                        </table>
                                        <table width="23%" style="float: left;margin-left: 5px;" id="oqcchargeTable">
                                            <tr>
                                                <td>
                                                    <table width="100%">
                                                        <tr>
                                                            <td style="border: none;text-align: right;">${requiredMap['oqcchargeno_name']}</td>
                                                            <td style="border: none;">
                                                                <div class="float_L qhUserIcon"
                                                                     onclick="selectRole2(211,'oqcchargeTable','oqcchargeno','oqcchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><input id="oqcchargeno" name="oqcchargeno"
                                                           class="easyui-validatebox"
                                                           data-options="width:80,required:${requiredMap['oqcchargeno']}"
                                                           readonly
                                                           value="${tQhMaterialUnlockedEntity.oqcchargeno }"/><c:if
                                                        test="${requiredMap['oqcchargeno'].equals('true')}"><font
                                                        color="red">*</font></c:if>
                                                    /<input id="oqcchargename" name="oqcchargename"
                                                            readonly class="easyui-validatebox"
                                                            data-options="width:80,required:${requiredMap['oqcchargeno']}"
                                                            value="${tQhMaterialUnlockedEntity.oqcchargename }"/>
                                                </td>
                                            </tr>
                                        </table>
                                        <table width="23%" style="float: left;margin-left: 5px;" id="faechargeTable">
                                            <tr>
                                                <td>
                                                    <table width="100%">
                                                        <tr>
                                                            <td style="border: none;text-align: right;">${requiredMap['faechargeno_name']}</td>
                                                            <td style="border: none;">
                                                                <div class="float_L qhUserIcon"
                                                                     onclick="selectRole2(646,'faechargeTable','faechargeno','faechargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><input id="faechargeno" name="faechargeno" class="easyui-validatebox" data-options="width:80,required:${requiredMap['faechargeno']}"
                                                           readonly value="${tQhMaterialUnlockedEntity.faechargeno }"/>
                                                    <c:if test="${requiredMap['faechargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                    <span id ="faecha"></span>
                                                    /<input id="faechargename" name="faechargename"
                                                            readonly class="easyui-validatebox"
                                                            data-options="width:80,required:${requiredMap['faechargeno']}"
                                                            value="${tQhMaterialUnlockedEntity.faechargename }"/>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="border:none">
                                        <table width="23%" style="float: left;margin-left: 5px;" id="qsunlockchargeTable">
                                            <tr>
                                                <td>
                                                    <table width="100%">
                                                        <tr>
                                                            <td style="border: none;text-align: right;">${requiredMap['qsunlockchargeno_name']}</td>
                                                            <td style="border: none;">
                                                                <div class="float_L qhUserIcon"
                                                                     onclick="selectRole2(212,'qsunlockchargeTable','qsunlockchargeno','qsunlockchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><input id="qsunlockchargeno" name="qsunlockchargeno"
                                                           class="easyui-validatebox"
                                                           data-options="width:80,required:${requiredMap['qsunlockchargeno']}"
                                                           readonly
                                                           value="${tQhMaterialUnlockedEntity.qsunlockchargeno }"/><c:if
                                                        test="${requiredMap['qsunlockchargeno'].equals('true')}"><font
                                                        color="red">*</font></c:if>
                                                    /<input id="qsunlockchargename" name="qsunlockchargename"
                                                            readonly class="easyui-validatebox"
                                                            data-options="width:80,required:${requiredMap['qsunlockchargeno']}"
                                                            value="${tQhMaterialUnlockedEntity.qsunlockchargename }"/>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhMaterialUnlockedEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="canelTask('${tQhMaterialUnlockedEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
    <div id="dlg"></div>
</form>
</div>
<script src='${ctx}/static/js/shengchan/tqhmaterialunlocked.js?random=<%= Math.random()%>'></script>
</body>
</html>