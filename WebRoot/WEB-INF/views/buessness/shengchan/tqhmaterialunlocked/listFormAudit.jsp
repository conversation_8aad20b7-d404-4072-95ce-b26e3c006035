<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>品質嫌疑品物料解鎖需求申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/tqhmaterialunlocked/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhMaterialUnlockedEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${tQhMaterialUnlockedEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">品質嫌疑品物料解鎖需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${tQhMaterialUnlockedEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${tQhMaterialUnlockedEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${tQhMaterialUnlockedEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${tQhMaterialUnlockedEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${tQhMaterialUnlockedEntity.makerno}/${tQhMaterialUnlockedEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">申請人工號</td>
                            <td width="12%" class="td_style2">${tQhMaterialUnlockedEntity.applyno}</td>
                            <td width="8%">申請人</td>
                            <td width="12%" class="td_style2">${tQhMaterialUnlockedEntity.applyname }</td>
                            <td width="8%">單位代碼</td>
                            <td width="12%" class="td_style2">${tQhMaterialUnlockedEntity.applydeptno }</td>
                            <td width="8%">聯繫方式</td>
                            <td width="12%" class="td_style2">${tQhMaterialUnlockedEntity.applytel }</td>
                            <td width="8%">所在廠區</td>
                            <td width="12%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox" data-options="width:100,required:true" disabled
                                       value="${tQhMaterialUnlockedEntity.applyfactoryid }"/>
                                <input id="applynofactoryid" name="applynofactoryid"
                                       value="${tQhMaterialUnlockedEntity.applynofactoryid}" type="hidden"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱</td>
                            <td colspan="5" class="td_style2">${tQhMaterialUnlockedEntity.applydeptname }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${tQhMaterialUnlockedEntity.applyemail }</td>
                        </tr>
                    </table>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">機種</td>
                            <td width="12%" class="td_style1">
                                <input id="machinetype" name="machinetype" class="easyui-combobox"
                                       data-options="width:100" disabled
                                       value="${tQhMaterialUnlockedEntity.machinetype}"/>
                            </td>
                            <td width="8%">顏色</td>
                            <td width="12%" class="td_style1">
                                <input id="color" name="color" class="easyui-combobox" disabled
                                       data-options="width:150,prompt:'選擇顏色,可多選',required:true,validType:'comboxValidate[\'color\',\'请選擇顏色\']'"
                                       value="${tQhMaterialUnlockedEntity.color}"/>
                            </td>
                            <td width="8%">階段</td>
                            <td width="12%" class="td_style1">
                                <input id="stage" name="stage" class="easyui-combobox" disabled
                                       data-options="width:150,prompt:'選擇階段,可多選',required:true,validType:'comboxValidate[\'machinetype\',\'请選擇階段\']'"
                                       value="${tQhMaterialUnlockedEntity.stage}"/>
                            </td>
                            <td width="8%">製程</td>
                            <td width="12%" class="td_style2">
                                <%--<input id="processprocedure" name="processprocedure" class="easyui-combobox"
                                       data-options="width:100" disabled
                                       value="${tQhMaterialUnlockedEntity.processprocedure}"/>--%>
                                ${tQhMaterialUnlockedEntity.processprocedure}
                            </td>
                            <td width="8%">樓層</td>
                            <td width="12%" class="td_style2">${tQhMaterialUnlockedEntity.floor}</td>
                        </tr>
                        <tr align="center">
                            <td>物料形態</td>
                            <td class="td_style1">
                                <input id="materialform" name="materialform" class="easyui-combobox"
                                       data-options="width:100" disabled
                                       value="${tQhMaterialUnlockedEntity.materialform}"/>
                            </td>
                            <td>品質類別</td>
                            <td colspan="5" class="td_style1">
                                <input id="qualitycategory" name="qualitycategory" class="easyui-combobox"
                                       data-options="width:400" disabled value="${tQhMaterialUnlockedEntity.qualitycategory}"/>
                            </td>
                            <td>解鎖數量</td>
                            <td class="td_style2">${tQhMaterialUnlockedEntity.unlockednum}</td>
                        </tr>
                        <tr align="center">
                            <td>出貨地</td>
                            <td class="td_style1" colspan="3">
                                <input id="placeofshipment" name="placeofshipment" class="easyui-validatebox" disabled  data-options="width:300" value="${tQhMaterialUnlockedEntity.placeofshipment}"/></td>
                            </td>
                            <td>出貨日期</td>
                            <td class="td_style1">
                                <input id="shipmentdate" name="shipmentdate" class="Wdate" disabled
                                       style="width:100px"  value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${tQhMaterialUnlockedEntity.shipmentdate}"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                            </td>
                            <td>是否CR</td>
                            <td class="td_style2" colspan="3">
                                <div class="whethercrDiv"></div>
                                <input id="whethercr" name="whethercr" type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${tQhMaterialUnlockedEntity.whethercr}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>鎖料提示</td>
                            <td class="td_style1" colspan="9">
                                <textarea style="width:99%;" rows="5" cols="6" id="unlockedprompt" name="unlockedprompt"
                                          class="easyui-validatebox" readonly>${tQhMaterialUnlockedEntity.unlockedprompt}</textarea></td>
                        </tr>
                        <tr align="center">
                            <td>緊急程度</td>
                            <td class="td_style2" colspan="5">
                                <div class="urgentdegreeDiv"></div>
                                <input id="urgentdegree" name="urgentdegree"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${tQhMaterialUnlockedEntity.urgentdegree }"/>
                            </td>
                            <td>剩餘解鎖數量</td>
                            <td class="td_style2">
                                ${tQhMaterialUnlockedEntity.surplusunlockednum}
                            </td>
                            <td>期望完成日期</td>
                            <td class="td_style2">
                                <input id="expectcompletedate" name="expectcompletedate" class="Wdate" disabled
                                       style="width:100px"  value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${tQhMaterialUnlockedEntity.expectcompletedate}"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                            </td>
                        </tr>
                        <tr align="center">
                            <td>作業系統</td>
                            <td class="td_style2" colspan="9">
                                <div class="operatesystemDiv"></div>
                                <input id="operatesystem" name="operatesystem"
                                       type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${tQhMaterialUnlockedEntity.operatesystem }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>原因說明</td>
                            <td class="td_style1" colspan="9">
                                <textarea style="width:99%;" rows="7" cols="6" id="reason" name="reason" readonly
                                          class="easyui-validatebox">${tQhMaterialUnlockedEntity.reason}</textarea></td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${tQhMaterialUnlockedEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>批註</td>
                            <td colspan="9" class="td_style1">
						        <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                          style="width:1000px;height:60px;"
                                          rows="4" cols="4"></textarea>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">

                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${tQhMaterialUnlockedEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','品質嫌疑品物料解鎖需求申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhMaterialUnlockedEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/shengchan/tqhmaterialunlocked.js?random=<%= Math.random()%>'></script>
</body>
</html>