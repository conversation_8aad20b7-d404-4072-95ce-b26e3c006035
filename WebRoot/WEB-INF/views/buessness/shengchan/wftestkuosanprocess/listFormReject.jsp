<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>量試樣品擴散單</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
	type="text/javascript"></script>
</head>
<body>
	<form id="mainform" action="${ctx}/wftestkuosanprocess/${action}"
		method="post">
		<input id="ids" name="ids" type="hidden"
			value="${wftestkuosanprocessEntity.id }" /> <input id="serialno"
			name="serialno" type="hidden"
			value="${wftestkuosanprocessEntity.serialno }" /> <input
			id="makerno" name="makerno" type="hidden"
			value="${wftestkuosanprocessEntity.makerno }" /> <input
			id="makername" name="makername" type="hidden"
			value="${wftestkuosanprocessEntity.makername }" /> <input
			id="makerdeptno" name="makerdeptno" type="hidden"
			value="${wftestkuosanprocessEntity.makerdeptno }" />
		<div class="commonW">
			<div class="headTitle">量試樣品擴散單</div>
			<div class="position_L">
				任務編碼：<span style="color:#999;"> <c:choose>
						<c:when test="${wftestkuosanprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
						<c:otherwise>
                           ${wftestkuosanprocessEntity.serialno}
                       </c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_L1 margin_L">
				填單時間：<span style="color:#999;"> <c:choose>
						<c:when test="${wftestkuosanprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
						<c:otherwise>
							<input style="border:0px" style="width: 120px" readonly="true"
								value="<fmt:formatDate value='${wftestkuosanprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>" />
						</c:otherwise>
					</c:choose>
				</span>
			</div>
			<c:if test="${empty wftestkuosanprocessEntity.makerno}">
				<div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
			</c:if>
			<c:if test="${not empty wftestkuosanprocessEntity.makerno}">
				<div class="position_R margin_R">填單人：${wftestkuosanprocessEntity.makerno}/${wftestkuosanprocessEntity.makername}</div>
			</c:if>
			<div class="clear"></div>
			<table class="formList">
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="2">量試樣品驗證單號&nbsp;<font color="red">*</font></td>
								<td colspan="8"><input id="yanzhengserialno"
									name="yanzhengserialno" onblur="queryYanzhengInfo(this)"
									class="easyui-validatebox"
									data-options="width: 750,required:true"
									value="${wftestkuosanprocessEntity.yanzhengserialno}" /></td>
							</tr>
							<tr align="center">
								<td>申請人工號&nbsp;<font color="red">*</font></td>
								<td><input id="dealno" name="dealno"
									onblur="queryUserInfo(this)" class="easyui-validatebox"
									data-options="width: 150,required:true"
									value="${wftestkuosanprocessEntity.dealno}" /></td>
								<td>申請人&nbsp;<font color="red">*</font></td>
								<td><input id="dealname" name="dealname" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wftestkuosanprocessEntity.dealname}" /></td>
								<td>聯繫分機&nbsp;<font color="red">*</font></td>
								<td><input id="dealtel" name="dealtel"
									onblur="validApplyTel('dealtel')" class="easyui-validatebox"
									data-options="width: 150,required:true"
									value="${wftestkuosanprocessEntity.dealtel}" /></td>
								<td>所在廠區&nbsp;<font color="red">*</font></td>
								<td><input id="dealfactoryid" name="dealfactoryid"
									class="easyui-combobox" data-options="width: 150,required:true"
									value="${wftestkuosanprocessEntity.dealfactoryid}" /></td>
								<td>單位代碼</td>
								<td><input id="dealdeptno" name="dealdeptno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wftestkuosanprocessEntity.dealdeptno}" /></td>
							</tr>
							<tr align="center">
								<td>單位名稱&nbsp;<font color="red">*</font></td>
								<td colspan="4"><input id="dealdeptname"
									style="width:380px" name="dealdeptname"
									class="easyui-validatebox"
									data-options="width: 380,required:true"
									value="${wftestkuosanprocessEntity.dealdeptname}" /></td>
								<td>聯繫郵箱&nbsp;<font color="red">*</font></td>
								<td colspan="4"><input id="dealemail" name="dealemail"
									onblur="validEmail('dealemail')" class="easyui-validatebox"
									data-options="width: 250,required:true"
									value="${wftestkuosanprocessEntity.dealemail}" /></td>

							</tr>
							<tr>
								<td align="center">參與單位&nbsp;<font color="red">*</font></td>
								<td colspan="9">
									<div class="participatingunitsDiv"></div> <input id="joindept"
									name="joindept" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${wftestkuosanprocessEntity.joindept}" /> <input
									id="joindept1" name="joindept1" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${wftestkuosanprocessEntity.joindept1}" /> <input
									id="other1" name="other1" readonly type="hidden"
									class="easyui-validatebox" data-options="width: 80"
									value="${wftestkuosanprocessEntity.other}" />
								</td>
							</tr>
							<tr align="center">
								<td>量試單位&nbsp;<font color="red">*</font></td>
								<td colspan="9"><input id="testdept" style="width:900px"
									name="testdept" class="easyui-validatebox"
									data-options="width: 900,required:true"
									value="${wftestkuosanprocessEntity.testdept}" /></td>
							</tr>
							<tr align="center">
								<td>品名&nbsp;<font color="red">*</font></td>
								<td colspan="2"><input id="samplename" style="width:150px"
									name="samplename" class="easyui-validatebox"
									data-options="width: 150,required:true"
									value="${wftestkuosanprocessEntity.samplename}" /></td>
								<td>品(料)號&nbsp;<font color="red">*</font></td>
								<td colspan="3"><input id="sampleno" style="width:200px"
									name="sampleno" class="easyui-validatebox"
									data-options="width: 200,required:true"
									value="${wftestkuosanprocessEntity.sampleno}" /></td>
								<td>原(物)料號&nbsp;<font color="red">*</font></td>
								<td colspan="2"><input id="rawno" style="width:150px"
									name="rawno" class="easyui-validatebox"
									data-options="width: 150,required:true"
									value="${wftestkuosanprocessEntity.rawno}" /></td>
							</tr>
							<tr align="center">
								<td>模號&nbsp;<font color="red">*</font></td>
								<td colspan="2"><input id="mouldno" style="width:150px"
									name="mouldno" class="easyui-validatebox"
									data-options="width: 150,required:true"
									value="${wftestkuosanprocessEntity.mouldno}" /></td>
								<td>REV&nbsp;<font color="red">*</font></td>
								<td colspan="3"><input id="rev" style="width:200px"
									name="rev" class="easyui-validatebox"
									data-options="width: 200,required:true"
									value="${wftestkuosanprocessEntity.rev}" /></td>
								<td>訂單號&nbsp;<font color="red">*</font></td>
								<td colspan="2"><input id="orderno" style="width:150px"
									name="orderno" class="easyui-validatebox"
									data-options="width: 150,required:true"
									value="${wftestkuosanprocessEntity.orderno}" /></td>
							</tr>
							<tr align="center">
								<td>需求數量&nbsp;<font color="red">*</font></td>
								<td colspan="2"><input id="needno" style="width:150px"
									name="needno" class="easyui-validatebox"
									onblur="valdIsNumberSelf('needno')"
									data-options="width: 150,required:true"
									value="${wftestkuosanprocessEntity.needno}" /></td>
								<td>需求日期&nbsp;<font color="red">*</font></td>
								<td colspan="3"><input id="needstartdate"
									name="needstartdate" class="Wdate"
									data-options="width:150,required:true" style="width:150px"
									value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${wftestkuosanprocessEntity.needstartdate}"/>"
									onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" />~ <input
									id="needenddate" name="needenddate" class="Wdate"
									data-options="width:150,required:true" style="width:150px"
									value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${wftestkuosanprocessEntity.needenddate}"/>"
									onclick="WdatePicker({minDate:'#F{$dp.$D(\'needstartdate\')}',dateFmt:'yyyy-MM-dd'})" /></td>
								<td>費用代碼&nbsp;<font color="red">*</font></td>
								<td colspan="2"><input id="costno" style="width:150px"
									name="costno" class="easyui-validatebox"
									onblur="valdApplyTel(this)"
									data-options="width: 150,required:true"
									value="${wftestkuosanprocessEntity.costno}" /></td>
							</tr>
							<tr align="center">
								<td>擴散名稱&nbsp;<font color="red">*</font></td>
								<td colspan="9"><input id="testname" style="width:1000px"
									name="testname" class="easyui-validatebox"
									data-options="width: 1000,required:true"
									value="${wftestkuosanprocessEntity.testname}" /></td>
							</tr>
							<tr align="center">
								<td>擴散背景&nbsp;<font color="red">*</font></td>
								<td align="left" colspan="9"><textarea id="testbak"
										name="testbak" oninput="return LessThanAuto(this,'txtNum1');"
										onchange="return LessThanAuto(this,'txtNum1');"
										onpropertychange="return LessThanAuto(this,'txtNum1');"
										data-options="required:true" maxlength="500"
										class="easyui-validatebox" style="width:1000px;height:120px;"
										rows="5" cols="6">${wftestkuosanprocessEntity.testbak}</textarea><span
									id="txtNum1"></span></td>
							</tr>
							<tr align="center">
								<td>擴散明細&nbsp;<font color="red">*</font></td>
								<td align="left" colspan="9"><textarea id="testdetail"
										name="testdetail"
										oninput="return LessThanAuto(this,'txtNum2');"
										onchange="return LessThanAuto(this,'txtNum2');"
										onpropertychange="return LessThanAuto(this,'txtNum2');"
										data-options="required:true" maxlength="500"
										class="easyui-validatebox" style="width:1000px;height:120px;"
										rows="5" cols="6">${wftestkuosanprocessEntity.testdetail}</textarea><span
									id="txtNum2"></span></td>
							</tr>
							<tr align="center">
								<td>擴散樓層&nbsp;<font color="red">*</font></td>
								<td align="left" colspan="9"><textarea id="testfloor"
										name="testfloor"
										oninput="return LessThanAuto(this,'txtNum3');"
										onchange="return LessThanAuto(this,'txtNum3');"
										onpropertychange="return LessThanAuto(this,'txtNum3');"
										data-options="required:true" maxlength="200"
										class="easyui-validatebox" style="width:1000px;height:55px;"
										rows="5" cols="6">${wftestkuosanprocessEntity.testfloor}</textarea><span
									id="txtNum3"></span></td>
							</tr>
							<tr align="center">
								<td>擴散效果&nbsp;<font color="red">*</font></td>
								<td align="left" colspan="9"><textarea id="testeffect"
										name="testeffect"
										oninput="return LessThanAuto(this,'txtNum4');"
										onchange="return LessThanAuto(this,'txtNum4');"
										onpropertychange="return LessThanAuto(this,'txtNum4');"
										data-options="required:true" maxlength="200"
										class="easyui-validatebox" style="width:1000px;height:55px;"
										rows="5" cols="6">${wftestkuosanprocessEntity.testeffect}</textarea><span
									id="txtNum4"></span></td>
							</tr>
							<tr align="center">
								<td>擴散流程&nbsp;<font color="red">*</font></td>
								<td align="left" colspan="9"><textarea id="testprocess"
										name="testprocess"
										oninput="return LessThanAuto(this,'txtNum5');"
										onchange="return LessThanAuto(this,'txtNum5');"
										onpropertychange="return LessThanAuto(this,'txtNum5');"
										data-options="required:true" maxlength="500"
										class="easyui-validatebox" style="width:1000px;height:80px;"
										rows="5" cols="6">${wftestkuosanprocessEntity.testprocess}</textarea><span
									id="txtNum5"></span></td>
							</tr>
							<tr align="center">
								<td>擴散負責人</td>
								<td>姓名&nbsp;<font color="red">*</font></td>
								<td colspan="3"><input id="testdutyname"
									style="width:150px" name="testdutyname"
									class="easyui-validatebox"
									data-options="width: 150,required:true"
									value="${wftestkuosanprocessEntity.testdutyname}" /></td>
								<td>電話<font color="red">*</font></td>
								<td colspan="4"><input id="testdutytel" style="width:150px"
									name="testdutytel" class="easyui-validatebox"
									onblur="valdApplyTel(this)"
									data-options="width: 150,required:true"
									value="${wftestkuosanprocessEntity.testdutytel}" /></td>
							</tr>
							<tr>
								<td align="center">生產狀況&nbsp;<font color="red">*</font></td>
								<td colspan="9" align="left">
									<div class="productionstatusDiv"></div> <input id="prodstatus"
									name="prodstatus" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${wftestkuosanprocessEntity.prodstatus }" /> <input
									id="prodstatus1" name="prodstatus1" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${wftestkuosanprocessEntity.prodstatus1}" /> <input
									id="prodstatus2" name="prodstatus2" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${wftestkuosanprocessEntity.prodstatus2}" /> <input
									id="proeffect1" name="proeffect1" disabled="disabled"
									type="hidden" class="easyui-validatebox"
									data-options="width: 30"
									value="${wftestkuosanprocessEntity.proeffect}" /> <input
									id="prodept1" name="prodept1" disabled="disabled" type="hidden"
									class="easyui-validatebox" data-options="width: 30"
									value="${wftestkuosanprocessEntity.prodept}" />
								</td>
							</tr>
							<tr align="center">
								<td>備註</td>
								<td align="left" colspan="9"><textarea id="describtion"
										name="describtion"
										oninput="return LessThanAuto(this,'txtNum');"
										onchange="return LessThanAuto(this,'txtNum');"
										onpropertychange="return LessThanAuto(this,'txtNum');"
										maxlength="200" class="easyui-validatebox"
										style="width:1000px;height:65px;" rows="5" cols="6">${wftestkuosanprocessEntity.describtion}</textarea><span
									id="txtNum"></span></td>
							</tr>
							<tr align="center">
								<td>附件&nbsp;<font color="red">*</font></td>
								<td colspan="9" class="td_style1"><span
									class="sl-custom-file"> <input type="button"
										value="点击上传文件" class="btn-file" /> <input
										id="attachidsUpload" name="attachidsUpload" type="file"
										onchange="oosUploadFile('${workFlowId}');" class="ui-input-file" />
								</span> <input type="hidden" id="attachids" name="attachids"
									value="${wftestkuosanprocessEntity.attachids }" /> <c:choose>
										<c:when test="${file.size()>0}">
											<input type="hidden" id="attachids" name="attachids"
												value="${wftestkuosanprocessEntity.attachids }" />
										</c:when>
										<c:otherwise>
											<input type="hidden" id="attachids" name="attachids" value="" />
										</c:otherwise>
									</c:choose>
									<div id="dowloadUrl">
										<c:forEach items="${file}" varStatus="i" var="item">
											<div id="${item.id}"
												style="line-height:30px;margin-left:5px;" class="float_L">
												<div class="float_L">
													<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
												</div>
												<div class="float_L deleteBtn"
													onclick="oosDelAtt('${item.id}')"></div>
											</div>
										</c:forEach>
									</div></td>
							</tr>
							<tr>
								<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
									<a href="javascript:void(0)"
									onclick="showWfImag('${processId}','量試樣品擴散單');">點擊查看簽核流程圖</a>
								</th>
							</tr>
							<tr>
								<td colspan="10" style="text-align:left;">
									<table class="flowList"
										style="margin-left:5px;margin-top:5px;width:99%">
										<tr>
											<td style="border:none">
												<table width="18%" style="float: left;margin-left: 5px;"
													id="kchargenoTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">課級主管<a
																		href="#" onclick="addRow('kchargeno')">添加一位</a></td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="kchargeno"
															onblur="getUserNameByEmpno(this);" name="kchargeno"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['kchargeno']}"
															value="${wftestkuosanprocessEntity.kchargeno}" /> <c:if
																test="${requiredMap['kchargeno'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="kchargename" name="kchargename"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['kchargeno']}"
															readonly value="${wftestkuosanprocessEntity.kchargename}" /></td>
													</tr>
												</table>

												<table width="18%" style="float: left;margin-left: 5px;"
													id="bchargenoTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">部級主管<a
																		href="#" onclick="addRow('bchargeno')">添加一位</a></td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="bchargeno" name="bchargeno"
															class="easyui-validatebox"
															onblur="getUserNameByEmpno(this);"
															data-options="width:80,required:${requiredMap['bchargeno']}"
															value="${wftestkuosanprocessEntity.bchargeno}" /> <c:if
																test="${requiredMap['bchargeno'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="bchargename" name="bchargename" readonly
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['bchargeno']}"
															value="${wftestkuosanprocessEntity.bchargename}" /></td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno1Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">企劃人員<a
																		id="qhry" href="#">添加一位</a></td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno1"
															onblur="getUserNameByEmpno(this);" name="ylno1" readonly
															class="easyui-validatebox"
															style="background-color:#D0D0D0"
															data-options="width: 80,required:${requiredMap['ylno1']}"
															value="${wftestkuosanprocessEntity.ylno1}" /> <c:if
																test="${requiredMap['ylno1'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname1" name="ylname1"
															class="easyui-validatebox"
															style="background-color:#D0D0D0"
															data-options="width: 80,required:${requiredMap['ylno1']}"
															readonly value="${wftestkuosanprocessEntity.ylname1}" />
														</td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno2Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">企劃主管<a
																		id="qhzg" href="#">添加一位</a></td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno2"
															onblur="getUserNameByEmpno(this);" name="ylno2"
															class="easyui-validatebox" readonly
															style="background-color:#D0D0D0"
															data-options="width: 80,required:${requiredMap['ylno2']}"
															value="${wftestkuosanprocessEntity.ylno2}" /> <c:if
																test="${requiredMap['ylno2'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname2" name="ylname2"
															class="easyui-validatebox"
															style="background-color:#D0D0D0"
															data-options="width: 80,required:${requiredMap['ylno2']}"
															readonly value="${wftestkuosanprocessEntity.ylname2}" />
														</td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno3Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">生產主管<a
																		href="#" onclick="addRow('ylno3')">添加一位</a></td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno3"
															onblur="getUserNameByEmpno(this);" name="ylno3"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno3']}"
															value="${wftestkuosanprocessEntity.ylno3 }" /> <c:if
																test="${requiredMap['ylno3'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname3" name="ylname3"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno3']}"
															readonly value="${wftestkuosanprocessEntity.ylname3}" />
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td style="border:none">
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno4Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">品管主管<a
																		href="#" onclick="addRow('ylno4')">添加一位</a></td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno4"
															onblur="getUserNameByEmpno(this);" name="ylno4"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno4']}"
															value="${wftestkuosanprocessEntity.ylno4 }" /> <c:if
																test="${requiredMap['ylno4'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname4" name="ylname4"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno4']}"
															readonly value="${wftestkuosanprocessEntity.ylname4}" />
														</td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno5Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">生技主管<a
																		href="#" onclick="addRow('ylno5')">添加一位</a></td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno5"
															onblur="getUserNameByEmpno(this);" name="ylno5"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno5']}"
															value="${wftestkuosanprocessEntity.ylno5}" /> <c:if
																test="${requiredMap['ylno5'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname5" name="ylname5"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno5']}"
															readonly value="${wftestkuosanprocessEntity.ylname5}" />
														</td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno6Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">刀具室主管<a
																		href="#" onclick="addRow('ylno6')">添加一位</a></td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno6"
															onblur="getUserNameByEmpno(this);" name="ylno6"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno6']}"
															value="${wftestkuosanprocessEntity.ylno6 }" /> <c:if
																test="${requiredMap['ylno6'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname6" name="ylname6"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno6']}"
															readonly value="${wftestkuosanprocessEntity.ylname6}" />
														</td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno7Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">加工室主管<a
																		href="#" onclick="addRow('ylno7')">添加一位</a></td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno7"
															onblur="getUserNameByEmpno(this);" name="ylno7"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno7']}"
															value="${wftestkuosanprocessEntity.ylno7 }" /> <c:if
																test="${requiredMap['ylno7'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname7" name="ylname7"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno7']}"
															readonly value="${wftestkuosanprocessEntity.ylname7}" />
														</td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno8Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">制工主管<a
																		href="#" onclick="addRow('ylno8')">添加一位</a></td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno8"
															onblur="getUserNameByEmpno(this);" name="ylno8"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno8']}"
															value="${wftestkuosanprocessEntity.ylno8 }" /> <c:if
																test="${requiredMap['ylno8'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname8" name="ylname8"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno8']}"
															readonly value="${wftestkuosanprocessEntity.ylname8}" />
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td style="border:none">
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno9Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">產工主管<a
																		href="#" onclick="addRow('ylno9')">添加一位</a></td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno9"
															onblur="getUserNameByEmpno(this);" name="ylno9"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno9']}"
															value="${wftestkuosanprocessEntity.ylno9}" /> <c:if
																test="${requiredMap['ylno9'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname9" name="ylname9"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno9']}"
															readonly value="${wftestkuosanprocessEntity.ylname9}" />
														</td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="ylno10Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">其他主管<a
																		href="#" onclick="addRow('ylno10')">添加一位</a></td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno10"
															onblur="getUserNameByEmpno(this);" name="ylno10"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno10']}"
															value="${wftestkuosanprocessEntity.ylno10 }" /> <c:if
																test="${requiredMap['ylno10'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname10" name="ylname10"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['ylno10']}"
															readonly value="${wftestkuosanprocessEntity.ylname10}" />
														</td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="cchargenoTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">廠級主管</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').combobox('getValue'))"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="cchargeno" name="cchargeno"
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['cchargeno']}"
															readonly value="${wftestkuosanprocessEntity.cchargeno }" />
															<c:if test="${requiredMap['cchargeno'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="cchargename" name="cchargename" readonly
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['cchargeno']}"
															value="${wftestkuosanprocessEntity.cchargename}" /></td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="zchargenoTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">製造處級主管</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole3('zchargenoTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealfactoryid').combobox('getValue'),null)"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="zchargeno" name="zchargeno"
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['zchargeno']}"
															readonly value="${wftestkuosanprocessEntity.zchargeno }" />
															<c:if test="${requiredMap['zchargeno'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="zchargename" name="zchargename" readonly
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['zchargeno']}"
															value="${wftestkuosanprocessEntity.zchargename }" /></td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="zcchargenoTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">製造總處級主管</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole3('zcchargenoTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealfactoryid').combobox('getValue'),null)"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="zcchargeno" name="zcchargeno"
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['zcchargeno']}"
															readonly value="${wftestkuosanprocessEntity.zcchargeno }" />
															<c:if test="${requiredMap['zcchargeno'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="zcchargename" name="zcchargename" readonly
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['zcchargeno']}"
															value="${wftestkuosanprocessEntity.zcchargename }" /></td>
													</tr>
												</table>
											</td>
										</tr>
									</table>
								</td>
							</tr>
							<tr>
								<td colspan="10" style="text-align:left;"><iframe
										id="qianheLogFrame" name="qianheLogFrame"
										src="${ctx}/wfcontroller/goChargeLog?serialNo=${wftestkuosanprocessEntity.serialno}"
										width="100%"></iframe></td>
							</tr>
							<tr>
								<td colspan="10"
									style="border:none;text-align:center;margin-top:10px"><a
									href="javascript:;" id="btnSave" class="easyui-linkbutton"
									data-options="iconCls:'icon-add'" style="width: 100px;"
									onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp; <a
									href="#" id="btnSubmit" class="easyui-linkbutton"
									data-options="iconCls:'icon-ok'" style="width: 100px;"
									onclick="canelTask('${wftestkuosanprocessEntity.serialno }');">取消申請</a>
								</td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</div>
		<input type="hidden" id="deptNo" name="deptNo" value="" /> <input
			type="hidden" id="chargeNo" name="chargeNo" value="" /> <input
			type="hidden" id="chargeName" name="chargeName" value="" /> <input
			type="hidden" id="factoryId" name="factoryId" value="" /> <input
			type="hidden" id="dutyId" name="dutyId" value="" />
		<div id="win"></div>
	</form>
	<script
		src='${ctx}/static/js/shengchan/wftestkuosanprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>