<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>製程報廢單二</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfprocessscrap2/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfProcessScrap2Entity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfProcessScrap2Entity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">製程報廢單二</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfProcessScrap2Entity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfProcessScrap2Entity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfProcessScrap2Entity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfProcessScrap2Entity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfProcessScrap2Entity.makerno}/${wfProcessScrap2Entity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="6" class="td_style1">申請基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號</td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 150,required:true,disabled:true"
                                       value="${wfProcessScrap2Entity.applyno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="4%">申請人姓名</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wfProcessScrap2Entity.applyname }"/>
                            </td>
                            <td width="4%">所在廠區</td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="400" value="${wfProcessScrap2Entity.applyfactoryid }"
                                       data-options="width: 120,disabled:true,required:true,validType:'comboxValidate[\'applyfactoryid\',\'请选择所在廠區\']',onChange:function(){auditTableReset('auditTable');}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfProcessScrap2Entity.applydeptno }"/>
                            </td>
                            <td>申請單位</td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox"
                                       style="width:90%;" data-options="required:true,disabled:true"
                                       value="${wfProcessScrap2Entity.applydeptname }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="4%">聯繫方式</td>
                            <td width="6%" class="td_style1">
                                <input id="applyphone" name="applyphone"
                                       class="easyui-validatebox" data-options="width: 150,required:true,disabled:true"
                                       value="${wfProcessScrap2Entity.applyphone }"/>
                            </td>
                            <td>申請人郵件</td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       style="width:90%;" data-options="required:true,disabled:true"
                                       value="${wfProcessScrap2Entity.applyemail }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="6" class="td_style1">報廢明細</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;">
                                    <table id="bondedgoodsItemTable" width="100%">
                                        <tr align="center">
                                            <td width="4%">序號</td>
                                            <td width="4%">表單編號</td>
                                            <td width="4%">機種</td>
                                            <td width="4%">品名</td>
                                            <td width="4%">料號</td>
                                            <td width="4%">版次</td>
                                            <td width="4%">顏色</td>
                                            <td width="4%">數量</td>
                                            <td width="4%">申請單位</td>
                                            <td width="4%">責任單位</td>
                                            <td width="4%">費用代碼</td>
                                            <td width="4%">操作</td>
                                        </tr>
                                        <tbody id="info_Body">
                                        <c:if test="${wfProcessScrap2Entity.itemsEntity!=null&&wfProcessScrap2Entity.itemsEntity.size()>0}">
                                            <c:forEach items="${wfProcessScrap2Entity.itemsEntity}" var="itemsEntity"
                                                       varStatus="status">
                                                <tr align="center" id="bondedgoodsItem${status.index+1}">
                                                    <td width="4%">${status.index+1}</td>
                                                    <td width="6%">
                                                        <input id="formno${status.index}"
                                                               name="itemsEntity[${status.index}].formno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 150,required:true,disabled:true"
                                                               value="${itemsEntity.formno }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input id="machinetype${status.index}"
                                                               name="itemsEntity[${status.index}].machinetype"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,disabled:true"
                                                               value="${itemsEntity.machinetype }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input id="charactername${status.index}"
                                                               name="itemsEntity[${status.index}].charactername"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,disabled:true"
                                                               value="${itemsEntity.charactername }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input id="materialcode${status.index}"
                                                               name="itemsEntity[${status.index}].materialcode"
                                                               class="easyui-validatebox"
                                                               data-options="width: 160,disabled:true"
                                                               value="${itemsEntity.materialcode }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input id="banci${status.index}"
                                                               name="itemsEntity[${status.index}].banci"
                                                               class="easyui-validatebox"
                                                               data-options="width: 60,disabled:true"
                                                               value="${itemsEntity.banci }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input id="color${status.index}"
                                                               name="itemsEntity[${status.index}].color"
                                                               class="easyui-validatebox"
                                                               data-options="width: 60,disabled:true"
                                                               value="${itemsEntity.color }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input id="scrapnum${status.index}"
                                                               name="itemsEntity[${status.index}].scrapnum"
                                                               class="easyui-numberbox"
                                                               data-options="width: 80,disabled:true,precision:2,groupSeparator:','"
                                                               value="${itemsEntity.scrapnum }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input id="deptname${status.index}"
                                                               name="itemsEntity[${status.index}].deptname"
                                                               class="easyui-tooltip" title="${itemsEntity.deptname }"
                                                               data-options="width: 80,disabled:true"
                                                               value="${itemsEntity.deptname }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input id="respdeptname${status.index}"
                                                               name="itemsEntity[${status.index}].respdeptname"
                                                               class="easyui-tooltip" title="${itemsEntity.respdeptname }"
                                                               data-options="width: 80,disabled:true"
                                                               value="${itemsEntity.respdeptname }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input id="costno${status.index}"
                                                               name="itemsEntity[${status.index}].costno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,disabled:true"
                                                               value="${itemsEntity.costno }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="bondedgooddeltr(${status.index+1});return false;" disabled/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr">
                                            <td colspan="6" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="bondedgoodItemAdd"
                                                       style="width:100px;float:left;" value="添加一筆" disabled/>
                                            </td>
                                            <td colspan="6" width="100%" style="text-align:right;padding-right:100px;">
                                                <label id="totalNum111" style="float: right;padding-right:100px;color: red;font-size: 20px;font-weight: bold">報廢總數:&nbsp;<label id="totalNum">0</label></label>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="4%">轉撥單號</td>
                            <td width="7%" class="td_style1" colspan="5">
                                <input id="transfernum1" name="transfernum" class="easyui-validatebox"
                                       data-options="width: 350,required:true,disabled:true"
                                       value="${wfProcessScrap2Entity.transfernum }"/>
                        </tr>
                        <tr align="center">
                            <td width="10%">備註</td>
                            <td width="90%" class="td_style1">
						    <textarea id="describtion" name="describtion"
                                      class="easyui-validatebox"
                                      oninput="return LessThan(this);"
                                      onchange="return LessThan(this);"
                                      onpropertychange="return LessThan(this);"
                                      maxlength="200"
                                      style="width:85%;height:80px;"
                                      rows="5" cols="6"
                                      data-options="validType:'length[0,200]',disabled:true">${wfProcessScrap2Entity.describtion }</textarea><span
                                    id="txtNum"></span></td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','製程報廢單二業務主表');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfProcessScrap2Entity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfProcessScrap2Entity.workstatus!=null&&wfProcessScrap2Entity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<div id="dlg"></div>
<script src='${ctx}/static/js/shengchan/wfprocessscrap2.js?random=<%= Math.random()%>'></script>
</body>
</html>