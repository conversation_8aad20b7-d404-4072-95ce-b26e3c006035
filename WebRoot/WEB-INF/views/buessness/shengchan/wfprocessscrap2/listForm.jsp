<%@ taglib prefix="remote" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>製程報廢單二</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfprocessscrap2/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfProcessScrap2Entity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfProcessScrap2Entity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfProcessScrap2Entity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfProcessScrap2Entity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfProcessScrap2Entity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${wfProcessScrap2Entity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">製程報廢單二</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfProcessScrap2Entity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfProcessScrap2Entity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfProcessScrap2Entity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfProcessScrap2Entity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfProcessScrap2Entity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfProcessScrap2Entity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfProcessScrap2Entity.makerno}/${wfProcessScrap2Entity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="6" class="td_style1">申請基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 150,required:true"
                                       value="${wfProcessScrap2Entity.applyno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="4%">申請人姓名</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wfProcessScrap2Entity.applyname }"/>
                            </td>
                            <td width="4%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="400" value="${wfProcessScrap2Entity.applyfactoryid }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applyfactoryid\',\'请选择所在廠區\']',onChange:function(){auditTableReset('auditTable');}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfProcessScrap2Entity.applydeptno }"/>
                            </td>
                            <td>申請單位&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox"
                                       style="width:90%;" data-options="required:true"
                                       value="${wfProcessScrap2Entity.applydeptname }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="4%">聯繫方式&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyphone" name="applyphone"
                                       class="easyui-validatebox" data-options="width: 150,required:true"
                                       value="${wfProcessScrap2Entity.applyphone }"/>
                            </td>
                            <td>申請人郵件&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       style="width:90%;" data-options="required:true"
                                       value="${wfProcessScrap2Entity.applyemail }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="6" class="td_style1">報廢明細</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="bondedgoodsItemTable" width="100%">
                                        <tr align="center">
                                            <td width="4%">序號</td>
                                            <td width="4%">表單編號&nbsp;<font color="red">*</font</td>
                                            <td width="4%">機種</td>
                                            <td width="4%">品名</td>
                                            <td width="4%">料號</td>
                                            <td width="4%">版次</td>
                                            <td width="4%">顏色</td>
                                            <td width="4%">數量</td>
                                            <td width="4%">申請單位</td>
                                            <td width="4%">責任單位</td>
                                            <td width="4%">費用代碼</td>
                                            <td width="4%">操作</td>
                                        </tr>
                                        <tbody id="info_Body">
                                        <c:if test="${wfProcessScrap2Entity.itemsEntity!=null&&wfProcessScrap2Entity.itemsEntity.size()>0}">
                                            <c:forEach items="${wfProcessScrap2Entity.itemsEntity}" var="itemsEntity"
                                                       varStatus="status">
                                                <tr align="center" id="bondedgoodsItem${status.index+1}">
                                                    <td width="4%">${status.index+1}</td>
                                                    <td width="6%">
                                                        <input id="formno${status.index}"
                                                               name="itemsEntity[${status.index}].formno"
                                                               class="easyui-validatebox" onblur="getScrap1Info(${status.index});"
                                                               data-options="width: 150"
                                                               value="${itemsEntity.formno }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input id="machinetype${status.index}"
                                                               name="itemsEntity[${status.index}].machinetype"
                                                               class="easyui-validatebox" readonly
                                                               data-options="width: 80" style="background-color: #c7cbc6"
                                                               value="${itemsEntity.machinetype }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input id="charactername${status.index}"
                                                               name="itemsEntity[${status.index}].charactername"
                                                               class="easyui-validatebox" readonly
                                                               data-options="width: 80" style="background-color: #c7cbc6"
                                                               value="${itemsEntity.charactername }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input id="materialcode${status.index}"
                                                               name="itemsEntity[${status.index}].materialcode"
                                                               class="easyui-validatebox" readonly
                                                               data-options="width: 160" style="background-color: #c7cbc6"
                                                               value="${itemsEntity.materialcode }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input id="banci${status.index}"
                                                               name="itemsEntity[${status.index}].banci"
                                                               class="easyui-validatebox"
                                                               data-options="width: 60"
                                                               value="${itemsEntity.banci }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input id="color${status.index}"
                                                               name="itemsEntity[${status.index}].color"
                                                               class="easyui-validatebox" style="background-color: #c7cbc6"
                                                               data-options="width: 60" readonly
                                                               value="${itemsEntity.color }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input id="scrapnum${status.index}"
                                                               name="itemsEntity[${status.index}].scrapnum"
                                                               class="easyui-numberbox" style="background-color: #c7cbc6"
                                                               data-options="width: 80,precision:2,groupSeparator:','" readonly
                                                               value="${itemsEntity.scrapnum }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input id="deptname${status.index}" style="background-color: #c7cbc6"
                                                               name="itemsEntity[${status.index}].deptname"
                                                               class="easyui-validatebox" style="background-color: #c7cbc6"
                                                               data-options="width: 80" readonly
                                                               value="${itemsEntity.deptname }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input id="respdeptname${status.index}"
                                                               name="itemsEntity[${status.index}].respdeptname"
                                                               class="easyui-validatebox" style="background-color: #c7cbc6"
                                                               data-options="width: 80" readonly
                                                               value="${itemsEntity.respdeptname }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input id="costno${status.index}"
                                                               name="itemsEntity[${status.index}].costno"
                                                               class="easyui-validatebox" style="background-color: #c7cbc6"
                                                               data-options="width: 80" readonly
                                                               value="${itemsEntity.costno }"/>
                                                    </td>
                                                    <td width="6%">
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="bondedgooddeltr(${status.index+1});return false;"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${wfProcessScrap2Entity.itemsEntity==null||wfProcessScrap2Entity.itemsEntity.size()==0}">
                                            <tr align="center" id="bondedgoodsItem0">
                                                <td width="4%">1</td>
                                                <td width="6%">
                                                    <input id="formno0" name="itemsEntity[0].formno"
                                                           class="easyui-validatebox" onblur="getScrap1Info(0);"
                                                           data-options="width: 150,required:true"
                                                           value="${wfProcessScrap2Entity.itemsEntity[0].formno }"/>
                                                </td>
                                                <td width="6%">
                                                    <input id="machinetype0" name="itemsEntity[0].machinetype"
                                                           class="easyui-validatebox" readonly
                                                           data-options="width: 80" style="background-color: #c7cbc6"
                                                           value="${wfProcessScrap2Entity.itemsEntity[0].machinetype }"/>
                                                </td>
                                                <td width="6%">
                                                    <input id="charactername0" name="itemsEntity[0].charactername"
                                                           class="easyui-validatebox" style="background-color: #c7cbc6"
                                                           data-options="width: 80" readonly
                                                           value="${wfProcessScrap2Entity.itemsEntity[0].charactername }"/>
                                                </td>
                                                <td width="6%">
                                                    <input id="materialcode0" name="itemsEntity[0].materialcode"
                                                           class="easyui-validatebox" style="background-color: #c7cbc6"
                                                           data-options="width: 160" readonly
                                                           value="${wfProcessScrap2Entity.itemsEntity[0].materialcode }"/>
                                                </td>
                                                <td width="6%">
                                                    <input id="banci0" name="itemsEntity[0].banci"
                                                           class="easyui-validatebox"
                                                           data-options="width: 60"
                                                           value="${wfProcessScrap2Entity.itemsEntity[0].banci }"/>
                                                </td>
                                                <td width="6%">
                                                    <input id="color0" name="itemsEntity[0].color"
                                                           class="easyui-validatebox" style="background-color: #c7cbc6"
                                                           data-options="width: 60" readonly
                                                           value="${wfProcessScrap2Entity.itemsEntity[0].color }"/>
                                                </td>
                                                <td width="6%">
                                                    <input id="scrapnum0" name="itemsEntity[0].scrapnum"
                                                           class="easyui-numberbox" style="background-color: #c7cbc6"
                                                           data-options="width: 80,precision:2,groupSeparator:','" readonly
                                                           value="${wfProcessScrap2Entity.itemsEntity[0].scrapnum }"/>
                                                </td>
                                                <td width="6%">
                                                    <input id="deptname0" name="itemsEntity[0].deptname"
                                                           class="easyui-validatebox" style="background-color: #c7cbc6"
                                                           data-options="width: 80" readonly
                                                           value="${wfProcessScrap2Entity.itemsEntity[0].deptname }"/>
                                                </td>
                                                <td width="6%">
                                                    <input id="respdeptname0" name="itemsEntity[0].respdeptname"
                                                           class="easyui-validatebox" style="background-color: #c7cbc6"
                                                           data-options="width: 80" readonly
                                                           value="${wfProcessScrap2Entity.itemsEntity[0].respdeptname }"/>
                                                </td>
                                                <td width="6%">
                                                    <input id="costno0" name="itemsEntity[0].costno"
                                                           class="easyui-validatebox" style="background-color: #c7cbc6"
                                                           data-options="width: 80" readonly
                                                           value="${wfProcessScrap2Entity.itemsEntity[0].costno }"/>
                                                </td>
                                                <td width="6%">
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="bondedgooddeltr(0);return false;"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr">
                                            <td colspan="6" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="bondedgoodItemAdd"
                                                       style="width:100px;float:left;" value="添加一筆"/>
                                            </td>
                                            <td colspan="6" width="100%" style="text-align:left;padding-left:10px;">
                                                <label id="totalNum111" style="float: right;padding-right:100px;color: red;font-size: 20px;font-weight: bold">報廢總數:&nbsp;<label id="totalNum">0</label></label>
                                                <input id="scraptotalnum" name="scraptotalnum" type="hidden"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">備註</td>
                            <td width="90%" class="td_style1">
						    <textarea id="describtion" name="describtion"
                                      class="easyui-validatebox"
                                      oninput="return LessThan(this);"
                                      onchange="return LessThan(this);"
                                      onpropertychange="return LessThan(this);"
                                      maxlength="200"
                                      style="width:85%;height:80px;"
                                      rows="5" cols="6"
                                      data-options="validType:'length[0,200]'">${wfProcessScrap2Entity.describtion }</textarea><span
                                    id="txtNum"></span></td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_zhichengbaofeidan_v2','製程報廢單二業務主表','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl1Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">廠部FF組級主管&nbsp;<a
                                                                        href="javascript:void(0);"
                                                                        onclick="addRowComm('ylno1');">添加一位</a>
                                                                </td>

                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno1" name="ylno1"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno1']}"
                                                               onblur='getUserNameByEmpnoComm(this);'
                                                               value="${wfProcessScrap2Entity.ylno1 }"/><c:if
                                                            test="${requiredMap['ylno1'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname1" name="ylname1"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno1']}"
                                                                value="${wfProcessScrap2Entity.ylname1 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">廠部FF課級以上主管&nbsp;<a
                                                                        href="javascript:void(0);"
                                                                        onclick="addRowComm('ylno2');">添加一位</a>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="ylno2"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}"
                                                               onblur='getUserNameByEmpnoComm(this);'
                                                               value="${wfProcessScrap2Entity.ylno2 }"/><c:if
                                                            test="${requiredMap['ylno2'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname2" name="ylname2"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}"
                                                                value="${wfProcessScrap2Entity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl3Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">
                                                                    會簽主管&nbsp;<a
                                                                        href="javascript:void(0);"
                                                                        onclick="addRowComm('ylno3');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno3" name="ylno3"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno3']}"
                                                               onblur='getUserNameByEmpnoComm(this);'
                                                               value="${wfProcessScrap2Entity.ylno3 }"/><c:if
                                                            test="${requiredMap['ylno3'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname3" name="ylname3"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno3']}"
                                                                value="${wfProcessScrap2Entity.ylname3 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl4Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">中央FF倉負責人
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(102,'yl4Table','ylno4','ylname4',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno4" name="ylno4"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno4']}"
                                                               readonly
                                                               value="${wfProcessScrap2Entity.ylno4 }"/><c:if
                                                            test="${requiredMap['ylno4'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname4" name="ylname4"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno4']}"
                                                                value="${wfProcessScrap2Entity.ylname4 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
</div>
<script src='${ctx}/static/js/shengchan/wfprocessscrap2.js?random=<%= Math.random()%>'></script>
</body>
</html>