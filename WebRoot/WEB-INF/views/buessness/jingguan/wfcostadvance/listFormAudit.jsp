<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>費用預提表</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/wfcostadvance/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfCostAdvanceEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfCostAdvanceEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">費用預提表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfCostAdvanceEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfCostAdvanceEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfCostAdvanceEntity.createDate==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfCostAdvanceEntity.createDate}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfCostAdvanceEntity.makerno}/${wfCostAdvanceEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="10" class="td_style1">承辦人信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">預提人員工號</td>
                            <td width="10%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true,disabled:true"
                                       value="${wfCostAdvanceEntity.applyno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="10%">預提人員姓名</td>
                            <td width="10%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss" readonly
                                       data-options="width:80" readonly value="${wfCostAdvanceEntity.applyname }"/>
                            </td>
                            <td width="10%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfCostAdvanceEntity.applydeptno }"/>
                            </td>
                            <td width="10%">費用代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="costdeptno" name="costdeptno" readonly
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfCostAdvanceEntity.costdeptno }"/>
                            </td>
                            <td width="10%">所在廠區</td>
                            <td width="10%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${wfCostAdvanceEntity.applyfactoryid }"
                                       data-options="width: 120,disabled:true,required:true,onSelect:function(){onchangeFactory();},validType:'comboxValidate[\'applyfactoryid\',\'请选择所在廠區\']'"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>法人</td>
                            <td class="td_style1" colspan="3">
                                <input id="layperson" name="layperson"
                                       class="easyui-combobox" data-options="required:true,disabled:true,width: 350,validType:'comboxValidate[\'layperson\',\'请选择法人\']'"
                                       panelHeight="400" editable="false"
                                       value="${wfCostAdvanceEntity.layperson }"/>
                            </td>
                            <td>日期</td>
                            <td class="td_style1">
                                <input id="applyDate" name="applyDate" class="easyui-validatebox Wdate"
                                       data-options="width:200,disabled:true,required:true,prompt:'请选择日期'" style="width:180px"
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfCostAdvanceEntity.applyDate}"/>"
                                       onclick="WdatePicker({doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"/>
                            </td>
                            <td>單位名稱</td>
                            <td colspan="3" class="td_style1">
                                <input id="deptname" name="deptname" class="easyui-tooltip" title="${wfCostAdvanceEntity.deptname }"
                                       style="width:90%;" disabled value="${wfCostAdvanceEntity.deptname }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" class="td_style1">費用預提信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;">
                                    <table id="bondedgoodsItemTable" width="100%">
                                        <tr align="center">
                                            <td width="2%">廠商名稱（全稱)</td>
                                            <td width="4%">廠商代碼</td>
                                            <td width="4%">摘要</td>
                                            <td width="4%">未稅金額</td>
                                            <td width="2%">未結報原因</td>
                                            <td width="2%">預計送件結報日期</td>
                                            <td width="2%">部門代碼</td>
                                            <td width="2%">備註</td>
<%--                                            <td width="2%">操作</td>--%>
                                        </tr>
                                        <tbody id="info_Body">
                                        <c:if test="${wfCostAdvanceEntity.itemsEntity!=null&&wfCostAdvanceEntity.itemsEntity.size()>0}">
                                            <c:forEach items="${wfCostAdvanceEntity.itemsEntity}" var="itemsEntity"
                                                       varStatus="status">
                                                <tr align="center" id="bondedgoodsItem${status.index}">
                                                    <td><input class="easyui-tooltip" title="${itemsEntity.manufacturerName }" id="manufacturerName${status.index}" name="itemsEntity[${status.index}].manufacturerName" value="${itemsEntity.manufacturerName }" disabled> </td>
                                                    <td><input class="easyui-validatebox" id="manufacturerCode${status.index}" name="itemsEntity[${status.index}].manufacturerCode" value="${itemsEntity.manufacturerCode }" data-options="required:true,disabled:true,validType:'englishOrNum[\'manufacturerCode${status.index}\',\'不能輸入漢字\']'"> </td>
                                                    <td><input class="easyui-validatebox" id="abstractContent${status.index}" name="itemsEntity[${status.index}].abstractContent" value="${itemsEntity.abstractContent }" data-options="required:true,disabled:true"> </td>
                                                    <td><input class="easyui-numberbox" id="nonTaxAmount${status.index}" name="itemsEntity[${status.index}].nonTaxAmount" value="${itemsEntity.nonTaxAmount }" data-options="required:true,disabled:true,min:0,precision:2,groupSeparator:','"> </td>
                                                    <td><input class="easyui-tooltip" title="${itemsEntity.unreportedReasons }" id="unreportedReasons${status.index}" name="itemsEntity[${status.index}].unreportedReasons" value="${itemsEntity.unreportedReasons }" disabled> </td>
                                                    <td><input class="easyui-validatebox Wdate" id="dateOfConclusion${status.index}" name="itemsEntity[${status.index}].dateOfConclusion" value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${itemsEntity.dateOfConclusion}"/>" data-options="required:true,disabled:true" onclick="WdatePicker({skin:'whyGreen',dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"> </td>
                                                    <td><input class="easyui-validatebox" id="departmentCode${status.index}" name="itemsEntity[${status.index}].departmentCode" value="${itemsEntity.departmentCode }" data-options="required:true,disabled:true"> </td>
                                                    <td><input class="easyui-tooltip" title="${itemsEntity.remark }" id="remark${status.index}" name="itemsEntity[${status.index}].remark" value="${itemsEntity.remark }" disabled> </td>
<%--                                                    <td>--%>
<%--                                                        <input type="image" src="${ctx}/static/images/deleteRow.png"--%>
<%--                                                               class="deleteBtnStr" disabled--%>
<%--                                                               onclick="bondedgooddeltr(${status.index});return false;"/>--%>
<%--                                                    </td>--%>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                        <tr align="center">
                                            <td colspan="3">合計</td>
                                            <td><label id="totalNum">0.00</label></td>
                                            <td colspan="5"></td>
                                        </tr>
<%--                                        <tr align="left" class="nottr">--%>
<%--                                            <td colspan="10" width="100%" style="text-align:left;padding-left:10px;">--%>
<%--                                                <input type="button" id="bondedgoodItemAdd" disabled--%>
<%--                                                       style="width:100px;float:left;" value="添加一筆"/>--%>
<%--                                            </td>--%>
<%--                                        </tr>--%>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                     <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${wfCostAdvanceEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                         <tr align="center">
                             <td width="10%">備註</td>
                             <td align="left">
                                 1.注明費用代碼(此代碼指:承擔此筆費用的部門代碼),若有多個部門則需附分攤表(否則費用將直接進資料提供者的部門代碼)；</br>
                                 2.預提範圍:同一家供應商當月貨額在5000(含)以上的均需預提(不含固定資產)；</br>
                                 3.不同的法人,不能同一費用預提表上進行費用預提。</br>
                             </td>
                         </tr>
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%">
						<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wfCostAdvanceEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','費用預提表');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfCostAdvanceEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
  <div id="dlg"></div>
<script src='${ctx}/static/js/jingguan/wfcostadvance.js?random=<%= Math.random()%>'></script>
</body>
</html>