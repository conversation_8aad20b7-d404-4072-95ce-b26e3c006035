<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>費用預提表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
            type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfcostadvance/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfCostAdvanceEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfCostAdvanceEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfCostAdvanceEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfCostAdvanceEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfCostAdvanceEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${wfCostAdvanceEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">費用預提表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfCostAdvanceEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfCostAdvanceEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfCostAdvanceEntity.createDate==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfCostAdvanceEntity.createDate}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfCostAdvanceEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfCostAdvanceEntity.makerno}">
            <div class="position_R margin_R">填單人：${wfCostAdvanceEntity.makerno}/${wfCostAdvanceEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="10" class="td_style1">承辦人信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">預提人員工號&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfCostAdvanceEntity.applyno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="10%">預提人員姓名</td>
                            <td width="10%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss" readonly
                                       data-options="width:80" readonly value="${wfCostAdvanceEntity.applyname }"/>
                            </td>
                            <td width="10%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfCostAdvanceEntity.applydeptno }"/>
                            </td>
                            <td width="10%">費用代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="costdeptno" name="costdeptno" readonly
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfCostAdvanceEntity.costdeptno }"/>
                            </td>
                            <td width="10%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${wfCostAdvanceEntity.applyfactoryid }"
                                       data-options="width: 120,required:true,onSelect:function(){onchangeFactory();},validType:'comboxValidate[\'applyfactoryid\',\'请选择所在廠區\']'"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>法人&nbsp;<font color="red">*</font></td>
                            <td class="td_style1" colspan="3">
                                <input id="layperson" name="layperson"
                                       class="easyui-combobox" data-options="required:true,width: 350,validType:'comboxValidate[\'layperson\',\'请选择法人\']'"
                                       panelHeight="400" editable="false"
                                       value="${wfCostAdvanceEntity.layperson }"/>
                            </td>
                            <td>日期&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyDate" name="applyDate" class="easyui-validatebox Wdate"
                                       data-options="width:120,required:true" readonly
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfCostAdvanceEntity.applyDate}"/>"
                                       onclick="WdatePicker({doubleCalendar:false,skin:'whyGreen',dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"/>
                            </td>
                            <td>單位名稱</td>
                            <td colspan="3" class="td_style1">
                                <input id="deptname" name="deptname" class="easyui-validatebox"
                                       style="width:90%;"
                                       value="${wfCostAdvanceEntity.deptname }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" class="td_style1">費用預提信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;">
                                    <table id="bondedgoodsItemTable" width="100%">
                                        <tr align="center">
                                            <td width="2%">廠商名稱（全稱)&nbsp;<font color="red">*</font></td>
                                            <td width="4%">廠商代碼&nbsp;<font color="red">*</font></td>
                                            <td width="4%">摘要&nbsp;<font color="red">*</font></td>
                                            <td width="4%">未稅金額&nbsp;<font color="red">*</font></td>
                                            <td width="2%">未結報原因&nbsp;<font color="red">*</font></td>
                                            <td width="2%">預計送件結報日期&nbsp;<font color="red">*</font></td>
                                            <td width="2%">部門代碼&nbsp;<font color="red">*</font></td>
                                            <td width="2%">備註</td>
                                            <td width="2%">操作</td>
                                        </tr>
                                        <tbody id="info_Body">
                                        <c:if test="${wfCostAdvanceEntity.itemsEntity!=null&&wfCostAdvanceEntity.itemsEntity.size()>0}">
                                            <c:forEach items="${wfCostAdvanceEntity.itemsEntity}" var="itemsEntity"
                                                       varStatus="status">
                                                <tr align="center" id="bondedgoodsItem${status.index}">
                                                    <td><input type="hidden" id="sort${status.index}" style="width: 2px" name="itemsEntity[${status.index}].sort" hidden="true" value="${itemsEntity.sort }"/><input class="easyui-validatebox" id="manufacturerName${status.index}" name="itemsEntity[${status.index}].manufacturerName" value="${itemsEntity.manufacturerName }" data-options="required:true"> </td>
                                                    <td><input class="easyui-validatebox" id="manufacturerCode${status.index}" name="itemsEntity[${status.index}].manufacturerCode" value="${itemsEntity.manufacturerCode }" data-options="required:true,validType:'englishOrNum[\'manufacturerCode${status.index}\',\'不能輸入漢字\']'"> </td>
                                                    <td><input class="easyui-validatebox" id="abstractContent${status.index}" name="itemsEntity[${status.index}].abstractContent" value="${itemsEntity.abstractContent }" data-options="required:true"> </td>
                                                    <td><input class="easyui-numberbox" id="nonTaxAmount${status.index}" name="itemsEntity[${status.index}].nonTaxAmount" value="${itemsEntity.nonTaxAmount }" data-options="required:true,min:0,precision:2,groupSeparator:','"> </td>
                                                    <td><input class="easyui-validatebox" id="unreportedReasons${status.index}" name="itemsEntity[${status.index}].unreportedReasons" value="${itemsEntity.unreportedReasons }" data-options="required:true"> </td>
                                                    <td><input class="easyui-validatebox Wdate" id="dateOfConclusion${status.index}" name="itemsEntity[${status.index}].dateOfConclusion" value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${itemsEntity.dateOfConclusion}"/>" data-options="required:true" readonly onclick="WdatePicker({skin:'whyGreen',dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"> </td>
                                                    <td><input class="easyui-validatebox" id="departmentCode${status.index}" name="itemsEntity[${status.index}].departmentCode" value="${itemsEntity.departmentCode }" data-options="required:true"> </td>
                                                    <td><input id="remark${status.index}" name="itemsEntity[${status.index}].remark" value="${itemsEntity.remark }"> </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               class="deleteBtnStr"
                                                               onclick="bondedgooddeltr(${status.index});return false;"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${wfCostAdvanceEntity.itemsEntity==null||wfCostAdvanceEntity.itemsEntity.size()==0}">
                                            <tr align="center" id="bondedgoodsItem0">
                                                <td><input id="sort0" type="hidden" name="itemsEntity[0].sort" hidden="true" value="0"/><input class="easyui-validatebox" id="manufacturerName0" name="itemsEntity[0].manufacturerName" data-options="required:true"> </td>
                                                <td><input class="easyui-validatebox" id="manufacturerCode0" name="itemsEntity[0].manufacturerCode" data-options="required:true,validType:'englishOrNum[\'manufacturerCode0\',\'不能輸入漢字\']'"> </td>
                                                <td><input class="easyui-validatebox" id="abstractContent0" name="itemsEntity[0].abstractContent" data-options="required:true"> </td>
                                                <td><input class="easyui-numberbox" id="nonTaxAmount0" onblur="autoSum()" name="itemsEntity[0].nonTaxAmount" data-options="required:true,min:0,precision:2,groupSeparator:','"> </td>
                                                <td><input class="easyui-validatebox" id="unreportedReasons0" name="itemsEntity[0].unreportedReasons" data-options="required:true"> </td>
                                                <td><input class="easyui-validatebox Wdate" id="dateOfConclusion0" name="itemsEntity[0].dateOfConclusion" data-options="required:true" readonly onclick="WdatePicker({skin:'whyGreen',dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"> </td>
                                                <td><input class="easyui-validatebox" id="departmentCode0" name="itemsEntity[0].departmentCode" data-options="required:true"> </td>
                                                <td><input id="remark0" name="itemsEntity[0].remark"> </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           class="deleteBtnStr"
                                                           onclick="bondedgooddeltr(0);return false;"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="center"  class="nottr">
                                            <td colspan="3">合計</td>
                                            <td><label id="totalNum">0.00</label></td>
                                            <td colspan="5"></td>
                                        </tr>
                                        <tr align="left" class="nottr">
                                            <td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="bondedgoodItemAdd"
                                                       style="width:100px;float:left;" value="添加一筆"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">導入格式 ：<a href="${ctx}/wfcostadvance/downLoad"
                                                     id="btnBatchImportTpl">參考.xls</a></td>
                            <td width="90%" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button" onclick="openBatchImportWin();"
                                                                   value="批量導入" class="btn-file"/>
                            </span>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button"
                                                                   value="點擊上傳文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span> <input type="hidden" id="attachids"
                                               name="attachids" value="${wfCostAdvanceEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">備註</td>
                            <td align="left">
                                1.注明費用代碼(此代碼指:承擔此筆費用的部門代碼),若有多個部門則需附分攤表(否則費用將直接進資料提供者的部門代碼)；</br>
                                2.預提範圍:同一家供應商當月貨額在5000(含)以上的均需預提(不含固定資產)；</br>
                                3.不同的法人,不能同一費用預提表上進行費用預提。</br>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_ziliaoyidongshenqing','費用預提表','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('kchargeTable',$('#applydeptno').val(),'kchargeno','kchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wfCostAdvanceEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfCostAdvanceEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('bchargeTable',$('#applydeptno').val(),'bchargeno','bchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfCostAdvanceEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfCostAdvanceEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('cchargeTable',$('#applydeptno').val(),'cchargeno','cchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfCostAdvanceEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfCostAdvanceEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfCostAdvanceEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfCostAdvanceEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#applydeptno').val(),'zcchargeno','zcchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wfCostAdvanceEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfCostAdvanceEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產品處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#applydeptno').val(),'pcchargeno','pcchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${wfCostAdvanceEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wfCostAdvanceEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="jgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">經管收件窗口</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(189,'jgchargeTable','jgchargeno','jgchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jgchargeno" name="jgchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['jgchargeno']}"
                                                               readonly
                                                               value="${wfCostAdvanceEntity.jgchargeno }"/><c:if
                                                            test="${requiredMap['jgchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="jgchargename" name="jgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jgchargeno']}"
                                                                value="${wfCostAdvanceEntity.jgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
<div id="optionWin" class="easyui-window" title="費用預提申請單信息導入" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%">
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="file" type="file" style="width: 300px"
                           accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <span id="labelListAddResult"></span>
                </td>
            </tr>
        </table>
    </form>
</div>
<script src='${ctx}/static/js/jingguan/wfcostadvance.js?random=<%= Math.random()%>'></script>
</body>
</html>