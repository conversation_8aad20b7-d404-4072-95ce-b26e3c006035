<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>TipTop系統異常處理申請單</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<form id="mainform" action="${ctx}/wfjingguanprocesses/${action}"
		method="post">
		<input id="ids" name="ids" type="hidden"
			value="${wfjingguanprocessesEntity.id }" /> <input id="serialno"
			name="serialno" type="hidden"
			value="${wfjingguanprocessesEntity.serialno }" />
		<div class="commonW">
			<div class="headTitle">TipTop系統異常處理申請單</div>
			<div class="position_L">
				任務編碼：<span style="color:#999;"> <c:choose>
						<c:when test="${wfjingguanprocessesEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
						<c:otherwise>
                           ${wfjingguanprocessesEntity.serialno}
                       </c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_L1 margin_L">
				填單時間：<span style="color:#999;"> <c:choose>
						<c:when test="${wfjingguanprocessesEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
						<c:otherwise>
							<input style="border:0px" style="width: 120px" readonly="true"
								value="<fmt:formatDate value='${wfjingguanprocessesEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>" />
						</c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_R margin_R">填單人：${wfjingguanprocessesEntity.makerno}/${wfjingguanprocessesEntity.makername}</div>
			<div class="clear"></div>
			<table class="formList">
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">申請人基本信息</td>
							</tr>
							<tr align="center">
								<td>申請人工號</td>
								<td><input id="dealno" name="dealno"
									class="easyui-validatebox inputCss" readonly
									data-options="width:80"
									value="${wfjingguanprocessesEntity.dealno}" /></td>
								<td>單位代碼</td>
								<td colspan="3"><input id="dealdeptno" name="dealdeptno"
									readonly class="easyui-validatebox inputCss"
									data-options="width: 150"
									value="${wfjingguanprocessesEntity.dealdeptno}" /></td>
								<td>所屬廠區</td>
								<td colspan="3"><input id="dealfactoryid"
									name="dealfactoryid" disabled class="easyui-combobox"
									data-options="width: 100"
									value="${wfjingguanprocessesEntity.dealfactoryid}" /></td>
							</tr>
							<tr align="center">
								<td>申請人姓名</td>
								<td><input id="dealname" name="dealname" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfjingguanprocessesEntity.dealname}" /></td>
								<td>單位名稱</td>
								<td colspan="3"><input id="dealdeptname"
									style="width:450px" name="dealdeptname"
									class="easyui-validatebox inputCss" readonly
									data-options="width:450"
									value="${wfjingguanprocessesEntity.dealdeptname}" /></td>
								<td>分機</td>
								<td colspan="3"><input id="phone" name="phone"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 100"
									value="${wfjingguanprocessesEntity.phone}" /></td>
							</tr>
							<tr align="center">
								<td>資位</td>
								<td><input id="leveltype" name="leveltype" readonly
									type="hidden" class="easyui-validatebox inputCss"
									data-options="width: 150"
									value="${wfjingguanprocessesEntity.leveltype}" /> <input
									id="leveltypename" name="leveltypename" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfjingguanprocessesEntity.leveltypename}" /></td>
								<td>費用代碼</td>
								<td colspan="3"><input id="deptcostno" readonly
									style="width:150px" name="deptcostno"
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfjingguanprocessesEntity.deptcostno}" /></td>
								<td>郵箱</td>
								<td colspan="3"><input id="contactnotes"
									name="contactnotes" class="easyui-validatebox inputCss"
									readonly data-options="width: 200"
									value="${wfjingguanprocessesEntity.contactnotes}" /></td>
							</tr>
							<tr align="center">
								<td>需求日期</td>
								<td><input id="requestdate" name="requestdate"
									class="Wdate" disabled data-options="width:100"
									style="width:100px"
									value="<fmt:formatDate  pattern="yyyy-MM-dd"
								value="${wfjingguanprocessesEntity.requestdate}"/>"
									onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" /></td>
								<td>申請日期</td>
								<td colspan="3"><input id="applydate" style="width:150px"
									name="applydate" class="easyui-validatebox inputCss" readonly
									data-options="width:150"
									value="${wfjingguanprocessesEntity.applydate}" /></td>
								<td>職責</td>
								<td colspan="3"><input id="dutydesc" style="width:150px"
									name="dutydesc" class="easyui-validatebox inputCss" readonly
									data-options="width:150"
									value="${wfjingguanprocessesEntity.dutydesc}" /></td>
							</tr>
							<tr align="center">
								<td colspan="10" class="td_style1">申請詳細信息</td>
							</tr>
							<tr align="center">
								<td>需求類別</td>
								<td colspan="9"><div class="requesttypeDiv"></div> <input
									id="requesttype" name="requesttype" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${wfjingguanprocessesEntity.requesttype}" /> <input
									type="hidden" id="disOrEnabled" value="disabled" /></td>
							</tr>
							<tr align="center">
								<td>需求描述(限250字以內)</td>
								<td colspan="9"><textarea id="requestdesc"
										name="requestdesc"
										maxlength="250" readonly class="easyui-validatebox"
										style="width:800px;height:80px;" rows="5" cols="6">${wfjingguanprocessesEntity.requestdesc}</textarea><span
									id="txtNum"></span></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td width="10%">附件</td>
								<td width="90%" class="td_style1"><input type="hidden"
									id="attachids" name="attachids"
									value="${wfjingguanprocessesEntity.attachids }" />
									<div id="dowloadUrl">
										<c:forEach items="${file}" varStatus="i" var="item">
											<div id="${item.id}"
												style="line-height:30px;margin-left:5px;" class="float_L">
												<div class="float_L">
													<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
												</div>
											</div>
										</c:forEach>
									</div></td>
							</tr>
							<tr align="center">
								<td width="10%">批註</td>
								<td width="90%"><textarea id="attachidsremark"
										name="attachidsremark" class="easyui-validatebox"
										style="width:1000px;height:60px;" rows="4" cols="4"></textarea>
									<%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
								</td>
							</tr>
							<tr align="center">
								<td colspan="10"
									style="border:none;text-align:center;margin-top:10px"><fox:action
										cssClass="easyui-linkbutton" cssStyle="width: 100px;"
										serialNo="${wfjingguanprocessesEntity.serialno}"></fox:action>
								</td>
							</tr>
							<tr>
								<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
									<a href="javascript:void(0)"
									onclick="showWfImag('${processId}','Tip top表單申請');">點擊查看簽核流程圖</a>
								</th>
							</tr>
							<tr>
								<td colspan="10" style="text-align:left;">
									${chargeNodeInfo}</td>
							</tr>

							<tr>
								<td colspan="10" style="text-align:left;"><iframe
										id="qianheLogFrame" name="qianheLogFrame"
										src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfjingguanprocessesEntity.serialno}"
										width="100%"></iframe></td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</div>
	</form>
	<div id="dlg"></div>
	<script
		src='${ctx}/static/js/jingguan/wfjingguanprocesses.js?random=<%= Math.random()%>'></script>
</body>
</html>