<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>TipTop表單申請</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
	type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfjingguanprocesses/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfjingguanprocessesEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfjingguanprocessesEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfjingguanprocessesEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfjingguanprocessesEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfjingguanprocessesEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${wfjingguanprocessesEntity.makerfactoryid }"/>
    <div class="commonW">
    <div class="headTitle">TipTop系統異常處理申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfjingguanprocessesEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfjingguanprocessesEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfjingguanprocessesEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfjingguanprocessesEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfjingguanprocessesEntity.makerno}">
			  <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfjingguanprocessesEntity.makerno}">
                <div class="position_R margin_R">填單人：${wfjingguanprocessesEntity.makerno}/${wfjingguanprocessesEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
		<table class="formList">
		   <tr>
                <td>
                    <table class="formList">
                            <tr align="center">
								<td colspan="10" class="td_style1">申請人基本信息</td>
							</tr>
							<tr align="center">
								<td>申請人工號&nbsp;<font color="red">*</font></td>
								<td><input id="dealno" name="dealno"
									class="easyui-validatebox" onblur="queryUserInfo(this)"
									data-options="width:80,required:true"
									value="${wfjingguanprocessesEntity.dealno}" /></td>
								<td>單位代碼</td>
								<td colspan="3"><input id="dealdeptno"
									name="dealdeptno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfjingguanprocessesEntity.dealdeptno}" /></td>
								<td>所屬廠區</td>
							    <td colspan="3"><input id="dealfactoryid"
									name="dealfactoryid" disabled
									class="easyui-combobox" data-options="width: 100"
									value="${wfjingguanprocessesEntity.dealfactoryid}" /></td>
							</tr>
							<tr align="center">
							    <td>申請人姓名</td>
								<td><input id="dealname" name="dealname"
									readonly class="easyui-validatebox inputCss"
									data-options="width: 150" value="${wfjingguanprocessesEntity.dealname}" /></td>
								<td>單位名稱</td>
								<td colspan="3"><input id="dealdeptname"
									style="width:450px" name="dealdeptname"
									class="easyui-validatebox inputCss" readonly
									data-options="width:450"
									value="${wfjingguanprocessesEntity.dealdeptname}" /></td>
								<td>分機&nbsp;<font color="red">*</font></td>
								<td colspan="3"><input id="phone" name="phone"
									class="easyui-validatebox"
									data-options="width: 100,required:true"
									value="${wfjingguanprocessesEntity.phone}" /></td>
							</tr>
							<tr align="center">
							    <td>資位</td>
								<td><input id="leveltype" name="leveltype" readonly type="hidden"
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfjingguanprocessesEntity.leveltype}" />
									<input id="leveltypename" name="leveltypename" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfjingguanprocessesEntity.leveltypename}" /></td>
								<td>費用代碼</td>
								<td colspan="3"><input id="deptcostno" readonly style="width:150px"
									name="deptcostno"
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfjingguanprocessesEntity.deptcostno}" /></td>   
								<td>郵箱&nbsp;<font color="red">*</font></td>
								<td colspan="3"><input id="contactnotes"
									name="contactnotes" class="easyui-validatebox"
									data-options="width: 200,required:true,validType:'email[\'contactnotes\',\'郵箱的格式不正確\']'"
									value="${wfjingguanprocessesEntity.contactnotes}" /></td>
							</tr>
							<tr align="center">
							    <td>需求日期&nbsp;<font color="red">*</font></td>
								<td><input id="requestdate"
									name="requestdate" class="Wdate"
									data-options="width:100,required:true" style="width:100px"
									value="<fmt:formatDate  pattern="yyyy-MM-dd"
								value="${wfjingguanprocessesEntity.requestdate}"/>"
									onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" /></td>
							    <td>申請日期</td>
								<td colspan="3"><input id="applydate" style="width:150px"
									name="applydate"
									class="easyui-validatebox inputCss" readonly
									data-options="width:150,required:true"
									value="${wfjingguanprocessesEntity.applydate}" /></td>
								<td>職責&nbsp;<font color="red">*</font></td>
								<td colspan="3"><input id="dutydesc" style="width:150px"
									name="dutydesc"
									class="easyui-validatebox" 
									data-options="width:150,required:true"
									value="${wfjingguanprocessesEntity.dutydesc}" /></td>
							</tr>
							<tr align="center">
								<td colspan="10" class="td_style1">申請詳細信息</td>
							</tr>
							<tr align="center">
							    <td>需求類別&nbsp;<font color="red">*</font></td>
							    <td colspan="9"><div class="requesttypeDiv"></div> <input
						         id="requesttype" name="requesttype" class="easyui-validatebox"
						         data-options="width: 150" type="hidden"
						         value="${wfjingguanprocessesEntity.requesttype}" /></td>
							</tr>
							<tr align="center">
							    <td>需求描述(限250字以內)&nbsp;<font color="red">*</font></td>
							    <td colspan="9"><textarea id="requestdesc"
									name="requestdesc"
									oninput="return LessThanAuto(this,'txtNum');"
									onchange="return LessThanAuto(this,'txtNum');"
									onpropertychange="return LessThanAuto(this,'txtNum');"
									data-options="required:true" maxlength="250"
									class="easyui-validatebox" style="width:800px;height:80px;"
									rows="5" cols="6">${wfjingguanprocessesEntity.requestdesc}</textarea><span
									id="txtNum"></span>
								</td>
							</tr>
                    </table>
                </td>
           </tr>
		   <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件&nbsp;<font color="red">*</font></td>
                            <td width="90%" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button"
                                                                   value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span> <input type="hidden" id="attachids"
                                               name="attachids" value="${wfjingguanprocessesEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_tiptopyichangchulishenqing','Tip top表單申請','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
            <tr>
                <td colspan="10" style="text-align:left;">
                    <table class="flowList"
                           style="margin-left:5px;margin-top:5px;width:99%">
                        <tr>
                            <td style="border:none">
                               <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">課級主管</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="kchargeno" name="kchargeno"
                                                   class="easyui-validatebox" data-options="width:80,required:${requiredMap['kchargeno']}"
                                                   readonly
                                                   value="${wfjingguanprocessesEntity.kchargeno }"/><c:if test="${requiredMap['kchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="kchargename" name="kchargename"
                                                    readonly class="easyui-validatebox"
                                                    data-options="width:80,required:${requiredMap['kchargeno']}"
                                                    value="${wfjingguanprocessesEntity.kchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">部級主管</td>
                                                    <td style="border: none;">
                                                       <div class="float_L qhUserIcon"
                                                             onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="bchargeno" name="bchargeno"
                                                   class="easyui-validatebox" data-options="width:80,required:${requiredMap['bchargeno']}"
                                                   readonly
                                                   value="${wfjingguanprocessesEntity.bchargeno }"/><c:if test="${requiredMap['bchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="bchargename" name="bchargename"
                                                    readonly class="easyui-validatebox"
                                                    data-options="width:80,required:${requiredMap['bchargeno']}"
                                                    value="${wfjingguanprocessesEntity.bchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">廠級主管</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="cchargeno" name="cchargeno"
                                                   class="easyui-validatebox" data-options="width:80,required:${requiredMap['cchargeno']}"
                                                   readonly
                                                   value="${wfjingguanprocessesEntity.cchargeno }"/><c:if test="${requiredMap['cchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="cchargename" name="cchargename"
                                                    readonly class="easyui-validatebox"
                                                    data-options="width:80,required:${requiredMap['cchargeno']}"
                                                    value="${wfjingguanprocessesEntity.cchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="16%" style="float: left;margin-left: 5px;"
													id="hchargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">會簽<a href="#" onclick="addRowNewCommon('hcharge')">添加一位</a></td>												
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="hchargeno" name="hchargeno"
															class="easyui-validatebox" onblur="getUserNameByEmpnoComm(this);"
															data-options="width:70,required:${requiredMap['hchargeno']}"
															value="${wfjingguanprocessesEntity.hchargeno}" /><c:if
																test="${requiredMap['hchargeno'].equals('true')}"><font color="red">*</font>
															</c:if> /<input id="hchargename" name="hchargename"
															class="easyui-validatebox"
															data-options="width:70,required:${requiredMap['hchargeno']}"
															readonly value="${wfjingguanprocessesEntity.hchargename}" />
														</td>
													</tr>
												</table>
                                <table width="18%" style="float: left;margin-left: 5px;"  id="jchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: center;">經管處理窗口</td>
                                                    <!-- <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(177,'jchargeTable','jchargeno','jchargename',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                    </td> -->
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="jchargeno" name="jchargeno"
                                                   class="easyui-validatebox" data-options="width:80,required:${requiredMap['jchargeno']}"
                                                   onblur ="queryjingguanname()"
                                                   value="${wfjingguanprocessesEntity.jchargeno }"/><c:if test="${requiredMap['jchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="jchargename" name="jchargename"
                                                    readonly class="easyui-validatebox"
                                                    data-options="width:80,required:${requiredMap['jchargeno']}"
                                                    value="${wfjingguanprocessesEntity.jchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>           
                    </table>
                </td>
            </tr>
		    <tr>
                <td colspan="10" style="text-align:left;">
                    <table class="flowList" style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                        <tr>
                            <td>簽核時間</td>
                            <td>簽核節點</td>
                            <td>簽核主管</td>
                            <td>簽核意見</td>
                            <td>批註</td>
                            <td>簽核電腦IP</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                    <a href="javascript:;" id="btnSave" class="easyui-linkbutton" data-options="iconCls:'icon-add'"
                       style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                    <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                       style="width: 100px;" onclick="saveInfo(2);">提交</a>
                </td>
            </tr>
        </table>
        </td>
        </tr>
        </table>
	</div>
	<input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
	</form>
  </div>
<script src='${ctx}/static/js/jingguan/wfjingguanprocesses.js?random=<%= Math.random()%>'></script>
</body>
</html>