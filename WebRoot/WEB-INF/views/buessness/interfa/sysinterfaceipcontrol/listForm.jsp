<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>接口ip管控表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/sysinterfaceipcontrol/${action}" method="post">
    <input type="hidden" name="id" value="${sysInterfaceIpControlEntity.id }"/>
    <input type="hidden" name="empno" value="${sysInterfaceIpControlEntity.empno }"/>
    <input type="hidden" name="name" value="${sysInterfaceIpControlEntity.name }"/>
    <table class="formTable">
        <tr>
            <td>IP地址：</td>
            <td>
                <input name="ipAdress" id="ipAdress"
                       data-options="width: 200,required:'required',prompt:'xx.xx.xx.xx',validType:'ip[\'ipAdress\']'"
                       class="easyui-validatebox" value="${sysInterfaceIpControlEntity.ipAdress }"/>
            </td>
        </tr>
        <tr>
            <td>標識碼：</td>
            <td><input name="identification" id="identification" class="easyui-validatebox"  value="${sysInterfaceIpControlEntity.identification }"/></td>
        </tr>
        <tr>
            <td>備註：</td>
            <td><textarea rows="4" name="remark"
                          style="font-size: 12px;font-family: '微软雅黑';width: 270px">${sysInterfaceIpControlEntity.remark}</textarea>
            </td>
        </tr>
    </table>
</form>
<script type="text/javascript">
    $(function () {
        $('#mainform').form({
            onSubmit: function () {
                var isValid = $(this).form('validate');
                return isValid;	// 返回false终止表单提交
            },
            success: function (data) {
                successTip(data, dg, d);
            }
        });
    });
    function queryUserInfo() {
        var empno = $.trim($("#empno").val().toUpperCase());
        if (empno != null && empno != "") {
            $.ajax({
                url: ctx+'/system/user/getUserInfo/',
                type: 'POST',
                dataType:'json',
                data: {empno: empno},
                success: function (data) {
                    if (!data) {
                        $('#empno').val('');
                        parent.$.messager.alert("溫馨提示", "工號不存在", "error");
                    } else {
                        $('#name').val(data.empname);
                    }
                }
            });
        }
    }
</script>
</body>
</html>