<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>接口ip管控表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input type="text" name="filter_LIKES_ipAdress" class="easyui-validatebox"
               data-options="width:150,prompt: 'ip地址'"/>
        <input type="text" name="filter_LIKES_identification" class="easyui-validatebox"
               data-options="width:150,prompt: '系統標識碼'"/>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
           onclick="listSearchReset()">重置</a>
        <!--導出用，請勿刪除-->
    </form>
    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true"
       onclick="add();">添加</a>
    <span class="toolbar-item dialog-tool-separator"></span>
    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true"
       data-options="disabled:false" onclick="del()">删除</a>
    <span class="toolbar-item dialog-tool-separator"></span>
    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true"
       onclick="upd()">修改</a>
</div>
<table id="dg"></table>
<div id="dlg"></div>

<script type="text/javascript">
    //創建下拉查詢條件
    $(function () {
        dg = $('#dg').datagrid({
            method: "get",
            url: ctx + '/sysinterfaceipcontrol/list',
            fit: true,
            fitColumns: true,
            //fitColumns: false,  //如需要底部出現滾動條，修改爲false
            border: false,
            idField: 'id',
            striped: true,
            cache: false,  //关闭AJAX相应的缓存
            pagination: true,
            rownumbers: true,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: true,
            columns: [[
                {field: 'id', title: '主鍵', hidden: true},
                {field: 'ipAdress', title: 'ip地址', sortable: true, width: 50},
                {field: 'identification', title: '系統標識碼', sortable: true, width: 80},
                {field: 'requestCount', title: '請求次數', sortable: true, width: 50},
                {field: 'empno', title: '添加人', sortable: true, width: 50},
                {field: 'name', title: '添加人姓名', sortable: true, width: 50},
                {field: 'remark', title: '備註', sortable: true, width: 200, tooltip: true, formatter: cellTextTip}
            ]],
            onLoadSuccess: function () {
                $(".easyui-tooltip").tooltip({
                    onShow: function () {
                        $(this).tooltip('tip').css({
                            borderColor: '#000'
                        });
                    }
                });
                //$(this).datagrid("fixRownumber");
            },
            rowStyler: rowStylerFun,
            enableHeaderClickMenu: true,
            enableHeaderContextMenu: true,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });
    });
    function add() {
        d=$("#dlg").dialog({
            title: '添加ip白名單',
            width: 380,
            height: 380,
            href:ctx+'/sysinterfaceipcontrol/create',
            maximizable:true,
            modal:true,
            buttons:[{
                text:'确认',
                handler:function(){
                    $("#mainform").submit();
                }
            },{
                text:'取消',
                handler:function(){
                    d.panel('close');
                }
            }]
        });
    }
    //删除
    function del(){
        var row = dg.datagrid('getSelected');
        if(rowIsNull(row)) return;
        parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data){
            if (data){
                $.ajax({
                    type:'get',
                    url:ctx+"/sysinterfaceipcontrol/delete/"+row.id,
                    success: function(data){
                        successTip(data,dg);
                    }
                },'text');
            }
        });
    }

    //弹窗修改
    function upd(){
        var row = dg.datagrid('getSelected');
        if(rowIsNull(row)) return;
        d=$("#dlg").dialog({
            title: '修改ip白名單',
            width: 380,
            height: 340,
            href:ctx+'/sysinterfaceipcontrol/update/'+row.id,
            maximizable:true,
            modal:true,
            buttons:[{
                text:'修改',
                handler:function(){
                    $('#mainform').submit();
                }
            },{
                text:'取消',
                handler:function(){
                    d.panel('close');
                }
            }]
        });
    }
    //创建查询对象并查询
    function cx() {
        var obj = $("#searchFrom").serializeObject();
        dg.datagrid('load', obj);
    }

    //表單進度查詢頁面查詢條件重置
    function listSearchReset() {
        $("#searchFrom").form("reset");
    }
</script>
</body>
</html>
