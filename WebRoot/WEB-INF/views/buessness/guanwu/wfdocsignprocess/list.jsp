<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>文件簽核</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input type="text" name="filter_EQS_applyno" class="easyui-validatebox"
               data-options="width:150,prompt: '承辦人工號'"/>
        <input type="text" name="filter_EQS_applydeptno" class="easyui-validatebox"
               data-options="width:150,prompt: '承辦人單位代碼'"/>
        <input type="text" name="filter_EQS_serialno" class="easyui-validatebox"
               data-options="width:150,prompt: '任務編碼'"/>
        <input type="text" name="filter_GED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd"
               data-options="width:150,prompt: '填單开始日期'"/>
        - <input type="text" name="filter_LED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd"
                 data-options="width:150,prompt: '填單结束日期'"/>
        <input type="text" name="filter_GED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"
               data-options="width:150,prompt: '簽核完成开始日期'"/>
        - <input type="text" name="filter_LED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"
                 data-options="width:150,prompt: '簽核完成结束日期'"/>
        <input id="qysjzt" style="width:100px" class="easyui-validatebox" name="filter_EQS_workstatus"/>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
           onclick="listSearchReset()">重置</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
           onclick="exportExcel()">导出Excel</a>
        <!--導出用，請勿刪除-->
        <input id="page" name="page" type="hidden" value="1"/>
        <input id="rows" name="rows" type="hidden" value="30"/>
    </form>

</div>
<table id="dg"></table>
<div id="dlg"></div>

<script type="text/javascript">
    //創建下拉查詢條件
    $.ajax({
        url: ctx + "/system/dict/getDictByType/audit_status",
        dataType: "json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#qysjzt").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
    $(function () {
        dg = $('#dg').datagrid({
            method: "get",
            url: ctx + '/wfdocsignprocess/list',
            fit: true,
            fitColumns: true,
            //fitColumns: false,  //如需要底部出現滾動條，修改爲false
            border: false,
            idField: 'id',
            striped: true,
            cache: false,  //关闭AJAX相应的缓存
            pagination: true,
            rownumbers: true,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: true,
            columns: [[
                {field: 'id', title: '主鍵', hidden: true},
                {field: 'serialno', title: '工單流水號', sortable: true, width: 150, formatter: operation},
                //{ field: 'makerno', title: '填單人工號',sortable:true,width:100},
                //{ field: 'makername', title: '填單人名稱',sortable:true,width:100},
                {field: 'createtime', title: '填單時間', sortable: true, width: 100},
                {field: 'workstatus', title: '表單狀態', sortable: true, width: 100},
                //{ field: 'complettime', title: '簽核完成時間',sortable:true,width:100},
                //{ field: 'createBy', title: '創建人',sortable:true,width:100},
                //{ field: 'createDate', title: '創建時間',sortable:true,width:100},
                //{ field: 'updateBy', title: '更新者',sortable:true,width:100},
                //{ field: 'updateDate', title: '更新時間',sortable:true,width:100},
                //{ field: 'delFlag', title: '刪除標識',sortable:true,width:100},
                //{ field: 'makerdeptno', title: '填單人所在部門',sortable:true,width:100},
                //{ field: 'makerfactoryid', title: '填單人廠區Id',sortable:true,width:100},
                //{ field: 'attachids1', title: '附件ID',sortable:true,width:100},
                //{ field: 'attachids2', title: '簽核件 ID',sortable:true,width:100},
                //{ field: 'attachids3', title: '表單內容 ID',sortable:true,width:100},
                //{ field: 'formtype', title: '表單類型',sortable:true,width:100},
                //{ field: 'formtypename', title: '表單類型名稱',sortable:true,width:100},
                //{ field: 'filename', title: '文件名稱',sortable:true,width:100},
                {field: 'applyno', title: '申請人工號', sortable: true, width: 100},
                {field: 'applyname', title: '申請人', sortable: true, width: 100},
                {field: 'applydeptno', title: '單位代碼', sortable: true, width: 100},
                //{ field: 'applytel', title: '聯繫方式',sortable:true,width:100},
                //{ field: 'applyfactoryid', title: '所在廠區',sortable:true,width:100},
                //{ field: 'applyfactoryname', title: '所在廠區名稱',sortable:true,width:100},
                //{ field: 'applynofactoryid', title: '歸屬廠區',sortable:true,width:100},
                {field: 'applydeptname', title: '單位名稱', sortable: true, width: 100, formatter: cellTextTip},
                //{ field: 'applyemail', title: '聯繫郵箱',sortable:true,width:100},
                //{ field: 'secretgrade', title: '文件機密等級',sortable:true,width:100},
                //{ field: 'fileflow', title: '文件流向',sortable:true,width:100},
                //{ field: 'applyreason', title: '簽核說明',sortable:true,width:100},
                //{ field: 'kchargeno', title: '課級主管',sortable:true,width:100},
                //{ field: 'kchargename', title: '課級主管',sortable:true,width:100},
                //{ field: 'bchargeno', title: '部級主管',sortable:true,width:100},
                //{ field: 'bchargename', title: '部級主管',sortable:true,width:100},
                //{ field: 'cchargeno', title: '廠級主管',sortable:true,width:100},
                //{ field: 'cchargename', title: '廠級主管',sortable:true,width:100},
                //{ field: 'zchargeno', title: '製造處級主管',sortable:true,width:100},
                //{ field: 'zchargename', title: '製造處級主管',sortable:true,width:100},
                //{ field: 'zcchargeno', title: '製造總處級主管',sortable:true,width:100},
                //{ field: 'zcchargename', title: '製造總處級主管',sortable:true,width:100},
                //{ field: 'gzchargeno', title: '管制單位確認',sortable:true,width:100},
                //{ field: 'gzchargename', title: '管制單位確認',sortable:true,width:100},
                //{ field: 'hq1chargeno', title: '會簽單位主管',sortable:true,width:100},
                //{ field: 'hq1chargename', title: '會簽單位主管',sortable:true,width:100},
                //{ field: 'hq2chargeno', title: '會簽單位主管',sortable:true,width:100},
                //{ field: 'hq2chargename', title: '會簽單位主管',sortable:true,width:100},
                {field: 'nodeName', title: '當前審核節點', sortable: true, width: 150, formatter: formatProgress},
                {field: 'auditUser', title: '當前簽核人', sortable: true, width: 120},
                {field: 'complettime', title: '簽核完成時間', sortable: true, width: 100}
            ]],
            onLoadSuccess: function () {
                $(".easyui-tooltip").tooltip({
                    onShow: function () {
                        $(this).tooltip('tip').css({
                            borderColor: '#000'
                        });
                    }
                });
                //$(this).datagrid("fixRownumber");
            },
            rowStyler: rowStylerFun,
            enableHeaderClickMenu: true,
            enableHeaderContextMenu: true,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });
    });

    //任務編號查看頁面
    function operation(value, row, index) {
        return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('詳情',ctx+'/wfdocsignprocess/view/"
            + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
    };

    //创建查询对象并查询
    function cx() {
        var obj = $("#searchFrom").serializeObject();
        dg.datagrid('load', obj);
    }

    //表單進度查詢頁面查詢條件重置
    function listSearchReset() {
        $("#searchFrom").form("reset");
    }

    //导出excel
    function exportExcel() {
        var options = $('#dg').datagrid('getPager').data("pagination").options;
        var page = options.pageNumber;//当前页数  
        var total = options.total;
        var rows = options.pageSize;//每页的记录数（行数）  
        var form = document.getElementById("searchFrom");
        $('#page').val(page);
        $('#rows').val(total);
        var form = document.getElementById("searchFrom");
        searchFrom.action = ctx + '/wfdocsignprocess/exportExcel';
        form.submit();
    }
</script>
</body>
</html>