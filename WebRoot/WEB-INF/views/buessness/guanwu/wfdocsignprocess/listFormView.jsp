<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>文件簽核</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script type="text/javascript" src='${ctx}/static/plugins/jquery/jquery.media.js'></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfdocsignprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfdocsignprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfdocsignprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">文件簽核</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfdocsignprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfdocsignprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfdocsignprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfdocsignprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfdocsignprocessEntity.makerno}/${wfdocsignprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="5%">表單類型</td>
                            <td colspan="3" width="16%" class="td_style2">${wfdocsignprocessEntity.formtypename }</td>
                            <td width="4%">文件名稱</td>
                            <td colspan="5" width="20%" class="td_style2">${wfdocsignprocessEntity.filename }</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">申請人工號</td>
                            <td width="6%" class="td_style2">${wfdocsignprocessEntity.applyno}</td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style2">${wfdocsignprocessEntity.applyname}</td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style2">${wfdocsignprocessEntity.applydeptno}</td>
                            <td width="4%">聯繫方式</td>
                            <td width="6%" class="td_style2">${wfdocsignprocessEntity.applytel}</td>
                            <td width="4%">所在廠區</td>
                            <td width="6%" class="td_style2">${wfdocsignprocessEntity.applyfactoryname}</td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="5" class="td_style2">${wfdocsignprocessEntity.applydeptname }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfdocsignprocessEntity.applyemail}</td>
                        </tr>
                        <tr align="center">
                            <td>文件機密等級</td>
                            <td colspan="3" class="td_style2">${wfdocsignprocessEntity.secretgrade }</td>
                            <td>文件流向</td>
                            <td colspan="5" class="td_style2">${wfdocsignprocessEntity.fileflow }</td>
                        </tr>
                        <tr align="center">
                            <td>簽核說明</td>
                            <td colspan="9" class="td_style2">
                                    <textarea id="applyreason" name="applyreason"
                                              maxlength="500" class="easyui-validatebox" style="width:99%;height:60px;"
                                              rows="5" cols="3">${wfdocsignprocessEntity.applyreason}</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>表單內容</td>
                            <td colspan="9" class="td_style1">
                                <input type="hidden" id="attachids2" name="attachids2" value="${wfdocsignprocessEntity.attachids2 }"/>
<%--                                <div id="dowloadUrl2">--%>
<%--                                    <c:forEach items="${file2}" varStatus="i" var="item">--%>
<%--                                        <div id="${item.id}"--%>
<%--                                             style="line-height:30px;margin-left:5px;" class="float_L">--%>
<%--                                            <div class="float_L">--%>
<%--                                                <a href="${ctx}/newEsign/downloadEncrypt/${item.id}">${item.name}</a>--%>
<%--                                            </div>--%>
<%--                                        </div>--%>
<%--                                    </c:forEach>--%>
<%--                                </div>--%>
                                <div class="pdf" style="padding-top: 5px;margin-left: 5px;">
                                    <iframe  id ="pdf_page"  name ="pdf_page" style="width:99%;height:1000px" src="${ctx}/admin/downloadPreviewImageEncryptDocFtps/${wfdocsignprocessEntity.attachids2 }">
                                    </iframe>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>签核件</td>
                            <td colspan="9" class="td_style1">
                                <input type="hidden" id="attachids3" name="attachids2" value="${wfdocsignprocessEntity.attachids3 }"/>
                                <div id="dowloadUrl3">
                                    <c:forEach items="${file3}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/newEsign/downloadEncryptDocFtps/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <input type="hidden" id="attachids1" name="attachids1" value="${wfdocsignprocessEntity.attachids1 }"/>
                                <div id="dowloadUrl1">
                                    <c:forEach items="${file1}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/newEsign/downloadEncryptDocFtps/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','文件簽核');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfdocsignprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfdocsignprocessEntity.workstatus!=null&&wfdocsignprocessEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                </c:if>
                                <c:if test="${wfdocsignprocessEntity.workstatus!=null&&wfdocsignprocessEntity.workstatus==3}">
                                    <a href="${ctx}/wfdocsignprocess/downloadEncrypt/${wfdocsignprocessEntity.serialno}"  class="l-btn l-btn-small" style="width: 100px;padding: 4px 0px;">合併</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/guanwu/wfdocsignprocess.js?random=<%= Math.random()%>'></script>
<script type="text/javascript">
    $(document).ready(function(){
        $('.pdf').media({
            width: 800,
            height: 800
        });
    });
</script>
</body>
</html>
