<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>文件簽核</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script type="text/javascript" src='${ctx}/static/plugins/jquery/jquery.media.js'></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfdocsignprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfdocsignprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfdocsignprocessEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfdocsignprocessEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfdocsignprocessEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfdocsignprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${wfdocsignprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">文件簽核</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfdocsignprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfdocsignprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfdocsignprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfdocsignprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfdocsignprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfdocsignprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfdocsignprocessEntity.makerno}/${wfdocsignprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="5%">表單類型&nbsp;<font color="red">*</font></td>
                            <td colspan="3" width="16%" class="td_style2">
                                <input id="formtype" name="formtype"
                                       class="easyui-combobox" data-options="width: 250,required:true,validType:'comboxValidate[\'formtype\',\'请選擇表單類型\']',onSelect:function(){onchangeFormtype();}"
                                       panelHeight="auto" editable="false"
                                       value="${wfdocsignprocessEntity.formtype }"/>
                                <input id="formtypename" name="formtypename" type="hidden" value="${wfdocsignprocessEntity.formtypename }"/>

                            </td>
                            <td width="4%">文件名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="5" width="20%" class="td_style2">
                                <input id="filename" name="filename" class="easyui-validatebox" data-options="width: 400,required:true" onblur="validFileInfo()"
                                       value="${wfdocsignprocessEntity.filename }"/>
                                <input id="docFileNames" name="docFileNames" type="hidden" value="${docFileNames}"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfdocsignprocessEntity.applyno}" onblur="queryUserInfo('apply');"/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfdocsignprocessEntity.applyname}"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfdocsignprocessEntity.applydeptno}"/>
                            </td>
                            <td width="4%">聯繫方式&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <%--<input id="applytel" name="applytel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfdocsignprocessEntity.applytel}" data-options="required:true,prompt:'579+66666'"
                                       onblur="validApplyTel('applytel')"/>--%>
                                <input id="applytel" name="applytel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfdocsignprocessEntity.applytel}" data-options="required:true"/>
                            </td>
                            <td width="4%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfdocsignprocessEntity.applyfactoryid}"
                                       data-options="width: 120,validType:'comboxValidate[\'applyfactoryid\',\'请選擇所在廠區\']',onSelect:function(){onchangeFactory();}"/>
                                <input id="applynofactoryid" name="applynofactoryid" type="hidden" value="${wfdocsignprocessEntity.applynofactoryid}"/>
                                <input id="applyfactoryname" name="applyfactoryname" type="hidden" value="${wfdocsignprocessEntity.applyfactoryname}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox" data-options="width: 450,required:true"
                                   value="${wfdocsignprocessEntity.applydeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="applyemail"
                                       class="easyui-validatebox"
                                       value="${wfdocsignprocessEntity.applyemail}" style="width:300px;"
                                       data-options="required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>文件機密等級&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style2">
                                <div class="secretgradeDiv" style="float: left;"></div>
                                <input id="secretgrade" name="secretgrade"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfdocsignprocessEntity.secretgrade }"/>
                            </td>
                            <td>文件流向&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style2">
                                <div class="fileflowDiv" style="float: left;"></div>
                                <input id="fileflow" name="fileflow"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfdocsignprocessEntity.fileflow }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>簽核說明&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style2">
                                    <textarea id="applyreason" name="applyreason"
                                              oninput="return LessThanAuto(this,'txtNum');"
                                              onchange="return LessThanAuto(this,'txtNum');"
                                              onpropertychange="return LessThanAuto(this,'txtNum');"
                                              maxlength="500" class="easyui-validatebox" style="width:99%;height:60px;"
                                              data-options="required:true"
                                              rows="5"
                                              cols="3">${wfdocsignprocessEntity.applyreason}</textarea><span
                                    id="txtNum"></span>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>表單內容&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1">
                                <span class="sl-custom-file" style="margin-top: 5px;">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachids2Upload" name="attachids2Upload" type="file" onchange="uploadEncryptPreviewFile('attachids2','dowloadUrl2');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids2" name="attachids2" value="${wfdocsignprocessEntity.attachids2 }"/>
<%--                                <div id="dowloadUrl2">--%>
<%--                                    <c:forEach items="${file2}" varStatus="i" var="item">--%>
<%--                                        <div id="${item.id}"--%>
<%--                                             style="line-height:30px;margin-left:5px;" class="float_L">--%>
<%--                                            <div class="float_L">--%>
<%--                                                <a href="${ctx}/newEsign/downloadEncrypt/${item.id}">${item.name}</a>--%>
<%--                                            </div>--%>
<%--                                            <div class="float_L deleteBtn" onclick="delAttFile('attachids2','${item.id}')"></div>--%>
<%--                                        </div>--%>
<%--                                    </c:forEach>--%>
<%--                                </div>--%>
                                <div class="pdf" style="padding-top: 5px;margin-left: 5px;">
                                    <%--<iframe  id ="pdf_page"  name ="pdf_page" style="width:99%;height:1550px">
                                    </iframe>--%>
                                    <c:choose>
                                        <c:when test="${wfdocsignprocessEntity.attachids2==null}">
                                            <iframe  id ="pdf_page"  name ="pdf_page" style="width:99%;height:1550px">
                                            </iframe>
                                        </c:when>
                                        <c:otherwise>
                                            <iframe  id ="pdf_page"  name ="pdf_page" style="width:99%;height:1550px" src="${ctx}/admin/downloadPreviewEncryptDocFtps/${wfdocsignprocessEntity.attachids2 }.pdf">
                                            </iframe>
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>签核件&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachids3Upload" name="attachids3Upload" type="file" onchange="uploadEncryptPreviewFile('attachids3','dowloadUrl3');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids3" name="attachids3" value="${wfdocsignprocessEntity.attachids3 }"/>
                                <div id="dowloadUrl3">
                                    <c:forEach items="${file3}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/newEsign/downloadEncryptDocFtps/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="delAttDocFile('attachids3','${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachids1Upload" name="attachids1Upload" type="file" onchange="uploadEncryptPreviewFile('attachids1','dowloadUrl1');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids1" name="attachids1" value="${wfdocsignprocessEntity.attachids1}"/>
                                <div id="dowloadUrl1">
                                    <c:forEach items="${file1}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/newEsign/downloadEncryptDocFtps/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="delAttDocFile('attachids1','${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="9" class="td_style2">
                                1.需要簽核內容的主要頁面直接展示，方便簽單主管快速瀏覽；<br/>
                                2.上傳表單內容請使用PDF格式；<br/>
                                3.要求此部份顯示高度/寬度，系統根據上傳內容大小自動調整；<br/>
                                4.表單原件電子檔必須上傳，佐證簽核資料，可添加多個附件。
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">

                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_fileverify_v2','文件簽核','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wfdocsignprocessEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfdocsignprocessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="gzchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">管制單位確認</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole10($('#formtype').combobox('getValue'),'gzchargeTable','gzchargeno','gzchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="gzchargeno" name="gzchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['gzchargeno']}"
                                                               readonly
                                                               value="${wfdocsignprocessEntity.gzchargeno }"/><c:if
                                                            test="${requiredMap['gzchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="gzchargename" name="gzchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['gzchargeno']}"
                                                                value="${wfdocsignprocessEntity.gzchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfdocsignprocessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfdocsignprocessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="hq1chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽單位主管1
                                                                    <a href="javascript:addHq2('hq1charge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hq1chargeno" name="hq1chargeno" onblur="gethqUserNameByEmpno(this,'hq1charge');"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hq1chargeno']}"
                                                               value="${wfdocsignprocessEntity.hq1chargeno }"/><c:if
                                                            test="${requiredMap['hq1chargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hq1chargename" name="hq1chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hq1chargeno']}"
                                                                value="${wfdocsignprocessEntity.hq1chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'cchargeno','cchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfdocsignprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfdocsignprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hq2chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽單位主管2
                                                                    <a href="javascript:addHq2('hq2charge');">添加一位</a>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hq2chargeno" name="hq2chargeno" onblur="gethqUserNameByEmpno(this,'hq2charge');"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hq2chargeno']}"
                                                               value="${wfdocsignprocessEntity.hq2chargeno }"/><c:if
                                                            test="${requiredMap['hq2chargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hq2chargename" name="hq2chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hq2chargeno']}"
                                                                value="${wfdocsignprocessEntity.hq2chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfdocsignprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfdocsignprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="hq3chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽單位主管3
                                                                    <a href="javascript:addHq2('hq3charge');">添加一位</a>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hq3chargeno" name="hq3chargeno" onblur="gethqUserNameByEmpno(this,'hq3charge');"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hq3chargeno']}"
                                                               value="${wfdocsignprocessEntity.hq3chargeno }"/><c:if
                                                            test="${requiredMap['hq3chargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hq3chargename" name="hq3chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hq3chargeno']}"
                                                                value="${wfdocsignprocessEntity.hq3chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#applydeptno').val(),'zcchargeno','zcchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wfdocsignprocessEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfdocsignprocessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
</form>
<script src='${ctx}/static/js/guanwu/wfdocsignprocess.js?random=<%= Math.random()%>'></script>
<script type="text/javascript">
    if ("${wfdocsignprocessEntity.hq1chargeno}" != "") {
        var nostr = "${wfdocsignprocessEntity.hq1chargeno}";
        var namestr = "${wfdocsignprocessEntity.hq1chargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hq1chargeTable tr:eq(" + (i + 2) + ")").find("#hq1chargeno").val(notr[i]);
            $("#hq1chargeTable tr:eq(" + (i + 2) + ")").find("#hq1chargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hq1chargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hq1chargeno' name='hq1chargeno' style='width: 80px' value='' onblur='gethqUserNameByEmpno(this,'hq1charge');'/>/<input id='hq1chargename' name='hq1chargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wfdocsignprocessEntity.hq2chargeno}" != "") {
        var nostr = "${wfdocsignprocessEntity.hq2chargeno}";
        var namestr = "${wfdocsignprocessEntity.hq2chargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hq2chargeTable tr:eq(" + (i + 2) + ")").find("#hq2chargeno").val(notr[i]);
            $("#hq2chargeTable tr:eq(" + (i + 2) + ")").find("#hq2chargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hq2chargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hq2chargeno' name='hq2chargeno' style='width: 80px' value='' onblur='gethqUserNameByEmpno(this,'hq2charge');'/>/<input id='hq2chargename' name='hq2chargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wfdocsignprocessEntity.hq3chargeno}" != "") {
        var nostr = "${wfdocsignprocessEntity.hq3chargeno}";
        var namestr = "${wfdocsignprocessEntity.hq3chargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hq3chargeTable tr:eq(" + (i + 2) + ")").find("#hq3chargeno").val(notr[i]);
            $("#hq3chargeTable tr:eq(" + (i + 2) + ")").find("#hq3chargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hq3chargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hq3chargeno' name='hq3chargeno' style='width: 80px' value='' onblur='gethqUserNameByEmpno(this,'hq3charge');'/>/<input id='hq3chargename' name='hq3chargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
</script>
</body>
</html>
