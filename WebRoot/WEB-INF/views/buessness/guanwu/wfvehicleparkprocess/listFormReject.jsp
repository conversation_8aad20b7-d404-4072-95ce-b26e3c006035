<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>機動車停車位辦理申請單業務</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src='${ctx}/static/js/guanwu/wfvehicleparkprocess.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfvehicleparkprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfvehicleparkprocessEntity.id }"/>
    <input id="serialno" name="wfvehiclepark.serialno" type="hidden" value="${wfvehicleparkprocessEntity.serialno }"/>
    <input id="makerno" name="wfvehiclepark.makerno" type="hidden" value="${wfvehicleparkprocessEntity.makerno }"/>
    <input id="makername" name="wfvehiclepark.makername" type="hidden" value="${wfvehicleparkprocessEntity.makername }"/>
    <input id="makerdeptno" name="wfvehiclepark.makerdeptno" type="hidden" value="${wfvehicleparkprocessEntity.makerdeptno }"/>
    <div class="commonW">
    <div class="headTitle">機動車停車位辦理申請單業務</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfvehicleparkprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfvehicleparkprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfvehicleparkprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfvehicleparkprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfvehicleparkprocessEntity.makerno}">
			  <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfvehicleparkprocessEntity.makerno}">
                <div class="position_R margin_R">填單人：${wfvehicleparkprocessEntity.makerno}/${wfvehicleparkprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table  class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="6%">使用廠區&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyfactoryid" name="wfvehiclepark.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfvehicleparkprocessEntity.applyfactoryid }"
                                       data-options="width: 400,required:true,onSelect:function(){clearCheckInfo();}"/>
                            </td>
                            <td width="6%">申請類別&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applytype" name="wfvehiclepark.applytype" class="easyui-combobox"
                                       panelHeight="auto" value="${wfvehicleparkprocessEntity.applytype }"
                                       data-options="width: 400,required:true,onSelect:function(){typeCheck();}"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="4" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="4" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <input id="vehicleparkItemTableIndex" type="hidden"
                                           value="<c:if test="${wfvehicleparkitems!=null && wfvehicleparkitems.size()>0}">${wfvehicleparkitems.size() +1}</c:if>
                                        <c:if test="${wfvehicleparkitems==null}">2</c:if>">
                                    </input>
                                    <table id="vehicleparkItemTable" width="150%">
                                        <tr align="center">
                                            <td>&nbsp;序號&nbsp;</td>
                                            <td>工號&nbsp;<font color="red">*</font></td>
                                            <td>姓名&nbsp;<font color="red">*</font></td>
                                            <td>資位&nbsp;<font color="red">*</font></td>
                                            <td>管理職&nbsp;<font color="red">*</font></td>
                                            <td>單位代碼&nbsp;<font color="red">*</font></td>
                                            <td>單位名稱&nbsp;<font color="red">*</font></td>
                                            <td>手機號碼&nbsp;<font color="red">*</font></td>
                                            <td>車牌號碼&nbsp;<font color="red">*</font></td>
                                            <td>品牌型號&nbsp;<font color="red">*</font></td>
                                            <td>備註</td>
                                            <td>&nbsp;操作&nbsp;</td>
                                        </tr>
                                        <tbody id="info_Body">
                                        <c:if test="${wfvehicleparkitems!=null&&wfvehicleparkitems.size()>0}">
                                            <c:forEach items="${wfvehicleparkitems}" var="vehicleparkitem" varStatus="status">
                                                <tr align="center" id="vehicleparkItem${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td><input id="vehiclepark_empno${status.index+1}" name="wfvehicleparkitems[${status.index}].empno"
                                                               class="easyui-validatebox" data-options="required:true" style="width: 80px;" onblur="queryUserInfo(this,'${status.index+1}');" value="${vehicleparkitem.empno}"/></td>
                                                    <td><input id="vehiclepark_username${status.index+1}" name="wfvehicleparkitems[${status.index}].username"
                                                               class="easyui-validatebox inputCss" readonly style="width:60px;text-align: center;" value="${vehicleparkitem.username}"/></td>
                                                    <td><input id="vehiclepark_levelname${status.index+1}" name="wfvehicleparkitems[${status.index}].levelname"
                                                               class="easyui-validatebox inputCss" readonly style="width:60px;text-align: center;" value="${vehicleparkitem.levelname}"/></td>
                                                    <td><input id="vehiclepark_ismanager${status.index+1}" name="wfvehicleparkitems[${status.index}].ismanager"
                                                               class="easyui-validatebox inputCss" readonly style="width:60px;text-align: center;" value="${vehicleparkitem.ismanager}"/></td>
                                                    <td><input id="vehiclepark_deptno${status.index+1}" name="wfvehicleparkitems[${status.index}].deptno"
                                                               class="easyui-validatebox inputCss" readonly data-options="required:true" style="width:100px;text-align: center;" value="${vehicleparkitem.deptno}"/></td>
                                                    <td><input id="vehiclepark_depname${status.index+1}" name="wfvehicleparkitems[${status.index}].depname"
                                                               class="easyui-validatebox" data-options="required:true" style="width:350px;text-align: center;" value="${vehicleparkitem.depname}"/></td>
                                                    <td><input id="vehiclepark_phonenumber${status.index+1}" name="wfvehicleparkitems[${status.index}].phonenumber"
                                                               class="easyui-validatebox" data-options="required:true" style="width:150px;" onblur="valdMobilephone(this)" value="${vehicleparkitem.phonenumber}"/></td>
                                                    <td><input id="vehiclepark_busnumber${status.index+1}" name="wfvehicleparkitems[${status.index}].busnumber"
                                                               class="easyui-validatebox" data-options="required:true" style="width:100px;" value="${vehicleparkitem.busnumber}"/></td>
                                                    <td><input id="vehiclepark_bustype${status.index+1}" name="wfvehicleparkitems[${status.index}].bustype"
                                                               class="easyui-validatebox" data-options="required:true" style="width:100px;" value="${vehicleparkitem.bustype}"/></td>
                                                    <td><input id="vehiclepark_remark${status.index+1}" name="wfvehicleparkitems[${status.index}].remark"
                                                               class="easyui-validatebox" style="width:150px;" value="${vehicleparkitem.remark}"/></td>
                                                    <td><input type="image" src="${ctx}/static/images/deleteRow.png" onclick="vehicleparkdeltr(${status.index+1});return false;"/></td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${wfvehicleparkitems==null}">
                                            <tr align="center" id="vehicleparkItem1">
                                                <td>1</td>
                                                <td><input id="vehiclepark_empno1" name="wfvehicleparkitems[0].empno"
                                                           class="easyui-validatebox" data-options="required:true" onblur="queryUserInfo(this,'1');" style="width: 80px;" value=""/></td>
                                                <td><input id="vehiclepark_username1" name="wfvehicleparkitems[0].username"
                                                           class="easyui-validatebox inputCss" readonly style="width:60px;text-align: center;" value=""/></td>
                                                <td><input id="vehiclepark_levelname1" name="wfvehicleparkitems[0].levelname"
                                                           class="easyui-validatebox inputCss" readonly style="width:60px;;text-align: center;" value=""/></td>
                                                <td><input id="vehiclepark_ismanager1" name="wfvehicleparkitems[0].ismanager"
                                                           class="easyui-validatebox inputCss" readonly style="width:60px;;text-align: center;" value=""/></td>
                                                <td><input id="vehiclepark_deptno1" name="wfvehicleparkitems[0].deptno"
                                                           class="easyui-validatebox inputCss" readonly data-options="required:true" style="width:100px;;text-align: center;" value=""/></td>
                                                <td><input id="vehiclepark_depname1" name="wfvehicleparkitems[0].depname"
                                                           class="easyui-validatebox" data-options="required:true" style="width:350px;;text-align: center;" value=""/></td>
                                                <td><input id="vehiclepark_phonenumber1" name="wfvehicleparkitems[0].phonenumber"
                                                           class="easyui-validatebox" data-options="required:true" onblur="valdMobilephone(this)" style="width:100px;" value=""/></td>
                                                <td><input id="vehiclepark_busnumber1" name="wfvehicleparkitems[0].busnumber"
                                                           class="easyui-validatebox" data-options="required:true" style="width:100px;" value=""/></td>
                                                <td><input id="vehiclepark_bustype1" name="wfvehicleparkitems[0].bustype"
                                                           class="easyui-validatebox" data-options="required:true" style="width:100px;" value=""/></td>
                                                <td><input id="vehiclepark_remark1" name="wfvehicleparkitems[0].remark"
                                                           class="easyui-validatebox" style="width:150px;" value=""/></td>
                                                <td><input type="image" src="${ctx}/static/images/deleteRow.png" onclick="vehicleparkdeltr(1);return false;"/></td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr">
                                            <td colspan="12" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="vehicleparkItemAdd" style="width:100px;float:left;" value="添加一筆"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">導入樣式&nbsp;
                                <%--正式--%>
                                <a href="${ctx}/ossAdmin/download/b929cc06b1a547feabb358672e88386d" id="btnBatchImportTpl">參考.xls</a>
                                <%--測試--%>
                                <%--<a href="${ctx}/admin/download/de2eeb5c328f445a8e0296d69c8a02b3" id="btnBatchImportTpl">參考.xls</a>--%>
                            </td>
                            <td width="90%" class="td_style1">
                                <a href="#" id="batchImport" class="easyui-linkbutton" disabled="true"
                                   data-options="iconCls:'icon-hamburg-cv'" style="width: 100px;"
                                   onclick="openBatchImportWin();">批量導入</a>

                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file"
                                           onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="wfvehiclepark.attachids"
                                       value="${wfvehicleparkprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:if test="${file!=null&&file.size()>0}">
                                        <c:forEach items="${file}" varStatus="i" var="item">
                                            <div id="${item.id}"
                                                 style="line-height:30px;margin-left:5px;" class="float_L">
                                                <div class="float_L">
                                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                                </div>
                                                <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                            </div>
                                        </c:forEach>
                                    </c:if>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td style="text-align: left;">停車位辦理說明：<br>
                                1.申請原則：師2有管理職或師3及以上<br>
                                2.凡擁有乘坐福利班車權限者，不得申請停車場權限，避免佔用公司雙重資源<br>
                                3.當月連續7天或累計15天無進出記錄，取消停車位資格(法定節假日不計入內)，后續不再辦理<br>
                                4.車輛進場須遵守停車場管理規定，服從指揮安排<br>
                                5.申請類別為A級時，需上傳資料：駕駛證、行駛證、廠證、機動車停車場行為公約承諾書、其它(如關係證明、出差單(僅限出差至蘭考人員))&nbsp;<a href="${ctx}/wfvehicleparkprocess/downLoad/batchImportTpl2" id="btnBatchImportTpl2">承諾書下載</a> <br>
                                6.蘭考廠區安全管理部24H服務熱線：分機直撥：582-110；手機直撥：0371-26552110
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_tingcheweibanlishenqingdan_v1','機動車停車位辦理申請單業務','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl1Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">停車場管理員</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(103,'yl1Table','ylno1','ylname1',$('#applyfactoryid').combobox('getValue'),'wfvehiclepark')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno1" name="wfvehiclepark.ylno1"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno1']}"
                                                               readonly
                                                               value="${wfvehicleparkprocessEntity.ylno1 }"/><c:if
                                                            test="${requiredMap['ylno1'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname1" name="wfvehiclepark.ylname1"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno1']}"
                                                                value="${wfvehicleparkprocessEntity.ylname1 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">車調中心部</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(104,'yl2Table','ylno2','ylname2',$('#applyfactoryid').combobox('getValue'),'wfvehiclepark')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="wfvehiclepark.ylno2"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}"
                                                               readonly
                                                               value="${wfvehicleparkprocessEntity.ylno2 }"/><c:if
                                                            test="${requiredMap['ylno2'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname2" name="wfvehiclepark.ylname2"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}"
                                                                value="${wfvehicleparkprocessEntity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl3Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">安全管理部</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(105,'yl3Table','ylno3','ylname3',$('#applyfactoryid').combobox('getValue'),'wfvehiclepark')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno3" name="wfvehiclepark.ylno3"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno3']}"
                                                               readonly
                                                               value="${wfvehicleparkprocessEntity.ylno3 }"/><c:if
                                                            test="${requiredMap['ylno3'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname3" name="wfvehiclepark.ylname3"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno3']}"
                                                                value="${wfvehicleparkprocessEntity.ylname3 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl4Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">停車場系統作業員
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(106,'yl4Table','ylno4','ylname4',$('#applyfactoryid').combobox('getValue'),'wfvehiclepark')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno4" name="wfvehiclepark.ylno4"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno4']}"
                                                               readonly
                                                               value="${wfvehicleparkprocessEntity.ylno4 }"/><c:if
                                                            test="${requiredMap['ylno4'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname4" name="wfvehiclepark.ylname4"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno4']}"
                                                                value="${wfvehicleparkprocessEntity.ylname4 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfvehicleparkprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="canelTask('${wfvehicleparkprocessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	<input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
<div id="optionWin" class="easyui-window" title="機動車停車位辦理申請單信息導入" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%">
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="file" type="file" style="width: 300px"
                           accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>
                    <input id="isExcel2003" name="isExcel2003" type="hidden" value="">

                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <span ID="labelListAddResult"></span><a href="${ctx}/wfvehicleparkprocess/downLoad/errorExcel" id="downloadError"
                                                            plain="true">查看錯誤信息</a>
                </td>
            </tr>
        </table>
    </form>
</div>
<script type="text/javascript">
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            successTip(data, dg, d);
        }
    });
</script>
</body>
</html>