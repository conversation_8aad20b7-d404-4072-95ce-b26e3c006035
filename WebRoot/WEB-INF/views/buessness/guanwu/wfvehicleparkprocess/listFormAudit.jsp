<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>機動車停車位辦理申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src='${ctx}/static/js/guanwu/wfvehicleparkprocess.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfvehicleparkprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfvehicleparkprocessEntity.id }"/>
    <input id="serialno" name="wfvehiclepark.serialno" type="hidden" value="${wfvehicleparkprocessEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">機動車停車位辦理申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfvehicleparkprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfvehicleparkprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfvehicleparkprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfvehicleparkprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfvehicleparkprocessEntity.makerno}/${wfvehicleparkprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="6%">使用廠區</td>
                            <td width="6%" class="td_style1">
                                <input id="applyfactoryid" name="wfvehiclepark.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfvehicleparkprocessEntity.applyfactoryid }"
                                       data-options="width: 400" disabled/>
                            </td>
                            <td width="6%">申請類別</td>
                            <td width="6%" class="td_style1">
                                <input id="applytype" name="wfvehiclepark.applytype" class="easyui-combobox"
                                       panelHeight="auto" value="${wfvehicleparkprocessEntity.applytype }"
                                       data-options="width: 400" disabled/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="4" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="4" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <input id="vehicleparkItemTableIndex" type="hidden"
                                           value="<c:if test="${wfvehicleparkitems!=null && wfvehicleparkitems.size()>0}">${wfvehicleparkitems.size() +1}</c:if>
                                        <c:if test="${wfvehicleparkitems==null}">2</c:if>">
                                    </input>
                                    <table id="vehicleparkItemTable" width="150%">
                                        <tr align="center">
                                            <td>&nbsp;序號&nbsp;</td>
                                            <td>工號</td>
                                            <td>姓名</td>
                                            <td>資位</td>
                                            <td>管理職</td>
                                            <td>單位代碼</td>
                                            <td>單位名稱</td>
                                            <td>手機號碼</td>
                                            <td>車牌號碼</td>
                                            <td>品牌型號</td>
                                            <td>備註</td>
                                        </tr>
                                        <tbody id="info_Body">
                                        <c:if test="${wfvehicleparkitems!=null&&wfvehicleparkitems.size()>0}">
                                            <c:forEach items="${wfvehicleparkitems}" var="vehicleparkitem" varStatus="status">
                                                <tr align="center" id="vehicleparkItem${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>${vehicleparkitem.empno}</td>
                                                    <td>${vehicleparkitem.username}</td>
                                                    <td>${vehicleparkitem.levelname}</td>
                                                    <td>${vehicleparkitem.ismanager}</td>
                                                    <td>${vehicleparkitem.deptno}</td>
                                                    <td>${vehicleparkitem.depname}</td>
                                                    <td>${vehicleparkitem.phonenumber}</td>
                                                    <td>${vehicleparkitem.busnumber}</td>
                                                    <td>${vehicleparkitem.bustype}</td>
                                                    <td>${vehicleparkitem.remark}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                             <td width="10%">附件</td>
                             <td width="90%" colspan="9" class="td_style1">
                                 <input type="hidden" id="attachids"
                                        name="wfvehiclepark.attachids" value="${wfvehicleparkprocessEntity.attachids }"/>
                                 <div id="dowloadUrl">
                                     <c:forEach items="${file}" varStatus="i" var="item">
                                         <div id="${item.id}"
                                              style="line-height:30px;margin-left:5px;" class="float_L">
                                             <div class="float_L">
                                                 <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                             </div>
                                         </div>
                                     </c:forEach>
                                 </div>
                             </td>
                        </tr>
                        <tr align="center">
                            <td>批註</td>
                            <td colspan="9" class="td_style1">
						        <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;" rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wfvehicleparkprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','機動車停車位辦理申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfvehicleparkprocessEntity.serialno}" width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
  <div id="dlg"></div>
</body>
</html>