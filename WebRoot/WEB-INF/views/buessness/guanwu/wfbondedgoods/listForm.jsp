<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>保稅貨物損溢申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src='${ctx}/static/js/guanwu/wfbondedgoods.js?random=<%= Math.random()%>'></script>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
            color: black;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfbondedgoods/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfbondedgoodsEntity.id }"/>
    <input id="serialno" name="wfbondedgoods.serialno" type="hidden" value="${wfbondedgoodsEntity.serialno }"/>
    <input id="makerno" name="wfbondedgoods.makerno" type="hidden" value="${wfbondedgoodsEntity.makerno }"/>
    <input id="makername" name="wfbondedgoods.makername" type="hidden" value="${wfbondedgoodsEntity.makername }"/>
    <input id="makerdeptno" name="wfbondedgoods.makerdeptno" type="hidden" value="${wfbondedgoodsEntity.makerdeptno }"/>
    <div class="commonW">
        <div class="headTitle">保稅貨物損溢申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfbondedgoodsEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfbondedgoodsEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfbondedgoodsEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfbondedgoodsEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfbondedgoodsEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfbondedgoodsEntity.makerno}">
            <div class="position_R margin_R">填單人：${wfbondedgoodsEntity.makerno}/${wfbondedgoodsEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="wfbondedgoods.applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfbondedgoodsEntity.applyno}" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="6%">申請人姓名</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="wfbondedgoods.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfbondedgoodsEntity.applyname }"/>
                            </td>
                            <td width="6%">單位代碼&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="wfbondedgoods.applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       value="${wfbondedgoodsEntity.applydeptno }" readonly/>
                            </td>
                            <td width="6%">廠區&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyfactoryid" name="wfbondedgoods.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfbondedgoodsEntity.applyfactoryid }"
                                       data-options="width: 100,required:true"/>
                            </td>
                            <td width="6%">費用代碼&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applycostno" name="wfbondedgoods.applycostno"
                                       class="easyui-validatebox inputCss" data-options="width:80"
                                       value="${wfbondedgoodsEntity.applycostno }" readonly/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="wfbondedgoods.applydeptname" class="easyui-validatebox"
                                       data-options="width: 350"
                                       value="${wfbondedgoodsEntity.applydeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="wfbondedgoods.applyemail" class="easyui-validatebox"
                                       value="${wfbondedgoodsEntity.applyemail}" style="width:300px;"
                                       data-options="required:true" onblur="valdEmail('')"/>
                            </td>
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyphone" name="wfbondedgoods.applyphone" class="easyui-validatebox"
                                       style="width:80px;"
                                       value="${wfbondedgoodsEntity.applyphone}"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>需求日期&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="requiredate" name="wfbondedgoods.requiredate" class="easyui-validatebox Wdate"
                                       data-options="width:100,required:true" value="<fmt:formatDate  pattern="yyyy-MM-dd"
                                       value="${wfbondedgoodsEntity.requiredate}"/>"
                                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"/>
                            </td>
                            <td colspan="8" class="td_style1"></td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <input id="bondedgoodsItemTableIndex" type="hidden"
                                           value="<c:if test="${bondedgoodsitemEntity!=null && bondedgoodsitemEntity.size()>0}">${bondedgoodsitemEntity.size() +1}</c:if>
                                        <c:if test="${bondedgoodsitemEntity==null}">2</c:if>">
                                    </input>
                                    <table id="bondedgoodsItemTable" width="200%">
                                        <tr align="center" class="nottr">
                                            <td colspan="14">廠部填寫</td>
                                            <td colspan="7">關務填寫</td>
                                        </tr>
                                        <tr align="center">
                                            <td>&nbsp;序號&nbsp;</td>
                                            <td>法人&nbsp;<font color="red">*</font></td>
                                            <td>幾種&nbsp;<font color="red">*</font></td>
                                            <td>工單號&nbsp;<font color="red">*</font></td>
                                            <td>主件料號&nbsp;<font color="red">*</font></td>
                                            <td>完工數量&nbsp;<font color="red">*</font></td>
                                            <td>發料料號&nbsp;<font color="red">*</font></td>
                                            <td>發料底階料號&nbsp;<font color="red">*</font></td>
                                            <td>用量&nbsp;<font color="red">*</font></td>
                                            <td>單位&nbsp;<font color="red">*</font></td>
                                            <td>應發(退)數量&nbsp;<font color="red">*</font></td>
                                            <td>實發(退)數量&nbsp;<font color="red">*</font></td>
                                            <td>虧料數量&nbsp;<font color="red">*</font></td>
                                            <td>虧料比率(%)<font color="red">*</font></td>
                                            <td>備案項號&nbsp;<font color="red">*</font></td>
                                            <td>商品名稱&nbsp;<font color="red">*</font></td>
                                            <td>採購單價(USD)&nbsp;<font color="red">*</font></td>
                                            <td>綜合稅率(%)&nbsp;<font color="red">*</font></td>
                                            <td>虧料貨值(USD)&nbsp;<font color="red">*</font></td>
                                            <td>應補稅額(USD)&nbsp;<font color="red">*</font></td>
                                            <td>&nbsp;操作&nbsp;</td>
                                        </tr>
                                        <tbody id="info_Body">
                                        <c:if test="${bondedgoodsitemEntity!=null&&bondedgoodsitemEntity.size()>0}">
                                            <c:forEach items="${bondedgoodsitemEntity}" var="bondedgoodsitem"
                                                       varStatus="status">
                                                <tr align="center" id="bondedgoodsItem${status.index+1}">
                                                    <td>${status.index+1}<input type="hidden"
                                                                                name="wfbondedgoodsitems[${status.index}].seqNum"
                                                                                value="${status.index}"/></td>
                                                    <td><input id="bondedgood_corporate${status.index+1}"
                                                               name="wfbondedgoodsitems[${status.index}].corporate"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width: 100px;"
                                                               value="${bondedgoodsitem.corporate}"/></td>
                                                    <td><input id="bondedgood_prtName${status.index+1}"
                                                               name="wfbondedgoodsitems[${status.index}].prtName"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width:60px;" value="${bondedgoodsitem.prtName}"/>
                                                    </td>
                                                    <td><input id="bondedgood_orderNo${status.index+1}"
                                                               name="wfbondedgoodsitems[${status.index}].orderNo"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width:100px;" value="${bondedgoodsitem.orderNo}"/>
                                                    </td>
                                                    <td><input id="bondedgood_partNo${status.index+1}"
                                                               name="wfbondedgoodsitems[${status.index}].partNo"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width:150px;" value="${bondedgoodsitem.partNo}"/>
                                                    </td>
                                                    <td><input id="bondedgood_finishNum${status.index+1}"
                                                               name="wfbondedgoodsitems[${status.index}].finishNum"
                                                               class="easyui-validatebox" onblur="f_isNum(this)"
                                                               data-options="required:true" style="width:100px;"
                                                               value="${bondedgoodsitem.finishNum}"/></td>
                                                    <td><input id="bondedgood_outPartNo${status.index+1}"
                                                               name="wfbondedgoodsitems[${status.index}].outPartNo"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width:150px;"
                                                               value="${bondedgoodsitem.outPartNo}"/></td>
                                                    <td><input id="bondedgood_outDPartNo${status.index+1}"
                                                               name="wfbondedgoodsitems[${status.index}].outDPartNo"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width:150px;"
                                                               value="${bondedgoodsitem.outDPartNo}"/></td>
                                                    <td><input id="bondedgood_dosage${status.index+1}"
                                                               name="wfbondedgoodsitems[${status.index}].dosage"
                                                               class="easyui-validatebox" onblur="f_isNum(this)"
                                                               data-options="required:true" style="width:60px;"
                                                               value="${bondedgoodsitem.dosage}"/></td>
                                                    <td><input id="bondedgood_unit${status.index+1}"
                                                               name="wfbondedgoodsitems[${status.index}].unit"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width:60px;" value="${bondedgoodsitem.unit}"/>
                                                    </td>
                                                    <td><input id="bondedgood_yftnum${status.index+1}"
                                                               name="wfbondedgoodsitems[${status.index}].yftnum"
                                                               class="easyui-validatebox" onblur="f_isNum(this)"
                                                               data-options="required:true" style="width:80px;"
                                                               value="${bondedgoodsitem.yftnum}"/></td>
                                                    <td><input id="bondedgood_sftnum${status.index+1}"
                                                                    name="wfbondedgoodsitems[${status.index}].sftnum"
                                                                    class="easyui-validatebox" onblur="f_isNum(this)"
                                                                    data-options="required:true" style="width:80px;"
                                                                    value="${bondedgoodsitem.sftnum}"/></td>
                                                         <td><input id="bondedgood_lossNum${status.index+1}"
                                                                    name="wfbondedgoodsitems[${status.index}].lossNum"
                                                                    class="easyui-validatebox" onblur="f_isNum(this)"
                                                                    data-options="required:true" style="width:80px;"
                                                                    value="${bondedgoodsitem.lossNum}"/></td>
                                                         <td><input id="bondedgood_lossRate${status.index+1}"
                                                                    name="wfbondedgoodsitems[${status.index}].lossRate"
                                                                    class="easyui-validatebox" onblur="f_isNum2(this)"
                                                                    data-options="required:true" style="width:80px;"
                                                                    value="${bondedgoodsitem.lossRate}"/></td>
                                                         <td><input id="bondedgood_recordItem${status.index+1}"
                                                                    name="wfbondedgoodsitems[${status.index}].recordItem"
                                                                    class="easyui-validatebox" onblur="f_isNum(this)"
                                                                    data-options="required:true" style="width:60px;"
                                                                    value="${bondedgoodsitem.recordItem}"/></td>
                                                         <td><input id="bondedgood_proName${status.index+1}"
                                                                    name="wfbondedgoodsitems[${status.index}].proName"
                                                                    class="easyui-validatebox" data-options="required:true"
                                                                    style="width:150px;" value="${bondedgoodsitem.proName}"/>
                                                         </td>
                                                         <td><input id="bondedgood_purchasePrice${status.index+1}"
                                                                    name="wfbondedgoodsitems[${status.index}].purchasePrice"
                                                                    class="easyui-validatebox" onblur="f_isNum(this)"
                                                                    data-options="required:true" style="width:80px;"
                                                                    value="${bondedgoodsitem.purchasePrice}"/></td>
                                                         <td><input id="bondedgood_compositeTaxrate${status.index+1}"
                                                                    name="wfbondedgoodsitems[${status.index}].compositeTaxrate"
                                                                    class="easyui-validatebox" onblur="f_isNum2(this)"
                                                                    data-options="required:true" style="width:80px;"
                                                                    value="${bondedgoodsitem.compositeTaxrate}"/></td>
                                                         <td><input id="bondedgood_lossValue${status.index+1}"
                                                                    name="wfbondedgoodsitems[${status.index}].lossValue"
                                                                    class="easyui-validatebox" onblur="f_loss_value(this)"
                                                                    data-options="required:true" style="width:80px;"
                                                                    value="${bondedgoodsitem.lossValue}"/></td>
                                                         <td><input id="bondedgood_taxpayable${status.index+1}"
                                                                    name="wfbondedgoodsitems[${status.index}].taxpayable"
                                                                    class="easyui-validatebox" onblur="f_taxpayable(this)"
                                                                    data-options="required:true" style="width:80px;"
                                                                    value="${bondedgoodsitem.taxpayable}"/></td>
                                                         <td><input type="image" src="${ctx}/static/images/deleteRow.png"
                                                                    onclick="bondedgooddeltr(${status.index+1});return false;"/>
                                                         </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${bondedgoodsitemEntity==null}">
                                            <tr align="center" id="bondedgoodsItem1">
                                                <td>1<input type="hidden" name="wfbondedgoodsitems[0].seqNum"
                                                            value="1"/></td>
                                                <td><input id="bondedgood_corporate1"
                                                           name="wfbondedgoodsitems[0].corporate"
                                                           class="easyui-validatebox" data-options="required:true"
                                                           style="width:100px;" value=""/></td>
                                                <td><input id="bondedgood_prtName1" name="wfbondedgoodsitems[0].prtName"
                                                           class="easyui-validatebox" data-options="required:true"
                                                           style="width:60px;" value=""/></td>
                                                <td><input id="bondedgood_orderNo1" name="wfbondedgoodsitems[0].orderNo"
                                                           class="easyui-validatebox" data-options="required:true"
                                                           style="width:100px;" value=""/></td>
                                                <td><input id="bondedgood_partNo1" name="wfbondedgoodsitems[0].partNo"
                                                           class="easyui-validatebox" data-options="required:true"
                                                           style="width:150px;" value=""/></td>
                                                <td><input id="bondedgood_finishNum1"
                                                           name="wfbondedgoodsitems[0].finishNum"
                                                           class="easyui-validatebox" onblur="f_isNum(this)"
                                                           data-options="required:true" style="width:100px;" value=""/>
                                                </td>
                                                <td><input id="bondedgood_outPartNo1"
                                                           name="wfbondedgoodsitems[0].outPartNo"
                                                           class="easyui-validatebox" data-options="required:true"
                                                           style="width:150px;" value=""/></td>
                                                <td><input id="bondedgood_outDPartNo1"
                                                           name="wfbondedgoodsitems[0].outDPartNo"
                                                           class="easyui-validatebox" data-options="required:true"
                                                           style="width:150px;" value=""/></td>
                                                <td><input id="bondedgood_dosage1" name="wfbondedgoodsitems[0].dosage"
                                                           class="easyui-validatebox" onblur="f_isNum(this)"
                                                           data-options="required:true" style="width:60px;" value=""/>
                                                </td>
                                                <td><input id="bondedgood_unit1" name="wfbondedgoodsitems[0].unit"
                                                           class="easyui-validatebox" data-options="required:true"
                                                           style="width:60px;" value=""/></td>
                                                <td><input id="bondedgood_yftnum1" name="wfbondedgoodsitems[0].yftnum"
                                                           class="easyui-validatebox" onblur="f_isNum(this)"
                                                           data-options="required:true" style="width:80px;" value=""/>
                                                </td>
                                                <td><input id="bondedgood_sftnum1" name="wfbondedgoodsitems[0].sftnum"
                                                           class="easyui-validatebox" onblur="f_isNum(this)"
                                                           data-options="required:true" style="width:80px;" value=""/>
                                                </td>
                                                <td><input id="bondedgood_lossNum1" name="wfbondedgoodsitems[0].lossNum"
                                                           class="easyui-validatebox" onblur="f_isNum(this)"
                                                           data-options="required:true" style="width:80px;" value=""/>
                                                </td>
                                                <td><input id="bondedgood_lossRate1"
                                                           name="wfbondedgoodsitems[0].lossRate"
                                                           class="easyui-validatebox" onblur="f_isNum2(this)"
                                                           data-options="required:true" style="width:80px;" value=""/>
                                                </td>
                                                <td><input id="bondedgood_recordItem1"
                                                           name="wfbondedgoodsitems[0].recordItem"
                                                           class="easyui-validatebox" onblur="f_isNum(this)"
                                                           data-options="required:true" style="width:60px;" value=""/>
                                                </td>
                                                <td><input id="bondedgood_proName1" name="wfbondedgoodsitems[0].proName"
                                                           class="easyui-validatebox" data-options="required:true"
                                                           style="width:150px;" value=""/></td>
                                                <td><input id="bondedgood_purchasePrice1"
                                                           name="wfbondedgoodsitems[0].purchasePrice"
                                                           class="easyui-validatebox" onblur="f_isNum(this)"
                                                           data-options="required:true" style="width:80px;" value=""/>
                                                </td>
                                                <td><input id="bondedgood_compositeTaxrate1"
                                                           name="wfbondedgoodsitems[0].compositeTaxrate"
                                                           class="easyui-validatebox" onblur="f_isNum2(this)"
                                                           data-options="required:true" style="width:80px;" value=""/>
                                                </td>
                                                <td><input id="bondedgood_lossValue1"
                                                           name="wfbondedgoodsitems[0].lossValue"
                                                           class="easyui-validatebox" onblur="f_loss_value(this)"
                                                           data-options="required:true" style="width:80px;" value=""/>
                                                </td>
                                                <td><input id="bondedgood_taxpayable1"
                                                           name="wfbondedgoodsitems[0].taxpayable"
                                                           class="easyui-validatebox" onblur="f_taxpayable(this)"
                                                           data-options="required:true" style="width:80px;" value=""/>
                                                </td>
                                                <td><input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="bondedgooddeltr(1);return false;"/></td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr class="nottr">
                                            <td colspan="18" style="text-align: right;">合計:&nbsp;&nbsp;</td>
                                            <td>
                                                <input id="loss_value_sum" name="wfbondedgoods.lossvaluesum"
                                                       class="easyui-validatebox inputCss" style="color: #ff9000;"
                                                       data-options="width:100" value="${wfbondedgoodsEntity.lossvaluesum}"/>

                                            </td>
                                            <td>
                                                <input id="taxpayable_sum" name="wfbondedgoods.taxpayablesum"
                                                       class="easyui-validatebox inputCss" style="color: #ff9000;"
                                                       data-options="width:100" readonly
                                                       value="${wfbondedgoodsEntity.taxpayablesum}"/>
                                            </td>
                                            <td>&nbsp;</td>
                                        </tr>
                                        <tr align="left" class="nottr">
                                            <td colspan="21" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="bondedgoodItemAdd"
                                                       style="width:100px;float:left;" value="添加一筆"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">導入樣式&nbsp;
                               <%--正式--%>
                               <a href="${ctx}/ossAdmin/download/a9358b2f8406465eb9f15f62335e6800" id="btnBatchImportTpl">參考.xls</a>
                               <%--測試--%>
                               <%-- <a href="${ctx}/admin/download/c9b69c9678dc4cefb14425af182b5393" id="btnBatchImportTpl">參考.xls</a>--%>
                            </td>
                            <td width="90%" class="td_style1">
                                <a href="#" id="batchImport" class="easyui-linkbutton" disabled="true"
                                   data-options="iconCls:'icon-hamburg-cv'" style="width: 100px;"
                                   onclick="openBatchImportWin();">批量導入</a>

                            </td>
                        </tr>
                        <tr align="center">
                            <td>申請原因描述&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1">
						    <textarea id="describtion" name="wfbondedgoods.describtion"
                                      class="easyui-validatebox"
                                      oninput="return LessThan(this);"
                                      onchange="return LessThan(this);"
                                      onpropertychange="return LessThan(this);"
                                      maxlength="500"
                                      style="width:99%;height:80px;"
                                      data-options="required:true,prompt:'請需求單位詳細說明，如欄位不夠，請附件說明'"
                                      rows="5" cols="6"
                                      data-options="required:true,validType:'length[0,500]'">${wfbondedgoodsEntity.describtion }</textarea><span
                                    id="txtNum"></span></td>
                        </tr>
                        <tr align="center">
                            <td>附件&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file"
                                           onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="wfbondedgoods.attachids"
                                       value="${wfbondedgoodsEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:if test="${file!=null&&file.size()>0}">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                    </c:if>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_shengchansunyishenqing','保稅貨物損溢申請單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl1Table"
                                                   class="qhtbodyonly">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">關務人員確認</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(83,'yl1Table','ylno1','ylname1',$('#applyfactoryid').combobox('getValue'),'wfbondedgoods')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno1" name="wfbondedgoods.ylno1"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno1']}"
                                                               readonly
                                                               value="${wfbondedgoodsEntity.ylno1 }"/><c:if
                                                            test="${requiredMap['ylno1'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname1" name="wfbondedgoods.ylname1"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno1']}"
                                                                value="${wfbondedgoodsEntity.ylname1 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   class="qhtbodyonly">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="wfbondedgoods.kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wfbondedgoodsEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="wfbondedgoods.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfbondedgoodsEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   class="qhtbodyonly">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="wfbondedgoods.bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfbondedgoodsEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="wfbondedgoods.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfbondedgoodsEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   class="qhtbodyonly">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'cchargeno','cchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wfbondedgoods.cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfbondedgoodsEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wfbondedgoods.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfbondedgoodsEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable"
                                                   class="qhtbodyonly">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applyfactoryid').combobox('getValue'),'wfbondedgoods')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wfbondedgoods.zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfbondedgoodsEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="wfbondedgoods.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfbondedgoodsEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl2Table"
                                                   class="qhtbodyonly">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">制工主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(84,'yl2Table','ylno2','ylname2',$('#applyfactoryid').combobox('getValue'),'wfbondedgoods')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="wfbondedgoods.ylno2"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}"
                                                               readonly
                                                               value="${wfbondedgoodsEntity.ylno2 }"/><c:if
                                                            test="${requiredMap['ylno2'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname2" name="wfbondedgoods.ylname2"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}"
                                                                value="${wfbondedgoodsEntity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl3Table"
                                                   class="qhtbodyonly">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產工主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(85,'yl3Table','ylno3','ylname3',$('#applyfactoryid').combobox('getValue'),'wfbondedgoods')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno3" name="wfbondedgoods.ylno3"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno3']}"
                                                               readonly
                                                               value="${wfbondedgoodsEntity.ylno3 }"/><c:if
                                                            test="${requiredMap['ylno3'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname3" name="wfbondedgoods.ylname3"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno3']}"
                                                                value="${wfbondedgoodsEntity.ylname3 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl4Table"
                                                   class="qhtbodyonly">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">關務主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(86,'yl4Table','ylno4','ylname4',$('#applyfactoryid').combobox('getValue'),'wfbondedgoods')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno4" name="wfbondedgoods.ylno4"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno4']}"
                                                               readonly
                                                               value="${wfbondedgoodsEntity.ylno4 }"/><c:if
                                                            test="${requiredMap['ylno4'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname4" name="wfbondedgoods.ylname4"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno4']}"
                                                                value="${wfbondedgoodsEntity.ylname4 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl5Table"
                                                   class="qhtbodyonly">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">企劃主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(87,'yl5Table','ylno5','ylname5',$('#applyfactoryid').combobox('getValue'),'wfbondedgoods')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno5" name="wfbondedgoods.ylno5"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno5']}"
                                                               readonly
                                                               value="${wfbondedgoodsEntity.ylno5 }"/><c:if
                                                            test="${requiredMap['ylno5'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname5" name="wfbondedgoods.ylname5"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno5']}"
                                                                value="${wfbondedgoodsEntity.ylname5 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl6Table"
                                                   class="qhtbodyonly">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產品安全處主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(88,'yl6Table','ylno6','ylname6',$('#applyfactoryid').combobox('getValue'),'wfbondedgoods')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno6" name="wfbondedgoods.ylno6"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno6']}"
                                                               readonly
                                                               value="${wfbondedgoodsEntity.ylno6 }"/><c:if
                                                            test="${requiredMap['ylno6'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname6" name="wfbondedgoods.ylname6"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno6']}"
                                                                value="${wfbondedgoodsEntity.ylname6 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl7Table"
                                                   class="qhtbodyonly">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">經管主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(62,'yl7Table','ylno7','ylname7',$('#applyfactoryid').combobox('getValue'),'wfbondedgoods')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno7" name="wfbondedgoods.ylno7"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno7']}"
                                                               readonly
                                                               value="${wfbondedgoodsEntity.ylno7 }"/><c:if
                                                            test="${requiredMap['ylno7'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname7" name="wfbondedgoods.ylname7"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno7']}"
                                                                value="${wfbondedgoodsEntity.ylname7 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable"
                                                   class="qhtbodyonly">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#applydeptno').val(),'zcchargeno','zcchargename',$('#applyfactoryid').combobox('getValue'),'wfbondedgoods')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="wfbondedgoods.zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wfbondedgoodsEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="wfbondedgoods.zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfbondedgoodsEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
</div>
<div id="optionWin" class="easyui-window" title="保稅貨物損溢申請單信息導入" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%">
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="file" type="file" style="width: 300px"
                           accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>
                    <input id="isExcel2003" name="isExcel2003" type="hidden" value="">

                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <span ID="labelListAddResult"></span><a href="${ctx}/wfbondedgoods/downLoad/errorExcel" id="downloadError"
                                                            plain="true">查看錯誤信息</a>
                </td>
            </tr>
        </table>
    </form>
</div>
<script type="text/javascript">
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            successTip(data, dg, d);
        }
    });
</script>
</body>
</html>