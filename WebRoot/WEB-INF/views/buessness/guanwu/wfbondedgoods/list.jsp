<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>保稅貨物損溢申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
        <form id="searchFrom" action="">
		<input type="text" name="filter_EQS_applyno" class="easyui-validatebox"
			   data-options="width:150,prompt: '申請人工號'"/>
		<input type="text" name="filter_EQS_applydeptno" class="easyui-validatebox"
			   data-options="width:150,prompt: '申請人單位代碼'"/>
		<input type="text" name="filter_EQS_serialno" class="easyui-validatebox"
			   data-options="width:150,prompt: '任務編碼'"/>
		<input type="text" name="filter_GED_createtime" class="easyui-my97" datefmt="yyyy-MM-dd"
			   data-options="width:150,prompt: '填單开始日期'"/>
		- <input type="text" name="filter_LED_createtime" class="easyui-my97" datefmt="yyyy-MM-dd"
				 data-options="width:150,prompt: '填單结束日期'"/>
		<input type="text" name="filter_GED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"
			   data-options="width:150,prompt: '簽核完成开始日期'"/>
		- <input type="text" name="filter_LED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"
				 data-options="width:150,prompt: '簽核完成结束日期'"/>
		<input id="qysjzt" style="width:100px" class="easyui-validatebox" name="filter_EQS_workstatus"/>
		<span class="toolbar-item dialog-tool-separator"></span>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
		   onclick="listSearchReset()">重置</a>
		<a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
		   onclick="exportExcel()">导出Excel</a>
		<input id="page" name="page" type="hidden" value="1" />
		<input id="rows" name="rows" type="hidden" value="30" />
	</form>

  </div>
<table id="dg"></table>
<div id="dlg"></div>

<script src="${ctx}/static/js/guanwu/wfbondedgoods.js?random=<%= Math.random()%>"></script>
<script type="text/javascript">
    //創建下拉查詢條件
    $.ajax({
        url: ctx+"/system/dict/getDictByType/audit_status",
        dataType:"json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#qysjzt").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
</script>
</body>
</html>