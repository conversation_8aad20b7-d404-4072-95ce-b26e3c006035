<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>保稅貨物損溢申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src='${ctx}/static/js/guanwu/wfbondedgoods.js?random=<%= Math.random()%>'></script>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfbondedgoods/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfbondedgoodsEntity.id }"/>
    <input id="serialno" name="wfbondedgoods.serialno" type="hidden" value="${wfbondedgoodsEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">保稅貨物損溢申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfbondedgoodsEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfbondedgoodsEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfbondedgoodsEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfbondedgoodsEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfbondedgoodsEntity.makerno}/${wfbondedgoodsEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
           <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td>
                                <table class="formList">
                                    <tr>
                                        <td colspan="10" class="td_style1">申請人基本信息</td>
                                    </tr>
                                    <tr align="center">
                                        <td width="6%">申請人工號</td>
                                        <td width="6%"class="td_style1">
                                            <input id="applyno" name="wfbondedgoods.applyno" class="easyui-validatebox inputCss"
                                                   data-options="width: 80" value="${wfbondedgoodsEntity.applyno}" readonly/>
                                        </td>
                                        <td width="6%">申請人姓名</td>
                                        <td width="6%" class="td_style1">
                                            <input id="applyname" name="wfbondedgoods.applyname" class="easyui-validatebox inputCss"
                                                   data-options="width:80" readonly value="${wfbondedgoodsEntity.applyname }"/>
                                        </td>
                                        <td width="6%">單位代碼</td>
                                        <td width="6%" class="td_style1">
                                            <input id="applydeptno" name="wfbondedgoods.applydeptno" class="easyui-validatebox inputCss" data-options="width: 90"
                                                   value="${wfbondedgoodsEntity.applydeptno }" readonly/>
                                        </td>
                                        <td width="6%">廠區</td>
                                        <td width="6%" class="td_style1">
                                            <input id="applyfactoryid" name="wfbondedgoods.applyfactoryid" class="easyui-combobox"
                                                   panelHeight="auto" value="${wfbondedgoodsEntity.applyfactoryid }" disabled
                                                   data-options="width: 100"/>
                                        </td>
                                        <td width="6%">費用代碼</td>
                                        <td width="6%" class="td_style1">
                                            <input id="applycostno" name="wfbondedgoods.applycostno" class="easyui-validatebox inputCss" data-options="width:80"
                                                   value="${wfbondedgoodsEntity.applycostno }" readonly/>
                                        </td>
                                    </tr>
                                    <tr align="center">
                                        <td>單位名稱</td>
                                        <td colspan="3" class="td_style1">
                                            <input id="applydeptname" name="wfbondedgoods.applydeptname" class="easyui-validatebox inputCss" data-options="width: 350"
                                                   value="${wfbondedgoodsEntity.applydeptname }"/>
                                        </td>
                                        <td>聯繫郵箱</td>
                                        <td colspan="3" class="td_style1">
                                            <input id="applyemail" name="wfbondedgoods.applyemail" class="easyui-validatebox inputCss"
                                                   value="${wfbondedgoodsEntity.applyemail}" style="width:300px;"/>
                                        </td>
                                        <td>聯繫分機</td>
                                        <td class="td_style1">
                                            <input id="applyphone" name="wfbondedgoods.applyphone" class="easyui-validatebox inputCss"
                                                   style="width:80px;" value="${wfbondedgoodsEntity.applyphone}"/>
                                        </td>
                                    </tr>
                                    <tr align="center">
                                        <td>需求日期</td>
                                        <td class="td_style1">
                                            <input id="requiredate" name="wfbondedgoods.requiredate" class="Wdate"
                                                   data-options="width:80,required:true" value="<fmt:formatDate  pattern="yyyy-MM-dd"
                                       value="${wfbondedgoodsEntity.requiredate}"/>" disabled
                                                   onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                                        </td>
                                        <td colspan="8" class="td_style1"></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
           </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="bondedgoodsItemTable" width="200%">
                                        <tr align="center">
                                            <td colspan="14">廠部填寫</td>
                                            <td colspan="6">關務填寫</td>
                                        </tr>
                                        <tr align="center">
                                            <td>&nbsp;序號&nbsp;</td>
                                            <td>法人</td>
                                            <td>幾種</td>
                                            <td>工單號</td>
                                            <td>主件料號</td>
                                            <td>完工數量</td>
                                            <td>發料料號</td>
                                            <td>發料底階料號</td>
                                            <td>用量</td>
                                            <td>單位</td>
                                            <td>應發(退)數量</td>
                                            <td>實發(退)數量</td>
                                            <td>虧料數量</td>
                                            <td>虧料比率(%)</td>
                                            <td>備案項號</td>
                                            <td>商品名稱</td>
                                            <td>採購單價(USD)</td>
                                            <td>綜合稅率(%)</td>
                                            <td>虧料貨值(USD)</td>
                                            <td>應補稅額(USD)</td>
                                        </tr>
                                        <tbody id="info_Body">
                                        <c:if test="${bondedgoodsitemEntity!=null&&bondedgoodsitemEntity.size()>0}">
                                            <c:forEach items="${bondedgoodsitemEntity}" var="bondedgoodsitem" varStatus="status">
                                                <tr align="center" id="bondedgoodsItem${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>${bondedgoodsitem.corporate}</td>
                                                    <td>${bondedgoodsitem.prtName}</td>
                                                    <td>${bondedgoodsitem.orderNo}</td>
                                                    <td>${bondedgoodsitem.partNo}</td>
                                                    <td>${bondedgoodsitem.finishNum}</td>
                                                    <td>${bondedgoodsitem.outPartNo}</td>
                                                    <td>${bondedgoodsitem.outDPartNo}</td>
                                                    <td>${bondedgoodsitem.dosage}</td>
                                                    <td>${bondedgoodsitem.unit}</td>
                                                    <td>${bondedgoodsitem.yftnum}</td>
                                                    <td>${bondedgoodsitem.sftnum}</td>
                                                    <td>${bondedgoodsitem.lossNum}</td>
                                                    <td>${bondedgoodsitem.lossRate}</td>
                                                    <td>${bondedgoodsitem.recordItem}</td>
                                                    <td>${bondedgoodsitem.proName}</td>
                                                    <td>${bondedgoodsitem.purchasePrice}</td>
                                                    <td>${bondedgoodsitem.compositeTaxrate}</td>
                                                    <td>${bondedgoodsitem.lossValue}</td>
                                                    <td>${bondedgoodsitem.taxpayable}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                        <tr class="nottr">
                                            <td colspan="18" style="text-align: right;">合計:&nbsp;&nbsp;</td>
                                            <td>
                                                <c:if test="${bondedgoodsitemEntity!=null&&bondedgoodsitemEntity.size()>0}">
                                                ${wfbondedgoodsEntity.lossvaluesum}
                                                </c:if>
                                            </td>
                                            <td>
                                                <c:if test="${bondedgoodsitemEntity!=null&&bondedgoodsitemEntity.size()>0}">
                                                    ${wfbondedgoodsEntity.taxpayablesum}
                                                </c:if>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">申請原因描述</td>
                            <td width="90%" colspan="9" class="td_style1">
						    <textarea id="describtion" name="wfbondedgoods.describtion" class="easyui-validatebox"
                                      style="width:99%;height:80px;" rows="5" cols="6">${wfbondedgoodsEntity.describtion }</textarea></td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="wfbondedgoods.attachids" value="${wfbondedgoodsEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>批註</td>
                            <td colspan="9" class="td_style1">
						    <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                      style="width:1000px;height:60px;"
                                      rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                     <table class="formList">
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wfbondedgoodsEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','保稅貨物損溢申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfbondedgoodsEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
  <div id="dlg"></div>
</body>
</html>