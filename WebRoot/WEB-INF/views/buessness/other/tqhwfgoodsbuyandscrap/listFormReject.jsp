<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>資訊（非固資類）物品新購及報廢申請</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/other/tqhwfgoodsbuyandscrap.js?random=<%= Math.random()%>'></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
        .td_style3{
            border: none 0px;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfgoodsbuyandscrap/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhWfgoodsbuyandscrap.id }"/>
    <input id="serialno" name="tQhWfgoodsbuyandscrap.serialno" type="hidden"
           value="${tQhWfgoodsbuyandscrap.serialno }"/>
    <input id="createtime" name="tQhWfgoodsbuyandscrap.createtime" type="hidden"
           value="${tQhWfgoodsbuyandscrap.createtime }"/>
    <input id="makerno" name="tQhWfgoodsbuyandscrap.makerno" type="hidden" value="${tQhWfgoodsbuyandscrap.makerno }"/>
    <input id="makername" name="tQhWfgoodsbuyandscrap.makername" type="hidden" value="${tQhWfgoodsbuyandscrap.makername }"/>
    <input id="makerdeptno" name="tQhWfgoodsbuyandscrap.makerdeptno" type="hidden" value="${tQhWfgoodsbuyandscrap.makerdeptno }"/>
    <input id="makerfactoryid" name="tQhWfgoodsbuyandscrap.makerfactoryid" type="hidden" value="${tQhWfgoodsbuyandscrap.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">資訊（非固資類）物品新購及報廢申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
            <c:choose>
                <c:when test="${tQhWfgoodsbuyandscrap.serialno==null}">
                    提交成功后自動編碼
                </c:when>
                <c:otherwise>
                    ${tQhWfgoodsbuyandscrap.serialno}
                </c:otherwise>
            </c:choose>
        </span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
            <c:choose>
                <c:when test="${tQhWfgoodsbuyandscrap.createtime==null}">
                    YYYY/MM/DD
                </c:when>
                <c:otherwise>
                    <input class="inputCss" style="width: 100px" value="<fmt:formatDate value='${tQhWfgoodsbuyandscrap.createtime}' pattern='yyyy-MM-dd hh:mm'/>" >
                </c:otherwise>
            </c:choose>
        </span>
        </div>
        <div class="position_R margin_R"> 填單人：${user.loginName}/${user.name}</div>
        <br>
        <div class="clear"></div>
        <table class="formList" id="buildprojecttable">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="5%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="applyno" name="tQhWfgoodsbuyandscrap.applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${tQhWfgoodsbuyandscrap.applyno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="tQhWfgoodsbuyandscrap.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${tQhWfgoodsbuyandscrap.applyname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="tQhWfgoodsbuyandscrap.applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${tQhWfgoodsbuyandscrap.applydeptno }"/>
                            </td>
                            <td width="3%">費用代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applycostno" name="tQhWfgoodsbuyandscrap.applycostno" class="easyui-validatebox" data-options="width: 80"
                                       value="${tQhWfgoodsbuyandscrap.applycostno }"/>
                            </td>
                            <td width="4%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="tQhWfgoodsbuyandscrap.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${tQhWfgoodsbuyandscrap.applyfactoryid }"
                                       data-options="width: 120,required:true,onSelect:function(){onchangeFactory();}"/>
                                <input id="applynofactoryid" name="tQhWfgoodsbuyandscrap.applynofactoryid" type="hidden" value="${tQhWfgoodsbuyandscrap.applynofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="tQhWfgoodsbuyandscrap.applyleveltype" class="easyui-validatebox" data-options="width: 80"
                                       value="${tQhWfgoodsbuyandscrap.applyleveltype }"/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="tQhWfgoodsbuyandscrap.applymanager" class="easyui-validatebox" data-options="width: 80"
                                       value="${tQhWfgoodsbuyandscrap.applymanager }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="tQhWfgoodsbuyandscrap.applyemail" class="easyui-validatebox"
                                       value="${tQhWfgoodsbuyandscrap.applyemail }" style="width:300px;"
                                       data-options="required:true" onblur="valdEmail(this)"/>
                            </td>
                            <td>使用區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyarea" name="tQhWfgoodsbuyandscrap.applyarea" class="easyui-combobox" data-options="width: 70,onSelect:function(){onchangeArea();}"
                                       value="${tQhWfgoodsbuyandscrap.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="tQhWfgoodsbuyandscrap.applybuilding" class="easyui-combobox" data-options="width: 70"
                                       value="${tQhWfgoodsbuyandscrap.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applytel" name="tQhWfgoodsbuyandscrap.applytel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${tQhWfgoodsbuyandscrap.applytel }" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="4" class="td_style1">
                                <input id="applydeptname" name="tQhWfgoodsbuyandscrap.applydeptname" class="easyui-validatebox" data-options="width: 400"
                                       value="${tQhWfgoodsbuyandscrap.applydeptname }"/>
                            </td>
                            <td>安保區域&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style2">
                                <div class="securityareaDiv"></div>
                                <input id="securityarea" name="tQhWfgoodsbuyandscrap.securityarea"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${tQhWfgoodsbuyandscrap.securityarea }"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="3" width="15%">申請類型&nbsp;<font color="red">*</font></td>
                            <td colspan="7" class="td_style2">
                                <div class="requiretypeDiv"></div>
                                <input id="requiretype" name="tQhWfgoodsbuyandscrap.requiretype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${tQhWfgoodsbuyandscrap.requiretype }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="height: 100%;width: 100%">
                                <div style="overflow-x: auto;width: 100%;">
                                    <%------------------------------------周邊設備及配件類-----------------------------------------%>
                                    <input id="zbItemTableIndex" type="hidden"
                                           value="<c:if test="${zbItemEntity!=null&&zbItemEntity.size()>0}">${zbItemEntity.size() + 1}</c:if>
                                        <c:if test="${zbItemEntity==null}">2</c:if>">
                                    </input>
                                    <table width="100%" id="zbItemTable">
                                        <tr align="center">
                                            <td width="8%">物品類別</td>
                                            <td width="5%">序號</td>
                                            <td width="20%">物品名稱&nbsp;<font color="red">*</font></td>
                                            <td width="10%">品牌&nbsp;<font color="red">*</font></td>
                                            <td width="15%">規格型號&nbsp;<font color="red">*</font></td>
                                            <td width="15%">編號&序列號</td>
                                            <td width="10%">數量&nbsp;<font color="red">*</font></td>
                                            <td width="5%">操作</td>
                                        </tr>
                                        <c:if test="${zbItemEntity!=null&&zbItemEntity.size()>0}">
                                            <c:forEach items="${zbItemEntity}" var="zbItem" varStatus="status">
                                                <tr align="center" id="zbItem${status.index+1}">
                                                    <c:if test="${status.index==0}">
                                                        <td rowspan="${zbItemEntity.size()+1}" class="zb_kh"><input type="checkbox" value="zb" onclick="checked_zb()" name="tQhWfgoodsbuyandscrap.zbgoodstype"  <c:if test="${tQhWfgoodsbuyandscrap.zbgoodstype=='zb'}">checked</c:if>/>周邊設備及配件類</td>
                                                    </c:if>
                                                    <td>${status.index+1}</td>
                                                    <td><input id="zb_goodsname${status.index+1}" name="tQhWfgoodsbuyandscrapzbitems[${status.index}].goodsname"
                                                               class="easyui-combobox" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadzbGoodsname(${status.index+1});},onSelect:function(){onchangezbGoodsname(${status.index+1});}" style="width:80px;"
                                                               value="${zbItem.goodsname}"/>&nbsp;&nbsp;
                                                        <input id="zb_othername${status.index+1}" name="tQhWfgoodsbuyandscrapzbitems[${status.index}].othername"
                                                               class="easyui-validatebox" data-options="prompt:'選擇其他時必填'" style="width:100px;"
                                                               value="${zbItem.othername}"/>
                                                    </td>
                                                    <td>
                                                        <input id="zb_brand${status.index+1}" name="tQhWfgoodsbuyandscrapzbitems[${status.index}].brand"
                                                               class="easyui-validatebox"  style="width:100px;"
                                                               value="${zbItem.brand}"/></td>
                                                    <td><input id="zb_specific${status.index+1}" name="tQhWfgoodsbuyandscrapzbitems[${status.index}].specific"
                                                               class="easyui-validatebox"  style="width:100px;"
                                                               value="${zbItem.specific}"/><font color="red">*</font></td>
                                                    <td><input id="zb_serialnumber${status.index+1}" name="tQhWfgoodsbuyandscrapzbitems[${status.index}].serialnumber"
                                                               class="easyui-validatebox"  style="width:100px;"
                                                               value="${zbItem.serialnumber}"/></td>
                                                    <td><input id="zb_goodsnum${status.index+1}" name="tQhWfgoodsbuyandscrapzbitems[${status.index}].goodsnum"
                                                               class="easyui-validatebox"  style="width:80px;"
                                                               value="${zbItem.goodsnum}"/></td>
                                                    <td><input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="zbdeltr(${status.index+1});return false;"/></td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${zbItemEntity==null}">
                                            <tr align="center" id="zbItem1">
                                                <td rowspan="1" class="zb_kh"><input type="checkbox" value="zb" name="tQhWfgoodsbuyandscrap.zbgoodstype"  onclick="checked_zb()"/>周邊設備及配件類</td>
                                                <td>1</td>
                                                <td>
                                                    <input id="zb_goodsname1" name="tQhWfgoodsbuyandscrapzbitems[0].goodsname" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadzbGoodsname(1);},onSelect:function(){onchangezbGoodsname(1);}" style="width:80px;"
                                                           class="easyui-combobox" editable="false" value=""/>&nbsp;&nbsp;
                                                    <input id="zb_othername1" name="tQhWfgoodsbuyandscrapzbitems[0].othername"
                                                           class="easyui-validatebox"  style="width:100px;" value="" data-options="prompt:'選擇其他時必填'"/>
                                                </td>
                                                <td><input id="zb_brand1" name="tQhWfgoodsbuyandscrapzbitems[0].brand"
                                                           class="easyui-validatebox"  style="width:100px;" value="" /></td>
                                                <td><input id="zb_specific1" name="tQhWfgoodsbuyandscrapzbitems[0].specific"
                                                           class="easyui-validatebox"  style="width:100px;" value=""/></td>
                                                <td><input id="zb_serialnumber1" name="tQhWfgoodsbuyandscrapzbitems[0].serialnumber"
                                                           class="easyui-validatebox"  style="width:100px;" value=""/></td>
                                                <td><input id="zb_goodsnum1" name="tQhWfgoodsbuyandscrapzbitems[0].goodsnum" onblur="valdIsNumber(this)"
                                                           class="easyui-validatebox"  style="width:80px;" value=""/></td>
                                                <td><input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="zbdeltr(1);return false;"/></td>
                                            </tr>
                                        </c:if>
                                        <tr id="addbutton_zb">
                                            <td colspan="7" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="zbItemAdd" style="width:100px;float:left;" value="添加一行"/>
                                            </td>
                                        </tr>
                                    </table>
                                    <%-----------------------------------------資安管制類------------------------------------------%>
                                    <input id="zaItemTableIndex" type="hidden"
                                           value="<c:if test="${zaItemEntity!=null&&zaItemEntity.size()>0}">${zaItemEntity.size() + 1}</c:if>
                                        <c:if test="${zaItemEntity==null}">2</c:if>">
                                    </input>
                                    <table width="100%" id="zaItemTable">
                                        <tr align="center">
                                            <c:if test="${zaItemEntity!=null&&zaItemEntity.size()>0}">
                                                <td width="8%" rowspan="${zaItemEntity.size()+2}" class="za_kh"><input type="checkbox" onclick="checked_za()" name="tQhWfgoodsbuyandscrap.zagoodstype" value="za" <c:if test="${tQhWfgoodsbuyandscrap.zagoodstype=='za'}">checked</c:if> />資安管制類</td>
                                            </c:if>
                                            <c:if test="${zaItemEntity==null}">
                                                <td width="8%" rowspan="3" class="za_kh"><input type="checkbox" value="za" onclick="checked_za()" name="tQhWfgoodsbuyandscrap.zagoodstype"/>資安管制類</td>
                                            </c:if>
                                            <td width="5%">序號</td>
                                            <td width="20%">物品名稱&nbsp;<font color="red">*</font></td>
                                            <td width="10%">品牌&nbsp;<font color="red">*</font></td>
                                            <td width="15%">規格型號&nbsp;<font color="red">*</font></td>
                                            <td width="15%">編號&序列號</td>
                                            <td width="10%">數量&nbsp;<font color="red">*</font></td>
                                            <td width="5%">操作</td>
                                        </tr>
                                        <c:if test="${zaItemEntity!=null&&zaItemEntity.size()>0}">
                                            <c:forEach items="${zaItemEntity}" var="zaItem" varStatus="status">
                                                <tr align="center" id="zaItem${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td><input id="za_goodsname${status.index+1}" name="tQhWfgoodsbuyandscrapzaitems[${status.index}].goodsname"
                                                               class="easyui-combobox" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadzaGoodsname(${status.index+1});},onSelect:function(){onchangezaGoodsname(${status.index+1});}" style="width:80px;"
                                                               value="${zaItem.goodsname}"/>&nbsp;&nbsp;
                                                        <input id="za_othername${status.index+1}" name="tQhWfgoodsbuyandscrapzaitems[${status.index}].othername"
                                                               class="easyui-validatebox" data-options="prompt:'選擇其他時必填'" style="width:100px;"
                                                               value="${zaItem.othername}"/>
                                                    </td>
                                                    <td>
                                                        <input id="za_brand${status.index+1}" name="tQhWfgoodsbuyandscrapzaitems[${status.index}].brand"
                                                               class="easyui-validatebox"  style="width:100px;"
                                                               value="${zaItem.brand}"/></td>
                                                    <td><input id="za_specific${status.index+1}" name="tQhWfgoodsbuyandscrapzaitems[${status.index}].specific"
                                                               class="easyui-validatebox"  style="width:100px;"
                                                               value="${zaItem.specific}"/></td>
                                                    <td><input id="za_serialnumber${status.index+1}" name="tQhWfgoodsbuyandscrapzaitems[${status.index}].serialnumber"
                                                               class="easyui-validatebox"  style="width:100px;"
                                                               value="${zaItem.serialnumber}"/></td>
                                                    <td><input id="za_goodsnum${status.index+1}" name="tQhWfgoodsbuyandscrapzaitems[${status.index}].goodsnum"
                                                               class="easyui-validatebox"  style="width:80px;"
                                                               value="${zaItem.goodsnum}"/></td>
                                                    <td><input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="zadeltr(${status.index+1});return false;"/></td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${zaItemEntity==null}">
                                            <tr align="center" id="zaItem1">
                                                <td>1</td>
                                                <td>
                                                    <input id="za_goodsname1" name="tQhWfgoodsbuyandscrapzaitems[0].goodsname" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadzaGoodsname(1);},onSelect:function(){onchangezaGoodsname(1);}" style="width:80px;"
                                                           class="easyui-combobox" editable="false" value="" />
                                                    &nbsp;&nbsp;
                                                    <input id="za_othername1" name="tQhWfgoodsbuyandscrapzaitems[0].othername"
                                                           class="easyui-validatebox" data-options="prompt:'選擇其他時必填'" style="width:100px;" value=""/>
                                                </td>
                                                <td><input id="za_brand1" name="tQhWfgoodsbuyandscrapzaitems[0].brand"
                                                           class="easyui-validatebox"  style="width:100px;"
                                                           value=""/></td>
                                                <td><input id="za_specific1" name="tQhWfgoodsbuyandscrapzaitems[0].specific"
                                                           class="easyui-validatebox"  style="width:100px;"
                                                           value=""/></td>
                                                <td><input id="za_serialnumber1" name="tQhWfgoodsbuyandscrapzaitems[0].serialnumber"
                                                           class="easyui-validatebox"  style="width:100px;"
                                                           value=""/></td>
                                                <td><input id="za_goodsnum1" name="tQhWfgoodsbuyandscrapzaitems[0].goodsnum"
                                                           class="easyui-validatebox"  style="width:80px;" onblur="valdIsNumber(this)"
                                                           value=""/></td>
                                                <td><input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="zadeltr(1);return false;"/></td>
                                            </tr>
                                        </c:if>
                                        <tr id="addbutton_za">
                                            <td colspan="7" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="zaItemAdd" style="width:100px;float:left;" value="添加一行"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="3">需求說明&nbsp;<font color="red">*</font></td>
                            <td colspan="7" class="td_style1">
                                <textarea id="describtion" name="tQhWfgoodsbuyandscrap.describtion"
                                          class="easyui-validatebox" style="width:800px;height:80px;" rows="5" cols="6"
                                          data-options="required:true">${tQhWfgoodsbuyandscrap.describtion}</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="3">附件</td>
                            <td colspan="7" class="td_style1">
						        <span class="sl-custom-file">
						            <input type="button" value="点击上传文件" class="btn-file"/>
						            <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file">
						<%--<input type="button" value="上传" onclick="uploadFile();"/>--%>
						        </span>
                                <input type="hidden" id="attachids" name="tQhWfgoodsbuyandscrap.attachids" value="${tQhWfgoodsbuyandscrap.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                                <%--<div class="clear"></div>--%>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>

                        <tr align="center">
                            <td colspan="3">備註</td>
                            <td colspan="7" class="td_style2">
                                1、此表僅作為資訊類非固資物品報廢后﹐提交請購需求時的依據﹐簽核完成后請將此表單列印保存，并在電子簽核系統中將此表單作為附件上傳﹐供各級主管審核時評估參考；<br/>
                                2、資安管制類物品的報廢鑒定需簽至資安人員﹐且報廢之實物交由資安課統一回收處理；<br/>
                                3、硬盤請購及電腦報廢之前必須將報廢硬盤先送至資安課消磁處理后方可進行請購或報廢作業。
                            </td>
                        </tr>

                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)" onclick="showWfImag('${workFlowId}','資訊（非固資類）物品新購及報廢申請單流程圖','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="ywkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ywkchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'ywkchargeTable','ywkchargeno','ywkchargename',$('#applyfactoryid').combobox('getValue'),'tQhWfgoodsbuyandscrap')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="ywkchargeno" name="tQhWfgoodsbuyandscrap.ywkchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:true" readonly
                                                               value="${tQhWfgoodsbuyandscrap.ywkchargeno }"/>
                                                        <c:if test="${requiredMap['ywkchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        /<input id="ywkchargename"
                                                                name="tQhWfgoodsbuyandscrap.ywkchargename"
                                                                class="easyui-validatebox"
                                                                data-options="width: 80,required:true" readonly
                                                                value="${tQhWfgoodsbuyandscrap.ywkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="bchargeno" name="tQhWfgoodsbuyandscrap.bchargeno"
                                                               class="easyui-validatebox" data-options="width: 80"
                                                               readonly
                                                               value="${tQhWfgoodsbuyandscrap.bchargeno }"/>
                                                        <c:if test="${requiredMap['bchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        /<input id="bchargename"
                                                                name="tQhWfgoodsbuyandscrap.bchargename"
                                                                class="easyui-validatebox" data-options="width: 80"
                                                                readonly
                                                                value="${tQhWfgoodsbuyandscrap.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#applydeptno').val(),'cchargeno','cchargename',$('#applynofactoryid').val(),'tQhWfgoodsbuyandscrap')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="cchargeno" name="tQhWfgoodsbuyandscrap.cchargeno"
                                                               class="easyui-validatebox" data-options="width: 80"
                                                               readonly
                                                               value="${tQhWfgoodsbuyandscrap.cchargeno }"/>
                                                        <c:if test="${requiredMap['cchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        /<input id="cchargename"
                                                                name="tQhWfgoodsbuyandscrap.cchargename"
                                                                class="easyui-validatebox" data-options="width: 80"
                                                                readonly
                                                                value="${tQhWfgoodsbuyandscrap.cchargename}"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zachargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zachargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(23,'zachargeTable','zachargeno','zachargename',$('#applyfactoryid').combobox('getValue'),'tQhWfgoodsbuyandscrap')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zachargeno" name="tQhWfgoodsbuyandscrap.zachargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:true" readonly
                                                               value="${tQhWfgoodsbuyandscrap.zachargeno }"/>
                                                        <span id="zaRedDiv"><font color="red">*</font></span>/
                                                        <input id="zachargename"
                                                               name="tQhWfgoodsbuyandscrap.zachargename"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:true" readonly
                                                               value="${tQhWfgoodsbuyandscrap.zachargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="kgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['kgchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(24,'kgchargeTable','kgchargeno','kgchargename',$('#applyfactoryid').combobox('getValue'),'tQhWfgoodsbuyandscrap')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="kgchargeno" name="tQhWfgoodsbuyandscrap.kgchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:true" readonly
                                                               value="${tQhWfgoodsbuyandscrap.kgchargeno }"/>
                                                        <c:if test="${requiredMap['kgchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>/
                                                        <input id="kgchargename"
                                                               name="tQhWfgoodsbuyandscrap.kgchargename"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:true" readonly
                                                               value="${tQhWfgoodsbuyandscrap.kgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="ywbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ywbchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(25,'ywbchargeTable','ywbchargeno','ywbchargename',$('#applyfactoryid').combobox('getValue'),'tQhWfgoodsbuyandscrap')"></div>

                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="ywbchargeno" name="tQhWfgoodsbuyandscrap.ywbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:true" readonly
                                                               value="${tQhWfgoodsbuyandscrap.ywbchargeno }"/>
                                                        <c:if test="${requiredMap['ywbchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>/
                                                        <input id="ywbchargename"
                                                               name="tQhWfgoodsbuyandscrap.ywbchargename"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:true" readonly
                                                               value="${tQhWfgoodsbuyandscrap.ywbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhWfgoodsbuyandscrap.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton" data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="canelTask('${tQhWfgoodsbuyandscrap.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" name="tQhWfgoodsbuyandscrap.infoassess" value="${tQhWfgoodsbuyandscrap.infoassess}">
    <input type="hidden" name="tQhWfgoodsbuyandscrap.isoldthings" value="${tQhWfgoodsbuyandscrap.isoldthings}">
    <input type="hidden" name="tQhWfgoodsbuyandscrap.isdegauss" value="${tQhWfgoodsbuyandscrap.isdegauss}">
    <input type="hidden" name="tQhWfgoodsbuyandscrap.degaussnum" value="${tQhWfgoodsbuyandscrap.degaussnum}">
    <input type="hidden" name="tQhWfgoodsbuyandscrap.isscrap" value="${tQhWfgoodsbuyandscrap.isscrap}">
    <input type="hidden" name="tQhWfgoodsbuyandscrap.scrapnum" value="${tQhWfgoodsbuyandscrap.scrapnum}">
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="areaId" name="areaId" value=""/>
    <input type="hidden" id="buildingId" name="buildingId" value=""/>
    <input type="hidden" id="onlyKchargeSignle" value="1" />
    <div id="win"></div>
</form>
</body>
</html>
