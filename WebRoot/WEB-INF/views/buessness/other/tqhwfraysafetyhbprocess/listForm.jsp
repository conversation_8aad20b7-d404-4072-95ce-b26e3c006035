<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>輻射安全許可證環保手續辦理委託申請單</title>
	<script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
	</script>
	<%@ include file="/WEB-INF/views/include/easyui.jsp" %>
	<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
	<script src='${ctx}/static/js/other/tqhwfraysafetyhbprocess.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfraysafetyhbprocess/${action}" method="post">
	<input id="ids" name="ids" type="hidden" value="${tQhWfraysafetyhbprocess.id }"/>
	<input id="serialno" name="tQhWfraysafetyhbprocess.serialno" type="hidden"
		   value="${tQhWfraysafetyhbprocess.serialno }"/>
	<input id="createtime" name="tQhWfraysafetyhbprocess.createtime" type="hidden"
		   value="${tQhWfraysafetyhbprocess.createtime }"/>
	<input id="makerno" name="tQhWfraysafetyhbprocess.makerno" type="hidden" value="${tQhWfraysafetyhbprocess.makerno }"/>
	<input id="makername" name="tQhWfraysafetyhbprocess.makername" type="hidden" value="${tQhWfraysafetyhbprocess.makername }"/>
	<input id="makerdeptno" name="tQhWfraysafetyhbprocess.makerdeptno" type="hidden" value="${tQhWfraysafetyhbprocess.makerdeptno }"/>
	<input id="makerfactoryid" name="tQhWfraysafetyhbprocess.makerfactoryid" type="hidden" value="${tQhWfraysafetyhbprocess.makerfactoryid }"/>
	<div class="commonW">
		<div class="headTitle">輻射安全環保手續辦理委託申請單</div>
		<div class="position_L">
			任務編碼：<span style="color:#999;">
			<c:choose>
				<c:when test="${tQhWfraysafetyhbprocess.serialno==null}">
					提交成功后自動編碼
				</c:when>
				<c:otherwise>
					${tQhWfraysafetyhbprocess.serialno}
				</c:otherwise>
			</c:choose></span>
		</div>
		<div class="position_L1 margin_L">
			填單時間：<span style="color:#999;">
			<c:choose>
				<c:when test="${tQhWfraysafetyhbprocess.createtime==null}">
					YYYY/MM/DD
				</c:when>
				<c:otherwise>
					<input class="inputCss" style="width: 100px" value="<fmt:formatDate value='${tQhWfraysafetyhbprocess.createtime}' pattern='yyyy-MM-dd hh:mm'/>" >
				</c:otherwise>
			</c:choose>
		</span>
		</div>
		<c:if test="${empty tQhWfraysafetyhbprocess.makerno}">
			<div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
		</c:if>
		<c:if test="${not empty tQhWfraysafetyhbprocess.makerno}">
			<div class="position_R margin_R">填單人：${tQhWfraysafetyhbprocess.makerno}/${tQhWfraysafetyhbprocess.makername}</div>
		</c:if>
		<br>
		<div class="clear"></div>
		<table class="formList" id="buildprojecttable">
			<tr>
				<td>
					<table class="formList">
						<tr>
							<td colspan="10" class="td_style1">承辦人詳細信息</td>
						</tr>
						<tr align="center">
							<td width="6%">承辦人工號&nbsp;<font color="red">*</font></td>
							<td width="6%" class="td_style1">
								<input id="dealno" name="tQhWfraysafetyhbprocess.dealno" class="easyui-validatebox"
									   data-options="width: 80,required:true"
									   value="${tQhWfraysafetyhbprocess.dealno }" onblur="queryUserInfo(this);"/>
							</td>
							<td width="6%">承辦人</td>
							<td width="6%" class="td_style1">
								<input id="dealname" name="tQhWfraysafetyhbprocess.dealname"
									   class="easyui-validatebox inputCss"
									   data-options="width:80" readonly value="${tQhWfraysafetyhbprocess.dealname }"/>
							</td>
							<td width="6%">單位代碼</td>
							<td width="6%" class="td_style1">
								<input id="dealdeptno" name="tQhWfraysafetyhbprocess.dealdeptno"
									   class="easyui-validatebox inputCss" data-options="width: 90"
									   readonly value="${tQhWfraysafetyhbprocess.dealdeptno }"/>
							</td>
							<td width="6%">提報日期&nbsp;<font color="red">*</font></td>
							<td width="6%" class="td_style1">
								<input id="dealtime" name="tQhWfraysafetyhbprocess.dealtime" class="easyui-my97"
									   datefmt="yyyy-MM-dd" data-options="width: 100,required:true"
									   value="<fmt:formatDate value="${tQhWfraysafetyhbprocess.dealtime}"/>"/>
							</td>
							<td width="6%">廠區&nbsp;<font color="red">*</font></td>
							<td width="6%" class="td_style1">
								<input id="dealfactoryid" name="tQhWfraysafetyhbprocess.dealfactoryid" class="easyui-combobox"
									   panelHeight="auto" value="${tQhWfraysafetyhbprocess.dealfactoryid }"
									   data-options="width: 120,required:true"/>
							</td>
						</tr>
						<tr align="center">
							<td>單位</td>
							<td colspan="3" class="td_style1">
								<input id="dealdeptname" name="tQhWfraysafetyhbprocess.dealdeptname"
									   class="easyui-validatebox" data-options="width: 400"
									   value="${tQhWfraysafetyhbprocess.dealdeptname }"/>
							</td>
							<td>聯繫郵箱&nbsp;<font color="red">*</font></td>
							<td colspan="3" class="td_style1">
								<input id="dealemail" name="tQhWfraysafetyhbprocess.dealemail" class="easyui-validatebox"
									   value="${tQhWfraysafetyhbprocess.dealemail }" style="width:300px;"
									   data-options="required:true" onblur="valdEmail(this)"/>
							</td>
							<td>聯繫分機&nbsp;<font color="red">*</font></td>
							<td class="td_style1">
								<input id="dealtel" name="tQhWfraysafetyhbprocess.dealtel" class="easyui-validatebox"
									   style="width:90px;"
									   value="${tQhWfraysafetyhbprocess.dealtel }" data-options="required:true,prompt:'579+66666'"
									   onblur="valdApplyTel(this)"/>
							</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td>
					<table class="formList">
						<tr>
							<td colspan="10" class="td_style1">項目基本信息</td>
						</tr>

						<tr align="center">
							<td colspan="3">法人名稱&nbsp;<font color="red">*</font></td>
							<td colspan="7" class="td_style1">
								<input id="corporateid" name="tQhWfraysafetyhbprocess.corporateid" data-options="width: 300,required:true"
									   class="easyui-combobox" panelHeight="auto" editable="false" value="${tQhWfraysafetyhbprocess.corporateid }"/>
							</td>
						</tr>
						<tr align="center">
							<td colspan="3">是否具有輻射安全許可證&nbsp;<font color="red">*</font></td>
							<td colspan="2">
								<div class="ishavepermitDiv"></div>
								<input id="ishavepermit" name="tQhWfraysafetyhbprocess.ishavepermit"
									   type="hidden" class="easyui-validatebox" data-options="width: 150"
									   value="${tQhWfraysafetyhbprocess.ishavepermit }"/>

							</td>
							<td colspan="3">建設性質&nbsp;<font color="red">*</font></td>
							<td colspan="2">
								<div class="buildpropertiesDiv"></div>
								<input id="buildproperties" name="tQhWfraysafetyhbprocess.buildproperties"
									   type="hidden" class="easyui-validatebox" data-options="width: 150"
									   value="${tQhWfraysafetyhbprocess.buildproperties }"/>
							</td>
						</tr>
						<tr align="center">
							<td colspan="10" width="100%">
								<div style="overflow-x: auto;width: 100%;padding-bottom: 15px;">
									<input id="raysafetyhbCostItemTableIndex" type="hidden"
										   value="<c:if test="${deviceItemEntity!=null&&deviceItemEntity.size()>0}">${deviceItemEntity.size() + 1}</c:if>
                            <c:if test="${deviceItemEntity==null}">2</c:if>">
									</input>
									<table id="raysafetyhbCostItemTable" width="100%">
										<tr align="center">
											<td>序號</td>
											<td>費用掛靠單位&nbsp;<font color="red">*</font></td>
											<td>費用代碼&nbsp;<font color="red">*</font></td>
											<td>費用佔比(%)</td>
											<td>操作</td>
										</tr>
										<c:if test="${costItemEntity!=null&&costItemEntity.size()>0}">
											<c:forEach items="${costItemEntity}" var="costItem" varStatus="status">
												<tr align="center" id="costItem${status.index+1}">
													<td>${status.index+1}</td>
													<td><input id="costname${status.index+1}"
															   name="tQhWfraysafetyhbcostitems[${status.index}].costname"
															   class="easyui-validatebox" data-options="required:true"
															   style="width:400px;"
															   value="${costItem.costname}"/><font color="red">*</font></td>
													<td><input id="costno${status.index+1}"
															   name="tQhWfraysafetyhbcostitems[${status.index}].costno"
															   class="easyui-validatebox" data-options="required:true"
															   style="width:200px;"
															   value="${costItem.costno}"/><font color="red">*</font></td>
													<td>
														<input id="costrate${status.index}"
															   name="tQhWfraysafetyhbcostitems[${status.index}].costrate"
															   class="easyui-validatebox" style="width:200px;"
															   value="${costItem.costrate}"/>
													</td>
													<td><input type="image" src="${ctx}/static/images/deleteRow.png"
															   onclick="costdeltr(${status.index+1});return false;"/></td>
												</tr>
											</c:forEach>
										</c:if>
										<c:if test="${costItemEntity==null}">
											<tr align="center" id="costItem1">
												<td>1</td>
												<td><input id="costname1" name="tQhWfraysafetyhbcostitems[0].costname"
														   class="easyui-validatebox" data-options="required:true" style="width:400px;"
														   value=""/></td>
												<td><input id="costno1" name="tQhWfraysafetyhbcostitems[0].costno"
														   class="easyui-validatebox" data-options="required:true" style="width:200px;"
														   value=""/></td>
												<td>
													<input id="costrate1" name="tQhWfraysafetyhbcostitems[0].costrate"
														   class="easyui-validatebox" style="width:200px;" value="" onblur="valdIsNumber(this)"/>
												</td>
												<td><input type="image" src="${ctx}/static/images/deleteRow.png"
														   onclick="costdeltr(1);return false;"/></td>
											</tr>
										</c:if>
									</table>
								</div>
							</td>
						</tr>
						<tr align="left">
							<td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
								<input type="button" id="costItemAdd" style="width:100px;float:left;" value="添加一筆"/>
							</td>
						</tr>
						<tr align="center">
							<td colspan="10" width="100%">
								<div style="overflow-x: auto;width: 100%;padding-bottom: 15px;">
									<input id="raysafetyhbItemTableIndex" type="hidden"
										   value="<c:if test="${deviceItemEntity!=null&&deviceItemEntity.size()>0}">${deviceItemEntity.size() + 1}</c:if>
                            <c:if test="${deviceItemEntity==null}">2</c:if>">
									</input>
									<table id="raysafetyhbItemTable" width="150%">
										<tr align="center">
											<td>序號</td>
											<td>射線裝置<br>名稱&nbsp;<font color="red">*</font></td>
											<td>射線裝置<br>型號&nbsp;<font color="red">*</font></td>
											<td>射線裝置<br>數量&nbsp;<font color="red">*</font></td>
											<td>工作場所名稱<br>及周邊環境描述&nbsp;<font color="red">*</font></td>
											<td>占地面積（㎡）<font color="red">*</font></td>
											<td>項目投資<br>（萬元）<font color="red">*</font></td>
											<td>環保投資<br>（萬元）<font color="red">*</font></td>
											<td>設備來源&nbsp;<font color="red">*</font></td>
											<td>設備類別&nbsp;<font color="red">*</font></td>
											<td>用途&nbsp;<font color="red">*</font></td>
											<td>備註&nbsp;<font color="red">*</font></td>
											<td>操作</td>
										</tr>
										<c:if test="${deviceItemEntity!=null&&deviceItemEntity.size()>0}">
											<c:forEach items="${deviceItemEntity}" var="deviceItem" varStatus="status">
												<tr align="center" id="deviceItem${status.index+1}">
													<td>${status.index+1}</td>
													<td><input id="devicename${status.index+1}" name="tQhWfraysafetyhbitems[${status.index}].devicename"
															   class="easyui-validatebox" data-options="required:true" style="width:100px;"
															   value="${deviceItem.devicename}"/></td>
													<td><input id="devicetype${status.index+1}" name="tQhWfraysafetyhbitems[${status.index}].devicetype"
															   class="easyui-validatebox" data-options="required:true" style="width:100px;"
															   value="${deviceItem.devicetype}"/></td>
													<td><input id="devicenumber${status.index+1}" name="tQhWfraysafetyhbitems[${status.index}].devicenumber"
															   class="easyui-validatebox" data-options="required:true" style="width:80px;" onblur="valdMoney(this)"
															   value="${deviceItem.devicenumber}"/></td>
													<td><input id="nameanddescription${status.index+1}" name="tQhWfraysafetyhbitems[${status.index}].nameanddescription"
															   class="easyui-validatebox" data-options="required:true" style="width:100px;"
															   value="${deviceItem.nameanddescription}"/></td>
													<td><input id="floorspace${status.index+1}" name="tQhWfraysafetyhbitems[${status.index}].floorspace"
															   class="easyui-validatebox" data-options="required:true" style="width:80px;" onblur="valdMoney(this)"
															   value="${deviceItem.floorspace}"/></td>
													<td><input id="projectcost${status.index+1}" name="tQhWfraysafetyhbitems[${status.index}].projectcost"
															   class="easyui-validatebox" data-options="required:true" style="width:80px;" onblur="valdMoney(this)"
															   value="${deviceItem.projectcost}"/></td>
													<td><input id="enviromentalcost${status.index+1}" name="tQhWfraysafetyhbitems[${status.index}].enviromentalcost" onblur="valdMoney(this)"
															   class="easyui-validatebox" data-options="required:true" style="width:80px;"
															   value="${deviceItem.enviromentalcost}"/></td>
													<td><input id="equipmentsource${status.index+1}" name="tQhWfraysafetyhbitems[${status.index}].equipmentsource"
															   class="easyui-validatebox" data-options="required:true" style="width:100px;"
															   value="${deviceItem.equipmentsource}"/></td>
													<td>
														<input id="equipmenttype${status.index+1}" name="tQhWfraysafetyhbitems[${status.index}].equipmenttype" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadEquipmentType(${status.index+1});}" style="width:80px;"
															   class="easyui-combobox" editable="false" value="${deviceItem.equipmenttype}"/>
													</td>
													<td><input id="deviceuse${status.index+1}" name="tQhWfraysafetyhbitems[${status.index}].deviceuse"
															   class="easyui-validatebox" data-options="required:true" style="width:100px;"
															   value="${deviceItem.deviceuse}"/></td>
													<td><input id="remark${status.index+1}" name="tQhWfraysafetyhbitems[${status.index}].remark"
															   class="easyui-validatebox" data-options="required:true" style="width:100px;"
															   value="${deviceItem.remark}"/></td>
													<td><input type="image" src="${ctx}/static/images/deleteRow.png"
															   onclick="deviceItemdeltr(${status.index+1});return false;"/></td>
												</tr>
											</c:forEach>
										</c:if>
										<c:if test="${deviceItemEntity==null}">
											<tr align="center" id="deviceItem1">
												<td>1</td>
												<td><input id="devicename1" name="tQhWfraysafetyhbitems[0].devicename"
														   class="easyui-validatebox" data-options="required:true" style="width:100px;"
														   value=""/></td>
												<td><input id="devicetype1" name="tQhWfraysafetyhbitems[0].devicetype"
														   class="easyui-validatebox" data-options="required:true" style="width:100px;"
														   value=""/></td>
												<td><input id="devicenumber1" name="tQhWfraysafetyhbitems[0].devicenumber"
														   class="easyui-validatebox" data-options="required:true" style="width:80px;" onblur="valdMoney(this)"
														   value=""/></td>
												<td><input id="nameanddescription1" name="tQhWfraysafetyhbitems[0].nameanddescription"
														   class="easyui-validatebox" data-options="required:true" style="width:100px;"
														   value=""/></td>
												<td><input id="floorspace1" name="tQhWfraysafetyhbitems[0].floorspace"
														   class="easyui-validatebox" data-options="required:true" style="width:80px;" onblur="valdMoney(this)"
														   value=""/></td>
												<td><input id="projectcost1" name="tQhWfraysafetyhbitems[0].projectcost"
														   class="easyui-validatebox" data-options="required:true" style="width:80px;" onblur="valdMoney(this)"
														   value=""/></td>
												<td><input id="enviromentalcost1" name="tQhWfraysafetyhbitems[0].enviromentalcost" onblur="valdMoney(this)"
														   class="easyui-validatebox" data-options="required:true" style="width:80px;"
														   value=""/></td>
												<td><input id="equipmentsource1" name="tQhWfraysafetyhbitems[0].equipmentsource"
														   class="easyui-validatebox" data-options="required:true" style="width:100px;"
														   value=""/></td>
												<td>
													<input id="equipmenttype1" name="tQhWfraysafetyhbitems[0].equipmenttype" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadEquipmentType(1);}" style="width:80px;"
														   class="easyui-combobox" editable="false" value=""/>
												</td>
												<td><input id="deviceuse1" name="tQhWfraysafetyhbitems[0].deviceuse"
														   class="easyui-validatebox" data-options="required:true" style="width:100px;"
														   value=""/></td>
												<td><input id="remark1" name="tQhWfraysafetyhbitems[0].remark"
														   class="easyui-validatebox" data-options="required:true" style="width:100px;"
														   value=""/></td>
												<td><input type="image" src="${ctx}/static/images/deleteRow.png"
														   onclick="deviceItemdeltr(1);return false;"/></td>
											</tr>
										</c:if>
									</table>
								</div>
							</td>
						</tr>
						<tr align="left">
							<td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
								<input type="button" id="deviceItemAdd" style="width:100px;float:left;" value="添加一筆"/>
							</td>
						</tr>
						<tr align="center">
							<td colspan="2">附件</td>
							<td colspan="8" class="td_style1">
						<span class="sl-custom-file">
						<input type="button" value="点击上传文件" class="btn-file"/>
						<input id="attachidsUpload" name="attachidsUpload"
							   type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file">
						</span>
								<input type="hidden" id="attachids" name="tQhWfraysafetyhbprocess.attachids" value="${tQhWfraysafetyhbprocess.attachids }"/>
								<div id="dowloadUrl">
									<c:forEach items="${file}" varStatus="i" var="item">
										<div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
											<div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
											<div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
										</div>
									</c:forEach>
								</div>
							</td>
						</tr>

						<tr align="center">
							<td colspan="2">委託事項說明&nbsp;<font color="red">*</font></td>
							<td colspan="8" class="td_style1">
						<textarea id="entrustexplain" name="tQhWfraysafetyhbprocess.entrustexplain"
								  class="easyui-validatebox" style="width:800px;height:80px;" rows="5" cols="6"
								  data-options="required:true,prompt:'例：1.委託太原周邊環保科技處環保手續辦理(可研立項，環評、竣工環保驗收)事宜；2.委託太原周邊總務處議價及合同簽訂事宜。'">${tQhWfraysafetyhbprocess.entrustexplain}</textarea>
							</td>
						</tr>

						<tr>
							<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
								<a href="javascript:void(0)" onclick="showWfImag('${workFlowId}','輻射安全許可證環保手續辦理委託申請流程圖','');">點擊查看簽核流程圖</a>
							</th>
						</tr>
						<tr>
							<td colspan="10" style="text-align:left;">
								<!--
								<table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
									<tr>
										<td style="border:none">
											<table width="18%" style="float: left;margin-left: 5px;">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">課級主管</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealfactoryid').combobox('getValue'))"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
-->
								<table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
									<tr>
										<td style="border:none">
											<table width="18%" style="float: left;margin-left: 5px;">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealfactoryid').combobox('getValue'))"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="kchargeno" name="tQhWfraysafetyhbprocess.kchargeno"
															   class="easyui-validatebox" data-options="width:80,required:${requiredMap['kchargeno']}" readonly
															   value="${tQhWfraysafetyhbprocess.kchargeno }"/><c:if test="${requiredMap['kchargeno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="kchargename" name="tQhWfraysafetyhbprocess.kchargename"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['kchargeno']}"
																value="${tQhWfraysafetyhbprocess.kchargename }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealfactoryid').combobox('getValue'))"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td>
														<input id="bchargeno" name="tQhWfraysafetyhbprocess.bchargeno"
															   class="easyui-validatebox" data-options="width:80,required:${requiredMap['bchargeno']}" readonly
															   value="${tQhWfraysafetyhbprocess.bchargeno }"/><c:if test="${requiredMap['bchargeno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="bchargename" name="tQhWfraysafetyhbprocess.bchargename"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['bchargeno']}"
																value="${tQhWfraysafetyhbprocess.bchargename }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').combobox('getValue'))"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td>
														<input id="cchargeno"
															   name="tQhWfraysafetyhbprocess.cchargeno"
															   class="easyui-validatebox" data-options="width: 80,required:${requiredMap['cchargeno']}" readonly
															   value="${tQhWfraysafetyhbprocess.cchargeno }"/><c:if test="${requiredMap['cchargeno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="cchargename"
																name="tQhWfraysafetyhbprocess.cchargename"
																class="easyui-validatebox" data-options="width: 80,required:${requiredMap['cchargeno']}"
																value="${tQhWfraysafetyhbprocess.cchargename }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;"  id="hchargeTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: center;">${requiredMap['hchargeno_name']}
																	<a href="javascript:addHq('hcharge');">添加一位</a></td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="hchargeno" onblur="getUserNameByEmpno(this,'hcharge');" name="tQhWfraysafetyhbprocess.hchargeno"
															   class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hchargeno']}" value="${tQhWfraysafetyhbprocess.hchargeno }"/>
														<c:if test="${requiredMap['hchargeno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="hchargename" name="tQhWfraysafetyhbprocess.hchargename" readonly
																class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hchargeno']}"  value="${tQhWfraysafetyhbprocess.hchargename }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfraysafetyhbprocess')"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td>
														<input id="zchargeno"
															   name="tQhWfraysafetyhbprocess.zchargeno"
															   class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zchargeno']}" readonly
															   value="${tQhWfraysafetyhbprocess.zchargeno }"/><c:if test="${requiredMap['zchargeno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="zchargename"
																name="tQhWfraysafetyhbprocess.zchargename"
																class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zchargeno']}" readonly
																value="${tQhWfraysafetyhbprocess.zchargename }"/>
													</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td style="border:none">
											<table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfraysafetyhbprocess')"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td>
														<input id="zcchargeno"
															   name="tQhWfraysafetyhbprocess.zcchargeno"
															   class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zcchargeno']}" readonly
															   value="${tQhWfraysafetyhbprocess.zcchargeno }"/><c:if test="${requiredMap['zcchargeno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="zcchargename"
																name="tQhWfraysafetyhbprocess.zcchargename"
																class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zcchargeno']}" readonly
																value="${tQhWfraysafetyhbprocess.zcchargename }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;" id="hbglcchargeTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['hbglcchargeno_name']}環保</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(671,'hbglcchargeTable','hbglcchargeno','hbglcchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfraysafetyhbprocess')"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td>
														<input id="hbglcchargeno"
															   name="tQhWfraysafetyhbprocess.hbglcchargeno"
															   class="easyui-validatebox"
															   data-options="width: 80,required:${requiredMap['hbglcchargeno']}" readonly
															   value="${tQhWfraysafetyhbprocess.hbglcchargeno }"/><c:if test="${requiredMap['hbglcchargeno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="hbglcchargename" name="tQhWfraysafetyhbprocess.hbglcchargename" readonly
																class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbglcchargeno']}" value="${tQhWfraysafetyhbprocess.hbglcchargename }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;" id="zwglcchargeTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['zwglcchargeno_name']}</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(672,'zwglcchargeTable','zwglcchargeno','zwglcchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfraysafetyhbprocess')"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td>
														<input id="zwglcchargeno"
															   name="tQhWfraysafetyhbprocess.zwglcchargeno"
															   class="easyui-validatebox"
															   data-options="width: 80,required:${requiredMap['zwglcchargeno']}" readonly
															   value="${tQhWfraysafetyhbprocess.zwglcchargeno }"/><c:if test="${requiredMap['zwglcchargeno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="zwglcchargename" name="tQhWfraysafetyhbprocess.zwglcchargename" readonly
																class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zwglcchargeno']}" value="${tQhWfraysafetyhbprocess.zwglcchargename }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;" id="jgglcchargeTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['jgglcchargeno_name']}</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(673,'jgglcchargeTable','jgglcchargeno','jgglcchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfraysafetyhbprocess')"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td>
														<input id="jgglcchargeno"
															   name="tQhWfraysafetyhbprocess.jgglcchargeno"
															   class="easyui-validatebox"
															   data-options="width: 80,required:${requiredMap['jgglcchargeno']}" readonly
															   value="${tQhWfraysafetyhbprocess.jgglcchargeno }"/><c:if test="${requiredMap['jgglcchargeno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="jgglcchargename" name="tQhWfraysafetyhbprocess.jgglcchargename" readonly
																class="easyui-validatebox" data-options="width: 80,required:${requiredMap['jgglcchargeno']}" value="${tQhWfraysafetyhbprocess.jgglcchargename }"/>
													</td>
												</tr>
											</table>
											<!--

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zwchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">總務處對應窗口</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(7,'zwchargeno','zwchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zwchargeno"
                                                               name="tQhWfraysafetyhbprocess.zwchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['zwchargeno']}" readonly
                                                               value="${tQhWfraysafetyhbprocess.zwchargeno }"/><c:if test="${requiredMap['zwchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="zwchargename" name="tQhWfraysafetyhbprocess.zwchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zwchargeno']}" readonly
                                                               value="${tQhWfraysafetyhbprocess.zwchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">


                                            -->
											<table width="18%" style="float: left;margin-left: 5px;" id="zgshchargeTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['zgshchargeno_name']}
																</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(274,'zgshchargeTable','zgshchargeno','zgshchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfraysafetyhbprocess')"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td>
														<input id="zgshchargeno"
															   name="tQhWfraysafetyhbprocess.zgshchargeno"
															   class="easyui-validatebox"
															   data-options="width: 80,required:${requiredMap['zgshchargeno']}" readonly
															   value="${tQhWfraysafetyhbprocess.zgshchargeno }"/><c:if test="${requiredMap['zgshchargeno'].equals('true')}"><font color="red">*</font></c:if>
														/
														<input id="zgshchargename" name="tQhWfraysafetyhbprocess.zgshchargename"
															   class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zgshchargeno']}"
															   value="${tQhWfraysafetyhbprocess.zgshchargename }"/>
													</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td style="border:none">
											<table width="18%" style="float: left;margin-left: 5px;" id="jczhchargeTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['jczhchargeno_name']}</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(269,'jczhchargeTable','jczhchargeno','jczhchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfraysafetyhbprocess')"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td>
														<input id="jczhchargeno"
															   name="tQhWfraysafetyhbprocess.jczhchargeno"
															   class="easyui-validatebox"
															   data-options="width: 80,required:${requiredMap['jczhchargeno']}" readonly
															   value="${tQhWfraysafetyhbprocess.jczhchargeno }"/><c:if test="${requiredMap['jczhchargeno'].equals('true')}"><font color="red">*</font></c:if>
														/
														<input id="jczhchargename" name="tQhWfraysafetyhbprocess.jczhchargename"
															   class="easyui-validatebox" data-options="width: 80,required:${requiredMap['jczhchargeno']}" readonly
															   value="${tQhWfraysafetyhbprocess.jczhchargename }"/>
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
							</td>
						</tr>
						<tr>
							<td colspan="10" style="text-align:left;">
								<table class="flowList" style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
									<tr>
										<td>簽核時間</td>
										<td>簽核節點</td>
										<td>簽核主管</td>
										<td>簽核意見</td>
										<td>批註</td>
										<td>簽核電腦IP</td>
									</tr>
								</table>
							</td>
						</tr>
						<tr>
							<td colspan="10" style="border:none;text-align:center;margin-top:10px">
								<a href="javascript:;" id="btnSave" class="easyui-linkbutton" data-options="iconCls:'icon-add'"
								   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
								<a href="javascript:;" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
								   style="width: 100px;" onclick="saveInfo(2);">提交</a>
								<%--<a href="#" id="btnadd" class="easyui-linkbutton" iconCls="icon-add" plain="true" code="add">提交</a>--%>
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
	</div>
	<input type="hidden" id="deptNo" name="deptNo" value=""/>
	<input type="hidden" id="chargeNo" name="chargeNo" value=""/>
	<input type="hidden" id="chargeName" name="chargeName" value=""/>
	<input type="hidden" id="factoryId" name="factoryId" value=""/>
	<input type="hidden" id="dutyId" name="dutyId" value=""/>
	<div id="win"></div>
</form>
<script type="text/javascript">
    if ("${tQhWfraysafetyhbprocess.hchargeno}" != "") {
        var nostr = "${tQhWfraysafetyhbprocess.hchargeno}";
        var namestr = "${tQhWfraysafetyhbprocess.hchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hchargeTable tr:eq(" + (i + 2) + ")").find("#hchargeno").val(notr[i]);
            $("#hchargeTable tr:eq(" + (i + 2) + ")").find("#hchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hchargeno' name='tQhWfraysafetyhbprocess.hchargeno' style='width: 80px' value='' onblur='getUserNameByEmpno(this,\"hcharge\");'/>/<input id='hchargename' name='tQhWfraysafetyhbprocess.hchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
</script>
</body>
</html>