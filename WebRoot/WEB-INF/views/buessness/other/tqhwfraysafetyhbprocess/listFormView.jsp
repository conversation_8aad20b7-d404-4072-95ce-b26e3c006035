<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>輻射安全許可證環保手續辦理委託申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/other/tqhwfraysafetyhbprocess.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfraysafetyhbprocess/${action}" method="post">
    <div class="commonW">
        <div class="headTitle">輻射安全環保手續辦理委託申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">${tQhWfraysafetyhbprocess.serialno}</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
            <input class="inputCss" style="width: 100px" value="<fmt:formatDate value='${tQhWfraysafetyhbprocess.createtime}' pattern='yyyy-MM-dd hh:mm'/>" >
        </span>
        </div>
        <div class="position_R margin_R"> 填單人：${tQhWfraysafetyhbprocess.makerno}/${tQhWfraysafetyhbprocess.makername}</div>
        <br>
        <div class="clear"></div>
        <table class="formList" id="buildprojecttable">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">承辦人工號</td>
                            <td width="6%" class="td_style1">
                                <input id="dealno" name="tQhWfraysafetyhbprocess.dealno" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${tQhWfraysafetyhbprocess.dealno }" readonly/>
                            </td>
                            <td width="6%">承辦人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="tQhWfraysafetyhbprocess.dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" value="${tQhWfraysafetyhbprocess.dealname }" readonly/>
                            </td>
                            <td width="6%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="tQhWfraysafetyhbprocess.dealdeptno" class="easyui-validatebox inputCss" data-options="width: 90"
                                       value="${tQhWfraysafetyhbprocess.dealdeptno }" readonly/>
                            </td>
                            <td width="6%">提報日期</td>
                            <td width="6%" class="td_style1">
                                <input id="dealtime" name="tQhWfraysafetyhbprocess.dealtime" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                       data-options="width: 100"
                                       value="<fmt:formatDate value="${tQhWfraysafetyhbprocess.dealtime}"/>" disabled/>
                            </td>
                            <td width="6%">廠區</td>
                            <td width="6%" class="td_style1">
                                <input id="dealfactoryid" name="tQhWfraysafetyhbprocess.dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${tQhWfraysafetyhbprocess.dealfactoryid }" disabled
                                       data-options="width: 120"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="3" class="td_style1">
                                <input id="dealdeptname" name="tQhWfraysafetyhbprocess.dealdeptname"
                                       class="easyui-validatebox inputCss" data-options="width: 400"
                                       value="${tQhWfraysafetyhbprocess.dealdeptname }" readonly/>
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="tQhWfraysafetyhbprocess.dealemail" class="easyui-validatebox inputCss"
                                       value="${tQhWfraysafetyhbprocess.dealemail }" style="width:300px;" readonly/>
                            </td>
                            <td>聯繫分機</td>
                            <td class="td_style1">
                                <input id="dealtel" name="tQhWfraysafetyhbprocess.dealtel" class="easyui-validatebox inputCss"
                                       style="width:90px;" value="${tQhWfraysafetyhbprocess.dealtel }" readonly/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">項目基本信息</td>
                        </tr>

                        <tr align="center">
                            <td colspan="3">法人名稱</td>
                            <td colspan="7" class="td_style1">
                                <input id="corporateid" name="tQhWfraysafetyhbprocess.corporateid" data-options="width: 300,required:true"
                                       class="easyui-combobox" panelHeight="auto" editable="false" value="${tQhWfraysafetyhbprocess.corporateid }" disabled/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="3">是否具有輻射安全許可證</td>
                            <td colspan="2">
                                <div class="ishavepermitDiv"></div>
                                <input id="ishavepermit" name="tQhWfraysafetyhbprocess.ishavepermit"
                                       type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${tQhWfraysafetyhbprocess.ishavepermit }"/>
                                <input id="disOrEnabled" type="hidden" value="disabled"/>

                            </td>
                            <td colspan="3">建設性質</td>
                            <td colspan="2">
                                <div class="buildpropertiesDiv"></div>
                                <input id="buildproperties" name="tQhWfraysafetyhbprocess.buildproperties"
                                       type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${tQhWfraysafetyhbprocess.buildproperties }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 100%;padding-bottom: 15px;">
                                    <table id="raysafetyhbCostItemTable" width="100%">
                                        <tr align="center">
                                            <td>序號</td>
                                            <td>費用掛靠單位</td>
                                            <td>費用代碼</td>
                                            <td>費用佔比(%)</td>
                                        </tr>
                                        <c:if test="${costItemEntity!=null&&costItemEntity.size()>0}">
                                            <c:forEach items="${costItemEntity}" varStatus="i" var="costItem">
                                                <tr align="center">
                                                    <td>${i.index+1}</td>
                                                    <td>${costItem.costname}</td>
                                                    <td>${costItem.costno}</td>
                                                    <td>${costItem.costrate}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 100%;padding-bottom: 15px;">
                                    <table id="raysafetyhbItemTable" width="100%">
                                        <tr align="center">
                                            <td>序號</td>
                                            <td>射線裝置<br>名稱</td>
                                            <td>射線裝置<br>型號</td>
                                            <td>射線裝置<br>數量</td>
                                            <td>工作場所名稱<br>及周邊環境描述</td>
                                            <td>占地面積（㎡）</td>
                                            <td>項目投資<br>（萬元）</td>
                                            <td>環保投資<br>（萬元）</td>
                                            <td>設備來源</td>
                                            <td>設備類別</td>
                                            <td>用途</td>
                                            <td>備註</td>
                                        </tr>
                                        <c:if test="${deviceItemEntity!=null&&deviceItemEntity.size()>0}">
                                            <c:forEach items="${deviceItemEntity}" var="deviceItem" varStatus="status">
                                                <tr align="center">
                                                    <td>${status.index+1}</td>
                                                    <td>${deviceItem.devicename}</td>
                                                    <td>${deviceItem.devicetype}</td>
                                                    <td>${deviceItem.devicenumber}</td>
                                                    <td>${deviceItem.nameanddescription}</td>
                                                    <td>${deviceItem.floorspace}</td>
                                                    <td>${deviceItem.projectcost}</td>
                                                    <td>${deviceItem.enviromentalcost}</td>
                                                    <td>${deviceItem.equipmentsource}</td>
                                                    <td>${deviceItem.equipmenttype}</td>
                                                    <td>${deviceItem.deviceuse}</td>
                                                    <td>${deviceItem.remark}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">附件</td>
                            <td colspan="8" class="td_style1">
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">委託事項說明</td>
                            <td colspan="8" class="td_style1">
						<textarea id="entrustexplain" name="tQhWfraysafetyhbprocess.entrustexplain"
                                  class="easyui-validatebox inputCss" style="width:800px;height:80px;"
                                  rows="5" cols="6" readonly>${tQhWfraysafetyhbprocess.entrustexplain }</textarea>
                            </td>
                        </tr>
                        <c:if test="${tQhWfraysafetyhbprocess.reattachids!=null}">
                            <tr align="center">
                                <td colspan="2">附件（補充說明）</td>
                                <td colspan="8" class="td_style1">
                                    <div id="dowloadUrll">
                                        <c:forEach items="${reFile}" varStatus="i" var="item">
                                            <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                                <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                            </div>
                                        </c:forEach>
                                    </div>
                                </td>
                            </tr>
                        </c:if>
                        <tr>
                            <th style="text-align:left;" colspan="10">審核路徑及審核記錄
                                <a href="javascript:void(0)" onclick="showWfImag('${processId}','輻射安全許可證環保手續辦理委託申請流程圖','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhWfraysafetyhbprocess.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                   style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>

                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <div id="dlg"></div>
</form>
</body>
</html>