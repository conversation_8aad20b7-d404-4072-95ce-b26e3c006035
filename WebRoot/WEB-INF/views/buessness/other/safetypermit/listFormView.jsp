<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>輻射安全許可證變更申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/other/safetyPermit.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/safetyPermit/${action}" method="post">
    <div class="commonW">
        <div id="mainDiv">
            <div class="headTitle">輻射安全許可證變更申請單</div>
            <div class="position_L">
                任務編碼：<span style="color:#999;">${safetyPermitEntity.serialno}</span>
            </div>
            <div class="position_L1 margin_L">
                填單時間：<span style="color:#999;">
                <input class="inputCss" style="width: 120px" readonly="true"
                       value="<fmt:formatDate value='${safetyPermitEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"></span>
            </div>
            <div class="position_R margin_R"> 填單人：${safetyPermitEntity.makerno}/${safetyPermitEntity.makername}</div>
            <br>
            <div class="clear"></div>
            <table class="formList">
                <tr>
                    <td>
                        <table class="formList">
                            <tr>
                                <td colspan="10" class="td_style1">承辦人詳細信息</td>
                            </tr>
                            <tr align="center">
                                <td width="6%">承辦人工號</td>
                                <td width="6%" class="td_style1" style="text-align: center">
                                    <input id="dealno" name="safetyPermitEntity.dealno" class="easyui-validatebox inputCss"
                                           data-options="width: 80,required:true" readonly="true" style="width: 80px"
                                           value="${safetyPermitEntity.dealno }" onblur="queryUserInfo(this);"/>
                                </td>
                                <td width="6%">承辦人</td>
                                <td width="6%" class="td_style1" style="text-align: center">
                                    <input id="dealname" name="safetyPermitEntity.dealname" class="easyui-validatebox inputCss"
                                           data-options="width:80" readonly value="${safetyPermitEntity.dealname }"/>
                                </td>
                                <td width="6%">單位代碼</td>
                                <td width="6%" class="td_style1" style="text-align: center">
                                    <input id="dealdeptno" name="safetyPermitEntity.dealdeptno" class="easyui-validatebox inputCss"
                                           data-options="width: 90"
                                           readonly value="${safetyPermitEntity.dealdeptno }"/>
                                </td>
                                <td width="6%">提報日期</td>
                                <td width="6%" class="td_style1">
                                    <input id="dealtime" name="safetyPermitEntity.dealtime" class="easyui-validatebox inputCss" datefmt="yyyy-MM-dd"
                                           data-options="width: 100,required:true" readonly="true"
                                           value="<fmt:formatDate value="${safetyPermitEntity.dealtime}"/>"/>
                                </td>
                                <td width="6%">廠區</td>
                                <td width="6%" class="td_style1" style="text-align: center">
                                    <input id="dealfactoryid" name="safetyPermitEntity.dealfactoryid" class="easyui-combobox inputCss"
                                           panelHeight="auto" value="${safetyPermitEntity.dealfactoryid }"  disabled
                                           data-options="width: 120,required:true"/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>單位</td>
                                <td colspan="3" class="td_style1">
                                    <input id="dealdeptname" name="safetyPermitEntity.dealdeptname"
                                           class="easyui-validatebox inputCss" data-options="width: 400"
                                           readonly value="${safetyPermitEntity.dealdeptname }"/>
                                </td>
                                <td>聯繫郵箱</td>
                                <td colspan="3" class="td_style1">
                                    <input id="dealemail" name="safetyPermitEntity.dealemail" class="easyui-validatebox inputCss"
                                           value="${safetyPermitEntity.dealemail }" style="width:300px;" readonly="true"
                                           data-options="required:true" onblur="valdEmail(this)"/>
                                </td>
                                <td>聯繫分機</td>
                                <td class="td_style1">
                                    <input id="dealtel" name="safetyPermitEntity.dealtel" class="easyui-validatebox inputCss"
                                           style="width:90px;" readonly="true"
                                           value="${safetyPermitEntity.dealtel }" data-options="required:true,prompt:'579+66666'"
                                           onblur="valdApplyTel(this)"/>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td>
                        <table class="formList">
                            <tr>
                                <td colspan="10" class="td_style1">申請單內容</td>
                            </tr>

                            <tr>
                                <td colspan="2"></td>
                                <td colspan="4" style="text-align: center">原核准事項</td>
                                <td colspan="4" style="text-align: center">變更后的事項</td>

                            </tr>
                            <tr>
                                <td colspan="2" style="text-align: center">單位名稱</td>
                                <td colspan="4" style="text-align: center">
                                    <input id="applydeptname" name="safetyPermitEntity.applydeptname" class="easyui-validatebox inputCss"
                                           style="width:80%;" readonly="true"
                                           data-options="required:true" value="${safetyPermitEntity.applydeptname }"/>
                                </td>
                                <td colspan="4" style="text-align: center">
                                    <input id="newapplydeptname" name="safetyPermitEntity.newapplydeptname" class="easyui-validatebox inputCss"
                                           style="width:80%;" readonly="true"
                                           data-options="required:true" value="${safetyPermitEntity.newapplydeptname }"/>
                                </td>

                            </tr>
                            <tr>
                                <td colspan="2" style="text-align: center">地址</td>
                                <td colspan="4" style="text-align: center">
                                    <input id="address" name="safetyPermitEntity.address" class="easyui-validatebox inputCss"
                                           style="width:80%;" readonly="true"
                                           data-options="required:true" value="${safetyPermitEntity.address }"/>
                                </td>
                                <td colspan="4" style="text-align: center">
                                    <input id="newaddress" name="safetyPermitEntity.newaddress" class="easyui-validatebox inputCss"
                                           style="width:80%;" readonly="true"
                                           data-options="required:true" value="${safetyPermitEntity.newaddress }"/>
                                </td>

                            </tr>
                            <tr>
                                <td colspan="2" style="text-align: center">法定代表人</td>
                                <td colspan="4" style="text-align: center">
                                    <input id="legalperson" name="safetyPermitEntity.legalperson" class="easyui-validatebox inputCss"
                                           style="width:80%;" readonly="true"
                                           data-options="required:true" value="${safetyPermitEntity.legalperson }"/>
                                </td>
                                <td colspan="4" style="text-align: center">
                                    <input id="newlegalperson" name="safetyPermitEntity.newlegalperson" class="easyui-validatebox inputCss"
                                           style="width:80%;" readonly="true"
                                           data-options="required:true" value="${safetyPermitEntity.newlegalperson }"/>
                                </td>

                            </tr>
                            <tr>
                                <td colspan="2" style="text-align: center">法定代表人身份證號碼</td>
                                <td colspan="4" style="text-align: center">
                                    <input id="legalpersonidno" name="safetyPermitEntity.legalpersonidno" class="easyui-validatebox inputCss"
                                           style="width:80%;" readonly="true"
                                           value="${safetyPermitEntity.legalpersonidno }"/>
                                </td>
                                <td colspan="4" style="text-align: center">
                                    <input id="newlegalpersonidno" name="safetyPermitEntity.newlegalpersonidno"
                                           class="easyui-validatebox inputCss" style="width:80%;" readonly="true"
                                           value="${safetyPermitEntity.newlegalpersonidno }"/>
                                </td>

                            </tr>
                            <tr>
                                <td colspan="2" style="text-align: center">許可證編號</td>
                                <td colspan="3" style="text-align: center">
                                    <input id="licenseno" name="safetyPermitEntity.licenseno" class="easyui-validatebox inputCss"
                                           style="width:80%;" readonly="true"
                                           data-options="required:true" value="${safetyPermitEntity.licenseno }"/>
                                </td>
                                <td colspan="2" style="text-align: center">種類和範圍</td>
                                <td colspan="3" style="text-align: center">
                                    <input id="typescope" name="safetyPermitEntity.typescope" class="easyui-validatebox inputCss"
                                           style="width:80%;" readonly="true"
                                           data-options="required:true" value="${safetyPermitEntity.typescope }"/>
                                </td>

                            </tr>
                            <tr>
                                <td colspan="2" style="text-align: center">發證日期</td>
                                <td colspan="3" style="text-align: center">
                                    <input id="licensedate" name="safetyPermitEntity.licensedate" class="easyui-validatebox inputCss"
                                           datefmt="yyyy-MM-dd" data-options="width: 100,required:true" readonly="true"
                                           value="<fmt:formatDate value="${safetyPermitEntity.licensedate}"/>"/>
                                </td>
                                <td colspan="2" style="text-align: center">有效期</td>
                                <td colspan="3" style="text-align: center">
                                    <input id="effectivedate" name="safetyPermitEntity.effectivedate" class="easyui-validatebox inputCss"
                                           style="width:80%;" readonly="true"
                                           data-options="required:true" value="${safetyPermitEntity.effectivedate }"/>
                                </td>

                            </tr>
                            <tr>
                                <td colspan="2" style="text-align: center">聯繫人</td>
                                <td colspan="3" style="text-align: center">
                                    <input id="contactperson" name="safetyPermitEntity.contactperson" class="easyui-validatebox inputCss"
                                           style="width:80%;" readonly="true"
                                           data-options="required:true" value="${safetyPermitEntity.contactperson }"/>
                                </td>
                                <td colspan="2" style="text-align: center">聯繫電話</td>
                                <td colspan="3" style="text-align: center">
                                    <input id="contactphone" name="safetyPermitEntity.contactphone" class="easyui-validatebox inputCss"
                                           style="width:80%;" readonly="true"
                                           data-options="required:true" value="${safetyPermitEntity.contactphone }"/>
                                </td>

                            </tr>

                            <tr align="center">
                                <td colspan="2">附件</td>
                                <td colspan="8" class="td_style1">
                                    <div id="dowloadUrl">
                                        <c:forEach items="${file}" varStatus="i" var="item">
                                            <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                                <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                            </div>
                                        </c:forEach>
                                    </div>
                                </td>
                            </tr>
                            <tr align="center">
                                <td colspan="2">說明</td>
                                <td colspan="8" class="td_style1">
                                    1.變更后的企業法人營業執照或事業單位法人證書正、副本複印件；
                                    2.變更后的法定代表人身份證複印件；
                                    3.其它。
                                </td>
                            </tr>
                            <tr align="center">
                                <td colspan="2">附件&nbsp;（補充說明）</td>
                                <td colspan="8" class="td_style1">
                                    <div id="dowloadUrl">
                                        <c:forEach items="${reFile}" varStatus="i" var="item">
                                            <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                                <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                            </div>
                                        </c:forEach>
                                    </div>
                                </td>
                            </tr>

                            <tr>
                                <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                    <a href="javascript:void(0)"
                                       onclick="showWfImag('${processId}','輻射安全許可證變更申請單流程圖');">點擊查看簽核流程圖</a>
                                </th>
                            </tr>
                            <tr>
                                <td colspan="10" style="text-align:left;">
                                    ${chargeNodeInfo}
                                </td>
                            </tr>
                            <tr>
                                <td colspan="10" style="text-align:left;">
                                    <iframe id="qianheLogFrame" name="qianheLogFrame"
                                            src="${ctx}/wfcontroller/goChargeLog?serialNo=${safetyPermitEntity.serialno}"
                                            width="100%"></iframe>
                                </td>
                            </tr>
                            <tr class="no-print">
                                <td colspan="10" style="text-align:center;padding-left:10px;">
                                    <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-cancel'"
                                       style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                    <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>

                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</form>
<div id="dlg"></div>
</body>
</html>