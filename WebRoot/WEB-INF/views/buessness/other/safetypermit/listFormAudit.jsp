<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>輻射安全許可證變更申請單審核</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src="${ctx}/static/js/other/safetyPermit.js?random=<%= Math.random()%>" type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/safetyPermit/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${safetyPermitEntity.id }"/>
    <div class="commonW">
        <div class="headTitle">輻射安全許可證變更申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">${safetyPermitEntity.serialno}</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
                    <input class="inputCss" style="width: 120px" readonly="true"
                           value="<fmt:formatDate value='${safetyPermitEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>">
        </span>

        </div>
        <div class="position_R margin_R">
            填單人：${safetyPermitEntity.makerno}/${safetyPermitEntity.makername}</div>
        <br>
        <div class="clear"></div>
        <table class="formList" id="">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th colspan="10" style="text-align: left" class="td_style1">&nbsp;&nbsp;承辦人詳細信息</th>
                        </tr>
                        <tr align="center">
                            <td width="6%">承辦人工號</td>
                            <td width="6%" class="td_style2">${safetyPermitEntity.dealno }
                            </td>
                            <td width="6%">承辦人</td>
                            <td width="6%" class="td_style2">${safetyPermitEntity.dealname }</td>
                            <td width="6%">單位代碼</td>
                            <td width="6%" class="td_style2">${safetyPermitEntity.dealdeptno }</td>
                            <td width="6%">提報日期</td>
                            <td width="6%" class="td_style2"><fmt:formatDate value="${safetyPermitEntity.dealtime}"/></td>
                            <td width="6%">廠區</td>
                            <td width="6%" class="td_style1" class="td_style2">
                                <input id="dealfactoryid" class="easyui-combobox inputCss" disabled
                                       panelHeight="auto" value="${safetyPermitEntity.dealfactoryid }" readonly="true"
                                       data-options="width: 120"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="3" class="td_style2">${safetyPermitEntity.dealdeptname }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${safetyPermitEntity.dealemail}</td>
                            <td>聯繫分機</td>
                            <td class="td_style2">${safetyPermitEntity.dealtel }</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th colspan="10" style="text-align: left" class="td_style1">&nbsp;&nbsp;申請單內容</th>
                        </tr>

                        <tr>
                            <td colspan="2"></td>
                            <td colspan="4" style="text-align: center">原核准事項</td>
                            <td colspan="4" style="text-align: center">變更后的事項</td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">單位名稱</td>
                            <td colspan="4" style="text-align: center">
                                <input id="applydeptname" class="easyui-validatebox inputCss"
                                       style="width:80%" readonly="true" value="${safetyPermitEntity.applydeptname }"/>
                            </td>
                            <td colspan="4" style="text-align: center">
                                <input id="newapplydeptname" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true" value="${safetyPermitEntity.newapplydeptname }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">地址</td>
                            <td colspan="4" style="text-align: center">
                                <input id="address" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true" value="${safetyPermitEntity.address }"/>
                            </td>
                            <td colspan="4" style="text-align: center">
                                <input id="newaddress" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true" value="${safetyPermitEntity.newaddress }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">法定代表人</td>
                            <td colspan="4" style="text-align: center">
                                <input id="legalperson" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true" value="${safetyPermitEntity.legalperson }"/>
                            </td>
                            <td colspan="4" style="text-align: center">
                                <input id="newlegalperson" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true" value="${safetyPermitEntity.newlegalperson }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">法定代表人身份證號碼</td>
                            <td colspan="4" style="text-align: center">
                                <input id="legalpersonidno" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true" value="${safetyPermitEntity.legalpersonidno }"/>
                            </td>
                            <td colspan="4" style="text-align: center">
                                <input id="newlegalpersonidno" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true" value="${safetyPermitEntity.newlegalpersonidno }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">許可證編號</td>
                            <td colspan="3" style="text-align: center">
                                <input id="licenseno" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true" value="${safetyPermitEntity.licenseno }"/>
                            </td>
                            <td colspan="2" style="text-align: center">種類和範圍</td>
                            <td colspan="3" style="text-align: center">
                                <input id="typescope" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true" value="${safetyPermitEntity.typescope }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">發證日期</td>
                            <td colspan="3" style="text-align: center">
                                <input id="licensedate" class="easyui-validatebox inputCss"
                                       datefmt="yyyy-MM-dd" data-options="width: 100" readonly="true"
                                       value="<fmt:formatDate value="${safetyPermitEntity.licensedate}"/>"/>
                            </td>
                            <td colspan="2" style="text-align: center">有效期</td>
                            <td colspan="3" style="text-align: center">
                                <input id="effectivedate" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true" value="${safetyPermitEntity.effectivedate }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">聯繫人</td>
                            <td colspan="3" style="text-align: center">
                                <input id="contactperson" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true" value="${safetyPermitEntity.contactperson }"/>
                            </td>
                            <td colspan="2" style="text-align: center">聯繫電話</td>
                            <td colspan="3" style="text-align: center">
                                <input id="contactphone" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true" value="${safetyPermitEntity.contactphone }"/>
                            </td>

                        </tr>

                        <tr align="center">
                            <td colspan="2">附件</td>
                            <td colspan="8" class="td_style1">
                                <div>
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">說明</td>
                            <td colspan="8" class="td_style1">
                                1.變更后的企業法人營業執照或事業單位法人證書正、副本複印件；
                                2.變更后的法定代表人身份證複印件；
                                3.其它。
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">批註</td>
                            <td colspan="8">
                                <input style="width: 90%" id="attachidsremark" name="attachidsremark" class="easyui-validatebox"/>
                            </td>
                        </tr>
                        <c:if test="${not empty nodeName&&'環保科技處對應窗口' eq nodeName}">
                            <tr>
                                <td colspan="2" style="text-align: center">附件&nbsp;（補充說明）</td>
                                <td colspan="8" class="td_style1">
                                    <span class="sl-custom-file">
						                <input type="button" value="点击上传文件" class="btn-file"/>
						                <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file">
						            </span>
                                    <input type="hidden" id="attachids" name="reattachids" value="${safetyPermitEntity.reattachids}"/>
                                    <div id="dowloadUrl"></div>
                                </td>
                            </tr>
                        </c:if>
                        <c:if test="${safetyPermitEntity.reattachids!=null}">
                            <tr align="center">
                                <td colspan="2" style="text-align: center">附件&nbsp;（補充說明）</td>
                                <td colspan="8" class="td_style1">
                                    <div>
                                        <c:forEach items="${reFile}" varStatus="i" var="item">
                                            <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                                <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                            </div>
                                        </c:forEach>
                                    </div>
                                </td>
                            </tr>
                        </c:if>

                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${safetyPermitEntity.serialno}"></fox:action>
                                <c:choose>
                                    <c:when test="${not empty nodeName && '環保科技處對應窗口' eq nodeName}">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" perCall="audiPrValid" serialNo="${safetyPermitEntity.serialno}"></fox:action>
                                    </c:when>
                                    <c:otherwise>
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" serialNo="${safetyPermitEntity.serialno}"></fox:action>
                                    </c:otherwise>
                                </c:choose>

                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','輻射安全許可證變更申請單流程圖');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${safetyPermitEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
<input type="hidden" id="currentNodeOrder" value="${nodeOrder}" />
<div id="dlg"></div>
</body>
</html>