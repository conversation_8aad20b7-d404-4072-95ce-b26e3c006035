<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>輻射安全許可證變更申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/other/safetyPermit.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/safetyPermit/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${safetyPermitEntity.id }"/>
    <input id="serialno" name="safetyPermitEntity.serialno" type="hidden" value="${safetyPermitEntity.serialno }"/>
    <input id="createtime" name="safetyPermitEntity.createtime" type="hidden"
           value="${safetyPermitEntity.createtime }"/>
    <input id="makerno" name="safetyPermitEntity.makerno" type="hidden" value="${safetyPermitEntity.makerno }"/>
    <input id="makername" name="safetyPermitEntity.makername" type="hidden" value="${safetyPermitEntity.makername }"/>
    <input id="makerdeptno" name="safetyPermitEntity.makerdeptno" type="hidden" value="${safetyPermitEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="safetyPermitEntity.makerfactoryid" type="hidden" value="${safetyPermitEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">輻射安全許可證變更申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                <c:choose>
                    <c:when test="${safetyPermitEntity.serialno==null}">
                        提交成功后自動編碼
                    </c:when>
                    <c:otherwise>
                        ${safetyPermitEntity.serialno}
                    </c:otherwise>
                </c:choose>
            </span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
                <c:choose>
                    <c:when test="${safetyPermitEntity.createtime==null}">
                        YYYY/MM/DD
                    </c:when>
                    <c:otherwise>
                        <input class="inputCss" style="width: 120px" readonly="true"
                               value="<fmt:formatDate value='${safetyPermitEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>">
                    </c:otherwise>
                </c:choose>
            </span>
        </div>
        <c:if test="${empty safetyPermitEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty safetyPermitEntity.makerno}">
            <div class="position_R margin_R">填單人：${safetyPermitEntity.makerno}/${safetyPermitEntity.makername}</div>
        </c:if>
        <br>
        <div class="clear"></div>
        <table class="formList" id="buildprojecttable">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealno" name="safetyPermitEntity.dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${safetyPermitEntity.dealno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="6%">承辦人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="safetyPermitEntity.dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${safetyPermitEntity.dealname }"/>
                            </td>
                            <td width="6%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="safetyPermitEntity.dealdeptno"
                                       class="easyui-validatebox inputCss"
                                       data-options="width: 90"
                                       readonly value="${safetyPermitEntity.dealdeptno }"/>
                            </td>
                            <td width="6%">提報日期&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealtime" name="safetyPermitEntity.dealtime" class="easyui-my97"
                                       datefmt="yyyy-MM-dd"
                                       data-options="width: 100,required:true" minDate="%y-%M-%d"
                                       value="<fmt:formatDate value="${safetyPermitEntity.dealtime}"/>"/>
                            </td>
                            <td width="6%">廠區&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealfactoryid" name="safetyPermitEntity.dealfactoryid"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${safetyPermitEntity.dealfactoryid }"
                                       data-options="width: 80,required:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="3" class="td_style1">
                                <input id="dealdeptname" name="safetyPermitEntity.dealdeptname"
                                       class="easyui-validatebox"
                                       data-options="width: 400"
                                       value="${safetyPermitEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="safetyPermitEntity.dealemail" class="easyui-validatebox"
                                       value="${safetyPermitEntity.dealemail }" style="width:300px;"
                                       data-options="required:true" onblur="valdEmail(this)"/>
                            </td>
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealtel" name="safetyPermitEntity.dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${safetyPermitEntity.dealtel }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請單內容</td>
                        </tr>

                        <tr>
                            <td colspan="2"></td>
                            <td colspan="4" style="text-align: center">原核准事項</td>
                            <td colspan="4" style="text-align: center">變更后的事項</td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">單位名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="4" style="text-align: center">
                                <input id="applydeptname" name="safetyPermitEntity.applydeptname"
                                       class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.applydeptname }"/>
                            </td>
                            <td colspan="4" style="text-align: center">
                                <input id="newapplydeptname" name="safetyPermitEntity.newapplydeptname"
                                       class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.newapplydeptname }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">地址&nbsp;<font color="red">*</font></td>
                            <td colspan="4" style="text-align: center">
                                <input id="address" name="safetyPermitEntity.address" class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.address }"/>
                            </td>
                            <td colspan="4" style="text-align: center">
                                <input id="newaddress" name="safetyPermitEntity.newaddress" class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.newaddress }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">法定代表人&nbsp;<font color="red">*</font></td>
                            <td colspan="4" style="text-align: center">
                                <input id="legalperson" name="safetyPermitEntity.legalperson" class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.legalperson }"/>
                            </td>
                            <td colspan="4" style="text-align: center">
                                <input id="newlegalperson" name="safetyPermitEntity.newlegalperson"
                                       class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.newlegalperson }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">法定代表人身份證號碼</td>
                            <td colspan="4" style="text-align: center">
                                <input id="legalpersonidno" name="safetyPermitEntity.legalpersonidno"
                                       class="easyui-validatebox"
                                       style="width:80%;"
                                       value="${safetyPermitEntity.legalpersonidno }"/>
                            </td>
                            <td colspan="4" style="text-align: center">
                                <input id="newlegalpersonidno" name="safetyPermitEntity.newlegalpersonidno"
                                       class="easyui-validatebox"
                                       style="width:80%;"
                                       value="${safetyPermitEntity.newlegalpersonidno }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">許可證編號&nbsp;<font color="red">*</font></td>
                            <td colspan="3" style="text-align: center">
                                <input id="licenseno" name="safetyPermitEntity.licenseno" class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.licenseno }"/>
                            </td>
                            <td colspan="2" style="text-align: center">種類和範圍&nbsp;<font color="red">*</font></td>
                            <td colspan="3" style="text-align: center">
                                <input id="typescope" name="safetyPermitEntity.typescope" class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.typescope }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">發證日期&nbsp;<font color="red">*</font></td>
                            <td colspan="3" style="text-align: left">
                                <input id="licensedate" name="safetyPermitEntity.licensedate" class="easyui-my97"
                                       datefmt="yyyy-MM-dd" data-options="width: 100,required:true"
                                       value="<fmt:formatDate value="${safetyPermitEntity.licensedate}"/>"/>
                            </td>
                            <td colspan="2" style="text-align: center">有效期&nbsp;<font color="red">*</font></td>
                            <td colspan="3" style="text-align: center">
                                <input id="effectivedate" name="safetyPermitEntity.effectivedate"
                                       class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.effectivedate }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">聯繫人&nbsp;<font color="red">*</font></td>
                            <td colspan="3" style="text-align: center">
                                <input id="contactperson" name="safetyPermitEntity.contactperson"
                                       class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.contactperson }"/>
                            </td>
                            <td colspan="2" style="text-align: center">聯繫電話&nbsp;<font color="red">*</font></td>
                            <td colspan="3" style="text-align: center">
                                <input id="contactphone" name="safetyPermitEntity.contactphone"
                                       class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.contactphone }"/>
                            </td>

                        </tr>

                        <tr align="center">
                            <td colspan="2">附件</td>
                            <td colspan="8" class="td_style1">
						        <span class="sl-custom-file">
						            <input type="button" value="点击上传文件" class="btn-file"/>
						            <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file">
						        </span>
                                <input type="hidden" id="attachids" name="safetyPermitEntity.attachids" value="${safetyPermitEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L"><a
                                                    href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">說明</td>
                            <td colspan="9" class="td_style1">
                                1.變更后的企業法人營業執照或事業單位法人證書正、副本複印件；
                                2.變更后的法定代表人身份證複印件；
                                3.其它。
                            </td>
                        </tr>

                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${workFlowId}','輻射安全許可證變更申請單流程圖','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="safetyPermitEntity.kchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['kchargeno']}" readonly
                                                               value="${safetyPermitEntity.kchargeno }"/><c:if test="${requiredMap['kchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="kchargename" name="safetyPermitEntity.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${safetyPermitEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="bchargeno" name="safetyPermitEntity.bchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${safetyPermitEntity.bchargeno }"/><c:if test="${requiredMap['bchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="bchargename" name="safetyPermitEntity.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${safetyPermitEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="cchargeno"
                                                               name="safetyPermitEntity.cchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['cchargeno']}" readonly
                                                               value="${safetyPermitEntity.cchargeno }"/><c:if test="${requiredMap['cchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="cchargename"
                                                                name="safetyPermitEntity.cchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['cchargeno']}"
                                                                value="${safetyPermitEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"  id="hchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽
                                                                    <a href="javascript:addHq('hcharge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hchargeno" onblur="getUserNameByEmpno(this,'hcharge');" name="safetyPermitEntity.hchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hchargeno']}" value="${safetyPermitEntity.hchargeno }"/>
                                                        <c:if test="${requiredMap['hchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hchargename" name="safetyPermitEntity.hchargename" readonly
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hchargeno']}"  value="${safetyPermitEntity.hchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealfactoryid').combobox('getValue'),'safetyPermitEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zchargeno"
                                                               name="safetyPermitEntity.zchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zchargeno']}" readonly
                                                               value="${safetyPermitEntity.zchargeno }"/><c:if test="${requiredMap['zchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="zchargename"
                                                                name="safetyPermitEntity.zchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zchargeno']}" readonly
                                                                value="${safetyPermitEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealfactoryid').combobox('getValue'),'safetyPermitEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zcchargeno"
                                                               name="safetyPermitEntity.zcchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zcchargeno']}" readonly
                                                               value="${safetyPermitEntity.zcchargeno }"/><c:if test="${requiredMap['zcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="zcchargename"
                                                                name="safetyPermitEntity.zcchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zcchargeno']}" readonly
                                                                value="${safetyPermitEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處對應窗口
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(3,'hbchargeno','hbchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbchargeno"
                                                               name="safetyPermitEntity.hbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbchargeno']}" readonly
                                                               value="${safetyPermitEntity.hbchargeno }"/><c:if test="${requiredMap['hbchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hbchargename" name="safetyPermitEntity.hbchargename" readonly
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbchargeno']}" value="${safetyPermitEntity.hbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="hbkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處課級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(4,'hbkchargeTable','hbkchargeno','hbkchargename',$('#dealfactoryid').combobox('getValue'),'safetyPermitEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbkchargeno"
                                                               name="safetyPermitEntity.hbkchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbkchargeno']}" readonly
                                                               value="${safetyPermitEntity.hbkchargeno }"/><c:if test="${requiredMap['hbkchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hbkchargename" name="safetyPermitEntity.hbkchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbkchargeno']}" readonly
                                                                value="${safetyPermitEntity.hbkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="hbbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處部級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(5,'hbbchargeTable','hbbchargeno','hbbchargename',$('#dealfactoryid').combobox('getValue'),'safetyPermitEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbbchargeno"
                                                               name="safetyPermitEntity.hbbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbbchargeno']}" readonly
                                                               value="${safetyPermitEntity.hbbchargeno }"/><c:if test="${requiredMap['hbbchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="hbbchargename"name="safetyPermitEntity.hbbchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbbchargeno']}" readonly
                                                               value="${safetyPermitEntity.hbbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="hbcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處處級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(6,'hbcchargeTable','hbcchargeno','hbcchargename',$('#dealfactoryid').combobox('getValue'),'safetyPermitEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbcchargeno"
                                                               name="safetyPermitEntity.hbcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbcchargeno']}" readonly
                                                               value="${safetyPermitEntity.hbcchargeno }"/><c:if test="${requiredMap['hbcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="hbcchargename" name="safetyPermitEntity.hbcchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbcchargeno']}" readonly
                                                               value="${safetyPermitEntity.hbcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
<script type="text/javascript">
    if ("${safetyPermitEntity.hchargeno}" != "") {
        var nostr = "${safetyPermitEntity.hchargeno}";
        var namestr = "${safetyPermitEntity.hchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hchargeTable tr:eq(" + (i + 2) + ")").find("#hchargeno").val(notr[i]);
            $("#hchargeTable tr:eq(" + (i + 2) + ")").find("#hchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hchargeno' name='safetyPermitEntity.hchargeno' style='width: 80px' value='' onblur='getUserNameByEmpno(this,\"hcharge\");'/>/<input id='hchargename' name='safetyPermitEntity.hchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
</script>
<div id="dlg"></div>
</body>
</html>