<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>輻射安全許可證變更申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input type="text" name="filter_EQS_dealno" class="easyui-validatebox"
               data-options="width:150,prompt: '承辦人工號'"/>
        <input type="text" name="filter_EQS_dealdeptno" class="easyui-validatebox"
               data-options="width:150,prompt: '承辦人單位代碼'"/>
        <input type="text" name="filter_EQS_serialno" class="easyui-validatebox"
               data-options="width:150,prompt: '任務編碼'"/>
        <input type="text" name="filter_GED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd"
               data-options="width:150,prompt: '填單开始日期'"/>
        - <input type="text" name="filter_LED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd"
                 data-options="width:150,prompt: '填單结束日期'"/>
        <input type="text" name="filter_GED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"
               data-options="width:150,prompt: '簽核完成开始日期'"/>
        - <input type="text" name="filter_LED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"
                 data-options="width:150,prompt: '簽核完成结束日期'"/>
        <input id="qysjzt" style="width:100px" class="easyui-validatebox" name="filter_EQS_workstatus"/>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
           onclick="listSearchReset()">重置</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
           onclick="exportExcel()">导出Excel</a>

    </form>

</div>
<table id="dg"></table>
<div id="dlg"></div>
<script type="text/javascript">
    var ctx = "${ctx}";
</script>
<script type="application/javascript">
    var dg;
    var d;
    $(function () {
        dg = $('#dg').datagrid({
            method: "get",
            url: ctx+'/safetyPermit/list',
            fit: true,
            fitColumns: true,
            border: false,
            idField: 'id',
            striped: true,
            cache: false,  //关闭AJAX相应的缓存
            pagination: true,
            rownumbers: true,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: true,
            columns: [[
                {field: 'id', title: '主鍵', hidden: true},
                {field: 'serialno', title: '任務編號', sortable: true, width: 120, formatter: operation},
                {field: 'dealno', title: '承辦人工號', sortable: true, width: 80},
                {field: 'dealname', title: '承辦人名稱', sortable: true, width: 80},
                {field: 'dealdeptno', title: '承辦人單位代碼', sortable: true, width: 80},
                {field: 'dealdeptname', title: '承辦人單位名稱', sortable: true, width: 240,formatter:cellTextTip},
                {field: 'dealtime', title: '提報日期', sortable: true, width: 100},
                {field: 'workstatus', title: '表單狀態', sortable: true, width: 60},
                {field: 'nodeName', title: '當前簽核節點', sortable: true, width: 100},
                {field: 'auditUser', title: '當前簽核人', sortable: true, width: 100},
                {field: 'complettime', title: '簽核完成時間', sortable: true, width: 100}
            ]],
            onLoadSuccess: function(){
                $(".easyui-tooltip").tooltip({
                    onShow: function () {
                        $(this).tooltip('tip').css({
                            borderColor: '#000'
                        });
                    }
                });

            },
            rowStyler:rowStylerFun,
            enableHeaderClickMenu: true,
            enableHeaderContextMenu: true,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });
        //創建下拉查詢條件
        $.ajax({
            url: ctx+"/system/dict/getDictByType/audit_status",
            dataType:"json",
            type: "GET",
            success: function (data) {
                //绑定第一个下拉框
                $("#qysjzt").combobox({
                    data: data,
                    valueField: "value",
                    textField: "label",
                    editable: false,
                    panelHeight: 400,
                    loadFilter: function (data) {
                        data.unshift({value: '', label: '請選擇'});
                        return data;
                    }
                });
            },
            error: function (error) {
                alert("初始化下拉控件失败");
            }
        });
    });
    function operation(value, row, index) {
        return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('輻射安全許可證變更申請單詳情',ctx+'/safetyPermit/view/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
    };
    //创建查询对象并查询
    function cx() {
        var obj = $("#searchFrom").serializeObject();
        dg.datagrid('load', obj);
    }

    //导出excel
    function exportExcel() {
        var form = document.getElementById("searchFrom");
        searchFrom.action = ctx+'/safetyPermit/exportExcel';
        form.submit();
    }
</script>
</body>
</html>