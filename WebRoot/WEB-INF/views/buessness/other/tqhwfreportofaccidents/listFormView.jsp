<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>工傷事故呈報表</title>
<script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
<script src='${ctx}/static/js/other/tqhwfreportofaccidents.js?random=<%= Math.random()%>'></script>
</head>
<body>
	<form id="mainform" action="${ctx}/tqhwfreportofaccidents/${action}"
		method="post">
		<div class="commonW">
			<div class="headTitle">工傷事故呈報表</div>
			<div class="position_L">
				任務編碼：<span style="color:#999;">
                   <c:choose>
						<c:when test="${tQhWfreportofaccidents.serialno==null}">
                                                    提交成功后自動編碼
                    </c:when>
						<c:otherwise>
                        ${tQhWfreportofaccidents.serialno}
                    </c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_L1 margin_L">
				填單時間：<span style="color:#999;"> 
				    <c:choose>
						<c:when test="${tQhWfreportofaccidents.createtime==null}">
                        YYYY/MM/DD
                        </c:when>
						<c:otherwise>
							<input style="border:0px"  readonly="true" value="<fmt:formatDate value='${tQhWfreportofaccidents.createtime}' pattern='yyyy-MM-dd HH:mm:ss'/>"/>
						</c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_R margin_R">填單人：${tQhWfreportofaccidents.makerno}/${tQhWfreportofaccidents.makername}</div>
			<div class="clear"></div>
			<table class="formList">
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">受傷員工基本信息</td>
							</tr>
							<tr align="center">
								<td>工號</td>
								<td><input id="dealno" name="dealno" readonly
									class="easyui-validatebox  inputCss" data-options="width:150,required:true"
									value="${tQhWfreportofaccidents.dealno }" /></td>
								<td>姓名</td>
								<td><input id="dealname" name="dealname"
									class="easyui-validatebox inputCss" data-options="width: 150" readonly
									value="${tQhWfreportofaccidents.dealname }" /></td>
								<td>性別</td>
								<td><input id="hurtsex" name="hurtsex"
									class="easyui-validatebox inputCss" data-options="width: 80" readonly
									value="${tQhWfreportofaccidents.hurtsex }" /></td>
								<td>年齡</td>
								<td><input id="hurtage" name="hurtage"
									class="easyui-validatebox inputCss" data-options="width: 80" readonly
									value="${tQhWfreportofaccidents.hurtage }" /></td>
								<td>入集團日期</td>
								<td><input id="hurtjointime" name="hurtjointime"
									class="easyui-validatebox inputCss" datefmt="yyyy-MM-dd" readonly
									data-options="width: 150"
									value="<fmt:formatDate value="${tQhWfreportofaccidents.hurtjointime}"/>" />
								</td>
							</tr>
							<tr align="center">
								<td>資位</td>
								<td><input id="hurtposition" name="hurtposition"
									class="easyui-validatebox inputCss" data-options="width: 150" readonly
									value="${tQhWfreportofaccidents.hurtposition }" /></td>
								<td>聯繫方式</td>
								<td><input id="hurtphone" name="hurtphone" readonly
									class="easyui-validatebox inputCss" data-options="width: 150,required:true"
									value="${tQhWfreportofaccidents.hurtphone }" /></td>
								<td>單位代碼</td>
								<td><input id="hurtdeptno" name="hurtdeptno"
									class="easyui-validatebox inputCss" data-options="width: 80" readonly
									value="${tQhWfreportofaccidents.hurtdeptno }" /></td>
								<td>單位名稱</td>
								<td colspan="3"><input id="hurtdeptname"
									name="hurtdeptname" class="easyui-validatebox  inputCss"
									data-options="width: 350"
									value="${tQhWfreportofaccidents.hurtdeptname }" /></td>
							</tr>
							<tr align="center">
								<td>身份證號碼</td>
								<td colspan="3"><input id="hurtidcard" name="hurtidcard"
									class="easyui-validatebox inputCss" data-options="width: 150" readonly
									value="${tQhWfreportofaccidents.hurtidcard }" /></td>
								<td>廠區</td>
								<td><input id="hurtfac" name="hurtfac"
									class="easyui-validatebox inputCss" data-options="width: 80" readonly
									value="${tQhWfreportofaccidents.hurtfac }" />
									<input id="hurtfacid" name="hurtfacid"
									class="easyui-validatebox inputCss" data-options="width: 80" readonly type=hidden
									value="${tQhWfreportofaccidents.hurtfacid }" />
									</td>
								<td>法人</td>
								<td colspan="3"><input id="hurtcorporate" name="hurtcorporate" 
								    class="easyui-combobox  inputCss"  data-options="width: 350" disabled="disabled"
									value="${tQhWfreportofaccidents.hurtcorporate }" /></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td rowspan="3" class="td_style1">員工受傷信息</td>
								<td>事故類型</td>
								<td colspan="2" style="width: 170px; "> <div class="hurtDiv"></div>
								<input id="hurtinoutfac" name="hurtinoutfac" class="easyui-validatebox"
									data-options="width: 80" type="hidden"
									value="${tQhWfreportofaccidents.hurtinoutfac }" />
								<input id="disOrEnabled" type="hidden" value="disabled"/>
								</td>
								<td>受傷廠區</td>
								<td colspan="2"><input id="hurtstayfac" name="hurtstayfac" disabled="disabled"
									class="easyui-combobox inputCss" data-options="width:150,required:true"
									value="${tQhWfreportofaccidents.hurtstayfac }" /></td>
								<td>受傷地點</td>
								<td colspan="2"><input id="hurtplace" name="hurtplace" readonly
									class="easyui-validatebox  inputCss" data-options="width: 150,required:true"
									value="${tQhWfreportofaccidents.hurtplace }" /></td>
							</tr>
							<tr>
								<td colspan="9">
									<div class="hurtkindDiv"></div> 
									<input id="hurtkind" name="hurtkind" type="hidden" class="easyui-validatebox"
									data-options="width: 150" disabled
									value="${tQhWfreportofaccidents.hurtkind }" />
									<input id="hurtothermeno1" name="hurtothermeno1" disabled="disabled"  type="hidden"
									class="easyui-validatebox" data-options="width: 30" 
									value="${tQhWfreportofaccidents.hurtothermeno}"/>
								    <%-- <input id="hurtothermeno" name="hurtothermeno"  class="easyui-validatebox"
									data-options="width: 30"
									value="${tQhWfreportofaccidents.hurtothermeno}" /> --%>
								</td>
							</tr>
							<tr align="center">
								<td>受傷時間</td>
								<!--  -->
								<td colspan="2"><input id="hurttime" name="hurttime"
									class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" disabled="disabled"
									data-options="width: 150,required:true" onchange="compareTime(this);"
									value="<fmt:formatDate pattern="yyyy-MM-dd HH:mm:ss" value="${tQhWfreportofaccidents.hurttime}"/>" />
								</td>
								<td>工傷提報時間</td>
								<td colspan="2"><input id="hurtreporttime"
									name="hurtreporttime" class="easyui-my97"
									datefmt="yyyy-MM-dd" style="width:150px;"
									data-options="required:true,prompt:'表單提交成功日期'" disabled="disabled"
									value="<fmt:formatDate pattern="yyyy-MM-dd HH:mm:ss" value="${tQhWfreportofaccidents.hurtreporttime}"/>" />
								</td>
								<td>證明人/聯繫方式</td>
								<td colspan="2"><input id="witnessnam" name="witnessnam" style="width:50px;text-align:right"
									class="easyui-validatebox inputCss" data-options="required:true" readonly
									value="${tQhWfreportofaccidents.witnessnam }" />&nbsp;/&nbsp;<input
									id="witnessphone" name="witnessphone" style="width:80px;text-align:left" readonly
									class="easyui-validatebox inputCss" data-options="required:true,"
									value="${tQhWfreportofaccidents.witnessphone }" /></td>
							</tr>
							<tr align="center">
								<td class="td_style1">工傷投保信息</td>
								<td>單位編號</td>
								<td colspan="2"><input id="insurancedeptno" name="insurancedeptno" class="easyui-validatebox inputCss"
									data-options="width: 150" readonly 
									value="${tQhWfreportofaccidents.insurancedeptno }" /></td>
								<td>個人參保電腦號</td>
								<td colspan="3"><input id="insurancecomputer" name="insurancecomputer" class="easyui-validatebox inputCss" readonly
									data-options="width: 150"
									value="${tQhWfreportofaccidents.insurancecomputer }" /></td>
								<td>個人參保時間</td>
								<td colspan="2"><input id="insurancetime" name="insurancetime" class="easyui-datebox inputCss"  disabled="disabled"
									data-options="width: 150"
									value="${tQhWfreportofaccidents.insurancetime}"/>
								</td>
							</tr>
							<tr align="center">
								<td class="td_style1">工傷提報單位</td>
								<td>負責人</td>
								<td colspan="2"><input id="reportchargeno"	name="reportchargeno" class="easyui-validatebox inputCss"
									data-options="width:70" onblur="queryNameByNo(this);" readonly style="text-align:right"
									value="${tQhWfreportofaccidents.reportchargeno }" />&nbsp;/&nbsp;<input
									id="reportchargenam" name="reportchargenam"
									class="easyui-validatebox inputCss" data-options="width:50" readonly style="text-align:left"
									value="${tQhWfreportofaccidents.reportchargenam }" /></td>
								<td>聯繫電話</td>
								<td><input id="reportchargephone" name="reportchargephone" readonly
									class="easyui-validatebox inputCss" data-options="width: 80"
									value="${tQhWfreportofaccidents.reportchargephone }" /></td>
								<td width = "50px">郵箱</td>
								<td><input id="repotrchargemail" name="repotrchargemail" readonly
									class="easyui-validatebox inputCss" data-options="width: 200" onblur="valdEmail(this)"
									value="${tQhWfreportofaccidents.repotrchargemail }" /></td>
								<td width = "50px">單位</td>
								<td><input id="reportdeptnam" name="reportdeptnam" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfreportofaccidents.reportdeptnam }" /></td>

							</tr>
							<tr align="center">
								<td>事故說明</td>
								<td align="left" colspan="9"><textarea id="accidentsmemo" name="accidentsmemo"
										style="width:800px;height:100px;" rows="5" cols="6" readonly
										>${tQhWfreportofaccidents.accidentsmemo }</textarea></td>
							</tr>
							<tr align="center">
								<td>附件</td>
								<td colspan="9" class="td_style1">
								 <input type="hidden" id="attachids" name="attachids"
									value="${tQhWfreportofaccidents.attachids }" />
									<div id="dowloadUrl">
										<c:forEach items="${file}" varStatus="i" var="item">
											<div id="${item.id}"
												style="line-height:30px;margin-left:5px;" class="float_L">
												<div class="float_L">
													<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
												</div>
											</div>
										</c:forEach>
									</div></td>
							</tr>
							<tr align="center">
								<td>備註</td>
								<td colspan="9" align="left">
									1.事故單位2小時內填單至CAA各區工安辦公室錄入工傷系統--說明工傷經過（需註明人、時、地、物）;<br/>
                                    2.錄入工傷系統后各單位負責人48小時內自行至iPEBG電子簽核平臺系統中錄入工傷員工信息并跟催系統工傷事故呈報表簽核進度;<br/>
                                    3.受傷員工單位及各職能單位主管簽核完畢后，工傷負責人請攜帶病歷、診斷證明、證言證詞、勞動合同、考勤、工傷認定申請表等原件至職業衛生課進行簽核。
								</td>
							</tr>
							<tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)" onclick="showWfImag('${processId}','工傷事故呈報表申請流程圖','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhWfreportofaccidents.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                   style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>

                            </td>
                        </tr>
						</table>
					</td>
				</tr>
			</table>
		</div>
		<div id="dlg"></div>
	</form>
</body>
</html>