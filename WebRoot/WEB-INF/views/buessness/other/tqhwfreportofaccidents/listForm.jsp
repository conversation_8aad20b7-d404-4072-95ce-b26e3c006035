<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>工傷事故呈報表</title>
<script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
<script src='${ctx}/static/js/other/tqhwfreportofaccidents.js?random=<%= Math.random()%>'></script>
</head>
<body>
	<form id="mainform" action="${ctx}/tqhwfreportofaccidents/${action}"
		method="post">
		<input id="ids" name="ids" type="hidden" value="${tQhWfreportofaccidents.id }" /> 
		<input id="serialno"name="serialno" type="hidden" value="${tQhWfreportofaccidents.serialno }" /> 
		<input id="createtime" name="tQhWfreportofaccidents.createtime" type="hidden" value="${tQhWfreportofaccidents.createtime }" />
		<input id="makerno" name="tQhWfreportofaccidents.makerno" type="hidden" value="${tQhWfreportofaccidents.makerno }"/>
		<input id="makername" name="tQhWfreportofaccidents.makername" type="hidden" value="${tQhWfreportofaccidents.makername }"/>
		<input id="makerdeptno" name="tQhWfreportofaccidents.dealdeptno" type="hidden" value="${tQhWfreportofaccidents.dealdeptno }"/>
		<input id="makerfactoryid" name="tQhWfreportofaccidents.dealfacid" type="hidden" value="${tQhWfreportofaccidents.dealfacid }"/>
		<div class="commonW">
			<div class="headTitle">工傷事故呈報表</div>
			<div class="position_L">
				任務編碼：<span style="color:#999;">
                   <c:choose>
						<c:when test="${tQhWfreportofaccidents.serialno==null}">
                                                    提交成功后自動編碼
                    </c:when>
						<c:otherwise>
                        ${tQhWfreportofaccidents.serialno}
                    </c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_L1 margin_L">
				填單時間：<span style="color:#999;"> 
				    <c:choose>
						<c:when test="${tQhWfreportofaccidents.createtime==null}">
                        YYYY/MM/DD HH:mm:ss
                        </c:when>
						<c:otherwise>
							<input style="border:0px" style="width: 120px" readonly="true" value="<fmt:formatDate value='${tQhWfreportofaccidents.createtime}' pattern='yyyy-MM-dd HH:mm:ss'/>"/>
						</c:otherwise>
					</c:choose>
				</span>
			</div>
			<c:if test="${empty tQhWfreportofaccidents.makerno}">
				<div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
			</c:if>
			<c:if test="${not empty tQhWfreportofaccidents.makerno}">
				<div class="position_R margin_R">填單人：${tQhWfreportofaccidents.makerno}/${tQhWfreportofaccidents.makername}</div>
			</c:if>
			<div class="clear"></div>
			<table class="formList">
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">受傷員工基本信息</td>
							</tr>
							<tr align="center">
								<td>工號&nbsp;<font color="red">*</font></td>
								<td><input id="dealno" name="dealno"
									class="easyui-validatebox" data-options="width:150,required:true"
									value="${tQhWfreportofaccidents.dealno }" onblur="queryUserInfo(this);"/></td>
								<td>姓名</td>
								<td><input id="dealname" name="dealname"
									class="easyui-validatebox inputCss" data-options="width: 150" readonly
									value="${tQhWfreportofaccidents.dealname }" /></td>
								<td>性別</td>
								<td><input id="hurtsex" name="hurtsex"
									class="easyui-validatebox inputCss" data-options="width: 80" readonly
									value="${tQhWfreportofaccidents.hurtsex }" /></td>
								<td>年齡</td>
								<td><input id="hurtage" name="hurtage"
									class="easyui-validatebox inputCss" data-options="width: 80" readonly
									value="${tQhWfreportofaccidents.hurtage }" /></td>
								<td>入集團日期</td>
								<td><input id="hurtjointime" name="hurtjointime"
									class="easyui-validatebox inputCss" datefmt="yyyy-MM-dd" readonly
									data-options="width: 150"
									value="<fmt:formatDate value="${tQhWfreportofaccidents.hurtjointime}"/>" />
								</td>
							</tr>
							<tr align="center">
								<td>資位</td>
								<td><input id="hurtposition" name="hurtposition"
									class="easyui-validatebox inputCss" data-options="width: 150" readonly
									value="${tQhWfreportofaccidents.hurtposition }" /></td>
								<td>聯繫方式&nbsp;<font color="red">*</font></td>
								<td><input id="hurtphone" name="hurtphone" onblur="valdMobilephone(this)"
									class="easyui-validatebox" data-options="width: 150,required:true,prompt:'請輸入11位手機號碼'"
									value="${tQhWfreportofaccidents.hurtphone }" /></td>
								<td>單位代碼</td>
								<td><input id="hurtdeptno" name="hurtdeptno"
									class="easyui-validatebox inputCss" data-options="width: 80" readonly
									value="${tQhWfreportofaccidents.hurtdeptno }" /></td>
								<td>單位名稱</td>
								<td colspan="3"><input id="hurtdeptname"
									name="hurtdeptname" class="easyui-validatebox"
									data-options="width: 350"
									value="${tQhWfreportofaccidents.hurtdeptname }" /></td>
							</tr>
							<tr align="center">
								<td>身份證號碼</td>
								<%--<td colspan="3"><input id="hurtidcard" name="hurtidcard"
									class="easyui-validatebox inputCss" data-options="width: 150" readonly
									value="${tQhWfreportofaccidents.hurtidcard }" /></td>--%>
								<td colspan="3"><input id="hurtidcard" name="hurtidcard"
													   class="easyui-validatebox" data-options="width: 150,required:true"
													   value="${tQhWfreportofaccidents.hurtidcard }" /></td>
								<td>廠區</td>
								<td><input id="hurtfac" name="hurtfac"
									class="easyui-validatebox inputCss" data-options="width: 80" readonly
									value="${tQhWfreportofaccidents.hurtfac }" />
									<input id="hurtfacid" name="hurtfacid"
									class="easyui-validatebox inputCss" data-options="width: 80" readonly type=hidden
									value="${tQhWfreportofaccidents.hurtfacid }" />
									</td>
								<td>法人</td>
								<td colspan="3"><input id="hurtcorporate" name="hurtcorporate" 
								    class="easyui-combobox" readonly data-options="width: 350"
									value="${tQhWfreportofaccidents.hurtcorporate }" /></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td rowspan="3" class="td_style1">員工受傷信息</td>
								<td>事故類型&nbsp;<font color="red">*</font></td>
								<td colspan="2"> <div class="hurtDiv"></div>
								<input id="hurtinoutfac" name="hurtinoutfac" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${tQhWfreportofaccidents.hurtinoutfac }" /></td>
								<td>受傷廠區&nbsp;<font color="red">*</font></td>
								<td colspan="2"><input id="hurtstayfac" name="hurtstayfac"
									class="easyui-combobox" data-options="width:150,required:true"
									value="${tQhWfreportofaccidents.hurtstayfac }" /></td>
								<td>受傷地點&nbsp;<font color="red">*</font></td>
								<td colspan="2"><input id="hurtplace" name="hurtplace"
									class="easyui-validatebox" data-options="width: 150,required:true"
									value="${tQhWfreportofaccidents.hurtplace }" />
									<%-- <input id='hurtothermeno' name='hurtothermeno' 
									class='easyui-validatebox' data-options='width: 30' 
									value='${tQhWfreportofaccidents.hurtothermeno}'/> --%>
								</td>
							</tr>
							<tr>
								<td colspan="9">
									<div class="hurtkindDiv">
									<%-- <input id="hurtothermeno" name="hurtothermeno" disabled="disabled" 
									class="easyui-validatebox" data-options="width: 30" 
									value="${tQhWfreportofaccidents.hurtothermeno}"/> --%>
									</div> 
									<input id="hurtkind" name="hurtkind" type="hidden" class="easyui-validatebox"
									data-options="width: 150"
									value="${tQhWfreportofaccidents.hurtkind }" />
									<input id="hurtothermeno1" name="hurtothermeno1" disabled="disabled"  type="hidden"
									class="easyui-validatebox" data-options="width: 30" 
									value="${tQhWfreportofaccidents.hurtothermeno}"/>
								    <%-- <input id="hurtothermeno" name="hurtothermeno"  class="easyui-validatebox"
									data-options="width: 30"
									value="${tQhWfreportofaccidents.hurtothermeno}" /> --%>
								</td>	
							</tr>
							<tr align="center">
								<td>受傷時間&nbsp;<font color="red">*</font></td>
								<!--  -->
								<td colspan="2">
								<input id="hurttime" name="hurttime" 
									class="Wdate" data-options="width:180" style="width:150px"
									value="<fmt:formatDate  pattern="yyyy-MM-dd HH:mm:ss"
									value="${tQhWfreportofaccidents.hurttime}"/>"   
									onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',maxDate:'%y-%M-%d %H:%m:%s'})" />
								</td>
								<td>工傷提報時間</td>
								<td colspan="2"><input id="hurtreporttime"
									name="hurtreporttime" class="easyui-my97"
									datefmt="yyyy-MM-dd" style="width:150px;"
									data-options="required:true,prompt:'表單提交成功日期'" disabled="disabled"
									value="<fmt:formatDate pattern="yyyy-MM-dd HH:mm:ss" value="${tQhWfreportofaccidents.hurtreporttime}"/>" />
								</td>
								<td style="width:100px;">證明人/聯繫方式&nbsp;<font color="red">*</font></td>
								<td colspan="3"><input id="witnessnam" name="witnessnam" style="width:50px;text-align:right"
									class="easyui-validatebox" data-options="required:true"
									value="${tQhWfreportofaccidents.witnessnam }" />/<input
									id="witnessphone" name="witnessphone" onblur="valdMobilephone(this)"
									class="easyui-validatebox" data-options="required:true,prompt:'請輸入11位手機號碼'" style="text-align:left"
									value="${tQhWfreportofaccidents.witnessphone }" /></td>
							</tr>
							<tr align="center">
								<td class="td_style1">工傷投保信息</td>
								<td>單位編號</td>
								<td colspan="2"><input id="insurancedeptno" name="insurancedeptno" class="easyui-validatebox"
									data-options="width: 150" readonly
									value="${tQhWfreportofaccidents.insurancedeptno }" /></td>
								<td>個人參保電腦號</td>
								<td colspan="3"><input id="insurancecomputer" name="insurancecomputer" class="easyui-validatebox" readonly
									data-options="width: 150"
									value="${tQhWfreportofaccidents.insurancecomputer }" /></td>
								<td style="width:90px;">個人參保時間</td>
								<td colspan="2"><input id="insurancetime" name="insurancetime" class="easyui-datebox"  disabled="disabled"
									data-options="width: 150"
									value="${tQhWfreportofaccidents.insurancetime}"/>
								</td>
							</tr>
							<tr align="center">
								<td class="td_style1">工傷提報單位</td>
								<td>負責人&nbsp;<font color="red">*</font></td>
								<td colspan="2" align="center"><input id="reportchargeno"	name="reportchargeno" class="easyui-validatebox"
									data-options="width:80,prompt:'請輸入工號',required:true" onblur="queryNameByNo(this);" style="text-align:right"
									value="${tQhWfreportofaccidents.reportchargeno }" />/<input
									id="reportchargenam" name="reportchargenam" style="text-align:left"
									class="easyui-validatebox" data-options="width: 80" readonly
									value="${tQhWfreportofaccidents.reportchargenam }" /></td>
								<td>聯繫電話&nbsp;<font color="red">*</font></td>
								<td><input id="reportchargephone" name="reportchargephone"
									class="easyui-validatebox" data-options="width: 80,required:true" onblur="valdApplyTel(this)"
									value="${tQhWfreportofaccidents.reportchargephone }" /></td>
								<td width = "50px">郵箱&nbsp;<font color="red">*</font></td>
								<td><input id="repotrchargemail" name="repotrchargemail"
									class="easyui-validatebox" data-options="width: 180,required:true" onblur="valdEmail(this)"
									value="${tQhWfreportofaccidents.repotrchargemail }" /></td>
								<td width = "50px">單位&nbsp;<font color="red">*</font></td>
								<td><input id="reportdeptnam" name="reportdeptnam"
									class="easyui-validatebox" data-options="width: 150,required:true"
									value="${tQhWfreportofaccidents.reportdeptnam }" /></td>

							</tr>
							<tr align="center">
								<td>事故說明&nbsp;<font color="red">*</font></td>
								<td align="left" colspan="9"><textarea id="accidentsmemo" name="accidentsmemo" data-options="required:true"
										style="width:1000px;height:80px;" rows="5" cols="6" 
										placeholder="最多輸入500個字！" maxlength="500" 
										>${tQhWfreportofaccidents.accidentsmemo }</textarea></td>				
							</tr>
							<!-- onkeyup="wordStatic(this)" <tr><td colspan="10" style="height:3px"><span id = "num">0</span>/500</td></tr> -->
							<tr align="center">
								<td>附件&nbsp;<font color="red">*</font></td>
								<td colspan="9" class="td_style1"><span
									class="sl-custom-file"> <input type="button"
										value="点击上传文件" class="btn-file" />
								<input id="attachidsUpload" name="attachidsUpload" type="file"
										onchange="oosUploadFile('${workFlowId}');" class="ui-input-file" />
								</span> <input type="hidden" id="attachids"
									name="attachids" value="${tQhWfreportofaccidents.attachids }" />
									<div id="dowloadUrl">
									<c:forEach items="${file}" varStatus="i" var="item">
											<div id="${item.id}"
												style="line-height:30px;margin-left:5px;" class="float_L">
												<div class="float_L">
													<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
												</div>
											</div>
										</c:forEach>
									</div></td>
							</tr>
							<tr align="center">
								<td>備註</td>
								<td colspan="9" align="left">
									1.事故單位2小時內填單至CAA各區工安辦公室錄入工傷系統--說明工傷經過（需註明人、時、地、物）;<br/>
                                    2.錄入工傷系統后各單位負責人48小時內自行至iPEBG電子簽核平臺系統中錄入工傷員工信息并跟催系統工傷事故呈報表簽核進度;<br/>
                                    3.受傷員工單位及各職能單位主管簽核完畢后，工傷負責人請攜帶病歷、診斷證明、證言證詞、勞動合同、考勤、工傷認定申請表等原件至職業衛生課進行簽核。
								</td>
							</tr>
							<tr>
								<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
									<a href="javascript:void(0)"
									onclick="showWfImag('${workFlowId}','工傷事故呈報表申請流程圖','');">點擊查看簽核流程圖</a>
								</th>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td colspan="10" style="text-align:left;">
						<table class="flowList"
							style="margin-left:5px;margin-top:5px;width:99%">
							<tr>
								<td style="border:none">
									<table width="18%" style="float: left;margin-left: 5px;">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">課級主管</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole($('#hurtdeptno').val(),'kchargeno','kchargename',$('#hurtfacid').val())"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="kchargeno" name="kchargeno"
												class="easyui-validatebox" data-options="width:80" readonly
												value="${tQhWfreportofaccidents.kchargeno }" /> 
												/<input id="kchargename" name="kchargename"
												readonly class="easyui-validatebox" data-options="width:80"
												value="${tQhWfreportofaccidents.kchargename }" /></td>
										</tr>
									</table>
									<table width="18%" style="float: left;margin-left: 5px;">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">部級主管</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole($('#hurtdeptno').val(),'bchargeno','bchargename',$('#hurtfacid').val())"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="bchargeno"
												name="bchargeno"
												class="easyui-validatebox" data-options="width: 80" readonly
												value="${tQhWfreportofaccidents.bchargeno }" /> /<input
												id="bchargename" name="bchargename"
												class="easyui-validatebox" data-options="width: 80" readonly
												value="${tQhWfreportofaccidents.bchargename }" /></td>
										</tr>
									</table>
									<table width="18%" style="float: left;margin-left: 5px;">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">廠級主管</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																 onclick="selectRole18('cchargeTable',$('#hurtdeptno').val(),'cchargeno','cchargename',$('#hurtfacid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="cchargeno"
												name="cchargeno"
												class="easyui-validatebox" data-options="width: 80" readonly
												value="${tQhWfreportofaccidents.cchargeno }" /> /<input
												id="cchargename" name="cchargename"
												class="easyui-validatebox" data-options="width: 80" readonly
												value="${tQhWfreportofaccidents.cchargename }" /></td>
										</tr>
									</table>
									<table width="18%" style="float: left;margin-left: 5px;"
										id="zchargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">製造處級主管</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole3('zchargeTable',$('#hurtdeptno').val(),'zchargeno','zchargename',$('#hurtfacid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="zchargeno"
												name="zchargeno"
												class="easyui-validatebox" data-options="width: 80,required:true" readonly
												value="${tQhWfreportofaccidents.zchargeno }" />
												<font color="red">*</font>/<input id="zchargename" name="zchargename"
												class="easyui-validatebox" data-options="width: 80,required:true" readonly
												value="${tQhWfreportofaccidents.zchargename }" /></td>
										</tr>
									</table>
									<table width="18%" style="float: left;margin-left: 5px;"
										id="zcchargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">製造總處級主管</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole3('zcchargeTable',$('#hurtdeptno').val(),'zcchargeno','zcchargename',$('#hurtfacid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="zcchargeno"
												name="zcchargeno"
												class="easyui-validatebox" data-options="width: 80" readonly
												value="${tQhWfreportofaccidents.zcchargeno }" /> 
												/<input id="zcchargename" name="zcchargename"
												class="easyui-validatebox" data-options="width: 80" readonly
												value="${tQhWfreportofaccidents.zcchargename }" /></td>
										</tr>
									</table>
								</td>
							</tr>
							<tr>
								<td style="border:none">
									<table width="18%" style="float: left;margin-left: 5px;"
										id="pcchargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">產品處級主管</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole3('pcchargeTable',$('#hurtdeptno').val(),'pcchargeno','pcchargename',$('#hurtfacid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="pcchargeno"	name="pcchargeno"
												class="easyui-validatebox"
												data-options="width: 80" readonly
												value="${tQhWfreportofaccidents.pcchargeno }" />
												/ <input id="pcchargename"	name="pcchargename" class="easyui-validatebox"
												data-options="width: 80" readonly
												value="${tQhWfreportofaccidents.pcchargename }" /></td>
										</tr>
									</table>
									<table width="18%" style="float: left;margin-left: 5px;"
										id="hrchargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">人資參保窗口確認</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole2(17,'hrchargeTable','hrchargeno','hrchargename',$('#hurtfacid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="hrchargeno"
												name="hrchargeno"
												class="easyui-validatebox"
												data-options="width: 80,required:true" readonly
												value="${tQhWfreportofaccidents.hrchargeno }" /> <font
												color="red">*</font>/ <input id="hrchargename"
												name="hrchargename"
												class="easyui-validatebox"
												data-options="width: 80,required:true" readonly
												value="${tQhWfreportofaccidents.hrchargename }" /></td>
										</tr>
									</table>
									<table width="18%" style="float: left;margin-left: 5px;"
										id="hrcchargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">人資主管</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole2(18,'hrcchargeTable','hrcchargeno','hrcchargename',$('#hurtfacid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="hrcchargeno"
												name="hrcchargeno"
												class="easyui-validatebox"
												data-options="width: 80,required:true" readonly
												value="${tQhWfreportofaccidents.hrcchargeno }" /><font color="red">*</font>
												/ <input id="hrcchargename" name="hrcchargename"
												class="easyui-validatebox" data-options="width: 80,required:true" readonly
												value="${tQhWfreportofaccidents.hrcchargename }" /></td>
										</tr>
									</table>
									<table width="18%" style="float: left;margin-left: 5px;"
										id="xfchargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">消防暨工業安全部</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole2(19,'xfchargeTable','xfchargeno','xfchargename',$('#hurtfacid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="xfchargeno"
												name="xfchargeno"
												class="easyui-validatebox"
												data-options="width: 80,required:true" readonly
												value="${tQhWfreportofaccidents.xfchargeno }" /><font color="red">*</font> 
												/<input id="xfchargename" name="xfchargename"
												class="easyui-validatebox" data-options="width: 80,required:true" readonly
												value="${tQhWfreportofaccidents.xfchargename }" /></td>
										</tr>
									</table>
									<table width="18%" style="float: left;margin-left: 5px;"
										id="zywschargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">職業衛生課</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole2(20,'zywschargeTable','zywschargeno','zywschargename',$('#hurtfacid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="zywschargeno" name="zywschargeno"
												class="easyui-validatebox" data-options="width: 80,required:true" readonly
												value="${tQhWfreportofaccidents.zywschargeno }" /> 
												<font color="red">*</font>/ 
												<input id="zywschargename" name="zywschargename"
												class="easyui-validatebox" data-options="width:80,required:true" readonly
												value="${tQhWfreportofaccidents.zywschargename }" />
											</td>
										</tr>
									</table>
								</td>
							</tr>
						</table>
					</td>
				</tr>
										<tr>
							<td colspan="10" style="text-align:left;">
								<table class="flowList" style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
									<tr>
										<td>簽核時間</td>
										<td>簽核節點</td>
										<td>簽核主管</td>
										<td>簽核意見</td>
										<td>批註</td>
										<td>簽核電腦IP</td>
									</tr>
								</table>
							</td>
						</tr>
						<tr>
							<td colspan="10" style="border:none;text-align:center;margin-top:10px">
								<a href="javascript:;" id="btnSave" class="easyui-linkbutton" data-options="iconCls:'icon-add'"
								   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
								<a href="javascript:;" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
								   style="width: 100px;" onclick="saveInfo(2);">提交</a>
							</td>
						</tr>
			</table>
		</div>
	<input type="hidden" id="deptNo" name="deptNo" value=""/>
	<input type="hidden" id="chargeNo" name="chargeNo" value=""/>
	<input type="hidden" id="chargeName" name="chargeName" value=""/>
	<input type="hidden" id="factoryId" name="factoryId" value=""/>
	<input type="hidden" id="dutyId" name="dutyId" value=""/>
	<input type="hidden" id="onlyKchargeSignle" value="1" />
	<div id="win"></div>
	</form>
</body>
</html>