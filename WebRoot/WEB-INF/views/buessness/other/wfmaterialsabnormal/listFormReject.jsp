<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>IPEG貴重物資異常處理單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfmaterialsabnormal/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfMaterialsAbnormal.id }"/>
    <input id="serialno" name="wfMaterialsAbnormal.serialno" type="hidden" value="${wfMaterialsAbnormal.serialno }"/>
    <input id="makerno" name="wfMaterialsAbnormal.makerno" type="hidden" value="${wfMaterialsAbnormal.makerno }"/>
    <input id="makername" name="wfMaterialsAbnormal.makername" type="hidden" value="${wfMaterialsAbnormal.makername }"/>
    <input id="makerdeptno" name="wfMaterialsAbnormal.makerdeptno" type="hidden" value="${wfMaterialsAbnormal.makerdeptno }"/>
    <input id="makerfactoryid" name="wfMaterialsAbnormal.makerfactoryid" type="hidden" value="${wfMaterialsAbnormal.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">${titleMemo}</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfMaterialsAbnormal.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfMaterialsAbnormal.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfMaterialsAbnormal.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfMaterialsAbnormal.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfMaterialsAbnormal.makerno}/${wfMaterialsAbnormal.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">申請人詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealno" name="wfMaterialsAbnormal.dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfMaterialsAbnormal.dealno }" onblur="queryUserInfo('dealno');"/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="wfMaterialsAbnormal.dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfMaterialsAbnormal.dealname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="wfMaterialsAbnormal.dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfMaterialsAbnormal.dealdeptno }"/>
                            </td>
                            <td width="4%">廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="dealfactoryid" name="wfMaterialsAbnormal.dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfMaterialsAbnormal.dealfactoryid }"
                                       data-options="width: 120,required:true,onSelect:function(){onchangeFactory();}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>申請單位&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealdeptname" name="wfMaterialsAbnormal.dealdeptname" class="easyui-validatebox" data-options="width: 350"
                                       value="${wfMaterialsAbnormal.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="wfMaterialsAbnormal.dealemail" class="easyui-validatebox"
                                       value="${wfMaterialsAbnormal.dealemail }" style="width:300px;"
                                       data-options="required:true" onblur="valdEmail('')"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">領用人詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">領用人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="collarNo" name="wfMaterialsAbnormal.collarNo" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfMaterialsAbnormal.collarNo }" onblur="queryUserInfo('collarNo');"/>
                            </td>
                            <td width="4%">領用人</td>
                            <td width="6%" class="td_style1">
                                <input id="collarName" name="wfMaterialsAbnormal.collarName"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfMaterialsAbnormal.collarName }"/>
                            </td>
                            <td width="4%">領用人單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="collarDeptno" name="wfMaterialsAbnormal.collarDeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfMaterialsAbnormal.collarDeptno }"/>
                            </td>
                            <td width="4%">廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="collarFactoryid" name="wfMaterialsAbnormal.collarFactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfMaterialsAbnormal.collarFactoryid }"
                                       data-options="width: 120,required:true,onSelect:function(){onchangeFactory();}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>領用單位&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="collarDeptName" name="wfMaterialsAbnormal.collarDeptName" class="easyui-validatebox" data-options="width: 350"
                                       value="${wfMaterialsAbnormal.collarDeptName }"/>
                            </td>
                            <td>樓層&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="collarFloor" name="wfMaterialsAbnormal.collarFloor" class="easyui-validatebox"
                                       value="${wfMaterialsAbnormal.collarFloor }" style="width:300px;"
                                       data-options="required:true"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">異常信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">異常類別&nbsp;<font color="red">*</font></td>
                            <td width="90%" class="td_style2">
                                <div class="abnormalTypeDiv"></div>
                                <input id="abnormalType" name="wfMaterialsAbnormal.abnormalType"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfMaterialsAbnormal.abnormalType }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="8" style="height: 100%;width: 100%">
                                <div style="overflow-x: auto;width: 100%;">
                                    <input id="maItemEntityTableIndex" type="hidden"
                                           value="<c:if test="${maItemEntity!=null&&maItemEntity.size()>0}">${maItemEntity.size() + 1}</c:if>
                                        <c:if test="${maItemEntity==null}">2</c:if>">
                                    </input>
                                    <table width="100%" id="maItemTable">
                                        <tr align="center">
                                            <td width="3%">序號</td>
                                            <td width="8%">料號&nbsp;<font color="red">*</font></td>
                                            <td width="8%">品名&nbsp;<font color="red">*</font></td>
                                            <td width="8%">品牌&nbsp;<font color="red">*</font></td>
                                            <td width="8%">規格型號&nbsp;<font color="red">*</font></td>
                                            <td width="8%">機種&nbsp;<font color="red">*</font></td>
                                            <td width="8%">單價&nbsp;<font color="red">*</font></td>
                                            <td width="8%">異常數量&nbsp;<font color="red">*</font></td>
                                            <td width="8%">損失金額&nbsp;<font color="red">*</font></td>
                                            <td width="10%">異常現象&nbsp;<font color="red">*</font></td>
                                            <td width="5%">操作</td>
                                        </tr>
                                        <c:if test="${maItemEntity!=null&&maItemEntity.size()>0}">
                                            <c:forEach items="${maItemEntity}" var="maItem" varStatus="status">
                                                <tr align="center" id="maItem${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td><input id="materialNumber${status.index+1}" name="wfMaterialsAbnormaItems[${status.index}].materialNumber"
                                                               class="easyui-validatebox" data-options="required:true" style="width:100px;"
                                                               value="${maItem.materialNumber}"/></td>
                                                    <td><input id="toolName${status.index+1}" name="wfMaterialsAbnormaItems[${status.index}].toolName"
                                                               class="easyui-validatebox" data-options="required:true" style="width:100px;"
                                                               value="${maItem.toolName}"/><font color="red">*</font></td>
                                                    <td><input id="brand${status.index+1}" name="wfMaterialsAbnormaItems[${status.index}].brand"
                                                               class="easyui-validatebox" data-options="required:true" style="width:100px;"
                                                               value="${maItem.brand}"/></td>
                                                    <td><input id="specificationType${status.index+1}" name="wfMaterialsAbnormaItems[${status.index}].specificationType"
                                                               class="easyui-validatebox" data-options="required:true"  style="width:100px;"
                                                               value="${maItem.specificationType}"/></td>
                                                    <td><input id="severalKinds${status.index+1}" name="wfMaterialsAbnormaItems[${status.index}].severalKinds"
                                                               class="easyui-validatebox" data-options="required:true" style="width:100px;"
                                                               value="${maItem.severalKinds}"/></td>
                                                    <td><input id="unitPrice${status.index+1}" name="wfMaterialsAbnormaItems[${status.index}].unitPrice"
                                                               class="easyui-validatebox" data-options="required:true" style="width:80px;" onblur="valdIsNumber(this)"
                                                               value="${maItem.unitPrice}"/></td>
                                                    <td><input id="abnormalQuantity${status.index+1}" name="wfMaterialsAbnormaItems[${status.index}].abnormalQuantity"
                                                               class="easyui-validatebox" data-options="required:true" style="width:80px;" onblur="valdIsNumber(this)"
                                                               value="${maItem.abnormalQuantity}"/></td>
                                                    <td><input id="amountLoss${status.index+1}" name="wfMaterialsAbnormaItems[${status.index}].amountLoss"
                                                               class="easyui-validatebox" data-options="required:true" style="width:80px;" onblur="valdIsNumber(this)"
                                                               value="${maItem.amountLoss}"/></td>
                                                    <td><input id="abnormalPhenomena${status.index+1}" name="wfMaterialsAbnormaItems[${status.index}].abnormalPhenomena"
                                                               class="easyui-validatebox" data-options="required:true" style="width:120px;"
                                                               value="${maItem.abnormalPhenomena}"/></td>
                                                    <td><input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="madeltr(${status.index+1});return false;"/></td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${maItemEntity==null}">
                                            <tr align="center" id="maItem1">
                                                <td>1</td>
                                                <td><input id="materialNumber1" name="wfMaterialsAbnormaItems[0].materialNumber"
                                                           class="easyui-validatebox" data-options="required:true" style="width:100px;" value=""/></td>
                                                <td><input id="toolName1" name="wfMaterialsAbnormaItems[0].toolName"
                                                           class="easyui-validatebox" data-options="required:true" style="width:100px;" value=""/></td>
                                                <td><input id="brand1" name="wfMaterialsAbnormaItems[0].brand"
                                                           class="easyui-validatebox" data-options="required:true" style="width:100px;" value=""/></td>
                                                <td><input id="specificationType1" name="wfMaterialsAbnormaItems[0].specificationType"
                                                           class="easyui-validatebox" data-options="required:true" style="width:100px;" value=""/></td>
                                                <td><input id="severalKinds1" name="wfMaterialsAbnormaItems[0].severalKinds"
                                                           class="easyui-validatebox" data-options="required:true" style="width:100px;" value=""/></td>
                                                <td><input id="unitPrice1" name="wfMaterialsAbnormaItems[0].unitPrice"
                                                           class="easyui-validatebox" data-options="required:true" style="width:80px;" onblur="valdIsNumber(this)" value=""/></td>
                                                <td><input id="abnormalQuantity1" name="wfMaterialsAbnormaItems[0].abnormalQuantity"
                                                           class="easyui-validatebox" data-options="required:true" style="width:80px;" onblur="valdIsNumber(this)" value=""/></td>
                                                <td><input id="amountLoss1" name="wfMaterialsAbnormaItems[0].amountLoss"
                                                           class="easyui-validatebox" data-options="required:true" style="width:80px;" onblur="valdIsNumber(this)" value=""/></td>
                                                <td><input id="abnormalPhenomena1" name="wfMaterialsAbnormaItems[0].abnormalPhenomena"
                                                           class="easyui-validatebox" data-options="required:true" style="width:120px;" value=""/></td>
                                                <td><input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="madeltr(1);return false;"/></td>
                                            </tr>
                                        </c:if>
                                        <tr id="addbutton_ma">
                                            <td colspan="11" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="maItemAdd" style="width:100px;float:left;" value="添加一行"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="7" class="td_style1">
                                <textarea id="remark" name="wfMaterialsAbnormal.remark"
                                          class="easyui-validatebox" style="width:800px;height:80px;" rows="5" cols="6">${wfMaterialsAbnormal.remark}</textarea>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                    <a href="javascript:void(0)"
                       onclick="showWfImag('${workFlowId}','IPEG貴重物資異常處理單','');">點擊查看簽核流程圖</a>
                </th>
            </tr>
            <tr>
                <td colspan="10" style="text-align:left;">
                    <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                        <tr>
                            <td style="border:none">
                                <table width="18%" style="float: left;margin-left: 5px;" id="scchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['scchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(51,'scchargeTable','scchargeno','scchargename',$('#dealfactoryid').combobox('getValue'),'wfMaterialsAbnormal')"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="scchargeno" name="wfMaterialsAbnormal.scchargeno"
                                                   class="easyui-validatebox" data-options="width:80,required:${requiredMap['scchargeno']}" readonly
                                                   value="${wfMaterialsAbnormal.scchargeno }"/><c:if test="${requiredMap['scchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="scchargename" name="wfMaterialsAbnormal.scchargename" readonly class="easyui-validatebox"
                                                    data-options="width:80,required:${requiredMap['scchargeno']}" value="${wfMaterialsAbnormal.scchargename }"/>
                                        </td>
                                    </tr>
                                </table>

                                <table width="18%" style="float: left;margin-left: 5px;" id="sckchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['sckchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(52,'sckchargeTable','sckchargeno','sckchargename',$('#dealfactoryid').combobox('getValue'),'wfMaterialsAbnormal')"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="sckchargeno" name="wfMaterialsAbnormal.sckchargeno"
                                                   class="easyui-validatebox" data-options="width:80,required:${requiredMap['sckchargeno']}" readonly
                                                   value="${wfMaterialsAbnormal.sckchargeno }"/><c:if test="${requiredMap['sckchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="sckchargename" name="wfMaterialsAbnormal.sckchargename" readonly class="easyui-validatebox"
                                                    data-options="width:80,required:${requiredMap['sckchargeno']}" value="${wfMaterialsAbnormal.sckchargename }"/>
                                        </td>
                                    </tr>
                                </table>

                                <table width="18%" style="float: left;margin-left: 5px;" id="lckchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['lckchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(53,'lckchargeTable','lckchargeno','lckchargename',$('#dealfactoryid').combobox('getValue'),'wfMaterialsAbnormal')"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="lckchargeno" name="wfMaterialsAbnormal.lckchargeno"
                                                   class="easyui-validatebox" data-options="width:80,required:${requiredMap['lckchargeno']}"
                                                   readonly value="${wfMaterialsAbnormal.lckchargeno }"/><c:if test="${requiredMap['lckchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="lckchargename" name="wfMaterialsAbnormal.lckchargename"
                                                    readonly class="easyui-validatebox" data-options="width:80,required:${requiredMap['lckchargeno']}"
                                                    value="${wfMaterialsAbnormal.lckchargename }"/>
                                        </td>
                                    </tr>
                                </table>

                                <table width="18%" style="float: left;margin-left: 5px;" id="cbcchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['cbcchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(54,'cbcchargeTable','cbcchargeno','cbcchargename',$('#dealfactoryid').combobox('getValue'),'wfMaterialsAbnormal')"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="cbcchargeno" name="wfMaterialsAbnormal.cbcchargeno"
                                                   class="easyui-validatebox" data-options="width:80,required:${requiredMap['cbcchargeno']}" readonly
                                                   value="${wfMaterialsAbnormal.cbcchargeno }"/><c:if test="${requiredMap['cbcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="cbcchargename" name="wfMaterialsAbnormal.cbcchargename"
                                                    readonly class="easyui-validatebox" data-options="width:80,required:${requiredMap['cbcchargeno']}"
                                                    value="${wfMaterialsAbnormal.cbcchargename }"/>
                                        </td>
                                    </tr>
                                </table>

                                <table width="18%" style="float: left;margin-left: 5px;">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="bchargeno" name="wfMaterialsAbnormal.bchargeno"
                                                   class="easyui-validatebox" data-options="width:80,required:${requiredMap['bchargeno']}"
                                                   readonly value="${wfMaterialsAbnormal.bchargeno }"/><c:if test="${requiredMap['bchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="bchargename" name="wfMaterialsAbnormal.bchargename"
                                                    readonly class="easyui-validatebox" data-options="width:80,required:${requiredMap['bchargeno']}"
                                                    value="${wfMaterialsAbnormal.bchargename }"/>
                                        </td>
                                    </tr>
                                </table>

                            </td>
                        </tr>
                        <tr>
                            <td style="border:none">
                                <table width="18%" style="float: left;margin-left: 5px;" id="ckzchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['ckzchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(55,'ckzchargeTable','ckzchargeno','ckzchargename',$('#dealfactoryid').combobox('getValue'),'wfMaterialsAbnormal')"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="ckzchargeno" name="wfMaterialsAbnormal.ckzchargeno" class="easyui-validatebox" data-options="width:80,required:${requiredMap['ckzchargeno']}"
                                                   readonly value="${wfMaterialsAbnormal.ckzchargeno }"/><c:if test="${requiredMap['ckzchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="ckzchargename" name="wfMaterialsAbnormal.ckzchargename" readonly class="easyui-validatebox" data-options="width:80,required:${requiredMap['ckzchargeno']}"
                                                    value="${wfMaterialsAbnormal.ckzchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;" id="ckkchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['ckkchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(56,'ckkchargeTable','ckkchargeno','ckkchargename',$('#dealfactoryid').combobox('getValue'),'wfMaterialsAbnormal')"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="ckkchargeno" name="wfMaterialsAbnormal.ckkchargeno"
                                                   class="easyui-validatebox" data-options="width:80,required:${requiredMap['ckkchargeno']}" readonly
                                                   value="${wfMaterialsAbnormal.ckkchargeno }"/><c:if test="${requiredMap['ckkchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="ckkchargename" name="wfMaterialsAbnormal.ckkchargename"
                                                    readonly class="easyui-validatebox" data-options="width:80,required:${requiredMap['ckkchargeno']}"
                                                    value="${wfMaterialsAbnormal.ckkchargename }"/>
                                        </td>
                                    </tr>
                                </table>

                            </td>
                        </tr>

                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="10" style="text-align:left;">
                    <iframe id="qianheLogFrame" name="qianheLogFrame"
                            src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfMaterialsAbnormal.serialno}"
                            width="100%"></iframe>
                </td>
            </tr>
            <tr>
                <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                    <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                       data-options="iconCls:'icon-add'"
                       style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                    <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                       style="width: 100px;" onclick="canelTask('${wfMaterialsAbnormal.serialno }');">取消申請</a>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" name="wfMaterialsAbnormal.isDamage" value="${wfMaterialsAbnormal.isDamage}">
    <input type="hidden" name="wfMaterialsAbnormal.causesAnalysis" value="${wfMaterialsAbnormal.causesAnalysis}">
    <input type="hidden" name="wfMaterialsAbnormal.attachids" value="${wfMaterialsAbnormal.attachids}">
    <input type="hidden" name="wfMaterialsAbnormal.sjSolutions" value="${wfMaterialsAbnormal.sjSolutions}">
    <input type="hidden" name="wfMaterialsAbnormal.lcSolutions" value="${wfMaterialsAbnormal.lcSolutions}">
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
</div>
<script src='${ctx}/static/js/other/wfmaterialsabnormal.js?random=<%= Math.random()%>'></script>
</body>
</html>