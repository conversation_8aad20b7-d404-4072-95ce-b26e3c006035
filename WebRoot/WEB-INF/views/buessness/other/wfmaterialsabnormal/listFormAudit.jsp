<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>IPEG貴重物資異常處理單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src='${ctx}/static/js/other/wfmaterialsabnormal.js?random=<%= Math.random()%>'></script>
<style type="text/css">
    .td_style2 {
        text-align: left;
    }
    .td_style3{
        border: none 0px;
    }
</style>
</head>
<body>
<form id="mainform" action="${ctx}/wfmaterialsabnormal/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfMaterialsAbnormal.id }"/>
    <input id="serialno" name="wfMaterialsAbnormal.serialno" type="hidden" value="${wfMaterialsAbnormal.serialno }"/>
    <div class="commonW">
    <div class="headTitle">${titleMemo}</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfMaterialsAbnormal.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfMaterialsAbnormal.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfMaterialsAbnormal.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfMaterialsAbnormal.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfMaterialsAbnormal.makerno}/${wfMaterialsAbnormal.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">申請人詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號</td>
                            <td width="6%" class="td_style1">
                                <input id="dealno" name="wfMaterialsAbnormal.dealno" class="easyui-validatebox inputCss"
                                       data-options="width: 80,required:true" value="${wfMaterialsAbnormal.dealno }" readonly/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="wfMaterialsAbnormal.dealname" class="easyui-validatebox inputCss"
                                       data-options="width:80" value="${wfMaterialsAbnormal.dealname }"  readonly/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="wfMaterialsAbnormal.dealdeptno" class="easyui-validatebox inputCss"
                                       data-options="width: 90" value="${wfMaterialsAbnormal.dealdeptno }" readonly />
                            </td>
                            <td width="4%">廠區</td>
                            <td width="7%" class="td_style1">
                                <input id="dealfactoryid" name="wfMaterialsAbnormal.dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfMaterialsAbnormal.dealfactoryid }"
                                       data-options="width: 120" disabled/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>申請單位</td>
                            <td colspan="3" class="td_style1">
                                <input id="dealdeptname" name="wfMaterialsAbnormal.dealdeptname" class="easyui-validatebox inputCss" data-options="width: 350"
                                       value="${wfMaterialsAbnormal.dealdeptname }" readonly/>
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="wfMaterialsAbnormal.dealemail" class="easyui-validatebox inputCss"
                                       value="${wfMaterialsAbnormal.dealemail }" style="width:300px;" readonly />
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">領用人詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">領用人工號</td>
                            <td width="6%" class="td_style1">
                                <input id="collarNo" name="wfMaterialsAbnormal.collarNo" class="easyui-validatebox inputCss"
                                       data-options="width: 80" value="${wfMaterialsAbnormal.collarNo }" readonly/>
                            </td>
                            <td width="4%">領用人</td>
                            <td width="6%" class="td_style1">
                                <input id="collarName" name="wfMaterialsAbnormal.collarName" class="easyui-validatebox inputCss"
                                       data-options="width:80" value="${wfMaterialsAbnormal.collarName }" readonly/>
                            </td>
                            <td width="4%">領用人單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="collarDeptno" name="wfMaterialsAbnormal.collarDeptno" class="easyui-validatebox inputCss"
                                       data-options="width: 90" value="${wfMaterialsAbnormal.collarDeptno }" readonly/>
                            </td>
                            <td width="4%">廠區</td>
                            <td width="7%" class="td_style1">
                                <input id="collarFactoryid" name="wfMaterialsAbnormal.collarFactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfMaterialsAbnormal.collarFactoryid }" data-options="width: 120" disabled/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>領用單位</td>
                            <td colspan="3" class="td_style1">
                                <input id="collarDeptName" name="wfMaterialsAbnormal.collarDeptName" class="easyui-validatebox inputCss" data-options="width: 350"
                                       value="${wfMaterialsAbnormal.collarDeptName }" readonly/>
                            </td>
                            <td>樓層</td>
                            <td colspan="3" class="td_style1">
                                <input id="collarFloor" name="wfMaterialsAbnormal.collarFloor" class="easyui-validatebox inputCss"
                                       value="${wfMaterialsAbnormal.collarFloor }" style="width:300px;" readonly/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">異常信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">異常類別</td>
                            <td width="90%" class="td_style2">
                                <div class="abnormalTypeDiv"></div>
                                <input id="abnormalType" name="wfMaterialsAbnormal.abnormalType"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfMaterialsAbnormal.abnormalType }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="8" style="height: 100%;width: 100%">
                                <div style="overflow-x: auto;width: 100%;">
                                    <c:if test="${maItemEntity!=null&&maItemEntity.size()>0}">
                                    <table width="100%" id="maItemTable">
                                        <tr align="center">
                                            <td width="3%">序號</td>
                                            <td width="8%">料號</td>
                                            <td width="8%">品名</td>
                                            <td width="8%">品牌</td>
                                            <td width="8%">規格型號</td>
                                            <td width="8%">機種</td>
                                            <td width="8%">單價</td>
                                            <td width="8%">異常數量</td>
                                            <td width="8%">損失金額</td>
                                            <td width="10%">異常現象</td>
                                        </tr>
                                        <c:forEach items="${maItemEntity}" var="maItem" varStatus="status">
                                            <tr align="center" id="maItem${status.index+1}">
                                                <td>${status.index+1}</td>
                                                <td>${maItem.materialNumber}</td>
                                                <td>${maItem.toolName}</td>
                                                <td>${maItem.brand}</td>
                                                <td>${maItem.specificationType}</td>
                                                <td>${maItem.severalKinds}</td>
                                                <td>${maItem.unitPrice}</td>
                                                <td>${maItem.abnormalQuantity}</td>
                                                <td>${maItem.amountLoss}</td>
                                                <td>${maItem.abnormalPhenomena}</td>
                                            </tr>
                                        </c:forEach>
                                    </table>
                                    </c:if>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="7" class="td_style1">
                                <textarea id="remark" name="wfMaterialsAbnormal.remark"
                                          class="easyui-validatebox inputCss" style="width:800px;height:80px;" rows="5" cols="6" readonly>${wfMaterialsAbnormal.remark}</textarea>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <c:choose>
                <c:when test="${not empty nodeName&&'生技/設備' eq nodeName}">
                    <tr>
                        <td>
                            <table class="formList">
                                <tr>
                                    <td colspan="8" class="td_style1">原因分析</td>
                                </tr>
                                <tr align="center">
                                    <td width="10%">是否人為損壞&nbsp;<font color="red">*</font></td>
                                    <td width="90%" class="td_style2">
                                        <div class="isDamageDiv"></div>
                                        <input id="isDamage" name="wfMaterialsAbnormal.isDamage"
                                               type="hidden" class="easyui-validatebox" data-options="width: 100"
                                               value="${wfMaterialsAbnormal.isDamage }"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td width="10%">原因分析*&nbsp;<font color="red">*</font></td>
                                    <td width="90%" class="td_style2">
                                         <textarea id="causesAnalysis" name="wfMaterialsAbnormal.causesAnalysis"
                                                   class="easyui-validatebox" style="width:800px;height:80px;" rows="5" cols="6">${wfMaterialsAbnormal.causesAnalysis}</textarea>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td width="10%">附件<font color="red">*</font></td>
                                    <td width="90%" class="td_style2">
                                        <span class="sl-custom-file">
                                            <input type="button" value="点击上传文件" class="btn-file"/>
								            <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								        </span>
                                        <input type="hidden" id="attachids" name="wfMaterialsAbnormal.attachids" value="${wfMaterialsAbnormal.attachids }"/>
                                        <div id="dowloadUrl">
                                            <c:forEach items="${file}" varStatus="i" var="item">
                                                <div id="${item.id}"
                                                     style="line-height:30px;margin-left:5px;" class="float_L">
                                                    <div class="float_L">
                                                        <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                                    </div>
                                                    <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                                </div>
                                            </c:forEach>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </c:when>
                <c:otherwise>
                    <c:if test="${wfMaterialsAbnormal.causesAnalysis!=null&&wfMaterialsAbnormal.causesAnalysis!=''&&
                    (nodeName=='生技/設備課長'||nodeName=='樓層課長'|| nodeName=='廠(部)級主管'||nodeName=='廠部廠長'|| nodeName=='倉庫主管'||nodeName=='倉庫課長' )}">
                        <tr>
                            <td>
                                <table class="formList">
                                    <tr>
                                        <td colspan="8" class="td_style1">原因分析</td>
                                    </tr>
                                    <tr align="center">
                                        <td width="10%">是否人為損壞&nbsp;<font color="red">*</font></td>
                                        <td width="90%" class="td_style2">
                                            <div class="isDamageDiv"></div>
                                            <input id="isDamageAudit" name="wfMaterialsAbnormal.isDamage"
                                                   type="hidden" class="easyui-validatebox" data-options="width: 100"
                                                   value="${wfMaterialsAbnormal.isDamage }"/>
                                        </td>
                                    </tr>
                                    <tr align="center">
                                        <td width="10%">原因分析*&nbsp;<font color="red">*</font></td>
                                        <td width="90%" class="td_style2">
                                         <textarea name="wfMaterialsAbnormal.causesAnalysis"
                                                   class="easyui-validatebox inputCss" style="width:800px;height:80px;" rows="5" cols="6" readonly>${wfMaterialsAbnormal.causesAnalysis}</textarea>
                                        </td>
                                    </tr>
                                    <tr align="center">
                                        <td width="10%">附件<font color="red">*</font></td>
                                        <td width="90%" class="td_style2">
                                            <c:if test="${wfMaterialsAbnormal.attachids!=null}">
                                                <div>
                                                    <c:forEach items="${file}" varStatus="i" var="item">
                                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                                            <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                                        </div>
                                                    </c:forEach>
                                                </div>
                                            </c:if>
                                            <c:if test="${wfMaterialsAbnormal.attachids==null}">無</c:if>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </c:if>
                </c:otherwise>
            </c:choose>
            <c:choose>
                <c:when test="${not empty nodeName&&'生技/設備課長' eq nodeName}">
                    <tr>
                        <td>
                            <table class="formList">
                                <tr>
                                    <td colspan="8" class="td_style1">生技/設備課長解決對策</td>
                                </tr>
                                <tr align="center">
                                    <td width="10%">解決對策*&nbsp;<font color="red">*</font></td>
                                    <td width="90%" class="td_style2">
                                         <textarea id="sjSolutions" name="wfMaterialsAbnormal.sjSolutions"
                                                   class="easyui-validatebox" style="width:800px;height:80px;" rows="5" cols="6">${wfMaterialsAbnormal.sjSolutions}</textarea>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </c:when>
                <c:otherwise>
                    <c:if test="${wfMaterialsAbnormal.sjSolutions!=null&&wfMaterialsAbnormal.sjSolutions!=''&&
                    (nodeName=='樓層課長'|| nodeName=='廠(部)級主管'||nodeName=='廠部廠長'|| nodeName=='倉庫主管'||nodeName=='倉庫課長' )}">
                    <tr>
                        <td>
                            <table class="formList">
                                <tr>
                                    <td colspan="8" class="td_style1">生技/設備課長解決對策</td>
                                </tr>
                                <tr align="center">
                                    <td width="10%">解決對策*&nbsp;<font color="red">*</font></td>
                                    <td width="90%" class="td_style2">
                                         <textarea name="wfMaterialsAbnormal.sjSolutions"
                                                   class="easyui-validatebox inputCss" style="width:800px;height:80px;" rows="5" cols="6" readonly>${wfMaterialsAbnormal.sjSolutions}</textarea>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    </c:if>
                </c:otherwise>
            </c:choose>
            <c:choose>
                <c:when test="${not empty nodeName&&'樓層課長' eq nodeName}">
                    <tr>
                        <td>
                            <table class="formList">
                                <tr>
                                    <td colspan="8" class="td_style1">樓層課長解決對策</td>
                                </tr>
                                <tr align="center">
                                    <td width="10%">解決對策*&nbsp;<font color="red">*</font></td>
                                    <td width="90%" class="td_style2">
                                         <textarea id="lcSolutions" name="wfMaterialsAbnormal.lcSolutions"
                                                   class="easyui-validatebox" style="width:800px;height:80px;" rows="5" cols="6">${wfMaterialsAbnormal.lcSolutions}</textarea>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </c:when>
                <c:otherwise>
                    <c:if test="${wfMaterialsAbnormal.lcSolutions!=null&&wfMaterialsAbnormal.lcSolutions!=''&&
                    (nodeName=='廠(部)級主管'||nodeName=='廠部廠長'|| nodeName=='倉庫主管'||nodeName=='倉庫課長' )}">
                        <tr>
                            <td>
                                <table class="formList">
                                    <tr>
                                        <td colspan="8" class="td_style1">樓層課長解決對策</td>
                                    </tr>
                                    <tr align="center">
                                        <td width="10%">解決對策*&nbsp;<font color="red">*</font></td>
                                        <td width="90%" class="td_style2">
                                         <textarea name="wfMaterialsAbnormal.lcSolutions"
                                                   class="easyui-validatebox inputCss" style="width:800px;height:80px;" rows="5" cols="6" readonly>${wfMaterialsAbnormal.lcSolutions}</textarea>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </c:if>
                </c:otherwise>
            </c:choose>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%" class="td_style2">
						    <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;" rows="4" cols="4" data-options="required:true,prompt:'例：'"></textarea>
                            </td>
                        </tr>
                        <c:choose>
                            <c:when test="${not empty nodeName&&'生技/設備' eq nodeName}">
                                <tr align="center">
                                    <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"  perCall="sjupdate"
                                                    serialNo="${wfMaterialsAbnormal.serialno}"></fox:action>
                                    </td>
                                </tr>
                            </c:when>
                            <c:when test="${not empty nodeName&&'生技/設備課長' eq nodeName}">
                                <tr align="center">
                                    <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"  perCall="sjkupdate"
                                                    serialNo="${wfMaterialsAbnormal.serialno}"></fox:action>
                                    </td>
                                </tr>
                            </c:when>
                            <c:when test="${not empty nodeName&&'樓層課長' eq nodeName}">
                                <tr align="center">
                                    <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"  perCall="lckupdate"
                                                    serialNo="${wfMaterialsAbnormal.serialno}"></fox:action>
                                    </td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <tr align="center">
                                    <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                                    serialNo="${wfMaterialsAbnormal.serialno}"></fox:action>
                                    </td>
                                </tr>
                            </c:otherwise>
                        </c:choose>

                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','IPEG貴重物資異常處理單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfMaterialsAbnormal.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
<div id="dlg"></div>
<input type="hidden" id="nodeName"  value="${nodeName}"/>
<input type="hidden" id="disOrEnabled"  value="disabled"/>
  </div>
</body>
</html>