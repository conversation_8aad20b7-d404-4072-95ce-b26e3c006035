<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>環保文件复印申請表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/other/tqhwfhbfilecopyprocess.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfhbfilecopyprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhWfhbfilecopyprocess.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${tQhWfhbfilecopyprocess.serialno }"/>
    <input id="createtime" name="createtime" type="hidden" value="${tQhWfhbfilecopyprocess.createtime }"/>
    <input id="makerno" name="makerno" type="hidden" value="${tQhWfhbfilecopyprocess.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${tQhWfhbfilecopyprocess.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${tQhWfhbfilecopyprocess.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${tQhWfhbfilecopyprocess.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">環保文件复印申请表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
            <c:choose>
                <c:when test="${tQhWfhbfilecopyprocess.serialno==null}">
                    提交成功后自動編碼
                </c:when>
                <c:otherwise>
                    ${tQhWfhbfilecopyprocess.serialno}
                </c:otherwise>
            </c:choose>
        </span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
            <c:choose>
                <c:when test="${tQhWfhbfilecopyprocess.createtime==null}">
                    YYYY/MM/DD
                </c:when>
                <c:otherwise>
                    <input class="inputCss" style="width: 100px" value="<fmt:formatDate value='${tQhWfhbfilecopyprocess.createtime}' pattern='yyyy-MM-dd hh:mm'/>" >
                </c:otherwise>
            </c:choose>
        </span>
        </div>
        <c:if test="${empty tQhWfhbfilecopyprocess.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty tQhWfhbfilecopyprocess.makerno}">
            <div class="position_R margin_R">填單人：${tQhWfhbfilecopyprocess.makerno}/${tQhWfhbfilecopyprocess.makername}</div>
        </c:if>
        <br>
        <div class="clear"></div>
        <table class="formList" id="buildprojecttable">
            <tr>
                <td>
                    <table>
                        <tr>
                            <td colspan="8" class="td_style1">承辦人詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealno" name="dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${tQhWfhbfilecopyprocess.dealno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="6%">承辦人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${tQhWfhbfilecopyprocess.dealname }"/>
                            </td>
                            <td width="6%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${tQhWfhbfilecopyprocess.dealdeptno }"/>
                            </td>
                            <td width="6%">廠區&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${tQhWfhbfilecopyprocess.dealfactoryid }"
                                       data-options="width: 120,required:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="2" class="td_style1">
                                <input id="dealdeptname" name="dealdeptname"
                                       class="easyui-validatebox" data-options="width: 300"
                                       value="${tQhWfhbfilecopyprocess.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="dealemail" name="dealemail" class="easyui-validatebox"
                                       value="${tQhWfhbfilecopyprocess.dealemail }" style="width:300px;"
                                       data-options="required:true" onblur="valdEmail(this)"/>
                            </td>
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealtel" name="dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${tQhWfhbfilecopyprocess.dealtel }" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">複印文件詳情</td>
                        </tr>

                        <tr align="center">
                            <td colspan="2">文件名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="6" class="td_style1">
                                <input id="filename" name="filename"
                                       class="easyui-validatebox" data-options="width: 400,required:true" value="${tQhWfhbfilecopyprocess.filename }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">複印用途&nbsp;<font color="red">*</font></td>
                            <td colspan="6" class="td_style1">
						<textarea id="copyuse" name="copyuse" class="easyui-validatebox" style="width:800px;height:80px;"
                                  rows="5" cols="6" data-options="required:true">${tQhWfhbfilecopyprocess.copyuse}</textarea>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="8">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)" onclick="showWfImag('${workFlowId}','環保文件复印申請流程圖','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="8" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="kchargeno" name="kchargeno"
                                                               class="easyui-validatebox" data-options="width: 80" readonly
                                                               value="${tQhWfhbfilecopyprocess.kchargeno }"/>
                                                        /<input id="kchargename" name="kchargename"
                                                                class="easyui-validatebox" data-options="width: 80" readonly
                                                                value="${tQhWfhbfilecopyprocess.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="bchargeno" name="bchargeno"
                                                               class="easyui-validatebox" data-options="width: 80" readonly
                                                               value="${tQhWfhbfilecopyprocess.bchargeno }"/>
                                                        /<input id="bchargename" name="bchargename"
                                                                class="easyui-validatebox" data-options="width: 80" readonly
                                                                value="${tQhWfhbfilecopyprocess.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox" data-options="width: 80" readonly
                                                               value="${tQhWfhbfilecopyprocess.cchargeno }"/>
                                                        /<input id="cchargename" name="cchargename"
                                                                class="easyui-validatebox" data-options="width: 80" readonly
                                                                value="${tQhWfhbfilecopyprocess.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zchargeno" name="zchargeno"
                                                               class="easyui-validatebox" data-options="width: 80" readonly
                                                               value="${tQhWfhbfilecopyprocess.zchargeno }"/>
                                                        /<input id="zchargename" name="zchargename"
                                                                class="easyui-validatebox" data-options="width: 80" readonly
                                                                value="${tQhWfhbfilecopyprocess.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zcchargeno" name="zcchargeno"
                                                               class="easyui-validatebox" data-options="width: 80" readonly
                                                               value="${tQhWfhbfilecopyprocess.zcchargeno }"/>
                                                        /<input id="zcchargename" name="zcchargename"
                                                                class="easyui-validatebox" data-options="width: 80" readonly
                                                                value="${tQhWfhbfilecopyprocess.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hbcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(6,'hbcchargeTable','hbcchargeno','hbcchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbcchargeno" name="hbcchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:true" readonly
                                                               value="${tQhWfhbfilecopyprocess.hbcchargeno }"/>
                                                        <font color="red">*</font>/
                                                        <input id="hbcchargename" name="hbcchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:true" readonly
                                                               value="${tQhWfhbfilecopyprocess.hbcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="bgrchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">文件保管人作業</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(15,'bgrchargeTable','bgrchargeno','bgrchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="bgrchargeno" name="bgrchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:true" readonly
                                                               value="${tQhWfhbfilecopyprocess.bgrchargeno }"/>
                                                        <font color="red">*</font>/
                                                        <input id="bgrchargename" name="bgrchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:true" readonly
                                                               value="${tQhWfhbfilecopyprocess.bgrchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" style="text-align:left;">
                                <table class="flowList" style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton" data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
</div>

</body>
</html>