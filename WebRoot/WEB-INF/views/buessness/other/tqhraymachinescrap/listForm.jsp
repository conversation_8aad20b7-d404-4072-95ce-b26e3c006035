<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>新建射線裝置報停、報廢申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/other/tqhraymachinescrap.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/tqhraymachinescrap/${action}" method="post">
    <div class="commonW">
        <div class="headTitle">新建射線裝置報停、報廢申請單</div>
        <input id="ids" name="tQhRayMachineScrap.id" type="hidden" value="${tQhRayMachineScrap.id }"/>
        <input id="serialno" name="tQhRayMachineScrap.serialno" type="hidden"
               value="${tQhRayMachineScrap.serialno }"/>
        <input id="createtime" name="tQhRayMachineScrap.createtime" type="hidden"
               value="${tQhRayMachineScrap.createtime }"/>
        <input id="makerno" name="tQhRayMachineScrap.makerno" type="hidden" value="${tQhRayMachineScrap.makerno }"/>
        <input id="makername" name="tQhRayMachineScrap.makername" type="hidden" value="${tQhRayMachineScrap.makername }"/>
        <input id="makerdeptno" name="tQhRayMachineScrap.makerdeptno" type="hidden" value="${tQhRayMachineScrap.makerdeptno }"/>
        <input id="makerfactoryid" name="tQhRayMachineScrap.makerfactoryid" type="hidden" value="${tQhRayMachineScrap.makerfactoryid }"/>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
            <c:choose>
                <c:when test="${tQhRayMachineScrap.serialno==null}">
                    提交成功后自動編碼
                </c:when>
                <c:otherwise>
                    ${tQhRayMachineScrap.serialno}
                </c:otherwise>
            </c:choose>
        </span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
        <c:choose>
            <c:when test="${tQhRayMachineScrap.createtime==null}">
                YYYY/MM/DD
            </c:when>
            <c:otherwise>
                <fmt:formatDate value="${tQhRayMachineScrap.createtime}" pattern="yyyy-MM-dd HH:mm:ss"/>
            </c:otherwise>
        </c:choose></span>
        </div>
        <c:if test="${empty tQhRayMachineScrap.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty tQhRayMachineScrap.makerno}">
            <div class="position_R margin_R">填單人：${tQhRayMachineScrap.makerno}/${tQhRayMachineScrap.makername}</div>
        </c:if>
        <br>
        <div class="clear"></div>
        <table class="formList" id="buildprojecttable">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealno" name="tQhRayMachineScrap.dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${tQhRayMachineScrap.dealno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="5%">承辦人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="tQhRayMachineScrap.dealname" class="easyui-validatebox inputCss"
                                       data-options="width: 80"
                                       value="${tQhRayMachineScrap.dealname }" readonly/>
                            </td>
                            <td width="6%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="tQhRayMachineScrap.dealdeptno" class="easyui-validatebox inputCss"
                                       data-options="width: 90"
                                       readonly value="${tQhRayMachineScrap.dealdeptno }"/>
                            </td>
                            <td width="6%">提報日期&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealtime" name="tQhRayMachineScrap.dealtime" class="easyui-my97" datefmt="yyyy-MM-dd"
                                       minDate="%y-%M-%d"
                                       data-options="width: 100,required:true"
                                       value="<fmt:formatDate value="${tQhRayMachineScrap.dealtime}"/>"/>
                            </td>
                            <td width="6%">廠區&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealfactoryid" name="tQhRayMachineScrap.dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${tQhRayMachineScrap.dealfactoryid }"
                                       data-options="width: 80,required:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="3" class="td_style1">
                                <input id="dealdeptname" name="tQhRayMachineScrap.dealdeptname" class="easyui-validatebox"
                                       data-options="width: 380"
                                       value="${tQhRayMachineScrap.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="tQhRayMachineScrap.dealemail" class="easyui-validatebox"
                                       value="${tQhRayMachineScrap.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email'" onblur="valdEmail(this)"/>
                            </td>
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealtel" name="tQhRayMachineScrap.dealtel" class="easyui-validatebox" style="width:90px;"
                                       value="${tQhRayMachineScrap.dealtel }" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">報停/報廢詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td>射線裝置使用單位&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="projecname" name="tQhRayMachineScrap.rayMachineUseDeptname" class="easyui-validatebox"
                                       style="width:350px;"
                                       data-options="required:true" value="${tQhRayMachineScrap.rayMachineUseDeptname }"/>

                            </td>
                            <td colspan="2">射線裝置工作場所&nbsp;<font color="red">*</font></td>
                            <td colspan="4" class="td_style1">
                                <input id="buildsize" name="tQhRayMachineScrap.rayMachineUsePlace" class="easyui-validatebox"
                                       data-options="width: 350,required:true" value="${tQhRayMachineScrap.rayMachineUsePlace }"/>
                            </td>
                        </tr>


                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x:auto;width: 100%">
                                    <input id="rayMachineScrapItemTableIndex" type="hidden"
                                           value="<c:choose><c:when test="${itemEntity!=null&&itemEntity.size()>0}">
                                ${itemEntity.size() + 1}
                            </c:when><c:otherwise>2</c:otherwise></c:choose>">
                                    </input>
                                    <table id="rayMachineScrapItemTable" width="120%">
                                        <thead>
                                        <tr>
                                            <th nowrap="nowrap">序號</th>
                                            <th>裝置名稱<font color="red">*</font></th>
                                            <th>生產廠家<font color="red">*</font></th>
                                            <th>型號<font color="red">*</font></th>
                                            <th>出廠日期<font color="red">*</font></th>
                                            <th>用途<font color="red">*</font></th>
                                            <th>最大管電壓<font color="red">*</font></th>
                                            <th>最大管電流<font color="red">*</font></th>
                                            <th>射線種類<font color="red">*</font></th>
                                            <th>辦理項目<font color="red">*</font></th>
                                            <th>報停/報廢日期<font color="red">*</font></th>
                                            <th>報停報廢原因<font color="red">*</font></th>
                                            <th>報停報廢去向<font color="red">*</font></th>
                                            <th nowrap="nowrap">操作</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <c:choose>
                                            <c:when test="${itemEntity!=null&&itemEntity.size()>0}">
                                                <c:forEach items="${itemEntity}" varStatus="i" var="item">
                                                    <tr align="center" id="rayMachineScrapItemTr${i.index+1}">
                                                        <td>${i.index+1}</td>
                                                        <td><input id="machineName${i.index+1}"
                                                                   name="tQhRayMachineScrapItems[${i.index}].machineName"
                                                                   value="${item.machineName }" class="easyui-validatebox"
                                                                   data-options="required:true" style="width:80px;" value=""/></td>
                                                        <td><input id="manufacturer${i.index+1}"
                                                                   name="tQhRayMachineScrapItems[${i.index}].manufacturer"
                                                                   value="${item.manufacturer }" class="easyui-validatebox"
                                                                   data-options="required:true" style="width:80px;" value=""/></td>
                                                        <td><input id="machineModel${i.index+1}"
                                                                   name="tQhRayMachineScrapItems[${i.index}].machineModel"
                                                                   value="${item.machineModel }" class="easyui-validatebox"
                                                                   data-options="required:true" style="width:80px;" value=""/></td>
                                                        <td><input id="dateOfProduction${i.index+1}"
                                                                   name="tQhRayMachineScrapItems[${i.index}].dateOfProduction"
                                                                   class="easyui-my97" datefmt="yyyy-MM-dd"
                                                                   value="<fmt:formatDate value="${item.dateOfProduction}"/>"
                                                                   data-options="width: 100,required:true"/></td>
                                                        <td><input id="usesFor${i.index+1}"
                                                                   name="tQhRayMachineScrapItems[${i.index}].usesFor"
                                                                   value="${item.usesFor }" class="easyui-validatebox"
                                                                   data-options="required:true" style="width:80px;" value=""/></td>
                                                        <td><input id="maxVoltage${i.index+1}"
                                                                   name="tQhRayMachineScrapItems[${i.index}].maxVoltage"
                                                                   value="${item.maxVoltage }" class="easyui-validatebox"
                                                                   data-options="required:true" style="width:80px;" value=""/></td>
                                                        <td><input id="minCurrent${i.index+1}"
                                                                   name="tQhRayMachineScrapItems[${i.index}].minCurrent"
                                                                   value="${item.minCurrent }" class="easyui-validatebox"
                                                                   data-options="required:true" style="width:80px;" value=""/></td>
                                                        <td><input id="rayType${i.index+1}"
                                                                   name="tQhRayMachineScrapItems[${i.index}].rayType"
                                                                   value="${item.rayType }" class="easyui-validatebox"
                                                                   data-options="required:true" style="width:80px;" value=""/></td>
                                                        <td><input id="rayOption${i.index+1}"
                                                                   name="tQhRayMachineScrapItems[${i.index}].rayOption"
                                                                   value="${item.rayOption }"
                                                                   class="easyui-combobox rayOptionitemclass"
                                                                   data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadRayOptionType(${i.index+1});}"
                                                                   style="width:80px;" value=""/></td>
                                                        <td><input id="scrapDate${i.index+1}"
                                                                   name="tQhRayMachineScrapItems[${i.index}].scrapDate"
                                                                   value="<fmt:formatDate value="${item.scrapDate}"/>"
                                                                   class="easyui-my97" datefmt="yyyy-MM-dd" minDate="%y-%M-%d"
                                                                   data-options="width: 100,required:true"/></td>
                                                        <td><input id="reason${i.index+1}"
                                                                   name="tQhRayMachineScrapItems[${i.index}].reason"
                                                                   value="${item.reason }" class="easyui-validatebox"
                                                                   data-options="required:true" style="width:100px;" value=""/></td>
                                                        <td><input id="rayWhere${i.index+1}"
                                                                   name="tQhRayMachineScrapItems[${i.index}].rayWhere"
                                                                   value="${item.rayWhere }" class="easyui-validatebox"
                                                                   data-options="required:true" style="width:100px;" value=""/></td>
                                                        <td><input type="image" src="${ctx}/static/images/deleteRow.png"
                                                                   onclick="deltr(${i.index+1});return false;"/></td>
                                                    </tr>
                                                </c:forEach>
                                            </c:when>
                                            <c:otherwise>
                                                <tr align="center" id="rayMachineScrapItemTr1">
                                                    <td>1</td>
                                                    <td><input id="machineName1" name="tQhRayMachineScrapItems[0].machineName"
                                                               value="${tQhRayMachineScrapItems[0].machineName }"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width:80px;" value=""/></td>
                                                    <td><input id="manufacturer1" name="tQhRayMachineScrapItems[0].manufacturer"
                                                               value="${tQhRayMachineScrapItems[0].manufacturer }"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width:80px;" value=""/></td>
                                                    <td><input id="machineModel1" name="tQhRayMachineScrapItems[0].machineModel"
                                                               value="${tQhRayMachineScrapItems[0].machineModel }"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width:80px;" value=""/></td>
                                                    <td><input id="dateOfProduction1"
                                                               name="tQhRayMachineScrapItems[0].dateOfProduction"
                                                               class="easyui-my97" datefmt="yyyy-MM-dd"
                                                               value="<fmt:formatDate value="${tQhRayMachineScrapItems[0].dateOfProduction}"/>"
                                                               data-options="width: 100,required:true"/></td>
                                                    <td><input id="usesFor1" name="tQhRayMachineScrapItems[0].usesFor"
                                                               value="${tQhRayMachineScrapItems[0].usesFor }"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width:80px;" value=""/></td>
                                                    <td><input id="maxVoltage1" name="tQhRayMachineScrapItems[0].maxVoltage"
                                                               value="${tQhRayMachineScrapItems[0].maxVoltage }"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width:80px;" value=""/></td>
                                                    <td><input id="minCurrent1" name="tQhRayMachineScrapItems[0].minCurrent"
                                                               value="${tQhRayMachineScrapItems[0].minCurrent }"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width:80px;" value=""/></td>
                                                    <td><input id="rayType1" name="tQhRayMachineScrapItems[0].rayType"
                                                               value="${tQhRayMachineScrapItems[0].rayType }"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width:80px;" value=""/></td>
                                                    <td>
                                                        <input id="rayOption1" name="tQhRayMachineScrapItems[0].rayOption"
                                                               value="${tQhRayMachineScrapItems[0].rayOption }"
                                                               class="easyui-combobox rayOptionitemclass"
                                                               data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadRayOptionType(1);}"
                                                               style="width:80px;" value=""/>
                                                    </td>
                                                    <td><input id="scrapDate1" name="tQhRayMachineScrapItems[0].scrapDate"
                                                               value="<fmt:formatDate value="${tQhRayMachineScrapItems[0].scrapDate}"/>"
                                                               class="easyui-my97" datefmt="yyyy-MM-dd" minDate="%y-%M-%d"
                                                               data-options="width: 100,required:true"/></td>
                                                    <td><input id="reason1" name="tQhRayMachineScrapItems[0].reason"
                                                               value="${tQhRayMachineScrapItems[0].reason }"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width:100px;" value=""/></td>
                                                    <td><input id="rayWhere1" name="tQhRayMachineScrapItems[0].rayWhere"
                                                               value="${tQhRayMachineScrapItems[0].rayWhere }"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width:100px;" value=""/></td>
                                                    <td><input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="deltr(1);return false;"/></td>
                                                </tr>
                                            </c:otherwise>
                                        </c:choose>

                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="left">
                            <td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
                                <input type="button" id="addScrapItem" style="width:100px;float:left;" value="添加一筆"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">附件</td>
                            <td colspan="9" class="td_style1">
						        <span class="sl-custom-file">
						            <input type="button" value="点击上传文件" class="btn-file"/>
						            <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file">
						        </span>
                                <input type="hidden" id="attachids" name="tQhRayMachineScrap.attachids" value="${tQhRayMachineScrap.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">備註</td>
                            <td colspan="8" class="td_style1">
                                射線裝置報停、報廢申請時需簽核并上傳 <a href="${ctx}/tqhraymachinescrap/downLoad/commitmentTpl" plain="true" id="btnCommitmentTpl">射線裝置報停注銷安全責任承諾書</a>

                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)" onclick="showWfImag('${processId}','系統上線申請流程圖','');">點擊查看簽核流程圖</a>
                                <div id="dlg"></div>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="tQhRayMachineScrap.kchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['kchargeno']}" readonly
                                                               value="${tQhRayMachineScrap.kchargeno }"/><c:if test="${requiredMap['kchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="kchargename" name="tQhRayMachineScrap.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${tQhRayMachineScrap.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealfactoryid').combobox('getValue'))"></div>

                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="bchargeno" name="tQhRayMachineScrap.bchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${tQhRayMachineScrap.bchargeno }"/><c:if test="${requiredMap['bchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="bchargename" name="tQhRayMachineScrap.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${tQhRayMachineScrap.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').combobox('getValue'))"></div>

                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="cchargeno"
                                                               name="tQhRayMachineScrap.cchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['cchargeno']}" readonly
                                                               value="${tQhRayMachineScrap.cchargeno }"/><c:if test="${requiredMap['cchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="cchargename"
                                                                name="tQhRayMachineScrap.cchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['cchargeno']}"
                                                                value="${tQhRayMachineScrap.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"  id="hchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽
                                                                    <a href="javascript:addHq('hcharge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hchargeno" onblur="getUserNameByEmpno(this,'hcharge');" name="tQhRayMachineScrap.hchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hchargeno']}" value="${tQhRayMachineScrap.hchargeno }"/>
                                                        <c:if test="${requiredMap['hchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hchargename" name="tQhRayMachineScrap.hchargename" readonly
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hchargeno']}"  value="${tQhRayMachineScrap.hchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zchargeno"
                                                               name="tQhRayMachineScrap.zchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zchargeno']}" readonly
                                                               value="${tQhRayMachineScrap.zchargeno }"/><c:if test="${requiredMap['zchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="zchargename"
                                                                name="tQhRayMachineScrap.zchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zchargeno']}" readonly
                                                                value="${tQhRayMachineScrap.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealfactoryid').combobox('getValue'))"></div>

                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zcchargeno"
                                                               name="tQhRayMachineScrap.zcchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zcchargeno']}" readonly
                                                               value="${tQhRayMachineScrap.zcchargeno }"/><c:if test="${requiredMap['zcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="zcchargename"
                                                                name="tQhRayMachineScrap.zcchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zcchargeno']}" readonly
                                                                value="${tQhRayMachineScrap.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處對應窗口</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(3,'hbchargeno','hbchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbchargeno"
                                                               name="tQhRayMachineScrap.hbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbchargeno']}" readonly
                                                               value="${tQhRayMachineScrap.hbchargeno }"/><c:if test="${requiredMap['hbchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hbchargename" name="tQhRayMachineScrap.hbchargename" readonly
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbchargeno']}" value="${tQhRayMachineScrap.hbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hbkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(4,'hbkchargeTable','hbkchargeno','hbkchargename',$('#dealfactoryid').combobox('getValue'),'tQhRayMachineScrap')"></div>

                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbkchargeno"
                                                               name="tQhRayMachineScrap.hbkchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbkchargeno']}" readonly
                                                               value="${tQhRayMachineScrap.hbkchargeno }"/><c:if test="${requiredMap['hbkchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hbkchargename" name="tQhRayMachineScrap.hbkchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbkchargeno']}" readonly
                                                                value="${tQhRayMachineScrap.hbkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hbbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(5,'hbbchargeTable','hbbchargeno','hbbchargename',$('#dealfactoryid').combobox('getValue'),'tQhRayMachineScrap')"></div>

                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbbchargeno"
                                                               name="tQhRayMachineScrap.hbbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbbchargeno']}" readonly
                                                               value="${tQhRayMachineScrap.hbbchargeno }"/><c:if test="${requiredMap['hbbchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="hbbchargename"name="tQhRayMachineScrap.hbbchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbbchargeno']}" readonly
                                                               value="${tQhRayMachineScrap.hbbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hbcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(6,'hbcchargeTable','hbcchargeno','hbcchargename',$('#dealfactoryid').combobox('getValue'),'tQhRayMachineScrap')"></div>

                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbcchargeno"
                                                               name="tQhRayMachineScrap.hbcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbcchargeno']}" readonly
                                                               value="${tQhRayMachineScrap.hbcchargeno }"/><c:if test="${requiredMap['hbcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="hbcchargename" name="tQhRayMachineScrap.hbcchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbcchargeno']}" readonly
                                                               value="${tQhRayMachineScrap.hbcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton" data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                                <%--<a href="#" id="btnadd" class="easyui-linkbutton" iconCls="icon-add" plain="true" code="add">提交</a>--%>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>

</form>
<div id="optionWin" class="easyui-window" title="報停/報廢信息批量導入" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%">
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="batchFile" type="file" style="width: 300px"
                           accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>

                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <span ID="labelListAddResult"></span><a href="${ctx}/tqhraymachinescrap/downLoad/errorExcel"
                                                            id="downloadError" plain="true">查看錯誤信息</a>
                </td>
            </tr>
        </table>
    </form>
</div>
<script type="text/javascript">
    if ("${tQhRayMachineScrap.hchargeno}" != "") {
        var nostr = "${tQhRayMachineScrap.hchargeno}";
        var namestr = "${tQhRayMachineScrap.hchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hchargeTable tr:eq(" + (i + 2) + ")").find("#hchargeno").val(notr[i]);
            $("#hchargeTable tr:eq(" + (i + 2) + ")").find("#hchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hchargeno' name='tQhRayMachineScrap.hchargeno' style='width: 80px' value='' onblur='getUserNameByEmpno(this,\"hcharge\");'/>/<input id='hchargename' name='tQhRayMachineScrap.hchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
</script>
</body>
</html>