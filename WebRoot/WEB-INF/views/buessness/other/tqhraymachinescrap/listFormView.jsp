<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>射線裝置報停、報廢申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/other/tqhraymachinescrap.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/tqhraymachinescrap/${action}" method="post">
    <div class="commonW" >
        <div class="headTitle">新建射線裝置報停、報廢申請單</div>
            <div class="position_L">
                <input id="ids" name="tQhRayMachineScrap.ids" type="hidden" value="${tQhRayMachineScrap.id }"/>
                任務編碼：${tQhRayMachineScrap.serialno}
                <input id="serialno" name="tQhRayMachineScrap.serialno" type="hidden" value="${tQhRayMachineScrap.serialno }"/>
            </div>
            <div class="position_L1 margin_L">
                填單時間：<fmt:formatDate value="${tQhRayMachineScrap.createtime}" pattern="yyyy-MM-dd HH:mm:ss"/>
            </div>
        <div class="position_R margin_R"> 填單人：${user.loginName}/${user.name}</div><br>
        <div class="clear"></div>
        <table class="formList" id="buildprojecttable">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%" nowrap="nowrap">承辦人工號</td>
                            <td width="6%" class="td_style1">
                                <input id="dealno" name="tQhRayMachineScrap.dealno" class="easyui-validatebox inputCss" readonly="true" data-options="width: 80,required:true"
                                       value="${tQhRayMachineScrap.dealno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="5%" nowrap="nowrap">承辦人</td>
                            <td width="4%" class="td_style1">
                                <input id="dealname" name="tQhRayMachineScrap.dealname" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${tQhRayMachineScrap.dealname }" readonly/>
                            </td>
                            <td width="6%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="tQhRayMachineScrap.dealdeptno" class="easyui-validatebox inputCss inputCss" readonly="true" data-options="width: 90"
                                       readonly value="${tQhRayMachineScrap.dealdeptno }" />
                            </td>
                            <td width="6%">提報日期</td>
                            <td width="6%" class="td_style1">
                                <input id="dealtime" name="tQhRayMachineScrap.dealtime" class="easyui-textbox inputCss" readonly="true" datefmt="yyyy-MM-dd"  minDate="%y-%M-%d"
                                       data-options="width: 100,required:true" value="<fmt:formatDate value="${tQhRayMachineScrap.dealtime}"/>" />
                            </td>
                            <td width="6%">廠區</td>
                            <td width="6%" class="td_style1">
                                <input class="inputCss inputCss" readonly="true" data-options="width: 90"
                                       readonly value="${factoryName}" />
                                <%--<input id="dealfactoryid" name="tQhRayMachineScrap.dealfactoryid" class="easyui-textbox inputCss" readonly="true"
                                       panelHeight="auto" disabled="none"
                                       data-options="width: 80,required:true"/>--%>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="3" class="td_style1">
                                <input id="dealdeptname" name="tQhRayMachineScrap.dealdeptname" class="easyui-textbox inputCss" readonly="true" data-options="width: 380"
                                       value="${tQhRayMachineScrap.dealdeptname }" />
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="tQhRayMachineScrap.dealemail" class="easyui-textbox inputCss" readonly="true" value="${tQhRayMachineScrap.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email'" onblur="valdEmail(this)"/>
                            </td>
                            <td>聯繫分機</td>
                            <td class="td_style1">
                                <input id="dealtel" name="tQhRayMachineScrap.dealtel" class="easyui-textbox inputCss" readonly="true" style="width:90px;"
                                       value="${tQhRayMachineScrap.dealtel }" data-options="required:true,prompt:'579+66666'"  onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">報停/報廢詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">射線裝置使用單位</td>
                            <td colspan="2" class="td_style1">
                                <input id="projecname" name="tQhRayMachineScrap.rayMachineUseDeptname" class="easyui-textbox inputCss" readonly="true" style="width:330px;"
                                       data-options="required:true" value="${tQhRayMachineScrap.rayMachineUseDeptname }" />

                            </td>
                            <td colspan="2">射線裝置工作場所</td>
                            <td colspan="4" class="td_style1">
                                <input id="buildsize" name="tQhRayMachineScrap.rayMachineUsePlace" class="easyui-textbox inputCss" readonly="true" data-options="width: 350,required:true" value="${tQhRayMachineScrap.rayMachineUsePlace }" />
                            </td>
                        </tr>


                        <tr align="center">
                            <td colspan="10"  width="100%">
                                <div style="overflow-x:auto;width: 100%">
                                    <table id="rayMachineScrapItemTable"  width="100%">
                                        <thead>
                                        <tr>
                                            <th nowrap="nowrap">序號</th>
                                            <th>裝置名稱</th>
                                            <th>生產廠家</th>
                                            <th>型號</th>
                                            <th>出廠日期</th>
                                            <th>用途</th>
                                            <th>最大管電壓</th>
                                            <th>最大管電流</th>
                                            <th>射線種類</th>
                                            <th>辦理項目</th>
                                            <th>報停/報廢日期</th>
                                            <th>報停報廢原因</th>
                                            <th>報停報廢去向</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <c:forEach items="${itemEntity}" varStatus="i" var="item">
                                            <tr align="center">
                                                <td>${i.index + 1}</td>
                                                <td>${item.machineName}</td>
                                                <td>${item.manufacturer}</td>
                                                <td>${item.machineModel}</td>
                                                <td><fmt:formatDate value="${item.dateOfProduction}"/></td>
                                                <td>${item.usesFor}</td>
                                                <td>${item.maxVoltage}</td>
                                                <td>${item.minCurrent}</td>
                                                <td>${item.rayType}</td>
                                                <td>${item.rayOptionName}</td>
                                                <td><fmt:formatDate value="${item.scrapDate}"/></td>
                                                <td>${item.reason}</td>
                                                <td>${item.rayWhere}</td>
                                            </tr>
                                        </c:forEach>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2" >附件</td>
                            <td colspan="8" class="td_style1">
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">批註</td>
                            <td colspan="8">

						<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:800px;height:60px;"
                                  rows="4" cols="4"
                                  data-options="required:true,prompt:'例：'"></textarea>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                        <c:if test="${tQhRayMachineScrap.reattachids!=null}">
                            <tr align="center">
                                <td colspan="2" >附件（補充說明）</td>
                                <td colspan="8" class="td_style1">
                                    <div id="dowloadUrl1">
                                        <c:forEach items="${reFile}" varStatus="i" var="item">
                                            <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                                <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                            </div>
                                        </c:forEach>
                                    </div>
                                </td>
                            </tr>
                        </c:if>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)" onclick="showWfImag('${processId}','系統上線申請流程圖','');">點擊查看簽核流程圖</a>
                                <div id="dlg"></div>

                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhRayMachineScrap.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton" data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                   style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/><input type="hidden" id="chargeNo" name="chargeNo" value=""/><input type="hidden" id="chargeName" name="chargeName" value=""/><input type="hidden" id="factoryId" name="factoryId" value=""/><input type="hidden" id="dutyId" name="dutyId" value=""/><div id="win"></div>


</form>

</body>
</html>