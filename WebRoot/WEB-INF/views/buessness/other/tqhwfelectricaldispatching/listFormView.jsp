<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>機電派工申請單</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
	type="text/javascript"></script>
<script src='${ctx}/static/js/other/tqhwfelectricaldispatching.js?random=<%= Math.random()%>'></script>
</head>
<body>
	<form id="mainform" action="${ctx}/tqhwfelectricaldispatching/${action}" method="post">
		<input id="ids" name="ids" type="hidden" value="${tQhWfelectricaldispatchingEntity.id }" />
		<input id="serialno" name="serialno" type="hidden" value="${tQhWfelectricaldispatchingEntity.serialno }" />
		<input id="createtime" name="createtime" type="hidden" value="${tQhWfelectricaldispatchingEntity.createtime }" />
		<div class="commonW">
		<div class="headTitle">機電派工申請單</div>
		<div class="position_L">
				任務編碼：<span style="color:#999;"> <c:choose>
						<c:when test="${tQhWfelectricaldispatchingEntity.serialno==null}">
                                                              提交成功后自動編碼
                   </c:when>
						<c:otherwise>
                   ${tQhWfelectricaldispatchingEntity.serialno}
                   </c:otherwise>
					</c:choose>
				</span>
		</div>
		<div class="position_L1 margin_L">
				填單時間：<span style="color:#999;"> <c:choose>
						<c:when
							test="${tQhWfelectricaldispatchingEntity.createtime==null}">
                  YYYY/MM/DD
                  </c:when>
						<c:otherwise>
							<input style="border:0px" style="width: 120px" readonly="true"
								value="<fmt:formatDate value='${tQhWfelectricaldispatchingEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>" />
						</c:otherwise>
					</c:choose>
				</span>
		</div>
		<div class="position_R margin_R">填單人：${tQhWfelectricaldispatchingEntity.dealno}/${tQhWfelectricaldispatchingEntity.dealname}</div>
		<div class="clear"></div>
			<table class="formList">
				<tr>
					<td>
                        <table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">申請人詳細信息</td>
							</tr>
							<tr align="center">
								<td>申請人工號</td>
								<td><input id="dealno" name="dealno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.dealno }" /></td>
								<td>申請人名稱</td>
								<td><input id="dealname" name="dealname" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.dealname }" /></td>
								<td>單位代碼</td>
								<td colspan = "2"><input id="dealdeptno" name="dealdeptno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.dealdeptno }" /></td>
								<%-- <td>提報日期</td>
								<td><input id="dealtime" name="dealtime" disabled="disabled"
									class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
									data-options="width: 150,required:true"
									value="<fmt:formatDate value="${tQhWfelectricaldispatchingEntity.dealtime}"/>" />
								</td> --%>
								<td>廠區</td>
								<td colspan = "2"><input id="dealfactoryid" name="dealfactoryid"  disabled="disabled"
									class="easyui-combobox" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.dealfactoryid }" /></td>
							</tr>
							<tr align="center">
								<td>申請單位</td>
								<td colspan="3"><input id="dealdeptname" readonly style="width:400px"
									name="dealdeptname" class="easyui-validatebox inputCss"
									data-options="width: 400,required:true"
									value="${tQhWfelectricaldispatchingEntity.dealdeptname }" /></td>
								<td>聯繫郵箱</td>
								<td colspan="3"><input id="dealemail" name="dealemail" readonly style="width:250px"
									class="easyui-validatebox inputCss" data-options="width: 250,required:true"
									value="${tQhWfelectricaldispatchingEntity.dealemail }" /></td>
								<td>聯繫分機</td>
								<td><input id="dealtel" name="dealtel" readonly
									class="easyui-validatebox inputCss" data-options="width: 150,required:true,prompt:'565+66666'"
									value="${tQhWfelectricaldispatchingEntity.dealtel }" /></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">派工申請信息</td>
							</tr>
							<tr align="center">
								<td>派工項目</td>
								<td colspan="2"><input id="taskproject" name="taskproject" readonly
									class="easyui-validatebox inputCss" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.taskproject }" /></td>
								<td>派工地點</td>
								<td colspan="2"><input id="taskplace" name="taskplace" readonly
									class="easyui-validatebox inputCss" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.taskplace }" /></td>
								<td colspan="2">期望完工日期</td>
								<td colspan="2"><input id="taskexceptdealtime"
									name="taskexceptdealtime" class="easyui-my97" disabled="disabled"
									datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 150,required:true"
									value="<fmt:formatDate value="${tQhWfelectricaldispatchingEntity.taskexceptdealtime}"/>" />
								</td>
							</tr>
							<tr align="center">
								<td>派工需求類別</td>
								<td colspan="2">
								<div class="categoryDiv"></div>
								<input id="taskcategory" name="taskcategory" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${tQhWfelectricaldispatchingEntity.taskcategory }" />
								<input id="disOrEnabled" type="hidden" value="disabled"/>
								</td>
								<td>緊急程度</td>
								<td colspan="2">
								<div class="emergencyDiv"></div>
								<input id="taskemergency" name="taskemergency" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${tQhWfelectricaldispatchingEntity.taskemergency }" /></td>
								<td colspan="2">制程</td>
								<td colspan="2"><input id="zhicheng" name="zhicheng" readonly
									class="easyui-validatebox inputCss" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.zhicheng }" /></td>
							</tr>
							<tr align="center">
								<td>派工需求說明</td>
								<td colspan="9"><textarea id="taskmemo" name="taskmemo" data-options="required:true" readonly
									class="easyui-validatebox" style="width:800px;height:90px;" rows="5" cols="6"
									>${tQhWfelectricaldispatchingEntity.taskmemo }</textarea></td>
							</tr>
							<tr align="center">
								<td>附件</td>
								<td colspan="9" style="text-align: left">
									<input type="hidden" id="attachids" name="attachids" value="${tQhWfelectricaldispatchingEntity.attachids }"/>
									<div id="dowloadUrl">
										<c:forEach items="${file}" varStatus="i" var="item">
											<div id="${item.id}"
												 style="line-height:30px;margin-left:5px;" class="float_L">
												<div class="float_L">
													<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
												</div>
											</div>
										</c:forEach>
									</div>
								</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">區域負責人信息</td>
							</tr>
							<tr align="center">
								<td>接單日期</td>
								<td colspan="2"><input id="qchargeaccepttime" disabled="disabled"
									name="qchargeaccepttime" class="easyui-my97"
									datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 150,required:true"
									value="<fmt:formatDate value="${tQhWfelectricaldispatchingEntity.qchargeaccepttime}"/>" />
								</td>
								<td colspan="2">區域負責人工號</td>
								<td><input id="qchargeno" name="qchargeno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.qchargeno }" /></td>
								<td>區域負責人</td>
								<td colspan="3"><input id="qchargename" name="qchargename" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.qchargename }" /></td>
							</tr>
							<tr align="center">
								<td>單位代碼</td>
								<td colspan="2"><input id="qchargedptno" readonly
									name="qchargedptno" class="easyui-validatebox inputCss"
									data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.qchargedptno }" /></td>
								<td colspan="2">聯繫分機</td>
								<td><input id="qchargetel" name="qchargetel"  readonly
									class="easyui-validatebox inputCss" data-options="width: 150,required:true,prompt:'565+66666'"
									value="${tQhWfelectricaldispatchingEntity.qchargetel }" /></td>
								<td>聯繫郵箱</td>
								<td colspan="3"><input id="qchargemail" name="qchargemail" readonly style="width:250px"
									class="easyui-validatebox inputCss" data-options="width: 250,required:true"
									value="${tQhWfelectricaldispatchingEntity.qchargemail }" /></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">表單作業信息</td>
							</tr>
							<%-- <tr align="center">
								<td colspan="2">施工人員工號<font color="red">*</font></td>
								<td colspan="2"><input id="constrno" name="constrno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.constrno }" /></td>
								<td colspan="2">施工人員姓名<font color="red">*</font></td>
								<td><input id="constrname" name="constrname" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.constrname }" /></td>
								<td>單位代碼<font color="red">*</font></td>
								<td colspan="2"><input id="constrdptno" name="constrdptno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.constrdptno }" /></td>
							</tr> --%>
							<tr align="center">
                                <td>序號</td>
                                <td colspan="3">施工人員工號</td>
                                <td colspan="3">施工人員姓名</td>
                                <td colspan="3">施工人員單位代碼</td>
                            </tr>
                            <c:if test="${itemEntity!=null&&itemEntity.size()>0}">
                                 <c:forEach items="${itemEntity}" var="item" varStatus="status">
                                   <tr align="center" id="constroItem${status.index}">
                                    <td>${status.index+1}</td>
                                    <td colspan="3"><input id="constrno${status.index}"
                                              name="tQhWfElectricalItemEntity[${status.index}].constrno" readonly
                                              class="easyui-validatebox inputCss" data-options="required:true" 
                                              style="width:300px;" value="${item.constrno}"/>
                                    </td>
                                    <td colspan="3"><input id="constrname${status.index}"
                                              name="tQhWfElectricalItemEntity[${status.index}].constrname"
                                              class="easyui-validatebox inputCss" data-options="required:true" readonly
                                              style="width:300px;" value="${item.constrname}"/>
                                    </td>
                                    <td colspan="3"><input id="constrdptno${status.index}"
                                              name="tQhWfElectricalItemEntity[${status.index}].constrdptno"
                                              class="easyui-validatebox inputCss" data-options="required:true" readonly
                                              style="width:300px;" value="${item.constrdptno}"/>
                                    </td>
                                   </tr>
                              </c:forEach>
                              </c:if>
                              <c:if test="${itemEntity==null||itemEntity.size()==0}">
                              <tr align="center">	
                                <td>1</td>	
								<td colspan="3"><input id="constrno" name="constrno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.constrno }" /></td>	
								<td colspan="3"><input id="constrname" name="constrname" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.constrname }" /></td>
								<td colspan="3"><input id="constrdptno" name="constrdptno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.constrdptno }" /></td>
							</tr>
                              </c:if>
							<tr align="center">
								<td colspan="2">開工日期</td>
								<td colspan="2"><input id="constrstaettime"
									name="constrstaettime" class="easyui-my97" disabled="disabled"
									datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 150,required:true"
									value="<fmt:formatDate pattern="yyyy-MM-dd HH:mm" value="${tQhWfelectricaldispatchingEntity.constrstaettime}"/>" />
								</td>
								<td colspan="2">施工進度</td>
								<td><input id="constrrate" name="constrrate" readonly style="width:30px"
									class="easyui-validatebox inputCss" data-options="width: 30,required:true"
									value="${tQhWfelectricaldispatchingEntity.constrrate }" />%</td>
								<td>完工日期</td>
								<td colspan="2"><input id="constrcomplettime"
									name="constrcomplettime" class="easyui-my97" disabled="disabled"
									datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 150,required:true"
									value="<fmt:formatDate pattern="yyyy-MM-dd HH:mm" value="${tQhWfelectricaldispatchingEntity.constrcomplettime}"/>" />
								</td>
							</tr>
							<tr align="center">
								<td colspan="2">施工狀況說明</td>
								<td colspan="8">
								<textarea id="constrmemo" name="constrmemo" readonly
									class="easyui-validatebox" style="width:800px;height:90px;" rows="5" cols="6"
									>${tQhWfelectricaldispatchingEntity.constrmemo }</textarea></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">服務評價基本信息</td>
							</tr>
							<tr align="center">
								<td>評價人工號</td>
								<td><input id="judgeno" name="judgeno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.judgeno }" /></td>
								<td>評價人</td>
								<td><input id="judgename" name="judgename" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.judgename }" /></td>
								<td>單位代碼</td>
								<td><input id="judgedeptno" name="judgedeptno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.judgedeptno }" /></td>
								<td>聯繫郵箱</td> 
								<td colspan="3"><input id="judgemail" name="judgemail" readonly style="width:250px"
									class="easyui-validatebox inputCss" data-options="width: 250,required:true"
									value="${tQhWfelectricaldispatchingEntity.judgemail }" /></td>
							</tr>
							<tr align="center">
								<td>聯繫分機</td>
								<td><input id="judgetel" name="judgetel" readonly
									class="easyui-validatebox inputCss" data-options="width: 150,required:true,prompt:'565+66666'"
									value="${tQhWfelectricaldispatchingEntity.judgetel }" /></td>
								<td>評價日期</td>
								<td><input id="judgetime" name="judgetime" disabled="disabled"
									class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
									data-options="width: 150,required:true"
									value="<fmt:formatDate value="${tQhWfelectricaldispatchingEntity.judgetime}"/>" />
								</td>
								<td>評價</td>
								<td colspan="5">
								<div class="judgeDiv"></div>
								<input id="judgeresult" name="judgeresult" type="hidden"
									colspan="3" class="easyui-validatebox" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.judgeresult }" />
								<input id="disOrEnabled2" type="hidden" value="disabled"/>
								</td>
							</tr>
							<tr align="center">
								<td>評價說明</td>
								<td colspan="9">
								<textarea id="judgememo" name="judgememo"  readonly
									class="easyui-validatebox" style="width:800px;height:90px;" rows="5" cols="6">
									${tQhWfelectricaldispatchingEntity.judgememo }
									</textarea></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
						<a href="javascript:void(0)"
						onclick="showWfImag('${processId}','機電派工申請單','');">點擊查看簽核流程圖</a>
					</th>
				</tr>
				<tr>
					<td colspan="10" style="text-align:left;">
					${chargeNodeInfo}
					</td>
				</tr>
				<tr>
					<td colspan="10" style="text-align:left;"><iframe
							id="qianheLogFrame" name="qianheLogFrame"
							src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhWfelectricaldispatchingEntity.serialno}"
							width="100%"></iframe></td>
				</tr>
				<tr class="no-print">
					<td colspan="10" style="text-align:center;padding-left:10px;">
						<a href="javascript:;" id="btnSave" class="easyui-linkbutton"
						data-options="iconCls:'icon-cancel'" style="width: 100px;"
						onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp; <a
						href="#" id="btnPrint" class="easyui-linkbutton"
						data-options="iconCls:'icon-print'" style="width: 100px;"
						onclick="printWindow('btnClose,btnPrint');">列印</a>

					</td>
				</tr>
			</table>
		</div>
		<div id="dlg"></div>
	</form>
</body>
</html>