<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>機電派工申請單</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
	type="text/javascript"></script>
<script src='${ctx}/static/js/other/tqhwfelectricaldispatching.js?random=<%= Math.random()%>'></script>
</head>
<body>
	<form id="mainform" action="${ctx}/tqhwfelectricaldispatching/${action}" method="post">
		<input id="ids" name="ids" type="hidden" value="${tQhWfelectricaldispatchingEntity.id }" />
		<input id="serialno" name="serialno" type="hidden" value="${tQhWfelectricaldispatchingEntity.serialno }" />
		<input id="createtime" name="createtime" type="hidden" value="${tQhWfelectricaldispatchingEntity.createtime }" />
		<input id="makerno" name="makerno" type="hidden" value="${tQhWfelectricaldispatchingEntity.makerno }"/>
		<input id="makername" name="makername" type="hidden" value="${tQhWfelectricaldispatchingEntity.makername }"/>
		<input id="makerdeptno" name="makerdeptno" type="hidden" value="${tQhWfelectricaldispatchingEntity.makerdeptno }"/>
		<input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${tQhWfelectricaldispatchingEntity.makerfactoryid }"/>
		<div class="commonW">
		<div class="headTitle">機電派工申請單</div>
		<div class="position_L">
				任務編碼：<span style="color:#999;"> <c:choose>
						<c:when test="${tQhWfelectricaldispatchingEntity.serialno==null}">
                                                              提交成功后自動編碼
                   </c:when>
						<c:otherwise>
                   ${tQhWfelectricaldispatchingEntity.serialno}
                   </c:otherwise>
					</c:choose>
				</span>
		</div>
		<div class="position_L1 margin_L">
				填單時間：<span style="color:#999;"> <c:choose>
						<c:when
							test="${tQhWfelectricaldispatchingEntity.createtime==null}">
                  YYYY/MM/DD
                  </c:when>
						<c:otherwise>
							<input style="border:0px" style="width: 120px" readonly="true"
								value="<fmt:formatDate value='${tQhWfelectricaldispatchingEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>" />
						</c:otherwise>
					</c:choose>
				</span>
		</div>
		<div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
		<div class="clear"></div>
			<table class="formList">
			  <c:choose>
                <c:when test="${not empty nodeName&&'生產服務部課級主管' eq nodeName}">
                <tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">申請人詳細信息</td>
							</tr>
							<tr align="center">
								<td>申請人工號&nbsp;<font color="red">*</font></td>
								<td><input id="dealno" name="dealno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.dealno }" /></td>
								<td>申請人名稱&nbsp;<font color="red">*</font></td>
								<td><input id="dealname" name="dealname" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.dealname }" /></td>
								<td>單位代碼&nbsp;<font color="red">*</font></td>
								<td colspan = "2"><input id="dealdeptno" name="dealdeptno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.dealdeptno }" /></td>
								<%-- <td>提報日期<font color="red">*</font></td>
								<td><input id="dealtime" name="dealtime" disabled="disabled"
									class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
									data-options="width: 150,required:true"
									value="<fmt:formatDate value="${tQhWfelectricaldispatchingEntity.dealtime}"/>" />
								</td> --%>
								<td>廠區&nbsp;<font color="red">*</font></td>
								<td colspan = "2"><input id="dealfactoryid" name="dealfactoryid"  disabled="disabled"
									class="easyui-combobox" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.dealfactoryid }" /></td>
							</tr>
							<tr align="center">
								<td>申請單位&nbsp;<font color="red">*</font></td>
								<td colspan="3"><input id="dealdeptname" readonly style="width:400px"
									name="dealdeptname" class="easyui-validatebox inputCss"
									data-options="width: 400,required:true"
									value="${tQhWfelectricaldispatchingEntity.dealdeptname }" /></td>
								<td>聯繫郵箱&nbsp;<font color="red">*</font></td>
								<td colspan="3"><input id="dealemail" name="dealemail" readonly style="width:300px"
									class="easyui-validatebox inputCss" data-options="width: 300,required:true"
									value="${tQhWfelectricaldispatchingEntity.dealemail }" /></td>
								<td>聯繫分機&nbsp;<font color="red">*</font></td>
								<td><input id="dealtel" name="dealtel" readonly
									class="easyui-validatebox inputCss" data-options="width: 150,required:true,prompt:'565+66666'"
									value="${tQhWfelectricaldispatchingEntity.dealtel }" /></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">派工申請信息</td>
							</tr>
							<tr align="center">
								<td>派工項目&nbsp;<font color="red">*</font></td>
								<td colspan="2"><input id="taskproject" name="taskproject" readonly
									class="easyui-validatebox inputCss" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.taskproject }" /></td>
								<td>派工地點&nbsp;<font color="red">*</font></td>
								<td colspan="2"><input id="taskplace" name="taskplace" readonly
									class="easyui-validatebox inputCss" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.taskplace }" /></td>
								<td colspan="2">期望完工日期&nbsp;<font color="red">*</font></td>
								<td colspan="2"><input id="taskexceptdealtime"
									name="taskexceptdealtime" class="easyui-my97" disabled="disabled"
									datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 150,required:true"
									value="<fmt:formatDate value="${tQhWfelectricaldispatchingEntity.taskexceptdealtime}"/>" />
								</td>
							</tr>
							<tr align="center">
								<td>派工需求類別&nbsp;<font color="red">*</font></td>
								<td colspan="2">
								<div class="categoryDiv"></div>
								<input id="taskcategory" name="taskcategory" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${tQhWfelectricaldispatchingEntity.taskcategory }" />
								<input id="disOrEnabled" type="hidden" value="disabled"/>
								</td>
								<td>緊急程度&nbsp;<font color="red">*</font></td>
								<td colspan="2">
								<div class="emergencyDiv"></div>
								<input id="taskemergency" name="taskemergency" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${tQhWfelectricaldispatchingEntity.taskemergency }" /></td>
								<td colspan="2">制程&nbsp;<font color="red">*</font></td>
								<td colspan="2"><input id="zhicheng" name="zhicheng" 
									class="easyui-validatebox" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.zhicheng }" /><font color = "red">例：802,803,902等，若不在此範圍內填寫其他</font></td>
									
									
							</tr>
							<tr align="center">
								<td>派工需求說明&nbsp;<font color="red">*</font></td>
								<td colspan="9"><textarea id="taskmemo" name="taskmemo" data-options="required:true" readonly
									class="easyui-validatebox" style="width:1000px;height:80px;" rows="5" cols="6"
									>${tQhWfelectricaldispatchingEntity.taskmemo }</textarea></td>
							</tr>
							<tr align="center">
								<td>附件</td>
								<td colspan="9" style="text-align: left">
									<input type="hidden" id="attachids" name="attachids" value="${tQhWfelectricaldispatchingEntity.attachids }"/>
									<div id="dowloadUrl">
										<c:forEach items="${file}" varStatus="i" var="item">
											<div id="${item.id}"
												 style="line-height:30px;margin-left:5px;" class="float_L">
												<div class="float_L">
													<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
												</div>
											</div>
										</c:forEach>
									</div>
								</td>
							</tr>
						</table>
					</td>
				</tr>     
                </c:when>
                <c:otherwise>
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">申請人詳細信息</td>
							</tr>
							<tr align="center">
								<td>申請人工號&nbsp;<font color="red">*</font></td>
								<td><input id="dealno" name="dealno" onblur="queryUserInfo1(this)"
									class="easyui-validatebox" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.dealno }" /></td>
								<td>申請人名稱&nbsp;<font color="red">*</font></td>
								<td><input id="dealname" name="dealname" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.dealname }" /></td>
								<td>單位代碼&nbsp;<font color="red">*</font></td>
								<td colspan = "2"><input id="dealdeptno" name="dealdeptno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.dealdeptno }" /></td>
								<%-- <td>提報日期<font color="red">*</font></td>
								<td><input id="dealtime" name="dealtime"
									class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
									data-options="width: 150,required:true"
									value="<fmt:formatDate value="${tQhWfelectricaldispatchingEntity.dealtime}"/>" />
								</td> --%>
								<td>廠區&nbsp;<font color="red">*</font></td>
								<td colspan = "2"><input id="dealfactoryid" name="dealfactoryid" 
									class="easyui-combobox" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.dealfactoryid }" /></td>
							</tr>
							<tr align="center">
								<td>申請單位&nbsp;<font color="red">*</font></td>
								<td colspan="3"><input id="dealdeptname"  style="width:400px"
									name="dealdeptname" class="easyui-validatebox"
									data-options="width: 400,required:true"
									value="${tQhWfelectricaldispatchingEntity.dealdeptname }" /></td>
								<td>聯繫郵箱&nbsp;<font color="red">*</font></td>
								<td colspan="3"><input id="dealemail" name="dealemail" style="width:300px"
									class="easyui-validatebox" data-options="width: 300,required:true"
									value="${tQhWfelectricaldispatchingEntity.dealemail }" /></td>
								<td>聯繫分機&nbsp;<font color="red">*</font></td>
								<td><input id="dealtel" name="dealtel"
									class="easyui-validatebox" data-options="width: 150,required:true,prompt:'565+66666'"
									value="${tQhWfelectricaldispatchingEntity.dealtel }" /></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">派工申請信息</td>
							</tr>
							<tr align="center">
								<td>派工項目&nbsp;<font color="red">*</font></td>
								<td colspan="2"><input id="taskproject" name="taskproject"
									class="easyui-validatebox" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.taskproject }" /></td>
								<td>派工地點&nbsp;<font color="red">*</font></td>
								<td colspan="2"><input id="taskplace" name="taskplace"
									class="easyui-validatebox" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.taskplace }" /></td>
								<td colspan="2">期望完工日期&nbsp;<font color="red">*</font></td>
								<td colspan="2"><input id="taskexceptdealtime"
									name="taskexceptdealtime" class="easyui-my97"
									datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 150,required:true"
									value="<fmt:formatDate value="${tQhWfelectricaldispatchingEntity.taskexceptdealtime}"/>" />
								</td>
							</tr>
							<tr align="center">
								<td>派工需求類別&nbsp;<font color="red">*</font></td>
								<td colspan="2">
								<div class="categoryDiv"></div>
								<input id="taskcategory" name="taskcategory" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${tQhWfelectricaldispatchingEntity.taskcategory }" /></td>
								<td>緊急程度&nbsp;<font color="red">*</font></td>
								<td colspan="2">
								<div class="emergencyDiv"></div>
								<input id="taskemergency" name="taskemergency" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${tQhWfelectricaldispatchingEntity.taskemergency }" /></td>
								<td colspan="2">制程&nbsp;<font color="red">*</font></td>
								<td colspan="2"><input id="zhicheng" name="zhicheng" 
									class="easyui-validatebox" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.zhicheng }" /></td>
							</tr>
							<tr align="center">
								<td>派工需求說明&nbsp;<font color="red">*</font></td>
								<td colspan="9"><textarea id="taskmemo" name="taskmemo" data-options="required:true"
									class="easyui-validatebox" style="width:1000px;height:80px;" rows="5" cols="6"
									>${tQhWfelectricaldispatchingEntity.taskmemo }</textarea></td>
							</tr>
							<tr align="center">
								<td>附件</td>
								<td colspan="9" style="text-align: left">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
									<input type="hidden" id="attachids" name="attachids" value="${tQhWfelectricaldispatchingEntity.attachids }"/>
									<div id="dowloadUrl">
										<c:forEach items="${file}" varStatus="i" var="item">
											<div id="${item.id}"
												 style="line-height:30px;margin-left:5px;" class="float_L">
												<div class="float_L">
													<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
												</div>
												<div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
											</div>
										</c:forEach>
									</div>
								</td>
							</tr>
						</table>
					</td>
				</tr>
			</c:otherwise>
			</c:choose>
				
<%-- 				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">區域負責人信息</td>
							</tr>
							<tr align="center">
								<td>接單日期<font color="red">*</font></td>
								<td colspan="2"><input id="qchargeaccepttime"
									name="qchargeaccepttime" class="easyui-my97"
									datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 150,required:true"
									value="<fmt:formatDate value="${tQhWfelectricaldispatchingEntity.qchargeaccepttime}"/>" />
								</td>
								<td colspan="2">區域負責人工號<font color="red">*</font></td>
								<td><input id="qchargeno" name="qchargeno"
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.qchargeno }" /></td>
								<td>區域負責人<font color="red">*</font></td>
								<td colspan="3"><input id="qchargename" name="qchargename"
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.qchargename }" /></td>
							</tr>
							<tr align="center">
								<td>單位代碼<font color="red">*</font></td>
								<td colspan="2"><input id="qchargedptno"
									name="qchargedptno" class="easyui-validatebox inputCss"
									data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.qchargedptno }" /></td>
								<td colspan="2">聯繫分機<font color="red">*</font></td>
								<td><input id="qchargetel" name="qchargetel"
									class="easyui-validatebox" data-options="width: 150,required:true,prompt:'565+66666'"
									value="${tQhWfelectricaldispatchingEntity.qchargetel }" /></td>
								<td>聯繫郵箱<font color="red">*</font></td>
								<td colspan="3"><input id="qchargemail" name="qchargemail"
									class="easyui-validatebox" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.qchargemail }" /></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">表單作業信息</td>
							</tr>
							<tr align="center">
								<td colspan="2">施工人員工號<font color="red">*</font></td>
								<td colspan="2"><input id="constrno" name="constrno" onblur="queryUserInfo3(this)"
									class="easyui-validatebox" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.constrno }" /></td>
								<td colspan="2">施工人員姓名<font color="red">*</font></td>
								<td><input id="constrname" name="constrname"
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.constrname }" /></td>
								<td>單位代碼<font color="red">*</font></td>
								<td colspan="2"><input id="constrdptno" name="constrdptno"
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.constrdptno }" /></td>
							</tr>
							<tr align="center">
								<td colspan="2">開工日期<font color="red">*</font></td>
								<td colspan="2"><input id="constrstaettime"
									name="constrstaettime" class="easyui-my97"
									datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 150,required:true"
									value="<fmt:formatDate value="${tQhWfelectricaldispatchingEntity.constrstaettime}"/>" />
								</td>
								<td colspan="2">施工進度<font color="red">*</font></td>
								<td><input id="constrrate" name="constrrate"
									class="easyui-validatebox" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.constrrate }" /></td>
								<td>完工日期<font color="red">*</font></td>
								<td colspan="2"><input id="constrcomplettime"
									name="constrcomplettime" class="easyui-my97"
									datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 150,required:true"
									value="<fmt:formatDate value="${tQhWfelectricaldispatchingEntity.constrcomplettime}"/>" />
								</td>
							</tr>
							<tr align="center">
								<td colspan="2">施工狀況說明</td>
								<td colspan="8">
								<textarea id="constrmemo" name="constrmemo"
									class="easyui-validatebox" style="width:1000px;height:80px;" rows="5" cols="6"
									>${tQhWfelectricaldispatchingEntity.constrmemo }</textarea></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">服務評價基本信息</td>
							</tr>
							<tr align="center">
								<td>評價人工號<font color="red">*</font></td>
								<td><input id="judgeno" name="judgeno" onblur="queryUserInfo4(this)"
									class="easyui-validatebox" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.judgeno }" /></td>
								<td>評價人<font color="red">*</font></td>
								<td><input id="judgename" name="judgename"
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhWfelectricaldispatchingEntity.judgename }" /></td>
								<td>單位代碼<font color="red">*</font></td>
								<td><input id="judgedeptno" name="judgedeptno"
									class="easyui-validatebox inputCss" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.judgedeptno }" /></td>
								<td>聯繫郵箱<font color="red">*</font></td>
								<td colspan="3"><input id="judgemail" name="judgemail"
									class="easyui-validatebox" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.judgemail }" /></td>
							</tr>
							<tr align="center">
								<td>聯繫分機<font color="red">*</font></td>
								<td><input id="judgetel" name="judgetel"
									class="easyui-validatebox" data-options="width: 150,required:true,prompt:'565+66666'"
									value="${tQhWfelectricaldispatchingEntity.judgetel }" /></td>
								<td>評價日期<font color="red">*</font></td>
								<td><input id="judgetime" name="judgetime"
									class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
									data-options="width: 150,required:true"
									value="<fmt:formatDate value="${tQhWfelectricaldispatchingEntity.judgetime}"/>" />
								</td>
								<td>評價<font color="red">*</font></td>
								<td colspan="5">
								<div class="judgeDiv"></div>
								<input id="judgeresult" name="judgeresult" type="hidden"
									colspan="3" class="easyui-validatebox" data-options="width: 150,required:true"
									value="${tQhWfelectricaldispatchingEntity.judgeresult }" /></td>
							</tr>
							<tr align="center">
								<td>評價說明</td>
								<td colspan="9">
								<textarea id="judgememo" name="judgememo"
									class="easyui-validatebox" style="width:1000px;height:80px;" rows="5" cols="6">
									${tQhWfelectricaldispatchingEntity.judgememo }
									</textarea></td>
							</tr>
						</table>
					</td>
				</tr> --%>
				<c:choose>
                <c:when test="${not empty nodeName&&'生產服務部課級主管' eq nodeName}">
                <tr>
				    <td>
				       <table  class="formList">
				         <tr>
                             <td style = "width:100px">批註</td>
                             <td align="left" colspan="9">
						      <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;" rows="4" cols="4" value=""></textarea>
                             </td>
                         </tr>
                       </table>
                    </td>
                </tr>
                 <tr align="center">
                    <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                      <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                        serialNo="${tQhWfelectricaldispatchingEntity.serialno}"></fox:action>
                    </td>
                </tr>
				<tr>
					<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
						<a href="javascript:void(0)"
						onclick="showWfImag('${processId}','機電派工申請流程','');">點擊查看簽核流程圖</a>
					</th>
				</tr>
				<tr>
					<td colspan="10" style="text-align:left;">
					${chargeNodeInfo}
					</td>
				</tr>
				<tr>
					<td colspan="10" style="text-align:left;"><iframe
							id="qianheLogFrame" name="qianheLogFrame"
							src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhWfelectricaldispatchingEntity.serialno}"
							width="100%"></iframe>
					</td>
				</tr>
                 </c:when>
                 <c:otherwise>
                 <tr>
					<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
						<a href="javascript:void(0)"
						onclick="showWfImag('${processId}','機電派工申請單','');">點擊查看簽核流程圖</a>
					</th>
				</tr>
				<tr>
					<td>
						<table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
							<tr>
								<td style="border:none">
									<table width="23%" style="float: left;margin-left: 5px;">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">課級主管</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealfactoryid').val())"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="kchargeno" name="kchargeno"
												class="easyui-validatebox"
												data-options="width:80,required:true" readonly
												value="${tQhWfelectricaldispatchingEntity.kchargeno }" /> <font
												color="red">*</font>/<input id="kchargename"
												name="kchargename" readonly class="easyui-validatebox"
												data-options="width:80,required:true"
												value="${tQhWfelectricaldispatchingEntity.kchargename }" /></td>
										</tr>
									</table>
									<table width="23%" style="float: left;margin-left: 5px;">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">部級主管</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealfactoryid').val())"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="bchargeno" name="bchargeno"
												class="easyui-validatebox" data-options="width: 80" readonly
												value="${tQhWfelectricaldispatchingEntity.bchargeno }" />/<input
												id="bchargename" name="bchargename"
												class="easyui-validatebox" data-options="width: 80" readonly
												value="${tQhWfelectricaldispatchingEntity.bchargename }" /></td>
										</tr>
									</table>
                                    <table width="23%" style="float: left;margin-left: 5px;">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">廠級主管</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon" id = "cchargeDiv"
																onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').combobox('getValue'))"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="cchargeno" name="cchargeno" style = "background-color:#D0D0D0" 
												class="easyui-validatebox" data-options="width: 80" readonly
												value="${tQhWfelectricaldispatchingEntity.cchargeno }" /><span id = "cXing"></span>/<input
												id="cchargename" name="cchargename" style = "background-color:#D0D0D0"
												class="easyui-validatebox" data-options="width: 80" readonly
												value="${tQhWfelectricaldispatchingEntity.cchargename }" /></td>
										</tr>
									</table>
									<table width="23%" style="float: left;margin-left: 5px;"
										id="scfwsafeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">安全管理部</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon" id = "scfwsafeDiv"
																onclick="selectRole2(34,'scfwsafeTable','scfwsafeno','scfwsafename',$('#dealfactoryid').combobox('getValue'),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="scfwsafeno" name="scfwsafeno"
												class="easyui-validatebox"  style = "background-color:#D0D0D0"
												data-options="width: 80," readonly
												value="${tQhWfelectricaldispatchingEntity.scfwsafeno }" /><span id = "scfwsafeXing"></span>/ <input id="scfwsafename"
												name="scfwsafename" class="easyui-validatebox" style = "background-color:#D0D0D0"
												data-options="width: 80" readonly
												value="${tQhWfelectricaldispatchingEntity.scfwsafename }" /></td>
										</tr>
									</table>						
								</td>
							</tr>
							<tr>
								<td style="border:none">
									<table width="23%" style="float: left;margin-left: 5px;"
										id="scfwpgchargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">機電窗口承接</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole2(30,'scfwpgchargeTable','scfwpgchargeno','scfwpgchargename',$('#dealfactoryid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="scfwpgchargeno" name="scfwpgchargeno"
												class="easyui-validatebox"
												data-options="width: 80,required:true" readonly
												value="${tQhWfelectricaldispatchingEntity.scfwpgchargeno }" /> <font
												color="red">*</font>/ <input id="scfwpgchargename"
												name="scfwpgchargename" class="easyui-validatebox"
												data-options="width: 80,required:true" readonly
												value="${tQhWfelectricaldispatchingEntity.scfwpgchargename }" /></td>
										</tr>
									</table>
									<table width="23%" style="float: left;margin-left: 5px;"
										id="scfwqychargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">區域負責人安排施工</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole2(31,'scfwqychargeTable','scfwqychargeno','scfwqychargename',$('#dealfactoryid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="scfwqychargeno" name="scfwqychargeno"
												class="easyui-validatebox"  onchange="getQycharge()"
												data-options="width: 80,required:true" readonly
												value="${tQhWfelectricaldispatchingEntity.scfwqychargeno }" /> <font
												color="red">*</font>/ <input id="scfwqychargename"
												name="scfwqychargename" class="easyui-validatebox"
												data-options="width: 80,required:true" readonly
												value="${tQhWfelectricaldispatchingEntity.scfwqychargename }" /></td>
										</tr>
									</table>
									<table width="23%" style="float: left;margin-left: 5px;"
										id="judgechargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: center;">申請單位服務評價</td>
														<td style="border: none;">
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="judgechargeno" name="judgechargeno"
												class="easyui-validatebox"
												data-options="width: 80,required:true" readonly
												value="${tQhWfelectricaldispatchingEntity.judgechargeno }" /> <font
												color="red">*</font>/ <input id="judgechargename"
												name="judgechargename" class="easyui-validatebox"
												data-options="width: 80,required:true" readonly
												value="${tQhWfelectricaldispatchingEntity.judgechargename }" /></td>
										</tr>
									</table>
									<table width="23%" style="float: left;margin-left: 5px;"
										id="scfwkchargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">主管核准</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole2(33,'scfwkchargeTable','scfwkchargeno','scfwkchargename',$('#dealfactoryid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="scfwkchargeno" name="scfwkchargeno"
												class="easyui-validatebox"
												data-options="width: 80,required:true" readonly
												value="${tQhWfelectricaldispatchingEntity.scfwkchargeno }" /> <font
												color="red">*</font>/ <input id="scfwkchargename"
												name="scfwkchargename" class="easyui-validatebox"
												data-options="width: 80,required:true" readonly
												value="${tQhWfelectricaldispatchingEntity.scfwkchargename }" /></td>
										</tr>
									</table>
								</td>
							</tr>
						</table>
					</td>
				</tr>
		        <tr>
                    <td colspan="10" style="text-align:left;">
                        <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhWfelectricaldispatchingEntity.serialno}"
                                        width="100%"></iframe>
                     </td>
                 </tr>
                 <tr>
                      <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                         <a href="javascript:;" id="btnSave" class="easyui-linkbutton" data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                         <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="canelTask('${tQhWfelectricaldispatchingEntity.serialno }');">取消申請</a>
                       </td>
                 </tr>
                 </c:otherwise>
                 </c:choose>
			</table>
		</div>
		<input type="hidden" id="deptNo" name="deptNo" value="" /> 
		<input type="hidden" id="chargeNo" name="chargeNo" value="" />
		<input type="hidden" id="chargeName" name="chargeName" value="" />
        <input type="hidden" id="factoryId" name="factoryId" value="" />
        <input type="hidden" id="dutyId" name="dutyId" value="" />
        
        <!--  區域負責人相關信息 -->
        <input id="qchargeno" name="qchargeno" type="hidden"/>
        <input id="qchargename" name="qchargename" type="hidden"/>
        <input id="qchargedptno" name="qchargedptno" type="hidden"/>
        <input id="qchargemail" name="qchargemail" type="hidden"/>
		<div id="win"></div>
	</form>
</body>
</html>