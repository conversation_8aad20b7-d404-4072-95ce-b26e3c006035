<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>清潔服務需求單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
<script src='${ctx}/static/js/other/wfcleanprocess.js?random=<%= Math.random()%>'></script>
<style type="text/css">
    .td_style2 {
        text-align: left;
        color: black;
    }
</style>
</head>
<body>
<form id="mainform" action="${ctx}/wfcleanprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfcleanprocessEntity.id }"/>
    <input id="serialno" name="wfcleanprocess.serialno" type="hidden" value="${wfcleanprocessEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">清潔服務需求單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfcleanprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfcleanprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfcleanprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfcleanprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfcleanprocessEntity.makerno}/${wfcleanprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
           <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">服務類型</td>
                            <td width="15%" class="td_style1">
                                <input id="servicetype" name="wfcleanprocess.servicetype" class="easyui-combobox"
                                       value="${wfcleanprocessEntity.servicetype }" panelHeight="auto" editable="false" disabled/>
                            </td>
                            <td width="10%">廠區</td>
                            <td width="15%" class="td_style1">
                                <input id="applyfactoryid" name="wfcleanprocess.factoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfcleanprocessEntity.factoryid }" disabled
                                       data-options="width: 120"/>
                            </td>
                            <td width="10%">需求日期</td>
                            <td width="15%" class="td_style1">
                                <input id="requiretime" name="wfcleanprocess.requiretime" class="Wdate"
                                       data-options="width:100,required:true" value="<fmt:formatDate  pattern="yyyy-MM-dd"
                                       value="${wfcleanprocessEntity.requiretime}"/>" disabled
                                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                            </td>
                            <td width="10%">生效日期</td>
                            <td width="15%" class="td_style1">
                                <c:choose>
                                    <c:when test="${not empty nodeName&&'總務確認窗口' eq nodeName}">
                                        <input id="takeeffecttime" name="wfcleanprocess.takeeffecttime" class="Wdate"
                                               data-options="width:100,required:true" value="<fmt:formatDate  pattern="yyyy-MM-dd"
                                               value="${wfcleanprocessEntity.takeeffecttime}"/>"
                                               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                                    </c:when>
                                    <c:otherwise>
                                        <input id="takeeffecttime" name="wfcleanprocess.takeeffecttime" class="Wdate"
                                               data-options="width:100,required:true" value="<fmt:formatDate  pattern="yyyy-MM-dd"
                                               value="${wfcleanprocessEntity.takeeffecttime}"/>" disabled
                                               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>申請人工號</td>
                            <td class="td_style1">
                                <input id="applyno" name="wfcleanprocess.applyno" class="easyui-validatebox inputCss"
                                       data-options="width: 80" value="${wfcleanprocessEntity.applyno}" readonly/>
                            </td>
                            <td>申請人姓名</td>
                            <td class="td_style1">
                                <input id="applyname" name="wfcleanprocess.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfcleanprocessEntity.applyname }"/>
                            </td>
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="leveltype" name="wfcleanprocess.leveltype" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfcleanprocessEntity.leveltype }" readonly/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="ismanager" name="wfcleanprocess.ismanager" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfcleanprocessEntity.ismanager }" readonly/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>分機/短號</td>
                            <td class="td_style1">
                                <input id="phone" name="wfcleanprocess.phone" class="easyui-validatebox inputCss"
                                       style="width:140px;" value="${wfcleanprocessEntity.phone}" readonly />
                            </td>
                            <td>郵箱</td>
                            <td colspan="3" class="td_style1">
                                <input id="email" name="wfcleanprocess.email" class="easyui-validatebox inputCss"
                                       value="${wfcleanprocessEntity.email}" style="width:300px;" readonly/>
                            </td>
                            <td>法人</td>
                            <td class="td_style1">
                                <input id="corporation" name="wfcleanprocess.corporation" class="easyui-combobox"
                                       value="${wfcleanprocessEntity.corporation}" panelHeight="auto" editable="false" disabled/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位代碼</td>
                            <td class="td_style1">
                                <input id="applydeptno" name="wfcleanprocess.deptno" class="easyui-validatebox inputCss" data-options="width: 90"
                                       value="${wfcleanprocessEntity.deptno }" readonly/>
                            </td>
                            <td>單位名稱</td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="wfcleanprocess.deptname" class="easyui-validatebox inputCss" data-options="width: 450"
                                       value="${wfcleanprocessEntity.deptname }" readonly/>
                            </td>
                            <td>費用代碼</td>
                            <td class="td_style1">
                                <input id="expenseno" name="wfcleanprocess.expenseno" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfcleanprocessEntity.expenseno }" readonly/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="8" width="100%">
                                <div style="overflow-x: auto;width: 100%;">
                                    <table id="cleanItemTable" width="100%">
                                        <tr align="center">
                                            <td width="30%">需求地點</td>
                                            <td width="15%">清潔類型</td>
                                            <td width="10%">清潔面積/人數</td>
                                            <td width="10%">單價</td>
                                            <td width="10%">金額</td>
                                        </tr>
                                        <c:if test="${cleanItemEntity!=null&&cleanItemEntity.size()>0}">
                                            <c:forEach items="${cleanItemEntity}" var="cleanItem" varStatus="status">
                                                <tr align="center" id="cleanItem${status.index+1}">
                                                    <td>
                                                            ${cleanItem.area}區 ${cleanItem.building}棟 ${cleanItem.floor}層 ${cleanItem.position}位置
                                                    </td>
                                                    <td>
                                                        <input id="clean_cleantype${status.index+1}" name="wfcleanitems[${status.index}].cleantype" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadCleantype(${status.index+1});}" style="width:150px;"
                                                               class="easyui-combobox" editable="false" disabled value="${cleanItem.cleantype}"/>
                                                    </td>
                                                    <td>${cleanItem.cleanacreage}</td>
                                                    <td>${cleanItem.unitprice}</td>
                                                    <td>${cleanItem.price}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <tr align="center">
                                            <td colspan="4" align="right">總金額：</td>
                                            <td colspan="2">
                                                <input type="text" id="pricesum" name="wfcleanprocess.pricesum" class="inputCss" style="width:80px;" value="${wfcleanprocessEntity.pricesum }"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>

                        <tr align="center">
                            <td>服務需求描述</td>
                            <td colspan="7" class="td_style1">
						    <textarea id="requiredescribe" name="wfcleanprocess.requiredescribe"
                                      class="easyui-validatebox"
                                      style="width:99%;height:80px;" rows="5" cols="6" readonly>${wfcleanprocessEntity.requiredescribe }</textarea></td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="7" class="td_style1">
                                <input type="hidden" id="attachids" name="wfcleanprocess.attachids" value="${wfcleanprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="7" class="td_style2">
                                1.一張單據若有多筆需求地點，則生效日期必須相同；<br>
                                2.“服務要求描述”需說明清潔頻率，清潔範圍；<br>
                                3.服務項目需取消時，也需填寫此單呈簽，以便總務單位取消費用預算作業；<br>
                                4.簽核流程：申請人填寫需求單——需求單位確認——會簽經管——清潔公司確認——總務確認并填寫生效日期；<br>
                                5.相關費用單價參照物業中標單價。
                            </td>
                        </tr>
                        <tr align="center">
                            <td>批註</td>
                            <td colspan="7" style="text-align: left">
						        <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                      style="width:1000px;height:60px;" rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                    </table>
                </td>
           </tr>
            <tr>
                <td>
                     <table class="formList">
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <c:choose>
                                    <c:when test="${not empty nodeName&&'總務確認窗口' eq nodeName}">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"  perCall="zwupdate"
                                                    serialNo="${wfcleanprocessEntity.serialno}"></fox:action>
                                    </c:when>
                                    <c:otherwise>
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                                    serialNo="${wfcleanprocessEntity.serialno}"></fox:action>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','清潔服務需求單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfcleanprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
  <div id="dlg"></div>
</body>
</html>