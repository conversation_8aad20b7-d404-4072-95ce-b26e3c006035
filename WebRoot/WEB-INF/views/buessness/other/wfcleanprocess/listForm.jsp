<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>清潔服務需求單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/other/wfcleanprocess.js?random=<%= Math.random()%>'></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
            color: black;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfcleanprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfcleanprocessEntity.id }"/>
    <input id="serialno" name="wfcleanprocess.serialno" type="hidden" value="${wfcleanprocessEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfcleanprocessEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfcleanprocessEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfcleanprocessEntity.makerdeptno }"/>
    <div class="commonW">
        <div class="headTitle">清潔服務需求單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfcleanprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfcleanprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfcleanprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfcleanprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfcleanprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfcleanprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${wfcleanprocessEntity.makerno}/${wfcleanprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">服務類型&nbsp;<font color="red">*</font></td>
                            <td width="15%" class="td_style1">
                                <input id="servicetype" name="wfcleanprocess.servicetype" class="easyui-combobox"
                                       value="${wfcleanprocessEntity.servicetype }" panelHeight="auto" editable="false"/>
                            </td>
                            <td width="10%">廠區&nbsp;<font color="red">*</font></td>
                            <td width="15%" class="td_style1">
                                <input id="applyfactoryid" name="wfcleanprocess.factoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfcleanprocessEntity.factoryid }"
                                       data-options="width: 120,required:true"/>
                            </td>
                            <td width="10%">需求日期&nbsp;<font color="red">*</font></td>
                            <td width="15%" class="td_style1">
                                <input id="requiretime" name="wfcleanprocess.requiretime" class="Wdate"
                                       data-options="width:100,required:true" value="<fmt:formatDate  pattern="yyyy-MM-dd"
                                       value="${wfcleanprocessEntity.requiretime}"/>"
                                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                            </td>
                            <td width="10%">生效日期</td>
                            <td width="15%" class="td_style1">
                                <input id="takeeffecttime" name="wfcleanprocess.takeeffecttime" class="Wdate"
                                       data-options="width:100,required:true" value="<fmt:formatDate  pattern="yyyy-MM-dd"
                                       value="${wfcleanprocessEntity.takeeffecttime}"/>" disabled
                                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                            </td>
                        </tr>
                        <tr align="center">
                            <td>申請人工號&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyno" name="wfcleanprocess.applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfcleanprocessEntity.applyno}" onblur="queryUserInfo(this);"/>
                            </td>
                            <td>申請人姓名</td>
                            <td class="td_style1">
                                <input id="applyname" name="wfcleanprocess.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfcleanprocessEntity.applyname }"/>
                            </td>
                            <td>資位&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="leveltype" name="wfcleanprocess.leveltype" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfcleanprocessEntity.leveltype }" readonly/>
                            </td>
                            <td>管理職&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="ismanager" name="wfcleanprocess.ismanager" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfcleanprocessEntity.ismanager }" readonly/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>分機/短號&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="phone" name="wfcleanprocess.phone" class="easyui-validatebox"
                                       style="width:140px;"
                                       value="${wfcleanprocessEntity.phone}" data-options="required:true,prompt:'579+66666/42+888888'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="email" name="wfcleanprocess.email" class="easyui-validatebox"
                                       value="${wfcleanprocessEntity.email}" style="width:300px;"
                                       data-options="required:true" onblur="valdEmail('')"/>
                            </td>
                            <td>法人&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="corporation" name="wfcleanprocess.corporation" class="easyui-combobox"
                                       value="${wfcleanprocessEntity.corporation}" panelHeight="auto" editable="false"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位代碼&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applydeptno" name="wfcleanprocess.deptno" class="easyui-validatebox inputCss" data-options="width: 90"
                                       value="${wfcleanprocessEntity.deptno }" readonly/>
                            </td>
                            <td>單位名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="wfcleanprocess.deptname" class="easyui-validatebox" data-options="width: 450"
                                       value="${wfcleanprocessEntity.deptname }"/>
                            </td>
                            <td>費用代碼&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="expenseno" name="wfcleanprocess.expenseno" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfcleanprocessEntity.expenseno }" readonly/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="8" width="100%">
                                <div style="overflow-x: auto;width: 100%;">
                                    <input id="cleanItemTableIndex" type="hidden"
                                           value="<c:if test="${cleanItemEntity!=null && cleanItemEntity.size()>0}">${cleanItemEntity.size() +1}</c:if>
                                        <c:if test="${cleanItemEntity==null}">2</c:if>">
                                    </input>
                                    <table id="cleanItemTable" width="100%">
                                        <tr align="center">
                                            <td width="30%">需求地點&nbsp;<font color="red">*</font></td>
                                            <td width="15%">清潔類型&nbsp;<font color="red">*</font></td>
                                            <td width="10%">清潔面積/人數&nbsp;<font color="red">*</font></td>
                                            <td width="10%">單價&nbsp;<font color="red">*</font></td>
                                            <td width="10%">金額&nbsp;<font color="red">*</font></td>
                                            <td width="5%">操作</td>
                                        </tr>
                                        <c:if test="${cleanItemEntity!=null&&cleanItemEntity.size()>0}">
                                            <c:forEach items="${cleanItemEntity}" var="cleanItem" varStatus="status">
                                                <tr align="center" id="cleanItem${status.index+1}">
                                                    <td>
                                                        <input id="clean_area${status.index+1}" name="wfcleanitems[${status.index}].area"
                                                               class="easyui-validatebox" data-options="required:true"  style="width:40px;" value="${cleanItem.area}"/>區
                                                        <input id="clean_building${status.index+1}" name="wfcleanitems[${status.index}].building"
                                                               class="easyui-validatebox" data-options="required:true" style="width:40px;" value="${cleanItem.building}"/>棟
                                                        <input id="clean_floor${status.index+1}" name="wfcleanitems[${status.index}].floor"
                                                               class="easyui-validatebox" data-options="required:true" style="width:40px;" value="${cleanItem.floor}"/>層
                                                        <input id="clean_position${status.index+1}" name="wfcleanitems[${status.index}].position"
                                                               class="easyui-validatebox" data-options="required:true" style="width:180px;" value="${cleanItem.position}"/>位置
                                                    </td>
                                                    <td>
                                                        <input id="clean_cleantype${status.index+1}" name="wfcleanitems[${status.index}].cleantype" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadCleantype(${status.index+1});}" style="width:150px;"
                                                               class="easyui-combobox" editable="false" value="${cleanItem.cleantype}"/>
                                                    </td>
                                                    <td><input id="clean_cleanacreage${status.index+1}" name="wfcleanitems[${status.index}].cleanacreage" onchange="computePrice(this)"
                                                               class="easyui-validatebox" data-options="required:true" style="width:80px;" value="${cleanItem.cleanacreage}"/></td>
                                                    <td><input id="clean_unitprice${status.index+1}" name="wfcleanitems[${status.index}].unitprice" onchange="computePrice(this)"
                                                               class="easyui-validatebox" data-options="required:true" style="width:80px;" value="${cleanItem.unitprice}"/></td>
                                                    <td>
                                                        <input id="clean_price${status.index+1}" name="wfcleanitems[${status.index}].price"
                                                               class="inputCss"  style="width:80px;" value="${cleanItem.price}"/>
                                                    </td>
                                                    <td><input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="cleandeltr(${status.index+1});return false;"/></td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${cleanItemEntity==null}">
                                            <tr align="center" id="cleanItem1">
                                                <td>
                                                    <input id="clean_area1" name="wfcleanitems[0].area"
                                                           class="easyui-validatebox" data-options="required:true" style="width:40px;" value=""/>區
                                                    <input id="clean_building1" name="wfcleanitems[0].building"
                                                           class="easyui-validatebox" data-options="required:true" style="width:40px;" value=""/>棟
                                                    <input id="clean_floor1" name="wfcleanitems[0].floor"
                                                           class="easyui-validatebox" data-options="required:true" style="width:40px;" value=""/>層
                                                    <input id="clean_position1" name="wfcleanitems[0].position"
                                                           class="easyui-validatebox" data-options="required:true" style="width:180px;" value=""/>位置
                                                </td>
                                                <td>
                                                    <input id="clean_cleantype1" name="wfcleanitems[0].cleantype" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadCleantype(1);}" style="width:150px;"
                                                           class="easyui-combobox" editable="false" value=""/></td>
                                                <td>
                                                    <input id="clean_cleanacreage1" name="wfcleanitems[0].cleanacreage" onchange="computePrice(this)"
                                                           class="easyui-validatebox" data-options="required:true" style="width:80px;" value=""/></td>
                                                <td>
                                                    <input id="clean_unitprice1" name="wfcleanitems[0].unitprice"  onchange="computePrice(this)"
                                                           class="easyui-validatebox" data-options="required:true" style="width:80px;" value=""/></td>
                                                <td>
                                                    <input id="clean_price1" name="wfcleanitems[0].price" class="inputCss"  style="width:80px;" value=""/>
                                                </td>
                                                <td><input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="cleandeltr(1);return false;"/></td>
                                            </tr>
                                        </c:if>
                                        <tr align="center" class="nottr">
                                            <td colspan="4" align="right">總金額：</td>
                                            <td colspan="2">
                                                <input type="text" id="pricesum" name="wfcleanprocess.pricesum" class="inputCss" style="width:80px;" value="${wfcleanprocessEntity.pricesum }"/>
                                            </td>
                                        </tr>
                                        <tr id="addbutton_clean" class="nottr">
                                            <td colspan="6" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="cleanItemAdd" style="width:100px;float:left;" value="添加一筆"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>

                        <tr align="center">
                            <td>服務需求描述&nbsp;<font color="red">*</font></td>
                            <td colspan="7" class="td_style1">
						    <textarea id="requiredescribe" name="wfcleanprocess.requiredescribe"
                                      class="easyui-validatebox"
                                      oninput="return LessThan(this);"
                                      onchange="return LessThan(this);"
                                      onpropertychange="return LessThan(this);"
                                      maxlength="500"
                                      style="width:99%;height:80px;" data-options="required:true,prompt:'請需求單位詳細說明，如欄位不夠，請附件說明'"
                                      rows="5" cols="6"
                                      data-options="required:true,validType:'length[0,500]'">${wfcleanprocessEntity.requiredescribe }</textarea><span id="txtNum"></span></td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="7" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file"
                                           onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="wfcleanprocess.attachids" value="${wfcleanprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="7" class="td_style2">
                                1.一張單據若有多筆需求地點，則生效日期必須相同；<br>
                                2.“服務要求描述”需說明清潔頻率，清潔範圍；<br>
                                3.服務項目需取消時，也需填寫此單呈簽，以便總務單位取消費用預算作業；<br>
                                4.簽核流程：申請人填寫需求單——需求單位確認——會簽經管——清潔公司確認——總務確認并填寫生效日期；<br>
                                5.相關費用單價參照物業中標單價。
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">

                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_qingjiefuwuxuqiudan','清潔服務需求單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"  class="qhtbodyonly">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="wfcleanprocess.kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}" readonly
                                                               value="${wfcleanprocessEntity.kchargeno }"/><c:if test="${requiredMap['kchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="kchargename" name="wfcleanprocess.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfcleanprocessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"  class="qhtbodyonly">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="wfcleanprocess.bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfcleanprocessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="wfcleanprocess.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfcleanprocessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"  class="qhtbodyonly">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'cchargeno','cchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wfcleanprocess.cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfcleanprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wfcleanprocess.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfcleanprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable" class="qhtbodyonly">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applyfactoryid').combobox('getValue'),'wfcleanprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wfcleanprocess.zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfcleanprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="wfcleanprocess.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfcleanprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="jgchargeTable" class="qhtbodyonly">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">會簽經管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(81,'jgchargeTable','jgchargeno','jgchargename',$('#applyfactoryid').combobox('getValue'),'wfcleanprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jgchargeno" name="wfcleanprocess.jgchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['jgchargeno']}"
                                                               readonly
                                                               value="${wfcleanprocessEntity.jgchargeno }"/><c:if
                                                            test="${requiredMap['jgchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="jgchargename" name="wfcleanprocess.jgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jgchargeno']}"
                                                                value="${wfcleanprocessEntity.jgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="jdgwchargeTable" class="qhtbodyonly">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">會簽機電工務</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(82,'jdgwchargeTable','jdgwchargeno','jdgwchargename',$('#applyfactoryid').combobox('getValue'),'wfcleanprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jdgwchargeno" name="wfcleanprocess.jdgwchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['jdgwchargeno']}"
                                                               readonly
                                                               value="${wfcleanprocessEntity.jdgwchargeno }"/><c:if
                                                            test="${requiredMap['jdgwchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="jdgwchargename" name="wfcleanprocess.jdgwchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jdgwchargeno']}"
                                                                value="${wfcleanprocessEntity.jdgwchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zwqchargeTable" class="qhtbodyonly">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">總務確認窗口</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(70,'zwqchargeTable','zwqchargeno','zwqchargename',$('#applyfactoryid').combobox('getValue'),'wfcleanprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zwqchargeno" name="wfcleanprocess.zwqchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zwqchargeno']}"
                                                               readonly
                                                               value="${wfcleanprocessEntity.zwqchargeno }"/><c:if
                                                            test="${requiredMap['zwqchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zwqchargename" name="wfcleanprocess.zwqchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zwqchargeno']}"
                                                                value="${wfcleanprocessEntity.zwqchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
</div>
</body>
</html>