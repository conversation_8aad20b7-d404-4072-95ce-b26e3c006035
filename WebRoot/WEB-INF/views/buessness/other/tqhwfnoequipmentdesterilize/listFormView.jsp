<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>報停射線裝置恢復使用申請單-詳細查詢頁面</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/other/tqhwfnoequipmentdesterilize.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfnoequipmentdesterilize/${action}" method="post">
    <div class="commonW">
        <div id="mainDiv">
            <div class="headTitle">報停射線裝置恢復使用申請單-詳細查詢頁面</div>
            <div class="position_L">
                任務編碼：
                <span id="" style="color:#999;">${tQhWfnoequipmentdesterilize.serialno}</span>
            </div>

            <div class="position_L1 margin_L">
                填單時間：<span style="color:#999;"><fmt:formatDate value="${tQhWfnoequipmentdesterilize.createtime}" pattern="yyyy-MM-dd HH:mm:ss"/></span>
            </div>
            <div class="position_R margin_R">
                填單人：：${tQhWfnoequipmentdesterilize.makerno}/${tQhWfnoequipmentdesterilize.makername}</div>
            <br>
            <div class="clear"></div>
            <table class="formList">
                <tr>
                    <td>
                        <table>
                            <tr>
                                <td colspan="10" class="td_style1">承辦人詳細信息</td>
                            </tr>
                            <tr align="center">
                                <td width="6%">承辦人工號</td>
                                <td width="6%" class="td_style1">
                                    <input id="dealno" name="dealno" class="easyui-textbox inputCss" readonly="true"
                                           value="${tQhWfnoequipmentdesterilize.dealno }"/>
                                </td>
                                <td width="6%">承辦人</td>
                                <td width="6%" class="td_style1">
                                    <input id="dealname" name="dealname" class="easyui-textbox inputCss" readonly="true"
                                           data-options="width:80" value="${tQhWfnoequipmentdesterilize.dealname }"/>
                                </td>
                                <td width="6%">單位代碼</td>
                                <td width="6%" class="td_style1">
                                    <input id="dealdeptno" name="dealdeptno" class="easyui-textbox inputCss" readonly="true"
                                           value="${tQhWfnoequipmentdesterilize.dealdeptno }"/>
                                </td>
                                <td width="6%">提報日期</td>
                                <td width="6%" class="td_style1">
                                    <input id="dealtime" name="dealtime" class="easyui-textbox inputCss" readonly="true" data-options="width: 100,required:true"
                                           value="<fmt:formatDate value="${tQhWfnoequipmentdesterilize.dealtime}"/>"/>
                                </td>
                                <td width="6%">廠區</td>
                                <td width="6%" class="td_style1">
                                    <input class="inputCss inputCss" readonly="true" data-options="width: 90"
                                           readonly value="${factoryName}" />
                                    <%--<input id="dealfactoryid" name="dealfactoryid" class="easyui-textbox inputCss"--%>
                                    <%--panelHeight="auto" value="${tQhWfnoequipmentdesterilize.dealfactoryid }"--%>
                                    <%--data-options="width: 120,required:true"/>--%>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>單位</td>
                                <td colspan="3" class="td_style1">
                                    <input id="dealdeptname" name="dealdeptname" class="easyui-textbox inputCss" readonly="true" data-options="width: 380"
                                           value="${tQhWfnoequipmentdesterilize.dealdeptname }"/>
                                </td>
                                <td>聯繫郵箱</td>
                                <td colspan="3" class="td_style1">
                                    <input id="dealemail" name="dealemail" class="easyui-textbox inputCss" readonly="true"
                                           value="${tQhWfnoequipmentdesterilize.dealemail }"
                                           data-options="width: 300"/>

                                </td>
                                <td>聯繫分機</td>
                                <td class="td_style1">
                                    <input id="dealtel" name="dealtel" class="easyui-textbox inputCss" readonly="true"
                                           style="width:80px;"
                                           value="${tQhWfnoequipmentdesterilize.dealtel }"/>

                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td>
                        <table class="formList">
                            <tr>
                                <td colspan="10" class="td_style1">報停/報廢詳細信息</td>
                            </tr>
                            <tr align="center">
                                <td>射線裝置使用單位</td>
                                <td colspan="4" class="td_style1">
                                    <input id="equipmentdepartment" name="tQhWfnoequipmentdesterilizeEntity.equipmentdepartment" class="easyui-validatebox" style="width:150px;"
                                           data-options="required:true" value="${tQhWfnoequipmentdesterilize.equipmentdepartment }" />
                                </td>

                                <td>射線裝置工作場所</td>
                                <td colspan="6" class="td_style1">
                                    <input id="equipmentworkplace" name="tQhWfnoequipmentdesterilizeEntity.equipmentworkplace" class="easyui-validatebox" data-options="width: 150,required:true" value="${tQhWfnoequipmentdesterilize.equipmentworkplace }" />
                                </td>
                            </tr>


                            <tr align="center" >
                                <td colspan="10" width="100%">
                                    <div style="overflow-x: auto;width: 100%;padding-bottom: 15px;">
                                        <table id="buildprojectApplyItemTable" width="100%">
                                            <td style="width:50px;">序號</td>
                                            <td style="width:50px;">裝置名稱</td>
                                            <td style="width:50px;">生產廠家</td>
                                            <td style="width:50px;">型號</td>
                                            <td style="width:50px;">出廠日期</td>
                                            <td style="width:50px;">用途</td>
                                            <td style="width:50px;">最大管電壓</td>
                                            <td style="width:50px;">最大管電流</td>
                                            <td style="width:50px;">射線種類</td>
                                            <td style="width:50px;">報停日期</td>
                                            <td style="width:50px;">恢復使用日期</td>
                                            <td style="width:50px;">恢復使用原因</td>
                                            <td style="width:50px;">輻射安全責任人</td>
                                            <td style="width:50px;">聯系方式</td>

                                            </tr>
                                            <c:forEach items="${itemEntity}" varStatus="i" var="item">
                                                <tr align="center">
                                                    <td>${i.index}</td>
                                                    <td style="width:50px;">${item.equipmentname}</td>
                                                    <td style="width:50px;">${item.manufacturer}</td>
                                                    <td style="width:50px;">${item.model}</td>
                                                    <td style="width:50px;"><fmt:formatDate value="${item.dateofproduction}"/></td>
                                                    <td style="width:50px;">${item.use}</td>
                                                    <td style="width:50px;">${item.maximumtubevoltage}</td>
                                                    <td style="width:50px;">${item.maximumcurrent}</td>
                                                    <td style="width:50px;">${item.rayspecies}</td>
                                                    <td style="width:50px;"><fmt:formatDate value="${item.stopdate}"/></td>
                                                    <td style="width:50px;"><fmt:formatDate value="${item.reusedate}"/></td>
                                                    <td style="width:50px;">${item.reusereason}</td>
                                                    <td style="width:50px;">${item.personofsafe}</td>
                                                    <td style="width:50px;">${item.telephoneofsafe}</td>

                                                </tr>
                                            </c:forEach>
                                        </table>
                                    </div>
                                </td>
                            <tr>
                            </tr>

                            <tr align="center">
                                <td colspan="2" >附件（補充說明）</td>
                                <td colspan="8" class="td_style1">
                                    <div id="dowloadUrl1">
                                        <c:forEach items="${reFile}" varStatus="i" var="item">
                                            <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                                <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                            </div>
                                        </c:forEach>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                    <a href="javascript:void(0)" onclick="showWfImag('${processId}','系統上線申請流程圖','');">點擊查看簽核流程圖</a>
                                </th>
                            </tr>
                            <tr>
                                <td colspan="10" style="text-align:left;">
                                    ${chargeNodeInfo}
                                </td>
                            </tr>

                            <tr>
                                <td colspan="10" style="text-align:left;">
                                    <iframe id="qianheLogFrame" name="qianheLogFrame"
                                            src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhWfnoequipmentdesterilize.serialno}"
                                            width="100%"></iframe>
                                </td>
                            </tr>
                            <tr class="no-print">
                                <td colspan="10" style="text-align:center;padding-left:10px;">
                                    <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-cancel'"
                                       style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                    <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>

                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</form>
<div id="dlg"></div>
</body>
</html>