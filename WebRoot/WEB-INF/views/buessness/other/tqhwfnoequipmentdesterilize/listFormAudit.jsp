<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
	<title>報停射線裝置恢復使用申請單審核</title>
	<script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
	</script>
	<%@ include file="/WEB-INF/views/include/easyui.jsp" %>
	<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
	<script src='${ctx}/static/js/other/tqhwfnoequipmentdesterilize.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfnoequipmentdesterilize/${action}" method="post">
	<input id="ids" name="ids" type="hidden" value="${tQhWfnoequipmentdesterilize.id }"/>
	<div class="commonW">
		<div class="headTitle">報停射線裝置恢復使用申請單審核</div>
		<div class="position_L">
			任務編碼：<span id="" style="color:#999;">${tQhWfnoequipmentdesterilize.serialno}</span>
		</div>
		<div class="position_L1 margin_L">
			填單時間：<span style="color:#999;"><fmt:formatDate value="${tQhWfnoequipmentdesterilize.createtime}" pattern="yyyy-MM-dd HH:mm:ss"/></span>
		</div>

		<div class="position_R margin_R">
			填單人：${tQhWfnoequipmentdesterilize.makerno}/${tQhWfnoequipmentdesterilize.makername}</div>
		<br>
		<div class="clear"></div>
		<table class="formList">
			<tr>
				<td>
					<table class="formList">
						<tr>
							<td colspan="10" class="td_style1">承辦人詳細信息</td>
						</tr>
						<tr align="center">
							<td width="6%">承辦人工號</td>
							<td width="6%" class="td_style1">
								<input id="dealno"  class="easyui-textbox inputCss" readonly="true"
									   value="${tQhWfnoequipmentdesterilize.dealno }"/>
							</td>
							<td width="6%">承辦人</td>
							<td width="6%" class="td_style1">
								<input id="dealname" class="easyui-textbox inputCss" readonly="true"
									   data-options="width:80" value="${tQhWfnoequipmentdesterilize.dealname }"/>
							</td>
							<td width="6%">單位代碼</td>
							<td width="6%" class="td_style1">
								<input id="dealdeptno" class="easyui-textbox inputCss" readonly="true"
									   value="${tQhWfnoequipmentdesterilize.dealdeptno }"/>
							</td>
							<td width="6%">提報日期</td>
							<td width="6%" class="td_style1">
								<fmt:formatDate value="${tQhWfnoequipmentdesterilize.dealtime}"/>
							</td>
							<td width="6%">廠區</td>
							<td width="6%" class="td_style1">
								<input id="dealfactoryid" class="easyui-combobox" readonly="true" disabled="disabled"
									   panelHeight="auto" value="${tQhWfnoequipmentdesterilize.dealfactoryid }"
									   data-options="width: 120"/>
							</td>
						</tr>
						<tr align="center">
							<td>單位</td>
							<td colspan="3" class="td_style1">
								<input id="dealdeptname" class="easyui-textbox inputCss" readonly="true"
									   value="${tQhWfnoequipmentdesterilize.dealdeptname }"/>
							</td>
							<td>聯繫郵箱</td>
							<td colspan="3" class="td_style1">
								<input id="dealemail" class="easyui-textbox inputCss" readonly="true"
									   value="${tQhWfnoequipmentdesterilize.dealemail }"
									   data-options="width: 300"/>

							</td>
							<td>聯繫分機</td>
							<td class="td_style1">
								<input id="dealtel" class="easyui-textbox inputCss" readonly="true"
									   style="width:80px;"
									   value="${tQhWfnoequipmentdesterilize.dealtel }"/>
							</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td>
					<table class="formList">
						<tr>
							<td colspan="10" class="td_style1">報停/報廢詳細信息</td>
						</tr>
						<tr align="center">
							<td>射線裝置使用單位</td>
							<td colspan="4" class="td_style1">
								<input id="equipmentdepartment" class="easyui-validatebox" style="width:150px;"
									   value="${tQhWfnoequipmentdesterilize.equipmentdepartment }" />
							</td>

							<td>射線裝置工作場所</td>
							<td colspan="6" class="td_style1">
								<input id="equipmentworkplace" class="easyui-validatebox" data-options="width: 150" value="${tQhWfnoequipmentdesterilize.equipmentworkplace }" />
							</td>
						</tr>
						<tr align="center" >
							<td colspan="10" width="100%">
								<div style="overflow-x: auto;width: 100%;padding-bottom: 15px;">
									<table id="buildprojectApplyItemTable" width="100%">
										<td style="width:50px;">序號</td>
										<td style="width:50px;">裝置名稱</td>
										<td style="width:50px;">生產廠家</td>
										<td style="width:50px;">型號</td>
										<td style="width:50px;">出廠日期</td>
										<td style="width:50px;">用途</td>
										<td style="width:50px;">最大管電壓</td>
										<td style="width:50px;">最大管電流</td>
										<td style="width:50px;">射線種類</td>
										<td style="width:50px;">報停日期</td>
										<td style="width:50px;">恢復使用日期</td>
										<td style="width:50px;">恢復使用原因</td>
										<td style="width:50px;">輻射安全責任人</td>
										<td style="width:50px;">聯系方式</td>

										</tr>
										<c:forEach items="${itemEntity}" varStatus="i" var="item">
											<tr align="center">
												<td>${i.index+1}</td>
												<td style="width:50px;">${item.equipmentname}</td>
												<td style="width:50px;">${item.manufacturer}</td>
												<td style="width:50px;">${item.model}</td>
												<td style="width:50px;"><fmt:formatDate value="${item.dateofproduction}"/></td>
												<td style="width:50px;">${item.use}</td>
												<td style="width:50px;">${item.maximumtubevoltage}</td>
												<td style="width:50px;">${item.maximumcurrent}</td>
												<td style="width:50px;">${item.rayspecies}</td>
												<td style="width:50px;"><fmt:formatDate value="${item.stopdate}"/></td>
												<td style="width:50px;"><fmt:formatDate value="${item.reusedate}"/></td>
												<td style="width:50px;">${item.reusereason}</td>
												<td style="width:50px;">${item.personofsafe}</td>
												<td style="width:50px;">${item.telephoneofsafe}</td>


											</tr>
										</c:forEach>
									</table>
								</div>
							</td>
						<tr>
							<td>批註</td>
							<td colspan="9">
                  				<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
										  style="width:1000px;height:60px;" rows="4" cols="4" value=""></textarea>
							</td>
						</tr>
						<c:if test="${not empty currentNodeName&&'環保科技處對應窗口' eq currentNodeName}">
							<tr>
								<td>附件&nbsp;（補充說明）</td>
								<td colspan="9" class="td_style1">
									<span class="sl-custom-file">
										<input type="button" value="点击上传文件" class="btn-file"/>
										<input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file">
									</span>
									<input type="hidden" id="attachids" name="reattachids" value="${tQhWfnoequipmentdesterilizeEntity.reattachids}"/>
									<div id="dowloadUrl"></div>
								</td>
							</tr>
						</c:if>
						<c:if test="${tQhWfnoequipmentdesterilize.reattachids!=null}">
							<tr align="center">
								<td>附件&nbsp;（補充說明）</td>
								<td colspan="9" class="td_style1">
									<div>
										<c:forEach items="${reFile}" varStatus="i" var="item">
											<div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
												<div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
											</div>
										</c:forEach>
									</div>
								</td>
							</tr>
						</c:if>
						<%--</c:if>--%>
						</tr>
						<tr align="center">
							<td colspan="10" style="border:none;text-align:center;margin-top:10px">
								<c:choose>
									<c:when test="${not empty nodeName && '環保科技處對應窗口' eq nodeName}">
										<fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" perCall="audiPrValid" serialNo="${tQhWfnoequipmentdesterilize.serialno}"></fox:action>
									</c:when>
									<c:otherwise>
										<fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" serialNo="${tQhWfnoequipmentdesterilize.serialno}"></fox:action>
									</c:otherwise>
								</c:choose>

							</td>
						</tr>
						<tr>
							<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
								<a href="javascript:void(0)"
								   onclick="showWfImag('${processId}','報停射線裝置恢復使用申請單');">點擊查看簽核流程圖</a>
							</th>
						</tr>
						<tr>
							<td colspan="10" style="text-align:left;">
								${chargeNodeInfo}
							</td>
						</tr>

						<tr>
							<td colspan="10" style="text-align:left;">
								<iframe id="qianheLogFrame" name="qianheLogFrame"
										src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhWfnoequipmentdesterilize.serialno}"
										width="100%"></iframe>
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
	</div>
</form>
<input type="hidden" id="currentNodeOrder" value="${nodeOrder}" />
<div id="dlg"></div>
</body>
</html>

