<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>報停射線裝置恢復使用申請表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp"%>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/other/tqhwfnoequipmentdesterilize.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfnoequipmentdesterilize/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhWfnoequipmentdesterilize.id }"/>
    <input id="serialno" name="tQhWfnoequipmentdesterilizeEntity.serialno" type="hidden"
           value="${tQhWfnoequipmentdesterilize.serialno }"/>
    <input id="createtime" name="tQhWfnoequipmentdesterilizeEntity.createtime" type="hidden"
           value="${tQhWfnoequipmentdesterilize.createtime }"/>
    <input id="makerno" name="tQhWfnoequipmentdesterilize.makerno" type="hidden" value="${tQhWfnoequipmentdesterilize.makerno }"/>
    <input id="makername" name="tQhWfnoequipmentdesterilize.makername" type="hidden" value="${tQhWfnoequipmentdesterilize.makername }"/>
    <input id="makerdeptno" name="tQhWfnoequipmentdesterilize.makerdeptno" type="hidden" value="${tQhWfnoequipmentdesterilize.makerdeptno }"/>
    <input id="makerfactoryid" name="tQhWfnoequipmentdesterilize.makerfactoryid" type="hidden" value="${tQhWfnoequipmentdesterilize.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">報停射線裝置恢復使用申請表</div>
        <div class="position_L">
            任務編碼：<span id="" style="color:#999;">
             <c:choose>
                 <c:when test="${tQhWfnoequipmentdesterilize.serialno==null}">
                     提交成功后自動編碼
                 </c:when>
                 <c:otherwise>
                     ${tQhWfnoequipmentdesterilize.serialno}
                 </c:otherwise>
             </c:choose>
        </span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
            <c:choose>
                <c:when test="${tQhWfnoequipmentdesterilize.createtime==null}">
                    YYYY/MM/DD
                </c:when>
                <c:otherwise>
                    ${tQhWfnoequipmentdesterilize.createtime}
                </c:otherwise>
            </c:choose>
        </span>
        </div>
        <c:if test="${empty tQhWfnoequipmentdesterilize.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty tQhWfnoequipmentdesterilize.makerno}">
            <div class="position_R margin_R">填單人：${tQhWfnoequipmentdesterilize.makerno}/${tQhWfnoequipmentdesterilize.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList" id="buildprojecttable">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealno" name="tQhWfnoequipmentdesterilizeEntity.dealno"  class="easyui-validatebox" data-options="width: 80,required:true"
                                       value="${tQhWfnoequipmentdesterilize.dealno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="6%">承辦人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="tQhWfnoequipmentdesterilizeEntity.dealname" class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${tQhWfnoequipmentdesterilize.dealname }"/>
                            </td>
                            <td width="6%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="tQhWfnoequipmentdesterilizeEntity.dealdeptno" class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${tQhWfnoequipmentdesterilize.dealdeptno }" />
                            </td>
                            <td width="6%">提報日期&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealtime" name="tQhWfnoequipmentdesterilizeEntity.dealtime" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width: 100,required:true" value="<fmt:formatDate value="${tQhWfnoequipmentdesterilize.dealtime}"/>" />
                            </td>
                            <td width="6%">廠區&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealfactoryid" name="tQhWfnoequipmentdesterilizeEntity.dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${tQhWfnoequipmentdesterilize.dealfactoryid }"
                                       data-options="width: 80,required:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="3" class="td_style1">
                                <input id="dealdeptname" name="tQhWfnoequipmentdesterilizeEntity.dealdeptname" class="easyui-validatebox" data-options="width: 380"
                                 value="${tQhWfnoequipmentdesterilize.dealdeptname }" />
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="tQhWfnoequipmentdesterilizeEntity.dealemail" class="easyui-validatebox" value="${tQhWfnoequipmentdesterilize.dealemail }" style="width:300px;"
                                       data-options="required:true" onblur="valdEmail(this)"/>
                            </td>
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealtel" name="tQhWfnoequipmentdesterilizeEntity.dealtel" class="easyui-validatebox" style="width:90px;"
                                       value="${tQhWfnoequipmentdesterilize.dealtel }" data-options="required:true,prompt:'579+66666'"  onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">報停/報廢詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td>射線裝置使用單位&nbsp;<font color="red">*</font></td>
                            <td colspan="4" class="td_style1">
                                <input id="equipmentdepartment" name="tQhWfnoequipmentdesterilizeEntity.equipmentdepartment" class="easyui-validatebox" style="width:150px;"
                                       data-options="required:true" value="${tQhWfnoequipmentdesterilize.equipmentdepartment }" />
                            </td>

                            <td>射線裝置工作場所&nbsp;<font color="red">*</font></td>
                            <td colspan="6" class="td_style1">
                                <input id="equipmentworkplace" name="tQhWfnoequipmentdesterilizeEntity.equipmentworkplace" class="easyui-validatebox" data-options="width: 150,required:true" value="${tQhWfnoequipmentdesterilize.equipmentworkplace }" />
                            </td>
                        </tr>

                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x:auto;width: 1200px">
                                    <table id="buildprojectApplyItemTable" width="100%">
                                        <thead>
                                        <tr>
                                            <th>序號</th>
                                            <th>裝置名稱<font color="red">*</font></th>
                                            <th>生產廠家<font color="red">*</font></th>
                                            <th>型號<font color="red">*</font></th>
                                            <th>出廠日期<font color="red">*</font></th>
                                            <th>用途<font color="red">*</font></th>
                                            <th>最大管電壓<font color="red">*</font></th>
                                            <th>最大管電流<font color="red">*</font></th>
                                            <th>射線種類<font color="red">*</font></th>
                                            <th>報停日期<font color="red">*</font></th>
                                            <th>恢復使用日期<font color="red">*</font></th>
                                            <th>恢復使用原因<font color="red">*</font></th>
                                            <th>輻射安全責任人<font color="red">*</font></th>
                                            <th>聯繫方式<font color="red">*</font></th>
                                            <th nowrap="nowrap">操作</th>

                                        </tr>
                                        </thead>

                                        <tbody>
                                        <c:if test="${itemEntity!=null&&itemEntity.size()>0}">
                                            <c:forEach items="${itemEntity}" var="item" varStatus="status">
                                                <tr align="center" id="1" >
                                                    <td>${status.index+1}</td>
                                                    <td><input id="equipmentname${status.index}" name="tQhWfnoequipmentEntity[${status.index}].equipmentname" class="easyui-validatebox" data-options="required:true" style="width:150px;" value="${item.equipmentname}"/></td>
                                                    <td><input id="manufacturer${status.index}" name="tQhWfnoequipmentEntity[${status.index}].manufacturer" class="easyui-validatebox" data-options="required:true" style="width:150px;" value="${item.manufacturer}"/></td>
                                                    <td><input id="model${status.index}" name="tQhWfnoequipmentEntity[${status.index}].model" class="easyui-validatebox" data-options="required:true" style="width:150px;" value="${item.model}"/></td>
                                                    <td><input id="dateofproduction${status.index}" name="tQhWfnoequipmentEntity[${status.index}].dateofproduction" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width: 100,required:true"   value="<fmt:formatDate value="${item.dateofproduction}"/>" /></td>
                                                        <%--<td><input id="dateofproduction${status.index}" name="tQhWfnoequipmentEntity[${status.index}].dateofproduction" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width: 100,required:true"   value="${item.dateofproduction}" /></td>--%>
                                                    <td><input id="use${status.index}" name="tQhWfnoequipmentEntity[${status.index}].use" class="easyui-validatebox" data-options="required:true" style="width:150px;" value="${item.use}"/></td>
                                                    <td><input id="maximumtubevoltage${status.index}" name="tQhWfnoequipmentEntity[${status.index}].maximumtubevoltage" class="easyui-validatebox" data-options="required:true" style="width:150px;" value="${item.maximumtubevoltage}"/></td>
                                                    <td><input id="maximumcurrent${status.index}" name="tQhWfnoequipmentEntity[${status.index}].maximumcurrent" class="easyui-validatebox" data-options="required:true" style="width:150px;" value="${item.maximumcurrent}"/></td>
                                                    <td><input id="rayspecies${status.index}" name="tQhWfnoequipmentEntity[${status.index}].rayspecies" class="easyui-validatebox" data-options="required:true" style="width:150px;" value="${item.rayspecies}"/></td>
                                                    <td><input id="stopdate${status.index}" name="tQhWfnoequipmentEntity[${status.index}].stopdate" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width: 100,required:true" value="<fmt:formatDate value="${item.stopdate}"/>" /></td>
                                                    <td><input id="reusedate${status.index}" name="tQhWfnoequipmentEntity[${status.index}].reusedate" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width: 100,required:true" value="<fmt:formatDate value="${item.reusedate}"/>" /></td>

                                                    <td><input id="reusereason${status.index}" name="tQhWfnoequipmentEntity[${status.index}].reusereason" class="easyui-validatebox" data-options="required:true" style="width:150px;" value="${item.reusereason}"/></td>
                                                    <td><input id="personofsafe${status.index}" name="tQhWfnoequipmentEntity[${status.index}].personofsafe" class="easyui-validatebox" data-options="required:true" style="width:150px;" value="${item.personofsafe}"/></td>
                                                    <td><input id="telephoneofsafe${status.index}" name="tQhWfnoequipmentEntity[${status.index}].telephoneofsafe" class="easyui-validatebox" data-options="required:true" style="width:150px;" value="${item.telephoneofsafe}" onblur="checkMobile(this)"/></td>

                                                    <td><input type="image" src="${ctx}/static/images/deleteRow.png" onclick="deltr(1);return false;"/></td>

                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${itemEntity==null}">
                                            <tr align="center" id="1" >
                                                <td>1</td>
                                                <td><input id="equipmentname1" name="tQhWfnoequipmentEntity[0].equipmentname" class="easyui-validatebox" data-options="required:true" style="width:150px;" value=""/></td>
                                                <td><input id="manufacturer1" name="tQhWfnoequipmentEntity[0].manufacturer" class="easyui-validatebox" data-options="required:true" style="width:150px;" value=""/></td>
                                                <td><input id="model1" name="tQhWfnoequipmentEntity[0].model" class="easyui-validatebox" data-options="required:true" style="width:150px;" value=""/></td>
                                                <td><input id="dateofproduction1" name="tQhWfnoequipmentEntity[0].dateofproduction" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width: 100,required:true" value="" /></td>
                                                <td><input id="use1" name="tQhWfnoequipmentEntity[0].use" class="easyui-validatebox" data-options="required:true" style="width:150px;" value=""/></td>
                                                <td><input id="maximumtubevoltage1" name="tQhWfnoequipmentEntity[0].maximumtubevoltage" class="easyui-validatebox" data-options="required:true" style="width:150px;" value=""/></td>
                                                <td><input id="maximumcurrent1" name="tQhWfnoequipmentEntity[0].maximumcurrent" class="easyui-validatebox" data-options="required:true" style="width:150px;;" value=""/></td>
                                                <td><input id="rayspecies1" name="tQhWfnoequipmentEntity[0].rayspecies" class="easyui-validatebox" data-options="required:true" style="width:150px;;" value=""/></td>
                                                <td><input id="stopdate1" name="tQhWfnoequipmentEntity[0].stopdate" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width: 100,required:true" value=""/></td>
                                                <td><input id="reusedate1" name="tQhWfnoequipmentEntity[0].reusedate" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width: 100,required:true" value=""/></td>
                                                <td><input id="reusereason1" name="tQhWfnoequipmentEntity[0].reusereason" class="easyui-validatebox" data-options="required:true" style="width:150px;;" value=""/></td>
                                                <td><input id="personofsafe1" name="tQhWfnoequipmentEntity[0].personofsafe" class="easyui-validatebox" data-options="required:true" style="width:150px;" value=""/></td>
                                                <td><input id="telephoneofsafe1" name="tQhWfnoequipmentEntity[0].telephoneofsafe" class="easyui-validatebox" data-options="required:true" style="width:150px;" value="" onblur="checkMobile(this)"/></td>
                                                <td><input type="image" src="${ctx}/static/images/deleteRow.png" onclick="deltr(1);return false;"/></td>

                                            </tr>
                                        </c:if>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="left">
                            <td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
                                <input type="button" id="add" style="width:100px;float:left;" value="添加一筆" />
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2" >導入樣式&nbsp; <a href="#" plain="true"  id="btnBatchImportTpl">參考.xls</a>
                            </td>
                            <td colspan="8" class="td_style1">
                                <a href="#" id="batchImport" class="easyui-linkbutton" data-options="iconCls:'icon-hamburg-cv'" style="width: 100px;" onclick="openBatchImportWin();">批量導入</a>

                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)" onclick="showWfImag('${workFlowId}','系統上線申請流程圖','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="tQhWfnoequipmentdesterilizeEntity.kchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['kchargeno']}" readonly
                                                               value="${tQhWfnoequipmentdesterilize.kchargeno }"/><c:if test="${requiredMap['kchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="kchargename" name="tQhWfnoequipmentdesterilizeEntity.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${tQhWfnoequipmentdesterilize.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="bchargeno" name="tQhWfnoequipmentdesterilizeEntity.bchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${tQhWfnoequipmentdesterilize.bchargeno }"/><c:if test="${requiredMap['bchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="bchargename" name="tQhWfnoequipmentdesterilizeEntity.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${tQhWfnoequipmentdesterilize.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="cchargeno"
                                                               name="tQhWfnoequipmentdesterilizeEntity.cchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['cchargeno']}" readonly
                                                               value="${tQhWfnoequipmentdesterilize.cchargeno }"/><c:if test="${requiredMap['cchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="cchargename"
                                                                name="tQhWfnoequipmentdesterilizeEntity.cchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['cchargeno']}"
                                                                value="${tQhWfnoequipmentdesterilize.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"  id="hchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽
                                                                    <a href="javascript:addHq('hcharge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hchargeno" onblur="getUserNameByEmpno(this,'hcharge');" name="tQhWfnoequipmentdesterilizeEntity.hchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hchargeno']}" value="${tQhWfnoequipmentdesterilize.hchargeno }"/>
                                                        <c:if test="${requiredMap['hchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hchargename" name="tQhWfnoequipmentdesterilizeEntity.hchargename" readonly
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hchargeno']}"  value="${tQhWfnoequipmentdesterilize.hchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfnoequipmentdesterilizeEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zchargeno"
                                                               name="tQhWfnoequipmentdesterilizeEntity.zchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zchargeno']}" readonly
                                                               value="${tQhWfnoequipmentdesterilize.zchargeno }"/><c:if test="${requiredMap['zchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="zchargename"
                                                                name="tQhWfnoequipmentdesterilizeEntity.zchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zchargeno']}" readonly
                                                                value="${tQhWfnoequipmentdesterilize.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfnoequipmentdesterilizeEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zcchargeno"
                                                               name="tQhWfnoequipmentdesterilizeEntity.zcchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zcchargeno']}" readonly
                                                               value="${tQhWfnoequipmentdesterilize.zcchargeno }"/><c:if test="${requiredMap['zcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="zcchargename"
                                                                name="tQhWfnoequipmentdesterilizeEntity.zcchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zcchargeno']}" readonly
                                                                value="${tQhWfnoequipmentdesterilize.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處對應窗口</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(3,'hbchargeno','hbchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbchargeno"
                                                               name="tQhWfnoequipmentdesterilizeEntity.hbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbchargeno']}" readonly
                                                               value="${tQhWfnoequipmentdesterilize.hbchargeno }"/><c:if test="${requiredMap['hbchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hbchargename" name="tQhWfnoequipmentdesterilizeEntity.hbchargename" readonly
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbchargeno']}" value="${tQhWfnoequipmentdesterilize.hbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hbkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(4,'hbkchargeTable','hbkchargeno','hbkchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfnoequipmentdesterilizeEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbkchargeno"
                                                               name="tQhWfnoequipmentdesterilizeEntity.hbkchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbkchargeno']}" readonly
                                                               value="${tQhWfnoequipmentdesterilize.hbkchargeno }"/><c:if test="${requiredMap['hbkchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hbkchargename" name="tQhWfnoequipmentdesterilizeEntity.hbkchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbkchargeno']}" readonly
                                                                value="${tQhWfnoequipmentdesterilize.hbkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hbbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(5,'hbbchargeTable','hbbchargeno','hbbchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfnoequipmentdesterilizeEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbbchargeno"
                                                               name="tQhWfnoequipmentdesterilizeEntity.hbbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbbchargeno']}" readonly
                                                               value="${tQhWfnoequipmentdesterilize.hbbchargeno }"/><c:if test="${requiredMap['hbbchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="hbbchargename"name="tQhWfnoequipmentdesterilizeEntity.hbbchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbbchargeno']}" readonly
                                                               value="${tQhWfnoequipmentdesterilize.hbbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hbcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(6,'hbcchargeTable','hbcchargeno','hbcchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfnoequipmentdesterilizeEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbcchargeno"
                                                               name="tQhWfnoequipmentdesterilizeEntity.hbcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbcchargeno']}" readonly
                                                               value="${tQhWfnoequipmentdesterilize.hbcchargeno }"/><c:if test="${requiredMap['hbcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="hbcchargename" name="tQhWfnoequipmentdesterilizeEntity.hbcchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbcchargeno']}" readonly
                                                               value="${tQhWfnoequipmentdesterilize.hbcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>


                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td  colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave"  class="easyui-linkbutton" data-options="iconCls:'icon-add'"  style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit"   class="easyui-linkbutton" data-options="iconCls:'icon-ok'" style="width: 100px;" onclick="saveInfo(2);">提交</a>
                                <%--<a href="#" id="btnadd" class="easyui-linkbutton" iconCls="icon-add" plain="true" code="add">提交</a>--%>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
<div id="optionWin" class="easyui-window" title="報停恢復信息批量導入" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%" >
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="batchFile" type="file" style="width: 300px"  accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>

                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <span ID="labelListAddResult" ></span><a href="/tqhwfnoequipmentdesterilize/downLoad/errorExcel" id="downloadError" plain="true" >查看錯誤信息</a>
                </td>
            </tr>
        </table>
    </form>
</div>

<script type="text/javascript">
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            successTip(data, dg, d);
        }
    });
    if ("${tQhWfnoequipmentdesterilize.hchargeno}" != "") {
        var nostr = "${tQhWfnoequipmentdesterilize.hchargeno}";
        var namestr = "${tQhWfnoequipmentdesterilize.hchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hchargeTable tr:eq(" + (i + 2) + ")").find("#hchargeno").val(notr[i]);
            $("#hchargeTable tr:eq(" + (i + 2) + ")").find("#hchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hchargeno' name='tQhWfnoequipmentdesterilizeEntity.hchargeno' style='width: 80px' value='' onblur='getUserNameByEmpno(this,\"hcharge\");'/>/<input id='hchargename' name='tQhWfnoequipmentdesterilizeEntity.hchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
</script>
</body>
</html>