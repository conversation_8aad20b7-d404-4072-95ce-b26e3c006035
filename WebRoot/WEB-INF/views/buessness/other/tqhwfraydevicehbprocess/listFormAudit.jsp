<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>射線裝置環保手續辦理委託申請單-審核頁面</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/other/tqhwfraydevicehbprocess.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfraydevicehbprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhWfraydevicehbprocess.id }"/>
    <div class="commonW">
        <div class="headTitle">射線裝置環保手續辦理委託申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">${tQhWfraydevicehbprocess.serialno}</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
            <input class="inputCss" style="width: 100px" value="<fmt:formatDate value='${tQhWfraydevicehbprocess.createtime}' pattern='yyyy-MM-dd hh:mm'/>" >
        </span>
        </div>
        <div class="position_R margin_R"> 填單人：${tQhWfraydevicehbprocess.makerno}/${tQhWfraydevicehbprocess.makername}</div>
        <br>
        <div class="clear"></div>
        <table class="formList" id="buildprojecttable">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">承辦人工號&nbsp;</td>
                            <td width="6%" class="td_style1">
                                <input id="dealno" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${tQhWfraydevicehbprocess.dealno }" readonly/>
                            </td>
                            <td width="6%">承辦人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" class="easyui-validatebox inputCss"
                                       data-options="width:80" value="${tQhWfraydevicehbprocess.dealname }" readonly/>
                            </td>
                            <td width="6%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" class="easyui-validatebox inputCss" data-options="width: 90"
                                       value="${tQhWfraydevicehbprocess.dealdeptno }" readonly/>
                            </td>
                            <td width="6%">提報日期&nbsp;</td>
                            <td width="6%" class="td_style1">
                                <input id="dealtime" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                       data-options="width: 100"
                                       value="<fmt:formatDate value="${tQhWfraydevicehbprocess.dealtime}"/>" disabled/>
                            </td>
                            <td width="6%">廠區&nbsp;</td>
                            <td width="6%" class="td_style1">
                                <input id="dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${tQhWfraydevicehbprocess.dealfactoryid }" disabled
                                       data-options="width: 120"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="3" class="td_style1">
                                <input id="dealdeptname"
                                       class="easyui-validatebox inputCss" data-options="width: 400"
                                       value="${tQhWfraydevicehbprocess.dealdeptname }" readonly/>
                            </td>
                            <td>聯繫郵箱&nbsp;</td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" class="easyui-validatebox inputCss"
                                       value="${tQhWfraydevicehbprocess.dealemail }" style="width:300px;" readonly/>
                            </td>
                            <td>聯繫分機&nbsp;</td>
                            <td class="td_style1">
                                <input id="dealtel" class="easyui-validatebox inputCss"
                                       style="width:90px;" value="${tQhWfraydevicehbprocess.dealtel }" readonly/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">項目基本信息</td>
                        </tr>

                        <tr align="center">
                            <td colspan="3">法人名稱&nbsp;</td>
                            <td colspan="7" class="td_style1">
                                <input id="corporateid" data-options="width: 300"
                                       class="easyui-combobox" panelHeight="auto" editable="false" value="${tQhWfraydevicehbprocess.corporateid }" disabled/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="3">是否具有輻射安全許可證&nbsp;</td>
                            <td colspan="2">
                                <div class="ishavepermitDiv"></div>
                                <input id="ishavepermit" type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${tQhWfraydevicehbprocess.ishavepermit }"/>
                                <input id="disOrEnabled" type="hidden" value="disabled"/>

                            </td>
                            <td colspan="3">建設性質&nbsp;</td>
                            <td colspan="2">
                                <div class="buildpropertiesDiv"></div>
                                <input id="buildproperties" type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${tQhWfraydevicehbprocess.buildproperties }" />
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 100%;padding-bottom: 15px;">
                                    <table id="rayDevicehbCostItemTable" width="100%">
                                        <tr align="center">
                                            <td>序號</td>
                                            <td>費用掛靠單位</td>
                                            <td>費用代碼</td>
                                            <td>費用佔比(%)</td>
                                        </tr>
                                        <c:if test="${costItemEntity!=null&&costItemEntity.size()>0}">
                                            <c:forEach items="${costItemEntity}" var="costItem" varStatus="status">
                                                <tr align="center" id="costItem${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>${costItem.costname}</td>
                                                    <td>${costItem.costno}</td>
                                                    <td>${costItem.costrate}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 100%;padding-bottom: 15px;">
                                    <table id="rayDevicehbItemTable" width="100%">
                                        <tr align="center">
                                            <td>序號</td>
                                            <td>射線裝置<br>名稱</td>
                                            <td>射線裝置<br>型號</td>
                                            <td>射線裝置<br>數量</td>
                                            <td>工作場所名稱<br>及周邊環境描述</td>
                                            <td>占地面積（㎡）</td>
                                            <td>項目投資<br>（萬元）</td>
                                            <td>環保投資<br>（萬元）</td>
                                            <td>設備來源</td>
                                            <td>設備類別</td>
                                            <td>用途</td>
                                            <td>備註</td>
                                        </tr>
                                        <c:if test="${deviceItemEntity!=null&&deviceItemEntity.size()>0}">
                                            <c:forEach items="${deviceItemEntity}" var="deviceItem" varStatus="status">
                                                <tr align="center" id="deviceItem${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>${deviceItem.devicename}</td>
                                                    <td>${deviceItem.devicetype}</td>
                                                    <td>${deviceItem.devicenumber}</td>
                                                    <td>${deviceItem.nameanddescription}</td>
                                                    <td>${deviceItem.floorspace}</td>
                                                    <td>${deviceItem.projectcost}</td>
                                                    <td>${deviceItem.enviromentalcost}</td>
                                                    <td>${deviceItem.equipmentsource}</td>
                                                    <td>${deviceItem.equipmenttype}</td>
                                                    <td>${deviceItem.deviceuse}</td>
                                                    <td>${deviceItem.remark}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">附件</td>
                            <td colspan="8" class="td_style1">
                                <div>
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">委託事項說明&nbsp;</td>
                            <td colspan="8" class="td_style1">
                                <textarea id="entrustexplain" class="easyui-validatebox inputCss" style="width:800px;height:80px;"
                              rows="5" cols="6" readonly>${tQhWfraydevicehbprocess.entrustexplain }</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">批註</td>
                            <td colspan="8" class="td_style1">
                                <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                          style="width:800px;height:60px;" rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                        <c:if test="${not empty nodeName&&'環保管理處(部)' eq nodeName}">
                            <tr align="center">
                                <td colspan="2">附件&nbsp;（補充說明）</td>
                                <td colspan="8" class="td_style1">
                                    <span class="sl-custom-file">
						                <input type="button" value="点击上传文件" class="btn-file"/>
						                <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file">
						            </span>
                                    <input type="hidden" id="attachids" name="reattachids" value="${tQhWfraydevicehbprocess.reattachids}"/>
                                    <div id="dowloadUrl"></div>
                                </td>
                            </tr>
                        </c:if>
                        <c:if test="${tQhWfraydevicehbprocess.reattachids!=null}">
                            <tr align="center">
                                <td colspan="2">附件&nbsp;（補充說明）</td>
                                <td colspan="8" class="td_style1">
                                    <div>
                                        <c:forEach items="${reFile}" varStatus="i" var="item">
                                            <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                                <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                            </div>
                                        </c:forEach>
                                    </div>
                                </td>
                            </tr>
                        </c:if>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <c:choose>
                                    <c:when test="${not empty nodeName && '環保管理處(部)' eq nodeName}">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" perCall="audiPrValid" serialNo="${tQhWfraydevicehbprocess.serialno}"></fox:action>
                                    </c:when>
                                    <c:otherwise>
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" serialNo="${tQhWfraydevicehbprocess.serialno}"></fox:action>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)" onclick="showWfImag('${processId}','射線裝置環保手續辦理委託申請流程圖','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhWfraydevicehbprocess.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
<input type="hidden" id="currentNodeOrder" value="${nodeOrder}" />
<div id="dlg"></div>
</body>
</html>