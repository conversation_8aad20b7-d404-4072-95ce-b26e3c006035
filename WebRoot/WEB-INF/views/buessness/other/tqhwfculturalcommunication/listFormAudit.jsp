<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>文化傳媒中心服務申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
            type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfculturalcommunication/${action}"
      method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhCulturalCommunicationEntity.id}"/>
    <input id="serialno" name="serialno" type="hidden" value="${tQhCulturalCommunicationEntity.serialno}"/>
    <div class="commonW">
        <div class="headTitle">文化傳媒中心服務申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;"> <c:choose>
            <c:when test="${tQhCulturalCommunicationEntity.serialno==null}">
                提交成功后自動編碼
            </c:when>
            <c:otherwise>
                ${tQhCulturalCommunicationEntity.serialno}
            </c:otherwise>
        </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;"> <c:choose>
            <c:when test="${tQhCulturalCommunicationEntity.createtime==null}">
                YYYY/MM/DD
            </c:when>
            <c:otherwise>
							<input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${tQhCulturalCommunicationEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
            </c:otherwise>
        </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${tQhCulturalCommunicationEntity.makerno}/${tQhCulturalCommunicationEntity.makerno}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td>承辦人工號</td>
                            <td><input id="dealno" name="dealno"
                                       class="easyui-validatebox" readonly
                                       data-options="width: 150,required:true"
                                       value="${tQhCulturalCommunicationEntity.dealno }"/></td>
                            <td>承辦人</td>
                            <td><input id="dealname" name="dealname" readonly
                                       class="easyui-validatebox inputCss" data-options="width: 150"
                                       value="${tQhCulturalCommunicationEntity.dealname }"/></td>
                            <td>單位代碼</td>
                            <td><input id="dealdeptno" name="dealdeptno" readonly
                                       class="easyui-validatebox inputCss" data-options="width: 150"
                                       value="${tQhCulturalCommunicationEntity.dealdeptno }"/></td>
                            <td>廠區</td>
                            <td><input id="dealfactoryid" name="dealfactoryid"
                                       class="easyui-combobox" disabled
                                       data-options="width: 150,required:true"
                                       value="${tQhCulturalCommunicationEntity.dealfactoryid }"/>
                            </td>
                            <td>申請日期</td>
                            <td><input id="dealtime" name="dealtime" class="Wdate" disabled
                                       data-options="width:180,required:true" style="width:180px"
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${tQhCulturalCommunicationEntity.dealtime}"/>"
                                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位名稱</td>
                            <td colspan="3"><input id="dealdeptname"
                                                   style="width:360px" name="dealdeptname"
                                                   class="easyui-validatebox inputCss" readonly
                                                   data-options="width: 360,required:true"
                                                   value="${tQhCulturalCommunicationEntity.dealdeptname }"/></td>
                            <td>聯繫方式</td>
                            <td><input id="dealtel" name="dealtel"
                                       readonly class="easyui-validatebox inputCss"
                                       data-options="width: 150,required:true"
                                       value="${tQhCulturalCommunicationEntity.dealtel }"/></td>
                            <td>聯繫郵箱</td>
                            <td colspan="3"><input id="dealemail" name="dealemail"
                                                   readonly class="easyui-validatebox inputCss"
                                                   data-options="width: 250,required:true"
                                                   value="${tQhCulturalCommunicationEntity.dealemail }"/></td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" class="td_style1">申請項目</td>
                        </tr>
                        <tr>
                            <td>項目名稱</td>
                            <td colspan="5"><input id="projecname" style="width:360px"
                                                   name="projecname" class="easyui-validatebox inputCss" readonly
                                                   data-options="width: 360,required:true"
                                                   value="${tQhCulturalCommunicationEntity.projecname }"/></td>
                            <td>需求時間</td>
                            <td colspan="3"><input id="demandStartTime"
                                                   name="demandStartTime" class="Wdate" disabled
                                                   data-options="width:180,required:true" style="width:180px"
                                                   value="<fmt:formatDate  pattern="yyyy-MM-dd  HH:mm:ss"
									value="${tQhCulturalCommunicationEntity.demandStartTime}"/>"
                                                   onclick="WdatePicker({dateFmt:'yyyy-MM-dd  HH:mm:ss',minDate:'%y-%M-%d'})"/>
                                ~<input id="demandEndTime" name="demandEndTime" class="Wdate" disabled
                                        data-options="width:180,required:true" style="width:180px"
                                        value="<fmt:formatDate  pattern="yyyy-MM-dd HH:mm:ss"
									value="${tQhCulturalCommunicationEntity.demandEndTime}"/>"
                                        onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'%y-%M-%d'})"/>
                            </td>
                        </tr>
                        <tr>
                            <td>交付格式</td>
                            <td colspan="5">
                                <div class="deliveryFormatDiv"></div>
                                <input
                                        id="deliveryFormat" name="deliveryFormat"
                                        class="easyui-validatebox" data-options="width: 150"
                                        type="hidden"
                                        value="${tQhCulturalCommunicationEntity.deliveryFormat }"/> <input
                                    id="deliveryFormatQita1" name="deliveryFormatQita1"
                                    disabled="disabled" type="hidden" class="easyui-validatebox"
                                    data-options="width: 30"
                                    value="${tQhCulturalCommunicationEntity.deliveryFormatQita}"/>
                                <input id="shifou" value="return false" type="hidden"/>
                            </td>
                            <td>作業地點</td>
                            <td colspan="3"><input id="jobLocation" style="width:360px"
                                                   name="jobLocation" class="easyui-validatebox inputCss"
                                                   data-options="width: 360,required:true" readonly
                                                   value="${tQhCulturalCommunicationEntity.jobLocation }"/></td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="2" class="td_style1" style="text-align:center;width:20%">申請類型</td>
                            <td colspan="8" class="td_style1" style="text-align:center;width:79%">明細內容</td>
                        </tr>
                        <tr id="filmProductionTR" align="center">
                            <td colspan="2">影片製作</td>
                            <td colspan="8" align="left"><input id="filmProduction"
                                                                name="filmProduction" data-options="width: 150"
                                                                type="hidden"
                                                                value="${tQhCulturalCommunicationEntity.filmProduction }"/>
                                <div class="filmProductionDiv"></div>
                            </td>
                        </tr>
                        <tr id="photographyTR" align="center">
                            <td colspan="2">攝影攝像</td>
                            <td colspan="8" align="left"><input id="photography" name="photography"
                                                                data-options="width: 150" type="hidden"
                                                                value="${tQhCulturalCommunicationEntity.photography }"/>
                                <div class="photographyDiv"></div>
                            </td>
                        </tr>
                        <tr id="broadcastTR" align="center">
                            <td colspan="2">廣播宣傳</td>
                            <td colspan="8" align="left"><input id="broadcast" name="broadcast"
                                                                data-options="width: 150" type="hidden"
                                                                value="${tQhCulturalCommunicationEntity.broadcast }"/>
                                <div class="broadcastDiv"></div>
                            </td>
                        </tr>
                        <tr id="ledPublicityTR" align="center">
                            <td colspan="2">電視/LED宣傳</td>
                            <td colspan="8" align="left"><input id="ledPublicity"
                                                                name="ledPublicity" data-options="width: 150"
                                                                type="hidden"
                                                                value="${tQhCulturalCommunicationEntity.ledPublicity }"/>
                                <div class="ledPublicityDiv"></div>
                            </td>
                        </tr>
                        <tr id="platformPublicityTR" align="center">
                            <td colspan="2">平臺宣傳</td>
                            <td colspan="8" align="left"><input id="platformPublicity"
                                                                name="platformPublicity" data-options="width: 150"
                                                                type="hidden"
                                                                value="${tQhCulturalCommunicationEntity.platformPublicity }"/>
                                <div class="platformPublicityDiv"></div>
                            </td>
                        </tr>
                        <tr id="graphicDesignTR1">
                            <td align="center" rowspan="4" colspan="2">平面設計</td>
                            <td align="center" style="width:100Px">宣傳看板（KT板）</td>
                            <td colspan="7"><input id="graphicDesignKanban"
                                                   name="graphicDesignKanban" data-options="width: 150"
                                                   type="hidden"
                                                   value="${tQhCulturalCommunicationEntity.graphicDesignKanban }"/>
                                <input id="graphicDesignKanbanQita1" name="graphicDesignKanbanQita1"
                                       disabled="disabled" type="hidden" class="easyui-validatebox"
                                       data-options="width: 30"
                                       value="${tQhCulturalCommunicationEntity.graphicDesignKanbanQita}"/>
                                <input id="disOrEnabled" type="hidden" value="disabled"/>
                                <div class="graphicDesignKanbanDiv"></div>
                            </td>
                        </tr>
                        <tr id="graphicDesignTR2">
                            <td align="center">海報設計</td>
                            <td colspan="7"><input id="graphicDesignHaibao"
                                                   name="graphicDesignHaibao" data-options="width: 150"
                                                   type="hidden"
                                                   value="${tQhCulturalCommunicationEntity.graphicDesignHaibao }"/>
                                <input id="graphicDesignHaibaoQt1" name="graphicDesignHaibaoQt1"
                                       disabled="disabled" type="hidden" class="easyui-validatebox"
                                       data-options="width: 30"
                                       value="${tQhCulturalCommunicationEntity.graphicDesignHaibaoQt}"/>
                                <div class="graphicDesignHaibaoDiv"></div>
                            </td>
                        </tr>
                        <tr id="graphicDesignTR3">
                            <td align="center">單頁設計</td>
                            <td colspan="7"><input id="graphicDesignDanye"
                                                   name="graphicDesignDanye" data-options="width: 150"
                                                   type="hidden"
                                                   value="${tQhCulturalCommunicationEntity.graphicDesignDanye }"/>
                                <div class="graphicDesignDanyeDiv"></div>
                            </td>
                        </tr>
                        <tr id="graphicDesignTR4">
                            <td align="center">其它</td>
                            <td colspan="7"><input id="graphicDesignQita"
                                                   name="graphicDesignQita" data-options="width: 150"
                                                   type="hidden"
                                                   value="${tQhCulturalCommunicationEntity.graphicDesignQita }"/>
                                <input id="graphicDesignQitaQt1" name="graphicDesignQitaQt1"
                                       disabled="disabled" type="hidden" class="easyui-validatebox"
                                       data-options="width: 30"
                                       value="${tQhCulturalCommunicationEntity.graphicDesignQitaQt}"/>
                                <div class="graphicDesignQitaDiv"></div>
                            </td>
                        </tr>
                        <tr id="informationApplicationTR" align="center">
                            <td colspan="2">資料申請</td>
                            <td colspan="8" align="left"><input id="informationApplication"
                                                                name="informationApplication" data-options="width: 150"
                                                                type="hidden"
                                                                value="${tQhCulturalCommunicationEntity.informationApplication }"/>
                                <div class="informationApplicationDiv"></div>
                            </td>
                        </tr>
                        <tr id="equipmentBorrowingTR" align="center">
                            <td colspan="2">場地/設備借用</td>
                            <td colspan="8" align="left"><input id="equipmentBorrowing"
                                                                name="equipmentBorrowing" data-options="width: 150"
                                                                type="hidden"
                                                                value="${tQhCulturalCommunicationEntity.equipmentBorrowing }"/>
                                <div class="equipmentBorrowingDiv"></div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">具體說明</td>
                            <td align="left" colspan="8"><textarea id="specicInstructions" readonly
                                                                   name="specicInstructions"
                                                                   data-options="required:true"
                                                                   class="easyui-validatebox"
                                                                   style="width:1000px;height:80px;"
                                                                   rows="5"
                                                                   cols="6">${tQhCulturalCommunicationEntity.specicInstructions}</textarea>
                            </td>
                        </tr>
                        <c:if test="${not empty nodeName&&'接單窗口派單' eq nodeName}">
                        <tr align="center">
                            <td colspan="10" class="td_style1">項目負責人</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;">
                                    <table id="leaderItemTable" width="100%">
                                        <tbody id="info_Body">
                                        <input id="serialno1" name="serialno" type="hidden" value="${tQhCulturalCommunicationEntity.serialno}"/>
                                        <tr align="center" id="leaderItem1">
                                            <td colspan="1">1</td>
                                            <td colspan="2">工號&nbsp;<font color="red">*</font></td>
                                            <td colspan="2" class="td_style1">
                                                <input id="leaderNo1" class="easyui-validatebox"
                                                       style="margin-left: 10px;" onblur="queryLeaderInfo(this)"
                                                       data-options="width:160,required:true" name="leaderNo"
                                                       value="${tQhCulturalCommunicationEntity.leaderNo }"/>
                                            </td>
                                            <td colspan="2">姓名&nbsp;<font color="red">*</font></td>
                                            <td colspan="2" class="td_style1">
                                                <input id="leaderName1" class="easyui-validatebox"
                                                       style="margin-left: 10px;"
                                                       data-options="width:160,required:true" name="leaderName"
                                                       value="${tQhCulturalCommunicationEntity.leaderName }"/>
                                            </td>
                                            <td width="6%" colspan="1">
                                                <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                       class="deleteBtnStr"
                                                       onclick="bondedgooddeltr(1);return false;"/>
                                            </td>
                                        </tr>
                                        </tbody>
                                        <tr align="left" class="nottr">
                                            <td colspan="10" width="100%"
                                                style="text-align:left;padding-left:10px;">
                                                <input type="button" id="bondedgoodItemAdd"
                                                       style="width:100px;float:left;" value="添加一筆"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        </c:if>
                        <c:if test="${nodeOrder ge 11}">
                            <tr align="center">
                                <td colspan="10" class="td_style1">項目負責人</td>
                            </tr>
                            <tr align="center">
                                <td colspan="10" width="100%">
                                    <div style="overflow-x: auto;">
                                        <table id="leaderItemTable1" width="100%">
                                            <c:if test="${not empty tQhCulturalCommunicationEntity.leaderNo}">
                                                <c:set value="${fn:split(tQhCulturalCommunicationEntity.leaderNo, ',') }"
                                                       var="leaderNos"/>
                                                <c:set value="${fn:split(tQhCulturalCommunicationEntity.leaderName, ',') }"
                                                       var="leaderNames"/>
                                                <c:forEach items="${leaderNos }" var="no" varStatus="indexes">
                                                    <tr align="center" id="leaderItem${indexes.index}">
                                                        <td colspan="1">${indexes.index+1}</td>
                                                        <td colspan="2">工號</td>
                                                        <td colspan="2" class="td_style1">
                                                            <input id="leaderNo${indexes.index}"
                                                                   class="easyui-validatebox"
                                                                   style="margin-left: 10px;"
                                                                   onblur="queryLeaderInfo(this)"
                                                                   data-options="width:160,required:true,disabled:true"
                                                                   name="leaderNo"
                                                                   value="${no }"/>
                                                        </td>
                                                        <td colspan="2">姓名</td>
                                                        <td colspan="2" class="td_style1">
                                                            <input id="leaderName${indexes.index}"
                                                                   class="easyui-validatebox"
                                                                   style="margin-left: 10px;"
                                                                   data-options="width:160,required:true,disabled:true"
                                                                   name="leaderName"
                                                                   value="${leaderNames[indexes.index] }"/>
                                                        </td>
                                                        <td width="6%" colspan="1">
                                                            <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                                   class="deleteBtnStr" disabled
                                                                   onclick="bondedgooddeltr(1);return false;"/>
                                                        </td>
                                                    </tr>
                                                </c:forEach>
                                            </c:if>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                        </c:if>
                        <tr align="center">
                            <td style="width:10%">附件</td>
                            <td style="width:89%" colspan="9" class="td_style1"><input type="hidden" id="attachids"
                                                                                       name="attachids"
                                                                                       value="${tQhCulturalCommunicationEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <c:choose>
                            <c:when test="${not empty nodeName&&'申請單位確認' eq nodeName}">
                                <tr>
                                    <td style="text-align:center">評價&nbsp;<font color="red">*</font></td>
                                    <td colspan="9">
                                        <div class="judgeDiv"></div>
                                        <input id="judgeresult" name="judgeresult" type="hidden"
                                               class="easyui-validatebox" data-options="width: 150"
                                               value="${tQhCulturalCommunicationEntity.judgeresult }"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>改善建議</td>
                                    <td align="left" colspan="9">
								<textarea id="judgememo" name="judgememo" oninput="return LessThan(this);"
                                          onchange="return LessThan(this);" maxlength="200"
                                          onpropertychange="return LessThan(this);" class="easyui-validatebox"
                                          style="width:900px;height:80px;" rows="5"
                                          cols="6">${tQhCulturalCommunicationEntity.judgememo}</textarea>
                                        <span id="txtNum"></span></td>
                                </tr>
                                <tr style="display:none">
                                    <td colspan="10">
							<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                      style="width:1000px;height:60px;" rows="4" cols="4"></textarea>
                                    </td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <tr align="center">
                                    <td>批註</td>
                                    <td align="left" colspan="9">
						         <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                           style="width:1100px;height:60px;" rows="4" cols="4"></textarea>
                                            <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                                    </td>
                                </tr>
                            </c:otherwise>
                        </c:choose>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <c:choose>
                            <c:when test="${not empty nodeName&&'申請單位確認' eq nodeName}">
                                <tr align="center">
                                    <td colspan="10"
                                        style="border:none;text-align:center;margin-top:10px"><fox:action
                                            cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            perCall="updateJudge"
                                            serialNo="${tQhCulturalCommunicationEntity.serialno}"></fox:action>
                                        <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                           data-options="iconCls:'icon-cancel'"
                                           style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                    </td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <tr align="center">
                                    <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                                    perCall="updateLeaderInfo(${nodeOrder})"
                                                    serialNo="${tQhCulturalCommunicationEntity.serialno}"></fox:action>
                                    </td>
                                </tr>
                            </c:otherwise>
                        </c:choose>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','文化傳媒中心服務申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhCulturalCommunicationEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
<div id="dlg"></div>
<script src='${ctx}/static/js/other/tqhwfculturalcommunication.js?random=<%= Math.random()%>'></script>
</body>
</html>