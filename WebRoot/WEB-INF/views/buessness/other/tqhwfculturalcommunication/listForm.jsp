<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>文化傳媒中心服務申請單</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
	<form id="mainform" action="${ctx}/tqhwfculturalcommunication/${action}" method="post">
		<input id="ids" name="ids" type="hidden" value="${tQhCulturalCommunicationEntity.id}" />
		<input id="serialno" name="serialno" type="hidden" value="${tQhCulturalCommunicationEntity.serialno}" />
		<input id="makerno" name="makerno" type="hidden" value="${tQhCulturalCommunicationEntity.makerno }"/>
		<input id="makername" name="makername" type="hidden" value="${tQhCulturalCommunicationEntity.makername }"/>
		<input id="makerdeptno" name="makerdeptno" type="hidden" value="${tQhCulturalCommunicationEntity.makerdeptno }"/>
		<div class="commonW">
			<div class="headTitle">文化傳媒中心服務申請單</div>
			<div class="position_L">
				任務編碼：<span style="color:#999;"> <c:choose>
						<c:when test="${tQhCulturalCommunicationEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
						<c:otherwise>
                           ${tQhCulturalCommunicationEntity.serialno}
                       </c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_L1 margin_L">
				填單時間：<span style="color:#999;"> <c:choose>
						<c:when test="${tQhCulturalCommunicationEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
						<c:otherwise>
							<input style="border:0px" style="width: 120px" readonly="true"
								value="<fmt:formatDate value='${tQhCulturalCommunicationEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>" />
						</c:otherwise>
					</c:choose>
				</span>
			</div>
			<c:if test="${empty tQhCulturalCommunicationEntity.makerno}">
				<div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
			</c:if>
			<c:if test="${not empty tQhCulturalCommunicationEntity.makerno}">
				<div class="position_R margin_R">填單人：${tQhCulturalCommunicationEntity.makerno}/${tQhCulturalCommunicationEntity.makername}</div>
			</c:if>
			<div class="clear"></div>
			<table class="formList">
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">承辦人基本信息</td>
							</tr>
							<tr align="center">
								<td>承辦人工號&nbsp;<font color="red">*</font></td>
								<td><input id="dealno" name="dealno"
									onblur="queryUserInfo(this)" class="easyui-validatebox"
									data-options="width: 150,required:true"
									value="${tQhCulturalCommunicationEntity.dealno }" /></td>
								<td>承辦人<font color="red">*</font></td>
								<td><input id="dealname" name="dealname" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhCulturalCommunicationEntity.dealname }" /></td>
								<td>單位代碼&nbsp;<font color="red">*</font></td>
								<td><input id="dealdeptno" name="dealdeptno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${tQhCulturalCommunicationEntity.dealdeptno }" /></td>
								<td>廠區&nbsp;<font color="red">*</font></td>
								<td><input id="dealfactoryid" name="dealfactoryid"
									class="easyui-combobox"
									data-options="width: 150,required:true"
									value="${tQhCulturalCommunicationEntity.dealfactoryid }" />

									<input id="applynofactoryid" name="applynofactoryid" type="hidden" value="${tQhCulturalCommunicationEntity.applynofactoryid}"/>
								</td>
								<td>申請日期&nbsp;<font color="red">*</font></td>
								<td><input id="dealtime" name="dealtime" class="Wdate"
									data-options="width:180,required:true" style="width:180px"
									value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${tQhCulturalCommunicationEntity.dealtime}"/>"
									onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
								</td>
							</tr>
							<tr align="center">
								<td>單位名稱&nbsp;<font color="red">*</font></td>
								<td colspan="3"><input id="dealdeptname"
									style="width:360px" name="dealdeptname"
									class="easyui-validatebox"
									data-options="width: 360,required:true"
									value="${tQhCulturalCommunicationEntity.dealdeptname }" /></td>
								<td>聯繫方式&nbsp;<font color="red">*</font></td>
								<td><input id="dealtel" name="dealtel"
									onblur="valdApplyTel(this)" class="easyui-validatebox"
									data-options="width: 150,required:true"
									value="${tQhCulturalCommunicationEntity.dealtel }" /></td>
								<td>聯繫郵箱&nbsp;<font color="red">*</font></td>
								<td colspan="3"><input id="dealemail" name="dealemail"
									onblur="valdEmail(this)" class="easyui-validatebox"
									data-options="width: 250,required:true"
									value="${tQhCulturalCommunicationEntity.dealemail }" /></td>
							</tr>
							<tr align="center">
								<td colspan="10" class="td_style1">申請項目</td>
							</tr>
							<tr>
								<td>項目名稱&nbsp;<font color="red">*</font></td>
								<td colspan="5"><input id="projecname" style="width:360px"
									name="projecname" class="easyui-validatebox"
									data-options="width: 360,required:true"
									value="${tQhCulturalCommunicationEntity.projecname }" /></td>
								<td>需求時間&nbsp;<font color="red">*</font></td>
								<td colspan="3"><input id="demandStartTime"
									name="demandStartTime" class="Wdate"
									data-options="width:180,required:true" style="width:180px"
									value="<fmt:formatDate  pattern="yyyy-MM-dd  HH:mm:ss"
									value="${tQhCulturalCommunicationEntity.demandStartTime}"/>"
									onclick="WdatePicker({dateFmt:'yyyy-MM-dd  HH:mm:ss',minDate:'%y-%M-%d'})" />
									~<input id="demandEndTime" name="demandEndTime" class="Wdate"
									data-options="width:180,required:true" style="width:180px"
									value="<fmt:formatDate  pattern="yyyy-MM-dd HH:mm:ss"
									value="${tQhCulturalCommunicationEntity.demandEndTime}"/>"
									onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'%y-%M-%d'})" />
								</td>
							</tr>
							<tr>
								<td>交付格式&nbsp;<font color="red">*</font></td>
								<td colspan="5">
									<div class="deliveryFormatDiv"></div> <input
									id="deliveryFormat" name="deliveryFormat"
									class="easyui-validatebox" data-options="width: 150"
									type="hidden"
									value="${tQhCulturalCommunicationEntity.deliveryFormat }" /> <input
									id="deliveryFormatQita1" name="deliveryFormatQita1"
									disabled="disabled" type="hidden" class="easyui-validatebox"
									data-options="width: 30"
									value="${tQhCulturalCommunicationEntity.deliveryFormatQita}" />
								</td>
								<td>作業地點&nbsp;<font color="red">*</font></td>
								<td colspan="3"><input id="jobLocation" style="width:360px"
									name="jobLocation" class="easyui-validatebox"
									data-options="width: 360,required:true"
									value="${tQhCulturalCommunicationEntity.jobLocation }" /></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="2" class="td_style1" style="text-align:center;width:20%">申請類型</td>
								<td colspan="8" class="td_style1" style="text-align:center;width:79%">明細內容</td>
							</tr>
							<tr align="center">
								<td colspan="2"><input id="filmProductionCK" class="kindCK"
									name="filmProductionCK" type="checkbox" onclick="filmProductionEdit()"
									data-options="width: 150" />影片製作</td>
								<td colspan="8"  align="left"><input id="filmProduction"
									name="filmProduction" data-options="width: 150" type="hidden"
									value="${tQhCulturalCommunicationEntity.filmProduction }" />
								<div class="filmProductionDiv"></div>
								</td>
							</tr>
							<tr align="center">
								<td colspan="2"><input id="photographyCK" class="kindCK"
									name="photographyCK" type="checkbox" onclick="photographyEdit()"
                                    data-options="width: 150" />攝影攝像
								</td>
								<td colspan="8"  align="left"><input id="photography" name="photography"
									data-options="width: 150" type="hidden"
									value="${tQhCulturalCommunicationEntity.photography }" />
								<div class="photographyDiv"></div>
								</td>
							</tr>
							<tr align="center">
								<td colspan="2"><input id="broadcastCK"  class="kindCK" name="broadcastCK" onclick="broadcastEdit()"
									type="checkbox" data-options="width: 150" />廣播宣傳</td>
								<td colspan="8" align="left"><input id="broadcast" name="broadcast"
									data-options="width: 150" type="hidden"
									value="${tQhCulturalCommunicationEntity.broadcast }" />
								<div class="broadcastDiv"></div>
								</td>
							</tr>
							<tr align="center">
								<td colspan="2"><input id="ledPublicityCK" onclick="ledPublicityEdit()" class="kindCK" 
									name="ledPublicityCK" type="checkbox" data-options="width: 150" />電視/LED宣傳
								</td>
								<td colspan="8"  align="left"><input id="ledPublicity"
									name="ledPublicity" data-options="width: 150" type="hidden"
									value="${tQhCulturalCommunicationEntity.ledPublicity }" />
								<div class="ledPublicityDiv"></div>
								</td>
							</tr>
							<tr align="center">
								<td colspan="2"><input id="platformPublicityCK" class="kindCK"
									name="platformPublicityCK" type="checkbox" onclick="platformPublicityEdit()"
									data-options="width: 150" />平臺宣傳</td>
								<td colspan="8"  align="left"><input id="platformPublicity"
									name="platformPublicity" data-options="width: 150"
									type="hidden"
									value="${tQhCulturalCommunicationEntity.platformPublicity }" />
								<div class="platformPublicityDiv"></div>
								</td>
							</tr>
							<tr>
								<td align="center" rowspan="4" colspan="2"><input id="graphicDesignCK" class="kindCK" 
									name="graphicDesignCK" type="checkbox" onclick="graphicDesignEdit()"
									data-options="width: 150" />平面設計</td>
								<td align="center" style="width:100Px">宣傳看板（KT板）</td>
								<td colspan="7"><input id="graphicDesignKanban"
									name="graphicDesignKanban" data-options="width: 150"
									type="hidden"
									value="${tQhCulturalCommunicationEntity.graphicDesignKanban }" />
								<input id="graphicDesignKanbanQita1" name="graphicDesignKanbanQita1"
									disabled="disabled" type="hidden" class="easyui-validatebox"
									data-options="width: 30"
									value="${tQhCulturalCommunicationEntity.graphicDesignKanbanQita}" />
								<div class="graphicDesignKanbanDiv"></div>
								</td>
							</tr>
							<tr>
								<td align="center">海報設計</td>
								<td colspan="7"><input id="graphicDesignHaibao"
									name="graphicDesignHaibao" data-options="width: 150"
									type="hidden"
									value="${tQhCulturalCommunicationEntity.graphicDesignHaibao }" />
								<input id="graphicDesignHaibaoQt1" name="graphicDesignHaibaoQt1"
									disabled="disabled" type="hidden" class="easyui-validatebox"
									data-options="width: 30"
									value="${tQhCulturalCommunicationEntity.graphicDesignHaibaoQt}" />
								<div class="graphicDesignHaibaoDiv"></div>
								</td>
							</tr>
							<tr>
								<td align="center">單頁設計</td>
								<td colspan="7"><input id="graphicDesignDanye"
									name="graphicDesignDanye" data-options="width: 150"
									type="hidden"
									value="${tQhCulturalCommunicationEntity.graphicDesignDanye }" />
								<div class="graphicDesignDanyeDiv"></div>
								</td>
							</tr>
							<tr>
								<td align="center">其它</td>
								<td colspan="7"><input id="graphicDesignQita"
									name="graphicDesignQita" data-options="width: 150"
									type="hidden"
									value="${tQhCulturalCommunicationEntity.graphicDesignQita }" />
								<input id="graphicDesignQitaQt1" name="graphicDesignQitaQt1"
									disabled="disabled" type="hidden" class="easyui-validatebox"
									data-options="width: 30"
									value="${tQhCulturalCommunicationEntity.graphicDesignQitaQt}" />
								<div class="graphicDesignQitaDiv"></div>
								</td>
							</tr>
							<tr align="center">
								<td colspan="2"><input id="informationApplicationCK" onclick="informationApplicationEdit()"
									name="informationApplicationCK" type="checkbox" class="kindCK" 
									data-options="width: 150" />資料申請</td>
								<td colspan="8" align="left"><input id="informationApplication"
									name="informationApplication" data-options="width: 150"
									type="hidden"
									value="${tQhCulturalCommunicationEntity.informationApplication }" />
								<div class="informationApplicationDiv"></div>
								</td>
							</tr>
							<tr align="center">
								<td colspan="2"><input id="equipmentBorrowingCK" class="kindCK" 
									name="equipmentBorrowingCK" type="checkbox" onclick="equipmentBorrowingEdit()"
									data-options="width: 150" />場地/設備借用</td>
								<td colspan="8" align="left"><input id="equipmentBorrowing"
									name="equipmentBorrowing" data-options="width: 150"
									type="hidden"
									value="${tQhCulturalCommunicationEntity.equipmentBorrowing }" />
								<div class="equipmentBorrowingDiv"></div>
								</td>
							</tr>
							<tr align="center">
								<td colspan="2">具體說明&nbsp;<font color="red">*</font></td>
								<td align="left" colspan="8"><textarea id="specicInstructions"
										name="specicInstructions" 
										oninput="return LessThan(this);"
                                        onchange="return LessThan(this);"
                                        onpropertychange="return LessThan(this);"
										data-options="required:true" maxlength="200"
										class="easyui-validatebox" style="width:800px;height:80px;"
										rows="5" cols="6">${tQhCulturalCommunicationEntity.specicInstructions}</textarea><span id="txtNum"></span></td>
							</tr>
							<tr align="center">
								<td style="text-align:center;width:10%">附件</td>
								<td style="width:89%" colspan="9" class="td_style1"><span
									class="sl-custom-file"> <input type="button"
										value="点击上传文件" class="btn-file" /> <input
										id="attachidsUpload" name="attachidsUpload" type="file"
										onchange="oosUploadFile('${workFlowId}');" class="ui-input-file" />
								</span> <input type="hidden" id="attachids" name="attachids"
									value="${tQhCulturalCommunicationEntity.attachids }" />
									<div id="dowloadUrl">
										<c:forEach items="${file}" varStatus="i" var="item">
											<div id="${item.id}"
												style="line-height:30px;margin-left:5px;" class="float_L">
												<div class="float_L">
													<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
												</div>
												<div class="float_L deleteBtn"
													onclick="oosDelAtt('${item.id}')"></div>
											</div>
										</c:forEach>
									</div></td>
							</tr>
							<tr>
								<td align="center">備註</td>
								<td colspan="9" align="left">
									1、文化傳媒中心將根據服務申請單遞交順序展開相關作業，申請人應至少提前2天提交服務申請單，避免影響服務需求；<br />
									2、影片製作腳本須部級及以上主管簽字核准並上傳系統，影片製作、光盤刻錄等屬資安管控需求，須處級及以上主管核准；<br />
									3、影片製作類、平面設計類需求作品交付后，可修改2次，如需大量變更製作內容及更改影片腳本，則須重新提出服務申請；<br />
									4、平面設計類、廣播宣傳類服務申請須上傳文字內容（簡體） ；<br />
									5、若需求單位活動延期，該項申請單可在系統中保留1周，超期以退單處理，待確定具體事項後可重新發起申請。
								</td>
							</tr>
							<tr>
								<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
									<a href="javascript:void(0)"
									onclick="showWfImag('${workFlowId}','文化傳媒中心服務申請單','');">點擊查看簽核流程圖</a>
								</th>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td colspan="10" style="text-align:left;">
						<table class="flowList"
							style="margin-left:5px;margin-top:5px;width:99%">
							<tr>
								<td style="border:none">
									<table width="18%" style="float: left;margin-left: 5px;">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['jdchargeno_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole5('42','jdchargeno','jdchargename','jdpchargeno','jdpchargename','','',$('#dealfactoryid').combobox('getValue'))"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="jdchargeno" name="jdchargeno"
												class="easyui-validatebox" 
												data-options="width:80,required:${requiredMap['jdchargeno']}" readonly
												value="${tQhCulturalCommunicationEntity.jdchargeno }"/><c:if test="${requiredMap['jdchargeno'].equals('true')}"><font color="red">*</font></c:if>
												/<input id="jdchargename" name="jdchargename" readonly class="easyui-validatebox" data-options="width:80,required:${requiredMap['jdchargeno']}"
												value="${tQhCulturalCommunicationEntity.jdchargename }" /></td>
										</tr>
									</table>
									<table width="18%" style="float: left;margin-left: 5px;">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#applynofactoryid').val())"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="kchargeno" name="kchargeno"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['kchargeno']}" readonly
												value="${tQhCulturalCommunicationEntity.kchargeno }" /><c:if test="${requiredMap['kchargeno'].equals('true')}"><font color="red">*</font></c:if>
												/<input id="kchargename"
												name="kchargename" readonly class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['kchargeno']}"
												value="${tQhCulturalCommunicationEntity.kchargename }" /></td>
										</tr>
									</table>

									<table width="18%" style="float: left;margin-left: 5px;">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#applynofactoryid').val())"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="bchargeno" name="bchargeno"
												class="easyui-validatebox" data-options="width:80,required:${requiredMap['bchargeno']}" readonly
												value="${tQhCulturalCommunicationEntity.bchargeno }" /><c:if test="${requiredMap['bchargeno'].equals('true')}"><font color="red">*</font></c:if>
												/<input id="bchargename" name="bchargename" readonly
												class="easyui-validatebox" data-options="width:80,required:${requiredMap['bchargeno']}"
												value="${tQhCulturalCommunicationEntity.bchargename }" /></td>
										</tr>
									</table>

									<table width="18%" style="float: left;margin-left: 5px;">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#applynofactoryid').val())"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="cchargeno" name="cchargeno"
												class="easyui-validatebox" data-options="width:80,required:${requiredMap['cchargeno']}" readonly
												value="${tQhCulturalCommunicationEntity.cchargeno }" /><c:if test="${requiredMap['cchargeno'].equals('true')}"><font color="red">*</font></c:if>
												/<input id="cchargename" name="cchargename" readonly
												class="easyui-validatebox" data-options="width:80,required:${requiredMap['cchargeno']}"
												value="${tQhCulturalCommunicationEntity.cchargename }" /></td>
										</tr>
									</table>
									<table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																 onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#applynofactoryid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="zchargeno" name="zchargeno"
													   class="easyui-validatebox"
													   data-options="width:80,required:${requiredMap['zchargeno']}"
													   readonly
													   value="${tQhCulturalCommunicationEntity.zchargeno }"/><c:if test="${requiredMap['zchargeno'].equals('true')}"><font color="red">*</font></c:if>
												/<input id="zchargename" name="zchargename"
														readonly class="easyui-validatebox"
														data-options="width:80,required:${requiredMap['zchargeno']}"
														value="${tQhCulturalCommunicationEntity.zchargename }"/>
											</td>
										</tr>
									</table>
								</td>
							</tr>
							<tr>
								<td style="border:none">
									<table width="18%" style="float: left;margin-left: 5px;"  id="zcchargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																 onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#applynofactoryid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="zcchargeno" name="zcchargeno"
													   class="easyui-validatebox" data-options="width:80,required:${requiredMap['zcchargeno']}"
													   readonly
													   value="${tQhCulturalCommunicationEntity.zcchargeno }"/><c:if test="${requiredMap['zcchargeno'].equals('true')}"><font color="red">*</font></c:if>
												/<input id="zcchargename" name="zcchargename"
														readonly class="easyui-validatebox"
														data-options="width:80,required:${requiredMap['zcchargeno']}"
														value="${tQhCulturalCommunicationEntity.zcchargename }"/>
											</td>
										</tr>
									</table>
									<table width="18%" style="float: left;margin-left: 5px;">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['rkchargeno_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole4('45','rkchargeno','rkchargename',$('#dealfactoryid').combobox('getValue'))"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="rkchargeno" name="rkchargeno"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['rkchargeno']}" readonly
												value="${tQhCulturalCommunicationEntity.rkchargeno }" /><c:if test="${requiredMap['rkchargeno'].equals('true')}"><font color="red">*</font></c:if>
												/<input id="rkchargename"
												name="rkchargename" readonly class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['rkchargeno']}"
												value="${tQhCulturalCommunicationEntity.rkchargename }" /></td>
										</tr>
									</table>

									<table width="18%" style="float: left;margin-left: 5px;">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['rzbchargeno_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole4('46','rzbchargeno','rzbchargename',$('#dealfactoryid').combobox('getValue'))"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="rzbchargeno" name="rzbchargeno"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['rzbchargeno']}" readonly
												value="${tQhCulturalCommunicationEntity.rzbchargeno }" /><c:if test="${requiredMap['rzbchargeno'].equals('true')}"><font color="red">*</font></c:if>
												/<input id="rzbchargename"
												name="rzbchargename" readonly class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['rzbchargeno']}"
												value="${tQhCulturalCommunicationEntity.rzbchargename }" /></td>
										</tr>
									</table>
									<table width="18%" style="float: left;margin-left: 5px;">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['rzcchargeno_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																 onclick="selectRole4('301','rzcchargeno','rzcchargename',$('#dealfactoryid').combobox('getValue'))"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="rzcchargeno" name="rzcchargeno"
													   class="easyui-validatebox"
													   data-options="width:80,required:${requiredMap['rzcchargeno']}" readonly
													   value="${tQhCulturalCommunicationEntity.rzcchargeno }" /><c:if test="${requiredMap['rzcchargeno'].equals('true')}"><font color="red">*</font></c:if>
												/<input id="rzcchargename"
														name="rzcchargename" readonly class="easyui-validatebox"
														data-options="width:80,required:${requiredMap['rzcchargeno']}"
														value="${tQhCulturalCommunicationEntity.rzcchargename }" /></td>
										</tr>
									</table>

									<table width="18%" style="float: left;margin-left: 5px;">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: center;">${requiredMap['jdpchargeno_name']}</td>
														<td style="border: none;">
															<div></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="jdpchargeno" name="jdpchargeno"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['jdpchargeno']}" readonly
												value="${tQhCulturalCommunicationEntity.jdpchargeno }" /><c:if test="${requiredMap['jdpchargeno'].equals('true')}"><font color="red">*</font></c:if>
												/<input id="jdpchargename"
												name="jdpchargename" readonly class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['jdpchargeno']}"
												value="${tQhCulturalCommunicationEntity.jdpchargename }" /></td>
										</tr>
									</table>
								</td>
							</tr>
							<tr>
								<td style="border:none">
									<table width="18%" style="float: left;margin-left: 5px;">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: center;">${requiredMap['sqchargeno_name']}</td>
														<td style="border: none;">
															<div></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="sqchargeno" name="sqchargeno"
													   class="easyui-validatebox" data-options="width:80,required:${requiredMap['sqchargeno']}" readonly
													   value="${tQhCulturalCommunicationEntity.sqchargeno }" /><c:if test="${requiredMap['sqchargeno'].equals('true')}"><font color="red">*</font></c:if>
												/<input id="sqchargename" name="sqchargename" readonly
														class="easyui-validatebox" data-options="width:80,required:${requiredMap['sqchargeno']}"
														value="${tQhCulturalCommunicationEntity.sqchargename }" /></td>
										</tr>
									</table>
								</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td colspan="10" style="text-align:left;">
						<table class="flowList"
							style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
							<tr>
								<td>簽核時間</td>
								<td>簽核節點</td>
								<td>簽核主管</td>
								<td>簽核意見</td>
								<td>批註</td>
								<td>簽核電腦IP</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td colspan="10"
						style="border:none;text-align:center;margin-top:10px"><a
						href="javascript:;" id="btnSave" class="easyui-linkbutton"
						data-options="iconCls:'icon-add'" style="width: 100px;"
						onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp; <a
						href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
						data-options="iconCls:'icon-ok'" style="width: 100px;"
						onclick="saveInfo(2);">提交</a></td>
				</tr>
			</table>
		</div>
		<input type="hidden" id="deptNo" name="deptNo" value="" /> <input
			type="hidden" id="chargeNo" name="chargeNo" value="" /> <input
			type="hidden" id="chargeName" name="chargeName" value="" /> <input
			type="hidden" id="factoryId" name="factoryId" value="" /> <input
			type="hidden" id="dutyId" name="dutyId" value="" />
		<div id="win"></div>
	</form>
	<script	src='${ctx}/static/js/other/tqhwfculturalcommunication.js?random=<%= Math.random()%>'></script>
</body>
</html>