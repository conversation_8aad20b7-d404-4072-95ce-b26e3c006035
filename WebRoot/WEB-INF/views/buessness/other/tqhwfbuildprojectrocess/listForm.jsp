<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>新增建設項目環保手續辦理委託申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>

</head>
<body>
<form id="mainform" action="${ctx}/tqhwfbuildprojectrocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhWfbuildprojectrocess.id }"/>
    <input id="serialno" name="tQhWfbuildprojectrocessEntity.serialno" type="hidden"
           value="${tQhWfbuildprojectrocess.serialno }"/>
    <input id="createtime" name="tQhWfbuildprojectrocessEntity.createtime" type="hidden"
           value="${tQhWfbuildprojectrocess.createtime }"/>
    <input id="makerno" name="tQhWfbuildprojectrocessEntity.makerno" type="hidden"
           value="${tQhWfbuildprojectrocess.makerno }"/>
    <input id="makername" name="tQhWfbuildprojectrocessEntity.makername" type="hidden"
           value="${tQhWfbuildprojectrocess.makername }"/>
    <input id="makerdeptno" name="tQhWfbuildprojectrocessEntity.makerdeptno" type="hidden"
           value="${tQhWfbuildprojectrocess.makerdeptno }"/>
    <input id="makerfactoryid" name="tQhWfbuildprojectrocessEntity.makerfactoryid" type="hidden"
           value="${tQhWfbuildprojectrocess.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">新增建設項目環保手續辦理委託申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
             <c:choose>
                 <c:when test="${tQhWfbuildprojectrocess.serialno==null}">
                     提交成功后自動編碼
                 </c:when>
                 <c:otherwise>
                     ${tQhWfbuildprojectrocess.serialno}
                 </c:otherwise>
             </c:choose>
        </span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
            <c:choose>
                <c:when test="${tQhWfbuildprojectrocess.createtime==null}">
                    YYYY/MM/DD
                </c:when>
                <c:otherwise>
                    <input class="inputCss" style="width: 100px"
                           value="<fmt:formatDate value='${tQhWfbuildprojectrocess.createtime}' pattern='yyyy-MM-dd hh:mm'/>">
                </c:otherwise>
            </c:choose>
        </span>
        </div>
        <c:if test="${empty tQhWfbuildprojectrocess.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty tQhWfbuildprojectrocess.makerno}">
            <div class="position_R margin_R">
                填單人：${tQhWfbuildprojectrocess.makerno}/${tQhWfbuildprojectrocess.makername}</div>
        </c:if>
        <br>
        <div class="clear"></div>
        <table class="formList" id="buildprojecttable">
            <tr align="center">
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealno" name="tQhWfbuildprojectrocessEntity.dealno"
                                       class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${tQhWfbuildprojectrocess.dealno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="6%">承辦人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="tQhWfbuildprojectrocessEntity.dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${tQhWfbuildprojectrocess.dealname }"/>
                            </td>
                            <td width="6%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="tQhWfbuildprojectrocessEntity.dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${tQhWfbuildprojectrocess.dealdeptno }"/>
                            </td>
                            <td width="6%">提報日期&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealtime" name="tQhWfbuildprojectrocessEntity.dealtime" class="easyui-my97"
                                       datefmt="yyyy-MM-dd" data-options="width: 100,required:true"
                                       value="<fmt:formatDate value="${tQhWfbuildprojectrocess.dealtime}"/>"/>
                            </td>
                            <td width="6%">廠區&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealfactoryid" name="tQhWfbuildprojectrocessEntity.dealfactoryid"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${tQhWfbuildprojectrocess.dealfactoryid }"
                                       data-options="width: 120,required:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealdeptname" name="tQhWfbuildprojectrocessEntity.dealdeptname"
                                       class="easyui-validatebox" data-options="width: 400,required:true"
                                       value="${tQhWfbuildprojectrocess.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="tQhWfbuildprojectrocessEntity.dealemail"
                                       class="easyui-validatebox"
                                       value="${tQhWfbuildprojectrocess.dealemail }" style="width:300px;"
                                       data-options="required:true" onblur="valdEmail(this)"/>
                            </td>
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealtel" name="tQhWfbuildprojectrocessEntity.dealtel"
                                       class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${tQhWfbuildprojectrocess.dealtel }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <tr align="center">
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">項目基本信息</td>
                        </tr>
                        <tr align="center">
                            <td>項目名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="projecname" name="tQhWfbuildprojectrocessEntity.projecname"
                                       class="easyui-validatebox"
                                       style="width:150px;"
                                       data-options="required:true" value="${tQhWfbuildprojectrocess.projecname }"/>
                            </td>
                            <td>項目法人&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="projeccorporateid" name="tQhWfbuildprojectrocessEntity.projeccorporateid"
                                       class="easyui-combobox" data-options="width: 300,required:true"
                                       panelHeight="auto" editable="false"
                                       value="${tQhWfbuildprojectrocess.projeccorporateid }"/>
                            </td>
                            <td>項目性質&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="projecnature" name="tQhWfbuildprojectrocessEntity.projecnature"
                                       class="easyui-combobox"
                                       value="${tQhWfbuildprojectrocess.projecnature }"
                                       panelHeight="auto" editable="false"/>
                            </td>
                            <td>建設規模&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="buildsize" name="tQhWfbuildprojectrocessEntity.buildsize"
                                       class="easyui-validatebox"
                                       data-options="width: 150,required:true"
                                       value="${tQhWfbuildprojectrocess.buildsize }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>環境影響評價<br/>報告類型&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="reporttypeid" name="tQhWfbuildprojectrocessEntity.reporttypeid"
                                       class="easyui-combobox" data-options="width: 100,required:true"
                                       panelHeight="auto" editable="false"
                                       value="${tQhWfbuildprojectrocess.reporttypeid }"/>
                            </td>
                            <td>建設總投資<br/>(單位：萬元)&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="buildtotalcost" name="tQhWfbuildprojectrocessEntity.buildtotalcost"
                                       class="easyui-validatebox"
                                       data-options="width: 150,required:true"
                                       value="${tQhWfbuildprojectrocess.buildtotalcost }" onblur="valdMoney(this)"/>
                            </td>
                            <td>環保投資<br/>(單位：萬元)&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="enviromentalcost" name="tQhWfbuildprojectrocessEntity.enviromentalcost"
                                       class="easyui-validatebox"
                                       data-options="width: 150,required:true"
                                       value="${tQhWfbuildprojectrocess.enviromentalcost }" onblur="valdMoney(this)"/>
                            </td>
                            <td>資金來源&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="fundsource" name="tQhWfbuildprojectrocessEntity.fundsource"
                                       class="easyui-validatebox"
                                       data-options="width: 150,required:true"
                                       value="${tQhWfbuildprojectrocess.fundsource }"/>
                            </td>
                        </tr>
                        <%--<tr align="center">
                            <td>建設總投資<br/>(單位：萬元)&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="buildtotalcost" name="tQhWfbuildprojectrocessEntity.buildtotalcost"
                                       class="easyui-validatebox"
                                       data-options="width: 150,required:true"
                                       value="${tQhWfbuildprojectrocess.buildtotalcost }" onblur="valdMoney(this)"/>
                            </td>
                            <td>環保投資<br/>(單位：萬元)&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="enviromentalcost" name="tQhWfbuildprojectrocessEntity.enviromentalcost"
                                       class="easyui-validatebox"
                                       data-options="width: 150,required:true"
                                       value="${tQhWfbuildprojectrocess.enviromentalcost }" onblur="valdMoney(this)"/>
                            </td>
                            <td colspan="2">資金來源&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="fundsource" name="tQhWfbuildprojectrocessEntity.fundsource"
                                       class="easyui-validatebox"
                                       data-options="width: 150,required:true"
                                       value="${tQhWfbuildprojectrocess.fundsource }"/>
                            </td>
                        </tr>--%>
                        <tr align="center">
                            <td>規劃人力<br/>(單位：人)&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="planperson" name="tQhWfbuildprojectrocessEntity.planperson"
                                       class="easyui-validatebox"
                                       data-options="width: 150,required:true"
                                       value="${tQhWfbuildprojectrocess.planperson }" onblur="valdMoney(this)"/>
                            </td>
                            <td>建設週期&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="buildcyclebegindate" name="tQhWfbuildprojectrocessEntity.buildcyclebegindate"
                                       class="easyui-my97" datefmt="yyyy-MM-dd"
                                       data-options="width: 100,required:true,"
                                       value="<fmt:formatDate value="${tQhWfbuildprojectrocess.buildcyclebegindate}"/>"/>~
                                <input id="buildcycleenddate" name="tQhWfbuildprojectrocessEntity.buildcycleenddate"
                                       class="easyui-my97" datefmt="yyyy-MM-dd"
                                       data-options="width: 100,required:true,validType:'endTime'"
                                       value="<fmt:formatDate value="${tQhWfbuildprojectrocess.buildcycleenddate}"/>"/>
                            </td>
                            <td colspan="2">計劃開工日期&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="planstartdate" name="tQhWfbuildprojectrocessEntity.planstartdate"
                                       class="easyui-my97"
                                       datefmt="yyyy-MM-dd"
                                       data-options="width: 120,required:true"
                                       value="<fmt:formatDate value="${tQhWfbuildprojectrocess.planstartdate}"/>"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>建設地點&nbsp;<font color="red">*</font></td>
                            <td colspan="4" class="td_style1">
                                <input id="buildsite" name="tQhWfbuildprojectrocessEntity.buildsite"
                                       class="easyui-validatebox"
                                       data-options="width: 300,required:true"
                                       value="${tQhWfbuildprojectrocess.buildsite }"/>
                            </td>
                            <td>生產工藝&nbsp;<font color="red">*</font></td>
                            <td colspan="4" class="td_style1">
                                <input id="produceart" name="tQhWfbuildprojectrocessEntity.produceart"
                                       class="easyui-validatebox"
                                       data-options="width: 300,required:true"
                                       value="${tQhWfbuildprojectrocess.produceart }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>項目主要建設內容&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1">
						<textarea id="projectbuildcontent" name="tQhWfbuildprojectrocessEntity.projectbuildcontent"
                                  class="easyui-validatebox"
                                  style="width:99%;height:80px;" data-options="required:true"
                                  rows="5" cols="6"
                                  value="${tQhWfbuildprojectrocess.projectbuildcontent }">${tQhWfbuildprojectrocess.projectbuildcontent }</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>排污環節及防治措施&nbsp;<font color="red">*</font></td>
                            <td colspan="9">
                                <table style="width:100%;">
                                    <tr>
                                        <td style="border:none;border-bottom: 1px solid #138CDD;">
                                            <div class="discharge" align="left">

                                            </div>
                                            <input id="dischargetype" name="tQhWfbuildprojectrocessEntity.dischargetype"
                                                   type="hidden" class="easyui-validatebox" data-options="width: 150"
                                                   value="${tQhWfbuildprojectrocess.dischargetype }"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
									<textarea id="dischargelink" name="tQhWfbuildprojectrocessEntity.dischargelink"
                                              class="easyui-validatebox" style="width:99%;height:80px;"
                                              data-options="required:true"
                                              rows="5" cols="6"
                                              value="${tQhWfbuildprojectrocess.dischargelink }">${tQhWfbuildprojectrocess.dischargelink }</textarea>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
						        <span class="sl-custom-file">
						            <input type="button" value="点击上传文件" class="btn-file"/>
						            <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file">
						        </span>
                                <input type="hidden" id="attachids" name="tQhWfbuildprojectrocessEntity.attachids" value="${tQhWfbuildprojectrocess.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L"><a
                                                    href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                                <%--<div class="clear"></div>--%>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>委託事項說明&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1">
						<textarea id="entrustexplain" name="tQhWfbuildprojectrocessEntity.entrustexplain"
                                  class="easyui-validatebox" style="width:99%;height:80px;"
                                  rows="5" cols="6" value="${tQhWfbuildprojectrocess.entrustexplain }"
                                  data-options="required:true,prompt:'例：1.委託太原周邊環保科技處環保手續辦理(可研立項，環評、竣工環保驗收)事宜；2.委託太原周邊總務處議價及合同簽訂事宜。'">${tQhWfbuildprojectrocess.entrustexplain }</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 100%;padding-bottom: 15px;">
                                    <input id="buildprojectApplyItemTableIndex" type="hidden"
                                           value="<c:if test="${itemEntity!=null&&itemEntity.size()>0}">${itemEntity.size() + 1}</c:if>
                            <c:if test="${itemEntity==null}">2</c:if>">
                                    </input>
                                    <table id="buildprojectApplyItemTable" width="100%">
                                        <tr align="center">
                                            <td>序號</td>
                                            <td>費用掛靠單位&nbsp;<font color="red">*</font></td>
                                            <td>費用代碼&nbsp;<font color="red">*</font></td>
                                            <td>費用佔比(%)</td>
                                            <td>操作</td>
                                        </tr>
                                        <c:if test="${itemEntity!=null&&itemEntity.size()>0}">
                                            <c:forEach items="${itemEntity}" var="item" varStatus="status">
                                                <tr align="center" id="costItem${status.index}">
                                                    <td>${status.index+1}</td>
                                                    <td><input id="costname${status.index}"
                                                               name="tQhWfbuildprojectitemEntity[${status.index}].costname"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width:400px;"
                                                               value="${item.costname}"/></td>
                                                    <td><input id="costno${status.index}"
                                                               name="tQhWfbuildprojectitemEntity[${status.index}].costno"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width:200px;"
                                                               value="${item.costno}"/></td>
                                                    <td>
                                                        <input id="costrate${status.index}"
                                                               name="tQhWfbuildprojectitemEntity[${status.index}].costrate"
                                                               class="easyui-validatebox" style="width:200px;"
                                                               value="${item.costrate}"/>
                                                    </td>
                                                    <td><input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="deltr(${status.index+1});return false;"/></td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${itemEntity==null}">
                                            <tr align="center" id="costItem1">
                                                <td>1</td>
                                                <td><input id="costname1" name="tQhWfbuildprojectitemEntity[0].costname"
                                                           class="easyui-validatebox" data-options="required:true"
                                                           style="width:400px;"
                                                           value=""/></td>
                                                <td><input id="costno1" name="tQhWfbuildprojectitemEntity[0].costno"
                                                           class="easyui-validatebox" data-options="required:true"
                                                           style="width:200px;"
                                                           value=""/></td>
                                                <td>
                                                    <input id="costrate1" name="tQhWfbuildprojectitemEntity[0].costrate"
                                                           class="easyui-validatebox" style="width:200px;" value="" onblur="valdIsNumber(this)"/>
                                                </td>
                                                <td><input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="deltr(1);return false;"/></td>
                                            </tr>
                                        </c:if>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="left">
                            <td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
                                <input type="button" id="add" style="width:100px;float:left;" value="添加一筆"/>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)" onclick="showWfImag('${workFlowId}','系統上線申請流程圖','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="tQhWfbuildprojectrocessEntity.kchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['kchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.kchargeno }"/><c:if test="${requiredMap['kchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="kchargename" name="tQhWfbuildprojectrocessEntity.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${tQhWfbuildprojectrocess.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="bchargeno" name="tQhWfbuildprojectrocessEntity.bchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['bchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.bchargeno }"/><c:if test="${requiredMap['bchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="bchargename" name="tQhWfbuildprojectrocessEntity.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${tQhWfbuildprojectrocess.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="cchargeno"
                                                               name="tQhWfbuildprojectrocessEntity.cchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['cchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.cchargeno }"/><c:if test="${requiredMap['cchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="cchargename"
                                                                name="tQhWfbuildprojectrocessEntity.cchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['cchargeno']}"
                                                                value="${tQhWfbuildprojectrocess.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"  id="hchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽
                                                                    <a href="javascript:addHq('hcharge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hchargeno" onblur="getUserNameByEmpno(this,'hcharge');" name="tQhWfbuildprojectrocessEntity.hchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hchargeno']}" value="${tQhWfbuildprojectrocess.hchargeno }"/>
                                                        <c:if test="${requiredMap['hchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hchargename" name="tQhWfbuildprojectrocessEntity.hchargename" readonly
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hchargeno']}"  value="${tQhWfbuildprojectrocess.hchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectrocessEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zchargeno"
                                                               name="tQhWfbuildprojectrocessEntity.zchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.zchargeno }"/><c:if test="${requiredMap['zchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="zchargename"
                                                                name="tQhWfbuildprojectrocessEntity.zchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zchargeno']}" readonly
                                                                value="${tQhWfbuildprojectrocess.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectrocessEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zcchargeno"
                                                               name="tQhWfbuildprojectrocessEntity.zcchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zcchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.zcchargeno }"/><c:if test="${requiredMap['zcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="zcchargename"
                                                                name="tQhWfbuildprojectrocessEntity.zcchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zcchargeno']}" readonly
                                                                value="${tQhWfbuildprojectrocess.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處對應窗口
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(3,'hbchargeno','hbchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbchargeno"
                                                               name="tQhWfbuildprojectrocessEntity.hbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.hbchargeno }"/><c:if test="${requiredMap['hbchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hbchargename" name="tQhWfbuildprojectrocessEntity.hbchargename" readonly
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbchargeno']}" value="${tQhWfbuildprojectrocess.hbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="hbkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處課級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(4,'hbkchargeTable','hbkchargeno','hbkchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectrocessEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbkchargeno"
                                                               name="tQhWfbuildprojectrocessEntity.hbkchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbkchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.hbkchargeno }"/><c:if test="${requiredMap['hbkchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hbkchargename" name="tQhWfbuildprojectrocessEntity.hbkchargename"
                                                            class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbkchargeno']}" readonly
                                                            value="${tQhWfbuildprojectrocess.hbkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="hbbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處部級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(5,'hbbchargeTable','hbbchargeno','hbbchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectrocessEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbbchargeno"
                                                               name="tQhWfbuildprojectrocessEntity.hbbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbbchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.hbbchargeno }"/><c:if test="${requiredMap['hbbchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="hbbchargename"name="tQhWfbuildprojectrocessEntity.hbbchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbbchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.hbbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="hbcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處處級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(6,'hbcchargeTable','hbcchargeno','hbcchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectrocessEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbcchargeno"
                                                               name="tQhWfbuildprojectrocessEntity.hbcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbcchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.hbcchargeno }"/><c:if test="${requiredMap['hbcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="hbcchargename" name="tQhWfbuildprojectrocessEntity.hbcchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbcchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.hbcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zwchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">總務處對應窗口</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(7,'zwchargeno','zwchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zwchargeno"
                                                               name="tQhWfbuildprojectrocessEntity.zwchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['zwchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.zwchargeno }"/><c:if test="${requiredMap['zwchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="zwchargename" name="tQhWfbuildprojectrocessEntity.zwchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zwchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.zwchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zwbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">總務處部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(8,'zwbchargeTable','zwbchargeno','zwbchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectrocessEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zwbchargeno"
                                                               name="tQhWfbuildprojectrocessEntity.zwbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['zwbchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.zwbchargeno }"/><c:if test="${requiredMap['zwbchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                       /
                                                        <input id="zwbchargename" name="tQhWfbuildprojectrocessEntity.zwbchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zwbchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.zwbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zwcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">總務處處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(9,'zwcchargeTable','zwcchargeno','zwcchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectrocessEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zwcchargeno"
                                                               name="tQhWfbuildprojectrocessEntity.zwcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['zwcchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.zwcchargeno }"/><c:if test="${requiredMap['zwcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="zwcchargename" name="tQhWfbuildprojectrocessEntity.zwcchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zwcchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.zwcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zbjgckchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">周邊經管對應窗口
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(131,'zbjgckchargeTable','zbjgckchargeno','zbjgckchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectrocessEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zbjgckchargeno"
                                                               name="tQhWfbuildprojectrocessEntity.zbjgckchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zbjgckchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.zbjgckchargeno }"/><c:if test="${requiredMap['zbjgckchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="zbjgckchargename"
                                                                name="tQhWfbuildprojectrocessEntity.zbjgckchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zbjgckchargeno']}" readonly
                                                                value="${tQhWfbuildprojectrocess.zbjgckchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zbjgkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">周邊經管處課級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(10,'zbjgkchargeTable','zbjgkchargeno','zbjgkchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectrocessEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zbjgkchargeno" name="tQhWfbuildprojectrocessEntity.zbjgkchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['zbjgkchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.zbjgkchargeno }"/><c:if test="${requiredMap['zbjgkchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="zbjgkchargename" name="tQhWfbuildprojectrocessEntity.zbjgkchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zbjgkchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.zbjgkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zbjgbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">周邊經管處部級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(11,'zbjgbchargeTable','zbjgbchargeno','zbjgbchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectrocessEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zbjgbchargeno"
                                                               name="tQhWfbuildprojectrocessEntity.zbjgbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['zbjgbchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.zbjgbchargeno }"/><c:if test="${requiredMap['zbjgbchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="zbjgbchargename" name="tQhWfbuildprojectrocessEntity.zbjgbchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zbjgbchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.zbjgbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zbjgzgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">周邊處主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(132,'zbjgzgchargeTable','zbjgzgchargeno','zbjgzgchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectrocessEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zbjgzgchargeno"
                                                               name="tQhWfbuildprojectrocessEntity.zbjgzgchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['zbjgzgchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.zbjgzgchargeno }"/><c:if test="${requiredMap['zbjgzgchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="zbjgzgchargename"
                                                                name="tQhWfbuildprojectrocessEntity.zbjgzgchargename"
                                                                class="easyui-validatebox"
                                                                data-options="width: 80,required:${requiredMap['zbjgzgchargeno']}" readonly
                                                                value="${tQhWfbuildprojectrocess.zbjgzgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zbjgcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">周邊經管處處級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(12,'zbjgcchargeTable','zbjgcchargeno','zbjgcchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectrocessEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zbjgcchargeno"
                                                               name="tQhWfbuildprojectrocessEntity.zbjgcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['zbjgcchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.zbjgcchargeno }"/><c:if test="${requiredMap['zbjgcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="zbjgcchargename" name="tQhWfbuildprojectrocessEntity.zbjgcchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zbjgcchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.zbjgcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zgshchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">主管審核
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(274,'zgshchargeTable','zgshchargeno','zgshchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectrocessEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zgshchargeno"
                                                               name="tQhWfbuildprojectrocessEntity.zgshchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['zgshchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.zgshchargeno }"/><c:if test="${requiredMap['zgshchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="zgshchargename" name="tQhWfbuildprojectrocessEntity.zgshchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zgshchargeno']}"
                                                               value="${tQhWfbuildprojectrocess.zgshchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <%--<table width="18%" style="float: left;margin-left: 5px;" id="zbzchchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">核准</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(13,'zbzchchargeTable','zbzchchargeno','zbzchchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectrocessEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zbzchchargeno"
                                                               name="tQhWfbuildprojectrocessEntity.zbzchchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['zbzchchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.zbzchchargeno }"/><c:if test="${requiredMap['zbzchchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="zbzchchargename" name="tQhWfbuildprojectrocessEntity.zbzchchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zbzchchargeno']}"
                                                               value="${tQhWfbuildprojectrocess.zbzchchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>--%>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="jczhchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">權限主管核准</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(269,'jczhchargeTable','jczhchargeno','jczhchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectrocessEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="jczhchargeno"
                                                               name="tQhWfbuildprojectrocessEntity.jczhchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['jczhchargeno']}" readonly
                                                               value="${tQhWfbuildprojectrocess.jczhchargeno }"/><c:if test="${requiredMap['jczhchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="jczhchargename" name="tQhWfbuildprojectrocessEntity.jczhchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['jczhchargeno']}"
                                                               value="${tQhWfbuildprojectrocess.jczhchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                                <%--<a href="#" id="btnadd" class="easyui-linkbutton" iconCls="icon-add" plain="true" code="add">提交</a>--%>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
<script src='${ctx}/static/js/other/tqhwfbuildprojectrocess.js?random=<%= Math.random()%>'></script>
<script type="text/javascript">
    if ("${tQhWfbuildprojectrocess.hchargeno}" != "") {
        var nostr = "${tQhWfbuildprojectrocess.hchargeno}";
        var namestr = "${tQhWfbuildprojectrocess.hchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hchargeTable tr:eq(" + (i + 2) + ")").find("#hchargeno").val(notr[i]);
            $("#hchargeTable tr:eq(" + (i + 2) + ")").find("#hchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hchargeno' name='tQhWfbuildprojectrocessEntity.hchargeno' style='width: 80px' value='' onblur='getUserNameByEmpno(this,\"hcharge\");'/>/<input id='hchargename' name='tQhWfbuildprojectrocessEntity.hchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
</script>

</body>
</html>