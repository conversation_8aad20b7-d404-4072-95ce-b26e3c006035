<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>環保手續辦理委託申請單-詳細查詢頁面</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/other/tqhwfbuildprojectrocess.js?random=<%= Math.random()%>'></script>
    <style type="text/css">
        .spantype{
            word-break: break-all;
            font-size: 12px;
            color: #0f0f0f;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfbuildprojectrocess/${action}" method="post">
    <div class="commonW">
        <div id="mainDiv">
            <div class="headTitle">新增建設項目環保手續辦理委託申請單</div>
            <div class="position_L">
                任務編碼：
                <span id="" style="color:#999;">${tQhWfbuildprojectrocess.serialno}</span>
            </div>

            <div class="position_L1 margin_L">
                填單時間：<span style="color:#999;">
                <input class="inputCss" style="width: 100px" value="<fmt:formatDate value='${tQhWfbuildprojectrocess.createtime}' pattern='yyyy-MM-dd hh:mm'/>" >
            </span>
            </div>
            <div class="position_R margin_R">
                填單人：${tQhWfbuildprojectrocess.makerno}/${tQhWfbuildprojectrocess.makername}</div>
            <br>
            <div class="clear"></div>
            <table class="formList">
                <tr align="center">
                    <td>
                        <table>
                            <tr>
                                <td colspan="10" class="td_style1">承辦人詳細信息</td>
                            </tr>
                            <tr align="center">
                                <td width="6%">承辦人工號</td>
                                <td width="6%" class="td_style1">
                                    <input id="dealno" name="dealno" class="easyui-validatebox inputCss" readonly
                                           data-options="width: 80"
                                           value="${tQhWfbuildprojectrocess.dealno }"/>
                                </td>
                                <td width="6%">承辦人</td>
                                <td width="3%" class="td_style1">
                                    <input id="dealname" name="dealname" class="easyui-validatebox inputCss" readonly
                                           data-options="width:80" value="${tQhWfbuildprojectrocess.dealname }"/>
                                </td>
                                <td width="10%">單位代碼</td>
                                <td width="6%" class="td_style1">
                                    <input id="dealdeptno" name="dealdeptno" class="easyui-validatebox inputCss" readonly
                                           data-options="width: 90"
                                           value="${tQhWfbuildprojectrocess.dealdeptno }"/>
                                </td>
                                <td width="6%">提報日期</td>
                                <td width="6%" class="td_style1">
                                    <input id="dealtime" name="dealtime" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                           data-options="width: 100"
                                           value="<fmt:formatDate value="${tQhWfbuildprojectrocess.dealtime}"/>" disabled/>
                                </td>
                                <td width="6%">廠區</td>
                                <td width="6%" class="td_style1">
                                    <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox inputCss"
                                           panelHeight="auto" value="${tQhWfbuildprojectrocess.dealfactoryid }" disabled
                                           data-options="width: 120,required:true"/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>單位</td>
                                <td colspan="3" class="td_style1">
                                    <input id="dealdeptname" name="dealdeptname" class="easyui-validatebox inputCss" readonly
                                           data-options="width: 400"
                                           value="${tQhWfbuildprojectrocess.dealdeptname }"/>
                                </td>
                                <td>聯繫郵箱</td>
                                <td colspan="3" class="td_style1">
                                    <input id="dealemail" name="dealemail" class="easyui-validatebox inputCss" readonly
                                           value="${tQhWfbuildprojectrocess.dealemail }"
                                           data-options="width: 300"/>

                                </td>
                                <td>聯繫分機</td>
                                <td class="td_style1">
                                    <input id="dealtel" name="dealtel" class="easyui-validatebox inputCss" readonly
                                           style="width:80px;"
                                           value="${tQhWfbuildprojectrocess.dealtel }"/>

                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td>
                       <table class="formList">
                           <tr>
                               <td colspan="10" class="td_style1">項目基本信息</td>
                           </tr>
                           <tr align="center">
                               <td>項目名稱</td>
                               <td colspan="2" class="td_style1">
                                   <input id="projecname" name="projecname" class="easyui-validatebox inputCss" readonly
                                          data-options="width: 150" value="${tQhWfbuildprojectrocess.projecname }"/>
                               </td>
                               <td width="50px;">項目法人&nbsp;&nbsp;</td>
                               <td class="td_style1">
                                   <input id="projeccorporateid" name="projeccorporateid" class="easyui-combobox inputCss" disabled
                                          value="${tQhWfbuildprojectrocess.projeccorporateid }"
                                          data-options="width: 300,required:true"/>
                               </td>
                               <td>項目性質</td>
                               <td class="td_style1">
                                   <input id="projecnature" name="projecnature" class="easyui-combobox inputCss" disabled
                                          value="${tQhWfbuildprojectrocess.projecnature }" data-options="width: 120,required:true"/>
                               </td>
                               <td>建設規模</td>
                               <td colspan="2" class="td_style1">
                                   <input id="buildsize" name="buildsize" class="easyui-validatebox inputCss" readonly
                                          data-options="width: 150" value="${tQhWfbuildprojectrocess.buildsize }"/>
                               </td>
                           </tr>
                           <tr align="center">
                               <td>環境影響評價<br/>報告類型</td>
                               <td colspan="2" class="td_style1">
                                   <input id="reporttypeid" name="reporttypeid" class="easyui-combobox inputCss"
                                          disabled
                                          value="${tQhWfbuildprojectrocess.reporttypeid }"
                                          data-options="width: 100,required:true"/>
                               </td>
                               <td>建設<br/>總投資</td>
                               <td class="td_style1">
                                   <input id="buildtotalcost" name="buildtotalcost" class="easyui-validatebox inputCss"
                                          readonly
                                          data-options="width: 150" value="${tQhWfbuildprojectrocess.buildtotalcost }"/><span class="spantype">萬元</span>
                               </td>
                               <td>環保投資</td>
                               <td class="td_style1">
                                   <input id="enviromentalcost" name="enviromentalcost" class="easyui-validatebox inputCss"
                                          readonly
                                          data-options="width: 150" value="${tQhWfbuildprojectrocess.enviromentalcost }"/><span class="spantype">萬元</span>
                               </td>
                               <td>資金來源</td>
                               <td colspan="2" class="td_style1">
                                   <input id="fundsource" name="fundsource" class="easyui-validatebox inputCss" readonly
                                          data-options="width: 150" value="${tQhWfbuildprojectrocess.fundsource }"/>
                               </td>
                           </tr>
                           <%--<tr align="center">
                               <td>建設總投資<br/>(單位：萬元)&nbsp;<font color="red">*</font></td>
                               <td colspan="2" class="td_style1">
                                   <input id="buildtotalcost" name="buildtotalcost" class="easyui-validatebox inputCss" readonly
                                          data-options="width: 150" value="${tQhWfbuildprojectrocess.buildtotalcost }"/>
                               </td>
                               <td>環保投資<br/>(單位：萬元)&nbsp;<font color="red">*</font></td>
                               <td colspan="2" class="td_style1">
                                   <input id="enviromentalcost" name="enviromentalcost" class="easyui-validatebox inputCss" readonly
                                          data-options="width: 150" value="${tQhWfbuildprojectrocess.enviromentalcost }"/>
                               </td>
                               <td colspan="2">資金來源&nbsp;<font color="red">*</font></td>
                               <td colspan="2" class="td_style1">
                                   <input id="fundsource" name="fundsource" class="easyui-validatebox inputCss" readonly
                                          data-options="width: 150" value="${tQhWfbuildprojectrocess.fundsource }"/>
                               </td>
                           </tr>--%>
                           <tr align="center">
                               <td>規劃人力<br/>(單位：人)</td>
                               <td colspan="2" class="td_style1">
                                   <input id="planperson" name="planperson" class="easyui-validatebox inputCss" readonly
                                          data-options="width: 150" value="${tQhWfbuildprojectrocess.planperson }"/>
                               </td>
                               <td>建設週期</td>
                               <td colspan="2" class="td_style1">
                                   <input id="buildcyclebegindate" name="buildcyclebegindate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd" disabled
                                          data-options="width: 100" value="<fmt:formatDate  value="${tQhWfbuildprojectrocess.buildcyclebegindate}"/>"/>~
                                   <input id="buildcycleenddate" name="buildcycleenddate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd" disabled
                                          data-options="width: 100" value="<fmt:formatDate  value="${tQhWfbuildprojectrocess.buildcycleenddate}"/>"/>
                               </td>
                               <td colspan="2">計劃開工日期</td>
                               <td colspan="2" class="td_style1">
                                   <input id="planstartdate" name="planstartdate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd" disabled
                                          data-options="width: 100"
                                          value="<fmt:formatDate value="${tQhWfbuildprojectrocess.planstartdate}"/>"/>
                               </td>
                           </tr>
                           <tr align="center">
                               <td>建設地點</td>
                               <td colspan="4" class="td_style1">
                                   <input id="buildsite" name="buildsite" class="easyui-validatebox inputCss" readonly
                                          style="width:300px;"
                                          value="${tQhWfbuildprojectrocess.buildsite }"/>
                               </td>
                               <td>生產工藝</td>
                               <td colspan="4" class="td_style1">
                                   <input id="produceart" name="produceart" class="easyui-validatebox inputCss" readonly
                                          style="width:300px;"
                                          value="${tQhWfbuildprojectrocess.produceart }"/>
                               </td>
                           </tr>
                           <tr align="center">
                               <td>項目主要建設內容</td>
                               <td colspan="9" class="td_style1">
                        <textarea id="projectbuildcontent" name="projectbuildcontent"
                                  class="easyui-validatebox inputCss" style="width:99%;height:80px;"
                                  rows="5" cols="6" readonly>${tQhWfbuildprojectrocess.projectbuildcontent }</textarea>
                               </td>
                           </tr>
                           <tr align="center">
                               <td>排污環節及防治措施</td>
                               <td colspan="9">
                                   <table style="width:100%;">
                                       <tr>
                                           <td style="border:none;border-bottom: 1px solid #138CDD;">
                                               <input id="dischargetype" type="hidden" name="tQhWfbuildprojectrocess.dischargetype"
                                                      value="${tQhWfbuildprojectrocess.dischargetype }"/>
                                               <div class="discharge">

                                               </div>
                                           </td>
                                       </tr>
                                       <tr>
                                           <td style="border:none">
                                    <textarea id="dischargelink" name="dischargelink"
                                              class="easyui-validatebox inputCss" style="width:99%;height:80px;"
                                              rows="5" cols="6" readonly>${tQhWfbuildprojectrocess.dischargelink }</textarea>
                                           </td>
                                       </tr>
                                   </table>
                               </td>
                           </tr>
                           <tr align="center">
                               <td>附件</td>
                               <td colspan="9" class="td_style1">
                                   <div id="dowloadUrl">
                                       <c:forEach items="${file}" varStatus="i" var="item">
                                           <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                               <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                           </div>
                                       </c:forEach>
                                   </div>
                               </td>
                           </tr>
                           <tr align="center">
                               <td>委託事項說明</td>
                               <td colspan="9" class="td_style1">
                        <textarea id="entrustexplain" name="entrustexplain"
                                  class="easyui-validatebox inputCss" style="width:99%;height:80px;"
                                  rows="5" cols="6" readonly>${tQhWfbuildprojectrocess.entrustexplain }</textarea>
                               </td>
                           </tr>
                           <tr align="center">
                               <td colspan="10" width="100%">
                                   <div style="overflow-x: auto;width: 100%;padding-bottom: 15px;">
                                       <table id="buildprojectCostItemTable" width="100%">
                                           <tr align="center">
                                               <td>序號</td>
                                               <td>費用掛靠單位</td>
                                               <td>費用代碼</td>
                                               <td>費用佔比</td>
                                           </tr>
                                           <c:if test="${itemEntity!=null&&itemEntity.size()>0}">
                                               <c:forEach items="${itemEntity}" varStatus="i" var="item">
                                                   <tr align="center">
                                                       <td>${i.index+1}</td>
                                                       <td>${item.costname}</td>
                                                       <td>${item.costno}</td>
                                                       <td>${item.costrate}</td>
                                                   </tr>
                                               </c:forEach>
                                           </c:if>
                                       </table>
                                   </div>
                               </td>
                           </tr>
                           <tr align="center">
                               <td>附件&nbsp;（補充說明）</td>
                               <td colspan="9" class="td_style1">
                                   <%--<span class="sl-custom-file">--%>
                                   <%--<a href="${ctx}/admin/download/${file.id}">${file.name}</a>--%>
                                   <%--</span>--%>
                                   <div id="dowloadUrl">
                                       <c:forEach items="${reFile}" varStatus="i" var="item">
                                           <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                               <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                           </div>
                                       </c:forEach>
                                   </div>
                               </td>
                           </tr>

                           <tr>
                               <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                   <a href="javascript:void(0)" onclick="showWfImag('${processId}','系統上線申請流程圖');">點擊查看簽核流程圖</a>
                               </th>
                           </tr>
                           <tr>
                               <td colspan="10" style="text-align:left;">
                                   ${chargeNodeInfo}
                               </td>
                           </tr>

                           <tr>
                               <td colspan="10" style="text-align:left;">
                                   <iframe id="qianheLogFrame" name="qianheLogFrame"
                                           src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhWfbuildprojectrocess.serialno}"
                                           width="100%"></iframe>
                               </td>
                           </tr>
                           <tr class="no-print">
                               <td colspan="10" style="text-align:center;padding-left:10px;">
                                   <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                      data-options="iconCls:'icon-cancel'"
                                      style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                   <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                      style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>

                               </td>
                           </tr>
                       </table>
                    </td>


                </tr>





            </table>
        </div>
    </div>
</form>
<div id="dlg"></div>
</body>
</html>