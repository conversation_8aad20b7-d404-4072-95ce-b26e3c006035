<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>環保手續辦理委託申請單審核</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/other/tqhwfbuildprojectrocess.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfbuildprojectrocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhWfbuildprojectrocess.id }"/>
    <div class="commonW">
        <div class="headTitle">新增建設項目環保手續辦理委託申請單</div>
        <div class="position_L">
            任務編碼：<span id="" style="color:#999;">${tQhWfbuildprojectrocess.serialno}</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
            <input class="inputCss" style="width: 100px"
                   value="<fmt:formatDate value='${tQhWfbuildprojectrocess.createtime}' pattern='yyyy-MM-dd hh:mm'/>">
            </span>
        </div>

        <div class="position_R margin_R">
            填單人：${tQhWfbuildprojectrocess.makerno}/${tQhWfbuildprojectrocess.makername}</div>
        <br>
        <div class="clear"></div>
        <table class="formList">
            <tr align="center">
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">承辦人工號</td>
                            <td width="6%" class="td_style2">${tQhWfbuildprojectrocess.dealno }</td>
                            <td width="6%">承辦人</td>
                            <td width="6%" class="td_style2">${tQhWfbuildprojectrocess.dealname }</td>
                            <td width="6%">單位代碼</td>
                            <td width="6%" class="td_style2">${tQhWfbuildprojectrocess.dealdeptno }</td>
                            <td width="6%">提報日期</td>
                            <td width="6%" class="td_style2"><fmt:formatDate value="${tQhWfbuildprojectrocess.dealtime}"/></td>
                            <td width="6%">廠區</td>
                            <td width="6%" class="td_style1">
                                <input id="dealfactoryid" class="easyui-combobox inputCss" panelHeight="auto" value="${tQhWfbuildprojectrocess.dealfactoryid }" disabled
                                       data-options="width: 120"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="3" class="td_style2">${tQhWfbuildprojectrocess.dealdeptname }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${tQhWfbuildprojectrocess.dealemail }</td>
                            <td>聯繫分機</td>
                            <td class="td_style2">${tQhWfbuildprojectrocess.dealtel }</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr align="center">
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">項目基本信息</td>
                        </tr>
                        <tr align="center">
                        <td>項目名稱</td>
                        <td colspan="2" class="td_style2">${tQhWfbuildprojectrocess.projecname }</td>
                        <td>項目法人</td>
                        <td class="td_style1">
                            <input id="projeccorporateid" class="easyui-combobox inputCss" disabled value="${tQhWfbuildprojectrocess.projeccorporateid }"
                                   data-options="width: 300"/>
                        </td>
                        <td>項目性質</td>
                        <td class="td_style1">
                            <input id="projecnature" class="easyui-combobox inputCss" disabled value="${tQhWfbuildprojectrocess.projecnature }"
                                   data-options="width: 120"/>
                        </td>
                        <td>建設規模</td>
                        <td colspan="2" class="td_style2">${tQhWfbuildprojectrocess.buildsize }</td>
                        </tr>
                        <tr align="center">
                            <td>環境影響評價<br/>報告類型</td>
                            <td colspan="2" class="td_style1">
                                <input id="reporttypeid" class="easyui-combobox inputCss" disabled value="${tQhWfbuildprojectrocess.reporttypeid }"
                                       data-options="width: 100"/>
                            </td>
                            <td>建設<br/>總投資</td>
                            <td class="td_style2">
                                ${tQhWfbuildprojectrocess.buildtotalcost}<span class="spantype">萬元</span>
                            </td>
                            <td>環保投資</td>
                            <td class="td_style2">
                               ${tQhWfbuildprojectrocess.enviromentalcost }<span class="spantype">萬元</span>
                            </td>
                            <td>資金來源</td>
                            <td colspan="2" class="td_style2">${tQhWfbuildprojectrocess.fundsource }</td>
                        </tr>
                        <%--<tr align="center">
                            <td>建設總投資<br/>(單位：萬元)&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="buildtotalcost" name="buildtotalcost" class="easyui-validatebox inputCss"
                                       readonly
                                       data-options="width: 150" value="${tQhWfbuildprojectrocess.buildtotalcost }"/>
                            </td>
                            <td>環保投資<br/>(單位：萬元)&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="enviromentalcost" name="enviromentalcost" class="easyui-validatebox inputCss"
                                       readonly
                                       data-options="width: 150" value="${tQhWfbuildprojectrocess.enviromentalcost }"/>
                            </td>
                            <td colspan="2">資金來源&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="fundsource" name="fundsource" class="easyui-validatebox inputCss" readonly
                                       data-options="width: 150" value="${tQhWfbuildprojectrocess.fundsource }"/>
                            </td>
                        </tr>--%>
                        <tr align="center">
                            <td>規劃人力<br/>(單位：人)</td>
                            <td colspan="2" class="td_style2">${tQhWfbuildprojectrocess.planperson }</td>
                            <td>建設週期</td>
                            <td colspan="2" class="td_style2">
                                <fmt:formatDate  value="${tQhWfbuildprojectrocess.buildcyclebegindate}"/>~
                                <fmt:formatDate  value="${tQhWfbuildprojectrocess.buildcycleenddate}"/>
                            </td>
                            <td colspan="2">計劃開工日期</td>
                            <td colspan="2" class="td_style2"><fmt:formatDate value="${tQhWfbuildprojectrocess.planstartdate}"/></td>
                        </tr>
                        <tr align="center">
                            <td>建設地點</td>
                            <td colspan="4" class="td_style2">${tQhWfbuildprojectrocess.buildsite }</td>
                            <td>生產工藝</td>
                            <td colspan="4" class="td_style2">${tQhWfbuildprojectrocess.produceart }</td>
                        </tr>
                        <tr align="center">
                            <td>項目主要建設內容</td>
                            <td colspan="9">
                                <textarea id="projectbuildcontent" class="easyui-validatebox inputCss" style="width:99%;height:80px;"
                              rows="5" cols="6" readonly>${tQhWfbuildprojectrocess.projectbuildcontent }</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>排污環節及防治措施</td>
                            <td colspan="9" class="td_style2">
                                <table style="width:100%;">
                                    <tr>
                                        <td style="border:none;border-bottom: 1px solid #138CDD;">
                                            <input id="dischargetype" type="hidden"
                                                   name="tQhWfbuildprojectrocess.dischargetype"
                                                   value="${tQhWfbuildprojectrocess.dischargetype }"/>
                                            <div class="discharge">

                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <textarea id="dischargelink" class="easyui-validatebox inputCss" style="width:99%;height:80px;" rows="5" cols="6" readonly>${tQhWfbuildprojectrocess.dischargelink }</textarea>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <div>
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L"><a
                                                    href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>委託事項說明</td>
                            <td colspan="9" class="td_style1">
                                <textarea id="entrustexplain" class="easyui-validatebox inputCss" style="width:99%;height:80px;" rows="5" cols="6" readonly>${tQhWfbuildprojectrocess.entrustexplain }</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 100%;padding-bottom: 15px;">
                                    <table id="buildprojectCostItemTable" width="100%">
                                        <tr align="center">
                                            <td>序號</td>
                                            <td>費用掛靠單位</td>
                                            <td>費用代碼</td>
                                            <td>費用佔比</td>
                                        </tr>
                                        <c:if test="${itemEntity!=null&&itemEntity.size()>0}">
                                            <c:forEach items="${itemEntity}" varStatus="i" var="item">
                                                <tr align="center">
                                                    <td>${i.index+1}</td>
                                                    <td>${item.costname}</td>
                                                    <td>${item.costno}</td>
                                                    <td>${item.costrate}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>批註</td>
                            <td colspan="9" style="text-align:left;">
						        <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox" style="width:1000px;height:60px;"
                                  rows="4" cols="4" value="${tQhWfbuildprojectrocess.entrustexplain }"></textarea>
                            </td>
                        </tr>

                        <c:if test="${not empty nodeName&&'環保科技處對應窗口' eq nodeName}">
                            <tr>
                                <td>附件&nbsp;（補充說明）</td>
                                <td colspan="9" class="td_style1">
                                    <span class="sl-custom-file">
						                <input type="button" value="点击上传文件" class="btn-file"/>
						                <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file">
						            </span>
                                    <input type="hidden" id="attachids" name="reattachids" value="${tQhWfbuildprojectrocessEntity.reattachids}"/>
                                    <div id="dowloadUrl"></div>
                                </td>
                            </tr>
                        </c:if>
                        <c:if test="${tQhWfbuildprojectrocess.reattachids!=null}">
                            <tr align="center">
                                <td>附件&nbsp;（補充說明）</td>
                                <td colspan="9" class="td_style1">
                                    <div>
                                        <c:forEach items="${reFile}" varStatus="i" var="item">
                                            <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                                <div class="float_L"><a
                                                        href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                                </div>
                                            </div>
                                        </c:forEach>
                                    </div>
                                </td>
                            </tr>
                        </c:if>

                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <c:choose>
                                    <c:when test="${not empty nodeName && '環保科技處對應窗口' eq nodeName}">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" perCall="audiPrValid" serialNo="${tQhWfbuildprojectrocess.serialno}"></fox:action>
                                    </c:when>
                                    <c:otherwise>
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" serialNo="${tQhWfbuildprojectrocess.serialno}"></fox:action>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','添加新增建設項目環保手續辦理委託申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhWfbuildprojectrocess.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
        </table>
    </div>
</form>
<input type="hidden" id="currentNodeOrder" value="${nodeOrder}" />
<div id="dlg"></div>
</body>
</html>