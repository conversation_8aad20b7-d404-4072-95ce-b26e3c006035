<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>建設項目環保變更報備申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/other/tqhwfbuildprojectchange.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfbuildprojectchange/${action}" method="post">
    <div class="commonW">
        <input id="ids" name="ids" type="hidden" value="${tQhWfbuildprojectchange.id }"/>
        <input id="serialno" name="tQhWfbuildprojectchangeEntity.serialno" type="hidden"
               value="${tQhWfbuildprojectchange.serialno }"/>
        <input id="makerno" name="tQhWfbuildprojectchangeEntity.makerno" type="hidden"
               value="${tQhWfbuildprojectchange.makerno }"/>
        <input id="makername" name="tQhWfbuildprojectchangeEntity.makername" type="hidden"
               value="${tQhWfbuildprojectchange.makername }"/>
        <input id="makerdeptno" name="tQhWfbuildprojectchangeEntity.makerdeptno" type="hidden"
               value="${tQhWfbuildprojectchange.makerdeptno }"/>
        <input id="makerfactoryid" name="tQhWfbuildprojectchangeEntity.makerfactoryid" type="hidden"
               value="${tQhWfbuildprojectchange.makerfactoryid }"/>
        <div class="headTitle">建設項目環保變更報備申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">      <c:choose>
            <c:when test="${tQhWfraydevicehbprocess.serialno==null}">
                提交成功后自動編碼
            </c:when>
            <c:otherwise>
                ${tQhWfraydevicehbprocess.serialno}
            </c:otherwise>
        </c:choose></span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;"> <c:choose>
            <c:when test="${tQhWfraydevicehbprocess.createtime==null}">
                YYYY/MM/DD
            </c:when>
            <c:otherwise>
                ${tQhWfraydevicehbprocess.createtime}
            </c:otherwise>
        </c:choose>
        </span>
        </div>
        <c:if test="${empty tQhWfraydevicehbprocess.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty tQhWfraydevicehbprocess.makerno}">
            <div class="position_R margin_R">
                填單人：${tQhWfraydevicehbprocess.makerno}/${tQhWfraydevicehbprocess.makername}</div>
        </c:if>
        <br>
        <div class="clear"></div>
        <table class="formList" id="buildprojecttable">
            <tr>
                <td>
                    <table class="formList">
                        <th style="text-align:left;" colspan="10">&nbsp;&nbsp;承辦人詳細信息
                        </th>
                        <tr align="center">
                            <td width="6%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealno" name="tQhWfbuildprojectchangeEntity.dealno"
                                       class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${tQhWfbuildprojectchange.dealno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="6%">承辦人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="tQhWfbuildprojectchangeEntity.dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${tQhWfbuildprojectchange.dealname }"/>
                            </td>
                            <td width="6%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="tQhWfbuildprojectchangeEntity.dealdeptno"
                                       class="easyui-validatebox inputCss"
                                       data-options="width: 90"
                                       readonly value="${tQhWfbuildprojectchange.dealdeptno }"/>
                            </td>
                            <td width="6%">提報日期&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealtime" name="tQhWfbuildprojectchangeEntity.dealtime" class="easyui-my97"
                                       datefmt="yyyy-MM-dd" data-options="width: 100,
                    required:true" value="<fmt:formatDate value="${tQhWfbuildprojectchange.dealtime}"/>"/>
                            </td>
                            <td width="6%">廠區&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealfactoryid" name="tQhWfbuildprojectchangeEntity.dealfactoryid"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${tQhWfbuildprojectchange.dealfactoryid }"
                                       data-options="width: 80,required:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="3" class="td_style1">
                                <input id="dealdeptname" name="tQhWfbuildprojectchangeEntity.dealdeptname"
                                       class="easyui-validatebox"
                                       data-options="width: 350"
                                       value="${tQhWfbuildprojectchange.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="tQhWfbuildprojectchangeEntity.dealemail"
                                       class="easyui-validatebox"
                                       value="${tQhWfbuildprojectchange.dealemail }" style="width:300px;"
                                       data-options="required:true" onblur="valdEmail(this)"/>
                            </td>
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealtel" name="tQhWfbuildprojectchangeEntity.dealtel"
                                       class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${tQhWfbuildprojectchange.dealtel }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <th style="text-align:left;" colspan="10">&nbsp;&nbsp;項目基本信息
                            <tr align="center">
                                <td>項目名稱&nbsp;<font color="red">*</font></td>
                                <td colspan="2" class="td_style1">
                                    <input id="projectname" name="tQhWfbuildprojectchangeEntity.projectname"
                                           class="easyui-validatebox" style="width:150px;"
                                           data-options="required:true"
                                           value="${tQhWfbuildprojectchange.projectname }"/>

                                </td>
                                <td>項目法人&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input id="projectcorporateid"
                                           name="tQhWfbuildprojectchangeEntity.projectcorporateid"
                                           class="easyui-combobox" style="width:300px;"
                                           panelHeight="auto" editable="false"
                                           value="${tQhWfbuildprojectchange.projectcorporateid }"/>
                                </td>
                                <td>項目階段&nbsp;<font color="red">*</font></td>
                                <td colspan="4">
                                    <%--<input id="projectstage" name="tQhWfbuildprojectchangeEntity.projectstage" class="easyui-validatebox" data-options="width: 150"--%>
                                    <%--value="${tQhWfbuildprojectchange.projectstage }"/>--%>
                                    <%--初步規劃、可研立項、環評編制、竣工驗收、正常運行--%>
                                    <div class="projectstageDiv">
                                    </div>
                                    <input id="projectstage" name="tQhWfbuildprojectchangeEntity.projectstage"
                                           type="hidden" class="easyui-validatebox"
                                           value="${tQhWfbuildprojectchange.projectstage }"/>
                                    <input id="disOrEnabled" type="hidden" value="enabled"/>

                                </td>

                            </tr>

                        <th style="text-align:left;" colspan="10">&nbsp;&nbsp;項目變更內容
                        </th>

                        <tr align="center">
                            <td colspan="2">項目</td>
                            <td colspan="2">是否發生變更</td>
                            <td colspan="3">變更前</td>
                            <td colspan="3">變更后</td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">項目名稱</td>
                            <td colspan="2">
                                <%--<input id="projectnameIfChange" name="tQhWfbuildprojectchangeEntity.projectnameIfChange" class="easyui-validatebox"--%>
                                <%--data-options="width: 150" value="${tQhWfbuildprojectchange.projectnameIfChange }"/>--%>

                                <div class="projectnameIfChangeDiv"></div>
                                <input id="projectnameIfChange" name="tQhWfbuildprojectchangeEntity.projectnameIfChange"
                                       type="hidden" class="easyui-validatebox"
                                       value="${tQhWfbuildprojectchange.projectnameIfChange}"/>
                                <input id="disOrEnabledIF" type="hidden" value="enabled"/>

                            </td>
                            <td colspan="3">
                                <input id="projectnameOld" name="tQhWfbuildprojectchangeEntity.projectnameOld"
                                       class="easyui-textbox" style="width:350px"
                                       disabled="true"
                                       value="${tQhWfbuildprojectchange.projectnameOld }"/></td>
                            <td colspan="3">
                                <input id="projectnameNew" name="tQhWfbuildprojectchangeEntity.projectnameNew"
                                       class="easyui-textbox" style="width:350px"
                                       disabled="true"
                                       value="${tQhWfbuildprojectchange.projectnameNew }"/></td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">建設法人</td>
                            <td colspan="2">
                                <%--<input type="radio" name="tQhWfbuildprojectchangeEntity.buildcorporateIfChange" value="1"--%>
                                <%--onchange="changeLeve('buildcorporateOld','buildcorporateNew','1');">是</input>--%>
                                <%--<input type="radio" name="tQhWfbuildprojectchangeEntity.buildcorporateIfChange" value="0"--%>
                                <%--checked="true"--%>
                                <%--onchange="changeLeve('buildcorporateOld','buildcorporateNew','0');">否</input>--%>

                                <div class="buildcorporateIfChangeDiv"></div>
                                <input id="buildcorporateIfChange"
                                       name="tQhWfbuildprojectchangeEntity.buildcorporateIfChange"
                                       type="hidden" class="easyui-validatebox"
                                       value="${tQhWfbuildprojectchange.buildcorporateIfChange}"/>
                                <%--<input id="disOrEnabledIF" type="hidden" value="enabled"/>--%>

                            </td>
                            <td colspan="3">
                                <input id="buildcorporateOld" name="tQhWfbuildprojectchangeEntity.buildcorporateOld"
                                       class="easyui-textbox" style="width:350px"
                                       disabled="true"
                                       value="${tQhWfbuildprojectchange.buildcorporateOld }"/>
                            </td>
                            <td colspan="3">
                                <input id="buildcorporateNew" name="tQhWfbuildprojectchangeEntity.buildcorporateNew"
                                       class="easyui-textbox" style="width:350px"
                                       disabled="true"
                                       value="${tQhWfbuildprojectchange.buildcorporateNew }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">項目性質</td>
                            <td colspan="2">
                                <%--<input id="projectnatureIfChange" name="tQhWfbuildprojectchangeEntity.projectnatureIfChange" class="easyui-validatebox"--%>
                                <%--data-options="width: 150" value="${tQhWfbuildprojectchange.projectnatureIfChange }"/>--%>
                                <%--<input type="radio" name="tQhWfbuildprojectchangeEntity.projectnatureIfChange" value="1"--%>
                                <%--onchange="changeLeve('projectnatureOld','projectnatureNew','1');">是</input>--%>
                                <%--<input type="radio" name="tQhWfbuildprojectchangeEntity.projectnatureIfChange" value="0"--%>
                                <%--checked="true"--%>
                                <%--onchange="changeLeve('projectnatureOld','projectnatureNew','0');">否</input>--%>

                                <div class="projectnatureIfChangeDiv"></div>
                                <input id="projectnatureIfChange"
                                       name="tQhWfbuildprojectchangeEntity.projectnatureIfChange"
                                       type="hidden" class="easyui-validatebox"
                                       value="${tQhWfbuildprojectchange.projectnatureIfChange}"/>
                                <%--<input id="disOrEnabledIF" type="hidden" value="enabled"/>--%>

                            </td>
                            <td colspan="3">
                                <input id="projectnatureOld" name="tQhWfbuildprojectchangeEntity.projectnatureOld"
                                       class="easyui-textbox" style="width:350px"
                                       disabled="true"
                                       value="${tQhWfbuildprojectchange.projectnatureOld }"/></td>
                            <td colspan="3">
                                <input id="projectnatureNew" name="tQhWfbuildprojectchangeEntity.projectnatureNew"
                                       class="easyui-textbox" style="width:350px"
                                       disabled="true"
                                       value="${tQhWfbuildprojectchange.projectnatureNew }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">建設規模</td>
                            <td colspan="2">
                                <%--<input id="buildsizeIfChange" name="tQhWfbuildprojectchangeEntity.buildsizeIfChange" class="easyui-validatebox"--%>
                                <%--data-options="width: 150" value="${tQhWfbuildprojectchange.buildsizeIfChange }"/>--%>
                                <%--<input type="radio" name="tQhWfbuildprojectchangeEntity.buildsizeIfChange" value="1"--%>
                                <%--onchange="changeLeve('buildsizeOld','buildsizeNew','1');">是</input>--%>
                                <%--<input type="radio" name="tQhWfbuildprojectchangeEntity.buildsizeIfChange" value="0" checked="true"--%>
                                <%--onchange="changeLeve('buildsizeOld','buildsizeNew','0');">否</input>--%>

                                <div class="buildsizeIfChangeDiv"></div>
                                <input id="buildsizeIfChange" name="tQhWfbuildprojectchangeEntity.buildsizeIfChange"
                                       type="hidden" class="easyui-validatebox"
                                       value="${tQhWfbuildprojectchange.buildsizeIfChange}"/>
                                <%--<input id="disOrEnabledIF" type="hidden" value="enabled"/>--%>
                            </td>
                            <td colspan="3">
                                <input id="buildsizeOld" name="tQhWfbuildprojectchangeEntity.buildsizeOld"
                                       class="easyui-textbox"
                                       style="width:350px"
                                       disabled="true"
                                       value="${tQhWfbuildprojectchange.buildsizeOld }"/></td>
                            <td colspan="3">
                                <input id="buildsizeNew" name="tQhWfbuildprojectchangeEntity.buildsizeNew"
                                       class="easyui-textbox"
                                       style="width:350px"
                                       disabled="true"
                                       value="${tQhWfbuildprojectchange.buildsizeNew }"/></td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">建設地點</td>
                            <td colspan="2">
                                <%--<input type="radio" name="tQhWfbuildprojectchangeEntity.buildsiteIfChange" value="1"--%>
                                <%--onchange="changeLeve('buildsiteOld','buildsiteNew','1');">是</input>--%>
                                <%--<input type="radio" name="tQhWfbuildprojectchangeEntity.buildsiteIfChange" value="0" checked="true"--%>
                                <%--onchange="changeLeve('buildsiteOld','buildsiteNew','0');">否</input>--%>

                                <div class="buildsiteIfChangeDiv"></div>
                                <input id="buildsiteIfChange" name="tQhWfbuildprojectchangeEntity.buildsiteIfChange"
                                       type="hidden" class="easyui-validatebox"
                                       value="${tQhWfbuildprojectchange.buildsiteIfChange}"/>
                                <%--<input id="disOrEnabledIF" type="hidden" value="enabled"/>--%>
                            </td>
                            <td colspan="3">
                                <input id="buildsiteOld" name="tQhWfbuildprojectchangeEntity.buildsiteOld"
                                       class="easyui-textbox"
                                       style="width:350px"
                                       disabled="true"
                                       value="${tQhWfbuildprojectchange.buildsiteOld }"/></td>
                            <td colspan="3">
                                <input id="buildsiteNew" name="tQhWfbuildprojectchangeEntity.buildsiteNew"
                                       class="easyui-textbox"
                                       style="width:350px"
                                       disabled="true"
                                       value="${tQhWfbuildprojectchange.buildsiteNew }"/></td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">總投資及環保投資</td>
                            <td colspan="2">
                                <%--<input id="buildtotalcostIfChange" name="tQhWfbuildprojectchangeEntity.buildtotalcostIfChange" class="easyui-validatebox"--%>
                                <%--data-options="width: 150" value="${tQhWfbuildprojectchange.buildtotalcostIfChange }"/>--%>
                                <%--<input type="radio" name="tQhWfbuildprojectchangeEntity.buildtotalcostIfChange" value="1"--%>
                                <%--onchange="changeLeve('buildtotalcostOld','buildtotalcostNew','1');">是</input>--%>
                                <%--<input type="radio" name="tQhWfbuildprojectchangeEntity.buildtotalcostIfChange" value="0"--%>
                                <%--checked="true"--%>
                                <%--onchange="changeLeve('buildtotalcostOld','buildtotalcostNew','0');">否</input>--%>

                                <div class="buildtotalcostIfChangeDiv"></div>
                                <input id="buildtotalcostIfChange"
                                       name="tQhWfbuildprojectchangeEntity.buildtotalcostIfChange"
                                       type="hidden" class="easyui-validatebox"
                                       value="${tQhWfbuildprojectchange.buildtotalcostIfChange}"/>
                                <%--<input id="disOrEnabledIF" type="hidden" value="enabled"/>--%>

                            </td>
                            <td colspan="3">
                                <input id="buildtotalcostOld" name="tQhWfbuildprojectchangeEntity.buildtotalcostOld"
                                       class="easyui-textbox" style="width:350px"
                                       disabled="true"
                                       value="${tQhWfbuildprojectchange.buildtotalcostOld }"/></td>
                            <td colspan="3">
                                <input id="buildtotalcostNew" name="tQhWfbuildprojectchangeEntity.buildtotalcostNew"
                                       class="easyui-textbox" style="width:350px"
                                       disabled="true"
                                       value="${tQhWfbuildprojectchange.buildtotalcostNew }"/></td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">投資方式</td>
                            <td colspan="2">
                                <%--<input id="investmodeIfChange" name="tQhWfbuildprojectchangeEntity.investmodeIfChange" class="easyui-validatebox"--%>
                                <%--data-options="width: 150" value="${tQhWfbuildprojectchange.investmodeIfChange }"/>--%>
                                <%--<input type="radio" name="tQhWfbuildprojectchangeEntity.investmodeIfChange" value="1"--%>
                                <%--onchange="changeLeve('investmodeOld','investmodeNew','1');">是</input>--%>
                                <%--<input type="radio" name="tQhWfbuildprojectchangeEntity.investmodeIfChange" value="0" checked="true"--%>
                                <%--onchange="changeLeve('investmodeOld','investmodeNew','0');">否</input>--%>

                                <div class="investmodeIfChangeDiv"></div>
                                <input id="investmodeIfChange" name="tQhWfbuildprojectchangeEntity.investmodeIfChange"
                                       type="hidden" class="easyui-validatebox"
                                       value="${tQhWfbuildprojectchange.investmodeIfChange}"/>
                                <%--<input id="disOrEnabledIF" type="hidden" value="enabled"/>--%>
                            </td>
                            <td colspan="3">
                                <input id="investmodeOld" name="tQhWfbuildprojectchangeEntity.investmodeOld"
                                       class="easyui-textbox"
                                       style="width:350px"
                                       disabled="true"
                                       value="${tQhWfbuildprojectchange.investmodeOld }"/></td>
                            <td colspan="3">
                                <input id="investmodeNew" name="tQhWfbuildprojectchangeEntity.investmodeNew"
                                       class="easyui-textbox"
                                       style="width:350px"
                                       disabled="true"
                                       value="${tQhWfbuildprojectchange.investmodeNew }"/></td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">項目主要建設內容</td>
                            <td colspan="2">
                                <%--<input id="projectbuildcontentIfChange" name="tQhWfbuildprojectchangeEntity.projectbuildcontentIfChange"--%>
                                <%--class="easyui-validatebox"--%>
                                <%--data-options="width: 150" value="${tQhWfbuildprojectchange.projectbuildcontentIfChange }"/>--%>
                                <%--<input type="radio" name="tQhWfbuildprojectchangeEntity.projectbuildcontentIfChange" value="1"--%>
                                <%--onchange="changeLeve('projectbuildcontentOld','projectbuildcontentNew','1');">是</input>--%>
                                <%--<input type="radio" name="tQhWfbuildprojectchangeEntity.projectbuildcontentIfChange" value="0"--%>
                                <%--checked="true"--%>
                                <%--onchange="changeLeve('projectbuildcontentOld','projectbuildcontentNew','0');">否</input>--%>

                                <div class="projectbuildcontentIfChangeDiv"></div>
                                <input id="projectbuildcontentIfChange"
                                       name="tQhWfbuildprojectchangeEntity.projectbuildcontentIfChange"
                                       type="hidden" class="easyui-validatebox"
                                       value="${tQhWfbuildprojectchange.projectbuildcontentIfChange}"/>
                                <%--<input id="disOrEnabledIF" type="hidden" value="enabled"/>--%>

                            </td>
                            <td colspan="3">
                                <input id="projectbuildcontentOld"
                                       name="tQhWfbuildprojectchangeEntity.projectbuildcontentOld"
                                       class="easyui-textbox"
                                       style="width:350px" disabled="true"
                                       value="${tQhWfbuildprojectchange.projectbuildcontentOld }"/></td>
                            <td colspan="3">
                                <input id="projectbuildcontentNew"
                                       name="tQhWfbuildprojectchangeEntity.projectbuildcontentNew"
                                       class="easyui-textbox"
                                       style="width:350px" disabled="true"
                                       value="${tQhWfbuildprojectchange.projectbuildcontentNew }"/></td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">生產工藝</td>
                            <td colspan="2">
                                <%--<input id="producteartIfChange" name="tQhWfbuildprojectchangeEntity.producteartIfChange" class="easyui-validatebox"--%>
                                <%--data-options="width: 150" value="${tQhWfbuildprojectchange.producteartIfChange }"/>--%>
                                <%--<input type="radio" name="tQhWfbuildprojectchangeEntity.producteartIfChange" value="1"--%>
                                <%--onchange="changeLeve('producteartOld','producteartNew','1');">是</input>--%>
                                <%--<input type="radio" name="tQhWfbuildprojectchangeEntity.producteartIfChange" value="0"--%>
                                <%--checked="true"--%>
                                <%--onchange="changeLeve('producteartOld','producteartNew','0');">否</input>--%>

                                <div class="producteartIfChangeDiv"></div>
                                <input id="producteartIfChange" name="tQhWfbuildprojectchangeEntity.producteartIfChange"
                                       type="hidden" class="easyui-validatebox"
                                       value="${tQhWfbuildprojectchange.producteartIfChange}"/>
                                <%--<input id="disOrEnabledIF" type="hidden" value="enabled"/>--%>

                            </td>
                            <td colspan="3">
                                <input id="producteartOld" name="tQhWfbuildprojectchangeEntity.producteartOld"
                                       class="easyui-textbox" style="width:350px"
                                       disabled="true"
                                       value="${tQhWfbuildprojectchange.producteartOld }"/></td>
                            <td colspan="3">
                                <input id="producteartNew" name="tQhWfbuildprojectchangeEntity.producteartNew"
                                       class="easyui-textbox" style="width:350px"
                                       disabled="true"
                                       value="${tQhWfbuildprojectchange.producteartNew }"/></td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">污染防治措施</td>
                            <td colspan="2">
                                <%--<input id="dischargelinkIfChange" name="tQhWfbuildprojectchangeEntity.dischargelinkIfChange" class="easyui-textbox" style="width:350px" disabled="true"--%>
                                <%--value="${tQhWfbuildprojectchange.dischargelinkIfChange }"/>--%>
                                <%--<input type="radio" name="tQhWfbuildprojectchangeEntity.dischargelinkIfChange" value="1"--%>
                                <%--onchange="changeLeve('dischargelinkOld','dischargelinkNew','1');">是</input>--%>
                                <%--<input type="radio" name="tQhWfbuildprojectchangeEntity.dischargelinkIfChange" value="0"--%>
                                <%--checked="true"--%>
                                <%--onchange="changeLeve('dischargelinkOld','dischargelinkNew','0');">否</input>--%>

                                <div class="dischargelinkIfChangeDiv"></div>
                                <input id="dischargelinkIfChange"
                                       name="tQhWfbuildprojectchangeEntity.dischargelinkIfChange"
                                       type="hidden" class="easyui-validatebox"
                                       value="${tQhWfbuildprojectchange.dischargelinkIfChange}"/>
                                <%--<input id="disOrEnabledIF" type="hidden" value="enabled"/>--%>

                            </td>
                            <td colspan="3">
                                <input id="dischargelinkOld" name="tQhWfbuildprojectchangeEntity.dischargelinkOld"
                                       class="easyui-textbox" style="width:350px"
                                       disabled="true"
                                       value="${tQhWfbuildprojectchange.dischargelinkOld }"/></td>
                            <td colspan="3">
                                <input id="dischargelinkNew" name="tQhWfbuildprojectchangeEntity.dischargelinkNew"
                                       class="easyui-textbox" style="width:350px"
                                       disabled="true"
                                       value="${tQhWfbuildprojectchange.dischargelinkNew }"/></td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">變更說明：<font color="red">*</font></td>
                            <td colspan="8" class="td_style1">
                                <%--<input id="changeexplain" name="tQhWfbuildprojectchangeEntity.changeexplain" class="easyui-validatebox" data-options="width: 150"--%>
                                <%--value="${tQhWfbuildprojectchange.changeexplain }"/>--%>

                                <textarea id="changeexplain" name="tQhWfbuildprojectchangeEntity.changeexplain"
                                          class="easyui-validatebox"
                                          style="width:800px;height:60px;" rows="5" cols="6"
                                          data-options="required:true">${tQhWfbuildprojectchange.changeexplain }</textarea>


                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">附件：</td>
                            <td colspan="9" class="td_style1">
						        <span class="sl-custom-file">
						            <input type="button" value="点击上传文件" class="btn-file"/>
						            <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file">
						        </span>
                                <input type="hidden" id="attachids" name="tQhWfbuildprojectchangeEntity.attachids" value="${tQhWfbuildprojectchange.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">委託事項說明：<font color="red">*</font></td>
                            <td colspan="8" class="td_style1">
                    <textarea id="entrustexplain" name="tQhWfbuildprojectchangeEntity.entrustexplain"
                              class="easyui-validatebox"
                              style="width:800px;height:60px;" rows="5" cols="6"
                              data-options="required:true,prompt:'例：1.委託太原周邊環保科技處環保手續辦理(可研立項，環評、竣工環保驗收)事宜；2.委託太原周邊總務處議價及合同簽訂事宜。'">${tQhWfbuildprojectchange.entrustexplain }</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 100%;padding-bottom: 15px;">
                                    <table id="buildprojectApplyItemTable" width="100%">
                                        <thead>
                                        <tr>
                                            <th>序號</th>
                                            <th>費用掛靠單位&nbsp;<font color="red">*</font></th>
                                            <th>費用代碼&nbsp;<font color="red">*</font></th>
                                            <th>費用佔比(%)</th>
                                            <th>操作</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <c:if test="${itemEntity!=null&&itemEntity.size()>0}">
                                            <c:forEach items="${itemEntity}" varStatus="i" var="item">
                                                <tr align="center" id="costItem${i.index+1}">
                                                    <td>${i.index+1}</td>
                                                    <td><input id="costname${i.index+1}"
                                                               name="tQhWfbuildprojectchangeitemEntity[${i.index}].costname"
                                                               class="easyui-textbox"
                                                               style="width:400px;" value="${item.costname}"> </input>
                                                    </td>
                                                    <td><input id="costno${i.index+1}"
                                                               name="tQhWfbuildprojectchangeitemEntity[${i.index}].costno"
                                                               class="easyui-textbox"
                                                               style="width:200px;" value="${item.costno}"> </input>
                                                    </td>
                                                    <td>
                                                        <input id="costrate${i.index}"
                                                               name="tQhWfbuildprojectchangeitemEntity[${i.index}].costrate"
                                                               class="easyui-validatebox" style="width:200px;"
                                                               value="${item.costrate}"/>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="deltr(${i.index+1});return false;"/></td>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${itemEntity==null}">
                                            <tr align="center" id="costItem1">
                                                <td>1</td>
                                                <td><input id="costname1"
                                                           name="tQhWfbuildprojectchangeitemEntity[0].costname"
                                                           class="easyui-validatebox" data-options="required:true"
                                                           style="width:400px;"
                                                           value=""/></td>
                                                <td><input id="costno1"
                                                           name="tQhWfbuildprojectchangeitemEntity[0].costno"
                                                           class="easyui-validatebox" data-options="required:true"
                                                           style="width:200px;"
                                                           value=""/></td>
                                                <td>
                                                    <input id="costrate1" name="tQhWfbuildprojectchangeitemEntity[0].costrate"
                                                           class="easyui-validatebox" style="width:200px;" value="" onblur='valdIsNumber(this)'/>
                                                </td>
                                                <td><input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="deltr(1);return false;"/></td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="left">
                            <td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
                                <input type="button" id="add" style="width:100px;float:left;" value="添加一筆"/>
                            </td>
                        </tr>

                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)" onclick="showWfImag('${workFlowId}','系統上線申請流程圖','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="tQhWfbuildprojectchangeEntity.kchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['kchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.kchargeno }"/><c:if test="${requiredMap['kchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="kchargename" name="tQhWfbuildprojectchangeEntity.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${tQhWfbuildprojectchange.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="bchargeno" name="tQhWfbuildprojectchangeEntity.bchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${tQhWfbuildprojectchange.bchargeno }"/><c:if test="${requiredMap['bchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="bchargename" name="tQhWfbuildprojectchangeEntity.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${tQhWfbuildprojectchange.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="cchargeno"
                                                               name="tQhWfbuildprojectchangeEntity.cchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['cchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.cchargeno }"/><c:if test="${requiredMap['cchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="cchargename"
                                                                name="tQhWfbuildprojectchangeEntity.cchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['cchargeno']}"
                                                                value="${tQhWfbuildprojectchange.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"  id="hchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽
                                                                    <a href="javascript:addHq('hcharge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hchargeno" onblur="getUserNameByEmpno(this,'hcharge');" name="tQhWfbuildprojectchangeEntity.hchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hchargeno']}" value="${tQhWfbuildprojectchange.hchargeno }"/>
                                                        <c:if test="${requiredMap['hchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hchargename" name="tQhWfbuildprojectchangeEntity.hchargename" readonly
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hchargeno']}"  value="${tQhWfbuildprojectchange.hchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectchangeEntity')"></div>
                                                                    <%--onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealfactoryid').combobox('getValue'))"></div>--%>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zchargeno"
                                                               name="tQhWfbuildprojectchangeEntity.zchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.zchargeno }"/><c:if test="${requiredMap['zchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="zchargename"
                                                                name="tQhWfbuildprojectchangeEntity.zchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zchargeno']}" readonly
                                                                value="${tQhWfbuildprojectchange.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectchangeEntity')"></div>
                                                                    <%--onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealfactoryid').combobox('getValue'))"></div>--%>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zcchargeno"
                                                               name="tQhWfbuildprojectchangeEntity.zcchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zcchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.zcchargeno }"/><c:if test="${requiredMap['zcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="zcchargename"
                                                                name="tQhWfbuildprojectchangeEntity.zcchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zcchargeno']}" readonly
                                                                value="${tQhWfbuildprojectchange.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處對應窗口
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(3,'hbchargeno','hbchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbchargeno"
                                                               name="tQhWfbuildprojectchangeEntity.hbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.hbchargeno }"/><c:if test="${requiredMap['hbchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hbchargename" name="tQhWfbuildprojectchangeEntity.hbchargename" readonly
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbchargeno']}" value="${tQhWfbuildprojectchange.hbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="hbkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處課級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(4,'hbkchargeTable','hbkchargeno','hbkchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectchangeEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbkchargeno"
                                                               name="tQhWfbuildprojectchangeEntity.hbkchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbkchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.hbkchargeno }"/><c:if test="${requiredMap['hbkchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hbkchargename" name="tQhWfbuildprojectchangeEntity.hbkchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbkchargeno']}" readonly
                                                                value="${tQhWfbuildprojectchange.hbkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hbbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處部級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(5,'hbbchargeTable','hbbchargeno','hbbchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectchangeEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbbchargeno"
                                                               name="tQhWfbuildprojectchangeEntity.hbbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbbchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.hbbchargeno }"/><c:if test="${requiredMap['hbbchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="hbbchargename"name="tQhWfbuildprojectchangeEntity.hbbchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbbchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.hbbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hbcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處處級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(6,'hbcchargeTable','hbcchargeno','hbcchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectchangeEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbcchargeno"
                                                               name="tQhWfbuildprojectchangeEntity.hbcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['hbcchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.hbcchargeno }"/><c:if test="${requiredMap['hbcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="hbcchargename" name="tQhWfbuildprojectchangeEntity.hbcchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hbcchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.hbcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zwchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">總務處對應窗口</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(7,'zwchargeno','zwchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zwchargeno"
                                                               name="tQhWfbuildprojectchangeEntity.zwchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['zwchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.zwchargeno }"/><c:if test="${requiredMap['zwchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="zwchargename" name="tQhWfbuildprojectchangeEntity.zwchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zwchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.zwchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zwbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">總務處部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(8,'zwbchargeTable','zwbchargeno','zwbchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectchangeEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zwbchargeno"
                                                               name="tQhWfbuildprojectchangeEntity.zwbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['zwbchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.zwbchargeno }"/><c:if test="${requiredMap['zwbchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="zwbchargename" name="tQhWfbuildprojectchangeEntity.zwbchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zwbchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.zwbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zwcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">總務處處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(9,'zwcchargeTable','zwcchargeno','zwcchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectchangeEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zwcchargeno"
                                                               name="tQhWfbuildprojectchangeEntity.zwcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['zwcchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.zwcchargeno }"/><c:if test="${requiredMap['zwcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="zwcchargename" name="tQhWfbuildprojectchangeEntity.zwcchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zwcchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.zwcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zbjgckchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">周邊經管對應窗口
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(131,'zbjgckchargeTable','zbjgckchargeno','zbjgckchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectchangeEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zbjgckchargeno"
                                                               name="tQhWfbuildprojectchangeEntity.zbjgckchargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zbjgckchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.zbjgckchargeno }"/><c:if test="${requiredMap['zbjgckchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="zbjgckchargename"
                                                                name="tQhWfbuildprojectchangeEntity.zbjgckchargename"
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zbjgckchargeno']}" readonly
                                                                value="${tQhWfbuildprojectchange.zbjgckchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zbjgkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">周邊經管處課級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(10,'zbjgkchargeTable','zbjgkchargeno','zbjgkchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectchangeEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zbjgkchargeno" name="tQhWfbuildprojectchangeEntity.zbjgkchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['zbjgkchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.zbjgkchargeno }"/><c:if test="${requiredMap['zbjgkchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="zbjgkchargename" name="tQhWfbuildprojectchangeEntity.zbjgkchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zbjgkchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.zbjgkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zbjgbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">周邊經管處部級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(11,'zbjgbchargeTable','zbjgbchargeno','zbjgbchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectchangeEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zbjgbchargeno"
                                                               name="tQhWfbuildprojectchangeEntity.zbjgbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['zbjgbchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.zbjgbchargeno }"/><c:if test="${requiredMap['zbjgbchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="zbjgbchargename" name="tQhWfbuildprojectchangeEntity.zbjgbchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zbjgbchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.zbjgbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zbjgzgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">周邊處主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(132,'zbjgzgchargeTable','zbjgzgchargeno','zbjgzgchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectchangeEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zbjgzgchargeno"
                                                               name="tQhWfbuildprojectchangeEntity.zbjgzgchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['zbjgzgchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.zbjgzgchargeno }"/><c:if test="${requiredMap['zbjgzgchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="zbjgzgchargename"
                                                                name="tQhWfbuildprojectchangeEntity.zbjgzgchargename"
                                                                class="easyui-validatebox"
                                                                data-options="width: 80,required:${requiredMap['zbjgzgchargeno']}" readonly
                                                                value="${tQhWfbuildprojectchange.zbjgzgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zbjgcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">周邊經管處處級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(12,'zbjgcchargeTable','zbjgcchargeno','zbjgcchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectchangeEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zbjgcchargeno"
                                                               name="tQhWfbuildprojectchangeEntity.zbjgcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['zbjgcchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.zbjgcchargeno }"/><c:if test="${requiredMap['zbjgcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="zbjgcchargename" name="tQhWfbuildprojectchangeEntity.zbjgcchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zbjgcchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.zbjgcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zgshchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">主管審核
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(274,'zgshchargeTable','zgshchargeno','zgshchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectchangeEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zgshchargeno"
                                                               name="tQhWfbuildprojectchangeEntity.zgshchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['zgshchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.zgshchargeno }"/><c:if test="${requiredMap['zgshchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="zgshchargename" name="tQhWfbuildprojectchangeEntity.zgshchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zgshchargeno']}"
                                                               value="${tQhWfbuildprojectchange.zgshchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <%--<table width="18%" style="float: left;margin-left: 5px;" id="zbzchchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">核准
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(13,'zbzchchargeTable','zbzchchargeno','zbzchchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectchangeEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zbzchchargeno"
                                                               name="tQhWfbuildprojectchangeEntity.zbzchchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['zbzchchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.zbzchchargeno }"/><c:if test="${requiredMap['zbzchchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="zbzchchargename" name="tQhWfbuildprojectchangeEntity.zbzchchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zbzchchargeno']}"
                                                               value="${tQhWfbuildprojectchange.zbzchchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>--%>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="jczhchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">權限主管核准</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(269,'jczhchargeTable','jczhchargeno','jczhchargename',$('#dealfactoryid').combobox('getValue'),'tQhWfbuildprojectchangeEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="jczhchargeno"
                                                               name="tQhWfbuildprojectchangeEntity.jczhchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['jczhchargeno']}" readonly
                                                               value="${tQhWfbuildprojectchange.jczhchargeno }"/><c:if test="${requiredMap['jczhchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="jczhchargename" name="tQhWfbuildprojectchangeEntity.jczhchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['jczhchargeno']}"
                                                               value="${tQhWfbuildprojectchange.jczhchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                                <%--<a href="#" id="btnadd" class="easyui-linkbutton" iconCls="icon-add" plain="true" code="add">提交</a>--%>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
<%--<script src='${ctx}/static/js/information/tqhwfbuildprojectchange.js?"+Math.random()"'></script>--%>
<script type="text/javascript">
    if ("${tQhWfbuildprojectchange.hchargeno}" != "") {
        var nostr = "${tQhWfbuildprojectchange.hchargeno}";
        var namestr = "${tQhWfbuildprojectchange.hchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hchargeTable tr:eq(" + (i + 2) + ")").find("#hchargeno").val(notr[i]);
            $("#hchargeTable tr:eq(" + (i + 2) + ")").find("#hchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hchargeno' name='tQhWfbuildprojectchangeEntity.hchargeno' style='width: 80px' value='' onblur='getUserNameByEmpno(this,\"hcharge\");'/>/<input id='hchargename' name='tQhWfbuildprojectchangeEntity.hchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
</script>
</body>
</html>