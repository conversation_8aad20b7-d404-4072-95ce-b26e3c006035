<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>建設項目環保變更報備申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/other/tqhwfbuildprojectchange.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfbuildprojectchange/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhWfbuildprojectchange.id }"/>
    <div class="commonW">
        <div class="headTitle">建設項目環保變更報備申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">${tQhWfbuildprojectchange.serialno}</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<input class="inputCss" style="width: 100px"
                        value="<fmt:formatDate value='${tQhWfbuildprojectchange.createtime}' pattern='yyyy-MM-dd hh:mm'/>">
        </div>
        <div class="position_R margin_R">
            填單人：${tQhWfbuildprojectchange.makerno}/${tQhWfbuildprojectchange.makername}</div>
        <br>
        <div class="clear"></div>
        <table class="formList" id="buildprojecttable">
            <tr>
                <td>
                    <table class="formList">
                        <th style="text-align:left;" colspan="10">&nbsp;&nbsp;承辦人詳細信息
                        </th>
                        <tr align="center">
                            <td width="6%">承辦人工號</td>
                            <td width="6%" class="td_style2">${tQhWfbuildprojectchange.dealno }</td>
                            <td width="6%">承辦人</td>
                            <td width="6%" class="td_style2">${tQhWfbuildprojectchange.dealname }</td>
                            <td width="6%">單位代碼</td>
                            <td width="6%" class="td_style2">${tQhWfbuildprojectchange.dealdeptno }</td>
                            <td width="6%">提報日期</td>
                            <td width="6%" class="td_style2"><fmt:formatDate value="${tQhWfbuildprojectchange.dealtime}"/></td>
                            <td width="6%">廠區</td>
                            <td width="6%" class="td_style1">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox" disabled="true"
                                       panelHeight="auto" value="${tQhWfbuildprojectchange.dealfactoryid }"
                                       data-options="width: 80,required:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="3" class="td_style2">${tQhWfbuildprojectchange.dealdeptname }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${tQhWfbuildprojectchange.dealemail}</td>
                            <td>聯繫分機</td>
                            <td class="td_style2">${tQhWfbuildprojectchange.dealtel}</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <th style="text-align:left;" colspan="10">&nbsp;&nbsp;項目基本信息
                            <tr align="center">
                                <td>項目名稱</td>
                                <td colspan="2" class="td_style2">${tQhWfbuildprojectchange.projectname }</td>
                                <td>項目法人</td>
                                <td class="td_style1">
                                    <input id="projectcorporateid" class="easyui-textbox inputCss" disabled="disabled"
                                           readonly="true" value="${tQhWfbuildprojectchange.projectcorporateid }"
                                           data-options="width: 300,required:true"/>
                                </td>
                                <td>項目階段</td>
                                <td colspan="4">
                                    <input id="projectstage" type="hidden" value="${tQhWfbuildprojectchange.projectstage }"/>
                                    <div class="projectstageDiv">

                                    </div>
                                    <input id="disOrEnabled" type="hidden" value="disabled"/>
                                </td>
                            </tr>
                        <th style="text-align:left;" colspan="10">&nbsp;&nbsp;項目變更內容
                        </th>

                        <tr align="center">
                            <td colspan="2">項目</td>
                            <td colspan="4">變更前</td>
                            <td colspan="4">變更后</td>
                        </tr>
                        <tr align="center" id="tr_projectnameIfChange">
                            <td colspan="2">項目名稱</td>
                            <input id="projectnameIfChange" type="hidden" value="${tQhWfbuildprojectchange.projectnameIfChange }"/>
                            <td colspan="4">
                                <input id="projectnameOld" class="easyui-textbox" style="width:450px" readonly="true"
                                       value="${tQhWfbuildprojectchange.projectnameOld }"/></td>
                            <td colspan="4">
                                <input id="projectnameNew" class="easyui-textbox" style="width:450px" readonly="true"
                                       value="${tQhWfbuildprojectchange.projectnameNew }"/></td>
                        </tr>
                        <tr align="center" id="tr_buildcorporateIfChange">
                            <td colspan="2">建設法人</td>
                            <input id="buildcorporateIfChange" type="hidden" value="${tQhWfbuildprojectchange.buildcorporateIfChange }"/>
                            <td colspan="4">
                                <input id="buildcorporateOld" class="easyui-textbox" style="width:450px" readonly="true"
                                       value="${tQhWfbuildprojectchange.buildcorporateOld }"/>
                            </td>
                            <td colspan="4">
                                <input id="buildcorporateNew" class="easyui-textbox" style="width:450px" readonly="true"
                                       value="${tQhWfbuildprojectchange.buildcorporateNew }"/>
                            </td>
                        </tr>
                        <tr align="center" id="tr_projectnatureIfChange">
                            <td colspan="2">項目性質</td>
                            <input id="projectnatureIfChange" type="hidden"
                                   value="${tQhWfbuildprojectchange.projectnatureIfChange }"/>
                            <td colspan="4">
                                <input id="projectnatureOld" class="easyui-textbox" style="width:450px"
                                       readonly="true"
                                       value="${tQhWfbuildprojectchange.projectnatureOld }"/></td>
                            <td colspan="4">
                                <input id="projectnatureNew" class="easyui-textbox" style="width:450px"
                                       readonly="true"
                                       value="${tQhWfbuildprojectchange.projectnatureNew }"/>
                            </td>
                        </tr>
                        <tr align="center" id="tr_buildsizeIfChange">
                            <td colspan="2">建設規模</td>
                            <input id="buildsizeIfChange" type="hidden"
                                   value="${tQhWfbuildprojectchange.buildsizeIfChange }"/>
                            <td colspan="4">
                                <input id="buildsizeOld" class="easyui-textbox" style="width:450px"
                                       readonly="true"
                                       value="${tQhWfbuildprojectchange.buildsizeOld }"/></td>
                            <td colspan="4">
                                <input id="buildsizeNew" class="easyui-textbox" style="width:450px"
                                       readonly="true"
                                       value="${tQhWfbuildprojectchange.buildsizeNew }"/></td>
                        </tr>
                        <tr align="center" id="tr_buildsiteIfChange">
                            <td colspan="2">建設地點</td>
                            <input id="buildsiteIfChange" type="hidden" value="${tQhWfbuildprojectchange.buildsiteIfChange }"/>
                            <td colspan="4">
                                <input id="buildsiteOld" class="easyui-textbox" style="width:450px"
                                       readonly="true"
                                       value="${tQhWfbuildprojectchange.buildsiteOld }"/></td>
                            <td colspan="4">
                                <input id="buildsiteNew" class="easyui-textbox" style="width:450px"
                                       readonly="true"
                                       value="${tQhWfbuildprojectchange.buildsiteNew }"/></td>
                        </tr>
                        <tr align="center" id="tr_buildtotalcostIfChange">
                            <td colspan="2">總投資及環保投資</td>
                            <input id="buildtotalcostIfChange" type="hidden" value="${tQhWfbuildprojectchange.buildtotalcostIfChange }"/>
                            <td colspan="4">
                                <input id="buildtotalcostOld" class="easyui-textbox" style="width:450px"
                                       readonly="true"
                                       value="${tQhWfbuildprojectchange.buildtotalcostOld }"/></td>
                            <td colspan="4">
                                <input id="buildtotalcostNew" class="easyui-textbox" style="width:450px"
                                       readonly="true"
                                       value="${tQhWfbuildprojectchange.buildtotalcostNew }"/></td>
                        </tr>
                        <tr align="center" id="tr_investmodeIfChange">
                            <td colspan="2">投資方式</td>
                            <input id="investmodeIfChange" type="hidden" value="${tQhWfbuildprojectchange.investmodeIfChange }"/>
                            <td colspan="4">
                                <input id="investmodeOld" class="easyui-textbox" style="width:450px"
                                       readonly="true"
                                       value="${tQhWfbuildprojectchange.investmodeOld }"/></td>
                            <td colspan="4">
                                <input id="investmodeNew" class="easyui-textbox" style="width:450px"
                                       readonly="true"
                                       value="${tQhWfbuildprojectchange.investmodeNew }"/></td>
                        </tr>
                        <tr align="center" id="tr_projectbuildcontentIfChange">
                            <td colspan="2">項目主要建設內容</td>
                            <input id="projectbuildcontentIfChange" type="hidden"
                                   value="${tQhWfbuildprojectchange.projectbuildcontentIfChange }"/>
                            <td colspan="4">
                                <input id="projectbuildcontentOld" class="easyui-textbox"
                                       style="width:450px" readonly="true"
                                       value="${tQhWfbuildprojectchange.projectbuildcontentOld }"/></td>
                            <td colspan="4">
                                <input id="projectbuildcontentNew" class="easyui-textbox"
                                       style="width:450px" readonly="true"
                                       value="${tQhWfbuildprojectchange.projectbuildcontentNew }"/></td>
                        </tr>
                        <tr align="center" id="tr_producteartIfChange">
                            <td colspan="2">生產工藝</td>
                            <input id="producteartIfChange" type="hidden" value="${tQhWfbuildprojectchange.producteartIfChange }"/>
                            <td colspan="4">
                                <input id="producteartOld" class="easyui-textbox" style="width:450px"
                                       readonly="true"
                                       value="${tQhWfbuildprojectchange.producteartOld }"/></td>
                            <td colspan="4">
                                <input id="producteartNew" class="easyui-textbox" style="width:450px"
                                       readonly="true"
                                       value="${tQhWfbuildprojectchange.producteartNew }"/></td>
                        </tr>
                        <tr align="center" id="tr_dischargelinkIfChange">
                            <td colspan="2">污染防治措施</td>
                            <input id="dischargelinkIfChange" type="hidden"
                                   value="${tQhWfbuildprojectchange.dischargelinkIfChange }"/>
                            <td colspan="4">
                                <input id="dischargelinkOld" class="easyui-textbox" style="width:450px"
                                       readonly="true"
                                       value="${tQhWfbuildprojectchange.dischargelinkOld }"/></td>
                            <td colspan="4">
                                <input id="dischargelinkNew" class="easyui-textbox" style="width:450px"
                                       readonly="true"
                                       value="${tQhWfbuildprojectchange.dischargelinkNew }"/></td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">變更說明：</td>
                            <td colspan="8" class="td_style1">
                                <textarea id="changeexplain" readonly
                                          class="easyui-validatebox"
                                          style="width:800px;height:60px;" rows="5" cols="6"
                                          data-options="required:true">${tQhWfbuildprojectchange.changeexplain }</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">附件：</td>
                            <td colspan="8" class="td_style1">
                                <div>
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">委託事項說明：</td>
                            <td colspan="8" class="td_style1">
                                <textarea id="entrustexplain" readonly
                                          class="easyui-validatebox"
                                          style="width:800px;height:60px;" rows="5" cols="6"
                                          data-options="required:true,prompt:'例：1.委託太原周邊環保科技處環保手續辦理(可研立項，環評、竣工環保驗收)事宜；2.委託太原周邊總務處議價及合同簽訂事宜。'">${tQhWfbuildprojectchange.entrustexplain }</textarea>

                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 100%;padding-bottom: 15px;">
                                    <table id="buildprojectApplyItemTable" width="100%">
                                        <thead>
                                        <tr>
                                            <th>序號</th>
                                            <th>費用掛靠單位</th>
                                            <th>費用代碼</th>
                                            <th>費用佔比</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <c:forEach items="${itemEntity}" varStatus="i" var="item">
                                            <tr align="center">
                                                <td>${i.index+1}</td>
                                                <td>${item.costname}</td>
                                                <td>${item.costno}</td>
                                                <td>${item.costrate}</td>
                                            </tr>
                                        </c:forEach>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">批註</td>
                            <td colspan="8" class="td_style2">
                    <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                              style="width:800px;height:40px;"
                              rows="4" cols="4"
                              data-options="required:true"></textarea>
                            </td>

                        </tr>
                        <c:if test="${not empty nodeName&&'環保科技處對應窗口' eq nodeName}">
                            <tr align="center">
                                <td colspan="2">附件&nbsp;（補充說明）</td>
                                <td colspan="8" class="td_style1">
                                    <span class="sl-custom-file">
						                <input type="button" value="点击上传文件" class="btn-file"/>
						                <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file">
						            </span>
                                    <input type="hidden" id="attachids" name="reattachids" value="${tQhWfbuildprojectchangeEntity.reattachids}"/>
                                    <div id="dowloadUrl"></div>
                                </td>
                            </tr>
                        </c:if>
                        <c:if test="${tQhWfbuildprojectchange.reattachids!=null}">
                            <tr align="center">
                                <td colspan="2">附件&nbsp;（補充說明）</td>
                                <td colspan="8" class="td_style1">
                                    <div>
                                        <c:forEach items="${reFile}" varStatus="i" var="item">
                                            <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                                <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                            </div>
                                        </c:forEach>
                                    </div>
                                </td>
                            </tr>
                        </c:if>

                        </tr>
                        <%--審核按鈕--%>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <c:choose>
                                    <c:when test="${not empty nodeName && '環保科技處對應窗口' eq nodeName}">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" perCall="audiPrValid" serialNo="${tQhWfbuildprojectchange.serialno}"></fox:action>
                                    </c:when>
                                    <c:otherwise>
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" serialNo="${tQhWfbuildprojectchange.serialno}"></fox:action>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>

                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','添加新增建設項目環保手續辦理委託申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhWfbuildprojectchange.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
<input type="hidden" id="currentNodeOrder" value="${nodeOrder}" />
<div id="dlg"></div>

</body>
</html>