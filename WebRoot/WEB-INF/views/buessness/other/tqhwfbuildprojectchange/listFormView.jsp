<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>建設項目環保變更報備申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/other/tqhwfbuildprojectchange.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfbuildprojectchange/${action}" method="post">
    <div class="commonW">
        <div id="mainDiv">
            <div class="headTitle">建設項目環保變更報備申請單</div>
            <div class="position_L">
                任務編碼：<span style="color:#999;">${tQhWfbuildprojectchange.serialno}</span>
            </div>
            <div class="position_L1 margin_L">
                填單時間： <input class="inputCss" style="width: 100px"
                             value="<fmt:formatDate value='${tQhWfbuildprojectchange.createtime}' pattern='yyyy-MM-dd hh:mm'/>">
            </div>
            <div class="position_R margin_R">
                填單人：${tQhWfbuildprojectchange.makerno}/${tQhWfbuildprojectchange.makername}</div>
            <br>
            <div class="clear"></div>
            <table class="formList" id="buildprojecttable">
                <tr>
                    <td>
                        <table class="formList">
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;承辦人詳細信息
                            </th>
                            <tr align="center">
                                <td width="6%">承辦人工號</td>
                                <td width="6%" class="td_style1">
                                    <input id="dealno" name="dealno" class="easyui-validatebox  inputCss" readonly
                                           data-options="width: 80"
                                           value="${tQhWfbuildprojectchange.dealno }"/>
                                </td>
                                <td width="6%">承辦人</td>
                                <td width="6%" class="td_style1">
                                    <input id="dealname" name="dealname" class="easyui-validatebox inputCss"
                                           data-options="width:80" readonly value="${tQhWfbuildprojectchange.dealname }"/>
                                </td>
                                <td width="6%">單位代碼</td>
                                <td width="6%" class="td_style1">
                                    <input id="dealdeptno" name="dealdeptno" class="easyui-validatebox inputCss" readonly
                                           data-options="width: 90"
                                           readonly value="${tQhWfbuildprojectchange.dealdeptno }"/>
                                </td>
                                <td width="6%">提報日期</td>
                                <td width="6%" class="td_style1">
                                    <%--<input id="dealtime" name="tQhWfbuildprojectchangeEntity.dealtime" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width: 100"--%>
                                    <%--readonly--%>
                                    <%--value="<fmt:formatDate value="${tQhWfbuildprojectchange.dealtime}"/>" />--%>
                                    <input id="dealtime" name="dealtime" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                           data-options="width: 100"
                                           value="<fmt:formatDate value="${tQhWfbuildprojectchange.dealtime}"/>" disabled/>
                                </td>
                                </td>
                                <td width="6%">廠區</td>
                                <td width="6%" class="td_style1">
                                    <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox" disabled="true"
                                           panelHeight="auto" value="${tQhWfbuildprojectchange.dealfactoryid }"
                                           data-options="width: 80,required:true"/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>單位</td>
                                <td colspan="3" class="td_style1">
                                    <input id="dealdeptname" name="dealdeptname" class="easyui-validatebox inputCss"
                                           data-options="width: 350"
                                           readonly value="${tQhWfbuildprojectchange.dealdeptname }"/>
                                </td>
                                <td>聯繫郵箱</td>
                                <td colspan="3" class="td_style1">
                                    <input id="dealemail" name="dealemail" class="easyui-validatebox inputCss" readonly
                                           value="${tQhWfbuildprojectchange.dealemail }" style="width:300px;"
                                    />
                                </td>
                                <td>聯繫分機</td>
                                <td class="td_style1">
                                    <input id="dealtel" name="dealtel" class="easyui-validatebox inputCss" readonly
                                           style="width:90px;"
                                           value="${tQhWfbuildprojectchange.dealtel }"/>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td>
                        <table class="formList">
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;項目基本信息
                                <tr align="center">
                                    <td>項目名稱</td>
                                    <td colspan="2" class="td_style1">
                                        <input id="projecname" name="projecname" class="easyui-validatebox inputCss" readonly
                                               style="width:150px;"
                                               value="${tQhWfbuildprojectchange.projectname }"/>

                                    </td>
                                    <td>項目法人</td>
                                    <td class="td_style1">
                                        <input id="projectcorporateid" name="projectcorporateid" class="easyui-textbox inputCss"
                                               disabled="disabled"
                                               readonly="true" value="${tQhWfbuildprojectchange.projectcorporateid }"
                                               data-options="width: 300,required:true"/>
                                    </td>
                                    <td>項目階段</td>
                                    <td colspan="4">
                                        <input id="projectstage" type="hidden" name="tQhWfbuildprojectchangeEntity.projectstage"
                                               value="${tQhWfbuildprojectchange.projectstage }"/>
                                        <div class="projectstageDiv">

                                        </div>
                                        <input id="disOrEnabled" type="hidden" value="disabled"/>
                                    </td>

                                </tr>

                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;項目變更內容
                            </th>

                            <tr align="center">
                                <td colspan="2">項目</td>
                                <%--<td colspan="2" >是否發生變更</td>--%>
                                <td colspan="4">變更前</td>
                                <td colspan="4">變更后</td>
                            </tr>
                            <tr align="center" id="tr_projectnameIfChange">
                                <td colspan="2">項目名稱</td>

                                <%--<input id="projectnameIfChange" name="projectnameIfChange" class="easyui-validatebox"--%>
                                <%--data-options="width: 150" value="${tQhWfbuildprojectchange.projectnameIfChange }"/>--%>
                                <input id="projectnameIfChange" type="hidden"
                                       name="tQhWfbuildprojectchangeEntity.projectnameIfChange"
                                       value="${tQhWfbuildprojectchange.projectnameIfChange }"/>

                                <%--</td>--%>
                                <td colspan="4">
                                    <input id="projectnameOld" name="projectnameOld" class="easyui-textbox" style="width:450px"
                                           readonly="true"
                                           value="${tQhWfbuildprojectchange.projectnameOld }"/></td>
                                <td colspan="4">
                                    <input id="projectnameNew" name="projectnameNew" class="easyui-textbox" style="width:450px"
                                           readonly="true"
                                           value="${tQhWfbuildprojectchange.projectnameNew }"/></td>
                            </tr>
                            <tr align="center" id="tr_buildcorporateIfChange">
                                <td colspan="2">建設法人</td>
                                <%--<td colspan="2">--%>
                                <%--<input type="radio" name="buildcorporateIfChange" value="1" >是</input>--%>
                                <%--<input type="radio" name="buildcorporateIfChange" value="0" >否</input>--%>
                                <input id="buildcorporateIfChange" type="hidden"
                                       name="tQhWfbuildprojectchangeEntity.buildcorporateIfChange"
                                       value="${tQhWfbuildprojectchange.buildcorporateIfChange }"/>

                                <%--</td>--%>
                                <td colspan="4">
                                    <input id="buildcorporateOld" name="buildcorporateOld" class="easyui-textbox"
                                           style="width:450px"
                                           readonly="true"
                                           value="${tQhWfbuildprojectchange.buildcorporateOld }"/>
                                </td>
                                <td colspan="4">
                                    <input id="buildcorporateNew" name="buildcorporateNew" class="easyui-textbox"
                                           style="width:450px"
                                           readonly="true"
                                           value="${tQhWfbuildprojectchange.buildcorporateNew }"/>
                                </td>
                            </tr>
                            <tr align="center" id="tr_projectnatureIfChange">
                                <td colspan="2">項目性質</td>
                                <%--<td colspan="2" >--%>
                                <%--&lt;%&ndash;<input id="projectnatureIfChange" name="projectnatureIfChange" class="easyui-validatebox"&ndash;%&gt;--%>
                                <%--&lt;%&ndash;data-options="width: 150" value="${tQhWfbuildprojectchange.projectnatureIfChange }"/>&ndash;%&gt;--%>
                                <%--</td>--%>
                                <input id="projectnatureIfChange" type="hidden"
                                       name="tQhWfbuildprojectchangeEntity.projectnatureIfChange"
                                       value="${tQhWfbuildprojectchange.projectnatureIfChange }"/>

                                <td colspan="4">
                                    <input id="projectnatureOld" name="projectnatureOld" class="easyui-textbox" style="width:450px"
                                           readonly="true"
                                           value="${tQhWfbuildprojectchange.projectnatureOld }"/></td>
                                <td colspan="4">
                                    <input id="projectnatureNew" name="projectnatureNew" class="easyui-textbox" style="width:450px"
                                           readonly="true"
                                           value="${tQhWfbuildprojectchange.projectnatureNew }"/>
                                </td>
                            </tr>
                            <tr align="center" id="tr_buildsizeIfChange">
                                <td colspan="2">建設規模</td>
                                <%--<td colspan="2" >--%>
                                <%--&lt;%&ndash;<input id="buildsizeIfChange" name="buildsizeIfChange" class="easyui-validatebox"&ndash;%&gt;--%>
                                <%--&lt;%&ndash;data-options="width: 150" value="${tQhWfbuildprojectchange.buildsizeIfChange }"/>&ndash;%&gt;--%>
                                <%--</td>--%>
                                <input id="buildsizeIfChange" type="hidden" name="tQhWfbuildprojectchangeEntity.buildsizeIfChange"
                                       value="${tQhWfbuildprojectchange.buildsizeIfChange }"/>
                                <td colspan="4">
                                    <input id="buildsizeOld" name="buildsizeOld" class="easyui-textbox" style="width:450px"
                                           readonly="true"
                                           value="${tQhWfbuildprojectchange.buildsizeOld }"/></td>
                                <td colspan="4">
                                    <input id="buildsizeNew" name="buildsizeNew" class="easyui-textbox" style="width:450px"
                                           readonly="true"
                                           value="${tQhWfbuildprojectchange.buildsizeNew }"/></td>
                            </tr>
                            <tr align="center" id="tr_buildsiteIfChange">
                                <td colspan="2">建設地點</td>
                                <%--<td colspan="2">--%>
                                <%--<input type="radio" name="buildsiteIfChange" value="1" >是</input>--%>
                                <%--<input type="radio" name="buildsiteIfChange" value="0" >否</input>--%>
                                <%--</td>--%>
                                <input id="buildsiteIfChange" type="hidden" name="tQhWfbuildprojectchangeEntity.buildsiteIfChange"
                                       value="${tQhWfbuildprojectchange.buildsiteIfChange }"/>
                                <td colspan="4">
                                    <input id="buildsiteOld" name="buildsiteOld" class="easyui-textbox" style="width:450px"
                                           readonly="true"
                                           value="${tQhWfbuildprojectchange.buildsiteOld }"/></td>
                                <td colspan="4">
                                    <input id="buildsiteNew" name="buildsiteNew" class="easyui-textbox" style="width:450px"
                                           readonly="true"
                                           value="${tQhWfbuildprojectchange.buildsiteNew }"/></td>
                            </tr>
                            <tr align="center" id="tr_buildtotalcostIfChange">
                                <td colspan="2">總投資及環保投資</td>
                                <%--<td colspan="2">--%>
                                <%--&lt;%&ndash;<input id="buildtotalcostIfChange" name="buildtotalcostIfChange" class="easyui-validatebox"&ndash;%&gt;--%>
                                <%--&lt;%&ndash;data-options="width: 150" value="${tQhWfbuildprojectchange.buildtotalcostIfChange }"/>&ndash;%&gt;--%>
                                <%--</td>--%>
                                <input id="buildtotalcostIfChange" type="hidden"
                                       name="tQhWfbuildprojectchangeEntity.buildtotalcostIfChange"
                                       value="${tQhWfbuildprojectchange.buildtotalcostIfChange }"/>
                                <td colspan="4">
                                    <input id="buildtotalcostOld" name="buildtotalcostOld" class="easyui-textbox"
                                           style="width:450px"
                                           readonly="true"
                                           value="${tQhWfbuildprojectchange.buildtotalcostOld }"/></td>
                                <td colspan="4">
                                    <input id="buildtotalcostNew" name="buildtotalcostNew" class="easyui-textbox"
                                           style="width:450px"
                                           readonly="true"
                                           value="${tQhWfbuildprojectchange.buildtotalcostNew }"/></td>
                            </tr>
                            <tr align="center" id="tr_investmodeIfChange">
                                <td colspan="2">投資方式</td>
                                <%--<td colspan="2">--%>
                                <%--&lt;%&ndash;<input id="investmodeIfChange" name="investmodeIfChange" class="easyui-validatebox"&ndash;%&gt;--%>
                                <%--&lt;%&ndash;data-options="width: 150" value="${tQhWfbuildprojectchange.investmodeIfChange }"/>&ndash;%&gt;--%>
                                <%--</td>--%>
                                <input id="investmodeIfChange" type="hidden" name="tQhWfbuildprojectchangeEntity.investmodeIfChange"
                                       value="${tQhWfbuildprojectchange.investmodeIfChange }"/>
                                <td colspan="4">
                                    <input id="investmodeOld" name="investmodeOld" class="easyui-textbox" style="width:450px"
                                           readonly="true"
                                           value="${tQhWfbuildprojectchange.investmodeOld }"/></td>
                                <td colspan="4">
                                    <input id="investmodeNew" name="investmodeNew" class="easyui-textbox" style="width:450px"
                                           readonly="true"
                                           value="${tQhWfbuildprojectchange.investmodeNew }"/></td>
                            </tr>
                            <tr align="center" id="tr_projectbuildcontentIfChange">
                                <td colspan="2">項目主要建設內容</td>
                                <%--<td colspan="2">--%>
                                <%--&lt;%&ndash;<input id="projectbuildcontentIfChange" name="projectbuildcontentIfChange"&ndash;%&gt;--%>
                                <%--&lt;%&ndash;class="easyui-validatebox"&ndash;%&gt;--%>
                                <%--&lt;%&ndash;data-options="width: 150" value="${tQhWfbuildprojectchange.projectbuildcontentIfChange }"/>&ndash;%&gt;--%>
                                <%--</td>--%>
                                <input id="projectbuildcontentIfChange" type="hidden"
                                       name="tQhWfbuildprojectchangeEntity.projectbuildcontentIfChange"
                                       value="${tQhWfbuildprojectchange.projectbuildcontentIfChange }"/>
                                <td colspan="4">
                                    <input id="projectbuildcontentOld" name="projectbuildcontentOld" class="easyui-textbox"
                                           style="width:450px" readonly="true"
                                           value="${tQhWfbuildprojectchange.projectbuildcontentOld }"/></td>
                                <td colspan="4">
                                    <input id="projectbuildcontentNew" name="projectbuildcontentNew" class="easyui-textbox"
                                           style="width:450px" readonly="true"
                                           value="${tQhWfbuildprojectchange.projectbuildcontentNew }"/></td>
                            </tr>
                            <tr align="center" id="tr_producteartIfChange">
                                <td colspan="2">生產工藝</td>
                                <%--<td colspan="2">--%>
                                <%--&lt;%&ndash;<input id="producteartIfChange" name="producteartIfChange" class="easyui-validatebox"&ndash;%&gt;--%>
                                <%--&lt;%&ndash;data-options="width: 150" value="${tQhWfbuildprojectchange.producteartIfChange }"/>&ndash;%&gt;--%>
                                <%--</td>--%>
                                <input id="producteartIfChange" type="hidden"
                                       name="tQhWfbuildprojectchangeEntity.producteartIfChange"
                                       value="${tQhWfbuildprojectchange.producteartIfChange }"/>
                                <td colspan="4">
                                    <input id="producteartOld" name="producteartOld" class="easyui-textbox" style="width:450px"
                                           readonly="true"
                                           value="${tQhWfbuildprojectchange.producteartOld }"/></td>
                                <td colspan="4">
                                    <input id="producteartNew" name="producteartNew" class="easyui-textbox" style="width:450px"
                                           readonly="true"
                                           value="${tQhWfbuildprojectchange.producteartNew }"/></td>
                            </tr>
                            <tr align="center" id="tr_dischargelinkIfChange">
                                <td colspan="2">污染防治措施</td>
                                <%--<td colspan="2">--%>
                                <%--&lt;%&ndash;<input id="dischargelinkIfChange" name="dischargelinkIfChange" class="easyui-textbox" style="width:350px" disabled="true"&ndash;%&gt;--%>
                                <%--&lt;%&ndash;value="${tQhWfbuildprojectchange.dischargelinkIfChange }"/>&ndash;%&gt;--%>
                                <%--</td>--%>
                                <input id="dischargelinkIfChange" type="hidden"
                                       name="tQhWfbuildprojectchangeEntity.dischargelinkIfChange"
                                       value="${tQhWfbuildprojectchange.dischargelinkIfChange }"/>
                                <td colspan="4">
                                    <input id="dischargelinkOld" name="dischargelinkOld" class="easyui-textbox" style="width:450px"
                                           readonly="true"
                                           value="${tQhWfbuildprojectchange.dischargelinkOld }"/></td>
                                <td colspan="4">
                                    <input id="dischargelinkNew" name="dischargelinkNew" class="easyui-textbox" style="width:450px"
                                           readonly="true"
                                           value="${tQhWfbuildprojectchange.dischargelinkNew }"/></td>
                            </tr>
                            <tr align="center">
                                <td colspan="2">變更說明：</td>
                                <td colspan="8" class="td_style1">
                                    <%--<input id="changeexplain" name="changeexplain" class="easyui-validatebox" readonly--%>
                                    <%--style="width:800px;height:60px;"--%>
                                    <%--rows="5" cols="6" value="${tQhWfbuildprojectchange.changeexplain }"></input>--%>

                                    <textarea id="changeexplain" name="tQhWfbuildprojectchangeEntity.changeexplain" readonly
                                              class="easyui-validatebox"
                                              style="width:800px;height:60px;" rows="5" cols="6"
                                              data-options="required:true">${tQhWfbuildprojectchange.changeexplain }</textarea>
                                </td>
                            </tr>
                            <tr align="center">
                                <td colspan="2">附件：</td>
                                <td colspan="8" class="td_style1">
                                    <div id="dowloadUrl">
                                        <c:forEach items="${file}" varStatus="i" var="item">
                                            <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                                <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                                </div>
                                            </div>
                                        </c:forEach>
                                    </div>

                                </td>
                            </tr>
                            <tr align="center">
                                <td colspan="2">委託事項說明：</td>
                                <td colspan="8" class="td_style1">
                     <textarea id="entrustexplain" name="tQhWfbuildprojectchangeEntity.entrustexplain" readonly
                               class="easyui-validatebox"
                               style="width:800px;height:60px;" rows="5" cols="6"
                               data-options="required:true,prompt:'例：1.委託太原周邊環保科技處環保手續辦理(可研立項，環評、竣工環保驗收)事宜；2.委託太原周邊總務處議價及合同簽訂事宜。'">${tQhWfbuildprojectchange.entrustexplain }</textarea>

                                </td>
                            </tr>

                            <tr align="center">
                                <td colspan="10" width="100%">
                                    <div style="overflow-x: auto;width: 100%;padding-bottom: 15px;">
                                        <table id="buildprojectApplyItemTable" width="100%">
                                            <thead>
                                            <tr>
                                                <th>序號</th>
                                                <th>費用掛靠單位</th>
                                                <th>費用代碼</th>
                                                <th>費用佔比</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <c:forEach items="${itemEntity}" varStatus="i" var="item">
                                                <tr align="center">
                                                    <td>${i.index+1}</td>
                                                    <td>${item.costname}</td>
                                                    <td>${item.costno}</td>
                                                    <td>${item.costrate}</td>
                                                </tr>
                                            </c:forEach>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr align="center">
                                <td colspan="2">附件&nbsp;（補充說明）</td>
                                <td colspan="8" class="td_style1">
                                    <div id="dowloadUrl">
                                        <c:forEach items="${reFile}" varStatus="i" var="item">
                                            <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                                <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                                </div>
                                            </div>
                                        </c:forEach>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                    <a href="javascript:void(0)"
                                       onclick="showWfImag('${processId}','添加新增建設項目環保手續辦理委託申請單');">點擊查看簽核流程圖</a>
                                </th>
                            </tr>
                            <tr>
                                <td colspan="10" style="text-align:left;">
                                    ${chargeNodeInfo}
                                </td>
                            </tr>

                            <tr>
                                <td colspan="10" style="text-align:left;">
                                    <iframe id="qianheLogFrame" name="qianheLogFrame"
                                            src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhWfbuildprojectchange.serialno}"
                                            width="100%"></iframe>
                                </td>
                            </tr>
                            <tr class="no-print">
                                <td colspan="10" style="text-align:center;padding-left:10px;">
                                    <a href="javascript:;" id="btnClose" class="easyui-linkbutton" data-options="iconCls:'icon-cancel'"
                                       style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                    <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</form>
<div id="dlg"></div>

</body>
</html>