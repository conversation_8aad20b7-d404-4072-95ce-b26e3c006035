<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>我的待辦</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input type="text" name="filter_EQS_serialno" class="easyui-validatebox"
               data-options="width:200,prompt: '任務編碼'"/>
        <%--<input type="text" name="filter_GTD_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"--%>
        <%--data-options="width:150,prompt: '簽核完成开始日期'"/>--%>
        <%--- <input type="text" name="filter_LTD_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"--%>
        <%--data-options="width:150,prompt: '簽核完成结束日期'"/>--%>
        <input id="qysjzt" style="width:100px" class="easyui-validatebox" name="filter_EQS_workstatus"/>
        <span class="toolbar-item dialog-tool-separator"></span>
        <input type="text" hidden="true" id="test" value="<shiro:principal property="loginName"/>"/>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
           onclick="listSearchReset()">重置</a>
    </form>
    <shiro:hasPermission name="mytask:batch:audit">
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-cologne-sign-in"
           onclick="batAudit()">批量通過</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-cologne-sign-out"
           onclick="batBack()">批量駁回</a>
    </shiro:hasPermission>
    <div id="passwordDialog"></div>
</div>
<table id="dg"></table>
<script type="text/javascript">
    var dg;
    var d;
    $(function () {
        dg = $('#dg').datagrid({
            method: "post",
            url: '${ctx}/wfcontroller/listMyTask?workFlowId=${workFlowId}&status=${status}&random=<%= Math.random()%>',
            fit: true,
            fitColumns: ${fitColumns==null?true:fitColumns},
            border: false,
            striped: true,
//            idField: 'id',
            pagination: true,
            rownumbers: true,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
//            singleSelect: true,
            columns: ${columns},
            onLoadSuccess: function (data) {
                var ds = data.rows;
                $.each(ds, function (i, v) {
                    if (v.workstatus == '駁回' || v.workstatus == null||v.canBatch=='N') {
//                        $('#dg').datagrid('checkRow', i);
                        $("input[type='checkbox']")[i + 1].disabled = true;
                    }
                });
                $(".easyui-tooltip").tooltip({
                    onShow: function () {
                        $(this).tooltip('tip').css({
                            borderColor: '#000'
                        });
                    }
                });
            },
            onClickRow: function (rowIndex, rowData) {
                //加载完毕后获取所有的checkbox遍历
                $("input[type='checkbox']").each(function (index, el) {
                    //如果当前的复选框不可选，则不让其选中
                    if (el.disabled == true) {
                        $('#dg').datagrid('uncheckRow', index - 1);
                    }
                })
            },
            onCheckAll: function (rows) {
                //加载完毕后获取所有的checkbox遍历
                $("input[type='checkbox']").each(function (index, el) {
                    //如果当前的复选框不可选，则不让其选中
                    if (el.disabled == true) {
                        $('#dg').datagrid('uncheckRow', index - 1);

                    }
                });
            },
            onUncheckAll: function (rows) {
                $("input[type='checkbox']").each(function (index, el) {
                    if (el.disabled != true) {
                        $('#dg').datagrid('checkRow', i);
                    }
                });
            },
            selectOnCheck: true,
            checkOnSelect: true,
            enableHeaderClickMenu: false,
            enableHeaderContextMenu: false,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });
        //初始化審核狀態
        $.ajax({
            url: ctx + "/system/dict/getDictByType/audit_status",
            dataType: "json",
            type: "GET",
            success: function (data) {
                //绑定第一个下拉框
                $("#qysjzt").combobox({
                    data: data,
                    valueField: "value",
                    textField: "label",
                    editable: false,
                    panelHeight: 400,
                    loadFilter: function (data) {
                        data.unshift({
                            value: '',
                            label: '請選擇'
                        });
                        return data;
                    }
                });
            },
            error: function (error) {
                alert("初始化下拉控件失败");
            }
        });
    });

    /*function workFormat1(value, row, index) {
           if (row.workstatus == '簽核中(暫不處理)') {
               var htmlstr = '签核中(<span  style="color: red;">暫不處理</span>)';
               return htmlstr;
           } else {
               return row.workstatus;
           }
       }*/
    function operation(value, row, index) {
        var loginNo = $("#test").val();
        if (row.formFrom == 'E') {
            <%--if (sureIsIEAndLower8()) {--%>
            <%--    return "<a href=\"javascript:goToChrome('" + row.auditAction + "','" + row.id + "');\">" + value + "</a>";--%>
            <%--} else {--%>
            <%--    return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('" + row.wfName + "','" + row.auditAction + "&id=" + row.id + "&empNo=" + loginNo + "&random=<%= Math.random()%>','icon-hamburg-basket')\">" + value + "</a>";--%>
            <%--}--%>
            return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('" + row.wfName + "','" + row.auditAction + "&id=" + row.id + "&empNo=" + loginNo + "&random=<%= Math.random()%>','icon-hamburg-basket')\">" + value + "</a>";
            // return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('" + row.wfName + "','" +"http://************/caaesign/entLiveSiteHire/EntLiveSiteHireAudit?appId=57bcf7ea8d6c92412447ade7d0eceb2c&secretKey=45E2BB7BC92A15DF3A6389FFBB728CAA0DEC3C6DC536E6BB3E40F114244CEF783AB5EE1DDED2EC313D83468D6BD01BEF96555A8DB84ADB7D6A8620E86C1BDA2A20C9BB8EE04372200D5666D9C5A9D78B8C51D2ABC832C2DC4D664BE692921E5AAF9EE26DDD74885D76ADCBEED5A0375C931164ACA1009E4E9FC911AADA63DC7F&id=" + row.id + "&empNo="+row.makerNo+"','icon-hamburg-basket')\">" + value + "</a>";
        } else {
            if (row.wfName.contains("文檔簽核申請") && row.filepassword != null && row.filepassword != '') {
                if (row.skipOrNot == 'Y') {
                    return '<a href="#" onclick="pwdDialog(\'' + row.serialno + '\',\'' + row.filepassword + '\',\'' + row.wfName + '\',\'' + row.auditAction + '\')">' + value + '<span style="color: red;">(緩辦)</span></a>';
                } else {
                    return '<a href="#" onclick="pwdDialog(\'' + row.serialno + '\',\'' + row.filepassword + '\',\'' + row.wfName + '\',\'' + row.auditAction + '\')">' + value + '</a>';
                }
            } else {
                if (row.skipOrNot == 'Y') {
                    return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('" + row.wfName + "',ctx+'/" + row.auditAction + "/" + row.serialno + "?random=<%= Math.random()%>','icon-hamburg-basket')\">" + value + "<span style='color: red;'>(緩辦)</span></a>";
                } else {
                    return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('" + row.wfName + "',ctx+'/" + row.auditAction + "/" + row.serialno + "?random=<%= Math.random()%>','icon-hamburg-basket')\">" + value + "</a>";
                }
            }
        }
    };

    function pwdDialog(serialno, filepassword, wfName, auditAction) {
        d = $("#passwordDialog").dialog({
            title: "操作提醒", // 对话框标题
            width: 300, // 宽度
            height: 200, // 高度
            modal: true, // 是否模态
            content: '<div style="padding:20px;font-size: 14px">此文件加密，密碼將發送至聚會、便易簽，請輸入密碼<br><br><input id="passwordInput" type="password" style="width:100%;"></div>', // 对话框内容
            buttons: [
                {
                    text: "確定",
                    handler: function () {
                        var passwordInput = $("#passwordInput").val(); // 获取输入的密码
                        if (passwordInput === "") {
                            $.messager.alert("操作提示", "請輸入密碼！", "warning");
                        } else {
                            // 发送密码到后端
                            $.ajax({
                                type: "get",
                                url: ctx + "/wffilesignprocess/validFilePassword",
                                beforeSend: ajaxLoading,
                                data: {passwordInput: passwordInput, password: filepassword},
                                success: function (data) {
                                    ajaxLoadEnd();
                                    if (data == "success") {
                                        window.parent.mainpage.mainTabs.addModule(wfName, ctx + "/" + auditAction + "/" + serialno + "?random=" + Math.random(), "icon-hamburg-basket");
                                        d.panel('close');
                                    } else {
                                        $.messager.alert("溫馨提示", "密碼不正確", "warning");
                                    }
                                }
                            });
                        }
                    }
                },
                {
                    text: "獲取密碼",
                    handler: function () {
                        $.ajax({
                            type: "get",
                            url: ctx + "/wffilesignprocess/sendFilePassword",
                            beforeSend: ajaxLoading,
                            data: {serialno: serialno, password: filepassword},
                            success: function (data) {
                                ajaxLoadEnd();
                                successTip(data, dg);
                                $.messager.alert("提示", "密码已发送,请检查您的聚會、便易签。", "info");
                            }
                        });

                    }
                }
            ],
            onClose: function () {
                d.panel('close');
            }
        });

    }

    function dateFormat1(dateString, row, index) {
        if (dateString == null || dateString == '') {
            return '';
        }
        var format = "yyyy-MM-dd hh:mm:ss";
        if (!dateString) return "";
        var time = new Date(dateString);
        var o = {
            "M+": time.getMonth() + 1, //月份
            "d+": time.getDate(), //日
            "h+": time.getHours(), //小时
            "m+": time.getMinutes(), //分
            "s+": time.getSeconds(), //秒
            "q+": Math.floor((time.getMonth() + 3) / 3), //季度
            "S": time.getMilliseconds() //毫秒
        };
        if (/(y+)/.test(format)) format = format.replace(RegExp.$1, (time.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (var k in o)
            if (new RegExp("(" + k + ")").test(format)) format = format.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return format;
    }


    //创建查询对象并查询
    function cx() {
        var obj = $("#searchFrom").serializeObject();
        dg.datagrid('load', obj);
    }

    //表單進度查詢頁面查詢條件重置
    function listSearchReset() {
        $("#searchFrom").form("reset");
    }

    /**
     * 批量通過
     */
    function batAudit() {
        var rowIsSelect = dg.datagrid('getSelected');
        if (rowIsNull(rowIsSelect)) return;
        $.messager.prompt('批量通過', '批註', function (r) {
            if (r||r.localeCompare("")==0) {
                var rows = dg.datagrid('getSelections');
                var serialnos = [];
                for (var i = 0; i < rows.length; i++) {
                    serialnos.push(rows[i].serialno);
                }
                auditBach(JSON.stringify(serialnos), "0", r);
            }
        });
    }

    function batBack() {
        var rowIsSelect = dg.datagrid('getSelected');
        if (rowIsNull(rowIsSelect)) return;
        $.messager.prompt('批量駁回', '批註', function (r) {
            if (r) {
                var rows = dg.datagrid('getSelections');
                var serialnos = [];
                for (var i = 0; i < rows.length; i++) {
                    serialnos.push(rows[i].serialno);
                }
                auditBach(JSON.stringify(serialnos), "1", r);
            } else if (r == undefined) {
            } else if (r.localeCompare("")==0) {
                $.messager.alert('操作提示', '批註不能為空！', 'warning');
            }
        });
    }

    function IEVersion() {
        var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
        var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器
        var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器
        var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
        if (isIE) {
            var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
            reIE.test(userAgent);
            var fIEVersion = parseFloat(RegExp["$1"]);
            if (fIEVersion == 7) {
                return 7;
            } else if (fIEVersion == 8) {
                return 8;
            } else if (fIEVersion == 9) {
                return 9;
            } else if (fIEVersion == 10) {
                return 10;
            } else {
                return 6; //IE版本<=7
            }
        } else if (isEdge) {
            return 'edge'; //edge
        } else if (isIE11) {
            return 11; //IE11
        } else {
            return -1; //不是ie浏览器
        }
    }

    var sureIsIEAndLower8 = function () {
        var version = IEVersion();
        if (-1 == version) {
            return false;
        } else if (8 < version || "edge" == version) {
            return false;
        } else {
            return true;
        }
    }

    function goToChrome(auditAction, id) {
        try {
            var loginNo = $("#test").val();
            var objShell = new ActiveXObject("WScript.Shell");
            objShell.Run('cmd.exe /c start chrome "' + auditAction + '&id=' + id + '&empNo=' + loginNo + '&random=<%= Math.random()%>"', 0, true);
        }catch (e) {
            $.messager.alert('操作提示', '此表單不兼容ie8及以下瀏覽器，請使用谷歌瀏覽器！', "warning");
        }
    }
</script>
</body>
</html>
