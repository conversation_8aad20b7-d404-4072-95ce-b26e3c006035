<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>流程參數配置表</title>
	<script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
	</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
	<div>
		<form id="searchFrom" action="">
			<input type="text" name="filter_EQS_workflowid" class="easyui-validatebox"
				   data-options="width:300,prompt: '流程編碼'"/>
			<input type="text" name="filter_EQS_version" class="easyui-validatebox"
				   data-options="width:150,prompt: '版本'"/>
			<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
			<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
			   onclick="listSearchReset()">重置</a>
			<a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
			   onclick="importExcel();">批量导入</a>
		</form>
		<shiro:hasPermission name="workflow:wfconfigparam:add">
	       		<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="add();">添加</a>
	       		<span class="toolbar-item dialog-tool-separator"></span>
	       	</shiro:hasPermission>
            <shiro:hasPermission name="workflow:wfconfigparam:delete">
	            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" data-options="disabled:false" onclick="del()">删除</a>
	        	<span class="toolbar-item dialog-tool-separator"></span>
	        </shiro:hasPermission>
            <shiro:hasPermission name="workflow:wfconfigparam:update">
	            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="upd()">修改</a>
	            <span class="toolbar-item dialog-tool-separator"></span>
	        </shiro:hasPermission>
	</div>
</div>
<table id="dg"></table>
<div id="dlg"></div>
<div id="optionWin" class="easyui-window" title="流程參數配置批量導入" style="width:350px;height:300px;"
	 collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
	 data-options="iconCls:'PageAdd', footer:'#addFooter'">
	<form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
		<br/>
		<table width="100%">
			<tr align="center">
				<td style="width: 60%; white-space: nowrap;">
					<input id="batchFile" name="batchFile" type="file" style="width: 300px"
						   accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>

				</td>
			</tr>
			<tr align="center">
				<td style="width: 60%; white-space: nowrap;">
					<a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
				</td>
			</tr>
			<tr align="center">
				<td style="width: 60%; white-space: nowrap;">
					<span ID="labelListAddResult"></span><a href="${ctx}/wfconfigparam/downLoad/errorExcel"
															id="downloadError"
															plain="true">查看錯誤信息</a>
				</td>
			</tr>
		</table>
	</form>
</div>
<script src="${ctx}/static/js/workflow/wfconfigparam.js?" +Math.random()></script>
</body>
</html>