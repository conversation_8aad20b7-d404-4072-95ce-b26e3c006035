<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>流程參數配置表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfconfigparam/${action}" method="post">
    <table class="formTable">
        <tr>
            <td style="width: 120px;">流程編碼：</td>
            <td>
                <input id="workflowid" name="workflowid" class="easyui-validatebox" data-options="width: 300"
                       value="${wfConfigparam.workflowid }"/>
            </td>
        </tr>
        <tr>
            <td>參數名稱：</td>
            <td>
                <input id="paramename" name="paramename" class="easyui-validatebox" data-options="width: 300"
                       value="${wfConfigparam.paramename }"/>
            </td>
        </tr>
        <tr>
            <td>條件字段sqrjbpd=1 派課級主管 sqrjbpd=2派部級主管 sqrjbpd=3派廠級主管 sqrjbpd=4派製造處 sqrjbpd=5 派製造總處
                sqrjbpd=6派產品處主管 ：
            </td>
            <td>
                <input id="paramvalue" name="paramvalue" class="easyui-validatebox" data-options="width: 300"
                       value="${wfConfigparam.paramvalue }"/>
            </td>
        </tr>
        <tr>
            <td>參數類型：</td>
            <td>
                <input id="paramtype" name="paramtype" class="easyui-validatebox" data-options="width: 300"
                       value="${wfConfigparam.paramtype }"/>
            </td>
        </tr>
        <tr>
            <td>主鍵：</td>
            <td>
                <input id="id" name="id" class="easyui-validatebox" data-options="width: 300"
                       value="${wfConfigparam.id }"/>
            </td>
        </tr>
        <tr>
            <td>創建人：</td>
            <td>
                <input id="createBy" name="createBy" class="easyui-validatebox" data-options="width: 300"
                       value="${wfConfigparam.createBy }"/>
            </td>
        </tr>
        <tr>
            <td>創建時間：</td>
            <td>
                <input id="createDate" name="createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
                       data-options="width: 300" value="<fmt:formatDate value="${wfConfigparam.createDate}"/>"/>
            </td>
        </tr>
        <tr>
            <td>更新者：</td>
            <td>
                <input id="updateBy" name="updateBy" class="easyui-validatebox" data-options="width: 300"
                       value="${wfConfigparam.updateBy }"/>
            </td>
        </tr>
        <tr>
            <td>更新時間：</td>
            <td>
                <input id="updateDate" name="updateDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
                       data-options="width: 300" value="<fmt:formatDate value="${wfConfigparam.updateDate}"/>"/>
            </td>
        </tr>
        <tr>
            <td>刪除標識：</td>
            <td>
                <input id="delFlag" name="delFlag" class="easyui-validatebox" data-options="width: 300"
                       value="${wfConfigparam.delFlag }"/>
            </td>
        </tr>
        <tr>
            <td>版本號：</td>
            <td>
                <input id="version" name="version" class="easyui-validatebox" data-options="width: 300"
                       value="${wfConfigparam.version }"/>
            </td>
        </tr>
    </table>
</form>
</div>
<script src='${ctx}/static/js/workflow/wfconfigparam.js?"+Math.random()"'></script>
<script type="text/javascript">
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            successTip(data, dg, d);
        }
    });
</script>
</body>
</html>