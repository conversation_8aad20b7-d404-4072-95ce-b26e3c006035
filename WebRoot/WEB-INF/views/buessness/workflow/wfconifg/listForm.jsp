<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>流程信息配置表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfconifg/${action}" method="post">
    <input id="id" name="id" value="${wfConifg.id }" type="hidden"/>
    <table class="formTable">
        <tr>
            <td style="width: 120px">流程編碼：</td>
            <td>
                <input id="workflowid" name="workflowid" class="easyui-validatebox" data-options="width: 400,required:'required'"
                       value="${wfConifg.workflowid }"/>
            </td>
        </tr>
        <tr>
            <td>流程名稱：</td>
            <td>
                <input id="workflowname" name="workflowname" class="easyui-validatebox" data-options="width: 400,required:'required'"
                       value="${wfConifg.workflowname }"/>
            </td>
        </tr>
        <tr>
            <td>表單簽核處理方法：</td>
            <td>
                <input id="action" name="action" class="easyui-validatebox" data-options="width: 400,required:'required'"
                       value="${wfConifg.action }"/>
            </td>
        </tr>
        <tr>
            <td>表單修改處理方法：</td>
            <td>
                <input id="modaction" name="modaction" class="easyui-validatebox" data-options="width: 400,required:'required'"
                       value="${wfConifg.modaction }"/>
            </td>
        </tr>
        <tr>
            <td>表單臨時保存方法：</td>
            <td>
                <input id="saveaction" name="saveaction" class="easyui-validatebox" data-options="width: 400,required:'required'"
                       value="${wfConifg.saveaction }"/>
            </td>
        </tr>
        <tr>
            <td>表單詳情方法：</td>
            <td>
                <input id="detailaction" name="detailaction" class="easyui-validatebox" data-options="width: 400,required:'required'"
                       value="${wfConifg.detailaction }"/>
            </td>
        </tr>
        <tr>
            <td>是否需要滾動條：</td>
            <td>
                <input id="dynfield01" name="dynfield01" class="easyui-validatebox" data-options="width: 400"
                       value="${wfConifg.dynfield01 }"/>
            </td>
        </tr>
        <tr>
            <td>子表類名：</td>
            <td>
                <input id="dynfield02" name="dynfield02" class="easyui-validatebox" data-options="width: 400"
                       value="${wfConifg.dynfield02 }"/>
            </td>
        </tr>
        <tr>
            <td>子表查詢排序字段：</td>
            <td>
                <input id="dynfield03" name="dynfield03" class="easyui-validatebox" data-options="width: 400"
                       value="${wfConifg.dynfield03 }"/>
            </td>
        </tr>
        <tr>
            <td>版本：</td>
            <td>
                <input id="version" name="version" class="easyui-validatebox" data-options="width: 400,required:'required'"
                       value="${wfConifg.version }"/>
            </td>
        </tr>
        <tr>
            <td>表單來源：</td>
            <td>
                <input id="workflowcode" name="workflowcode" class="easyui-combobox" data-options="width: 400,required:'required'"
                       value="${wfConifg.workflowcode }"/>
            </td>
        </tr>
        <tr>
            <td>是否app審核：</td>
            <td>
                <input id="whetherApp" name="whetherApp"  class="easyui-combobox" data-options="width: 400"
                       value="${wfConifg.whetherApp }"/>
            </td>
        </tr>
        <tr>
            <td>app審核url：</td>
            <td>
                <input id="appAuditUrl" name="appAuditUrl" class="easyui-validatebox" data-options="width: 400"
                       value="${wfConifg.appAuditUrl }"/>
            </td>
        </tr>
        <tr>
            <td>app詳情url：</td>
            <td>
                <input id="appDetailUrl" name="appDetailUrl" class="easyui-validatebox" data-options="width: 400"
                       value="${wfConifg.appDetailUrl }"/>
            </td>
        </tr>
        <tr>
            <td>app類型：</td>
            <td>
                <input id="appType" name="appType" class="easyui-combobox" data-options="width: 400"
                       value="${wfConifg.appType }"/>
            </td>
        </tr>
        <tr>
            <td>app端返回跳轉url：</td>
            <td>
                <input id="appBackUrl" name="appBackUrl" class="easyui-combobox" data-options="width: 400"
                       value="${wfConifg.appBackUrl }"/>
            </td>
        </tr>
        <tr>
            <td>存儲路徑：</td>
            <td>
                <input id="oosFilePath" name="oosFilePath" class="easyui-combobox" data-options="width: 400"
                       value="${wfConifg.oosFilePath }"/>
            </td>
        </tr>
        <tr>
            <td>是否自研流程：</td>
            <td>
                <input id="isLocalFlow" name="isLocalFlow" class="easyui-combobox" data-options="width: 400,required:'required', editable: false, valueField: 'itemValue', textField: 'itemName',data:[{itemValue: 'Y', itemName: '是'}, {itemValue: 'N', itemName: '否'}]"
                       value="${wfConifg.isLocalFlow }"/>
            </td>
        </tr>
        <tr>
            <td>簽核線是否不顯示空節點：</td>
            <td>
                <input id="signPathTemp" name="signPathTemp" class="easyui-combobox" data-options="width: 400,required:'required', editable: false, valueField: 'itemValue', textField: 'itemName',data:[{itemValue: 'Y', itemName: '是'}, {itemValue: 'N', itemName: '否'}]"
                       value="${wfConifg.signPathTemp }"/>
            </td>
        </tr>
        <tr>
            <td>是否自動審核：</td>
            <td>
                <input id="autoAudit" name="autoAudit" class="easyui-combobox" data-options="width: 400,required:'required',editable: false, valueField: 'itemValue', textField: 'itemName',data:[{itemValue: 'Y', itemName: '是'}, {itemValue: 'N', itemName: '否'}]"
                       value="${wfConifg.autoAudit }"/>
            </td>
        </tr>
    </table>
</form>
</div>
<script src='${ctx}/static/js/workflow/wfconifg.js?'+Math.random()></script>
<script type="text/javascript">
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            successTip(data, dg, d);
        }
    });
</script>
</body>
</html>