<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>流程中會簽節點流程規則</title>

    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfnoderule/${action}" method="post">
    <table class="formTable">
        <tr>
            <td style="width: 120px;">主鍵：</td>
            <td>
                <input id="id" name="id" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNoderule.id }"/>
            </td>
        </tr>
        <tr>
            <td>創建人：</td>
            <td>
                <input id="createBy" name="createBy" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNoderule.createBy }"/>
            </td>
        </tr>
        <tr>
            <td>創建時間：</td>
            <td>
                <input id="createDate" name="createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
                       data-options="width: 300" value="<fmt:formatDate value="${wfNoderule.createDate}"/>"/>
            </td>
        </tr>
        <tr>
            <td>更新者：</td>
            <td>
                <input id="updateBy" name="updateBy" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNoderule.updateBy }"/>
            </td>
        </tr>
        <tr>
            <td>更新時間：</td>
            <td>
                <input id="updateDate" name="updateDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
                       data-options="width: 300" value="<fmt:formatDate value="${wfNoderule.updateDate}"/>"/>
            </td>
        </tr>
        <tr>
            <td>刪除標識：</td>
            <td>
                <input id="delFlag" name="delFlag" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNoderule.delFlag }"/>
            </td>
        </tr>
        <tr>
            <td>節點id：</td>
            <td>
                <input id="nodeid" name="nodeid" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNoderule.nodeid }"/>
            </td>
        </tr>
        <tr>
            <td>會簽節點會簽核參數名：</td>
            <td>
                <input id="nodeparamname" name="nodeparamname" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNoderule.nodeparamname }"/>
            </td>
        </tr>
        <tr>
            <td>會簽節點人總數：</td>
            <td>
                <input id="totalpeople" name="totalpeople" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNoderule.totalpeople }"/>
            </td>
        </tr>
        <tr>
            <td>會簽通過率：</td>
            <td>
                <input id="passrate" name="passrate" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNoderule.passrate }"/>
            </td>
        </tr>
        <tr>
            <td>會簽通過類型1一票否決 2一票通過 3 超過某個比例才算通過：</td>
            <td>
                <input id="voterule" name="voterule" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNoderule.voterule }"/>
            </td>
        </tr>
        <tr>
            <td>會簽狀態變更名稱：</td>
            <td>
                <input id="taskstatusvariable" name="taskstatusvariable" class="easyui-validatebox"
                       data-options="width: 300" value="${wfNoderule.taskstatusvariable }"/>
            </td>
        </tr>
        <tr>
            <td>會簽節點后分支變更：</td>
            <td>
                <input id="taskcompletevariable" name="taskcompletevariable" class="easyui-validatebox"
                       data-options="width: 300" value="${wfNoderule.taskcompletevariable }"/>
            </td>
        </tr>
        <tr>
            <td>版本號：</td>
            <td>
                <input id="version" name="version" class="easyui-validatebox"
                       data-options="width: 300" value="${wfNoderule.version }"/>
            </td>
        </tr>
    </table>
</form>
</div>
<script src='${ctx}/static/js/workflow/wfnoderule.js?"+Math.random()"'></script>
<script type="text/javascript">
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            successTip(data, dg, d);
        }
    });
</script>
</body>
</html>