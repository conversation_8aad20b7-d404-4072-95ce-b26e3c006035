<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title></title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>

</head>
<body style="font-family: '微软雅黑'">
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <textarea name="serialno" id="serialno" class="easyui-validatebox" cols="1" rows="2"
                  data-options="width:600,prompt: '任務編碼（需要查詢多個使用逗號隔開）'"></textarea>
        <input type="text" id="startDate" name="startDate" class="easyui-my97" datefmt="yyyy-MM-dd"
        data-options="width:150,prompt: '簽核開始時間'"/>
        <input type="text" id="endDate" name="endDate" class="easyui-my97" datefmt="yyyy-MM-dd"
               data-options="width:150,prompt: '簽核結束時間'"/>
        <input id="qysjzt" style="width:100px" class="easyui-validatebox" name="workstatus"/>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
           onclick="listSearchReset()">重置</a>
        <span class="toolbar-item dialog-tool-separator"></span>
        <input type="text" hidden="true" id="test" value="<shiro:principal property="loginName"/>"/>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
           onclick="exportExcel()">导出Excel</a>
        <!--導出用，請勿刪除-->
        <input id="page" name="page" type="hidden" value="1"/>
        <input id="rows" name="rows" type="hidden" value="30"/>

    </form>
    <div id="passwordDialog"></div>
</div>
<table id="dg"></table>
<script type="text/javascript">
    var dg;
    var d;
    $(function () {
        dg = $('#dg').datagrid({
            method: "post",
            url: '${ctx}/wfcontroller/listMyDownTask?random=<%= Math.random()%>',
            fit: true,
            fitColumns: true,
            border: false,
            striped: true,
            idField: 'id',
            pagination: true,
            rownumbers: true,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: true,
            columns: [[
                {field: 'serialno', title: '表單流水號', width: 50, formatter: operation},
                {field: 'auditAction', title: '流程審核', width: 50, hidden: true},
                {field: 'type', title: '是否加密', width: 50, hidden: true},
                {field: 'makerno', title: '填單人工號', width: 25},
                {field: 'makername', title: '填單人', width: 25},
                {field: 'wfName', title: '表單類型', width: 80},
                {field: 'taskName', title: '簽核節點', width: 50},
                // {field: 'updateDate', title: '簽核時間', width: 80,formatter:formatDate},
                {field: 'createtime', title: '填單時間', width: 50, formatter: dateFormat},
                {field: 'workstatus', title: '表單狀態', width: 80},
            ]],
            enableHeaderClickMenu: false,
            enableHeaderContextMenu: false,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });

        //初始化審核狀態
        $.ajax({
            url: ctx + "/system/dict/getDictByType/audit_status",
            dataType:"json",
            type: "GET",
            success: function (data) {
                //绑定第一个下拉框
                $("#qysjzt").combobox({
                    data: data,
                    valueField: "value",
                    textField: "label",
                    editable: false,
                    panelHeight: 400,
                    loadFilter: function (data) {
                        data.unshift({
                            value: '',
                            label: '審核狀態'
                        });
                        return data;
                    }
                });
            },
            error: function (error) {
                alert("初始化下拉控件失败");
            }
        });
    });

    function operation(value, row, index) {
        var loginNo = $("#test").val();
        if (row.formFrom == 'E') {
            // return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('" + row.wfName + "','" + "http://10.76.213.33/caaesign/entLiveSiteHire/EntLiveSiteHireDetail?id=" + row.id + "&appId=57bcf7ea8d6c92412447ade7d0eceb2c&secretKey=45E2BB7BC92A15DF3A6389FFBB728CAA0DEC3C6DC536E6BB3E40F114244CEF783AB5EE1DDED2EC313D83468D6BD01BEF96555A8DB84ADB7D6A8620E86C1BDA2A20C9BB8EE04372200D5666D9C5A9D78B8C51D2ABC832C2DC4D664BE692921E5AAF9EE26DDD74885D76ADCBEED5A0375C931164ACA1009E4E9FC911AADA63DC7F&empNo=" + row.makerno + "','icon-hamburg-basket')\">" + value + "</a>";
            return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('" + row.wfName + "','" + row.auditAction + "&id=" + row.id + "&empNo=" + loginNo + "&random=<%= Math.random()%>','icon-hamburg-basket')\">" + value + "</a>";
        } else {
            if (row.wfName.contains("文檔簽核申請") && row.type != null && row.type != '') {
                console.log("row.type:" + row.type);
                return '<a href="#" onclick="pwdDialog(\'' + row.serialno + '\',\'' + row.type + '\',\'' + row.wfName + '\',\'' + row.auditAction + '\')">' + value + '</a>';
            } else {
                return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('" + row.wfName + "',ctx+'/" + row.auditAction + "/" + row.serialno + "?random=<%= Math.random()%>','icon-hamburg-basket')\">" + value + "</a>";
            }
        }
    };

    function pwdDialog(serialno, filepassword, wfName, auditAction) {
        d = $("#passwordDialog").dialog({
            title: "操作提醒", // 对话框标题
            width: 300, // 宽度
            height: 200, // 高度
            modal: true, // 是否模态
            content: '<div style="padding:20px;font-size: 14px">此文件加密，密碼將發送至聚會、便易簽，請輸入密碼<br><br><input id="passwordInput" type="password" style="width:100%;"></div>', // 对话框内容
            buttons: [
                {
                    text: "確定",
                    handler: function () {
                        var passwordInput = $("#passwordInput").val(); // 获取输入的密码
                        if (passwordInput === "") {
                            $.messager.alert("操作提示", "請輸入密碼！", "warning");
                        } else {
                            // 发送密码到后端
                            $.ajax({
                                type: "get",
                                url: ctx + "/wffilesignprocess/validFilePassword",
                                beforeSend: ajaxLoading,
                                data: {passwordInput: passwordInput, password: filepassword},
                                success: function (data) {
                                    ajaxLoadEnd();
                                    if (data == "success") {
                                        window.parent.mainpage.mainTabs.addModule(wfName, ctx + "/" + auditAction + "/" + serialno + "?random=" + Math.random(), "icon-hamburg-basket");
                                        d.panel('close');
                                    } else {
                                        $.messager.alert("溫馨提示", "密碼不正確", "warning");
                                    }
                                }
                            });
                        }
                    }
                },
                {
                    text: "獲取密碼",
                    handler: function () {
                        $.ajax({
                            type: "get",
                            url: ctx + "/wffilesignprocess/sendFilePassword",
                            beforeSend: ajaxLoading,
                            data: {serialno: serialno, password: filepassword},
                            success: function (data) {
                                ajaxLoadEnd();
                                successTip(data, dg);
                                $.messager.alert("提示", "密码已发送,请检查您的聚會、便易签。", "info");
                            }
                        });

                    }
                }
            ],
            onClose: function () {
                d.panel('close');
            }
        });

    }

    //创建查询对象并查询
    function cx() {
        var obj = $("#searchFrom").serializeObject();
        dg.datagrid('load', obj);
    }

    //表單進度查詢頁面查詢條件重置
    function listSearchReset() {
        $("#searchFrom").form("reset");
    }

    //表單進度查詢頁面Excel導出
    function exportExcel() {
        // if ((!isEmpty($("#serialno").val()))||(!isEmpty($("#startDate").datebox('getValue')))) {
        //     if (!isEmpty($("#startDate").datebox('getValue'))) {
        //         if (isEmpty($("#endDate").datebox('getValue'))) {
        //             $.messager.alert("溫馨提示", "請選擇結束時間", "info");
        //             return false;
        //         }
        //         var starDate = new Date($("#startDate").datebox('getValue')).getTime();
        //         var endDate = new Date($("#endDate").datebox('getValue')).getTime();
        //         var day = (endDate-starDate)/(24*60*60*1000)
        //         if (day > 365) {
        //             $.messager.alert("溫馨提示", "所選時間間隔不能超過一年", "info");
        //             return false;
        //         }
        //     }
        //     var options = $('#dg').datagrid('getPager').data("pagination").options;
        //     var page = options.pageNumber;//当前页数
        //     var total = options.total;
        //     var rows = options.pageSize;//每页的记录数（行数）
        //     var form = document.getElementById("searchFrom");
        //     $('#page').val(page);
        //     $('#rows').val(total);
        //     searchFrom.action=ctx+'/wfcontroller/exportExcel';
        //     form.submit();
        //     parent.$.messager.alert("溫馨提示","正在導出，請耐心等待，期間不要操作系統！","warning");
        // }else{
        //     $.messager.alert("溫馨提示", "請選擇查詢時間", "info");
        // }
        var options = $('#dg').datagrid('getPager').data("pagination").options;
        var page = options.pageNumber;//当前页数
        var total = options.total;
        var rows = options.pageSize;//每页的记录数（行数）
        var form = document.getElementById("searchFrom");
        $('#page').val(page);
        $('#rows').val(total);
        searchFrom.action=ctx+'/wfcontroller/exportExcel';
        form.submit();
        parent.$.messager.alert("溫馨提示","正在導出，請耐心等待，期間不要操作系統！","warning");
    }
</script>
</body>
</html>
