<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>流程節點出口信息表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfnodeparam/${action}" method="post">
    <table class="formTable" style="text-align: right">
        <input id="id" name="id" class="easyui-validatebox" data-options="width: 150" type="hidden" value="${wfNodeparam.id }"/>
        <tr>
            <td style="width: 120px;">流程編碼：</td>
            <td>
                <input id="workflowid" name="workflowid" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeparam.workflowid }"/>
            </td>
        </tr>
        <tr>
            <td>節點編碼：</td>
            <td>
                <input id="nodeid" name="nodeid" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeparam.nodeid }"/>
            </td>
        </tr>
        <tr>
            <td>參數名稱：</td>
            <td>
                <input id="paramename" name="paramename" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeparam.paramename }"/>
            </td>
        </tr>
        <tr>
            <td>參數值：</td>
            <td>
                <input id="paramvalue" name="paramvalue" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeparam.paramvalue }"/>
            </td>
        </tr>
        <tr>
            <td>參數類型：</td>
            <td>
                <input id="paramtype" name="paramtype" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeparam.paramtype }"/>
            </td>
        </tr>
        <tr>
            <td>去向描述：</td>
            <td>
                <input id="describ" name="describ" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeparam.describ }"/>
            </td>
        </tr>
        <tr>
            <td>表單目錄狀態：</td>
            <td>
                <input id="toworkstatus" name="toworkstatus" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeparam.toworkstatus }"/>
            </td>
        </tr>
        <tr>
            <td>操作標識：</td>
            <td>
                <input id="ispass" name="ispass" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeparam.ispass }"/>
            </td>
        </tr>
        <tr>
            <td>版本號：</td>
            <td>
                <input id="version" name="version" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeparam.version }"/>
            </td>
        </tr>
        <tr>
            <td>是否需要發送短信(1需要 0不需要)：</td>
            <td>
                <input id="isNeedSms" name="isNeedSms" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeparam.isNeedSms }"/>
            </td>
        </tr>
        <tr>
            <td>短信模版前綴：</td>
            <td>
                <input id="smsTemplete" name="smsTemplete" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeparam.smsTemplete }"/>
            </td>
        </tr>
        <tr>
            <td>發送短信手機字段名稱：</td>
            <td>
                <input id="smsCol" name="smsCol" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeparam.smsCol }"/>
            </td>
        </tr>
    </table>
</form>
</div>
<script src='${ctx}/static/js/workflow/wfnodeparam.js?"+Math.random()"'></script>
<script type="text/javascript">
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            successTip(data, dg, d);
        }
    });
</script>
</body>
</html>