<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>流程節點配置表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfnodeinfo/${action}" method="post" style="text-align: right">
    <input id="id" name="id" type="hidden" value="${wfNodeinfo.id }"/>
    <table class="formTable">
        <tr>
            <td style="width: 120px;">節點編碼：</td>
            <td>
                <input id="nodeid" name="nodeid" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeinfo.nodeid }"/>
            </td>
        </tr>
        <tr>
            <td>流程編碼：</td>
            <td>
                <input id="workflowid" name="workflowid" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeinfo.workflowid }"/>
            </td>
        </tr>
        <tr>
            <td>節點名稱：</td>
            <td>
                <input id="nodename" name="nodename" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeinfo.nodename }"/>
            </td>
        </tr>
        <tr>
            <td>排序號：</td>
            <td>
                <input id="orderby" name="orderby" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeinfo.orderby }"/>
            </td>
        </tr>
        <tr>
            <td>簽核節點類型 0 普通簽核 1會簽：</td>
            <td>
                <input id="signtype" name="signtype" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeinfo.signtype }"/>
            </td>
        </tr>
        <tr>
            <td>節點簽核人是否必填 Y 必填 N非必填：</td>
            <td>
                <input id="required" name="required" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeinfo.required }"/>
            </td>
        </tr>
        <tr>
            <td>表單表保存簽核主管的字段名稱：</td>
            <td>
                <input id="colname" name="colname" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeinfo.colname }"/>
            </td>
        </tr>
        <tr>
            <td>該結點是否可以參與批量簽核：</td>
            <td>
                <input id="canbatch" name="canbatch" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeinfo.canbatch }"/>
            </td>
        </tr>
        <tr>
            <td>節點別名：</td>
            <td>
                <input id="nodealain" name="nodealain" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeinfo.nodealain }"/>
            </td>
        </tr>
        <tr>
            <td>版本號：</td>
            <td>
                <input id="version" name="version" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeinfo.version }"/>
            </td>
        </tr>
        <tr>
            <td>擴展字段：</td>
            <td>
                <input id="dynfield01" name="dynfield01" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeinfo.dynfield01 }"/>
            </td>
        </tr>
        <tr>
            <td>擴展字段：</td>
            <td>
                <input id="dynfield02" name="dynfield02" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeinfo.dynfield02 }"/>
            </td>
        </tr>
        <tr>
            <td>擴展字段：</td>
            <td>
                <input id="dynfield03" name="dynfield03" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeinfo.dynfield03 }"/>
            </td>
        </tr>
        <tr>
            <td>擴展字段：</td>
            <td>
                <input id="dynfield04" name="dynfield04" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeinfo.dynfield04 }"/>
            </td>
        </tr>
        <tr>
            <td>擴展字段：</td>
            <td>
                <input id="dynfield05" name="dynfield05" class="easyui-validatebox" data-options="width: 300"
                       value="${wfNodeinfo.dynfield05 }"/>
            </td>
        </tr>
    </table>
</form>
<script src='${ctx}/static/js/workflow/wfnodeinfo.js?' +Math.random()></script>
<script type="text/javascript">
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            successTip(data, dg, d);
        }
    });
</script>
</body>
</html>