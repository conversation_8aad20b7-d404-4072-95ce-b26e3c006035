<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>${comments}</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/tproxyuserinfo/${action}" method="post">
    <!--
          suppliedempno 被代理人工號
suppliedusername 被代理人姓名
supplyempno 代理人工號
supplyusername 代理人姓名
supplyreason 代理原因
supplybegin 代理開始時間
supplyend 代理結束時間
isvalid 字典：DICT_ISVALID
setmail 設置發送提醒郵件
cancelmail 取消發送提醒郵件
id 主鍵
createBy 創建人
createDate 創建時間
updateBy 更新者
updateDate 更新時間
delFlag 刪除標識
      -->
    <table>
        <tr>
            <td style="text-align: right"><font color="red">注意事項：</font></td>
            <td><font color="red">在代理人代理有效期間，所有被代理人的審核通知（包括及時郵件，催辦郵件以及聚慧消息）將會發送給代理人，敬請知會代理人！</font></td>
        </tr>
        <tr>
            <td width="100" style="text-align: right">被代理人工號：</td>
            <td>
                <input type="hidden" name="id" value="${user.id }"/>
                <input id="suppliedempno" name="suppliedempno" class="easyui-validatebox" style="background: #cfcfcf"
                       data-options="width: 100,readonly:true" value="${user.suppliedempno }" readonly/>/
                <input id="suppliedusername" name="suppliedusername" class="easyui-validatebox" style="background: #cfcfcf"
                       data-options="width: 100,readonly:true" value="${user.suppliedusername }" readonly/>
            </td>
        </tr>
        <tr>
            <td style="text-align: right">代理人工號：</td>
            <td>
                <c:choose>
                    <c:when test="${entityLast.supplyempno!=null}">
                        <input id="supplyempno" name="supplyempno" class="easyui-validatebox"
                               data-options="width: 100" value="${entityLast.supplyempno }"
                               onblur="getUserNameByEmpno(this);"/>/
                        <input id="supplyusername" name="supplyusername" class="easyui-validatebox" style="background: #cfcfcf"
                               data-options="width: 100,readonly:true" value="${entityLast.supplyusername }" readonly/>
                    </c:when>
                    <c:otherwise>
                        <input id="supplyempno" name="supplyempno" class="easyui-validatebox"
                               data-options="width: 100" value="${user.supplyempno }"
                               onblur="getUserNameByEmpno(this);"/>/
                        <input id="supplyusername" name="supplyusername" class="easyui-validatebox" style="background: #cfcfcf"
                               data-options="width: 100,readonly:true" value="${user.supplyusername }" readonly/>
                    </c:otherwise>
                </c:choose>
            </td>
        </tr>
        <tr>
            <td style="text-align: right">代理原因：</td>
            <td><textarea rows="8" id="supplyreason" name="supplyreason" class="easyui-validatebox"
                          value="${user.supplyreason }" data-options="width: 400,prompt:'请输入代理原因',required:true"/></td>
        </tr>
        <tr>
            <td style="text-align: right">代理開始時間：</td>
            <td><input name="supplybegin" id="supplybegin" type="text" class="easyui-validatebox Wdate"
                       data-options="width: 200,required:true,prompt:'请选择开始时间'"
                       value="<fmt:formatDate value="${user.supplybegin}" pattern='yyyy-MM-dd HH:mm'/>"
                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',minDate:'%y-%M-%d %H:%m'})"/></td>
        </tr>
        <tr>
            <td style="text-align: right">代理結束時間：</td>
            <td><input name="supplyend" type="text" class="easyui-validatebox Wdate" data-options="width: 200,required:true,prompt:'请选择结束时间'"
                       value="<fmt:formatDate value="${user.supplyend}" pattern='yyyy-MM-dd HH:mm'/>"
                       onclick="WdatePicker({minDate:'#F{$dp.$D(\'supplybegin\')}',dateFmt:'yyyy-MM-dd HH:mm'})"/>
            </td>
        </tr>
        <tr>
            <td style="text-align: right">表單名稱：</td>
            <td><input name="workflowid" id="workflowid" class="easyui-combobox" data-options="width: 300" value="${user.workflowid}" /></td>
        </tr>
        <tr>
            <td style="text-align: right">節點名稱：</td>
            <td><input name="nodename" value="${user.nodename}" type="text" class="easyui-validatebox" data-options="width: 200,prompt:'请输入節點名稱'"/></td>
        </tr>
        <tr>
            <td style="text-align: right">組織：</td>
            <td><textarea rows="5" id="deptNoCollection" name="deptNoCollection" class="easyui-validatebox" data-options="width: 400,prompt:'多個組織請用逗號隔開'" value="${user.deptNoCollection }"></textarea></td>
        </tr>
        <tr>
            <td style="text-align: right">優先級：</td>
            <td><input name="priorityLevel" value="${user.priorityLevel}" type="text" class="easyui-numberspinner" data-options="width: 200,prompt:'请输入優先級',validType:'number',invalidMessage:'请输入有效数字'"/></td>
        </tr>
        <input type="hidden" name="proxType" value="P"/>
        <input type="hidden" name="workflowName" id="workflowName" value="${user.workflowName }"/>
    </table>
</form>
<script src='${ctx}/static/js/common/tproxyuserinfo.js?random=<%= Math.random()%>'></script>
<script type="text/javascript">
        $("#workflowid").combobox({
            url:ctx + "/tproxyuserinfo/getFormNameSelectOption",
            method: 'get',
            prompt:"请选择或输入内容...",
            valueField : "value",
            textField : "label",
            panelHeight : 350,
            onSelect: function(record){
                $("#workflowName").val(record.label);
            }
        });
</script>
</body>
</html>
