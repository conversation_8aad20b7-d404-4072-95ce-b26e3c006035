<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>${comments}</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<%--<div id="tb1" style="padding:5px;height:auto">--%>
<%--        <form id="searchFrom" action="">--%>
<%--		<input type="text" name="filter_EQS_supplyempno" class="easyui-validatebox"--%>
<%--			   data-options="width:150,prompt: '代理人工號'"/>--%>
<%--		<span class="toolbar-item dialog-tool-separator"></span>--%>
<%--		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>--%>
<%--		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"--%>
<%--		   onclick="listSearchReset()">重置</a>--%>
<%--		<a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"--%>
<%--		   onclick="exportExcel()">导出Excel</a>--%>
<%--	</form>--%>
<%--  </div>--%>
<table id="dg1"></table>
<div id="dlg1"></div>

<script type="application/javascript">
	var dg1;
	var d1;
	$(function () {
		var flag = "${flag}";
		dg1 = $('#dg1').datagrid({
			method: "get",
			url: ctx + '/tproxyuserinfo/list?flag='+flag,
			fit: true,
			fitColumns: false,
			border: false,
			idField: 'suppliedempno',
			striped: true,
			pagination: true,
			rownumbers: true,
			cache: false,  //关闭AJAX相应的缓存
			pageNumber: 1,
			pageSize: 10,
			pageList: [10, 20, 30, 40, 50],
			singleSelect: true,
            frozenColumns:[[
                {field: 'suppliedempno', title: '被代理人工號', sortable: true, width: 100},
                {field: 'suppliedusername', title: '被代理人姓名', sortable: true, width: 100},
                {field: 'supplyempno', title: '代理人工號', sortable: true, width: 80},
                {field: 'supplyusername', title: '代理人姓名', sortable: true, width: 80},
                {field: 'proxType', title: '代理類型', sortable: true, width: 100,formatter:function(value,row,index){
                        if(value=='A'){
                            return '全局代理';
                        }else if(value=='P'){
                            return '特殊代理';
                        }else{
                            return value;
                        }
                    }},
            ]],
            columns: [[
                {field: 'supplyreason', title: '代理原因', sortable: true, width: 350,formatter: function(value,row,index){
                        if(value!=null||value!=undefined&&value!='') {
                            return '<font style="color:#297dd1;"><span title=\"' + value + '\">' + value + '</span></font>';
                        }else{
                            return value;
                        }
                    }},
                {field: 'supplybegin', title: '代理開始時間', sortable: true, width: 120,formatter:formatDateHHmm},
                {field: 'supplyend', title: '代理結束時間', sortable: true, width: 120,formatter:formatDateHHmm},
                {field: 'isvalid', title: '是否有效', sortable: true, width: 60},
                {field: 'workflowName', title: '表單名稱', sortable: true, width: 300},
                {field: 'workflowid', title: '表單Key', sortable: true, width: 300},
                {field: 'nodename', title: '節點名稱', sortable: true, width: 200},
                {field: 'deptNoCollection', title: '組織', sortable: true, width: 350,formatter: function(value,row,index){
                        if(value!=null||value!=undefined&&value!='') {
                            return '<font style="color:#297dd1;"><span title=\"' + value + '\">' + value + '</span></font>';
                        }else{
                            return value;
                        }
                    }},
                {field: 'id', title: '主鍵', hidden: true},
                {field: 'whetherFlag', title: '是否刪除', sortable: true, width: 100,formatter:function(value,row,index){
                        if(value=='0'){
                            return '否';
                        }else if(value=='1'){
                            return '是';
                        }else{
                            return value;
                        }
                    }}
            ]],
			enableHeaderClickMenu: true,
			enableHeaderContextMenu: true,
			enableRowContextMenu: false,
			toolbar: '#tb1',
			onLoadSuccess: function (data) {
				$('.mybutton').linkbutton({text: '删除', plain: true, iconCls: 'icon-remove'});
			}
		});

		$('#mainform').form({
			onSubmit: function () {
				var isValid = $(this).form('validate');
				return isValid;	// 返回false终止表单提交
			},
			success: function (data) {
				successTip(data, dg1, d1);
			}
		});
		$('#supplybegin').val(new Date().format("yyyy-MM-dd"));
	});
</script>
</body>
</html>