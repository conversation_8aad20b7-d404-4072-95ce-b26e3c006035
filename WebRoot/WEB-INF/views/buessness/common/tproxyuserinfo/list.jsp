<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>${comments}</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
        <form id="searchFrom" action="">
		<input type="text" name="filter_EQS_supplyempno" class="easyui-validatebox"
			   data-options="width:150,prompt: '代理人工號'"/>
			<input type="text" name="filter_LIKES_nodename" class="easyui-validatebox"
			   data-options="width:150,prompt: '節點名稱'"/>
			<input type="text" name="filter_LIKES_deptNoCollection" class="easyui-validatebox"
				   data-options="width:150,prompt: '組織'"/>
			<input id="proxType" name="filter_EQS_proxType" class="easyui-combobox"
				   panelHeight="auto" data-options="width:150,valueField: 'id',textField: 'text',prompt: '请选择或输入内容...',
					data: [
						{id: 'A', text: '全局代理'},
						{id: 'P', text: '特殊代理'}
					]"/>
			<input id="proxType" name="filter_EQS_workflowid" class="easyui-combobox" data-options="width:300,prompt: '请选择或输入内容...',
					valueField: 'value',
					textField: 'label',
					url: '${ctx}/tproxyuserinfo/getFormNameSelectOption',
					method: 'get'"/>
		<span class="toolbar-item dialog-tool-separator"></span>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
		   onclick="listSearchReset()">重置</a>
<%--		<a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"--%>
<%--		   onclick="exportExcel()">导出Excel</a>--%>
	</form>
	<shiro:hasPermission name="sys:proxuser:add">
		<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="add();" title="有效期內代理全部表單的審核">添加全局代理人</a>
		<span class="toolbar-item dialog-tool-separator"></span>
	</shiro:hasPermission>
	<shiro:hasPermission name="sys:proxuser:batchadd">
		<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-filter" plain="true" onclick="addSpec();" title="有效期內代理指定表單的審核">添加特殊代理人</a>
		<span class="toolbar-item dialog-tool-separator"></span>
		<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-save" plain="true" onclick="batchAdd();">特殊代理人批量維護</a>
		<a href="${ctx}/ossAdmin/download/ebfa2d7fcf55485e8ac4873e89c9e513" id="btnBatchImportTpl">模板下載.xls</a>
	</shiro:hasPermission>
	<shiro:hasPermission name="sys:proxuser:view">
		<span class="toolbar-item dialog-tool-separator"></span>
		<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-sum" plain="true" onclick="userProxList(1);" title="查看當前我代理了誰的任務,且任務有效">查看當前我的代理</a>
				<span class="toolbar-item dialog-tool-separator"></span>
				<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-large-chart" plain="true" onclick="userProxList(2);" title="查看歷史我代理了誰的任務,包括無效或者有效超期的">查看歷史我的代理</a>
	</shiro:hasPermission>
  </div>
<table id="dg"></table>
<div id="dlg"></div>
<div id="optionWin" class="easyui-window" title="批量導入" style="width:350px;height:300px;"
	 collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
	 data-options="iconCls:'PageAdd', footer:'#addFooter'">
	<form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
		<br/>
		<table width="100%">
			<tr align="center">
				<td style="width: 60%; white-space: nowrap;">
					<input id="batchFile" name="file" type="file" style="width: 300px"
						   accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>
					<input id="isExcel2003" name="isExcel2003" type="hidden" value="">

				</td>
			</tr>
			<tr align="center">
				<td style="width: 60%; white-space: nowrap;">
					<a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
				</td>
			</tr>
			<tr align="center">
				<td style="width: 60%; white-space: nowrap;">
					<span ID="labelListAddResult"></span><a href="${ctx}/tproxyuserinfo/downLoad/errorExcel" id="downloadError"
															plain="true">查看錯誤信息</a>
				</td>
			</tr>
		</table>
	</form>
</div>
<script src="${ctx}/static/js/common/tproxyuserinfo.js?random=<%= Math.random()%>"></script>
</body>
</html>
