<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>綠化零星工程申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src='${ctx}/static/js/generalAffairs/wfgreensporadicprocess.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfgreensporadicprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfgreensporadicprocessEntity.id }"/>
    <input id="serialno" name="wfgreensporadic.serialno" type="hidden" value="${wfgreensporadicprocessEntity.serialno }"/>
    <input id="makerno" name="wfgreensporadic.makerno" type="hidden" value="${wfgreensporadicprocessEntity.makerno }"/>
    <input id="makername" name="wfgreensporadic.makername" type="hidden" value="${wfgreensporadicprocessEntity.makername }"/>
    <input id="makerdeptno" name="wfgreensporadic.makerdeptno" type="hidden" value="${wfgreensporadicprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfgreensporadic.makerfactoryid" type="hidden"
           value="${wfgreensporadicprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">綠化零星工程申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfgreensporadicprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfgreensporadicprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfgreensporadicprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfgreensporadicprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfgreensporadicprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfgreensporadicprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfgreensporadicprocessEntity.makerno}/${wfgreensporadicprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="wfgreensporadic.applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfgreensporadicprocessEntity.applyno}" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="wfgreensporadic.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfgreensporadicprocessEntity.applyname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="wfgreensporadic.applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfgreensporadicprocessEntity.applydeptno }"/>
                            </td>
                            <td width="5%">廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="wfgreensporadic.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfgreensporadicprocessEntity.applyfactoryid }"
                                       data-options="width: 120,required:true"/>
                                <input class="easyui-validatebox"
                                       id="guishufactoryid" name="wfgreensporadic.guishufactoryid" style="width:50px" readonly type="hidden"
                                       value="${wfgreensporadicprocessEntity.guishufactoryid}" />
                            </td>
                        </tr>
                        <tr align="center">
                            <td>費用代碼</td>
                            <td class="td_style1">
                                <input id="applycostno" name="wfgreensporadic.applycostno" class="easyui-validatebox" data-options="width: 80"
                                       value="${wfgreensporadicprocessEntity.applycostno }"/>
                            </td>
                            <td>申請單位&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="wfgreensporadic.applydeptname" class="easyui-validatebox" data-options="width: 400,required:true"
                                       value="${wfgreensporadicprocessEntity.applydeptname }"/>
                            </td>
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applytel" name="wfgreensporadic.applytel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfgreensporadicprocessEntity.applytel }" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="applyemail" name="wfgreensporadic.applyemail" class="easyui-validatebox"
                                       value="${wfgreensporadicprocessEntity.applyemail }" style="width:300px;"
                                       data-options="required:true" onblur="valdEmail('')"/>
                            </td>
                            <td>綠化施工名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="buildname" name="wfgreensporadic.buildname" class="easyui-validatebox" data-options="width: 250,required:true"
                                       value="${wfgreensporadicprocessEntity.buildname }"/>
                            </td>
                            <td>綠化施工地點&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="buildplace" name="wfgreensporadic.buildplace" class="easyui-validatebox" data-options="width: 150,required:true"
                                       value="${wfgreensporadicprocessEntity.buildplace }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">工程內容</td>
                        </tr>
                        <tr align="center">
                            <td>需求原因&nbsp;<font color="red">*</font></td>
                            <td colspan="7" class="td_style1">
						    <textarea id="needreason" name="wfgreensporadic.needreason"
                                      class="easyui-validatebox"
                                      oninput="return LessThan(this);"
                                      onchange="return LessThan(this);"
                                      onpropertychange="return LessThan(this);"
                                      maxlength="300"
                                      style="width:99%;height:80px;" data-options="required:true,prompt:'請需求單位詳細說明'"
                                      rows="5" cols="6"
                                      data-options="required:true,validType:'length[0,300]'">${wfgreensporadicprocessEntity.needreason }</textarea><span id="txtNum"></span></td>
                        </tr>
                        <tr align="center">
                            <td>具體要求&nbsp;<font color="red">*</font></td>
                            <td colspan="7" class="td_style1">
						    <textarea id="specificrequire" name="wfgreensporadic.specificrequire"
                                      class="easyui-validatebox"
                                      oninput="return LessThan2(this);"
                                      onchange="return LessThan2(this);"
                                      onpropertychange="return LessThan2(this);"
                                      maxlength="300"
                                      style="width:99%;height:80px;" data-options="required:true,prompt:'請需求單位詳細說明'"
                                      rows="5" cols="6"
                                      data-options="required:true,validType:'length[0,300]'">${wfgreensporadicprocessEntity.specificrequire }</textarea><span id="txtNum2"></span></td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="7" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="wfgreensporadic.attachids" value="${wfgreensporadicprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="7" class="td_style1">
                                請下載《綠化零星工程報價單.pdf》查看單價<a href="${ctx}/wfgreensporadicprocess/downLoad/batchImportTpl2" id="btnBatchImportTpl2"> 綠化零星工程報價單.pdf</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_afforest_shenqing_v2','綠化零星工程申請單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#guishufactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="wfgreensporadic.kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wfgreensporadicprocessEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="wfgreensporadic.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfgreensporadicprocessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         <%--onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#guishufactoryid').val())"></div>--%>
                                                                         onclick="selectRole3('bchargeTable',$('#applydeptno').val(),'bchargeno','bchargename',$('#guishufactoryid').val(),'wfgreensporadic')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="wfgreensporadic.bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfgreensporadicprocessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="wfgreensporadic.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfgreensporadicprocessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#guishufactoryid').val(),'wfgreensporadic')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wfgreensporadic.zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfgreensporadicprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="wfgreensporadic.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfgreensporadicprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zwhdchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">總務核定呈簽</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2('115','zwhdchargeTable','zwhdchargeno','zwhdchargename',$('#applyfactoryid').combobox('getValue'),'wfgreensporadic')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zwhdchargeno" name="wfgreensporadic.zwhdchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zwhdchargeno']}"
                                                               readonly
                                                               value="${wfgreensporadicprocessEntity.zwhdchargeno }"/><c:if
                                                            test="${requiredMap['zwhdchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zwhdchargename" name="wfgreensporadic.zwhdchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zwhdchargeno']}"
                                                                value="${wfgreensporadicprocessEntity.zwhdchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zwzgshchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">總務主管審核</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2('116','zwzgshchargeTable','zwzgshchargeno','zwzgshchargename',$('#applyfactoryid').combobox('getValue'),'wfgreensporadic')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zwzgshchargeno" name="wfgreensporadic.zwzgshchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zwzgshchargeno']}"
                                                               readonly
                                                               value="${wfgreensporadicprocessEntity.zwzgshchargeno }"/><c:if
                                                            test="${requiredMap['zwzgshchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zwzgshchargename" name="wfgreensporadic.zwzgshchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zwzgshchargeno']}"
                                                                value="${wfgreensporadicprocessEntity.zwzgshchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zwzghzchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">總務主管核准</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2('117','zwzghzchargeTable','zwzghzchargeno','zwzghzchargename',$('#applyfactoryid').combobox('getValue'),'wfgreensporadic')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zwzghzchargeno" name="wfgreensporadic.zwzghzchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zwzghzchargeno']}"
                                                               readonly
                                                               value="${wfgreensporadicprocessEntity.zwzghzchargeno }"/><c:if
                                                            test="${requiredMap['zwzghzchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zwzghzchargename" name="wfgreensporadic.zwzghzchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zwzghzchargeno']}"
                                                                value="${wfgreensporadicprocessEntity.zwzghzchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="jgshchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">經管審核</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2('63','jgshchargeTable','jgshchargeno','jgshchargename',$('#applyfactoryid').combobox('getValue'),'wfgreensporadic')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jgshchargeno" name="wfgreensporadic.jgshchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['jgshchargeno']}"
                                                               readonly
                                                               value="${wfgreensporadicprocessEntity.jgshchargeno }"/><c:if
                                                            test="${requiredMap['jgshchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="jgshchargename" name="wfgreensporadic.jgshchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jgshchargeno']}"
                                                                value="${wfgreensporadicprocessEntity.jgshchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zgshchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">主管審核</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(274,'zgshchargeTable','zgshchargeno','zgshchargename',$('#applyfactoryid').combobox('getValue'),'wfgreensporadic')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zgshchargeno" name="wfgreensporadic.zgshchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zgshchargeno']}"
                                                               readonly
                                                               value="${wfgreensporadicprocessEntity.zgshchargeno }"/><c:if
                                                            test="${requiredMap['zgshchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zgshchargename" name="wfgreensporadic.zgshchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zgshchargeno']}"
                                                                value="${wfgreensporadicprocessEntity.zgshchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">總處主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'zcchargeno','zcchargename',$('#guishufactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="wfgreensporadic.zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wfgreensporadicprocessEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="wfgreensporadic.zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfgreensporadicprocessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zwyschargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">總務驗收</td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zwyschargeno" name="wfgreensporadic.zwyschargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zwyschargeno']}"
                                                               readonly
                                                               value="${wfgreensporadicprocessEntity.zwyschargeno }"/><c:if
                                                            test="${requiredMap['zwyschargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zwyschargename" name="wfgreensporadic.zwyschargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zwyschargeno']}"
                                                                value="${wfgreensporadicprocessEntity.zwyschargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">


                                            <table width="18%" style="float: left;margin-left: 5px;" id="tdryschargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">表單申請人驗收</td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="tdryschargeno" name="wfgreensporadic.tdryschargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['tdryschargeno']}"
                                                               readonly
                                                               value="${wfgreensporadicprocessEntity.tdryschargeno }"/><c:if
                                                            test="${requiredMap['tdryschargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="tdryschargename" name="wfgreensporadic.tdryschargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['tdryschargeno']}"
                                                                value="${wfgreensporadicprocessEntity.tdryschargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="jgyschargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">經管驗收</td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jgyschargeno" name="wfgreensporadic.jgyschargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['jgyschargeno']}"
                                                               readonly
                                                               value="${wfgreensporadicprocessEntity.jgyschargeno }"/><c:if
                                                            test="${requiredMap['jgyschargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="jgyschargename" name="wfgreensporadic.jgyschargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jgyschargeno']}"
                                                                value="${wfgreensporadicprocessEntity.jgyschargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zwysshchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">總務驗收審核</td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zwysshchargeno" name="wfgreensporadic.zwysshchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zwysshchargeno']}"
                                                               readonly
                                                               value="${wfgreensporadicprocessEntity.zwysshchargeno }"/><c:if
                                                            test="${requiredMap['zwysshchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zwysshchargename" name="wfgreensporadic.zwysshchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zwysshchargeno']}"
                                                                value="${wfgreensporadicprocessEntity.zwysshchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zwyshzchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">總務驗收核准</td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zwyshzchargeno" name="wfgreensporadic.zwyshzchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zwyshzchargeno']}"
                                                               readonly
                                                               value="${wfgreensporadicprocessEntity.zwyshzchargeno }"/><c:if
                                                            test="${requiredMap['zwyshzchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zwyshzchargename" name="wfgreensporadic.zwyshzchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zwyshzchargeno']}"
                                                                value="${wfgreensporadicprocessEntity.zwyshzchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="onlyKchargeSignle" value="1" />
    <div id="win"></div>
</form>
</body>
</html>