<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>綠化零星工程申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src='${ctx}/static/js/generalAffairs/wfgreensporadicprocess.js?random=<%= Math.random()%>'></script>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
        .td_style3{
            border: none 0px;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfgreensporadicprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfgreensporadicprocessEntity.id }"/>
    <input id="serialno" name="wfgreensporadic.serialno" type="hidden" value="${wfgreensporadicprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">綠化零星工程申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfgreensporadicprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfgreensporadicprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfgreensporadicprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfgreensporadicprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfgreensporadicprocessEntity.makerno}/${wfgreensporadicprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">申請人工號</td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="wfgreensporadic.applyno" class="easyui-validatebox inputCss"
                                       data-options="width: 80" value="${wfgreensporadicprocessEntity.applyno}" readonly/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="wfgreensporadic.applyname" class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfgreensporadicprocessEntity.applyname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="wfgreensporadic.applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfgreensporadicprocessEntity.applydeptno }"/>
                            </td>
                            <td width="5%">廠區</td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="wfgreensporadic.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfgreensporadicprocessEntity.applyfactoryid }" disabled
                                       data-options="width: 120"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>費用代碼</td>
                            <td class="td_style1">
                                <input id="applycostno" name="wfgreensporadic.applycostno" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfgreensporadicprocessEntity.applycostno }" readonly/>
                            </td>
                            <td>申請單位</td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="wfgreensporadic.applydeptname" class="easyui-validatebox inputCss" data-options="width: 400"
                                       value="${wfgreensporadicprocessEntity.applydeptname }" readonly/>
                            </td>
                            <td>聯繫方式</td>
                            <td class="td_style1">
                                <input id="applytel" name="wfgreensporadic.applytel" class="easyui-validatebox inputCss"
                                       style="width:90px;" value="${wfgreensporadicprocessEntity.applytel }" readonly/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫郵箱</td>
                            <td colspan="2" class="td_style1">
                                <input id="applyemail" name="wfgreensporadic.applyemail" class="easyui-validatebox inputCss"
                                       value="${wfgreensporadicprocessEntity.applyemail }" style="width:250px;" readonly/>
                            </td>
                            <td>綠化施工名稱</td>
                            <td colspan="2" class="td_style1">
                                <input id="buildname" name="wfgreensporadic.buildname" class="easyui-validatebox inputCss" data-options="width: 250"
                                       value="${wfgreensporadicprocessEntity.buildname }" readonly/>
                            </td>
                            <td>綠化施工地點</td>
                            <td class="td_style1">
                                <input id="buildplace" name="wfgreensporadic.buildplace" class="easyui-validatebox inputCss" data-options="width: 150"
                                       value="${wfgreensporadicprocessEntity.buildplace }" readonly/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">工程內容</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">需求原因</td>
                            <td width="90% "colspan="7" class="td_style1">
						        <textarea id="needreason" name="wfgreensporadic.needreason" readonly
                                      class="easyui-validatebox" style="width:99%;height:80px;" rows="5" cols="6">${wfgreensporadicprocessEntity.needreason }</textarea></td>
                        </tr>
                        <tr align="center">
                            <td width="10%">具體要求</td>
                            <td width="90%" colspan="7" class="td_style1">
						    <textarea id="" name="wfgreensporadic.specificrequire" readonly
                                      class="easyui-validatebox" style="width:99%;height:80px;" rows="5" cols="6">${wfgreensporadicprocessEntity.specificrequire }</textarea></td>
                        </tr>
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" colspan="7" class="td_style1">
                                <input type="hidden" id="attachids" name="wfgreensporadic.attachids" value="${wfgreensporadicprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <c:choose>
                            <c:when test="${not empty nodeName&&'總務核定呈簽' eq nodeName}">
                                <tr>
                                    <td colspan="8" class="td_style1">綠化零星工程預算核定</td>
                                </tr>
                                <tr align="center">
                                    <td colspan="8" width="100%">
                                        <div style="overflow-x: auto;width: 100%;">
                                            <input id="greensporadicItemTableIndex" type="hidden"
                                                   value="<c:if test="${greensporadicItem!=null && greensporadicItem.size()>0}">${greensporadicItem.size() + 1}</c:if>
                                                <c:if test="${greensporadicItem.size()==0}">2</c:if>">
                                            </input>
                                            <table id="greensporadicItemTable" width="100%">
                                                <tr align="center">
                                                    <td>序號&nbsp;<font color="red">*</font></td>
                                                    <td>名稱&nbsp;<font color="red">*</font></td>
                                                    <td>單價（未稅）<font color="red">*</font></td>
                                                    <td>工程量&nbsp;<font color="red">*</font></td>
                                                    <td>總價（未稅）<font color="red">*</font></td>
                                                    <td>備註</td>
                                                    <td>操作</td>
                                                </tr>
                                                <tbody id="info_Body">
                                                <c:if test="${greensporadicItem!=null&&greensporadicItem.size()>0}">
                                                    <c:forEach items="${greensporadicItem}" var="greensporadicItem" varStatus="status">
                                                        <tr align="center" id="grspItem${status.index+1}">
                                                            <td width="5%">${status.index+1}</td>
                                                            <td width="15%"><input id="grsp_budgetname${status.index+1}" name="wfgreensporadicitems[${status.index}].budgetname"
                                                                                   class="easyui-validatebox"  style="width:150px;" value="${greensporadicItem.budgetname}"/><font color="red">*</font></td>
                                                            <td width="15%"><input id="grsp_unitprice${status.index+1}" name="wfgreensporadicitems[${status.index}].unitprice"
                                                                                   class="easyui-validatebox"  style="width:100px;" onchange="countSellMoney(this)" value="${greensporadicItem.budgetname}"/><font color="red">*</font></td>
                                                            <td width="15%">
                                                                <input id="grsp_quantity${status.index+1}" name="wfgreensporadicitems[${status.index}].quantity"  style="width:100px;" onblur="valdIsNumber(this)"
                                                                       class="easyui-validatebox" onchange="countSellMoney(this)" value="${greensporadicItem.quantity}"/><font color="red">*</font>
                                                            </td>
                                                            <td width="15%"><input id="grsp_totalprice${status.index+1}" name="wfgreensporadicitems[${status.index}].totalprice"
                                                                                   class="easyui-validatebox"  style="width:100px;" readonly value="${greensporadicItem.totalprice}"/><font color="red">*</font></td>
                                                            <td width="30%"><input id="grsp_remarks${status.index+1}" name="wfgreensporadicitems[${status.index}].remarks"
                                                                                   class="easyui-validatebox"  style="width:250px;" value="${greensporadicItem.remarks}"/></td>
                                                            <td width="5%"><input type="image" src="${ctx}/static/images/deleteRow.png" onclick="grspdeltr(this,${status.index+1});return false;"/></td>
                                                        </tr>
                                                    </c:forEach>
                                                </c:if>
                                                <c:if test="${greensporadicItem.size()==0}">
                                                    <tr align="center" id="grspItem1">
                                                        <td width="5%">1</td>
                                                        <td width="15%"><input id="grsp_budgetname1" name="wfgreensporadicitems[0].budgetname"
                                                                               class="easyui-validatebox"  style="width:150px;" value=""/><font color="red">*</font></td>
                                                        <td width="15%"><input id="grsp_unitprice1" name="wfgreensporadicitems[0].unitprice"
                                                                               class="easyui-validatebox"  style="width:100px;" value="" onchange="countSellMoney(this)" /><font color="red">*</font></td>
                                                        <td width="15%">
                                                            <input id="grsp_quantity1" name="wfgreensporadicitems[0].quantity"  style="width:100px;" onchange="countSellMoney(this)"
                                                                   class="easyui-validatebox" value=""/><font color="red">*</font>
                                                        </td>
                                                        <td width="15%"><input id="grsp_totalprice1" name="wfgreensporadicitems[0].totalprice"
                                                                               class="easyui-validatebox"  style="width:100px;" value="" readonly/><font color="red">*</font></td>
                                                        <td width="30%"><input id="grsp_remarks1" name="wfgreensporadicitems[0].remarks"
                                                                               class="easyui-validatebox"  style="width:250px;" value=""/></td>
                                                        <td width="5%"><input type="image" src="${ctx}/static/images/deleteRow.png" onclick="grspdeltr(this,1);return false;"/></td>
                                                    </tr>
                                                </c:if>
                                                </tbody>
                                                <tr class="nottr">
                                                    <td colspan="2" style="text-align: right;">總計:</td>
                                                    <td colspan="5">
                                                        &nbsp;&nbsp;&nbsp;&nbsp;<input id="budgetcost" name="wfgreensporadic.budgetcost"
                                                               class="easyui-validatebox" data-options="width:150" readonly value="${wfgreensporadicprocessEntity.budgetcost}"/>（RMB）
                                                    </td>
                                                </tr>
                                                <tr align="left" class="nottr">
                                                    <td colspan="7" width="100%" style="text-align:left;padding-left:10px;">
                                                        <input type="button" id="grspItemAdd" style="width:100px;float:left;" value="添加一筆"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <c:if test="${greensporadicItem!=null&&greensporadicItem.size()>0 &&(nodeOrder ge 4)}">
                                    <tr>
                                        <td colspan="8" class="td_style1">綠化零星工程預算核定</td>
                                    </tr>
                                    <tr align="center">
                                        <td colspan="8" width="100%">
                                            <div style="overflow-x: auto;width: 100%;">
                                                <table width="100%">
                                                    <tr align="center">
                                                        <td>序號</td>
                                                        <td>名稱</td>
                                                        <td>單價（未稅）</td>
                                                        <td>工程量</td>
                                                        <td>總價（未稅）</td>
                                                        <td>備註</td>
                                                    </tr>
                                                    <c:forEach items="${greensporadicItem}" var="greensporadicItem" varStatus="status">
                                                        <tr align="center" id="grspItem${status.index+1}">
                                                            <td width="5%">${status.index+1}</td>
                                                            <td width="15%">${greensporadicItem.budgetname}</td>
                                                            <td width="15%">${greensporadicItem.unitprice}</td>
                                                            <td width="15%">${greensporadicItem.quantity}</td>
                                                            <td width="15%">${greensporadicItem.totalprice}</td>
                                                            <td width="30%">${greensporadicItem.remarks}</td>
                                                        </tr>
                                                    </c:forEach>
                                                    <tr class="nottr">
                                                        <td colspan="2" style="text-align: right;">總計:</td>
                                                        <td colspan="5">${wfgreensporadicprocessEntity.budgetcost}（RMB）</td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </td>
                                    </tr>
                                </c:if>
                            </c:otherwise>
                        </c:choose>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="7" class="td_style1">
                                請下載《綠化零星工程報價單.pdf》查看單價<a href="${ctx}/wfgreensporadicprocess/downLoad/batchImportTpl2" id="btnBatchImportTpl2"> 綠化零星工程報價單.pdf</a>
                            </td>
                        </tr>
                        <c:choose>
                            <c:when test="${not empty nodeName&&'總務驗收' eq nodeName}">
                                <tr>
                                    <td colspan="8" class="td_style1">2019綠化零星工程驗收表</td>
                                </tr>
                                <tr align="center">
                                    <td width="10%">工程名稱<font color="red">*</font></td>
                                    <td width="40%" colspan="3" class="td_style1">
                                        <input id="projectname" name="wfgreensporadic.projectname" class="easyui-validatebox" data-options="width: 250,required:true"
                                               value="${wfgreensporadicprocessEntity.projectname }"/>
                                    </td>
                                    <td width="10%">承辦廠商<font color="red">*</font></td>
                                    <td width="40%" colspan="3" class="td_style1">
                                        <input id="contractor" name="wfgreensporadic.contractor" class="easyui-validatebox" data-options="width: 250,required:true"
                                               value="${wfgreensporadicprocessEntity.contractor }"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td width="10%">工程業主<font color="red">*</font></td>
                                    <td width="40%" colspan="3" class="td_style1">
                                        <input id="projectowner" name="wfgreensporadic.projectowner" class="easyui-validatebox" data-options="width: 250,required:true"
                                               value="${wfgreensporadicprocessEntity.projectowner }"/>
                                    </td>
                                    <td width="10%">驗收日期<font color="red">*</font></td>
                                    <td width="40%" colspan="3" class="td_style1">
                                        <input id="acceptdate" name="wfgreensporadic.acceptdate" class="Wdate"
                                               data-options="width:100,required:true"
                                               value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfgreensporadicprocessEntity.acceptdate}"/>"
                                               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="8" class="td_style1">驗收內容</td>
                                </tr>
                                <tr align="center">
                                    <td width="10%">工程內容&nbsp;<font color="red">*</font></td>
                                    <td width="90%" colspan="7" class="td_style1">
						                <textarea id="projectcontent" name="wfgreensporadic.projectcontent"
                                                  class="easyui-validatebox"
                                                  oninput="return LessThan(this);"
                                                  onchange="return LessThan(this);"
                                                  onpropertychange="return LessThan(this);"
                                                  maxlength="300"
                                                  style="width:99%;height:80px;" data-options="required:true"
                                                  rows="5" cols="6"
                                                  data-options="required:true,validType:'length[0,300]'">${wfgreensporadicprocessEntity.projectcontent }</textarea><span id="txtNum"></span></td>
                                </tr>
                                <tr align="center">
                                    <td width="10%">驗收結果&nbsp;<font color="red">*</font></td>
                                    <td width="90%" colspan="7" class="td_style1">
                                        <div class="acceptresultsDiv"></div>
                                        <input id="acceptresults" name="wfgreensporadic.acceptresults"
                                               type="hidden" class="easyui-validatebox" data-options="width: 150"
                                               value="${wfgreensporadicprocessEntity.acceptresults }"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td width="10%">驗收結果說明&nbsp;<font color="red">*</font></td>
                                    <td width="90%" colspan="7" class="td_style1">
						                <textarea id="acceptresultsexplain" name="wfgreensporadic.acceptresultsexplain"
                                                  class="easyui-validatebox"
                                                  oninput="return LessThan(this);"
                                                  onchange="return LessThan(this);"
                                                  onpropertychange="return LessThan(this);"
                                                  maxlength="300"
                                                  style="width:99%;height:80px;" data-options="required:true"
                                                  rows="5" cols="6"
                                                  data-options="required:true,validType:'length[0,300]'">${wfgreensporadicprocessEntity.acceptresultsexplain }</textarea><span id="txtNum2"></span></td>
                                </tr>
                                <tr align="center">
                                    <td width="10%">總計工程費用&nbsp;<font color="red">*</font></td>
                                    <td width="90%" colspan="7" class="td_style1">
                                        <input id="actualcost" name="wfgreensporadic.actualcost" class="easyui-validatebox"
                                               data-options="width: 250,required:true" onblur="f_isNum(this)"
                                               value="${wfgreensporadicprocessEntity.actualcost}"/>RMB
                                    </td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <c:if test="${wfgreensporadicprocessEntity.projectname!=null &&(nodeOrder ge 11)}">
                                    <tr>
                                        <td colspan="8" class="td_style1">2019綠化零星工程驗收表</td>
                                    </tr>
                                    <tr align="center">
                                        <td width="10%">工程名稱</td>
                                        <td width="40%" colspan="3" class="td_style1">
                                            <input name="wfgreensporadic.projectname" class="easyui-validatebox inputCss" data-options="width: 250"
                                                   value="${wfgreensporadicprocessEntity.projectname }" readonly/>
                                        </td>
                                        <td width="10%">承辦廠商</td>
                                        <td width="40%" colspan="3" class="td_style1">
                                            <input name="wfgreensporadic.contractor" class="easyui-validatebox inputCss" data-options="width: 250"
                                                   value="${wfgreensporadicprocessEntity.contractor }" readonly/>
                                        </td>
                                    </tr>
                                    <tr align="center">
                                        <td width="10%">工程業主</td>
                                        <td width="40%" colspan="3" class="td_style1">
                                            <input name="wfgreensporadic.projectowner" class="easyui-validatebox inputCss" data-options="width: 250"
                                                   value="${wfgreensporadicprocessEntity.projectowner }" readonly/>
                                        </td>
                                        <td width="10%">驗收日期</td>
                                        <td width="40%" colspan="3" class="td_style1">
                                            <input name="wfgreensporadic.acceptdate" class="Wdate"
                                                   data-options="width:100,required:true"
                                                   value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfgreensporadicprocessEntity.acceptdate}"/>"
                                                   onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"  disabled />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="8" class="td_style1">驗收內容</td>
                                    </tr>
                                    <tr align="center">
                                        <td width="10%">工程內容</td>
                                        <td width="90%" colspan="7" class="td_style1">
						                <textarea name="wfgreensporadic.projectcontent" class="easyui-validatebox" readonly
                                                  style="width:99%;height:80px;" rows="5" cols="6">${wfgreensporadicprocessEntity.projectcontent }</textarea></td>
                                    </tr>
                                    <tr align="center">
                                        <td width="10%">驗收結果</td>
                                        <td width="90%" colspan="7" class="td_style1">
                                            <div class="acceptresultsDiv"></div>
                                            <input id="acceptresultsAudit" name="wfgreensporadic.acceptresults"
                                                   type="hidden" class="easyui-validatebox" data-options="width: 150"
                                                   value="${wfgreensporadicprocessEntity.acceptresults }"/>
                                        </td>
                                    </tr>
                                    <tr align="center">
                                        <td width="10%">驗收結果說明</td>
                                        <td width="90%" colspan="7" class="td_style1">
						                <textarea name="wfgreensporadic.acceptresultsexplain" class="easyui-validatebox" readonly
                                                  style="width:99%;height:80px;" rows="5" cols="6">${wfgreensporadicprocessEntity.acceptresultsexplain }</textarea></td>
                                    </tr>
                                    <tr align="center">
                                        <td width="10%">總計工程費用</td>
                                        <td width="90%" colspan="7" class="td_style1">
                                            <input name="wfgreensporadic.actualcost" class="easyui-validatebox" readonly
                                                   data-options="width: 250" value="${wfgreensporadicprocessEntity.actualcost}"/>RMB</td>
                                    </tr>
                                </c:if>
                            </c:otherwise>
                        </c:choose>
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%" colspan="7" class="td_style1">
						        <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                          style="width:1000px;height:60px;"
                                          rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="8" style="border:none;text-align:center;margin-top:10px">
                                <c:choose>
                                    <c:when test="${not empty nodeName&&'總務核定呈簽' eq nodeName}">
                                        <tr align="center">
                                            <td colspan="8" style="border:none;text-align:center;margin-top:10px">
                                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"  perCall="zwhdupdate"
                                                            serialNo="${wfgreensporadicprocessEntity.serialno}"></fox:action>
                                            </td>
                                        </tr>
                                    </c:when>
                                    <c:when test="${not empty nodeName&&'總務驗收' eq nodeName}">
                                        <tr align="center">
                                            <td colspan="8" style="border:none;text-align:center;margin-top:10px">
                                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"  perCall="zwysupdate"
                                                            serialNo="${wfgreensporadicprocessEntity.serialno}"></fox:action>
                                            </td>
                                        </tr>
                                    </c:when>
                                    <c:otherwise>
                                        <tr align="center">
                                            <td colspan="8" style="border:none;text-align:center;margin-top:10px">
                                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" serialNo="${wfgreensporadicprocessEntity.serialno}"></fox:action>
                                            </td>
                                        </tr>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="8">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)" onclick="showWfImag('${processId}','綠化零星工程申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="8" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="8" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfgreensporadicprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
<div id="dlg"></div>
<input type="hidden" id="nodeName"  value="${nodeName}"/>
<input type="hidden" id="disOrEnabled"  value="disabled"/>
</body>
</html>