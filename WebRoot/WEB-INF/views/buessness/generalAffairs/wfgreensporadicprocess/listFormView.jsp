<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>綠化零星工程申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src='${ctx}/static/js/generalAffairs/wfgreensporadicprocess.js?random=<%= Math.random()%>'></script>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfgreensporadicprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfgreensporadicprocessEntity.id }"/>
    <input id="serialno" name="wfgreensporadic.serialno" type="hidden" value="${wfgreensporadicprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">綠化零星工程申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfgreensporadicprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfgreensporadicprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfgreensporadicprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfgreensporadicprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfgreensporadicprocessEntity.makerno}/${wfgreensporadicprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">申請人工號</td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="wfgreensporadic.applyno" class="easyui-validatebox inputCss"
                                       data-options="width: 80" value="${wfgreensporadicprocessEntity.applyno}" readonly/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="wfgreensporadic.applyname" class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfgreensporadicprocessEntity.applyname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="wfgreensporadic.applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfgreensporadicprocessEntity.applydeptno }"/>
                            </td>
                            <td width="5%">廠區</td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="wfgreensporadic.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfgreensporadicprocessEntity.applyfactoryid }" disabled
                                       data-options="width: 120"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>費用代碼</td>
                            <td class="td_style1">
                                <input id="applycostno" name="wfgreensporadic.applycostno" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfgreensporadicprocessEntity.applycostno }" readonly/>
                            </td>
                            <td>申請單位</td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="wfgreensporadic.applydeptname" class="easyui-validatebox inputCss" data-options="width: 400"
                                       value="${wfgreensporadicprocessEntity.applydeptname }" readonly/>
                            </td>
                            <td>聯繫方式</td>
                            <td class="td_style1">
                                <input id="applytel" name="wfgreensporadic.applytel" class="easyui-validatebox inputCss"
                                       style="width:90px;" value="${wfgreensporadicprocessEntity.applytel }" readonly/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫郵箱</td>
                            <td colspan="2" class="td_style1">
                                <input id="applyemail" name="wfgreensporadic.applyemail" class="easyui-validatebox inputCss"
                                       value="${wfgreensporadicprocessEntity.applyemail }" style="width:300px;" readonly/>
                            </td>
                            <td>綠化施工名稱</td>
                            <td colspan="2" class="td_style1">
                                <input id="buildname" name="wfgreensporadic.buildname" class="easyui-validatebox inputCss" data-options="width: 250"
                                       value="${wfgreensporadicprocessEntity.buildname }" readonly/>
                            </td>
                            <td>綠化施工地點</td>
                            <td class="td_style1">
                                <input id="buildplace" name="wfgreensporadic.buildplace" class="easyui-validatebox inputCss" data-options="width: 150"
                                       value="${wfgreensporadicprocessEntity.buildplace }" readonly/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">工程內容</td>
                        </tr>
                        <tr align="center">
                            <td>需求原因</td>
                            <td colspan="7" class="td_style1">
						        <textarea id="needreason" name="wfgreensporadic.needreason" readonly
                                          class="easyui-validatebox" style="width:99%;height:80px;" rows="5" cols="6">${wfgreensporadicprocessEntity.needreason }</textarea></td>
                        </tr>
                        <tr align="center">
                            <td>具體要求</td>
                            <td colspan="7" class="td_style1">
						    <textarea id="specificrequire" name="wfgreensporadic.specificrequire" readonly
                                      class="easyui-validatebox" style="width:99%;height:80px;" rows="5" cols="6">${wfgreensporadicprocessEntity.specificrequire }</textarea></td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td width="90%" colspan="7" class="td_style1">
                                <input type="hidden" id="attachids" name="wfgreensporadic.attachids" value="${wfgreensporadicprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <c:if test="${greensporadicItem!=null&&greensporadicItem.size()>0 }">
                            <tr>
                                <td colspan="8" class="td_style1">綠化零星工程預算核定</td>
                            </tr>
                            <tr align="center">
                                <td colspan="8" width="100%">
                                    <div style="overflow-x: auto;width: 100%;">
                                        <table width="100%">
                                            <tr align="center">
                                                <td>序號</td>
                                                <td>名稱</td>
                                                <td>單價（未稅）</td>
                                                <td>工程量</td>
                                                <td>總價（未稅）</td>
                                                <td>備註</td>
                                            </tr>
                                            <c:forEach items="${greensporadicItem}" var="greensporadicItem" varStatus="status">
                                                <tr align="center" id="grspItem${status.index+1}">
                                                    <td width="5%">${status.index+1}</td>
                                                    <td width="10%">${greensporadicItem.budgetname}</td>
                                                    <td width="10%">${greensporadicItem.unitprice}</td>
                                                    <td width="15%">${greensporadicItem.quantity}</td>
                                                    <td width="15%">${greensporadicItem.totalprice}</td>
                                                    <td width="30%">${greensporadicItem.remarks}</td>
                                                </tr>
                                            </c:forEach>
                                            <tr class="nottr">
                                                <td colspan="2" style="text-align: right;">總計:&nbsp;&nbsp;</td>
                                                <td colspan="5">${wfgreensporadicprocessEntity.budgetcost}（RMB）</td>
                                            </tr>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                        </c:if>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="7" class="td_style1">
                                請下載《綠化零星工程報價單.pdf》查看單價<a href="${ctx}/wfgreensporadicprocess/downLoad/batchImportTpl2" id="btnBatchImportTpl2"> 綠化零星工程報價單.pdf</a>
                            </td>
                        </tr>
                        <c:if test="${wfgreensporadicprocessEntity.projectname!=null}">
                            <tr>
                                <td colspan="8" class="td_style1">2019綠化零星工程驗收表</td>
                            </tr>
                            <tr align="center">
                                <td width="10%">工程名稱</td>
                                <td width="90%" colspan="3" class="td_style1">
                                    <input name="wfgreensporadic.projectname" class="easyui-validatebox inputCss" data-options="width: 250"
                                           value="${wfgreensporadicprocessEntity.projectname }"/>
                                </td>
                                <td width="10%">承辦廠商</td>
                                <td width="90%"colspan="3" class="td_style1">
                                    <input name="wfgreensporadic.contractor" class="easyui-validatebox inputCss" data-options="width: 250"
                                           value="${wfgreensporadicprocessEntity.contractor }"/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td width="10%">工程業主</td>
                                <td width="90%" colspan="3" class="td_style1">
                                    <input name="wfgreensporadic.projectowner" class="easyui-validatebox inputCss" data-options="width: 250"
                                           value="${wfgreensporadicprocessEntity.projectowner }"/>
                                </td>
                                <td width="10%">驗收日期</td>
                                <td width="90%" colspan="3" class="td_style1">
                                    <input name="wfgreensporadic.acceptdate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                           data-options="width: 100"
                                           value="<fmt:formatDate value="${wfgreensporadicprocessEntity.acceptdate}"/>" disabled/>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="8" class="td_style1">驗收內容</td>
                            </tr>
                            <tr align="center">
                                <td width="10%">工程內容</td>
                                <td width="90%" colspan="7" class="td_style1">
                                <textarea name="wfgreensporadic.projectcontent" class="easyui-validatebox"
                                          style="width:99%;height:80px;" rows="5" cols="6">${wfgreensporadicprocessEntity.projectcontent }</textarea></td>
                            </tr>
                            <tr align="center">
                                <td width="10%">驗收結果</td>
                                <td width="90%" colspan="7" class="td_style1">
                                    <div class="acceptresultsDiv"></div>
                                    <input id="acceptresultsAudit" name="wfgreensporadic.acceptresults"
                                           type="hidden" class="easyui-validatebox" data-options="width: 150"
                                           value="${wfgreensporadicprocessEntity.acceptresults }"/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td width="10%">驗收結果說明</td>
                                <td width="90%" colspan="7" class="td_style1">
                                <textarea name="wfgreensporadic.acceptresultsexplain" class="easyui-validatebox"
                                          style="width:99%;height:80px;" rows="5" cols="6">${wfgreensporadicprocessEntity.acceptresultsexplain }</textarea></td>
                            </tr>
                            <tr align="center">
                                <td width="10%">總計工程費用</td>
                                <td width="90%" colspan="7" class="td_style1">
                                    <input name="wfgreensporadic.actualcost" class="easyui-validatebox"
                                           data-options="width: 250" value="${wfgreensporadicprocessEntity.actualcost}"/></td>
                            </tr>
                        </c:if>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="8">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','綠化零星工程申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="8" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="8" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfgreensporadicprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="8" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                   style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>

                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
<div id="dlg"></div>
</body>
</html>