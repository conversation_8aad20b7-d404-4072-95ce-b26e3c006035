<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>公共場地借用申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
            type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/arearentprocess/${action}" method="post">
    <!--
          applyarea 區域
applybuilding 樓棟
applyfloor 樓層
describtion 使用說明
kchargeno 課級主管
kchargename 課級主管
bchargeno 部級主管
bchargename 部級主管
cchargeno 廠級主管
cchargename 廠級主管
zchargeno 製造處級主管
zchargename 製造處級主管
ylno1 5S管理單位主管
ylname1 5S管理單位主管
ylno2 工業安全主管
ylname2 工業安全主管
ylno3 總務確認主管
ylname3 總務確認主管
ylno4 總務主管
ylname4 總務主管
layperson 法人
applybegin 開始使用時間
applyend 結束使用時間
location 物品存放地點
workflowid 流程ID
usefactoryid 使用廠區
serialno 任務編碼
processid 工單實例ID
makerno 填單人工號
makername 填單人名稱
makerip 填單人IP
createtime 填單時間
complettime 簽核完成時間
workstatus 表單狀態
makerfactoryid 填單人廠區Id
attachids 附件Id
applyno 申請人工號
applyname 申請人姓名
applydeptno 申請人單位代碼
applydeptname 申請 人單位名稱
applyleveltype 資位
applymanager 管理職
applyfactoryid 申請人廠區id
applyemail 申請人郵箱
applyphone 申請人電話
createBy 創建人
createDate 創建時間
updateBy 更新者
updateDate 更新時間
delFlag 刪除標識
makerdeptno 填單人所在部門
id 主鍵
      -->
    <input id="ids" name="ids" type="hidden" value="${arearentProcessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${arearentProcessEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${arearentProcessEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${arearentProcessEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${arearentProcessEntity.makerdeptno }"/>
    <div class="commonW">
        <div class="headTitle">公共場地借用申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${arearentProcessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${arearentProcessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${arearentProcessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${arearentProcessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty arearentProcessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty arearentProcessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${arearentProcessEntity.makerno}/${arearentProcessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="8" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 120,required:true"
                                       value="${arearentProcessEntity.applyno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="4%">申請人姓名</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:120" readonly value="${arearentProcessEntity.applyname }"/>
                            </td>
                            <td width="4%">資位</td>
                            <td width="6%" class="td_style1">
                                <input id="applyleveltype" name="applyleveltype"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:120" readonly
                                       value="${arearentProcessEntity.applyleveltype }"/>
                            </td>
                            <td width="4%">管理職</td>
                            <td width="6%" class="td_style1">
                                <input id="applymanager" name="applymanager"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:120" readonly
                                       value="${arearentProcessEntity.applymanager }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:120" readonly value="${arearentProcessEntity.applydeptno }"/>
                            </td>
                            <td>單位名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="applydeptname"
                                       data-options="required:true"
                                       class="easyui-validatebox" style="width:90%;"
                                       value="${arearentProcessEntity.applydeptname }"/>
                            </td>
                            <td width="4%">分機/短號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyphone" name="applyphone"
                                       class="easyui-validatebox"
                                       data-options="width:120,required:true"
                                       value="${arearentProcessEntity.applyphone }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="applyemail"
                                       data-options="required:true"
                                       class="easyui-validatebox" style="width:90%;"
                                       value="${arearentProcessEntity.applyemail }"/>
                            </td>
                            <td width="4%">使用廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="usefactoryid" name="usefactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${arearentProcessEntity.usefactoryid }"
                                       data-options="width: 120,required:true"/>
                            </td>
                            <td>法人&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="layperson" name="layperson"
                                       class="easyui-combobox" data-options="required:true"
                                       panelHeight="auto" editable="false"
                                       value="${arearentProcessEntity.layperson }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>借用說明&nbsp;<font color="red">*</font></td>
                            <td colspan="7" class="td_style1">
						<textarea id="describtion" name="describtion"
                                  class="easyui-validatebox"
                                  style="width:99%;height:80px;" data-options="required:true"
                                  rows="5" cols="6"
                                  value="${arearentProcessEntity.describtion }">${arearentProcessEntity.describtion }</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>借用時間段&nbsp;<font color="red">*</font></td>
                            <td colspan="7" align="center">
                                <input id="applybegin" name="applybegin" class="Wdate"
                                                                data-options="width:300,required:true" style="width:180px"
                                                                value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${arearentProcessEntity.applybegin}"/>"
                                                                onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" placeholder="请选择开始时间"/>
                                ~<input id="applyend" name="applyend" class="Wdate"
                                        data-options="width:300,required:true" style="width:180px"
                                        value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${arearentProcessEntity.applyend}"/>"
                                        onclick="WdatePicker({minDate:'#F{$dp.$D(\'applybegin\')}',dateFmt:'yyyy-MM-dd'})" placeholder="请选择結束时间"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>
                                物品存放地點&nbsp;<font color="red">*</font>
                            </td>
                            <td colspan="7" align="center">
                                <input id="applyarea" name="applyarea"
                                       class="easyui-validatebox" data-options="width:50,required:true"
                                       value="${arearentProcessEntity.applyarea }"/>區
                                <input id="applybuilding" name="applybuilding"
                                       class="easyui-validatebox" data-options="width:50,required:true"
                                       value="${arearentProcessEntity.applybuilding }"/>棟
                                <input id="applyfloor" name="applyfloor"
                                       class="easyui-validatebox" data-options="width:50,required:true"
                                       value="${arearentProcessEntity.applyfloor }"/>層
                                <input id="location" name="location"
                                       class="easyui-validatebox" data-options="width:200,required:true"
                                       value="${arearentProcessEntity.location }"/>位置
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="7" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button"
                                                                   value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span> <input type="hidden" id="attachids"
                                               name="attachids" value="${arearentProcessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="left">
                            <td colspan="8" style="padding-left: 10px;">
                                說明：<br>
                                1.借用流程：申請人填寫申請單并寫明存放地點→申請單位處級主管核准→5S責任區域管理單位→工業安全部主管核准（符合消防安全管理規定）→總務主管核准→物品存放→粘貼公共區域佔用名牌卡；<br>
                                2.無申請單私自存放者，總務部將以亂擺、亂放處理；<br>
                                3.超出借用時段，需重新提出需求，經總務主管核准后，方予續借。
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="8">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_changdijieyongshenqing','公共場地借用申請單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="8" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('input[name=\'usefactoryid\']').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${arearentProcessEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${arearentProcessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('input[name=\'usefactoryid\']').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${arearentProcessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${arearentProcessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'cchargeno','cchargename',$('input[name=\'usefactoryid\']').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${arearentProcessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${arearentProcessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('input[name=\'usefactoryid\']').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${arearentProcessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${arearentProcessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl1Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">5S管理單位</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(68,'yl1Table','ylno1','ylname1',$('input[name=\'usefactoryid\']').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno1" name="ylno1"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno1']}"
                                                               readonly
                                                               value="${arearentProcessEntity.ylno1 }"/><c:if
                                                            test="${requiredMap['ylno1'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname1" name="ylname1"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno1']}"
                                                                value="${arearentProcessEntity.ylname1 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">工業安全</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(69,'yl2Table','ylno2','ylname2',$('input[name=\'usefactoryid\']').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="ylno2"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}"
                                                               readonly
                                                               value="${arearentProcessEntity.ylno2 }"/><c:if
                                                            test="${requiredMap['ylno2'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname2" name="ylname2"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}"
                                                                value="${arearentProcessEntity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl3Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">總務確認窗口</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(70,'yl3Table','ylno3','ylname3',$('input[name=\'usefactoryid\']').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno3" name="ylno3"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno3']}"
                                                               readonly
                                                               value="${arearentProcessEntity.ylno3 }"/><c:if
                                                            test="${requiredMap['ylno3'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname3" name="ylname3"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno3']}"
                                                                value="${arearentProcessEntity.ylname3 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl4Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">總務主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(71,'yl4Table','ylno4','ylname4',$('input[name=\'usefactoryid\']').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno4" name="ylno4"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno4']}"
                                                               readonly
                                                               value="${arearentProcessEntity.ylno4 }"/><c:if
                                                            test="${requiredMap['ylno4'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname4" name="ylname4"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno4']}"
                                                                value="${arearentProcessEntity.ylname4 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
</div>
<script src='${ctx}/static/js/generalAffairs/arearentprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>