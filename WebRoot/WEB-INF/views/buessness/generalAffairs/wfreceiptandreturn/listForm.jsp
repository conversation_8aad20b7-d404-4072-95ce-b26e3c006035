<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>費用性領退料需求申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: right;
        }

        .border_style_bottom {
            border-top: none;
            border-left: none;
            border-right: none;
        }
        .formList2 {
            width: 100%;
            border-right:1px solid #138CDD;
            border-bottom:1px solid #138CDD;
            border-collapse:collapse;
        }
        .formList2 td{
            height: 30px;
            word-break: break-all;
            font-size: 12px;
            border-left:1px solid #138CDD;
            border-top:1px solid #138CDD;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfreceiptandreturn/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfreceiptandreturnEntity.id }"/>
    <input id="serialno" name="wfreceiptandreturn.serialno" type="hidden" value="${wfreceiptandreturnEntity.serialno }"/>
    <input id="makerno" name="wfreceiptandreturn.makerno" type="hidden" value="${wfreceiptandreturnEntity.makerno }"/>
    <input id="makername" name="wfreceiptandreturn.makername" type="hidden" value="${wfreceiptandreturnEntity.makername }"/>
    <input id="makerdeptno" name="wfreceiptandreturn.makerdeptno" type="hidden" value="${wfreceiptandreturnEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfreceiptandreturn.makerfactoryid" type="hidden" value="${wfreceiptandreturnEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">費用性領退料需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfreceiptandreturnEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfreceiptandreturnEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfreceiptandreturnEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfreceiptandreturnEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfreceiptandreturnEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfreceiptandreturnEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfreceiptandreturnEntity.makerno}/${wfreceiptandreturnEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="8" style="width:100%;" class="td_style1">
                                Resevation number：
                                <input id="resevationnumber2" name="wfreceiptandreturn.resevationnumber2"
                                       class="easyui-validatebox border_style_bottom"
                                       data-options="width: 120"
                                       value="${wfreceiptandreturnEntity.resevationnumber2}"/>
                                Site:&nbsp;<font color="red">*</font>
                                <input id="siteno" name="wfreceiptandreturn.siteno" class="easyui-combobox" data-options="width:100,required:true,onSelect:function(){changeSiteno('siteno');}"
                                       value="${wfreceiptandreturnEntity.siteno }"  />
                                <input id="sitename" name="wfreceiptandreturn.sitename"
                                       value="${wfreceiptandreturnEntity.sitename}"  type="hidden"/>

                                <a href="javascript:;" id="btnExtract" class="easyui-linkbutton" style="width: 120px;"
                                   onclick="getSapRfcInfo()">提取預留單號</a>&nbsp;&nbsp;&nbsp;&nbsp;
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="13%" style="border: none;border-left: 1px solid #138CDD;" class="td_style2">Resevation number：</td>
                            <td width="12%" style="border: none" class="td_style1">
                                <input id="resevationnumber" name="wfreceiptandreturn.resevationnumber"
                                       class="easyui-validatebox border_style_bottom"
                                       data-options="width: 120" readonly
                                       value="${wfreceiptandreturnEntity.resevationnumber}"/></td>
                            <td width="13%" style="border: none" class="td_style2">Base date：</td>
                            <td width="12%" style="border: none" class="td_style1">
                                <input id="basedate" name="wfreceiptandreturn.basedate" class="easyui-validatebox border_style_bottom"
                                       data-options="width:100" readonly value="${wfreceiptandreturnEntity.basedate}"/>

                                <input id="basedate2" name="wfreceiptandreturn.basedate2" type="hidden" class="easyui-validatebox Wdate"
                                       value="<fmt:formatDate  pattern="yyyy/MM/dd" value="${wfreceiptandreturnEntity.basedate2}"/>"/>
                            </td>
                            <td width="13%" style="border: none" class="td_style2">Plant：</td>
                            <td width="12%" style="border: none" class="td_style1">
                               <%-- <input id="plant" name="wfreceiptandreturn.plant"
                                       class="easyui-validatebox border_style_bottom"
                                       data-options="width: 120" readonly value="${wfreceiptandreturnEntity.plant}"/>--%>
                                <input id="plant" name="wfreceiptandreturn.plant" class="easyui-combobox" data-options="width:120,onSelect:function(){changePlant();}"
                                       value="${wfreceiptandreturnEntity.plant}"  />
                                <input id="plantname" name="wfreceiptandreturn.plantname" value="${wfreceiptandreturnEntity.plantname}"  type="hidden"/>

                            </td>
                            <td width="13%" style="border: none" class="td_style2">G/L account NO：</td>
                            <td width="12%" style="border: none;border-right: 1px solid #138CDD;" class="td_style1">
                                <input id="glaccountno" name="wfreceiptandreturn.glaccountno"
                                       class="easyui-validatebox border_style_bottom"
                                       data-options="width: 120" readonly
                                       value="${wfreceiptandreturnEntity.glaccountno}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td style="border: none;border-left: 1px solid #138CDD;" class="td_style2">Movement Type：</td>
                            <td style="border: none" class="td_style1">
                                <input id="movemengtype" name="wfreceiptandreturn.movemengtype"
                                       class="easyui-validatebox border_style_bottom"
                                       data-options="width: 120" readonly
                                       value="${wfreceiptandreturnEntity.movemengtype}"/></td>
                            <td style="border: none" class="td_style2">Cost center：</td>
                            <td style="border: none" class="td_style1">
                                <input id="costcenter" name="wfreceiptandreturn.costcenter"
                                       class="easyui-validatebox border_style_bottom"
                                       data-options="width: 120" readonly
                                       value="${wfreceiptandreturnEntity.costcenter}"/></td>
                            <td style="border: none" class="td_style2">Order：</td>
                            <td style="border: none"  class="td_style1">
                                <input id="sadorder" name="wfreceiptandreturn.sadorder"
                                       class="easyui-validatebox border_style_bottom"
                                       data-options="width: 120" value="${wfreceiptandreturnEntity.sadorder}"/>
                            </td>
                            <td style="border: none" class="td_style2">Order description：</td>
                            <td style="border: none;border-right: 1px solid #138CDD;"  class="td_style1">
                                <input id="orderdescription" name="wfreceiptandreturn.orderdescription"
                                       class="easyui-validatebox border_style_bottom"
                                       data-options="width: 120"
                                       value="${wfreceiptandreturnEntity.orderdescription}"/></td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">領退料清單</td>
                        </tr>
                        <tr>
                            <td colspan="8" width="100%">
                                <table id="receiptandreturnitemTable" class="formList">
                                    <tr align="center">
                                        <td width="5%">&nbsp;Item&nbsp;</td>
                                        <td width="15%">Material Number<br>Description</td>
                                        <td width="10%">Require QTY</td>
                                        <td width="5%">Unit</td>
                                        <td width="5%">Sloc</td>
                                        <td width="10%">Batch</td>
                                        <td width="30%">Reason</td>
                                        <td width="10%">Unit price</td>
                                        <td width="10%">Total price</td>
                                    </tr>
                                    <c:if test="${wfreceiptandreturnitems!=null&&wfreceiptandreturnitems.size()>0}">
                                        <c:forEach items="${wfreceiptandreturnitems}" var="receiptandreturnitem" varStatus="status">
                                            <tr align="center" id="receiptandreturnItem${status.index+1}">
                                                <td><input type='hidden' name='wfreceiptandreturnitems[${status.index}].itemnum' value='${receiptandreturnitem.itemnum}'/>${receiptandreturnitem.itemnum}</td>
                                                <td><input type='hidden' name='wfreceiptandreturnitems[${status.index}].materialnumber' value='${receiptandreturnitem.materialnumber}'/>
                                                        ${receiptandreturnitem.materialnumber}<br><input type='hidden' name='wfreceiptandreturnitems[${status.index}].marnumdesc' value='${receiptandreturnitem.marnumdesc}'/>${receiptandreturnitem.marnumdesc}
                                                </td>
                                                <td><input type='hidden' name='wfreceiptandreturnitems[${status.index}].requireqty' value='${receiptandreturnitem.requireqty}'/>${receiptandreturnitem.requireqty}</td>
                                                <td><input type='hidden' name='wfreceiptandreturnitems[${status.index}].unitslocqty' value='${receiptandreturnitem.unitslocqty}'/>${receiptandreturnitem.unitslocqty}</td>
                                                <td><input type='hidden' name='wfreceiptandreturnitems[${status.index}].sloc' value='${receiptandreturnitem.sloc}'/>${receiptandreturnitem.sloc}</td>
                                                <td><input type='hidden' name='wfreceiptandreturnitems[${status.index}].batch' value='${receiptandreturnitem.batch}'/>${receiptandreturnitem.batch}</td>
                                                <td><input type='hidden' name='wfreceiptandreturnitems[${status.index}].reason' value='${receiptandreturnitem.reason}'/>${receiptandreturnitem.reason}</td>
                                                <td><input type='hidden' name='wfreceiptandreturnitems[${status.index}].unitprice'  value='${receiptandreturnitem.unitprice}'/>${receiptandreturnitem.unitprice}</td>
                                                <td><input type='hidden' name='wfreceiptandreturnitems[${status.index}].totalprice'  value='${receiptandreturnitem.totalprice}'/>${receiptandreturnitem.totalprice}</td>
                                            </tr>
                                        </c:forEach>
                                        <tr align="center">
                                            <td colspan="2">TOTAL</td>
                                            <td>
                                                ${wfreceiptandreturnEntity.totalrequireqty}
                                                <input type='hidden' name='wfreceiptandreturn.totalrequireqty' value='${wfreceiptandreturnEntity.totalrequireqty}' />
                                            </td>
                                            <td colspan="5"></td>
                                            <td>
                                                ${wfreceiptandreturnEntity.totalpricemain}
                                                <input type='hidden' name='wfreceiptandreturn.totalpricemain' value='${wfreceiptandreturnEntity.totalpricemain}'/>
                                            </td>
                                        </tr>
                                    </c:if>
                                </table>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>申請人工號&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">&nbsp;
                                <input id="applyno" name="wfreceiptandreturn.applyno" class="easyui-validatebox"
                                       data-options="width: 100,required:true"
                                       value="${wfreceiptandreturnEntity.applyno}" onblur="queryUserInfo(this);"/>
                                <input id="applyfactoryid" name="wfreceiptandreturn.applyfactoryid" type="hidden"
                                       value="${wfreceiptandreturnEntity.applyfactoryid }"/>
                            </td>
                            <td>申請人姓名</td>
                            <td colspan="2" class="td_style1">&nbsp;
                                <input id="applyname" name="wfreceiptandreturn.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:100" readonly value="${wfreceiptandreturnEntity.applyname }"/>
                            </td>
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">&nbsp;
                                <input id="applytel" name="wfreceiptandreturn.applytel" class="easyui-validatebox"
                                       style="width:100px;"
                                       value="${wfreceiptandreturnEntity.applytel}"
                                       data-options="required:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>領退料具體原因&nbsp;<font color="red">*</font></td>
                            <td colspan="7" class="td_style1">
						    <textarea id="specificreasons" name="wfreceiptandreturn.specificreasons"
                                      class="easyui-validatebox"
                                      oninput="return LessThan(this);"
                                      onchange="return LessThan(this);"
                                      onpropertychange="return LessThan(this);"
                                      maxlength="500"
                                      style="width:99%;height:80px;"
                                      data-options="required:true"
                                      rows="5" cols="6"
                                      data-options="required:true,validType:'length[0,500]'">${wfreceiptandreturnEntity.specificreasons}</textarea><span
                                    id="txtNum"></span></td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="7" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="wfreceiptandreturn.attachids" value="${wfreceiptandreturnEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align:left;" colspan="8">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_feiyongxinglingtuiliaodan','費用性領退料需求申請單','');">點擊查看簽核流程圖</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">

                        <tr>
                            <td colspan="8" style="text-align:left;">
                                <c:choose>
                                    <c:when test="${wfreceiptandreturnEntity.makerfactoryid=='IPESZ' or wfreceiptandreturnEntity.makerfactoryid=='YZSZ'}">
                                        <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                            <tr>
                                                <td style="border:none">
                                                    <table width="18%" style="float: left;margin-left: 5px;" id="empmakerchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['empmakerchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole2(400,'empmakerchargeTable','empmakerchargeno','empmakerchargename',$('#applyfactoryid').val(),'wfreceiptandreturn')"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="empmakerchargeno" name="wfreceiptandreturn.empmakerchargeno"
                                                                       class="easyui-validatebox" data-options="width: 80,required:${requiredMap['empmakerchargeno']}"
                                                                       value="${wfreceiptandreturnEntity.empmakerchargeno }"/>
                                                                <c:if test="${requiredMap['empmakerchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                /<input id="empmakerchargename"
                                                                        name="wfreceiptandreturn.empmakerchargename" readonly
                                                                        class="easyui-validatebox"
                                                                        data-options="width: 80,required:${requiredMap['empmakerchargeno']}"
                                                                        value="${wfreceiptandreturnEntity.empmakerchargename }"/>
                                                            </td>

                                                        </tr>
                                                    </table>

                                                    <table width="18%" style="float: left;margin-left: 5px;" id="empleaderchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['empleaderchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon" id="chargerDiv1"
                                                                                 onclick="selectRole2(405,'empleaderchargeTable','empleaderchargeno','empleaderchargename',$('#applyfactoryid').val(),'wfreceiptandreturn')"></div>
                                                                            <div id="chargerDiv1_" class="float_L qhUserIcon" style="display: none"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="empleaderchargeno" name="wfreceiptandreturn.empleaderchargeno"
                                                                       class="easyui-validatebox" data-options="width: 80,required:${requiredMap['empleaderchargeno']}"
                                                                       value="${wfreceiptandreturnEntity.empleaderchargeno }"/>
                                                                <c:if test="${requiredMap['zabchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                /<input id="empleaderchargename"
                                                                        name="wfreceiptandreturn.empleaderchargename" readonly
                                                                        class="easyui-validatebox"
                                                                        data-options="width: 80,required:${requiredMap['empleaderchargeno']}"
                                                                        value="${wfreceiptandreturnEntity.empleaderchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>

                                                    <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole($('#makerdeptno').val(),'kchargeno','kchargename',$('#makerfactoryid').val())"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="kchargeno" name="wfreceiptandreturn.kchargeno"
                                                                       class="easyui-validatebox"
                                                                       data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                       readonly
                                                                       value="${wfreceiptandreturnEntity.kchargeno }"/><c:if
                                                                    test="${requiredMap['kchargeno'].equals('true')}"><font
                                                                    color="red">*</font></c:if>
                                                                /<input id="kchargename" name="wfreceiptandreturn.kchargename"
                                                                        readonly class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                        value="${wfreceiptandreturnEntity.kchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>

                                                    <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole($('#makerdeptno').val(),'bchargeno','bchargename',$('#makerfactoryid').val())"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="bchargeno" name="wfreceiptandreturn.bchargeno"
                                                                       class="easyui-validatebox"
                                                                       data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                       readonly
                                                                       value="${wfreceiptandreturnEntity.bchargeno }"/><c:if
                                                                    test="${requiredMap['bchargeno'].equals('true')}"><font
                                                                    color="red">*</font></c:if>
                                                                /<input id="bchargename" name="wfreceiptandreturn.bchargename"
                                                                        readonly class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                        value="${wfreceiptandreturnEntity.bchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>

                                                    <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole($('#makerdeptno').val(),'cchargeno','cchargename',$('#makerfactoryid').val())"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="cchargeno" name="wfreceiptandreturn.cchargeno"
                                                                       class="easyui-validatebox"
                                                                       data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                       readonly
                                                                       value="${wfreceiptandreturnEntity.cchargeno }"/><c:if
                                                                    test="${requiredMap['cchargeno'].equals('true')}"><font
                                                                    color="red">*</font></c:if>
                                                                /<input id="cchargename" name="wfreceiptandreturn.cchargename"
                                                                        readonly class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                        value="${wfreceiptandreturnEntity.cchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="border:none">
                                                    <table width="18%" style="float: left;margin-left: 5px;" id="bmpmakerchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['bmpmakerchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon" id="chargerDiv1"
                                                                                 onclick="selectRole2(410,'bmpmakerchargeTable','bmpmakerchargeno','bmpmakerchargename',$('#applyfactoryid').val(),'wfreceiptandreturn')"></div>
                                                                            <div id="chargerDiv1_" class="float_L qhUserIcon" style="display: none"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="bmpmakerchargeno" name="wfreceiptandreturn.bmpmakerchargeno"
                                                                       class="easyui-validatebox" data-options="width: 80,required:${requiredMap['bmpmakerchargeno']}"
                                                                       value="${wfreceiptandreturnEntity.bmpmakerchargeno }"/>
                                                                <c:if test="${requiredMap['bmpmakerchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                /<input id="bmpmakerchargename"
                                                                        name="wfreceiptandreturn.bmpmakerchargename" readonly
                                                                        class="easyui-validatebox"
                                                                        data-options="width: 80,required:${requiredMap['bmpmakerchargeno']}"
                                                                        value="${wfreceiptandreturnEntity.bmpmakerchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <table width="18%" style="float: left;margin-left: 5px;" id="bmpleaderchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">&nbsp;${requiredMap['bmpleaderchargeno_name']}&nbsp;</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole2(415,'bmpleaderchargeTable','bmpleaderchargeno','bmpleaderchargename',$('#applyfactoryid').val(),'wfreceiptandreturn')"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="bmpleaderchargeno" name="wfreceiptandreturn.bmpleaderchargeno"
                                                                       class="easyui-validatebox" data-options="width: 80,required:${requiredMap['bmpleaderchargeno']}"
                                                                       value="${wfreceiptandreturnEntity.bmpleaderchargeno }"/>
                                                                <c:if test="${requiredMap['bmpleaderchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                /<input id="bmpleaderchargename" name="wfreceiptandreturn.bmpleaderchargename"
                                                                        readonly
                                                                        class="easyui-validatebox"
                                                                        data-options="width: 80,required:${requiredMap['bmpleaderchargeno']}"
                                                                        value="${wfreceiptandreturnEntity.bmpleaderchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>


                                                    <table width="18%" style="float: left;margin-left: 5px;"
                                                           id="hq1chargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: center;">${requiredMap['hq1chargeno_name']}
                                                                            <a href="javascript:addHq2('hq1charge');">添加一位</a></td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="hq1chargeno" name="wfreceiptandreturn.hq1chargeno" onblur="gethqUserNameByEmpno(this,'hq1charge');"
                                                                       class="easyui-validatebox"
                                                                       data-options="width:80,required:${requiredMap['hq1chargeno']}"
                                                                       value="${wfreceiptandreturnEntity.hq1chargeno }"/><c:if
                                                                    test="${requiredMap['hq1chargeno'].equals('true')}"><font
                                                                    color="red">*</font></c:if>
                                                                /<input id="hq1chargename" name="wfreceiptandreturn.hq1chargename"
                                                                        readonly class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['hq1chargeno']}"
                                                                        value="${wfreceiptandreturnEntity.hq1chargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <table width="18%" style="float: left;margin-left: 5px;" id="hq2chargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: center;">${requiredMap['hq2chargeno_name']}
                                                                            <a href="javascript:addHq2('hq2charge');">添加一位</a>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="hq2chargeno" name="wfreceiptandreturn.hq2chargeno" onblur="gethqUserNameByEmpno(this,'hq2charge');"
                                                                       class="easyui-validatebox"
                                                                       data-options="width:80,required:${requiredMap['hq2chargeno']}"
                                                                       value="${wfreceiptandreturnEntity.hq2chargeno }"/><c:if
                                                                    test="${requiredMap['hq2chargeno'].equals('true')}"><font
                                                                    color="red">*</font></c:if>
                                                                /<input id="hq2chargename" name="wfreceiptandreturn.hq2chargename"
                                                                        readonly class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['hq2chargeno']}"
                                                                        value="${wfreceiptandreturnEntity.hq2chargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>

                                                    <table width="18%" style="float: left;margin-left: 5px;" id="zalchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['zalchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole2(120,'zalchargeTable','zalchargeno','zalchargename',$('#applyfactoryid').val(),'wfreceiptandreturn')"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="zalchargeno" name="wfreceiptandreturn.zalchargeno"
                                                                       class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zalchargeno']}"
                                                                       value="${wfreceiptandreturnEntity.zalchargeno }"/>
                                                                <c:if test="${requiredMap['zalchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                /<input id="zalchargename"
                                                                        name="wfreceiptandreturn.zalchargename" readonly
                                                                        class="easyui-validatebox"
                                                                        data-options="width: 80,required:${requiredMap['zalchargeno']}"
                                                                        value="${wfreceiptandreturnEntity.zalchargename }"/>
                                                            </td>

                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="border:none">
                                                    <table width="18%" style="float: left;margin-left: 5px;" id="hzchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['hzchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole2(125,'hzchargeTable','hzchargeno','hzchargename',$('#applyfactoryid').val(),'wfreceiptandreturn')"></div>
                                                                        </td>
                                                                    </tr>

                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="hzchargeno" name="wfreceiptandreturn.hzchargeno"
                                                                       class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hzchargeno']}"
                                                                       value="${wfreceiptandreturnEntity.hzchargeno }"/>
                                                                <c:if test="${requiredMap['hzchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                /<input id="hzchargename" name="wfreceiptandreturn.hzchargename"
                                                                        readonly
                                                                        class="easyui-validatebox"
                                                                        data-options="width: 80,required:${requiredMap['hzchargeno']}"
                                                                        value="${wfreceiptandreturnEntity.hzchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </c:when>
                                    <c:otherwise>
                                        <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                            <tr>
                                                <td style="border:none">
                                                    <table width="18%" style="float: left;margin-left: 5px;" id="zalchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['zalchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole2(120,'zalchargeTable','zalchargeno','zalchargename',$('#applyfactoryid').val(),'wfreceiptandreturn')"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="zalchargeno" name="wfreceiptandreturn.zalchargeno"
                                                                       class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zalchargeno']}"
                                                                       value="${wfreceiptandreturnEntity.zalchargeno }"/>
                                                                <c:if test="${requiredMap['zalchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                /<input id="zalchargename"
                                                                        name="wfreceiptandreturn.zalchargename" readonly
                                                                        class="easyui-validatebox"
                                                                        data-options="width: 80,required:${requiredMap['zalchargeno']}"
                                                                        value="${wfreceiptandreturnEntity.zalchargename }"/>
                                                            </td>

                                                        </tr>
                                                    </table>

                                                    <table width="18%" style="float: left;margin-left: 5px;" id="zabchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['zabchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon" id="chargerDiv1"
                                                                                 onclick="selectRole2(121,'zabchargeTable','zabchargeno','zabchargename',$('#applyfactoryid').val(),'wfreceiptandreturn')"></div>
                                                                            <div id="chargerDiv1_" class="float_L qhUserIcon" style="display: none"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="zabchargeno" name="wfreceiptandreturn.zabchargeno"
                                                                       class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zabchargeno']}"
                                                                       value="${wfreceiptandreturnEntity.zabchargeno }"/>
                                                                <c:if test="${requiredMap['zabchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                /<input id="zabchargename"
                                                                        name="wfreceiptandreturn.zabchargename" readonly
                                                                        class="easyui-validatebox"
                                                                        data-options="width: 80,required:${requiredMap['zabchargeno']}"
                                                                        value="${wfreceiptandreturnEntity.zabchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <table width="18%" style="float: left;margin-left: 5px;" id="pmchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">&nbsp;${requiredMap['pmchargeno_name']}&nbsp;</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole2(122,'pmchargeTable','pmchargeno','pmchargename',$('#applyfactoryid').val(),'wfreceiptandreturn')"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="pmchargeno" name="wfreceiptandreturn.pmchargeno"
                                                                       class="easyui-validatebox" data-options="width: 80,required:${requiredMap['pmchargeno']}"
                                                                       value="${wfreceiptandreturnEntity.pmchargeno }"/>
                                                                <c:if test="${requiredMap['pmchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                /<input id="pmchargename" name="wfreceiptandreturn.pmchargename"
                                                                        readonly
                                                                        class="easyui-validatebox"
                                                                        data-options="width: 80,required:${requiredMap['pmchargeno']}"
                                                                        value="${wfreceiptandreturnEntity.pmchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>

                                                    <table width="18%" style="float: left;margin-left: 5px;" id="zxjchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['zxjchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon" id="chargerDiv2"
                                                                                 onclick="selectRole2(123,'zxjchargeTable','zxjchargeno','zxjchargename',$('#applyfactoryid').val(),'wfreceiptandreturn')"></div>
                                                                            <div id="chargerDiv2_" class="float_L qhUserIcon" style="display: none"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="zxjchargeno" name="wfreceiptandreturn.zxjchargeno"
                                                                       class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zxjchargeno']}"
                                                                       value="${wfreceiptandreturnEntity.zxjchargeno }"/>
                                                                <c:if test="${requiredMap['zxjchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                /<input id="zxjchargename"
                                                                        name="wfreceiptandreturn.zxjchargename" readonly
                                                                        class="easyui-validatebox"
                                                                        data-options="width: 80,required:${requiredMap['zxjchargeno']}"
                                                                        value="${wfreceiptandreturnEntity.zxjchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>

                                                    <table width="18%" style="float: left;margin-left: 5px;" id="gcgchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['gcgchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole2(124,'gcgchargeTable','gcgchargeno','gcgchargename',$('#applyfactoryid').val(),'wfreceiptandreturn')"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="gcgchargeno" name="wfreceiptandreturn.gcgchargeno"
                                                                       class="easyui-validatebox" data-options="width: 80,required:${requiredMap['gcgchargeno']}"
                                                                       value="${wfreceiptandreturnEntity.gcgchargeno }"/>
                                                                <c:if test="${requiredMap['gcgchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                /<input id="gcgchargename"
                                                                        name="wfreceiptandreturn.gcgchargename" readonly
                                                                        class="easyui-validatebox"
                                                                        data-options="width: 80,required:${requiredMap['gcgchargeno']}"
                                                                        value="${wfreceiptandreturnEntity.gcgchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="border:none">
                                                    <table width="18%" style="float: left;margin-left: 5px;" id="shchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['shchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon" id="chargerDiv3"
                                                                                 onclick="selectRole2(152,'shchargeTable','shchargeno','shchargename',$('#applyfactoryid').val(),'wfreceiptandreturn')"></div>
                                                                            <div id="chargerDiv3_" class="float_L qhUserIcon" style="display: none"></div>
                                                                        </td>
                                                                    </tr>

                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="shchargeno" name="wfreceiptandreturn.shchargeno"
                                                                       class="easyui-validatebox" data-options="width: 80,required:${requiredMap['shchargeno']}"
                                                                       value="${wfreceiptandreturnEntity.shchargeno }"/>
                                                                <c:if test="${requiredMap['shchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                /<input id="shchargename" name="wfreceiptandreturn.shchargename"
                                                                        readonly
                                                                        class="easyui-validatebox"
                                                                        data-options="width: 80,required:${requiredMap['shchargeno']}"
                                                                        value="${wfreceiptandreturnEntity.shchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <table width="18%" style="float: left;margin-left: 5px;" id="jgchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['jgchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole2(126,'jgchargeTable','jgchargeno','jgchargename',$('#applyfactoryid').val(),'wfreceiptandreturn')"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="jgchargeno" name="wfreceiptandreturn.jgchargeno"
                                                                       class="easyui-validatebox"
                                                                       data-options="width: 80,required:${requiredMap['jgchargeno']}"
                                                                       value="${wfreceiptandreturnEntity.jgchargeno }"/>
                                                                <c:if test="${requiredMap['jgchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                /<input id="jgchargename" name="wfreceiptandreturn.jgchargename"
                                                                        readonly class="easyui-validatebox"
                                                                        data-options="width: 80,required:${requiredMap['jgchargeno']}"
                                                                        value="${wfreceiptandreturnEntity.jgchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <table width="18%" style="float: left;margin-left: 5px;" id="hzchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['hzchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole2(125,'hzchargeTable','hzchargeno','hzchargename',$('#applyfactoryid').val(),'wfreceiptandreturn')"></div>
                                                                        </td>
                                                                    </tr>

                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="hzchargeno" name="wfreceiptandreturn.hzchargeno"
                                                                       class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hzchargeno']}"
                                                                       value="${wfreceiptandreturnEntity.hzchargeno }"/>
                                                                <c:if test="${requiredMap['hzchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                /<input id="hzchargename" name="wfreceiptandreturn.hzchargename"
                                                                        readonly
                                                                        class="easyui-validatebox"
                                                                        data-options="width: 80,required:${requiredMap['hzchargeno']}"
                                                                        value="${wfreceiptandreturnEntity.hzchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="8" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>

    <div id="win"></div>
</form>
<script src='${ctx}/static/js/generalAffairs/wfreceiptandreturn.js?random=<%= Math.random()%>'></script>
</body>
</html>