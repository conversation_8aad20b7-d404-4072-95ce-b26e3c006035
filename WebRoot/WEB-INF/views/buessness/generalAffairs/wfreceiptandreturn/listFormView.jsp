<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
<style type="text/css">
    .td_style2 {
        text-align: right;
    }

    .border_style_bottom {
        border-top: none;
        border-left: none;
        border-right: none;
    }
    .formList td {
        height: 30px;
        word-break: break-all;
        font-size: 14px;
        border: 1px solid #138CDD;
        vertical-align: middle;
    }
    .border_style_bottom2 {
        border-top: none;
        border-left: none;
        border-right: none;
        border-bottom: 1px solid #138CDD;
    }
</style>
</head>
<body>
<form id="mainform" action="${ctx}/wfreceiptandreturn/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfreceiptandreturnEntity.id }"/>
    <input id="serialno" name="wfreceiptandreturn.serialno" type="hidden" value="${wfreceiptandreturnEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">
        <c:if test="${wfreceiptandreturnEntity.movemengtype=='Z01'}">
            費用性領料單
        </c:if>
        <c:if test="${wfreceiptandreturnEntity.movemengtype=='Z02'}">
            費用性退料單
        </c:if>
        <c:if test="${wfreceiptandreturnEntity.movemengtype==null}">
            費用性領退料需求申請單
        </c:if>
    </div>
    <div class="position_L">
        任務編碼：<span style="color:#999;">
               <c:choose>
                   <c:when test="${wfreceiptandreturnEntity.serialno==null}">
                       提交成功后自動編碼
                   </c:when>
                   <c:otherwise>
                       ${wfreceiptandreturnEntity.serialno}
                   </c:otherwise>
               </c:choose>
            </span>
    </div>
    <div class="position_L1 margin_L">
        填單時間：<span style="color:#999;">
                <c:choose>
                    <c:when test="${wfreceiptandreturnEntity.createtime==null}">
                        YYYY/MM/DD
                    </c:when>
                    <c:otherwise>
                        <input style="border:0px" style="width: 120px" readonly="true"
                               value="<fmt:formatDate value='${wfreceiptandreturnEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                    </c:otherwise>
                </c:choose>
            </span>
    </div>
    <div class="position_R margin_R">填單人：${wfreceiptandreturnEntity.makerno}/${wfreceiptandreturnEntity.makername}</div>
    <div class="clear"></div>
    <table  class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="8" style="width:100%;" class="td_style1">
                                Resevation number：
                                <input id="resevationnumber2" name="wfreceiptandreturn.resevationnumber"
                                       class="easyui-validatebox border_style_bottom"
                                       data-options="width: 150" readonly
                                       value="${wfreceiptandreturnEntity.resevationnumber}"/>
                                Site:
                                <input id="sitename" name="wfreceiptandreturn.sitename"
                                       class="easyui-validatebox border_style_bottom"
                                       value="${wfreceiptandreturnEntity.sitename}" readonly />
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="17%" style="border: none;border-left: 1px solid #138CDD" class="td_style2">Resevation number：</td>
                            <td width="10%" style="border: none;" class="td_style1">
                                <input id="resevationnumber"
                                       class="easyui-validatebox border_style_bottom"
                                       data-options="width: 120" readonly
                                       value="${wfreceiptandreturnEntity.resevationnumber}"/></td>
                            <td width="13%" style="border: none " class="td_style2">Base date：</td>
                            <td width="12%" style="border: none" class="td_style1">
                                <input id="basedate" name="wfreceiptandreturn.basedate" class="easyui-validatebox border_style_bottom"
                                       data-options="width:100" readonly
                                       value="${wfreceiptandreturnEntity.basedate}"/>
                            </td>
                            <td width="12%" style="border: none" class="td_style2">Plant：</td>
                            <td width="11%" style="border: none" class="td_style1">
                                <c:choose>
                                    <c:when test="${not empty wfreceiptandreturnEntity.plantname}">
                                        <input name="wfreceiptandreturn.plantname"
                                               class="easyui-validatebox border_style_bottom"
                                               data-options="width: 120" readonly value="${wfreceiptandreturnEntity.plantname}"/>
                                    </c:when>
                                    <c:otherwise>
                                        <input name="wfreceiptandreturn.plant"
                                               class="easyui-validatebox border_style_bottom"
                                               data-options="width: 120" readonly value="${wfreceiptandreturnEntity.plant}"/>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td width="15%" style="border: none" class="td_style2">G/L account NO：</td>
                            <td width="10%" style="border: none;border-right: 1px solid #138CDD;" class="td_style1">
                                <input id="glaccountno" name="wfreceiptandreturn.glaccountno"
                                       class="easyui-validatebox border_style_bottom"
                                       data-options="width: 120" readonly
                                       value="${wfreceiptandreturnEntity.glaccountno}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td style="border: none;border-left: 1px solid #138CDD;" class="td_style2">Movement Type：</td>
                            <td style="border: none" class="td_style1">
                                <input id="movemengtype" name="wfreceiptandreturn.movemengtype"
                                       class="easyui-validatebox border_style_bottom"
                                       data-options="width: 120" readonly
                                       value="${wfreceiptandreturnEntity.movemengtype}"/></td>
                            <td style="border: none" class="td_style2">Cost center：</td>
                            <td style="border: none" class="td_style1">
                                <input id="costcenter" name="wfreceiptandreturn.costcenter"
                                       class="easyui-validatebox border_style_bottom"
                                       data-options="width: 120" readonly
                                       value="${wfreceiptandreturnEntity.costcenter}"/></td>
                            <td style="border: none" class="td_style2">Order：</td>
                            <td style="border: none"  class="td_style1">
                                <input id="sadorder" name="wfreceiptandreturn.sadorder"
                                       class="easyui-validatebox border_style_bottom"
                                       data-options="width: 120" readonly value="${wfreceiptandreturnEntity.sadorder}"/>
                            </td>
                            <td style="border: none" class="td_style2">Order description：</td>
                            <td style="border: none;border-right: 1px solid #138CDD;"  class="td_style1">
                                <input id="orderdescription" name="wfreceiptandreturn.orderdescription"
                                       class="easyui-validatebox border_style_bottom"
                                       data-options="width: 120" readonly
                                       value="${wfreceiptandreturnEntity.orderdescription}"/></td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">領退料清單</td>
                        </tr>
                        <tr>
                            <td colspan="8" width="100%">
                                <table id="receiptandreturnitemTable" class="formList">
                                    <tr align="center">
                                        <td width="5%">&nbsp;Item&nbsp;</td>
                                        <td width="15%">Material Number<br>Description</td>
                                        <td width="10%">Require QTY</td>
                                        <td width="5%">Unit</td>
                                        <td width="5%">Sloc</td>
                                        <td width="10%">Batch</td>
                                        <td width="30%">Reason</td>
                                        <td width="10%">Unit price</td>
                                        <td width="10%">Total price</td>
                                    </tr>
                                    <c:if test="${wfreceiptandreturnitems!=null&&wfreceiptandreturnitems.size()>0}">
                                        <c:forEach items="${wfreceiptandreturnitems}" var="receiptandreturnitem"
                                                   varStatus="status">
                                            <tr align="center" id="receiptandreturnItem${status.index+1}">
                                                <td>${receiptandreturnitem.itemnum}</td>
                                                <td>${receiptandreturnitem.materialnumber}<br>${receiptandreturnitem.marnumdesc}</td>
                                                <td>${receiptandreturnitem.requireqty}</td>
                                                <td>${receiptandreturnitem.unitslocqty}</td>
                                                <td>${receiptandreturnitem.sloc}</td>
                                                <td>${receiptandreturnitem.batch}</td>
                                                <td>${receiptandreturnitem.reason}</td>
                                                <td>${receiptandreturnitem.unitprice}</td>
                                                <td>${receiptandreturnitem.totalprice}</td>
                                            </tr>
                                        </c:forEach>
                                        <tr align="center">
                                            <td colspan="2">TOTAL</td>
                                            <td>${wfreceiptandreturnEntity.totalrequireqty}</td>
                                            <td colspan="5"></td>
                                            <td>${wfreceiptandreturnEntity.totalpricemain}</td>
                                        </tr>
                                    </c:if>
                                </table>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>申請人工號</td>
                            <td colspan="2" class="td_style1">&nbsp;
                                <input id="applyno" name="wfreceiptandreturn.applyno" class="easyui-validatebox inputCss"
                                       data-options="width: 100" readonly
                                       value="${wfreceiptandreturnEntity.applyno}"/>
                            </td>
                            <td>申請人姓名</td>
                            <td colspan="2" class="td_style1">&nbsp;
                                <input id="applyname" name="wfreceiptandreturn.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:100" readonly value="${wfreceiptandreturnEntity.applyname }"/>
                            </td>
                            <td>聯繫分機</td>
                            <td class="td_style1">&nbsp;
                                <input id="applytel" name="wfreceiptandreturn.applytel" class="easyui-validatebox inputCss"
                                       style="width:100px;" value="${wfreceiptandreturnEntity.applytel}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>領退料具體原因</td>
                            <td colspan="7" class="td_style1">
                                <textarea id="specificreasons" name="wfreceiptandreturn.specificreasons"
                                          class="easyui-validatebox " readonly
                                          style="width:99%;height:80px;"
                                          rows="5" cols="6">${wfreceiptandreturnEntity.specificreasons}</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="7" class="td_style1">
                                <input type="hidden" id="attachids" name="attachids" value="${wfreceiptandreturnEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <c:if test="${wfreceiptandreturnEntity.paytype!=null}">
                            <tr align="center">
                                <td>付費類型确认</td>
                                <td colspan="7" class="td_style1">
                                    <div class="paytypeDiv"></div>
                                    <input id="paytype" name="wfreceiptandreturn.paytype"
                                           type="hidden" class="easyui-validatebox" data-options="width: 150"
                                           value="${wfreceiptandreturnEntity.paytype}"/>
                                    <input id="paytypeAudit" name="wfreceiptandreturn.paytype"
                                           type="hidden" class="easyui-validatebox" data-options="width: 150"
                                           value="${wfreceiptandreturnEntity.paytype }"/>
                                </td>
                            </tr>
                        </c:if>
                        <tr>
                            <th style="text-align:left;" colspan="8">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','費用性領退料需求申請單');">點擊查看簽核流程圖</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfreceiptandreturnEntity.workstatus==2}">
                                <a href="javascript:;" id="btnExtract" class="easyui-linkbutton" style="width: 100px;"
                                   onclick="getNodeUserInfo('${nodeUserNo}')">待簽核主管</a>
                                </c:if>
                            </th>
                        </tr>
                    </table>
                </td>
            </tr>

            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfreceiptandreturnEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="8" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                               <c:if test="${wfreceiptandreturnEntity.workstatus==3}">
                                  <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                      style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                               </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <c:if test="${wfreceiptandreturnEntity.workstatus==3}">
            <div style="margin-top: 50px;margin-left: 30px;margin-bottom: 10px;">
                <label style="margin-right: 100px;">
                    其它：<input class="easyui-validatebox border_style_bottom" data-options="width: 200" readonly/>
                </label>
                <label style="margin-right: 100px;">
                    簽收：<input class="easyui-validatebox border_style_bottom" data-options="width: 200" readonly/>
                </label>
                <label style="margin-right: 100px;">
                    倉管：<input class="easyui-validatebox border_style_bottom" data-options="width: 200" readonly/>
                </label>
                <P>
                    備註：其他欄位簽核權限：原料---採購；半成品---生管；成品---企劃主管
                </P>
            </div>
        </c:if>
    </div>
</form>
<div id="dlg"></div>
<input type="hidden" id="disOrEnabled"  value="disabled"/>
<input type="hidden" id="customerpaysAudit"   value="${wfreceiptandreturnEntity.customerpays}" />
<script src='${ctx}/static/js/generalAffairs/wfreceiptandreturn.js?random=<%= Math.random()%>'></script>
</body>
</html>