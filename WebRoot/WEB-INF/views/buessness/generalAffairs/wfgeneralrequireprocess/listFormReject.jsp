<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>庶務需求申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfgeneralrequireprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfgeneralrequireprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfgeneralrequireprocessEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfgeneralrequireprocessEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfgeneralrequireprocessEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfgeneralrequireprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden"
           value="${wfgeneralrequireprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">庶務需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfgeneralrequireprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfgeneralrequireprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfgeneralrequireprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfgeneralrequireprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfgeneralrequireprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfgeneralrequireprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfgeneralrequireprocessEntity.makerno}/${wfgeneralrequireprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfgeneralrequireprocessEntity.applyno}" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="5%">申請人</td>
                            <td width="5%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wfgeneralrequireprocessEntity.applyname }"/>
                            </td>
                            <td width="5%">部門代碼</td>
                            <td width="5%" class="td_style1">
                                <input id="applydepartno" name="applydepartno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfgeneralrequireprocessEntity.applydepartno}"/>
                            </td>
                            <td width="5%">費用代碼</td>
                            <td width="5%" class="td_style1">
                                <input id="deptcostno" name="deptcostno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfgeneralrequireprocessEntity.deptcostno}"/>
                            </td>

                            <td width="5%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="applyFactoryId" name="applyFactoryId" class="easyui-combobox"
                                       panelHeight="auto" value="${wfgeneralrequireprocessEntity.applyFactoryId }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applyFactoryId\',\'请选择所在廠區\']'"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="applydepartname" name="applydepartname" class="easyui-validatebox"
                                       data-options="width: 500,required:true"
                                       value="${wfgeneralrequireprocessEntity.applydepartname}"/>
                            </td>

                            <td>法人&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="layperson" name="layperson" class="easyui-combobox"
                                       panelHeight="auto" value="${wfgeneralrequireprocessEntity.layperson }"
                                       data-options="width: 425,required:true,validType:'comboxValidate[\'layperson\',\'请选择法人\']'"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫電話&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applytel" name="applytel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfgeneralrequireprocessEntity.applytel}" data-options="required:true"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       value="${wfgeneralrequireprocessEntity.applyemail }" style="width:500px;"
                                       data-options="required:true" onblur="valdEmail('')"/>
                            </td>
                            <td>期望完工日期&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="wantDate" name="wantDate" class="easyui-validatebox Wdate"
                                       data-options="width:120,required:true" style="width:120px"
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd"
										value="${wfgeneralrequireprocessEntity.wantDate}"/>"
                                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td>名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="4" class="td_style1">
                                <input id="title" name="title" class="easyui-validatebox"
                                       data-options="width: 400,required:true,validType:'length[1,50]'"
                                       value="${wfgeneralrequireprocessEntity.title}"/>
                            </td>

                            <td>地點&nbsp;<font color="red">*</font></td>
                            <td colspan="4" class="td_style1">
                                <input id="address" name="address" class="easyui-validatebox"
                                       data-options="width: 400,required:true,validType:'length[1,50]'"
                                       value="${wfgeneralrequireprocessEntity.address}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>需求類別&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style2" style="text-align: left;">
                                <div class="requireTypeDiv"></div>
                                <input id="requireType" name="requireType"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfgeneralrequireprocessEntity.requireType}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>需求內容&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1">
                                <textarea id="details"
                                          name="details" class="easyui-validatebox"
                                          oninput="return LessThan(this);"
                                          onchange="return LessThan(this);"
                                          onpropertychange="return LessThan(this);"
                                          maxlength="200"
                                          data-options="multiline:true,required:true,validType:'length[1,200]'"
                                          style="width:99%;height:60px;resize:none;" rows="5"
                                          cols="4">${wfgeneralrequireprocessEntity.details}</textarea>
                                <span id="txtNum"></span>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="9" class="td_style2" align="left">
                                1.需求申請--廠/部核准--機能項目評估--庶務費用確認--需求單位權限主管核准--維保實施--驗收確認--費用結報(按需保養流程)；<br/>
                                2.需求申請--廠/部核准--機能項目評估--庶務議價/費用確認--權限主管核准--維修實施--驗收確認--費用結報(維修流程)；<br/>
                                3.委外完工驗收由需求單位、機能單位、維保廠商進行共同驗收確認；<br/>
                                4.驗收結果之費用將作為最終結報依據。
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_shuwuxuqiushenqingdan','庶務需求申請單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="kjzgTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole6($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealfactoryid').val(),'kjfyshno','kjfyshname')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfgeneralrequireprocessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bjzgTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole6($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealfactoryid').val(),'bjfyshno','bjfyshname')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfgeneralrequireprocessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfgeneralrequireprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="jndwrypgTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    機能單位人員評估
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole5('178','jndwrypgno','jndwrypgname','jndwryysno','jndwryysname','','',$('#dealfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jndwrypgno" name="jndwrypgno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['jndwrypgno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.jndwrypgno }"/><c:if
                                                            test="${requiredMap['jndwrypgno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="jndwrypgname" name="jndwrypgname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jndwrypgno']}"
                                                                value="${wfgeneralrequireprocessEntity.jndwrypgname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="jndwzghezTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    機能單位主管核准
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(179,'jndwzghezTable','jndwzghezno','jndwzghezname',$('#dealfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jndwzghezno" name="jndwzghezno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['jndwzghezno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.jndwzghezno }"/><c:if
                                                            test="${requiredMap['jndwzghezno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="jndwzghezname" name="jndwzghezname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jndwzghezno']}"
                                                                value="${wfgeneralrequireprocessEntity.jndwzghezname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="swryTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">庶務人員
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole5('180','swryno','swryname','swryysno','swryysname','','',$('#dealfactoryid').val())">
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="swryno" name="swryno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['swryno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.swryno }"/><c:if
                                                            test="${requiredMap['swryno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="swryname" name="swryname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['swryno']}"
                                                                value="${wfgeneralrequireprocessEntity.swryname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="kjshTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級審核
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(181,'kjshTable','kjshno','kjshname',$('#dealfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kjshno" name="kjshno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kjshno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.kjshno }"/><c:if
                                                            test="${requiredMap['kjshno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kjshname" name="kjshname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kjshno']}"
                                                                value="${wfgeneralrequireprocessEntity.kjshname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bjshTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級審核
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(182,'bjshTable','bjshno','bjshname',$('#dealfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bjshno" name="bjshno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bjshno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.bjshno }"/><c:if
                                                            test="${requiredMap['bjshno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bjshname" name="bjshname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bjshno']}"
                                                                value="${wfgeneralrequireprocessEntity.bjshname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zxjshTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">中心級核准
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(183,'zxjshTable','zxjshno','zxjshname',$('#dealfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxjshno" name="zxjshno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxjshno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.zxjshno }"/><c:if
                                                            test="${requiredMap['zxjshno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxjshname" name="zxjshname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxjshno']}"
                                                                value="${wfgeneralrequireprocessEntity.zxjshname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="sqrfyqrTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">
                                                                    申請人費用確認
                                                                </td>
                                                                <td style="border: none;"></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="sqrfyqrno" name="sqrfyqrno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['sqrfyqrno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.sqrfyqrno }"/><c:if
                                                            test="${requiredMap['sqrfyqrno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="sqrfyqrname" name="sqrfyqrname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['sqrfyqrno']}"
                                                                value="${wfgeneralrequireprocessEntity.sqrfyqrname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="kjfyshTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">
                                                                    課級費用審核
                                                                </td>
                                                                <td style="border: none;"></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kjfyshno" name="kjfyshno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kjfyshno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.kjfyshno }"/><c:if
                                                            test="${requiredMap['kjfyshno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kjfyshname" name="kjfyshname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kjfyshno']}"
                                                                value="${wfgeneralrequireprocessEntity.kjfyshname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bjfyshTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">
                                                                    部級費用審核
                                                                </td>
                                                                <td style="border: none;"></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bjfyshno" name="bjfyshno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bjfyshno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.bjfyshno }"/><c:if
                                                            test="${requiredMap['bjfyshno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bjfyshname" name="bjfyshname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bjfyshno']}"
                                                                value="${wfgeneralrequireprocessEntity.bjfyshname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cjfyshTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">
                                                                    廠級費用審核
                                                                </td>
                                                                <td style="border: none;"></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cjfyshno" name="cjfyshno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cjfyshno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.cjfyshno }"/><c:if
                                                            test="${requiredMap['cjfyshno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cjfyshname" name="cjfyshname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cjfyshno']}"
                                                                value="${wfgeneralrequireprocessEntity.cjfyshname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cjfyhzTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    處級費用核准
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(186,'cjfyhzTable','cjfyhzno','cjfyhzname',$('#dealfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cjfyhzno" name="cjfyhzno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cjfyhzno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.cjfyhzno }"/><c:if
                                                            test="${requiredMap['cjfyhzno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cjfyhzname" name="cjfyhzname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cjfyhzno']}"
                                                                value="${wfgeneralrequireprocessEntity.cjfyhzname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="jgcsTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">經管初審
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(184,'jgcsTable','jgcsno','jgcsname',$('#dealfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jgcsno" name="jgcsno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['jgcsno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.jgcsno }"/><c:if
                                                            test="${requiredMap['jgcsno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="jgcsname" name="jgcsname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jgcsno']}"
                                                                value="${wfgeneralrequireprocessEntity.jgcsname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="jgshTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">經管審核
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(63,'jgshTable','jgshno','jgshname',$('#dealfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jgshno" name="jgshno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['jgshno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.jgshno }"/><c:if
                                                            test="${requiredMap['jgshno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="jgshname" name="jgshname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jgshno']}"
                                                                value="${wfgeneralrequireprocessEntity.jgshname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="jghzTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">經管核准
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(150,'jghzTable','jghzno','jghzname',$('#dealfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jghzno" name="jghzno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['jghzno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.jghzno }"/><c:if
                                                            test="${requiredMap['jghzno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="jghzname" name="jghzname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jghzno']}"
                                                                value="${wfgeneralrequireprocessEntity.jghzname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcjfyhzTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    總處級費用核准
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(185,'zcjfyhzTable','zcjfyhzno','zcjfyhzname',$('#dealfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcjfyhzno" name="zcjfyhzno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcjfyhzno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.zcjfyhzno }"/><c:if
                                                            test="${requiredMap['zcjfyhzno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcjfyhzname" name="zcjfyhzname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcjfyhzno']}"
                                                                value="${wfgeneralrequireprocessEntity.zcjfyhzname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="swryysTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">
                                                                    庶務人員驗收
                                                                </td>
                                                                <td style="border: none;"></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="swryysno" name="swryysno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['swryysno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.swryysno }"/><c:if
                                                            test="${requiredMap['swryysno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="swryysname" name="swryysname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['swryysno']}"
                                                                value="${wfgeneralrequireprocessEntity.swryysname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="sqrysTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">
                                                                    申請人驗收
                                                                </td>
                                                                <td style="border: none;"></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="sqrysno" name="sqrysno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['sqrysno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.sqrysno }"/><c:if
                                                            test="${requiredMap['sqrysno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="sqrysname" name="sqrysname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['sqrysno']}"
                                                                value="${wfgeneralrequireprocessEntity.sqrysname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="jndwryysTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">
                                                                    機能單位人員驗收
                                                                </td>
                                                                <td style="border: none;"></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jndwryysno" name="jndwryysno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['jndwryysno']}"
                                                               readonly
                                                               value="${wfgeneralrequireprocessEntity.jndwryysno }"/><c:if
                                                            test="${requiredMap['jndwryysno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="jndwryysname" name="jndwryysname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jndwryysno']}"
                                                                value="${wfgeneralrequireprocessEntity.jndwryysname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfgeneralrequireprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;"
                                   onclick="canelTask('${wfgeneralrequireprocessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="onlyKchargeSignle" value="1" />
    <input type="hidden" id="dealfactoryid" value="${wfgeneralrequireprocessEntity.applyFactoryId}"/>
    <input type="hidden" id="dealdeptno" value="${wfgeneralrequireprocessEntity.applydepartno}"/>
    <input type="hidden" id="xmpg" name="xmpg" value="${wfgeneralrequireprocessEntity.xmpg}"/>
    <input type="hidden" id="attachids" name="attachids" value="${wfgeneralrequireprocessEntity.attachids }"/>
    <div id="win"></div>
    <div id="dlg"></div>
</form>
</div>
<script src='${ctx}/static/js/generalAffairs/wfgeneralrequireprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>