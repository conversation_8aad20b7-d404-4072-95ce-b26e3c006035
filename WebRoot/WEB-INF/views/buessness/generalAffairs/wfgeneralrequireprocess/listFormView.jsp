<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>庶務需求申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/wfgeneralrequireprocess/${action}" method="post">
  <input id="ids" name="ids" type="hidden" value="${wfgeneralrequireprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfgeneralrequireprocessEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">庶務需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfgeneralrequireprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfgeneralrequireprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfgeneralrequireprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfgeneralrequireprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfgeneralrequireprocessEntity.makerno}/${wfgeneralrequireprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
           <tr>
                <td>
                     <table class="formList">
                    	 <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">申請人工號</td>
                            <td width="5%" class="td_style2">
                                ${wfgeneralrequireprocessEntity.applyno}
                            </td>
                            <td width="5%">申請人</td>
                            <td width="5%" class="td_style2">
                                ${wfgeneralrequireprocessEntity.applyname }
                            </td>
                            <td width="5%">部門代碼</td>
                            <td width="5%" class="td_style2">
                               ${wfgeneralrequireprocessEntity.applydepartno}
                            </td>
                            <td width="5%">費用代碼</td>
                            <td width="5%" class="td_style2">
                                ${wfgeneralrequireprocessEntity.deptcostno}
                            </td>
                            
                            <td width="5%">所在廠區</td>
                            <td width="5%" class="td_style1">
                                <input id="applyFactoryId" name="applyFactoryId" class="easyui-combobox"
                                       panelHeight="auto" value="${wfgeneralrequireprocessEntity.applyFactoryId }" disabled
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applyFactoryId\',\'请选择所在廠區\']'"/>
                            </td>
                          </tr>
                          <tr align="center">  
                            <td>部門名稱</td>
                            <td colspan="5" class="td_style2">
                                ${wfgeneralrequireprocessEntity.applydepartname}
                            </td>
                            
                            <td>法人</td>
                            <td colspan="3" class="td_style1">
                                <input id="layperson" name="layperson" class="easyui-combobox"
                                       panelHeight="auto" value="${wfgeneralrequireprocessEntity.layperson }" disabled
                                       data-options="width: 425,required:true,validType:'comboxValidate[\'layperson\',\'请选择法人\']'"/>
                            </td>
                        </tr>
                        <tr align="center">
                        	<td>聯繫電話</td>
                            <td class="td_style2">
                                ${wfgeneralrequireprocessEntity.applytel}
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="5" class="td_style2">
                                ${wfgeneralrequireprocessEntity.applyemail }
                            </td>
                            <td>期望完工日期</td>
                            <td class="td_style2">
                            	<fmt:formatDate value='${wfgeneralrequireprocessEntity.wantDate}' pattern='yyyy-MM-dd'/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                        	<td>名稱</td>
                            <td colspan="4" class="td_style2">
                                ${wfgeneralrequireprocessEntity.title}
                            </td>
                            
                            <td>地點</td>
                            <td colspan="4" class="td_style2">
                                ${wfgeneralrequireprocessEntity.address}
                            </td>
                        </tr>
                       	<tr align="center">
                        	<td>需求類別</td>
                            <td colspan="9" class="td_style2" style="text-align: left;">
                                 <div class="requireTypeDiv"></div>
                                 <input id="requireType" name="requireType"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfgeneralrequireprocessEntity.requireType}"/>
                            </td>
                        </tr>
                       	<tr align="center">
                        	<td>需求內容</td>
                            <td colspan="9" class="td_style2" align="left">
                                ${wfgeneralrequireprocessEntity.details}
                            </td>
                        </tr>
                        <tr align="center">
                        	<td>項目評估</td>
                            <td colspan="9" class="td_style2" align="left">
                                ${wfgeneralrequireprocessEntity.xmpg}
                            </td>
                        </tr>
                    </table>
                </td>
           </tr>
            
                       <tr>
                        <td colspan="2">
                   		   <table class="formList">
                       <tr>
	                            <td colspan="5" class="td_style1">
	                            	議價信息
	                            </td>
	                        </tr>
	                        <tr align="center">
	                        	<td width="5%;">項次</td>
	                        	<td width="40%;">廠商</td>
	                        	<td width="15%;">報價金額（元）</td>
	                        	<td width="15%;">議價金額（元）</td>
	                        	<td width="15%;">建議選擇廠商</td>
	                        </tr>
                        	<c:if test="${not empty wfgeneralnegotiates}">
	                       		<c:forEach items="${wfgeneralnegotiates}" var="item" varStatus="rs">
	                       			<tr align="center" id="wfgeneralnegotiates${rs.index}" class="wfgeneralnegotiatesTr">
			                        	<td>${rs.index + 1}</td>
			                        	<td>
			                        		${item.manufacturer}
			                        	</td>
			                        	<td>
			                        		${item.quotationAmount}
			                        	</td>
			                        	<td>
			                        		${item.bargainingAmount}
			                        	</td>
			                        	<td>
			                        		<input type="radio" disabled="disabled" <c:if test="${item.suggest == '1'}">checked="checked"</c:if>/>
			                        	</td>
			                        </tr>
	                       		</c:forEach>
	                       	</c:if>
                         </table>
                        </td>
                       </tr>
                       
                       
                       <tr>
                        <td colspan="2">
                        <table class="formList">
                    	<tr>
	                            <td colspan="6" class="td_style1">
	                            	項目明細
	                            </td>
	                        </tr>
	                        <tr align="center">
	                        	<td width="5%;">項次</td>
	                        	<td width="40%;">項目</td>
	                        	<td width="10%;">單位</td>
	                        	<td width="10%;">數量</td>
	                        	<td width="10%;">單價（元）</td>
	                        	<td width="15%;">小計（元）</td>
	                        </tr>
                        	<c:if test="${not empty wfgeneralitems}">
                        		<c:set var="vitemCount" value="0"/>
	                       		<c:forEach items="${wfgeneralitems}" var="item" varStatus="rs">
	                       			<tr align="center" id="wfgeneralitems${rs.index}" class="wfgeneralitemsTr">
			                        	<td>${rs.index + 1}</td>
			                        	<td>
			                        		${item.itemname}
			                        	</td>
			                        	<td>
			                        		${item.units}
			                        	</td>
			                        	<td>
			                        		${item.nums}
			                        	</td>
			                        	<td>
			                        		${item.singleAmount}
			                        	</td>
			                        	<td>
			                        		<fmt:formatNumber type="number" value="${item.nums * item.singleAmount + 0.00001}" pattern="0.00" maxFractionDigits="2"/>
			                        	</td>
			                        </tr>
			                        <c:set var="vitemCount" value="${vitemCount + item.nums * item.singleAmount}"/>
	                       		</c:forEach>
	                       		<tr align="left" class="nottr">
		                            <td colspan="5" align="center">
		                               	 合計（未稅/元）
		                            </td>
		                            <td id="item_count_total" align="center">
		                                <fmt:formatNumber type="number" value="${vitemCount + 0.00001}" pattern="0.00" maxFractionDigits="2"/>
		                            </td>
		                        </tr>
	                       	</c:if>
                         </table>
                        </td>
                       </tr>
                        
                        
                        <tr>
                        <td colspan="2">
                        <table class="formList">
                        <tr>
	                            <td colspan="8" class="td_style1">
	                            	驗收確認
	                            </td>
	                        </tr>
	                        <tr align="center">
	                        	<td width="5%;">項次</td>
	                        	<td width="30%;">項目</td>
	                        	<td width="10%;">單位</td>
	                        	<td width="10%;">單價（元）</td>
	                        	<td width="10%;">預計數量</td>
	                        	<td width="10%;">實際數量</td>
	                        	<td width="10%;">數量差異</td>
	                        	<td width="10%;">小計（元）</td>
	                        </tr>
                       <c:if test="${not empty wfgeneralitemsconfirms}">
	                       		<c:set var="cvitemCount" value="0"/>
	                       		<c:forEach items="${wfgeneralitemsconfirms}" var="item" varStatus="rs">
	                       			<tr align="center" id="wfgeneralitemsconfirms${rs.index}" class="wfgeneralitemsconfirmsTr">
			                        	<td>${rs.index + 1}</td>
			                        	<td>
			                        		${item.itemname}
			                        	</td>
			                        	<td>
			                        		${item.units}
			                        	</td>
			                        	<td>
			                        		${item.singleAmount}
			                        	</td>
			                        	<td>
			                        		${item.nums}
			                        	</td>
			                        	<td>
			                        		${item.actualnums}
			                        	</td>
			                        	<td>
			                        		<fmt:formatNumber type="number" value="${item.nums - item.actualnums + 0.00001}" pattern="0.00" maxFractionDigits="2"/>
			                        	</td>
			                        	<td>
			                        	<fmt:formatNumber type="number" value="${item.actualnums * item.singleAmount + 0.00001}" pattern="0.00" maxFractionDigits="2"/>
			                        	</td>
			                        </tr>
			                        <c:set var="cvitemCount" value="${cvitemCount + item.actualnums * item.singleAmount}"/>
	                       		</c:forEach>
	                       		<tr align="left" class="nottr">
		                            <td colspan="7" align="center">
		                               	 合計（未稅/元）
		                            </td>
		                            <td align="center" id="citem_count_total">
		                            	<fmt:formatNumber type="number" value="${cvitemCount + 0.00001}" pattern="0.00" maxFractionDigits="2"/>
		                            </td>
		                        </tr>
	                       	</c:if>
                         </table>
                        </td>
                       </tr>
            <tr>
                <td>
                    <table class="formList">
                    	<c:if test="${not empty file}">
	                       <tr align="center">
	                            <td width="10%">附件</td>
	                            <td width="90%" class="td_style1">
	                                <div id="dowloadUrl">
	                                    <c:forEach items="${file}" varStatus="i" var="item">
	                                        <div id="${item.id}"
	                                             style="line-height:30px;margin-left:5px;" class="float_L">
	                                            <div class="float_L">
	                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
	                                            </div>
	                                        </div>
	                                    </c:forEach>
	                                </div>
	                            </td>
	                        </tr>
                        </c:if>
                    	
                    
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','庶務需求申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfgeneralrequireprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                  <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                      style="width: 100px;" onclick="printWindow_form('btnClose,btnPrint');">列印</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
<div id="dlg"></div>
<script src='${ctx}/static/js/generalAffairs/wfgeneralrequireprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>