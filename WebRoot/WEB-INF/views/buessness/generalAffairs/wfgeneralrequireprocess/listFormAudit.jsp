<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>庶務需求申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/wfgeneralrequireprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfgeneralrequireprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfgeneralrequireprocessEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">庶務需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfgeneralrequireprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfgeneralrequireprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfgeneralrequireprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfgeneralrequireprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfgeneralrequireprocessEntity.makerno}/${wfgeneralrequireprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
           <tr>
                <td>
                     <table class="formList">
                    	 <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">申請人工號</td>
                            <td width="5%" class="td_style2">${wfgeneralrequireprocessEntity.applyno}</td>
                            <td width="5%">申請人</td>
                            <td width="5%" class="td_style2">
                                ${wfgeneralrequireprocessEntity.applyname }
                            </td>
                            <td width="5%">部門代碼</td>
                            <td width="5%" class="td_style2">
                               ${wfgeneralrequireprocessEntity.applydepartno}
                            </td>
                            <td width="5%">費用代碼</td>
                            <td width="5%" class="td_style2">
                                ${wfgeneralrequireprocessEntity.deptcostno}
                            </td>
                            
                            <td width="5%">所在廠區</td>
                            <td width="5%" class="td_style1">
                                <input id="applyFactoryId" name="applyFactoryId" class="easyui-combobox"
                                       panelHeight="auto" value="${wfgeneralrequireprocessEntity.applyFactoryId }" disabled
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applyFactoryId\',\'请选择所在廠區\']'"/>
                            </td>
                          </tr>
                          <tr align="center">  
                            <td>部門名稱</td>
                            <td colspan="5" class="td_style2">
                                ${wfgeneralrequireprocessEntity.applydepartname}
                            </td>
                            
                            <td>法人</td>
                            <td colspan="3" class="td_style1">
                                <input id="layperson" name="layperson" class="easyui-combobox"
                                       panelHeight="auto" value="${wfgeneralrequireprocessEntity.layperson }" disabled
                                       data-options="width: 425,required:true,validType:'comboxValidate[\'layperson\',\'请选择法人\']'"/>
                            </td>
                        </tr>
                        <tr align="center">
                        	<td>聯繫電話</td>
                            <td class="td_style2">
                                ${wfgeneralrequireprocessEntity.applytel}
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="5" class="td_style2">
                                ${wfgeneralrequireprocessEntity.applyemail }
                            </td>
                            <td>期望完工日期</td>
                            <td class="td_style2">
                            	<fmt:formatDate value='${wfgeneralrequireprocessEntity.wantDate}' pattern='yyyy-MM-dd'/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                        	<td>名稱</td>
                            <td colspan="4" class="td_style2">
                                ${wfgeneralrequireprocessEntity.title}
                            </td>
                            
                            <td>地點</td>
                            <td colspan="4" class="td_style2">
                                ${wfgeneralrequireprocessEntity.address}
                            </td>
                        </tr>
                       	<tr align="center">
                        	<td>需求類別</td>
                            <td colspan="9" class="td_style2" style="text-align: left;">
                                 <div class="requireTypeDiv"></div>
                                 <input id="requireType" name="requireType"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfgeneralrequireprocessEntity.requireType}"/>
                            </td>
                        </tr>
                       	<tr align="center">
                        	<td>需求內容</td>
                            <td colspan="9" class="td_style2" align="left">
                                ${wfgeneralrequireprocessEntity.details}
                            </td>
                        </tr>
                        <c:if test="${nodeOrder >= 4}">
                        <c:choose>
	           			<c:when test="${not empty nodeName && '機能單位人員評估' eq nodeName}">
                       	<tr align="center">
                        	<td>項目評估<font color="red">*</font></td>
                            <td colspan="9" class="td_style1">
                                <textarea id="xmpg"
										name="xmpg" class="easyui-validatebox" 
										oninput="return LessThan(this);"
                                      	onchange="return LessThan(this);"
                                      	onpropertychange="return LessThan(this);"
                                      	maxlength="200"
										data-options="multiline:true,required:true,validType:'length[1,200]'"
										style="width:99%;height:60px;resize:none;" rows="5" cols="4">${wfgeneralrequireprocessEntity.xmpg}</textarea>
								<span id="txtNum"></span>
                            </td>
                        </tr>
                        </c:when>
                        <c:otherwise>
                        <c:if test="${not empty wfgeneralrequireprocessEntity.xmpg}">
                        <tr align="center">
                        	<td>項目評估</td>
                            <td colspan="9" class="td_style2" align="left">
                                ${wfgeneralrequireprocessEntity.xmpg}
                            </td>
                        </tr>
                        </c:if>
                        </c:otherwise>
                        </c:choose>
                        </c:if>
                    </table>
                </td>
           </tr>
            <tr>
                <td>
                     <table class="formList">
                       <c:if test="${nodeOrder >= 6}">
                       <tr>
                        <td colspan="2">
                   		   <table class="formList">
                       	<c:choose>
	           			<c:when test="${not empty nodeName && '庶務人員' eq nodeName}">
	                    	 <tr>
	                            <td colspan="6" class="td_style1">
	                            	議價信息
	                            </td>
	                        </tr>
	                        <tr align="center">
	                        	<td width="5%;">項次</td>
	                        	<td width="40%;">廠商</td>
	                        	<td width="15%;">報價金額（元）</td>
	                        	<td width="15%;">議價金額（元）</td>
	                        	<td width="15%;">建議選擇廠商</td>
	                        	<td width="10%;">操作</td>
	                        </tr>
	           				<c:choose>
	           					<c:when test="${not empty wfgeneralnegotiates}">
	           						<c:forEach items="${wfgeneralnegotiates}" var="item" varStatus="rs">
		                       			<tr align="center" id="wfgeneralnegotiates${rs.index}" class="wfgeneralnegotiatesTr">
				                        	<td>${rs.index + 1}</td>
				                        	<td>
				                        		<input id="negotiates_manufacturer${rs.index}" name="wfgeneralnegotiates[${rs.index}].manufacturer" class="easyui-validatebox" data-options="width: 300" value="${item.manufacturer}"/>
				                        	</td>
				                        	<td>
				                        		<input id="negotiates_quotationAmount${rs.index}" name="wfgeneralnegotiates[${rs.index}].quotationAmount" class="easyui-numberbox" data-options="width:100,min:0.01,precision:2" value="${item.quotationAmount}"/>
				                        	</td>
				                        	<td>
				                        		<input id="negotiates_bargainingAmount${rs.index}" name="wfgeneralnegotiates[${rs.index}].bargainingAmount" class="easyui-numberbox" data-options="width:100,min:0.01,precision:2" value="${item.bargainingAmount}"/>
				                        	</td>
				                        	<td>
				                        		<input type="radio" id="negotiates_suggest${rs.index}" name="wfgeneralnegotiates[${rs.index}].suggest" onclick="setSuggestManufacturer(${rs.index});" class="suggestradio" <c:if test="${item.suggest == '1'}">checked="checked"</c:if>/> 
				                        	</td>
				                        	<td>
				                        		<input type="image" src="${ctx}/static/images/deleteRow.png" onclick="negotiatesItemDeltr(${rs.index});return false;"/>
				                        	</td>
				                        </tr>
		                       		</c:forEach>
	           					</c:when>
	           					<c:otherwise>
		           					<tr align="center" id="wfgeneralnegotiates0" class="wfgeneralnegotiatesTr">
			                        	<td>1</td>
			                        	<td>
			                        		<input id="negotiates_manufacturer0" name="wfgeneralnegotiates[0].manufacturer" class="easyui-validatebox" data-options="width: 300"/>
			                        	</td>
			                        	<td>
			                        		<input id="negotiates_quotationAmount0" name="wfgeneralnegotiates[0].quotationAmount" class="easyui-numberbox" data-options="width:100,min:0.01,precision:2" />
			                        	</td>
			                        	<td>
			                        		<input id="negotiates_bargainingAmount0" name="wfgeneralnegotiates[0].bargainingAmount" class="easyui-numberbox" data-options="width:100,min:0.01,precision:2"/>
			                        	</td>
			                        	<td>
			                        		<input type="radio" id="negotiates_suggest0" name="wfgeneralnegotiates[0].suggest" onclick="setSuggestManufacturer(0);" class="suggestradio"/>
			                        	</td>
			                        	<td>
			                        		<input type="image" src="${ctx}/static/images/deleteRow.png" onclick="negotiatesItemDeltr(0);return false;"/>
			                        	</td>
			                        </tr>
	           					</c:otherwise>
	           				</c:choose>
	           				 <tr align="left" class="nottr">
	                            <td colspan="6" width="100%" style="text-align:left;padding-left:10px;">
	                                <input type="button" id="negotiatesItemAdd" style="width:100px;float:left;" value="添加一筆" />
	                            </td>
	                        </tr>
                        </c:when>
                        <c:otherwise>
	                    	 <tr>
	                            <td colspan="5" class="td_style1">
	                            	議價信息
	                            </td>
	                        </tr>
	                        <tr align="center">
	                        	<td width="5%;">項次</td>
	                        	<td width="40%;">廠商</td>
	                        	<td width="15%;">報價金額（元）</td>
	                        	<td width="15%;">議價金額（元）</td>
	                        	<td width="15%;">建議選擇廠商</td>
	                        </tr>
                        	<c:if test="${not empty wfgeneralnegotiates}">
	                       		<c:forEach items="${wfgeneralnegotiates}" var="item" varStatus="rs">
	                       			<tr align="center" id="wfgeneralnegotiates${rs.index}" class="wfgeneralnegotiatesTr">
			                        	<td>${rs.index + 1}</td>
			                        	<td>
			                        		${item.manufacturer}
			                        	</td>
			                        	<td>
			                        		${item.quotationAmount}
			                        	</td>
			                        	<td>
			                        		${item.bargainingAmount}
			                        	</td>
			                        	<td>
			                        		<input type="radio" disabled="disabled" <c:if test="${item.suggest == '1'}">checked="checked"</c:if>/>
			                        	</td>
			                        </tr>
	                       		</c:forEach>
	                       	</c:if>
                        </c:otherwise>
                        </c:choose>
                         </table>
                        </td>
                       </tr>
                       
                       
                       <tr>
                        <td colspan="2">
                        <table class="formList">
                    	 
                       	<c:choose>
	           			<c:when test="${not empty nodeName && '庶務人員' eq nodeName}">
	           				<tr>
	                            <td colspan="7" class="td_style1">
	                            	項目明細
	                            </td>
	                        </tr>
	                        <tr align="center">
	                        	<td width="5%;">項次</td>
	                        	<td width="40%;">項目&nbsp;<font color="red">*</font></td>
	                        	<td width="10%;">單位&nbsp;<font color="red">*</font></td>
	                        	<td width="10%;">數量&nbsp;<font color="red">*</font></td>
	                        	<td width="10%;">單價（元）<font color="red">*</font></td>
	                        	<td width="15%;">小計（元）</td>
	                        	<td width="10%;">操作</td>
	                        </tr>
	           				<c:choose>
	           					<c:when test="${not empty wfgeneralitems}">
	           						<c:set var="vitemCount" value="0"/>
	           						<c:forEach items="${wfgeneralitems}" var="item" varStatus="rs">
		                       			<tr align="center" id="wfgeneralitems${rs.index}" class="wfgeneralitemsTr">
				                        	<td>${rs.index + 1}</td>
				                        	<td>
				                        		<input id="items_itemname${rs.index}" name="wfgeneralitems[${rs.index}].itemname" class="easyui-validatebox" data-options="width: 300,required:true" value="${item.itemname}"/>
				                        	</td>
				                        	<td>
				                        		<input id="items_units${rs.index}" name="wfgeneralitems[${rs.index}].units" class="easyui-validatebox" data-options="width:100,required:true" value="${item.units}"/>
				                        	</td>
				                        	<td>
				                        		<input id="items_nums_${rs.index}" name="wfgeneralitems[${rs.index}].nums" class="easyui-numberbox" data-options="width:100,required:true,min:0.01,precision:2,onChange:countItem" value="${item.nums}"/>
				                        	</td>
				                        	<td>
				                        		<input id="items_singleAmount_${rs.index}" name="wfgeneralitems[${rs.index}].singleAmount" class="easyui-numberbox" data-options="width:80,required:true,min:0.01,precision:2,onChange:countItem" value="${item.singleAmount}"/>
				                        	</td>
				                        	<td id="items_count${rs.index}" class="itemCount">
				                        		<fmt:formatNumber type="number" value="${item.nums * item.singleAmount + 0.00001}" pattern="0.00" maxFractionDigits="2"/>
				                        	</td>
				                        	<td>
				                        		<input type="image" src="${ctx}/static/images/deleteRow.png" onclick="generalItemDeltr(${rs.index});return false;"/>
				                        	</td>
				                        </tr>
				                        <c:set var="vitemCount" value="${vitemCount + item.nums * item.singleAmount}"/>
		                       		</c:forEach>
	           					</c:when>
	           					<c:otherwise>
		           					<tr align="center" id="wfgeneralitems0" class="wfgeneralitemsTr">
			                        	<td>1</td>
			                        	<td>
			                        		<input id="items_itemname0" name="wfgeneralitems[0].itemname" class="easyui-validatebox" data-options="width: 300,required:true"/>
			                        	</td>
			                        	<td>
			                        		<input id="items_units0" name="wfgeneralitems[0].units" class="easyui-validatebox" data-options="width:100,required:true" />
			                        	</td>
			                        	<td>
			                        		<input id="items_nums_0" name="wfgeneralitems[0].nums" class="easyui-numberbox" data-options="width:100,required:true,min:0.01,precision:2,onChange:countItem" />
			                        	</td>
			                        	<td>
			                        		<input id="items_singleAmount_0" name="wfgeneralitems[0].singleAmount" class="easyui-numberbox" data-options="width:80,required:true,min:0.01,precision:2,onChange:countItem" />
			                        	</td>
			                        	<td id="items_count0" class="itemCount"></td>
			                        	<td>
			                        		<input type="image" src="${ctx}/static/images/deleteRow.png" onclick="generalItemDeltr(0);return false;"/>
			                        	</td>
			                        </tr>
	           					</c:otherwise>
	           				</c:choose>
	           				<tr align="left" class="nottr">
	                            <td colspan="5" align="center">
	                               	 合計（未稅/元）
	                            </td>
	                            <td colspan="2" id="item_count_total" align="center">
	                            	<fmt:formatNumber type="number" value="${vitemCount + 0.00001}" pattern="0.00" maxFractionDigits="2"/>
	                            </td>
	                        </tr>
	           				 <tr align="left" class="nottr">
	                            <td colspan="7" width="100%" style="text-align:left;padding-left:10px;">
	                                <input type="button" id="generalItemAdd" style="width:100px;float:left;" value="添加一筆" />
	                            </td>
	                        </tr>
                        </c:when>
                        <c:otherwise>
                        	<tr>
	                            <td colspan="6" class="td_style1">
	                            	項目明細
	                            </td>
	                        </tr>
	                        <tr align="center">
	                        	<td width="5%;">項次</td>
	                        	<td width="40%;">項目</td>
	                        	<td width="10%;">單位</td>
	                        	<td width="10%;">數量</td>
	                        	<td width="10%;">單價（元）</td>
	                        	<td width="15%;">小計（元）</td>
	                        </tr>
                        	<c:if test="${not empty wfgeneralitems}">
                        		<c:set var="vitemCount" value="0"/>
	                       		<c:forEach items="${wfgeneralitems}" var="item" varStatus="rs">
	                       			<tr align="center" id="wfgeneralitems${rs.index}" class="wfgeneralitemsTr">
			                        	<td>${rs.index + 1}</td>
			                        	<td>
			                        		${item.itemname}
			                        	</td>
			                        	<td>
			                        		${item.units}
			                        	</td>
			                        	<td>
			                        		${item.nums}
			                        	</td>
			                        	<td>
			                        		${item.singleAmount}
			                        	</td>
			                        	<td>
			                        		<fmt:formatNumber type="number" value="${item.nums * item.singleAmount + 0.00001}" pattern="0.00" maxFractionDigits="2"/>
			                        	</td>
			                        </tr>
			                        <c:set var="vitemCount" value="${vitemCount + item.nums * item.singleAmount}"/>
	                       		</c:forEach>
	                       		<tr align="left" class="nottr">
		                            <td colspan="5" align="center">
		                               	 合計（未稅/元）
		                            </td>
		                            <td id="item_count_total" align="center">
		                                <fmt:formatNumber type="number" value="${vitemCount + 0.00001}" pattern="0.00" maxFractionDigits="2"/>
		                            </td>
		                        </tr>
	                       	</c:if>
                        </c:otherwise>
                        </c:choose>
                         </table>
                        </td>
                       </tr>
                        </c:if>
                        
                        
                     <c:if test="${nodeOrder >= 19}">
                        <tr>
                        <td colspan="2">
                        <table class="formList">
                       	<c:choose>
	           			<c:when test="${not empty nodeName && '庶務人員驗收' eq nodeName}">
	           				<tr>
	                            <td colspan="9" class="td_style1">
	                            	驗收確認
	                            </td>
	                        </tr>
	                        <tr align="center">
	                        	<td width="5%;">項次</td>
	                        	<td width="30%;">項目&nbsp;<font color="red">*</font></td>
	                        	<td width="10%;">單位&nbsp;<font color="red">*</font></td>
	                        	<td width="10%;">單價（元）<font color="red">*</font></td>
	                        	<td width="10%;">預計數量&nbsp;<font color="red">*</font></td>
	                        	<td width="10%;">實際數量&nbsp;<font color="red">*</font></td>
	                        	<td width="10%;">數量差異</td>
	                        	<td width="10%;">小計（元）</td>
	                        	<td width="5%;">操作</td>
	                        </tr>
	           				<c:choose>
	           					<c:when test="${not empty wfgeneralitemsconfirms}">
	           						<c:set var="cvitemCount" value="0"/>
	           						<c:forEach items="${wfgeneralitemsconfirms}" var="item" varStatus="rs">
		                       			<tr align="center" id="wfgeneralitemsconfirms${rs.index}" class="wfgeneralitemsconfirmsTr">
				                        	<td>${rs.index + 1}</td>
				                        	<td>
				                        		<input id="citems_itemname${rs.index}" name="wfgeneralitemsconfirms[${rs.index}].itemname" class="easyui-validatebox" data-options="width: 300,required:true" value="${item.itemname}"/>
				                        	</td>
				                        	<td>
				                        		<input id="citems_units${rs.index}" name="wfgeneralitemsconfirms[${rs.index}].units" class="easyui-validatebox" data-options="width:100,required:true" value="${item.units}"/>
				                        	</td>
				                        	<td>
				                        		<input id="citems_singleAmount_${rs.index}" name="wfgeneralitemsconfirms[${rs.index}].singleAmount" class="easyui-numberbox" data-options="width:80,required:true,min:0.01,precision:2,onChange:countCItem" value="${item.singleAmount}"/>
				                        	</td>
				                        	<td>
				                        		<input id="citems_nums_${rs.index}" name="wfgeneralitemsconfirms[${rs.index}].nums" class="easyui-numberbox" data-options="width:100,required:true,min:0.01,precision:2,onChange:countCItem" value="${item.nums}"/>
				                        	</td>
				                        	<td>
				                        		<input id="citems_actualnums_${rs.index}" name="wfgeneralitemsconfirms[${rs.index}].actualnums" class="easyui-numberbox" data-options="width:100,required:true,min:0.01,precision:2,onChange:countCItem" value="${item.actualnums}"/>
				                        	</td>
				                        	<td id="citems_dif${rs.index}" class="cItemDif">${item.nums - item.actualnums}</td>
				                        	<td id="citems_count${rs.index}" class="cItemCount">
				                        		<fmt:formatNumber type="number" value="${item.actualnums * item.singleAmount + 0.00001}" pattern="0.00" maxFractionDigits="2"/>
				                        	</td>
				                        	<td>
				                        		<input type="image" src="${ctx}/static/images/deleteRow.png" onclick="generalItemConfirmDeltr(${rs.index});return false;"/>
				                        	</td>
				                        </tr>
				                        <c:set var="cvitemCount" value="${cvitemCount + item.actualnums * item.singleAmount}"/>
		                       		</c:forEach>
	           					</c:when>
	           					<c:otherwise>
		           					<tr align="center" id="wfgeneralitemsconfirms0" class="wfgeneralitemsconfirmsTr">
			                        	<td>1</td>
			                        	<td>
			                        		<input id="citems_itemname0" name="wfgeneralitemsconfirms[0].itemname" class="easyui-validatebox" data-options="width: 300,required:true"/>
			                        	</td>
			                        	<td>
			                        		<input id="citems_units0" name="wfgeneralitemsconfirms[0].units" class="easyui-validatebox" data-options="width:100,required:true" />
			                        	</td>
			                        	<td>
			                        		<input id="citems_singleAmount_0" name="wfgeneralitemsconfirms[0].singleAmount" class="easyui-numberbox" data-options="width:80,required:true,min:0.01,precision:2,onChange:countCItem" />
			                        	</td>
			                        	<td>
			                        		<input id="citems_nums_0" name="wfgeneralitemsconfirms[0].nums" class="easyui-numberbox" data-options="width:100,required:true,min:0.01,precision:2,onChange:countCItem" />
			                        	</td>
			                        	<td>
			                        		<input id="citems_actualnums_0" name="wfgeneralitemsconfirms[0].actualnums" class="easyui-numberbox" data-options="width:100,required:true,min:0.01,precision:2,onChange:countCItem" />
			                        	</td>
			                        	<td id="citems_dif0" class="cItemDif"></td>
			                        	<td id="citems_count0" class="cItemCount">
			                        	</td>
			                        	<td>
			                        		<input type="image" src="${ctx}/static/images/deleteRow.png" onclick="generalItemConfirmDeltr(0);return false;"/>
			                        	</td>
			                        </tr>
	           					</c:otherwise>
	           				</c:choose>
	           				<tr align="left" class="nottr">
	                            <td colspan="7" align="center">
	                               	 合計（未稅/元）
	                            </td>
	                            <td colspan="2" align="center" id="citem_count_total">
	                            	<fmt:formatNumber type="number" value="${cvitemCount + 0.00001}" pattern="0.00" maxFractionDigits="2"/>
	                            </td>
	                        </tr>
	           				 <tr align="left" class="nottr">
	                            <td colspan="9" width="100%" style="text-align:left;padding-left:10px;">
	                                <input type="button" id="generalItemConfirmAdd" style="width:100px;float:left;" value="添加一筆" />
	                            </td>
	                        </tr>
                        </c:when>
                        <c:otherwise>
                        	<tr>
	                            <td colspan="8" class="td_style1">
	                            	驗收確認
	                            </td>
	                        </tr>
	                        <tr align="center">
	                        	<td width="5%;">項次</td>
	                        	<td width="30%;">項目</td>
	                        	<td width="10%;">單位</td>
	                        	<td width="10%;">單價（元）</td>
	                        	<td width="10%;">預計數量</td>
	                        	<td width="10%;">實際數量</td>
	                        	<td width="10%;">數量差異</td>
	                        	<td width="10%;">小計（元）</td>
	                        </tr>
                        	<c:if test="${not empty wfgeneralitemsconfirms}">
	                       		<c:set var="cvitemCount" value="0"/>
	                       		<c:forEach items="${wfgeneralitemsconfirms}" var="item" varStatus="rs">
	                       			<tr align="center" id="wfgeneralitemsconfirms${rs.index}" class="wfgeneralitemsconfirmsTr">
			                        	<td>${rs.index + 1}</td>
			                        	<td>
			                        		${item.itemname}
			                        	</td>
			                        	<td>
			                        		${item.units}
			                        	</td>
			                        	<td>
			                        		${item.singleAmount}
			                        	</td>
			                        	<td>
			                        		${item.nums}
			                        	</td>
			                        	<td>
			                        		${item.actualnums}
			                        	</td>
			                        	<td>
			                        	 <fmt:formatNumber type="number" value="${item.nums - item.actualnums + 0.00001}" pattern="0.00" maxFractionDigits="2"/>
			                        	</td>
			                        	<td>
			                        	<fmt:formatNumber type="number" value="${item.actualnums * item.singleAmount + 0.00001}" pattern="0.00" maxFractionDigits="2"/>
			                        	</td>
			                        </tr>
			                        <c:set var="cvitemCount" value="${cvitemCount + item.actualnums * item.singleAmount}"/>
	                       		</c:forEach>
	                       		<tr align="left" class="nottr">
		                            <td colspan="7" align="center">
		                               	 合計（未稅/元）
		                            </td>
		                            <td align="center" id="citem_count_total">
		                            	<fmt:formatNumber type="number" value="${cvitemCount + 0.00001}" pattern="0.00" maxFractionDigits="2"/>
		                            </td>
		                        </tr>
	                       	</c:if>
                        </c:otherwise>
                        </c:choose>
                         </table>
                        </td>
                       </tr>
                        </c:if>
                        
                     <c:choose>
	           			<c:when test="${not empty nodeName && '庶務人員' eq nodeName}">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
								<span class="sl-custom-file">
									<input type="button" value="点击上传文件" class="btn-file"/>
									<input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
								<input type="hidden" id="attachids" name="attachids" value="${wfgeneralrequireprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        </c:when>
                        <c:otherwise>
                        	<c:if test="${nodeOrder >= 6 && not empty file}">
	                       <tr align="center">
	                            <td width="10%">附件</td>
	                            <td width="90%" class="td_style1">
	                                <div>
	                                    <c:forEach items="${file}" varStatus="i" var="item">
	                                        <div id="${item.id}"
	                                             style="line-height:30px;margin-left:5px;" class="float_L">
	                                            <div class="float_L">
	                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
	                                            </div>
	                                        </div>
	                                    </c:forEach>
	                                </div>
	                            </td>
	                        </tr>
	                        </c:if>
                        </c:otherwise>
                        </c:choose>
                        
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%">
						<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                	perCall="audiPrValid"
                                            serialNo="${wfgeneralrequireprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','庶務需求申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfgeneralrequireprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
  <div id="dlg"></div>
  <input type="hidden" id="currentNodeOrder" value="${nodeOrder}" />
<script src='${ctx}/static/js/generalAffairs/wfgeneralrequireprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>