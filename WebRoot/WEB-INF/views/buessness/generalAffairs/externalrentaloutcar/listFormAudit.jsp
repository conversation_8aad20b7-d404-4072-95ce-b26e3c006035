<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>外租用車申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/externalrentaloutcar/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${externalRentalOutCarEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${externalRentalOutCarEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">外租用車申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${externalRentalOutCarEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${externalRentalOutCarEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${externalRentalOutCarEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${externalRentalOutCarEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${externalRentalOutCarEntity.makerno}/${externalRentalOutCarEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
           <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號</td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 150,disabled:true"
                                       value="${externalRentalOutCarEntity.applyno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${externalRentalOutCarEntity.applyname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${externalRentalOutCarEntity.applydeptno }"/>
                            </td>
                            <td width="3%">費用代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applycostno" name="externalRentalOutCarEntity.applycostno"
                                       class="easyui-validatebox" data-options="width: 120,disabled:true"
                                       value="${externalRentalOutCarEntity.applycostno }"/>
                            </td>
                            <td width="4%">所在廠區</td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${externalRentalOutCarEntity.applyfactoryid }"
                                       data-options="width: 120,validType:'comboxValidate[\'applyfactoryid\',\'请选择所在廠區\']',disabled:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox"
                                       style="width:90%;" data-options="disabled:true"
                                       value="${externalRentalOutCarEntity.applydeptname }"/>
                            </td>
                            <td>申請日期</td>
                            <td colspan="3" class="td_style1">
                                <input type="text" id="createtime" name="createtime" class="easyui-validatebox inputCss"
                                       data-options="width: 150" readonly value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${externalRentalOutCarEntity.createtime}"/>"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫電話</td>
                            <td class="td_style1">
                                <input id="applytel" name="applytel" class="easyui-validatebox"
                                       data-options="width: 150,validType:'tel[\'applytel\',\'分機格式不正確\']',disabled:true"
                                       value="${externalRentalOutCarEntity.applytel }"/>
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="7" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       data-options="width: 450,validType:'email[\'applyemail\',\'郵箱的格式不正確\']',disabled:true"
                                       value="${externalRentalOutCarEntity.applyemail }"/>
                            </td>
                        </tr>
                    </table>
                </td>
           </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">專案用車名稱</td>
                            <td colspan="9" class="td_style1">
                                <input id="nameOfSpecial" name="nameOfSpecial" class="easyui-validatebox" data-options="disabled:true"
                                       style="width:90%;" oninput="return LessThanTWO(this);"
                                       onchange="return LessThanTWO(this);" maxlength="30"
                                       value="${externalRentalOutCarEntity.nameOfSpecial }"/><span id="nameOfSpecial30"></span>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>用車事由</td>
                            <td colspan="9" class="td_style1">
                                <textarea id="theCarFor" name="theCarFor"
                                          class="easyui-validatebox"
                                          oninput="return LessThan(this);"
                                          onchange="return LessThan(this);"
                                          onpropertychange="return LessThan(this);"
                                          maxlength="300"
                                          style="width:90%;height:80px;"
                                          rows="5" cols="6"
                                          data-options="disabled:true">${externalRentalOutCarEntity.theCarFor }</textarea><span
                                    id="txtNum"></span></td>
                            </td>
                        </tr>
                        <tr align="center">
                            <td rowspan="4">用車說明</td>
                            <td>車型要求</td>
                            <td class="td_style1">
                                <input id="modelsFor" name="modelsFor" class="easyui-validatebox"
                                       data-options="width: 150,disabled:true"
                                       value="${externalRentalOutCarEntity.modelsFor }"/>
                            </td>
                            <td>數量</td>
                            <td class="td_style1">
                                <input id="numberOfBus" name="numberOfBus" class="easyui-validatebox"
                                       data-options="width: 150,disabled:true"
                                       value="${externalRentalOutCarEntity.numberOfBus }"/>
                            </td>
                            <td>負責人</td>
                            <td class="td_style1">
                                <input id="principal" name="principal" class="easyui-validatebox"
                                       data-options="width: 150,disabled:true"
                                       value="${externalRentalOutCarEntity.principal }"/>
                            </td>
                            <td>聯繫方式</td>
                            <td class="td_style1">
                                <input id="contactInformation" name="contactInformation" class="easyui-validatebox"
                                       data-options="width: 150,disabled:true"
                                       value="${externalRentalOutCarEntity.contactInformation }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>用車時間</td>
                            <td colspan="7" align="center">
                                <input id="transportStartTime" name="transportStartTime" class="easyui-validatebox Wdate"
                                       data-options="width:200,prompt:'请选择开始时间',disabled:true" style="width:180px"
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd HH:mm:ss"
									value="${externalRentalOutCarEntity.transportStartTime}"/>"
                                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'%y-%M-%d %H:%m:%s'})"/>
                                ~<input id="transportEndTime" name="transportEndTime" class="easyui-validatebox Wdate"
                                        data-options="width:200,prompt:'请选择結束时间',disabled:true" style="width:180px"
                                        value="<fmt:formatDate  pattern="yyyy-MM-dd HH:mm:ss"
									value="${externalRentalOutCarEntity.transportEndTime}"/>"
                                        onclick="WdatePicker({minDate:'#F{$dp.$D(\'transportStartTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>
                                行車路線<br>
                                （詳細、準確）
                            </td>
                            <td class="td_style1" colspan="7">
                                            <textarea id="driveCircuit" name="driveCircuit"
                                                      class="easyui-validatebox"
                                                      oninput="return LessThanTWO(this);"
                                                      onchange="return LessThanTWO(this);"
                                                      onpropertychange="return LessThan(this);"
                                                      maxlength="200"
                                                      style="width:87%;height:80px;"
                                                      rows="5" cols="6"
                                                      data-options="disabled:true">${externalRentalOutCarEntity.driveCircuit }</textarea><span
                                    id="driveCircuit200"></span>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>特別要求</td>
                            <td class="td_style1" colspan="7">
                                <input id="specialRequests" name="specialRequests" class="easyui-validatebox"
                                       oninput="return LessThanTWO(this);"
                                       onchange="return LessThanTWO(this);" maxlength="100"
                                       style="width:87%;" data-options="disabled:true"
                                       value="${externalRentalOutCarEntity.specialRequests }"/><span
                                    id="specialRequests100"></span>
                            </td>
                        </tr>
                       <%-- <tr>
                            <td colspan="9">
                                <table style="width: 100%" class="formList">
                                    <tr align="center">


                                    </tr>
                                    <tr align="center">

                                    </tr>
                                    <tr align="center">

                                    </tr>
                                    <tr align="center">

                                    </tr>
                                </table>
                            </td>
                        </tr>--%>
                    </table>
                </td>
           </tr>
            <tr>
                <td>
                     <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td colspan="9" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${externalRentalOutCarEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                         <c:if test="${not empty nodeName&&'車調中心評估' eq nodeName}">
                             <tr align="center">
                                 <td>費用預算<font color="red">*</font></td>
                                 <td colspan="9" class="td_style1">
                                     <input id="costBugdet" class="easyui-validatebox" value="" style="margin-left: 10px;"
                                            data-options="width:160,required:true" name="costBugdet" value="${externalRentalOutCarEntity.costBugdet }"/>&nbsp;RMB
                                 </td>
                             </tr>
                         </c:if>
                         <c:if test="${nodeOrder ge 6}">
                             <tr align="center">
                                 <td>費用預算<font color="red">*</font></td>
                                 <td colspan="9" class="td_style1">
                                     <input class="easyui-validatebox" style="margin-left: 10px;"
                                            data-options="width:160,disabled:true" name="costBugdet" value="${externalRentalOutCarEntity.costBugdet }"/>&nbsp;RMB
                                 </td>
                             </tr>
                         </c:if>
                         <c:if test="${not empty nodeName&&'車調中心銷單' eq nodeName}">
                             <tr align="center">
                                 <td>行車稽查&nbsp;<font color="red">*</font></td>
                                 <td class="td_style1" colspan="3">
                                     <input id="drivingInspection" name="drivingInspection" class="easyui-validatebox"
                                            data-options="width: 150,required:true"
                                            value="${externalRentalOutCarEntity.drivingInspection }"/>
                                 </td>
                                 <td>實際費用&nbsp;<font color="red">*</font></td></td>
                                 <td class="td_style1" colspan="5">
                                     <input id="actualCosts" name="actualCosts" class="easyui-validatebox"
                                            data-options="width: 450,required:true"
                                            value="${externalRentalOutCarEntity.actualCosts }"/>
                                 </td>

                             </tr>
                         </c:if>
                         <c:if test="${nodeOrder ge 12}">
                             <tr align="center">
                                 <td>行車稽查</td>
                                 <td class="td_style1" colspan="3">
                                     <input name="drivingInspection" class="easyui-validatebox"
                                            data-options="width: 150,disabled:true"
                                            value="${externalRentalOutCarEntity.drivingInspection }"/>
                                 </td>
                                 <td>實際費用</td></td>
                                 <td class="td_style1" colspan="5">
                                     <input name="actualCosts" class="easyui-validatebox"
                                            data-options="width: 450,disabled:true"
                                            value="${externalRentalOutCarEntity.actualCosts }"/>
                                 </td>

                             </tr>
                         </c:if>
                         <tr align="center">
                             <td>備註</td>
                             <td align="left" colspan="7" style="padding-top: 10px;padding-left: 25px;padding-bottom: 10px">
                                 1.用車部門至少提前兩周提交有效申請單至車調中心<br><br>
                                 2.太原周邊車調中心聯繫電話：565+63304
                             </td>
                         </tr>
                        <tr align="center">
                            <td>批註</td>
                            <td colspan="9">
						<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${externalRentalOutCarEntity.serialno}" perCall="audiPrValid(${nodeOrder})"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','外租用車申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${externalRentalOutCarEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
  <div id="dlg"></div>
<script src='${ctx}/static/js/generalAffairs/externalrentaloutcar.js?random=<%= Math.random()%>'></script>
</body>
</html>