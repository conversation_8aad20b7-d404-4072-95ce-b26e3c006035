<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>外租用車申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
            type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/externalrentaloutcar/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${externalRentalOutCarEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${externalRentalOutCarEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${externalRentalOutCarEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${externalRentalOutCarEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${externalRentalOutCarEntity.makerdeptno }"/>
    <input id="affiliationFactoryId" name="affiliationFactoryId" type="hidden" value="${externalRentalInCarEntity.affiliationFactoryId }"/>
    <div class="commonW">
        <div class="headTitle">外租用車申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${externalRentalOutCarEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${externalRentalOutCarEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${externalRentalOutCarEntity.createDate==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${externalRentalOutCarEntity.createDate}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty externalRentalOutCarEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty externalRentalOutCarEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${externalRentalOutCarEntity.makerno}/${externalRentalOutCarEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 150,required:true"
                                       value="${externalRentalOutCarEntity.applyno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${externalRentalOutCarEntity.applyname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${externalRentalOutCarEntity.applydeptno }"/>
                            </td>
                            <td width="3%">費用代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applycostno" name="applycostno"
                                       class="easyui-validatebox inputCss" data-options="width: 120"
                                       value="${externalRentalOutCarEntity.applycostno }"/>
                            </td>
                            <td width="4%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${externalRentalOutCarEntity.applyfactoryid }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applyfactoryid\',\'请选择所在廠區\']'"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox"
                                       style="width:90%;"
                                       value="${externalRentalOutCarEntity.applydeptname }"/>
                            </td>
                            <td>申請日期</td>
                            <td colspan="3" class="td_style1">
                                <input type="text" id="createtime" name="createtime" class="easyui-validatebox inputCss"
                                       data-options="width: 150" readonly value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${externalRentalOutCarEntity.createtime}"/>"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>
                                聯繫電話&nbsp;<font color="red">*</font>
                            </td>
                            <td class="td_style1">
                                <input id="applytel" name="applytel" class="easyui-validatebox"
                                       data-options="width: 150,required:true,validType:'tel[\'applytel\',\'分機格式不正確\']'"
                                       value="${externalRentalOutCarEntity.applytel }"/>
                            </td>
                            <td>
                                聯繫郵箱&nbsp;<font color="red">*</font>
                            </td>
                            <td colspan="7" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       data-options="width: 450,required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"
                                       value="${externalRentalOutCarEntity.applyemail }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">專案用車名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1">
                                <input id="nameOfSpecial" name="nameOfSpecial" class="easyui-validatebox" data-options="required:true"
                                       style="width:90%;" oninput="return LessThanTWO(this);"
                                       onchange="return LessThanTWO(this);" maxlength="30"
                                       value="${externalRentalOutCarEntity.nameOfSpecial }"/><span id="nameOfSpecial30"></span>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>用車事由&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1">
                                <textarea id="theCarFor" name="theCarFor"
                                          class="easyui-validatebox"
                                          oninput="return LessThan(this);"
                                          onchange="return LessThan(this);"
                                          onpropertychange="return LessThan(this);"
                                          maxlength="300"
                                          style="width:90%;height:80px;" data-options="required:true"
                                          rows="5" cols="6"
                                          data-options="required:true,validType:'length[0,300]'">${externalRentalOutCarEntity.theCarFor }</textarea><span
                                    id="txtNum"></span></td>
                            </td>
                        </tr>
                        <tr align="center">
                            <td rowspan="7">用車說明</td>
                            <td colspan="9">
                                    <table style="width: 100%" class="formList">
                                        <tr align="center">
                                            <td>車型要求&nbsp;<font color="red">*</font></td>
                                            <td class="td_style1">
                                                <input id="modelsFor" name="modelsFor" class="easyui-validatebox"
                                                       data-options="width: 150,required:true"
                                                       value="${externalRentalOutCarEntity.modelsFor }"/>
                                            </td>
                                            <td>數量&nbsp;<font color="red">*</font></td>
                                            <td class="td_style1">
                                                <input id="numberOfBus" name="numberOfBus" class="easyui-validatebox"
                                                       data-options="width: 150,required:true"
                                                       value="${externalRentalOutCarEntity.numberOfBus }"/>
                                            </td>
                                            <td>負責人&nbsp;<font color="red">*</font></td>
                                            <td class="td_style1">
                                                <input id="principal" name="principal" class="easyui-validatebox"
                                                       data-options="width: 150,required:true"
                                                       value="${externalRentalOutCarEntity.principal }"/>
                                            </td>
                                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                                            <td class="td_style1">
                                                <input id="contactInformation" name="contactInformation" class="easyui-validatebox"
                                                       data-options="width: 150,required:true"
                                                       value="${externalRentalOutCarEntity.contactInformation }"/>
                                            </td>

                                        </tr>
                                        <tr align="center">
                                            <td>用車時間&nbsp;<font color="red">*</font></td>
                                            <td colspan="7" align="center">
                                                <input id="transportStartTime" name="transportStartTime" class="easyui-validatebox Wdate"
                                                       data-options="width:200,required:true,prompt:'请选择开始时间'" style="width:180px"
                                                       value="<fmt:formatDate  pattern="yyyy-MM-dd HH:mm:ss"
									value="${externalRentalOutCarEntity.transportStartTime}"/>"
                                                       onclick="WdatePicker({onpicked:function(){transportEndTime.click()},doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'%y-%M-%d %H:%m:%s'})"/>
                                                ~<input id="transportEndTime" name="transportEndTime" class="easyui-validatebox Wdate"
                                                        data-options="width:200,required:true,prompt:'请选择結束时间'" style="width:180px"
                                                        value="<fmt:formatDate  pattern="yyyy-MM-dd HH:mm:ss"
									value="${externalRentalOutCarEntity.transportEndTime}"/>"
                                                        onclick="WdatePicker({doubleCalendar:true,skin:'whyGreen',minDate:'#F{$dp.$D(\'transportStartTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})"/>
                                            </td>
                                        </tr>
                                        <tr align="center">
                                            <td>
                                                行車路線<br>
                                                （詳細、準確）<font color="red">*</font>
                                            </td>
                                            <td class="td_style1" colspan="7">
                                            <textarea id="driveCircuit" name="driveCircuit"
                                                      class="easyui-validatebox"
                                                      oninput="return LessThanTWO(this);"
                                                      onchange="return LessThanTWO(this);"
                                                      onpropertychange="return LessThan(this);"
                                                      maxlength="200"
                                                      style="width:87%;height:80px;" data-options="required:true"
                                                      rows="5" cols="6"
                                                      data-options="required:true,validType:'length[0,200]'">${externalRentalOutCarEntity.driveCircuit }</textarea><span
                                                    id="driveCircuit200"></span>
                                            </td>
                                        </tr>
                                        <tr align="center">
                                            <td>特別要求</td>
                                            <td class="td_style1" colspan="7">
                                                <input id="specialRequests" name="specialRequests" class="easyui-validatebox"
                                                       oninput="return LessThanTWO(this);"
                                                       onchange="return LessThanTWO(this);" maxlength="100"
                                                       style="width:87%;"
                                                       value="${externalRentalOutCarEntity.specialRequests }"/><span
                                                    id="specialRequests100"></span>
                                            </td>
                                        </tr>
                                    </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button"
                                                                   value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span> <input type="hidden" id="attachids"
                                               name="attachids" value="${externalRentalOutCarEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                        <tr align="center">
                            <td>備註</td>
                            <td align="left" colspan="7" style="padding-top: 10px;padding-left: 25px;padding-bottom: 10px">
                                1.用車部門至少提前兩周提交有效申請單至車調中心<br><br>
                                2.太原周邊車調中心聯繫電話：565+63304
                            </td>
                        </tr>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_waizuyongcheshenqingdan','外租用車申請單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" id="auditTable"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#affiliationFactoryId').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${externalRentalOutCarEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${externalRentalOutCarEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#affiliationFactoryId').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${externalRentalOutCarEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${externalRentalOutCarEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'cchargeno','cchargename',$('#affiliationFactoryId').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${externalRentalOutCarEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${externalRentalOutCarEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRoleOut($('#applydeptno').val(),'zchargeno','zchargename',$('#affiliationFactoryId').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${externalRentalOutCarEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${externalRentalOutCarEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cdzxchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">車調中心評估</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(76,'cdzxchargeno','cdzxchargename',$('input[name=\'applyfactoryid\']').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cdzxchargeno" name="cdzxchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cdzxchargeno']}"
                                                               readonly
                                                               value="${externalRentalOutCarEntity.cdzxchargeno }"/><c:if
                                                            test="${requiredMap['cdzxchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cdzxchargename" name="cdzxchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cdzxchargeno']}"
                                                                value="${externalRentalOutCarEntity.cdzxchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cdshchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">車調中心主管審核</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(275,'cdshchargeno','cdshchargename',$('input[name=\'applyfactoryid\']').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cdshchargeno" name="cdshchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cdshchargeno']}"
                                                               readonly
                                                               value="${externalRentalOutCarEntity.cdshchargeno }"/><c:if
                                                            test="${requiredMap['cdshchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cdshchargename" name="cdshchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cdshchargeno']}"
                                                                value="${externalRentalOutCarEntity.cdshchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="ghjgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">工會經管審核&nbsp;<a
                                                                        href="javascript:void(0);" onclick="addGhHq();">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ghjgchargeno" name="ghjgchargeno"
                                                               class="easyui-validatebox" onblur='getUserNameByEmpno(this);'
                                                               data-options="width:80,required:${requiredMap['ghjgchargeno']}"
                                                               value="${externalRentalOutCarEntity.ghjgchargeno }"/><c:if
                                                            test="${requiredMap['ghjgchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ghjgchargename" name="ghjgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ghjgchargeno']}"
                                                                value="${externalRentalOutCarEntity.ghjgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="jgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">經管審核&nbsp;<a href="javascript:void(0);" onclick="addGhShHq();">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jgchargeno" name="jgchargeno"
                                                               class="easyui-validatebox" onblur='getUserNameByEmpno(this);'
                                                               data-options="width:80,required:${requiredMap['jgchargeno']}"
                                                               value="${externalRentalOutCarEntity.jgchargeno }"/><c:if
                                                            test="${requiredMap['jgchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="jgchargename" name="jgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jgchargeno']}"
                                                                value="${externalRentalOutCarEntity.jgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#applydeptno').val(),'zcchargeno','zcchargename',$('#affiliationFactoryId').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${externalRentalOutCarEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${externalRentalOutCarEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產品處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#applydeptno').val(),'pcchargeno','pcchargename',$('#affiliationFactoryId').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${externalRentalOutCarEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${externalRentalOutCarEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>



                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cdxdchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">車調中心銷單</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(78,'cdxdchargeno','cdxdchargename',$('input[name=\'applyfactoryid\']').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cdxdchargeno" name="cdxdchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cdxdchargeno']}"
                                                               readonly
                                                               value="${externalRentalOutCarEntity.cdxdchargeno }"/><c:if
                                                            test="${requiredMap['cdxdchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cdxdchargename" name="cdxdchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cdxdchargeno']}"
                                                                value="${externalRentalOutCarEntity.cdxdchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cdxdshchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">車調中心銷單審核
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(79,'cdxdshchargeno','cdxdshchargename',$('input[name=\'applyfactoryid\']').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cdxdshchargeno" name="cdxdshchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cdxdshchargeno']}"
                                                               readonly
                                                               value="${externalRentalOutCarEntity.cdxdshchargeno }"/><c:if
                                                            test="${requiredMap['cdxdshchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cdxdshchargename" name="cdxdshchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cdxdshchargeno']}"
                                                                value="${externalRentalOutCarEntity.cdxdshchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cdxdhzchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">車調中心銷單核准
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(80,'cdxdhzchargeno','cdxdhzchargename',$('input[name=\'applyfactoryid\']').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cdxdhzchargeno" name="cdxdhzchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cdxdhzchargeno']}"
                                                               readonly
                                                               value="${externalRentalOutCarEntity.cdxdhzchargeno }"/><c:if
                                                            test="${requiredMap['cdxdhzchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cdxdhzchargename" name="cdxdhzchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cdxdhzchargeno']}"
                                                                value="${externalRentalOutCarEntity.cdxdhzchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">申請人確認</td>
                                                                <%--<td style="border: none;">--%>
                                                                    <%--<div class="float_L qhUserIcon"--%>
                                                                         <%--onclick="selectRole($('#applyno').val(),'apcchargeno','apcchargename',$('#applyfactoryid').val())"></div>--%>
                                                                <%--</td>--%>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="apcchargeno" name="apcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['apcchargeno']}"
                                                               readonly
                                                               value="${externalRentalOutCarEntity.apcchargeno }"/><c:if
                                                            test="${requiredMap['apcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="apcchargename" name="apcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['apcchargeno']}"
                                                                value="${externalRentalOutCarEntity.apcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">申請人主管確認</td>
                                                                <%--<td style="border: none;">--%>
                                                                    <%--<div class="float_L qhUserIcon"--%>
                                                                         <%--onclick="selectRole($('#applyno').val(),'apzchargeno','apzchargename',$('#applyfactoryid').val())"></div>--%>
                                                                <%--</td>--%>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="apzchargeno" name="apzchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['apzchargeno']}"
                                                               readonly
                                                               value="${externalRentalOutCarEntity.apzchargeno }"/><c:if
                                                            test="${requiredMap['apzchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="apzchargename" name="apzchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['apzchargeno']}"
                                                                value="${externalRentalOutCarEntity.apzchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
</div>
<script src='${ctx}/static/js/generalAffairs/externalrentaloutcar.js?random=<%= Math.random()%>'></script>
<script type="text/javascript">
    //設置申請時間
    setApplyDate();
    function setApplyDate() {
        var myDate = new Date();
        var year = myDate.getFullYear(); //获取完整的年份
        var month = myDate.getMonth() + 1; //获取当前月份(0-11,0代表1月)
        if (month < 10) {
            month = "0" + month;
        }
        var day = myDate.getDate(); //获取当前日(1-31)
        if (day < 10) {
            day = "0" + day;
        }
        var sysdate = year + "-" + month + "-" + day;
        $("input[name='createtime']").val(sysdate);
    }
</script>
</body>
</html>