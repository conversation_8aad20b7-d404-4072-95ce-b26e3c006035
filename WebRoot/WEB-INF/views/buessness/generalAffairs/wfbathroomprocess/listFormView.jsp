<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>洗手間零星維修申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
            color: black;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfbathroomprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfbathroomprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfbathroomprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">洗手間/直飲機維修申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfbathroomprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfbathroomprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfbathroomprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfbathroomprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfbathroomprocessEntity.makerno}/${wfbathroomprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">申請人工號</td>
                            <td width="10%" class="td_style2">${wfbathroomprocessEntity.applyno}</td>
                            <td width="8%">申請人</td>
                            <td width="10%" class="td_style2">${wfbathroomprocessEntity.applyname}</td>
                            <td width="8%">單位代碼</td>
                            <td width="10%" class="td_style2">${wfbathroomprocessEntity.applydeptno}</td>
                            <td width="8%">費用代碼</td>
                            <td width="10%" class="td_style2">${wfbathroomprocessEntity.applycostno}</td>
                            <td width="8%">所在廠區</td>
                            <td width="10%" class="td_style1">
                                <input id="applyfactoryid" name="wfbathroomprocess.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfbathroomprocessEntity.applyfactoryid }" disabled
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applyfactoryid\',\'请選擇所在廠區\']',onSelect:function(){onchangeFactory();}"/>
                                <input id="applynofactoryid" name="wfbathroomprocess.applynofactoryid" type="hidden" value="${wfbathroomprocessEntity.applynofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">部門名稱</td>
                            <td colspan="5" class="td_style2">${wfbathroomprocessEntity.applydeptname}</td>
                            <td width="8%">法人</td>
                            <td colspan="3" class="td_style1">
                                <input id="applycorporateid" name="wfbathroomprocess.applycorporateid" class="easyui-combobox" data-options="width: 200,required:true,validType:'comboxValidate[\'applycorporateid\',\'请選擇法人\']'"
                                       value="${wfbathroomprocessEntity.applycorporateid}" disabled panelHeight="auto" editable="false"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">聯繫電話</td>
                            <td class="td_style2">${wfbathroomprocessEntity.applytel}</td>
                            <td width="8%">聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfbathroomprocessEntity.applyemail}</td>
                            <td width="8%">報修類別</td>
                            <td  class="td_style1">
                                <input id="repairtype" name="wfbathroomprocess.repairtype" class="easyui-combobox" data-options="width: 150,required:true,validType:'comboxValidate[\'repairtype\',\'请選擇報修類別\']'"
                                       value="${wfbathroomprocessEntity.repairtype}" disabled panelHeight="auto" editable="false"/>
                            </td>
                            <td width="8%">期望完工日期</td>
                            <td class="td_style1">
                                <input id="expectdate" name="wfbathroomprocess.expectdate" class="Wdate"
                                       data-options="width:100,required:true" value="<fmt:formatDate  pattern="yyyy-MM-dd"
                                       value="${wfbathroomprocessEntity.expectdate}"/>" disabled
                                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <c:if test="${bathroomitems!=null&&bathroomitems.size()>0}">
                            <tr align="center">
                                <td colspan="10" width="100%">
                                    <div style="overflow-x: auto;width: 1200px;">
                                        <table width="100%">
                                            <tr align="center">
                                                <td width="5%">項次</td>
                                                <td width="8%">區域</td>
                                                <td width="12%">具體位置/設備編號</td>
                                                <td width="12%">異常情況</td>
                                                <td width="5%">數量</td>
                                                <td width="10%">備註</td>
                                                <c:if test="${wfbathroomprocessEntity.maintotalprice!=null}">
                                                    <td width="8%">是否更換配件</td>
                                                    <td width="10%">配件名稱</td>
                                                    <td width="10%">品牌/型號</td>
                                                    <td width="6%">單價</td>
                                                    <td width="6%">數量</td>
                                                    <td width="10%">金額<br/>（RMB/元）</td>
                                                </c:if>
                                            </tr>
                                            <c:if test="${bathroomitems!=null&&bathroomitems.size()>0}">
                                                <c:forEach items="${bathroomitems}" var="bathroomitems" varStatus="status">
                                                    <tr align="center" id="bathroomitems${status.index+1}">
                                                        <td>${status.index+1}</td>
                                                        <td>${bathroomitems.region}</td>
                                                        <td>${bathroomitems.specificlocation}</td>
                                                        <td>${bathroomitems.abnormalsituation}</td>
                                                        <td>${bathroomitems.abnormalnum}</td>
                                                        <td>${bathroomitems.remarks}</td>
                                                        <c:if test="${wfbathroomprocessEntity.maintotalprice!=null}">
                                                        <td>
                                                            <input id="isreplace${status.index+1}" name="bathroomitem[${status.index+1}].isreplace" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadIsreplace(${status.index+1});}" style="width:80px;"
                                                                   class="easyui-combobox" editable="false" value="${bathroomitems.isreplace}" disabled/>&nbsp;&nbsp;
                                                        </td>
                                                        <td><c:if test="${bathroomitems.partsname!=null}">${bathroomitems.partsname}</c:if></td>
                                                        <td><c:if test="${bathroomitems.brandmodel!=null}">${bathroomitems.brandmodel}</c:if></td>
                                                        <td><c:if test="${bathroomitems.unitprice!=null}">${bathroomitems.unitprice}</c:if></td>
                                                        <td><c:if test="${bathroomitems.partsnum!=null}">${bathroomitems.partsnum}</c:if></td>
                                                        <td><c:if test="${bathroomitems.totalprice!=null}">${bathroomitems.totalprice}</c:if></td>
                                                        </c:if>
                                                    </tr>
                                                </c:forEach>
                                            </c:if>
                                            <c:if test="${wfbathroomprocessEntity.maintotalprice!=null}">
                                            <tr align="left" class="nottr0">
                                                <td colspan="10" style="text-align:center;">總費用（RMB/元）：</td>
                                                <td colspan="2" style="text-align:left;">
                                                    ${wfbathroomprocessEntity.maintotalprice}
                                                </td>
                                            </tr>
                                            </c:if>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                        </c:if>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <input type="hidden" id="attachids" name="attachids" value="${wfbathroomprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','洗手間零星維修申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfbathroomprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfbathroomprocessEntity.workstatus!=null&&wfbathroomprocessEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<div id="dlg"></div>
<script src='${ctx}/static/js/generalAffairs/wfbathroomprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>