<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>洗手間零星維修申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
            color: black;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfbathroomprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfbathroomprocessEntity.id }"/>
    <input id="serialno" name="wfbathroomprocess.serialno" type="hidden" value="${wfbathroomprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">洗手間/直飲機維修申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfbathroomprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfbathroomprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfbathroomprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfbathroomprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfbathroomprocessEntity.makerno}/${wfbathroomprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">申請人工號</td>
                            <td width="10%" class="td_style2">${wfbathroomprocessEntity.applyno}</td>
                            <td width="8%">申請人</td>
                            <td width="10%" class="td_style2">${wfbathroomprocessEntity.applyname}</td>
                            <td width="8%">單位代碼</td>
                            <td width="10%" class="td_style2">${wfbathroomprocessEntity.applydeptno}</td>
                            <td width="8%">費用代碼</td>
                            <td width="10%" class="td_style2">${wfbathroomprocessEntity.applycostno}</td>
                            <td width="8%">所在廠區</td>
                            <td width="10%" class="td_style1">
                                <input id="applyfactoryid" name="wfbathroomprocess.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfbathroomprocessEntity.applyfactoryid }" disabled
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applyfactoryid\',\'请選擇所在廠區\']',onSelect:function(){onchangeFactory();}"/>
                                <input id="applynofactoryid" name="wfbathroomprocess.applynofactoryid" type="hidden" value="${wfbathroomprocessEntity.applynofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">部門名稱</td>
                            <td colspan="5" class="td_style2">${wfbathroomprocessEntity.applydeptname}</td>
                            <td width="8%">法人</td>
                            <td colspan="3" class="td_style1">
                                <input id="applycorporateid" name="wfbathroomprocess.applycorporateid" class="easyui-combobox" data-options="width: 200,required:true,validType:'comboxValidate[\'applycorporateid\',\'请選擇法人\']'"
                                       value="${wfbathroomprocessEntity.applycorporateid}" disabled panelHeight="auto" editable="false"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">聯繫電話</td>
                            <td class="td_style2">${wfbathroomprocessEntity.applytel}</td>
                            <td width="8%">聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfbathroomprocessEntity.applyemail}</td>
                            <td width="8%">報修類別</td>
                            <td  class="td_style1">
                                <input id="repairtype" name="wfbathroomprocess.repairtype" class="easyui-combobox" data-options="width: 150,required:true,validType:'comboxValidate[\'repairtype\',\'请選擇報修類別\']'"
                                       value="${wfbathroomprocessEntity.repairtype}" disabled panelHeight="auto" editable="false"/>
                            </td>
                            <td width="8%">期望完工日期</td>
                            <td class="td_style1">
                                <input id="expectdate" name="wfbathroomprocess.expectdate" class="Wdate"
                                       data-options="width:100,required:true" value="<fmt:formatDate  pattern="yyyy-MM-dd"
                                       value="${wfbathroomprocessEntity.expectdate}"/>" disabled
                                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <c:choose>
                            <c:when test="${bathroomitems!=null&&bathroomitems.size()>0 &&(nodeOrder lt 1)}">
                                <tr align="center">
                                    <td colspan="10" width="100%">
                                        <div style="overflow-x: auto;width: 1200px;">
                                            <table width="100%">
                                                <tr align="center">
                                                    <td width="5%">項次</td>
                                                    <td width="10%">區域</td>
                                                    <td width="15%">具體位置/設備編號</td>
                                                    <td width="15%">異常情況</td>
                                                    <td width="10%">數量</td>
                                                    <td width="15%">備註</td>
                                                </tr>
                                                <c:if test="${bathroomitems!=null&&bathroomitems.size()>0}">
                                                    <c:forEach items="${bathroomitems}" var="bathroomitems" varStatus="status">
                                                        <tr align="center" id="bathroomitems${status.index+1}">
                                                            <td>${status.index+1}</td>
                                                            <td>${bathroomitems.region}</td>
                                                            <td>${bathroomitems.specificlocation}</td>
                                                            <td>${bathroomitems.abnormalsituation}</td>
                                                            <td>${bathroomitems.abnormalnum}</td>
                                                            <td>${bathroomitems.remarks}</td>
                                                        </tr>
                                                    </c:forEach>
                                                </c:if>
                                            </table>
                                        </div>
                                    </td>
                                </tr>
                            </c:when>
                            <c:when test="${not empty nodeName &&'總務窗口確認' eq nodeName}">
                                <c:if test="${bathroomitems!=null&&bathroomitems.size()>0}">
                                <tr align="center">
                                    <td colspan="10" width="100%">
                                        <div style="overflow-x: auto;width: 1200px;">
                                            <input id="bathroomItemTableIndex" type="hidden"
                                                   value="<c:if test="${bathroomitems!=null && bathroomitems.size()>0}">${bathroomitems.size() +1}</c:if>
                                        <c:if test="${bathroomitems.size()==0 || bathroomitems==null}">2</c:if>"/>
                                            </input>
                                            <table id="bathroomItemTable" width="130%">
                                                <tr align="center">
                                                    <td width="5%">項次</td>
                                                    <td width="8%">區域</td>
                                                    <td width="12%">具體位置</td>
                                                    <td width="12%">異常情況</td>
                                                    <td width="5%">數量</td>
                                                    <td width="10%">備註</td>
                                                    <td width="8%">是否更換配件&nbsp;<font color="red">*</font></td>
                                                    <td width="10%">配件名稱</td>
                                                    <td width="10%">品牌/型號</td>
                                                    <td width="6%">單價</td>
                                                    <td width="6%">數量</td>
                                                    <td width="10%">金額<br/>（RMB/元）</td>
                                                </tr>
                                                <tbody id="info_Body0">
                                                <c:if test="${bathroomitems!=null&&bathroomitems.size()>0}">
                                                    <c:forEach items="${bathroomitems}" var="bathroomitems" varStatus="status">
                                                        <tr align="center" id="bathroomitems${status.index+1}">
                                                            <td>${status.index+1}
                                                                <input id="shunxu${status.index+1}" type="hidden" name="bathroomitem[${status.index+1}].shunxu" value="${status.index+1}"/>
                                                            </td>
                                                            <td>
                                                                <input id="region${status.index+1}" name="bathroomitem[${status.index+1}].region"
                                                                   class="easyui-validatebox inputCss" style="width:80px;" readonly  value="${bathroomitems.region}"/>
                                                            </td>
                                                            <td>
                                                                <input id="specificlocation${status.index+1}" name="bathroomitem[${status.index+1}].specificlocation"
                                                                       class="easyui-validatebox inputCss" style="width:150px;" readonly  value="${bathroomitems.specificlocation}"/>
                                                            </td>
                                                            <td>
                                                                <input id="abnormalsituation${status.index+1}" name="bathroomitem[${status.index+1}].abnormalsituation"
                                                                       class="easyui-validatebox inputCss" style="width:150px;" readonly value="${bathroomitems.abnormalsituation}"/>
                                                            </td>
                                                            <td>
                                                                <input id="abnormalnum${status.index+1}" name="bathroomitem[${status.index+1}].abnormalnum"
                                                                       class="easyui-validatebox inputCss" style="width:60px;" readonly  value="${bathroomitems.abnormalnum}"/>
                                                            </td>
                                                            <td>
                                                                <input id="remarks${status.index+1}" name="bathroomitem[${status.index+1}].remarks"
                                                                       class="easyui-validatebox inputCss" style="width:120px;" readonly  value="${bathroomitems.remarks}"/>
                                                            </td>
                                                            <td>
                                                                <input id="isreplace${status.index+1}" name="bathroomitem[${status.index+1}].isreplace" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,validType:'comboxValidate[\'isreplace${status.index+1}\',\'请選擇是否更換配件\']',onBeforeLoad:function(){loadIsreplace(${status.index+1});},onSelect:function(){adOnchangeIsreplace(${status.index+1});}" style="width:80px;"
                                                                       class="easyui-combobox" editable="false" value="${bathroomitems.isreplace}"/>&nbsp;&nbsp;
                                                            </td>
                                                            <td>
                                                                <input id="partsname${status.index+1}" name="bathroomitem[${status.index+1}].partsname"
                                                                       class="easyui-validatebox" style="width:100px;"  value="${bathroomitems.partsname}"/>
                                                            </td>
                                                            <td>
                                                                <input id="brandmodel${status.index+1}" name="bathroomitem[${status.index+1}].brandmodel"
                                                                       class="easyui-validatebox" style="width:100px;"  value="${bathroomitems.brandmodel}"/>
                                                            </td>
                                                            <td>
                                                                <input id="unitprice${status.index+1}" name="bathroomitem[${status.index+1}].unitprice"
                                                                       class="easyui-validatebox" style="width:60px;" onchange="countSellMoney(this)" data-options="validType:'xiaoshu[\'unitprice${status.index+1}\']'"  value="${bathroomitems.unitprice}"/>
                                                            </td>
                                                            <td>
                                                                <input id="partsnum${status.index+1}" name="bathroomitem[${status.index+1}].partsnum"
                                                                       class="easyui-validatebox" style="width:60px;" onchange="countSellMoney(this)" data-options="validType:'pinteger[\'partsnum${status.index+1}\']'" value="${bathroomitems.partsnum}"/>
                                                            </td>
                                                            <td>
                                                                <input id="totalprice${status.index+1}" name="bathroomitem[${status.index+1}].totalprice"
                                                                       class="easyui-validatebox" style="width:60px;" readonly value="${bathroomitems.totalprice}"/>
                                                            </td>
                                                        </tr>
                                                    </c:forEach>
                                                </c:if>
                                                </tbody>
                                                <tr align="left" class="nottr0">
                                                    <td colspan="10" style="text-align:center;">總費用（RMB/元）：</td>
                                                    <td colspan="2" style="text-align:left;">
                                                        <input id="maintotalprice" name="wfbathroomprocess.maintotalprice" class="easyui-validatebox inputCss" style="color: #ff9000;" data-options="width:100" value="${wfbathroomprocessEntity.maintotalprice}" /></td>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </td>
                                </tr>
                                </c:if>
                            </c:when>
                            <c:otherwise>
                                <c:if test="${bathroomitems!=null&&bathroomitems.size()>0 &&(nodeOrder ge 2)}">
                                    <tr align="center">
                                        <td colspan="10" width="100%">
                                            <div style="overflow-x: auto;width: 1200px;">
                                                <table width="100%">
                                                    <tr align="center">
                                                        <td width="5%">項次</td>
                                                        <td width="8%">區域</td>
                                                        <td width="12%">具體位置</td>
                                                        <td width="12%">異常情況</td>
                                                        <td width="5%">數量</td>
                                                        <td width="10%">備註</td>
                                                        <td width="8%">是否更換配件</td>
                                                        <td width="10%">配件名稱</td>
                                                        <td width="10%">品牌/型號</td>
                                                        <td width="6%">單價</td>
                                                        <td width="6%">數量</td>
                                                        <td width="10%">金額<br/>（RMB/元）</td>
                                                    </tr>
                                                    <c:if test="${bathroomitems!=null&&bathroomitems.size()>0}">
                                                        <c:forEach items="${bathroomitems}" var="bathroomitems" varStatus="status">
                                                            <tr align="center" id="bathroomitems${status.index+1}">
                                                                <td>${status.index+1}</td>
                                                                <td>${bathroomitems.region}</td>
                                                                <td>${bathroomitems.specificlocation}</td>
                                                                <td>${bathroomitems.abnormalsituation}</td>
                                                                <td>${bathroomitems.abnormalnum}</td>
                                                                <td>${bathroomitems.remarks}</td>
                                                                <td>
                                                                    <input id="isreplace${status.index+1}" name="bathroomitem[${status.index+1}].isreplace" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadIsreplace(${status.index+1});}" style="width:80px;"
                                                                           class="easyui-combobox" editable="false" value="${bathroomitems.isreplace}" disabled/>&nbsp;&nbsp;
                                                                </td>
                                                                <td><c:if test="${bathroomitems.partsname!=null}">${bathroomitems.partsname}</c:if></td>
                                                                <td><c:if test="${bathroomitems.brandmodel!=null}">${bathroomitems.brandmodel}</c:if></td>
                                                                <td><c:if test="${bathroomitems.unitprice!=null}">${bathroomitems.unitprice}</c:if></td>
                                                                <td><c:if test="${bathroomitems.partsnum!=null}">${bathroomitems.partsnum}</c:if></td>
                                                                <td><c:if test="${bathroomitems.totalprice!=null}">${bathroomitems.totalprice}</c:if></td>
                                                            </tr>
                                                        </c:forEach>
                                                    </c:if>
                                                    <tr align="left" class="nottr0">
                                                        <td colspan="10" style="text-align:center;">總費用（RMB/元）：</td>
                                                        <td colspan="2" style="text-align:left;">${wfbathroomprocessEntity.maintotalprice}</td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </td>
                                    </tr>
                                </c:if>
                            </c:otherwise>
                        </c:choose>

                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style2">
                                <c:if test="${wfbathroomprocessEntity.attachids!=null}">
                                    <input type="hidden" id="attachids" name="wfbathroomprocess.attachids" value="${wfbathroomprocessEntity.attachids }"/>
                                    <div id="dowloadUrl">
                                        <c:forEach items="${file}" varStatus="i" var="item">
                                            <div id="${item.id}"
                                                 style="line-height:30px;margin-left:5px;" class="float_L">
                                                <div class="float_L">
                                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                                </div>
                                            </div>
                                        </c:forEach>
                                    </div>
                                </c:if>
                                <c:if test="${wfbathroomprocessEntity.attachids==null}">無</c:if>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%" class="td_style2">
						        <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox" style="width:1000px;height:60px;" rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <c:choose>
                                    <c:when test="${not empty nodeName&&'總務窗口確認' eq nodeName}">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"  perCall="zwqrupdate"
                                                    serialNo="${wfbathroomprocessEntity.serialno}"></fox:action>
                                    </c:when>
                                    <c:otherwise>
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                                    serialNo="${wfbathroomprocessEntity.serialno}"></fox:action>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','洗手間零星維修申請單');">點擊查看簽核流程圖</a>
                                <a href="javascript:;" id="btnExtract" class="easyui-linkbutton" style="width: 100px;"
                                   onclick="getNodeUserInfo('${nodeUserNo}')">待簽核主管</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfbathroomprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<div id="dlg"></div>
<input type="hidden" id="nodeName"  value="${nodeName}"/>
<input type="hidden" id="disOrEnabled"  value="disabled"/>
<script src='${ctx}/static/js/generalAffairs/wfbathroomprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>