<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>洗手間零星維修申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
            color: black;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfbathroomprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfbathroomprocessEntity.id }"/>
    <input id="serialno" name="wfbathroomprocess.serialno" type="hidden" value="${wfbathroomprocessEntity.serialno }"/>
    <input id="makerno" name="wfbathroomprocess.makerno" type="hidden" value="${wfbathroomprocessEntity.makerno }"/>
    <input id="makername" name="wfbathroomprocess.makername" type="hidden" value="${wfbathroomprocessEntity.makername }"/>
    <input id="makerdeptno" name="wfbathroomprocess.makerdeptno" type="hidden" value="${wfbathroomprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfbathroomprocess.makerfactoryid" type="hidden" value="${wfbathroomprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">洗手間/直飲機維修申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfbathroomprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfbathroomprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfbathroomprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfbathroomprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfbathroomprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfbathroomprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfbathroomprocessEntity.makerno}/${wfbathroomprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="applyno" name="wfbathroomprocess.applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfbathroomprocessEntity.applyno}" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="8%">申請人</td>
                            <td width="10%" class="td_style1">
                                <input id="applyname" name="wfbathroomprocess.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfbathroomprocessEntity.applyname }"/>
                            </td>
                            <td width="8%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="applydeptno" name="wfbathroomprocess.applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfbathroomprocessEntity.applydeptno }"/>
                            </td>
                            <td width="8%">費用代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="applycostno" name="wfbathroomprocess.applycostno" class="easyui-validatebox inputCss" data-options="width: 90"
                                       value="${wfbathroomprocessEntity.applycostno }"/>
                            </td>
                            <td width="8%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="applyfactoryid" name="wfbathroomprocess.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfbathroomprocessEntity.applyfactoryid }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applyfactoryid\',\'请選擇所在廠區\']',onSelect:function(){onchangeFactory();}"/>
                                <input id="applynofactoryid" name="wfbathroomprocess.applynofactoryid" type="hidden" value="${wfbathroomprocessEntity.applynofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">部門名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="wfbathroomprocess.applydeptname" class="easyui-validatebox" data-options="width: 450"
                                       value="${wfbathroomprocessEntity.applydeptname }"/>
                            </td>
                            <td width="8%">法人&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applycorporateid" name="wfbathroomprocess.applycorporateid" class="easyui-combobox" data-options="width: 200,required:true,validType:'comboxValidate[\'applycorporateid\',\'请選擇法人\']'"
                                       value="${wfbathroomprocessEntity.applycorporateid}" panelHeight="auto" editable="false"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">聯繫電話&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applytel" name="wfbathroomprocess.applytel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfbathroomprocessEntity.applytel }" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td width="8%">聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="wfbathroomprocess.applyemail" class="easyui-validatebox"
                                       value="${wfbathroomprocessEntity.applyemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td width="8%">報修類別&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="repairtype" name="wfbathroomprocess.repairtype" class="easyui-combobox" data-options="width: 150,required:true,validType:'comboxValidate[\'repairtype\',\'请選擇報修類別\']'"
                                       value="${wfbathroomprocessEntity.repairtype}" panelHeight="auto" editable="false"/>
                            </td>
                            <td width="8%">期望完工日期&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="expectdate" name="wfbathroomprocess.expectdate" class="Wdate"
                                       data-options="width:100,required:true" value="<fmt:formatDate  pattern="yyyy-MM-dd"
                                       value="${wfbathroomprocessEntity.expectdate}"/>"
                                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <input id="bathroomItemTableIndex" type="hidden"
                                           value="<c:if test="${bathroomitems!=null && bathroomitems.size()>0}">${bathroomitems.size() +1}</c:if>
                                        <c:if test="${bathroomitems.size()==0 || bathroomitems==null}">2</c:if>"/>
                                    </input>
                                    <table id="bathroomItemTable" width="100%">
                                        <tr align="center">
                                            <td width="10%">項次</td>
                                            <td width="10%">區域&nbsp;<font color="red">*</font></td>
                                            <td width="15%">具體位置/設備編號&nbsp;<font color="red">*</font></td>
                                            <td width="15%">異常情況&nbsp;<font color="red">*</font></td>
                                            <td width="10%">數量&nbsp;<font color="red">*</font></td>
                                            <td width="15%">備註</td>
                                            <td width="5%">&nbsp;操作&nbsp;</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${bathroomitems!=null&&bathroomitems.size()>0}">
                                            <c:forEach items="${bathroomitems}" var="bathroomitems" varStatus="status">
                                                <tr align="center" id="bathroomitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <input id="region${status.index+1}" name="bathroomitem[${status.index+1}].region"
                                                               class="easyui-validatebox" style="width:100px;" data-options="required:true"  value="${bathroomitems.region}"/>
                                                    </td>
                                                    <td>
                                                        <input id="specificlocation${status.index+1}" name="bathroomitem[${status.index+1}].specificlocation"
                                                               class="easyui-validatebox" style="width:200px;"data-options="required:true"  value="${bathroomitems.specificlocation}"/>
                                                    </td>
                                                    <td>
                                                        <input id="abnormalsituation${status.index+1}" name="bathroomitem[${status.index+1}].abnormalsituation"
                                                               class="easyui-validatebox" style="width:200px;" data-options="required:true"  value="${bathroomitems.abnormalsituation}"/>
                                                    </td>
                                                    <td>
                                                        <input id="abnormalnum${status.index+1}" name="bathroomitem[${status.index+1}].abnormalnum"
                                                               class="easyui-validatebox" style="width:80px;" data-options="required:true,validType:'pinteger[\'abnormalnum${status.index+1}\']'"  value="${bathroomitems.abnormalnum}"/>
                                                    </td>
                                                    <td>
                                                        <input id="remarks${status.index+1}" name="bathroomitem[${status.index+1}].remarks"
                                                               class="easyui-validatebox" style="width:150px;"  value="${bathroomitems.remarks}"/>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="bathroomdeltr(${status.index+1});return false;"/>
                                                        <input id="shunxu${status.index+1}" type="hidden" name="bathroomitem[${status.index+1}].shunxu" value="${status.index+1}"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${bathroomitems.size()==0 || bathroomitems==null}">
                                            <tr align="center" id="bathroomitems1">
                                                <td>1</td>
                                                <td>
                                                    <input id="region1" name="bathroomitem[1].region" class="easyui-validatebox" style="width:100px;" data-options="required:true"  value=""/>
                                                </td>
                                                <td>
                                                    <input id="specificlocation1" name="bathroomitem[1].specificlocation" class="easyui-validatebox" style="width:200px;" data-options="required:true"  value=""/>
                                                </td>
                                                <td>
                                                    <input id="abnormalsituation1" name="bathroomitem[1].abnormalsituation" class="easyui-validatebox" style="width:200px;" data-options="required:true"  value=""/>
                                                </td>
                                                <td>
                                                    <input id="abnormalnum1" name="bathroomitem[1].abnormalnum" class="easyui-validatebox" style="width:80px;" data-options="required:true,validType:'pinteger[\'abnormalnum1\']'"  value=""/>
                                                </td>
                                                <td>
                                                    <input id="remarks1" name="bathroomitem[1].remarks" class="easyui-validatebox" style="width:150px;"  value=""/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="bathroomdeltr(1);return false;"/>
                                                    <input id="shunxu1" type="hidden" name="bathroomitem[1].shunxu" value="1"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr0">
                                            <td colspan="8" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="bathroomItemAdd" style="width:100px;float:left;" value="添加一筆"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">附件</td>
                            <td width="92%" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="wfbathroomprocess.attachids" value="${wfbathroomprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','洗手間零星維修申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zwqchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zwqchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(70,'zwqchargeno','zwqchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zwqchargeno" name="wfbathroomprocess.zwqchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['zwqchargeno']}"
                                                               readonly
                                                               value="${wfbathroomprocessEntity.zwqchargeno }"/><c:if test="${requiredMap['zwqchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="zwqchargename" name="wfbathroomprocess.zwqchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zwqchargeno']}"
                                                                value="${wfbathroomprocessEntity.zwqchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="wfbathroomprocess.kchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wfbathroomprocessEntity.kchargeno }"/><c:if test="${requiredMap['kchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="kchargename" name="wfbathroomprocess.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfbathroomprocessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="wfbathroomprocess.bchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfbathroomprocessEntity.bchargeno }"/><c:if test="${requiredMap['bchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="bchargename" name="wfbathroomprocess.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfbathroomprocessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'cchargeno','cchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wfbathroomprocess.cchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfbathroomprocessEntity.cchargeno }"/><c:if test="${requiredMap['cchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wfbathroomprocess.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfbathroomprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>


                                            <table width="18%" style="float: left;margin-left: 5px;"  id="jgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['jgchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(63,'jgchargeTable','jgchargeno','jgchargename',$('#applyfactoryid').combobox('getValue'),'wfbathroomprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jgchargeno" name="wfbathroomprocess.jgchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['jgchargeno']}"
                                                               readonly
                                                               value="${wfbathroomprocessEntity.jgchargeno }"/><c:if test="${requiredMap['jgchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="jgchargename" name="wfbathroomprocess.jgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jgchargeno']}"
                                                                value="${wfbathroomprocessEntity.jgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="kschargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['kschargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(201,'kschargeno','kschargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kschargeno" name="wfbathroomprocess.kschargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['kschargeno']}"
                                                               readonly
                                                               value="${wfbathroomprocessEntity.kschargeno }"/><c:if test="${requiredMap['kschargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="kschargename" name="wfbathroomprocess.kschargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kschargeno']}"
                                                                value="${wfbathroomprocessEntity.kschargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="bschargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['bschargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(202,'bschargeno','bschargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bschargeno" name="wfbathroomprocess.bschargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['bschargeno']}"
                                                               readonly
                                                               value="${wfbathroomprocessEntity.bschargeno }"/><c:if test="${requiredMap['bschargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="bschargename" name="wfbathroomprocess.bschargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bschargeno']}"
                                                                value="${wfbathroomprocessEntity.bschargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cschargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cschargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(203,'cschargeno','cschargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cschargeno" name="wfbathroomprocess.cschargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['cschargeno']}"
                                                               readonly
                                                               value="${wfbathroomprocessEntity.cschargeno }"/><c:if test="${requiredMap['cschargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="cschargename" name="wfbathroomprocess.cschargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cschargeno']}"
                                                                value="${wfbathroomprocessEntity.cschargename }"/>
                                                    </td>
                                                </tr>
                                            </table>


                                            <table width="18%" style="float: left;margin-left: 5px;" id="zwychargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zwychargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(204,'zwychargeno','zwychargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zwychargeno" name="wfbathroomprocess.zwychargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['zwychargeno']}"
                                                               readonly
                                                               value="${wfbathroomprocessEntity.zwychargeno }"/><c:if test="${requiredMap['zwychargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="zwychargename" name="wfbathroomprocess.zwychargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zwychargeno']}"
                                                                value="${wfbathroomprocessEntity.zwychargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfbathroomprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="canelTask('${wfbathroomprocessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
    <div id="dlg"></div>
</form>
</div>
<script src='${ctx}/static/js/generalAffairs/wfbathroomprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>