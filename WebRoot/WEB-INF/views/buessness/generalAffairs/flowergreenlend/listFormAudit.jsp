<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>花卉綠植申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/flowergreenlend/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${flowergreenlendEntity.id }"/>
    <input id="serialno" name="flowergreenlend.serialno" type="hidden" value="${flowergreenlendEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">花卉綠植申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${flowergreenlendEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${flowergreenlendEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${flowergreenlendEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${flowergreenlendEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${flowergreenlendEntity.makerno}/${flowergreenlendEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
           <tr>
                <td>
						<table class="formList">
							<tr align="center">
								<td>申請人工號</td>
								<td><input id="dealno" name="flowergreenlend.dealno"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 150"
									value="${flowergreenlendEntity.dealno}" /></td>
								<td>申請人</td>
								<td><input id="dealname"
									name="flowergreenlend.dealname" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${flowergreenlendEntity.dealname}" /></td>
								<td>單位代碼</td>
								<td colspan="2"><input id="dealdeptno"
									name="flowergreenlend.dealdeptno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${flowergreenlendEntity.dealdeptno}" /></td>
								<td>廠區</td>
								<td colspan="2"><input id="dealfactoryid"
									name="flowergreenlend.dealfactoryid" disabled
									class="easyui-combobox" data-options="width: 150,required:true"
									value="${flowergreenlendEntity.dealfactoryid}" /></td>
							</tr>
							<tr align="center">
							    <td>費用代碼</td>
								<td><input id="costno" readonly
									style="width:150px" name="flowergreenlend.costno"
									class="easyui-validatebox inputCss" 
									data-options="width: 150,required:true"
									value="${flowergreenlendEntity.costno}" /></td>
							    <td>申請單位</td>
								<td colspan="4"><input id="dealdeptname"
									style="width:380px" name="flowergreenlend.dealdeptname"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 380,required:true"
									value="${flowergreenlendEntity.dealdeptname}" /></td>
							    <td>聯繫分機</td>
								<td colspan="2"><input id="dealtel" name="flowergreenlend.dealtel"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 150,required:true,prompt:'579+66666',validType:'tel[\'applytel\',\'分機格式不正確\']'"
									value="${flowergreenlendEntity.dealtel}" /></td>
							</tr>
							<tr align="center">
								<td>聯繫郵箱</td>
								<td colspan="2"><input id="dealemail"
									name="flowergreenlend.dealemail"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 250,required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"
									value="${flowergreenlendEntity.dealemail}" /></td>
								<td>租擺位置</td>
								<td colspan="3"><input id="lendposition"
									name="flowergreenlend.lendposition"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 250,required:true"
									value="${flowergreenlendEntity.lendposition}" /></td>
								<td>租擺類別</td>
                                <td colspan="2">
								<div class="lendTypeDiv"></div>
								<input id="lendtype" name="flowergreenlend.lendtype" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${flowergreenlendEntity.lendtype }" />
								<input id='disOrEnabled2' value="disabled" type="hidden"/></td>
							</tr>
                    </table>
                </td>
           </tr>
           <c:choose>
           <c:when test="${not empty nodeName&&'總務核定呈簽' eq nodeName}">
           <tr>
					<td>
						<table class="formList">
							<tr>
								<td colspan="10" class="td_style1">租擺費用</td>
							</tr>
							<tr align="center">
								<td colspan="10" width="100%">
									<div style="overflow-x: auto;width: 1200px;">
										<input id="flowerItemTableIndex" type="hidden"
											value="<c:if test="${itemEntity!=null && itemEntity.size()>0}">${itemEntity.size() +1}</c:if>
                                        <c:if test="${itemEntity==null}">2</c:if>">
										</input>
										<table id="flowerItemTable" width="100%" border="1" cellpadding="0" cellspacing="0">
											<tr align="center">
												<td>&nbsp;序號&nbsp;</td>
												<td>花卉綠植名稱&nbsp;<font color="red">*</font></td>
												<td>花卉綠植規格&nbsp;<font color="red">*</font></td>
												<td>數量&nbsp;<font color="red">*</font></td>
												<td>單價（RMB）<font color="red">*</font></td>
												<td>合計（RMB）<font color="red">*</font></td>
												<td>備註&nbsp;<font color="red">*</font></td>
												<td>&nbsp;操作&nbsp;</td>
											</tr>
											<tbody id="info_Body">
												<c:if test="${itemEntity!=null&&itemEntity.size()>0}">
													<c:forEach items="${itemEntity}" var="item"
														varStatus="status">
														<tr align="center" id="flowerItem${status.index+1}">
															<td>${status.index+1}<input type="hidden"
																name="flowergreenitem[${status.index}].flowerorder"
																value="${status.index+1}" /></td>
															<td><input id="flowername${status.index+1}"
																name="flowergreenitem[${status.index}].flowername"
																class="easyui-validatebox" data-options="required:true"
																style="width: 100px;" value="${item.flowername}" /></td>
														    <td><input id="flowertype${status.index+1}"
																name="flowergreenitem[${status.index}].flowertype"
																class="easyui-validatebox" data-options="required:true"
																style="width: 100px;" value="${item.flowertype}" /></td>
															<td><input id="flowernum${status.index+1}"  positivenum
																name="flowergreenitem[${status.index}].flowernum"
																class="easyui-validatebox" data-options="required:true"
																style="width:50px;" value="${item.flowernum}" /></td>
															<td><input id="flowerprice${status.index+1}"
																name="flowergreenitem[${status.index}].flowerprice"
																class="easyui-validatebox" data-options="required:true"
																style="width: 50px;" value="${item.flowerprice}" /></td>
															<td><input id="flowerpricetotal${status.index+1}"
																name="flowergreenitem[${status.index}].flowerpricetotal"
																class="easyui-validatebox" data-options="required:true" onblur="autoSum()"
																style="width: 80px;" value="${item.flowerpricetotal}" /></td>
															<td><input id="remark${status.index+1}"
																name="flowergreenitem[${status.index}].remark"
																class="easyui-validatebox" data-options="required:true"
																style="width: 130px;" value="${item.remark}" /></td>
															<td><input type="image"
																src="${ctx}/static/images/deleteRow.png"
																onclick="deltr(${status.index+1});return false;" /></td>
														</tr>
													</c:forEach>
												</c:if>
												<c:if test="${itemEntity==null}">
													<tr align="center" id="flowerItem1">
														<td>1<input  type="hidden"
															name="flowergreenitem[0].flowerorder" value="1" /></td>
														<td><input id="flowername1"
															name="flowergreenitem[0].flowername"
															class="easyui-validatebox" data-options="required:true"
															style="width:100px;" value="" /></td>
														<td><input id="flowertype1"
															name="flowergreenitem[0].flowertype"
															class="easyui-validatebox" data-options="required:true"
															style="width:100px;" value="" /></td>
														<td><input id="flowernum1"
															name="flowergreenitem[0].flowernum"
															class="easyui-validatebox" data-options="required:true"
															style="width:50px;" value="" /></td>
														<td><input id="flowerprice1"
															name="flowergreenitem[0].flowerprice"
															class="easyui-validatebox" data-options="required:true"
															style="width:50px;" value="" /></td>
														<td><input id="flowerpricetotal1"
															name="flowergreenitem[0].flowerpricetotal" onblur="autoSum()"
															class="easyui-validatebox" data-options="required:true"
															style="width:80px;" value="" /></td>
														<td><input id="remark1"
															name="flowergreenitem[0].remark"
															class="easyui-validatebox" data-options="required:true"
															style="width:130px;" value="" /></td>
														<td><input type="image"
															src="${ctx}/static/images/deleteRow.png"
															onclick="deltr(1);return false;" /></td>
													</tr>
												</c:if>
											</tbody>
											<tr class="nottr">
												<td colspan="2" style="text-align: right;">總計:&nbsp;&nbsp;<font color="red">*</font></td>
												<td colspan="8" ><input id="feesum"
													name="flowergreenlend.feesum"
													class="easyui-validatebox inputCss" style="color: #ff9000;"
													data-options="width:100"
													value="${flowergreenlendEntity.feesum}" /></td>
											</tr>
											<tr align="left" class="nottr">
												<td colspan="21" width="100%"
													style="text-align:left;padding-left:10px;"><input
													type="button" id="itemAdd" style="width:100px;float:left;"
													value="添加一筆" /></td>
											</tr>
										</table>
									</div>
								</td>
							</tr>
							<tr align="center">
								<td>附件</td>
								<td colspan="9" class="td_style1">
									<input type="hidden" id="attachids"
										   name="flowergreenlend.attachids" value="${flowergreenlendEntity.attachids }"/>
									<div id="dowloadUrl">
										<c:forEach items="${file}" varStatus="i" var="item">
											<div id="${item.id}"
												 style="line-height:30px;margin-left:5px;" class="float_L">
												<div class="float_L">
													<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
												</div>
											</div>
										</c:forEach>
									</div>
								</td>
							</tr>
							<tr align="center">
							   	<td>備註</td>
								<td colspan="8" class="td_style1">請先下載《花卉綠植報價單.pdf》查看單價<a href="${ctx}/ossAdmin/download/${flowerFileId}">花卉綠植報價單.pdf</a></td>
							</tr>
						</table>
					</td>
				</tr>
		   </c:when>
           <c:otherwise>
                <tr>
					<td>
						<table class="formList">
							<tr>
								<td colspan="10" class="td_style1">租擺費用</td>
							</tr>
							<tr align="center">
								<td colspan="10" width="100%">
									<div style="overflow-x: auto;width: 1200px;">
										<input id="flowerItemTableIndex" type="hidden"
											value="<c:if test="${itemEntity!=null && itemEntity.size()>0}">${itemEntity.size() +1}</c:if>
                                        <c:if test="${itemEntity==null}">2</c:if>">
										</input>
										<table id="flowerItemTable" width="100%" border="1" cellpadding="0" cellspacing="0">
											<tr align="center">
												<td>&nbsp;序號&nbsp;</td>
												<td>花卉綠植名稱</td>
												<td>花卉綠植規格</td>
												<td>數量</td>
												<td>單價（RMB）</td>
												<td>合計（RMB）</td>
												<td>備註</td>		
											</tr>
											<tbody id="info_Body">
												<c:if test="${itemEntity!=null&&itemEntity.size()>0}">
													<c:forEach items="${itemEntity}" var="item"
														varStatus="status">
														<tr align="center" id="flowerItem${status.index+1}">
															<td>${status.index+1}</td>
															<td>${item.flowername}</td>
														    <td>${item.flowertype}</td>
															<td>${item.flowernum}</td>
															<td>${item.flowerprice}</td>
															<td>${item.flowerpricetotal}</td>
															<td>${item.remark}</td>
														</tr>
													</c:forEach>
												</c:if>
												<c:if test="${itemEntity==null}">
													<tr align="center" id="flowerItem1">
														<td>1</td>
														<td></td>
														<td></td>
														<td></td>
														<td></td>
														<td></td>
														<td></td>
													</tr>
												</c:if>
											</tbody>
											<tr class="nottr">
												<td colspan="2" style="text-align: right;">總計:&nbsp;&nbsp;</td>
												<td colspan="8" >${flowergreenlendEntity.feesum}</td>
											</tr>
										</table>
									</div>
								</td>
							</tr>
							<tr align="center">
								<td>附件</td>
								<td colspan="9" class="td_style1">
									<input type="hidden" id="attachids"
										   name="flowergreenlend.attachids" value="${flowergreenlendEntity.attachids }"/>
									<div id="dowloadUrl">
										<c:forEach items="${file}" varStatus="i" var="item">
											<div id="${item.id}"
												 style="line-height:30px;margin-left:5px;" class="float_L">
												<div class="float_L">
													<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
												</div>
											</div>
										</c:forEach>
									</div>
								</td>
							</tr>
							<tr align="center">
							   <td>備註</td>
							   <td colspan="9" class="td_style1">請先下載《花卉綠植報價單.pdf》查看單價<a href="${ctx}/flowergreenlend/downLoad">花卉綠植報價單.pdf</a></td>
							</tr>
						</table>
					</td>
				</tr>
           </c:otherwise>
           </c:choose>
            <tr>
                <td>
                     <table class="formList">
                        <tr align="center">
                            <td>批註</td>
                            <td colspan="9">
						<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                        <c:choose>
                        <c:when test="${not empty nodeName&&'總務核定呈簽' eq nodeName}">
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                    perCall="updateItem"  serialNo="${flowergreenlendEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        </c:when>
                        <c:otherwise>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                       serialNo="${flowergreenlendEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        </c:otherwise>
                        </c:choose>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','花卉綠植申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${flowergreenlendEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  <div id="dlg"></div>
<script src='${ctx}/static/js/generalAffairs/flowergreenlend.js?random=<%= Math.random()%>'></script>
</body>
</html>