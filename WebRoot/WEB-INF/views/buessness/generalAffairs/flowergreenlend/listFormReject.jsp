<%@ page language="java" contentType="text/html; charset=UTF-8"
		 pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
	<title>花卉綠植申請單</title>
	<script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
	</script>
	<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/flowergreenlend/${action}"
	  method="post">
	<input id="ids" name="ids" type="hidden"
		   value="${flowergreenlendEntity.id }" /> <input id="serialno"
														  name="flowergreenlend.serialno" type="hidden"
														  value="${flowergreenlendEntity.serialno }" /> <input id="makerno"
																											   name="flowergreenlend.makerno" type="hidden"
																											   value="${flowergreenlendEntity.makerno }" /> <input id="makername"
																																								   name="flowergreenlend.makername" type="hidden"
																																								   value="${flowergreenlendEntity.makername }" /> <input
		id="flowergreenlend.makerdeptno" name="makerdeptno" type="hidden"
		value="${flowergreenlendEntity.makerdeptno }" />
	<div class="commonW">
		<div class="headTitle">花卉綠植申請單</div>
		<div class="position_L">
			任務編碼：<span style="color:#999;"> <c:choose>
			<c:when test="${flowergreenlendEntity.serialno==null}">
				提交成功后自動編碼
			</c:when>
			<c:otherwise>
				${flowergreenlendEntity.serialno}
			</c:otherwise>
		</c:choose>
				</span>
		</div>
		<div class="position_L1 margin_L">
			填單時間：<span style="color:#999;"> <c:choose>
			<c:when test="${flowergreenlendEntity.createtime==null}">
				YYYY/MM/DD
			</c:when>
			<c:otherwise>
							<input style="border:0px" style="width: 120px" readonly="true"
								   value="<fmt:formatDate value='${flowergreenlendEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>" />
			</c:otherwise>
		</c:choose>
				</span>
		</div>
		<c:if test="${empty flowergreenlendEntity.makerno}">
			<div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
		</c:if>
		<c:if test="${not empty flowergreenlendEntity.makerno}">
			<div class="position_R margin_R">填單人：${flowergreenlendEntity.makerno}/${flowergreenlendEntity.makername}</div>
		</c:if>
		<div class="clear"></div>
		<table class="formList">
			<tr>
				<td>
					<table class="formList">
						<tr align="center">
							<td>申請人工號&nbsp;<font color="red">*</font></td>
							<td><input id="dealno" name="flowergreenlend.dealno"
									   onblur="queryUserInfo(this)" class="easyui-validatebox"
									   data-options="width: 150,required:true"
									   value="${flowergreenlendEntity.dealno}" /></td>
							<td>申請人</td>
							<td><input id="dealname" name="flowergreenlend.dealname"
									   readonly class="easyui-validatebox inputCss"
									   data-options="width: 150"
									   value="${flowergreenlendEntity.dealname}" /></td>
							<td>單位代碼</td>
							<td colspan="2"><input id="dealdeptno"
												   name="flowergreenlend.dealdeptno" readonly
												   class="easyui-validatebox inputCss" data-options="width: 150"
												   value="${flowergreenlendEntity.dealdeptno}" /></td>
							<td>廠區&nbsp;<font color="red">*</font></td>
							<td colspan="2"><input id="dealfactoryid"
												   name="flowergreenlend.dealfactoryid" class="easyui-combobox"
												   data-options="width: 150,required:true"
												   value="${flowergreenlendEntity.dealfactoryid}" />
								<input class="easyui-validatebox"
									   id="guishufactoryid" name="flowergreenlend.guishufactoryid" style="width:50px" readonly type="hidden"
									   value="${flowergreenlendEntity.guishufactoryid}" />
							</td>
						</tr>
						<tr align="center">
							<td>費用代碼</td>
							<td><input id="costno" readonly style="width:150px"
									   name="flowergreenlend.costno"
									   class="easyui-validatebox inputCss"
									   data-options="width: 150,required:true"
									   value="${flowergreenlendEntity.costno}" /></td>
							<td>申請單位&nbsp;<font color="red">*</font></td>
							<td colspan="4"><input id="dealdeptname"
												   style="width:380px" name="flowergreenlend.dealdeptname"
												   class="easyui-validatebox"
												   data-options="width: 380,required:true"
												   value="${flowergreenlendEntity.dealdeptname}" /></td>
							<td>聯繫分機&nbsp;<font color="red">*</font></td>
							<td colspan="2"><input id="dealtel"
												   name="flowergreenlend.dealtel" class="easyui-validatebox"
												   data-options="width: 150,required:true,prompt:'579+66666',validType:'tel[\'applytel\',\'分機格式不正確\']'"
												   value="${flowergreenlendEntity.dealtel}" /></td>
						</tr>
						<tr align="center">
							<td>聯繫郵箱&nbsp;<font color="red">*</font></td>
							<td colspan="2"><input id="dealemail"
												   name="flowergreenlend.dealemail" class="easyui-validatebox"
												   data-options="width: 250,required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"
												   value="${flowergreenlendEntity.dealemail}" /></td>
							<td>租擺位置&nbsp;<font color="red">*</font></td>
							<td colspan="3"><input id="lendposition"
												   name="flowergreenlend.lendposition" class="easyui-validatebox"
												   data-options="width: 250,required:true"
												   value="${flowergreenlendEntity.lendposition}" /></td>
							<td>租擺類別&nbsp;<font color="red">*</font></td>
							<td colspan="2">
								<div class="lendTypeDiv"></div> <input id="lendtype"
																	   name="flowergreenlend.lendtype" class="easyui-validatebox"
																	   data-options="width: 150" type="hidden"
																	   value="${flowergreenlendEntity.lendtype }" />
							</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td>
					<table class="formList">
						<tr>
							<td colspan="10" class="td_style1">租擺費用</td>
						</tr>
						<tr align="center">
							<td colspan="10" width="100%">
								<div style="overflow-x: auto;width: 1200px;">
									<input id="flowerItemTableIndex" type="hidden"
										   value="<c:if test="${itemEntity!=null && itemEntity.size()>0}">${itemEntity.size() +1}</c:if>
                                        <c:if test="${itemEntity==null||itemEntity.size()==0}">2</c:if>">
									</input>
									<table id="flowerItemTable" width="100%" border="1"
										   cellpadding="0" cellspacing="0">
										<tr align="center">
											<td>&nbsp;序號&nbsp;</td>
											<td>花卉綠植名稱&nbsp;<font color="red">*</font></td>
											<td>花卉綠植規格&nbsp;<font color="red">*</font></td>
											<td>數量&nbsp;<font color="red">*</font></td>
											<td>單價（RMB）<font color="red">*</font></td>
											<td>合計（RMB）<font color="red">*</font></td>
											<td>備註</td>
											<td>&nbsp;操作&nbsp;</td>
										</tr>
										<tbody id="info_Body">
										<c:if test="${itemEntity!=null&&itemEntity.size()>0}">
											<c:forEach items="${itemEntity}" var="item"
													   varStatus="status">
												<tr align="center" id="flowerItem${status.index+1}">
													<td>${status.index+1}<input type="hidden"
																				name="flowergreenitem[${status.index}].flowerorder"
																				value="${status.index+1}" /></td>
													<td><input id="flowername${status.index+1}"
															   name="flowergreenitem[${status.index}].flowername"
															   class="easyui-validatebox" data-options="required:true"
															   style="width: 100px;" value="${item.flowername}" /></td>
													<td><input id="flowertype${status.index+1}"
															   name="flowergreenitem[${status.index}].flowertype"
															   class="easyui-validatebox" data-options="required:true"
															   style="width: 100px;" value="${item.flowertype}" /></td>
													<td><input id="flowernum${status.index+1}"
															   name="flowergreenitem[${status.index}].flowernum" onblur="valdIsInterNumber(this)"
															   class="easyui-validatebox" data-options="required:true"
															   style="width:50px;" value="${item.flowernum}" /></td>
													<td><input id="flowerprice${status.index+1}"
															   name="flowergreenitem[${status.index}].flowerprice" onblur="valdIsNumber(this)"
															   class="easyui-validatebox" data-options="required:true"
															   style="width: 50px;" value="${item.flowerprice}" /></td>
													<td><input id="flowerpricetotal${status.index+1}"
															   name="flowergreenitem[${status.index}].flowerpricetotal"
															   class="easyui-validatebox" data-options="required:true"
															   onblur="autoSum()" style="width: 80px;"
															   value="${item.flowerpricetotal}" /></td>
													<td><input id="remark${status.index+1}"
															   name="flowergreenitem[${status.index}].remark"
															   class="easyui-validatebox"
															   style="width: 130px;" value="${item.remark}" /></td>
													<td><input type="image"
															   src="${ctx}/static/images/deleteRow.png"
															   onclick="deltr(${status.index+1});return false;" /></td>
												</tr>
											</c:forEach>
										</c:if>
										<c:if test="${itemEntity==null||itemEntity.size()==0}">
											<tr align="center" id="flowerItem1">
												<td>1<input type="hidden"
															name="flowergreenitem[0].flowerorder" value="1" /></td>
												<td><input id="flowername1"
														   name="flowergreenitem[0].flowername"
														   class="easyui-validatebox" data-options="required:true"
														   style="width:100px;" value="" /></td>
												<td><input id="flowertype1"
														   name="flowergreenitem[0].flowertype"
														   class="easyui-validatebox" data-options="required:true"
														   style="width:100px;" value="" /></td>
												<td><input id="flowernum1"
														   name="flowergreenitem[0].flowernum" onblur="valdIsInterNumber(this)"
														   class="easyui-validatebox" data-options="required:true"
														   style="width:50px;" value="" /></td>
												<td><input id="flowerprice1"
														   name="flowergreenitem[0].flowerprice" onblur="valdIsNumber(this)"
														   class="easyui-validatebox" data-options="required:true"
														   style="width:50px;" value="" /></td>
												<td><input id="flowerpricetotal1"
														   name="flowergreenitem[0].flowerpricetotal"
														   onblur="autoSum()" class="easyui-validatebox"
														   data-options="required:true" style="width:80px;" value="" /></td>
												<td><input id="remark1"
														   name="flowergreenitem[0].remark"
														   class="easyui-validatebox"
														   style="width:130px;" value="" /></td>
												<td><input type="image"
														   src="${ctx}/static/images/deleteRow.png"
														   onclick="deltr(1);return false;" /></td>
											</tr>
										</c:if>
										</tbody>
										<tr class="nottr">
											<td colspan="2" style="text-align: right;">總計:&nbsp;<font
													color="red">*</font></td>
											<td colspan="8"><input id="feesum"
																   name="flowergreenlend.feesum"
																   class="easyui-validatebox inputCss" style="color: #ff9000;"
																   data-options="width:100"
																   value="${flowergreenlendEntity.feesum}" /></td>
										</tr>
										<tr align="left" class="nottr">
											<td colspan="21" width="100%"
												style="text-align:left;padding-left:10px;"><input
													type="button" id="itemAdd" style="width:100px;float:left;"
													value="添加一筆" /></td>
										</tr>
									</table>
								</div>
							</td>
						</tr>
						<tr align="center">
							<td>附件</td>
							<td colspan="9" class="td_style1"><span
									class="sl-custom-file"> <input type="button"
																   value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
									   onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span> <input type="hidden" id="attachids"
											   name="flowergreenlend.attachids" value="${flowergreenlendEntity.attachids }"/>
								<div id="dowloadUrl">
									<c:forEach items="${file}" varStatus="i" var="item">
										<div id="${item.id}"
											 style="line-height:30px;margin-left:5px;" class="float_L">
											<div class="float_L">
												<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
											</div>
											<div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
										</div>
									</c:forEach>
								</div>
							</td>
						</tr>
						<tr align="center">
							<td colspan="2">備註</td>
							<td colspan="8" class="td_style1">請先下載《花卉綠植報價單.pdf》查看單價<a href="${ctx}/ossAdmin/download/${flowerFileId}">花卉綠植報價單.pdf</a></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td>
					<table class="formList">
						<tr>
							<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
								<a href="javascript:void(0)"
								   onclick="showWfImag('${processId}','花卉綠植申請單');">點擊查看簽核流程圖</a>
							</th>
						</tr>
						<tr>
							<td colspan="10" style="text-align:left;">
								<table class="flowList"
									   style="margin-left:5px;margin-top:5px;width:99%">
									<tr>
										<td style="border:none">
											<table width="18%" style="float: left;margin-left: 5px;">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">課級主管</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#guishufactoryid').val())"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="kchargeno" name="flowergreenlend.kchargeno"
															   class="easyui-validatebox"
															   data-options="width:80,required:${requiredMap['kchargeno']}"
															   readonly
															   value="${flowergreenlendEntity.kchargeno }"/><c:if
															test="${requiredMap['kchargeno'].equals('true')}"><font
															color="red">*</font></c:if>
														/<input id="kchargename" name="flowergreenlend.kchargename"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['kchargeno']}"
																value="${flowergreenlendEntity.kchargename }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">部級主管</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 <%--onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#guishufactoryid').val())"></div>--%>
																		 onclick="selectRole3('bchargeTable',$('#dealdeptno').val(),'bchargeno','bchargename',$('#guishufactoryid').val(),'flowergreenlend')"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="bchargeno" name="flowergreenlend.bchargeno"
															   class="easyui-validatebox"
															   data-options="width:80,required:${requiredMap['bchargeno']}"
															   readonly
															   value="${flowergreenlendEntity.bchargeno }"/><c:if
															test="${requiredMap['bchargeno'].equals('true')}"><font
															color="red">*</font></c:if>
														/<input id="bchargename" name="flowergreenlend.bchargename"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['bchargeno']}"
																value="${flowergreenlendEntity.bchargename }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">處級主管</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		<%-- onclick="selectRole($('#dealdeptno').val(),'zchargeno','zchargename',$('#guishufactoryid').val())"></div>--%>
																	onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#guishufactoryid').val(),'flowergreenlend')"></div>

																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="zchargeno" name="flowergreenlend.zchargeno"
															   class="easyui-validatebox"
															   data-options="width:80,required:${requiredMap['zchargeno']}"
															   readonly
															   value="${flowergreenlendEntity.zchargeno }"/><c:if
															test="${requiredMap['zchargeno'].equals('true')}"><font
															color="red">*</font></c:if>
														/<input id="zchargename" name="flowergreenlend.zchargename"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['zchargeno']}"
																value="${flowergreenlendEntity.zchargename }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;"
												   id="zwhdcqchargeTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">總務核定呈簽</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole5(115,'zwhdcqchargeno','zwhdcqchargename','zwpsyschargeno','zwpsyschargename',null,null,$('#dealfactoryid').combobox('getValue'))"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="zwhdcqchargeno" name="flowergreenlend.zwhdcqchargeno"
															   class="easyui-validatebox" readonly
															   data-options="width: 80,required:${requiredMap['zwhdcqchargeno']}"
															   value="${flowergreenlendEntity.zwhdcqchargeno}" /> <c:if
															test="${requiredMap['zwhdcqchargeno'].equals('true')}">
														<font color="red">*</font>
													</c:if> /<input id="zwhdcqchargename" name="flowergreenlend.zwhdcqchargename"
																	class="easyui-validatebox"
																	data-options="width: 80,required:${requiredMap['zwhdcqchargeno']}"
																	readonly value="${flowergreenlendEntity.zwhdcqchargename}" />
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;"
												   id="zwzgshchargeTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">總務主管審核</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(116,'zwzgshchargeTable','zwzgshchargeno','zwzgshchargename',$('#dealfactoryid').combobox('getValue'),'flowergreenlend',null)"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="zwzgshchargeno" name="flowergreenlend.zwzgshchargeno"
															   class="easyui-validatebox" readonly
															   data-options="width: 80,required:${requiredMap['zwzgshchargeno']}"
															   value="${flowergreenlendEntity.zwzgshchargeno}" /> <c:if
															test="${requiredMap['zwzgshchargeno'].equals('true')}">
														<font color="red">*</font>
													</c:if> /<input id="zwzgshchargename" name="flowergreenlend.zwzgshchargename"
																	class="easyui-validatebox"
																	data-options="width: 80,required:${requiredMap['zwzgshchargeno']}"
																	readonly value="${flowergreenlendEntity.zwzgshchargename}" />
													</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td style="border:none">
											<table width="18%" style="float: left;margin-left: 5px;"
												   id="zwzghzchargeTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">總務主管核准</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(117,'zwzghzchargeTable','zwzghzchargeno','zwzghzchargename',$('#dealfactoryid').combobox('getValue'),'flowergreenlend',null)"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="zwzghzchargeno" name="flowergreenlend.zwzghzchargeno"
															   class="easyui-validatebox" readonly
															   data-options="width: 80,required:${requiredMap['zwzghzchargeno']}"
															   value="${flowergreenlendEntity.zwzghzchargeno}" /> <c:if
															test="${requiredMap['zwzghzchargeno'].equals('true')}">
														<font color="red">*</font>
													</c:if> /<input id="zwzghzchargename" name="flowergreenlend.zwzghzchargename"
																	class="easyui-validatebox"
																	data-options="width: 80,required:${requiredMap['zwzghzchargeno']}"
																	readonly value="${flowergreenlendEntity.zwzghzchargename}" />
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;"
												   id="jgshchargeTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">經管審核</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(63,'jgshchargeTable','jgshchargeno','jgshchargename',$('#dealfactoryid').combobox('getValue'),'flowergreenlend',null)"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="jgshchargeno" name="flowergreenlend.jgshchargeno"
															   class="easyui-validatebox" readonly
															   data-options="width: 80,required:${requiredMap['jgshchargeno']}"
															   value="${flowergreenlendEntity.jgshchargeno}" /> <c:if
															test="${requiredMap['jgshchargeno'].equals('true')}">
														<font color="red">*</font>
													</c:if> /<input id="jgshchargename" name="flowergreenlend.jgshchargename"
																	class="easyui-validatebox"
																	data-options="width: 80,required:${requiredMap['jgshchargeno']}"
																	readonly value="${flowergreenlendEntity.jgshchargename}" />
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;" id="zgshchargeTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">主管審核</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(274,'zgshchargeTable','zgshchargeno','zgshchargename',$('#dealfactoryid').combobox('getValue'),'flowergreenlend')"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="zgshchargeno" name="flowergreenlend.zgshchargeno"
															   class="easyui-validatebox"
															   data-options="width:80,required:${requiredMap['zgshchargeno']}"
															   readonly
															   value="${flowergreenlendEntity.zgshchargeno }"/><c:if
															test="${requiredMap['zgshchargeno'].equals('true')}"><font
															color="red">*</font></c:if>
														/<input id="zgshchargename" name="flowergreenlend.zgshchargename"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['zgshchargeno']}"
																value="${flowergreenlendEntity.zgshchargename }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">總處主管</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole($('#dealdeptno').val(),'zcchargeno','zcchargename',$('#guishufactoryid').val())"></div>
																	<%--onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealfactoryid').combobox('getValue'),'flowergreenlend')"></div>--%>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="zcchargeno" name="flowergreenlend.zcchargeno"
															   class="easyui-validatebox"
															   data-options="width:80,required:${requiredMap['zcchargeno']}"
															   readonly value="${flowergreenlendEntity.zcchargeno }"/><c:if
															test="${requiredMap['zcchargeno'].equals('true')}"><font
															color="red">*</font></c:if>
														/<input id="zcchargename" name="flowergreenlend.zcchargename"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['zcchargeno']}"
																value="${flowergreenlendEntity.zcchargename }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;" id="zwpsyschargeTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: center;">總務配送驗收</td>
																<td style="border: none;">
																	<!-- <div class="float_L qhUserIcon" onclick="selectRole2(118,'jgshchargeTable','jgshchargeno','jgshchargename',$('#dealfactoryid').combobox('getValue'),'flowergreenlendEntity',null)"></div> -->
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="zwpsyschargeno" name="flowergreenlend.zwpsyschargeno"
															   class="easyui-validatebox" readonly
															   data-options="width: 80,required:${requiredMap['zwpsyschargeno']}"
															   value="${flowergreenlendEntity.zwpsyschargeno}" /> <c:if
															test="${requiredMap['zwpsyschargeno'].equals('true')}">
														<font color="red">*</font>
													</c:if> /<input id="zwpsyschargename" name="flowergreenlend.zwpsyschargename"
																	class="easyui-validatebox"
																	data-options="width: 80,required:${requiredMap['zwpsyschargeno']}"
																	readonly value="${flowergreenlendEntity.zwpsyschargename}" />
													</td>
												</tr>
											</table>

										</td>
									</tr>
								</table>
							</td>
						</tr>
						<tr>
							<td colspan="10" style="text-align:left;"><iframe
									id="qianheLogFrame" name="qianheLogFrame"
									src="${ctx}/wfcontroller/goChargeLog?serialNo=${flowergreenlendEntity.serialno}"
									width="100%"></iframe></td>
						</tr>
						<tr>
							<td colspan="10"
								style="border:none;text-align:center;margin-top:10px"><a
									href="javascript:;" id="btnSave" class="easyui-linkbutton"
									data-options="iconCls:'icon-add'" style="width: 100px;"
									onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp; <a
									href="#" id="btnSubmit" class="easyui-linkbutton"
									data-options="iconCls:'icon-ok'" style="width: 100px;"
									onclick="canelTask('${flowergreenlendEntity.serialno }');">取消申請</a>
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
	</div>
	<input type="hidden" id="deptNo" name="deptNo" value="" /> <input
		type="hidden" id="chargeNo" name="chargeNo" value="" /> <input
		type="hidden" id="chargeName" name="chargeName" value="" /> <input
		type="hidden" id="factoryId" name="factoryId" value="" /> <input
		type="hidden" id="dutyId" name="dutyId" value="" />
	<input type="hidden" id="onlyKchargeSignle" value="1" />
	<div id="win"></div>
</form>
<script
		src='${ctx}/static/js/generalAffairs/flowergreenlend.js?random=<%= Math.random()%>'></script>
</body>
</html>