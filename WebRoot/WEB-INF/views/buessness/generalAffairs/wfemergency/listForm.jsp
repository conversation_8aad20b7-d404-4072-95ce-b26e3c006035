<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>緊急叫貨申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
	type="text/javascript"></script>
</head>
<body>
	<form id="mainform" action="${ctx}/wfemergency/${action}" method="post">
		<input id="ids" name="ids" type="hidden"
			value="${wfEmergencyEntity.id }" /> <input id="serialno"
			name="wfEmergency.serialno" type="hidden"
			value="${wfEmergencyEntity.serialno }" /> <input id="makerno"
			name="wfEmergency.makerno" type="hidden"
			value="${wfEmergencyEntity.makerno }" /> <input id="makername"
			name="wfEmergency.makername" type="hidden"
			value="${wfEmergencyEntity.makername }" /> <input id="makerdeptno"
			name="wfEmergency.makerdeptno" type="hidden"
			value="${wfEmergencyEntity.makerdeptno }" /> <input
			id="makerfactoryid" name="wfEmergency.makerfactoryid" type="hidden"
			value="${wfEmergencyEntity.makerfactoryid }" />
		<div class="commonW">
			<div class="headTitle">緊急叫貨申請單</div>
			<div class="position_L">
				任務編碼：<span style="color:#999;"> <c:choose>
						<c:when test="${wfEmergencyEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
						<c:otherwise>
                           ${wfEmergencyEntity.serialno}
                       </c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_L1 margin_L">
				填單時間：<span style="color:#999;"> <c:choose>
						<c:when test="${wfEmergencyEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
						<c:otherwise>
							<input style="border:0px" style="width: 120px" readonly="true"
								value="<fmt:formatDate value='${wfEmergencyEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>" />
						</c:otherwise>
					</c:choose>
				</span>
			</div>
			<c:if test="${empty wfEmergencyEntity.makerno}">
				<div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
			</c:if>
			<c:if test="${not empty wfEmergencyEntity.makerno}">
				<div class="position_R margin_R">填單人：${wfEmergencyEntity.makerno}/${wfEmergencyEntity.makername}</div>
			</c:if>
			<div class="clear"></div>
			<table class="formList">
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">申請人基本信息</td>
							</tr>
							<tr align="center">
								<td>申請人工號&nbsp;<font color="red">*</font></td>
								<td><input id="dealno" name="wfEmergency.dealno"
									class="easyui-validatebox" onblur="queryUserInfo(this)"
									data-options="width:80,required:true"
									value="${wfEmergencyEntity.dealno}" /></td>
								<td>申請人</td>
								<td><input id="dealname" name="wfEmergency.dealname"
									readonly class="easyui-validatebox inputCss"
									data-options="width: 150" value="${wfEmergencyEntity.dealname}" /></td>
								<td>部門代碼</td>
								<td><input id="dealdeptno"
									name="wfEmergency.dealdeptno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfEmergencyEntity.dealdeptno}" /></td>
								<td>費用代碼</td>
								<td><input id="dealcostno" readonly style="width:150px"
									name="wfEmergency.dealcostno"
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfEmergencyEntity.dealcostno}" /></td>
								<td>所在廠區&nbsp;<font color="red">*</font></td>
							     <td><input id="dealfactoryid"
									name="wfEmergency.dealfactoryid"
									class="easyui-combobox" data-options="width: 100"
									value="${wfEmergencyEntity.dealfactoryid}" /><input class="easyui-validatebox"
									id="guishufactoryid" name="wfEmergency.guishufactoryid" style="width:50px" readonly type="hidden"
									value="${wfEmergencyEntity.guishufactoryid}" /></td>
							</tr>
							<tr align="center">
								<td>部門名稱</td>
								<td colspan="5"><input id="dealdeptname"
									style="width:450px" name="wfEmergency.dealdeptname"
									class="easyui-validatebox"
									data-options="width:450,required:true"
									value="${wfEmergencyEntity.dealdeptname}" /></td>
								<td>申請日期</td>
								<td><input id="applydate" style="width:150px"
									name="wfEmergency.applydate"
									class="easyui-validatebox inputCss" readonly
									data-options="width:150,required:true"
									value="${wfEmergencyEntity.applydate}" /></td>
								<td>預計補單日期&nbsp;<font color="red">*</font></td>
								<td colspan="2"><input id="reapplydate"
									name="wfEmergency.reapplydate" class="Wdate"
									data-options="width:150,required:true" style="width:150px"
									value="<fmt:formatDate  pattern="yyyy-MM-dd"
								value="${wfEmergencyEntity.reapplydate}"/>"
									onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" /></td>
							</tr>
							<tr align="center">
								<td>聯繫電話&nbsp;<font color="red">*</font></td>
								<td><input id="dealtel" name="wfEmergency.dealtel"
									class="easyui-validatebox"
									data-options="width: 100,required:true"
									value="${wfEmergencyEntity.dealtel}" /></td>
								<td>聯繫郵箱&nbsp;<font color="red">*</font></td>
								<td colspan="7" align="left"><input id="dealemail"
									name="wfEmergency.dealemail" class="easyui-validatebox"
									data-options="width: 350,required:true,validType:'email[\'wfEmergency.dealemail\',\'郵箱的格式不正確\']'"
									value="${wfEmergencyEntity.dealemail}" /></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">申請詳細信息</td>
							</tr>
							<tr align="center">
								<td width="15%">緊急叫貨說明&nbsp;<font color="red">*</font></td>
								<td align="left" width="85%"><textarea id="memo"
										name="wfEmergency.memo"
										oninput="return LessThanAuto(this,'txtNum');"
										onchange="return LessThanAuto(this,'txtNum');"
										onpropertychange="return LessThanAuto(this,'txtNum');"
										data-options="required:true" maxlength="100"
										class="easyui-validatebox" style="width:1000px;height:40px;"
										rows="5" cols="6">${wfEmergencyEntity.memo}</textarea><span
									id="txtNum"></span></td>
							</tr>
							<tr align="center">
								<td colspan="10" width="100%">
									<div style="overflow-x: auto;width:1200px">
										<input id="wfEmergencyItemTableIndex" type="hidden"
											value="<c:if test="${itemEntity!=null && itemEntity.size()>0}">${itemEntity.size() +1}</c:if>
                                        <c:if test="${itemEntity==null}">2</c:if>">
										</input>
										<table id="wfEmergencyItemTable" width="100%">
											<tr align="center">
												<td width="3%">&nbsp;序號&nbsp;</td>
												<td>ECS料號&nbsp;<font color="red">*</font></td>
												<td>料號類型&nbsp;<font color="red">*</font></td>
												<td>品名&nbsp;<font color="red">*</font></td>
												<td>品牌&nbsp;<font color="red">*</font></td>
												<td>規格&nbsp;<font color="red">*</font></td>
												<td>計量單位&nbsp;<font color="red">*</font></td>
												<td>單價(元)&nbsp;<font color="red">*</font></td>
												<td>需求數量&nbsp;<font color="red">*</font></td>
												<td>總金額（元）<font color="red">*</font></td>
												<td>供應商&nbsp;<font color="red">*</font></td>
												<td>備註</td>
												<td>&nbsp;操作&nbsp;</td>
											</tr>
											<tbody id="info_Body">
												<c:if test="${itemEntity!=null&&itemEntity.size()>0}">
													<c:forEach items="${itemEntity}" var="item"
														varStatus="status">
														<tr align="center" id="emergencyItem${status.index+1}">
															<td>${status.index+1}<input type="hidden"
																name="wfEmergencyitem[${status.index}].itemorder"
																value="${status.index+1}" /></td>
															<td><input id="ecspartno${status.index+1}"
																name="wfEmergencyitem[${status.index}].ecspartno"
																class="easyui-validatebox" data-options="required:true"
																style="width:100px;" value="${item.ecspartno}" /></td>
															<td><div class="partnotypeDiv${status.index+1}"></div>
															    <input id="partnotype${status.index+1}"
																name="wfEmergencyitem[${status.index}].partnotype" readonly type="hidden"
																class="easyui-validatebox" data-options="required:true"
																style="width:50px;" value="${item.partnotype}" /></td>
															<td><input id="productname${status.index+1}"
																name="wfEmergencyitem[${status.index}].productname"
																class="easyui-validatebox" data-options="required:true"
																style="width:50px;" value="${item.productname}" /></td>
															<td><input id="brand${status.index+1}"
																name="wfEmergencyitem[${status.index}].brand"
																class="easyui-validatebox" data-options="required:true"
																style="width:50px;" value="${item.brand}" /></td>
															<td><input id="specifications${status.index+1}"
																name="wfEmergencyitem[${status.index}].specifications"
																class="easyui-validatebox" data-options="required:true"
																style="width:80px;" value="${item.specifications}" /></td>
															<td><input id="unitofmeasuerment${status.index+1}"
																name="wfEmergencyitem[${status.index}].unitofmeasuerment"
																class="easyui-validatebox" data-options="required:true"
																style="width:50px;" value="${item.unitofmeasuerment}" /></td>
															<td><input id="price${status.index+1}" 
																name="wfEmergencyitem[${status.index}].price" onchange="autoTotalprice(this)"
																class="easyui-validatebox" data-options="required:true"
																style="width:50px;" value="${item.price}" /></td>
															<td><input id="num${status.index+1}"
																name="wfEmergencyitem[${status.index}].num"  onchange="autoTotalprice(this)"
																class="easyui-validatebox" data-options="required:true"
																style="width:50px;" value="${item.num}" /></td>
															<td><input id="totalprice${status.index+1}" 
																name="wfEmergencyitem[${status.index}].totalprice" readonly
																class="easyui-validatebox inputCss" data-options="required:true"
																style="width:80px;" value="${item.totalprice}" /></td>
															<td><input id="supplier${status.index+1}"
																name="wfEmergencyitem[${status.index}].supplier"
																class="easyui-validatebox" data-options="required:true"
																style="width:90px;" value="${item.supplier}" /></td>
															<td><input id="remark${status.index+1}"
																name="wfEmergencyitem[${status.index}].remark"
																class="easyui-validatebox"
																style="width:90px;" value="${item.remark}" /></td>
															<td><input type="image"
																src="${ctx}/static/images/deleteRow.png"
																onclick="deltr(${status.index+1});return false;" /></td>
														</tr>
													</c:forEach>
												</c:if>
												<c:if test="${itemEntity==null}">
													<tr align="center" id="emergencyItem1">
														<td>${status.index+1}<input type="hidden"
															name="wfEmergencyitem[0].itemorder" value="1" /></td>
														<td><input id="ecspartno1"
															name="wfEmergencyitem[0].ecspartno"
															class="easyui-validatebox" data-options="required:true"
															style="width: 100px;" value="" /></td>
														<td><div class="partnotypeDiv1"></div>
														    <input id="partnotype1"
															name="wfEmergencyitem[0].partnotype" readonly type="hidden"
															class="easyui-validatebox" data-options="required:true"
															style="width:50px;" value="" /></td>
														<td><input id="productname1"
															name="wfEmergencyitem[0].productname"
															class="easyui-validatebox" data-options="required:true"
															style="width:50px;" value="" /></td>
														<td><input id="brand1"
															name="wfEmergencyitem[0].brand"
															class="easyui-validatebox" data-options="required:true"
															style="width:50px;" value="" /></td>
														<td><input id="specifications1"
															name="wfEmergencyitem[0].specifications"
															class="easyui-validatebox" data-options="required:true"
															style="width:80px;" value="" /></td>
														<td><input id="unitofmeasuerment1"
															name="wfEmergencyitem[0].unitofmeasuerment"
															class="easyui-validatebox" data-options="required:true"
															style="width:50px;" value="" /></td>
														<td><input id="price1"
															name="wfEmergencyitem[0].price" onchange="autoTotalprice(this)"
															class="easyui-validatebox" data-options="required:true"
															style="width:50px;" value="" /></td>
														<td><input id="num1" name="wfEmergencyitem[0].num" onchange="autoTotalprice(this)"
															class="easyui-validatebox" data-options="required:true"
															style="width:50px;" value="" /></td>
														<td><input id="totalprice1"
															name="wfEmergencyitem[0].totalprice" readonly
															class="easyui-validatebox inputCss" data-options="required:true"
															style="width:80px;" value="" /></td>
														<td><input id="supplier1"
															name="wfEmergencyitem[0].supplier"
															class="easyui-validatebox" data-options="required:true"
															style="width:90px;" value="" /></td>
														<td><input id="remark1"
															name="wfEmergencyitem[0].remark"
															class="easyui-validatebox"
															style="width:90px;" value="" /></td>
														<td><input type="image"
															src="${ctx}/static/images/deleteRow.png"
															onclick="deltr(1);return false;" /></td>
													</tr>
												</c:if>
											</tbody>
											<tr align="left" class="nottr">
												<td colspan="21" width="100%"
													style="text-align:left;padding-left:10px;"><input
													type="button" id="itemAdd" style="width:100px;float:left;"
													value="添加一筆" /></td>
											</tr>
										</table>
									</div>
								</td>
							</tr>
							<tr align="center">
								<td>附件</td>
								<td colspan="9" class="td_style1">
									<span class="sl-custom-file"> <input type="button" value="点击上传文件" class="btn-file" />
									<input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file" />
						            </span>
									<c:choose>
									<c:when test="${file.size()>0}">
										<input type="hidden" id="attachids" name="wfEmergency.attachids"
											   value="${wfEmergencyEntity.attachids }"/>
									</c:when>
									<c:otherwise>
										<input type="hidden" id="attachids" name="wfEmergency.attachids" value="" />
									</c:otherwise>
								</c:choose>
									<div id="dowloadUrl">
										<c:forEach items="${file}" varStatus="i" var="item">
											<div id="${item.id}" style="line-height:30px;margin-left:5px;"
												 class="float_L">
												<div class="float_L">
													<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
												</div>
												<div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
											</div>
										</c:forEach>
									</div></td>
							</tr>
							<c:choose>
							<c:when test="${not empty factoryType&&(('IPETY' eq factoryType) || ('CAATY' eq factoryType) || ('IPEJC' eq factoryType) || ('CAAJC' eq factoryType) )}">
							<tr style="height:135px">
								<td align="center" width="5%">備註</td>
								<td align="left" width="95%">1.緊急叫貨申請單由各需求單位查ECS系統準確填寫申請信息，需求單位各處（部）權限主管審核、會簽太原總務處及需求單位經管權限主管，最後由事業處財務核決權限主管核准，核准后經庶務採購部依此單緊急叫貨供應；<br />
									2.驗貨時供應商憑此份緊急叫貨申請單至收貨中心登記及驗貨，如需求物品為具體料號，則同一申請單同一廠商下物品總金額大于1000元時，周邊經管參與驗收并作系統驗收審核、領用審核；如需求物品為類別料號，則不論物品金額大小，周邊經管均參與驗收并作系統驗收審核、領用審核；<br />
									3.需求部門在ECS系統補請購單時需確保項目、規格、金額、數量、廠商等信息和緊急叫貨單一一對應，否則收貨中心將做退單處理；<br />
									4.需求部門補請購單后,供貨商需及時憑簽核完畢的採購訂單及相應緊急叫貨單另行至收貨中心登記，進行系統驗收及帳目清理作業。<br /></td>
							</tr>
							</c:when>
							<c:otherwise>
								<tr style="height:105px">
									<td align="center" width="5%">備註</td>
									<td align="left" width="95%">
										1.此申請單只作為緊急採購需求使用，需求物料品名、品牌、規格需正確詳細填寫，并請合理考量需求日期;<br/>
										2.供應商可憑此份緊急叫貨申請單至IQC和收貨中心驗貨；<br/>
										3.需求單位在ECS系統補請購單時需確保品名、規格、數量、金額、廠商等信息和緊急叫貨申請單一一對應，否則將做退單處理；<br/>
										4.此申請單由需求單位處級權限主管審核、會簽總務採購及經管權限主管，最後由事業處財務核決權限主管核准，核准后總務採購依此單緊急叫貨供應；<br/>
										5.此申請單中涉及價格及金額均為未稅價。<br/>
									</td>
								</tr>
							</c:otherwise>
							</c:choose>
						</table>
					</td>
				</tr>

				<tr>
					<td>
						<table class="formList">
							<tr>
								<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
									<a href="javascript:void(0)"
									onclick="showWfImag('dzqh_jinjijiaohuoshenqingdan_v2','緊急叫貨申請單','');">點擊查看簽核流程圖</a>
								</th>
							</tr>
							<tr>
								<td colspan="10" style="text-align:left;">
									<table class="flowList"
										style="margin-left:5px;margin-top:5px;width:99%">
										<tr>
											<td style="border:none">
												<table width="16%" style="float: left;margin-left: 5px;">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">課級主管</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#guishufactoryid').val())"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="kchargeno"
															name="wfEmergency.kchargeno" class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['kchargeno']}"
															readonly value="${wfEmergencyEntity.kchargeno }" /><c:if test="${requiredMap['kchargeno'].equals('true')}"><font color="red">*</font>
															</c:if> /<input id="kchargename" name="wfEmergency.kchargename"
															readonly class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['kchargeno']}"
															value="${wfEmergencyEntity.kchargename }" /></td>
													</tr>
												</table>
												<table width="16%" style="float: left;margin-left: 5px;">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">部級主管</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#guishufactoryid').val())"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="bchargeno"
															name="wfEmergency.bchargeno" class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['bchargeno']}"
															readonly value="${wfEmergencyEntity.bchargeno }" /><c:if test="${requiredMap['bchargeno'].equals('true')}"><font color="red">*</font>
															</c:if> /<input id="bchargename" name="wfEmergency.bchargename"
															readonly class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['bchargeno']}"
															value="${wfEmergencyEntity.bchargename }" /></td>
													</tr>
												</table>
												<table width="16%" style="float: left;margin-left: 5px;">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">廠級主管</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#guishufactoryid').val())"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="cchargeno"
															name="wfEmergency.cchargeno" class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['cchargeno']}"
															readonly value="${wfEmergencyEntity.cchargeno }" />
														<c:if test="${requiredMap['cchargeno'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="cchargename" name="wfEmergency.cchargename"
															readonly class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['cchargeno']}"
															value="${wfEmergencyEntity.cchargename }" /></td>
													</tr>
												</table>
												<table width="16%" style="float: left;margin-left: 5px;"
													id="zchargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">製造處級主管</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#guishufactoryid').val(),'wfEmergency')"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="zchargeno"
															name="wfEmergency.zchargeno" class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['zchargeno']}"
															readonly value="${wfEmergencyEntity.zchargeno }" />
														<c:if test="${requiredMap['zchargeno'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="zchargename" name="wfEmergency.zchargename"
															readonly class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['zchargeno']}"
															value="${wfEmergencyEntity.zchargename }" /></td>
													</tr>
												</table>
												<table width="16%" style="float: left;margin-left: 5px;"
													id="cgckchargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">採購窗口確認</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																		onclick="selectRole5(147,'cgckchargeno','cgckchargename','cgckqrrchargeno','cgckqrrchargename','pochargeno','pochargename',$('#dealfactoryid').combobox('getValue'))"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="cgckchargeno" name="wfEmergency.cgckchargeno"
															class="easyui-validatebox" readonly
															data-options="width: 80,required:${requiredMap['cgckchargeno']}"
															value="${wfEmergencyEntity.cgckchargeno}" /><c:if
																test="${requiredMap['cgckchargeno'].equals('true')}"><font color="red">*</font>
															</c:if> /<input id="cgckchargename" name="wfEmergency.cgckchargename"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['cgckchargeno']}"
															readonly value="${wfEmergencyEntity.cgckchargename}" />
														</td>
													</tr>
												</table>
												<table width="16%" style="float: left;margin-left: 5px;"
													id="cgzgchargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">採購主管審核</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole4(148,'cgzgchargeno','cgzgchargename',$('#dealfactoryid').combobox('getValue'))"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="cgzgchargeno" name="wfEmergency.cgzgchargeno"
															class="easyui-validatebox" readonly
															data-options="width: 80,required:${requiredMap['cgzgchargeno']}"
															value="${wfEmergencyEntity.cgzgchargeno}" /><c:if
																test="${requiredMap['cgzgchargeno'].equals('true')}"><font color="red">*</font></c:if> /<input id="cgzgchargename" name="wfEmergency.cgzgchargename"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['cgzgchargeno']}"
															readonly value="${wfEmergencyEntity.cgzgchargename}" />
														</td>
													</tr>
												</table>
												</td>
												</tr>
												<tr>
												<td style="border:none">
												<table width="16%" style="float: left;margin-left: 5px;"
													id="cgzghzchargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">採購主管核准</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole4(149,'cgzghzchargeno','cgzghzchargename',$('#dealfactoryid').combobox('getValue'))"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="cgzghzchargeno" name="wfEmergency.cgzghzchargeno"
															class="easyui-validatebox" readonly
															data-options="width: 80,required:${requiredMap['cgzghzchargeno']}"
															value="${wfEmergencyEntity.cgzghzchargeno}" /><c:if
																test="${requiredMap['cgzghzchargeno'].equals('true')}"><font color="red">*</font>
															</c:if> /<input id="cgzghzchargename" name="wfEmergency.cgzghzchargename"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['cgzghzchargeno']}"
															readonly value="${wfEmergencyEntity.cgzghzchargename}" />
														</td>
													</tr>
												</table>
												<table width="16%" style="float: left;margin-left: 5px;"
													id="jgshchargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">經管審核<a href="#" onclick="addRowEmergency('jgshcharge')">添加一位</a></td>												
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="jgshchargeno" name="wfEmergency.jgshchargeno"
															class="easyui-validatebox" onblur="getUserNameByEmpnoComm(this);"
															data-options="width:70,required:${requiredMap['jgshchargeno']}"
															value="${wfEmergencyEntity.jgshchargeno}" /><c:if
																test="${requiredMap['jgshchargeno'].equals('true')}"><font color="red">*</font>
															</c:if> /<input id="jgshchargename" name="wfEmergency.jgshchargename"
															class="easyui-validatebox"
															data-options="width:70,required:${requiredMap['jgshchargeno']}"
															readonly value="${wfEmergencyEntity.jgshchargename}" />
														</td>
													</tr>
												</table>
												<table width="16%" style="float: left;margin-left: 5px;" id="shchargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">審核</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			 onclick="selectRole2(159,'shchargeTable','shchargeno','shchargename',$('#dealfactoryid').combobox('getValue'),'wfEmergency',null)"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="shchargeno" name="wfEmergency.shchargeno"
																   class="easyui-validatebox" readonly
																   data-options="width: 80,required:${requiredMap['shchargeno']}"
																   value="${wfEmergencyEntity.shchargeno}" /><c:if
																test="${requiredMap['shchargeno'].equals('true')}"><font color="red">*</font>
														</c:if> /<input id="shchargename" name="wfEmergency.shchargename"
																		class="easyui-validatebox"
																		data-options="width: 80,required:${requiredMap['shchargeno']}"
																		readonly value="${wfEmergencyEntity.shchargename}" />
														</td>
													</tr>
												</table>
												<table width="16%" style="float: left;margin-left: 5px;"
													id="jghzchargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">經管核准</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole2(150,'jghzchargeTable','jghzchargeno','jghzchargename',$('#dealfactoryid').combobox('getValue'),'wfEmergency',null)"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="jghzchargeno" name="wfEmergency.jghzchargeno"
															class="easyui-validatebox" readonly
															data-options="width: 80,required:${requiredMap['jghzchargeno']}"
															value="${wfEmergencyEntity.jghzchargeno}" /><c:if
																test="${requiredMap['jghzchargeno'].equals('true')}"><font color="red">*</font>
															</c:if> /<input id="jghzchargename" name="wfEmergency.jghzchargename"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['jghzchargeno']}"
															readonly value="${wfEmergencyEntity.jghzchargename}" />
														</td>
													</tr>
												</table>

												<table width="16%" style="float: left;margin-left: 5px;" id="zgshchargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">主管審核</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			 onclick="selectRole2(274,'zgshchargeTable','zgshchargeno','zgshchargename',$('#dealfactoryid').combobox('getValue'),'wfEmergency')"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="zgshchargeno" name="wfEmergency.zgshchargeno"
																   class="easyui-validatebox"
																   data-options="width:80,required:${requiredMap['zgshchargeno']}"
																   readonly
																   value="${wfEmergencyEntity.zgshchargeno }"/><c:if
																test="${requiredMap['zgshchargeno'].equals('true')}"><font
																color="red">*</font></c:if>
															/<input id="zgshchargename" name="wfEmergency.zgshchargename"
																	readonly class="easyui-validatebox"
																	data-options="width:80,required:${requiredMap['zgshchargeno']}"
																	value="${wfEmergencyEntity.zgshchargename }"/>
														</td>
													</tr>
												</table>
												<table width="16%" style="float: left;margin-left: 5px;"
													id="hzchargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">核准</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole2(151,'hzchargeTable','hzchargeno','hzchargename',$('#dealfactoryid').combobox('getValue'),'wfEmergency',null)"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="hzchargeno" name="wfEmergency.hzchargeno"
															class="easyui-validatebox" readonly
															data-options="width: 80,required:${requiredMap['hzchargeno']}"
															value="${wfEmergencyEntity.hzchargeno}" /> <c:if
																test="${requiredMap['hzchargeno'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="hzchargename" name="wfEmergency.hzchargename"
															class="easyui-validatebox"
															data-options="width: 80,required:${requiredMap['hzchargeno']}"
															readonly value="${wfEmergencyEntity.hzchargename}" />
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td style="border:none">
												<table width="16%" style="float: left;margin-left: 5px;" id="jczhchargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">敬呈知會</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			 onclick="selectRole2(269,'jczhchargeTable','jczhchargeno','jczhchargename',$('#dealfactoryid').combobox('getValue'),'wfEmergency')"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td>
															<input id="jczhchargeno"
																   name="wfEmergency.jczhchargeno"
																   class="easyui-validatebox"
																   data-options="width: 80,required:${requiredMap['jczhchargeno']}" readonly
																   value="${wfEmergencyEntity.jczhchargeno }"/><c:if test="${requiredMap['jczhchargeno'].equals('true')}"><font color="red">*</font></c:if>
															/
															<input id="jczhchargename" name="wfEmergency.jczhchargename"
																   class="easyui-validatebox" data-options="width: 80,required:${requiredMap['jczhchargeno']}"
																   value="${wfEmergencyEntity.jczhchargename }"/>
														</td>
													</tr>
												</table>
												<table width="16%" style="float: left;margin-left: 5px;"
													   id="cgckqrrchargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">採購窗口確認人派單</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="cgckqrrchargeno" name="wfEmergency.cgckqrrchargeno"
																   class="easyui-validatebox" readonly
																   data-options="width: 80,required:${requiredMap['cgckqrrchargeno']}"
																   value="${wfEmergencyEntity.cgckqrrchargeno}" /> <c:if
																test="${requiredMap['cgckqrrchargeno'].equals('true')}">
															<font color="red">*</font>
														</c:if> /<input id="cgckqrrchargename" name="wfEmergency.cgckqrrchargename"
																		class="easyui-validatebox"
																		data-options="width: 80,required:${requiredMap['cgckqrrchargeno']}"
																		readonly value="${wfEmergencyEntity.cgckqrrchargename}" />
														</td>
													</tr>
												</table>
												<table width="16%" style="float: left;margin-left: 5px;"
													   id="pochargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: center;">PO單作業</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="pochargeno" name="wfEmergency.pochargeno"
																   class="easyui-validatebox" readonly
																   data-options="width: 80,required:${requiredMap['pochargeno']}"
																   value="${wfEmergencyEntity.pochargeno}" /> <c:if
																test="${requiredMap['pochargeno'].equals('true')}">
															<font color="red">*</font>
														</c:if> /<input id="pochargename" name="wfEmergency.pochargename"
																		class="easyui-validatebox"
																		data-options="width: 80,required:${requiredMap['pochargeno']}"
																		readonly value="${wfEmergencyEntity.pochargename}" />
														</td>
													</tr>
												</table>
											</td>
										</tr>
									</table>
								</td>
							</tr>
							<tr>
								<td colspan="10" style="text-align:left;">
									<table class="flowList"
										style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
										<tr>
											<td>簽核時間</td>
											<td>簽核節點</td>
											<td>簽核主管</td>
											<td>簽核意見</td>
											<td>批註</td>
											<td>簽核電腦IP</td>
										</tr>
									</table>
								</td>
							</tr>
							<tr>
								<td colspan="10"
									style="border:none;text-align:center;margin-top:10px"><a
									href="javascript:;" id="btnSave" class="easyui-linkbutton"
									data-options="iconCls:'icon-add'" style="width: 100px;"
									onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp; <a
									href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
									data-options="iconCls:'icon-ok'" style="width: 100px;"
									onclick="saveInfo(2);">提交</a></td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</div>
		<input type="hidden" id="deptNo" name="deptNo" value="" /> <input
			type="hidden" id="chargeNo" name="chargeNo" value="" /> <input
			type="hidden" id="chargeName" name="chargeName" value="" /> <input
			type="hidden" id="factoryId" name="factoryId" value="" /> <input
			type="hidden" id="dutyId" name="dutyId" value="" />
		<div id="win"></div>
	</form>
	<script
		src='${ctx}/static/js/generalAffairs/wfemergency.js?random=<%= Math.random()%>'></script>
</body>
</html>