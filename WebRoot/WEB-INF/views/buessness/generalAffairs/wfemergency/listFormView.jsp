<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>緊急叫貨申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfemergency/${action}" method="post">
    <input id="ids" name="ids" type="hidden"
           value="${wfEmergencyEntity.id }"/> <input id="serialno"
                                                     name="serialno" type="hidden"
                                                     value="${wfEmergencyEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">緊急叫貨申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;"> <c:choose>
            <c:when test="${wfEmergencyEntity.serialno==null}">
                提交成功后自動編碼
            </c:when>
            <c:otherwise>
                ${wfEmergencyEntity.serialno}
            </c:otherwise>
        </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;"> <c:choose>
            <c:when test="${wfEmergencyEntity.createtime==null}">
                YYYY/MM/DD
            </c:when>
            <c:otherwise>
							<input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfEmergencyEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
            </c:otherwise>
        </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfEmergencyEntity.makerno}/${wfEmergencyEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td>申請人工號</td>
                            <td><input id="dealno" name="wfEmergency.dealno" readonly
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80,required:true"
                                       value="${wfEmergencyEntity.dealno}"/></td>
                            <td width="6%">申請人</td>
                            <td><input id="dealname" name="wfEmergency.dealname"
                                       readonly class="easyui-validatebox inputCss"
                                       data-options="width: 150" value="${wfEmergencyEntity.dealname}"/></td>
                            <td width="6%">部門代碼</td>
                            <td><input id="dealdeptno" name="wfEmergency.dealdeptno"
                                       readonly class="easyui-validatebox inputCss"
                                       data-options="width: 100"
                                       value="${wfEmergencyEntity.dealdeptno}"/></td>
                            <td width="6%">費用代碼</td>
                            <td><input id="dealcostno" readonly style="width:100px"
                                       name="wfEmergency.dealcostno"
                                       class="easyui-validatebox inputCss" data-options="width: 100"
                                       value="${wfEmergencyEntity.dealcostno}"/></td>
                            <td>所在廠區</td>
                            <td><input id="dealfactoryid"
                                       name="wfEmergency.dealfactoryid" class="easyui-combobox"
                                       data-options="width: 70" disabled
                                       value="${wfEmergencyEntity.dealfactoryid}"/><input
                                    class="easyui-validatebox inputCss" readonly type="hidden"
                                    id="guishufactoryid" name="wfEmergency.guishufactoryid"
                                    style="width:50px" value="${wfEmergencyEntity.guishufactoryid}"/></td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱</td>
                            <td colspan="5"><input id="dealdeptname"
                                                   style="width:450px" name="wfEmergency.dealdeptname"
                                                   class="easyui-validatebox inputCss" readonly
                                                   data-options="width:450,required:true"
                                                   value="${wfEmergencyEntity.dealdeptname}"/></td>
                            <td>申請日期</td>
                            <td><input id="applydate" style="width:100px"
                                       name="wfEmergency.applydate"
                                       class="easyui-validatebox inputCss" readonly
                                       data-options="width:100,required:true"
                                       value="${wfEmergencyEntity.applydate}"/></td>
                            <td>預計補單日期</td>
                            <td colspan="2"><input id="reapplydate" disabled
                                                   name="wfEmergency.reapplydate" class="Wdate"
                                                   data-options="width:100,required:true" style="width:100px"
                                                   value="<fmt:formatDate  pattern="yyyy-MM-dd"
								value="${wfEmergencyEntity.reapplydate}"/>"
                                                   onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫電話</td>
                            <td><input id="dealtel" name="wfEmergency.dealtel"
                                       class="easyui-validatebox inputCss" readonly
                                       data-options="width: 90,required:true"
                                       value="${wfEmergencyEntity.dealtel}"/></td>
                            <td>聯繫郵箱</td>
                            <td colspan="7" align="left"><input id="dealemail"
                                                                name="wfEmergency.dealemail"
                                                                class="easyui-validatebox inputCss" readonly
                                                                data-options="width: 280,required:true,validType:'email[\'wfEmergency.dealemail\',\'郵箱的格式不正確\']'"
                                                                value="${wfEmergencyEntity.dealemail}"/></td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">緊急叫貨說明</td>
                            <td align="left" width="90%"><textarea id="memo"
                                                                   name="wfEmergency.memo" readonly
                                                                   data-options="required:true" maxlength="100"
                                                                   class="easyui-validatebox"
                                                                   style="width:800px;height:50px;"
                                                                   rows="5"
                                                                   cols="6">${wfEmergencyEntity.memo}</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;">
                                    <input id="wfEmergencyItemTableIndex" type="hidden"
                                           value="<c:if test="${itemEntity!=null && itemEntity.size()>0}">${itemEntity.size() +1}</c:if>
                                        <c:if test="${itemEntity==null}">2</c:if>">
                                    </input>
                                    <table id="wfEmergencyItemTable" width="100%">
                                        <tr align="center">
                                            <td width="3%">&nbsp;序號&nbsp;</td>
                                            <td width="10%">ECS料號</td>
                                            <td width="8%">料號類型</td>
                                            <td width="8%">品名</td>
                                            <td width="8%">品牌</td>
                                            <td width="8%">規格</td>
                                            <td width="6%">計量單位</td>
                                            <td width="6%">單價(元)</td>
                                            <td width="6%">需求數量</td>
                                            <td width="6%">總金額（元）</td>
                                            <td width="8%">供應商</td>
                                            <td width="6%">備註</td>
                                        </tr>
                                        <tbody id="info_Body">
                                        <c:if test="${itemEntity!=null&&itemEntity.size()>0}">
                                            <c:forEach items="${itemEntity}" var="item"
                                                       varStatus="status">
                                                <tr align="center" id="emergencyItem${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>${item.ecspartno}</td>
                                                    <td>
                                                        <div class="partnotypeDiv${status.index+1}"></div>
                                                        <input id="partnotype${status.index+1}"
                                                               name="wfEmergencyitem[${status.index}].partnotype"
                                                               readonly type="hidden"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width:50px;" value="${item.partnotype}"/> <input
                                                            type="hidden" id="disOrEnabled" value="disabled"/></td>
                                                    <td>${item.productname}</td>
                                                    <td>${item.brand}</td>
                                                    <td>${item.specifications}</td>
                                                    <td>${item.unitofmeasuerment}</td>
                                                    <td>${item.price}</td>
                                                    <td>${item.num}</td>
                                                    <td>${item.totalprice}</td>
                                                    <td>${item.supplier}</td>
                                                    <td>${item.remark}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${itemEntity==null}">
                                            <tr align="center" id="emergencyItem1">
                                                <td>1</td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr" style="display:none">
                                            <td colspan="21" width="100%"></td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <%-- <c:if test="${not empty ispomemo}"> --%>
                        <tr align="center">
                            <td width="10%">PO單作業</td>
                            <td align="left" width="90%"><textarea id="pomemo"
                                                                   name="wfEmergency.pomemo" readonly
                                                                   maxlength="50"
                                                                   class="easyui-validatebox"
                                                                   style="width:800px;height:50px;"
                                                                   rows="5"
                                                                   cols="6">${wfEmergencyEntity.pomemo}</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1"><input type="hidden"
                                                                     id="attachids" name="attachids"
                                                                     value="${wfEmergencyEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <%-- </c:if> --%>
                        <c:choose>
                            <c:when test="${not empty factoryType&&(('IPETY' eq factoryType) || ('CAATY' eq factoryType) || ('IPEJC' eq factoryType) || ('CAAJC' eq factoryType) )}">
                                <tr style="height:135px">
                                    <td align="center" width="10%">備註</td>
                                    <td align="left" width="90%">
                                        1.緊急叫貨申請單由各需求單位查ECS系統準確填寫申請信息，需求單位各處（部）權限主管審核、會簽太原總務處及需求單位經管權限主管，最後由事業處財務核決權限主管核准，核准后經庶務採購部依此單緊急叫貨供應；<br/>
                                        2.驗貨時供應商憑此份緊急叫貨申請單至收貨中心登記及驗貨，如需求物品為具體料號，則同一申請單同一廠商下物品總金額大于1000元時，周邊經管參與驗收并作系統驗收審核、領用審核；如需求物品為類別料號，則不論物品金額大小，周邊經管均參與驗收并作系統驗收審核、領用審核；<br/>
                                        3.需求部門在ECS系統補請購單時需確保項目、規格、金額、數量、廠商等信息和緊急叫貨單一一對應，否則收貨中心將做退單處理；<br/>
                                        4.需求部門補請購單后,供貨商需及時憑簽核完畢的採購訂單及相應緊急叫貨單另行至收貨中心登記，進行系統驗收及帳目清理作業。<br/></td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <tr style="height:105px">
                                    <td align="center" width="5%">備註</td>
                                    <td align="left" width="95%">
                                        1.此申請單只作為緊急採購需求使用，需求物料品名、品牌、規格需正確詳細填寫，并請合理考量需求日期;<br/>
                                        2.供應商可憑此份緊急叫貨申請單至IQC和收貨中心驗貨；<br/>
                                        3.需求單位在ECS系統補請購單時需確保品名、規格、數量、金額、廠商等信息和緊急叫貨申請單一一對應，否則將做退單處理；<br/>
                                        4.此申請單由需求單位處級權限主管審核、會簽總務採購及經管權限主管，最後由事業處財務核決權限主管核准，核准后總務採購依此單緊急叫貨供應；<br/>
                                        5.此申請單中涉及價格及金額均為未稅價。<br/>
                                    </td>
                                </tr>
                            </c:otherwise>
                        </c:choose>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','緊急叫貨申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}</td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe
                                        id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfEmergencyEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'" style="width: 100px;"
                                   onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp; <%-- <c:if
										test="${wfEmergencyEntity.workstatus!=null&&wfEmergencyEntity.workstatus==3}"> --%>
                                <a href="#" id="btnPrint" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-print'" style="width: 100px;"
                                   onclick="printWindow_emergency('btnClose,btnPrint');">列印</a>
                                <%-- </c:if> --%>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<div id="dlg"></div>
<script
        src='${ctx}/static/js/generalAffairs/wfemergency.js?random=<%= Math.random()%>'></script>
</body>
</html>