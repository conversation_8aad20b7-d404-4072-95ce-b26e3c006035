<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>園區內班車專案用車申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/externalrentalincar/${action}" method="post">
       <!--
		               serialno 任務編號
                   makerno 填單人工號
                   makername 填單人名稱
                   createtime 填單時間
                   complettime 簽核完成時間
                   workstatus 表單狀態
                   attachids 附件Id
                   createBy 創建人
                   createDate 創建時間
                   updateBy 更新者
                   updateDate 更新時間
                   delFlag 刪除標識
                   makerdeptno 填單人所在部門
                   applyno 申請人工號
                   applyname 申請人姓名
                   applydeptno 申請人單位代碼
                   applydeptname 申請人單位名稱
                   applyfactoryid 申請人廠區id
                   applyemail 申請人郵箱
                   applytel 申請人電話
                   applycostno 申請人費用代碼
                   id 主鍵
                   theCarFor 用車事由
                   numberOfBus 乘車人數
                   principal 負責人
                   contactInformation 聯繫方式
                   transportStartTime 用車開始時間
                   transportEndTime 用車結束時間
                   driveCircuit 行車路線
                   specialRequests 特別要求
                   kchargeno 課級主管
                   kchargename 課級主管姓名
                   bchargeno 部級主管
                   bchargename 部級主管姓名
                   cchargeno 廠級主管
                   cchargename 廠級主管姓名
                   zchargeno 製造處級主管
                   zchargename 製造處級主管姓名
                   cdzxchargeno 車調中心評估
                   cdzxchargename 車調中心評估姓名
                   ghjgchargeno 工會經管
                   ghjgchargename 工會經管姓名
                   jgchargeno 經管審核
                   jgchargename 經管審核姓名
                   zcchargeno 製造總處級主管
                   zcchargename 製造總處級主管
                   pcchargeno 產品處級主管工號
                   pcchargename 產品處級主管姓名
                   cdxdchargeno 車調中心銷單
                   cdxdchargename 車調中心銷單
                   cdxdshchargeno 車調中心銷單審核
                   cdxdshchargename 車調中心銷單審核
                   cdxdhzchargeno 車調中心銷單核准
                   cdxdhzchargename 車調中心銷單核准
                   apcchargeno 申請人確認
                   apcchargename 申請人確認
        		   -->
    <input id="ids" name="ids" type="hidden" value="${externalRentalInCarEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${externalRentalInCarEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">園區內班車專案用車申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${externalRentalInCarEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${externalRentalInCarEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${externalRentalInCarEntity.createDate==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${externalRentalInCarEntity.createDate}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${externalRentalInCarEntity.makerno}/${externalRentalInCarEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號</td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 150,disabled:true"
                                       value="${externalRentalInCarEntity.applyno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${externalRentalInCarEntity.applyname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${externalRentalInCarEntity.applydeptno }"/>
                            </td>
                            <td width="3%">費用代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applycostno" name="externalRentalInCarEntity.applycostno"
                                       class="easyui-validatebox" data-options="width: 120,disabled:true"
                                       value="${externalRentalInCarEntity.applycostno }"/>
                            </td>
                            <td width="4%">所在廠區</td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${externalRentalInCarEntity.applyfactoryid }"
                                       data-options="width: 120,validType:'comboxValidate[\'applyfactoryid\',\'请选择所在廠區\']',disabled:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox"
                                       style="width:90%;" data-options="disabled:true"
                                       value="${externalRentalInCarEntity.applydeptname }"/>
                            </td>
                            <td>申請日期</td>
                            <td colspan="3" class="td_style1">
                                <input type="text" id="createtime" name="createtime" class="easyui-validatebox inputCss"
                                       data-options="width: 150" readonly value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${externalRentalInCarEntity.createtime}"/>"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫電話</td>
                            <td class="td_style1">
                                <input id="applytel" name="applytel" class="easyui-validatebox"
                                       data-options="width: 150,validType:'tel[\'applytel\',\'分機格式不正確\']',disabled:true"
                                       value="${externalRentalInCarEntity.applytel }"/>
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="7" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       data-options="width: 450,validType:'email[\'applyemail\',\'郵箱的格式不正確\']',disabled:true"
                                       value="${externalRentalInCarEntity.applyemail }"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td>用車事由</td>
                            <td colspan="9" class="td_style1">
                                <textarea id="theCarFor" name="theCarFor"
                                          class="easyui-validatebox"
                                          oninput="return LessThan(this);"
                                          onchange="return LessThan(this);"
                                          onpropertychange="return LessThan(this);"
                                          maxlength="300"
                                          style="width:90%;height:80px;"
                                          rows="5" cols="6"
                                          data-options="validType:'length[0,300]',disabled:true">${externalRentalInCarEntity.theCarFor }</textarea><span
                                    id="txtNum"></span></td>
                            </td>
                        </tr>
                        <tr align="center">
                            <td rowspan="4">用車說明</td>
                            <td>數量</td>
                            <td class="td_style1">
                                <input id="numberOfBus" name="numberOfBus" class="easyui-validatebox"
                                       data-options="width: 150,disabled:true"
                                       value="${externalRentalInCarEntity.numberOfBus }"/>
                            </td>
                            <td>負責人</td>
                            <td class="td_style1">
                                <input id="principal" name="principal" class="easyui-validatebox"
                                       data-options="width: 150,disabled:true"
                                       value="${externalRentalInCarEntity.principal }"/>
                            </td>
                            <td>聯繫方式</td>
                            <td class="td_style1">
                                <input id="contactInformation" name="contactInformation" class="easyui-validatebox"
                                       data-options="width: 150,disabled:true"
                                       value="${externalRentalInCarEntity.contactInformation }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>用車時間</td>
                            <td colspan="7" align="center">
                                <input id="transportStartTime" name="transportStartTime" class="easyui-validatebox Wdate"
                                       data-options="width:200,prompt:'请选择开始时间',disabled:true" style="width:180px"
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd HH:mm:ss"
									value="${externalRentalInCarEntity.transportStartTime}"/>"
                                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'%y-%M-%d %H:%m:%s'})"/>
                                ~<input id="transportEndTime" name="transportEndTime" class="easyui-validatebox Wdate"
                                        data-options="width:200,prompt:'请选择結束时间',disabled:true" style="width:180px"
                                        value="<fmt:formatDate  pattern="yyyy-MM-dd HH:mm:ss"
									value="${externalRentalInCarEntity.transportEndTime}"/>"
                                        onclick="WdatePicker({minDate:'#F{$dp.$D(\'transportStartTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>
                                行車路線<br>
                                （詳細、準確）
                            </td>
                            <td class="td_style1" colspan="7">
                                            <textarea id="driveCircuit" name="driveCircuit"
                                                      class="easyui-validatebox"
                                                      oninput="return LessThanTWO(this);"
                                                      onchange="return LessThanTWO(this);"
                                                      onpropertychange="return LessThan(this);"
                                                      maxlength="200"
                                                      style="width:87%;height:80px;"
                                                      rows="5" cols="6"
                                                      data-options="validType:'length[0,200]',disabled:true">${externalRentalInCarEntity.driveCircuit }</textarea><span
                                    id="driveCircuit200"></span>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>特別要求</td>
                            <td class="td_style1" colspan="7">
                                <input id="specialRequests" name="specialRequests" class="easyui-validatebox"
                                       oninput="return LessThanTWO(this);"
                                       onchange="return LessThanTWO(this);" maxlength="100"
                                       style="width:87%;" data-options="disabled:true"
                                       value="${externalRentalInCarEntity.specialRequests }"/><span
                                    id="specialRequests100"></span>
                            </td>
                        </tr>
                        <%-- <tr>
                             <td colspan="9">
                                 <table style="width: 100%" class="formList">
                                     <tr align="center">


                                     </tr>
                                     <tr align="center">

                                     </tr>
                                     <tr align="center">

                                     </tr>
                                     <tr align="center">

                                     </tr>
                                 </table>
                             </td>
                         </tr>--%>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td colspan="9" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${externalRentalInCarEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <c:if test="${not empty nodeName&&'車調中心評估' eq nodeName}">
                            <tr align="center">
                                <td>車牌號碼&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input id="busNumber" name="busNumber" class="easyui-validatebox"
                                           data-options="width: 150,required:true"
                                           value="${externalRentalInCarEntity.busNumber }"/>
                                </td>
                                <td>司機&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input id="driver" name="driver" class="easyui-validatebox"
                                           data-options="width: 150,required:true"
                                           value="${externalRentalInCarEntity.driver }"/>
                                </td>
                                <td>聯繫電話&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input id="contactNumber" name="contactNumber" class="easyui-validatebox"
                                           data-options="width: 150,required:true"
                                           value="${externalRentalInCarEntity.contactNumber }"/>
                                </td>

                            </tr>
                        </c:if>
                        <c:if test="${nodeOrder ge 6}">
                            <tr align="center">
                                <td>車牌號碼&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input name="busNumber" class="easyui-validatebox"
                                           data-options="width: 150,disabled:true"
                                           value="${externalRentalInCarEntity.busNumber }"/>
                                </td>
                                <td>司機&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input name="driver" class="easyui-validatebox"
                                           data-options="width: 150,disabled:true"
                                           value="${externalRentalInCarEntity.driver }"/>
                                </td>
                                <td>聯繫電話&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input name="contactNumber" class="easyui-validatebox"
                                           data-options="width: 150,disabled:true"
                                           value="${externalRentalInCarEntity.contactNumber }"/>
                                </td>

                            </tr>
                        </c:if>
                        <c:if test="${not empty nodeName&&'車調中心銷單' eq nodeName}">
                            <tr align="center">
                                <td>行車公里數&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input id="kilometersOfTravel" name="kilometersOfTravel" class="easyui-validatebox"
                                           data-options="width: 150,required:true"
                                           value="${externalRentalInCarEntity.kilometersOfTravel }"/>
                                </td>
                                <td>用車耗時&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input id="transportTime" name="transportTime" class="easyui-validatebox"
                                           data-options="width: 150,required:true"
                                           value="${externalRentalInCarEntity.transportTime }"/>
                                </td>

                            </tr>
                        </c:if>
                        <c:if test="${nodeOrder ge 11}">
                            <tr align="center">
                                <td>行車公里數&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input name="kilometersOfTravel" class="easyui-validatebox"
                                           data-options="width: 150,disabled:true"
                                           value="${externalRentalInCarEntity.kilometersOfTravel }"/>
                                </td>
                                <td>用車耗時&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input name="transportTime" class="easyui-validatebox"
                                           data-options="width: 150,disabled:true"
                                           value="${externalRentalInCarEntity.transportTime }"/>
                                </td>

                            </tr>
                        </c:if>
                        <tr align="center">
                            <td>備註</td>
                            <td align="left" colspan="7" style="padding-top: 10px;padding-left: 25px;padding-bottom: 10px">
                                1.一車一單（如需用兩輛車，則填寫并簽核兩張申請單）</br>

                                2.填單前請閱讀<a href="${ctx}/externalrentalincar/downLoad">園區內班車專案用車乘車須知</a></br>

                                3.用車部門至少提前三個工作日提交有效申請單至車調中心</br>

                                4.太原周邊車調中心聯繫電話：565+63319
                            </td>
                        </tr>
                        <tr align="center">
                            <td>批註</td>
                            <td colspan="9">
						<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${externalRentalInCarEntity.serialno}" perCall="audiPrValid(${nodeOrder})"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','外租用車申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${externalRentalInCarEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
  <div id="dlg"></div>
<script src='${ctx}/static/js/generalAffairs/externalrentalincar.js?random=<%= Math.random()%>'></script>
</body>
</html>