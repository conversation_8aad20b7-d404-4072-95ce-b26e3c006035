<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>園區內班車專案用車申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp"%>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
            type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/externalrentalincar/${action}" method="post">
    <!--
          serialno 任務編號
makerno 填單人工號
makername 填單人名稱
createtime 填單時間
complettime 簽核完成時間
workstatus 表單狀態
attachids 附件Id
createBy 創建人
createDate 創建時間
updateBy 更新者
updateDate 更新時間
delFlag 刪除標識
makerdeptno 填單人所在部門
applyno 申請人工號
applyname 申請人姓名
applydeptno 申請人單位代碼
applydeptname 申請人單位名稱
applyfactoryid 申請人廠區id
applyemail 申請人郵箱
applytel 申請人電話
applycostno 申請人費用代碼
id 主鍵
theCarFor 用車事由
numberOfBus 乘車人數
principal 負責人
contactInformation 聯繫方式
transportStartTime 用車開始時間
transportEndTime 用車結束時間
driveCircuit 行車路線
specialRequests 特別要求
kchargeno 課級主管
kchargename 課級主管姓名
bchargeno 部級主管
bchargename 部級主管姓名
cchargeno 廠級主管
cchargename 廠級主管姓名
zchargeno 製造處級主管
zchargename 製造處級主管姓名
cdzxchargeno 車調中心評估
cdzxchargename 車調中心評估姓名
ghjgchargeno 工會經管
ghjgchargename 工會經管姓名
jgchargeno 經管審核
jgchargename 經管審核姓名
zcchargeno 製造總處級主管
zcchargename 製造總處級主管
pcchargeno 產品處級主管工號
pcchargename 產品處級主管姓名
cdxdchargeno 車調中心銷單
cdxdchargename 車調中心銷單
cdxdshchargeno 車調中心銷單審核
cdxdshchargename 車調中心銷單審核
cdxdhzchargeno 車調中心銷單核准
cdxdhzchargename 車調中心銷單核准
apcchargeno 申請人確認
apcchargename 申請人確認
busNumber 車牌號碼
driver 司機
contactNumber 聯繫電話
kilometersOfTravel 行車公里數
transportTime 用車耗時
      -->
    <input id="ids" name="ids" type="hidden" value="${externalRentalInCarEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${externalRentalInCarEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${externalRentalInCarEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${externalRentalInCarEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${externalRentalInCarEntity.makerdeptno }"/>
    <input id="affiliationFactoryId" name="affiliationFactoryId" type="hidden" value="${externalRentalInCarEntity.affiliationFactoryId }"/>
    <div class="commonW">
        <div class="headTitle">園區內班車專案用車申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${externalRentalInCarEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${externalRentalInCarEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${externalRentalInCarEntity.createDate==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${externalRentalInCarEntity.createDate}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty externalRentalInCarEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty externalRentalInCarEntity.makerno}">
            <div class="position_R margin_R">填單人：${externalRentalInCarEntity.makerno}/${externalRentalInCarEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 150,required:true"
                                       value="${externalRentalInCarEntity.applyno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${externalRentalInCarEntity.applyname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${externalRentalInCarEntity.applydeptno }"/>
                            </td>
                            <td width="3%">費用代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applycostno" name="applycostno"
                                       class="easyui-validatebox inputCss" data-options="width: 120"
                                       value="${externalRentalInCarEntity.applycostno }"/>
                            </td>
                            <td width="4%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${externalRentalInCarEntity.applyfactoryid }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applyfactoryid\',\'请选择所在廠區\']',onChange:function(){auditTableReset('auditTable');}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox"
                                       style="width:90%;"
                                       value="${externalRentalInCarEntity.applydeptname }"/>
                            </td>
                            <td>申請日期</td>
                            <td colspan="3" class="td_style1">
                                <input type="text" id="createtime" name="createtime" class="easyui-validatebox inputCss"
                                       data-options="width: 150" readonly value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${externalRentalInCarEntity.createtime}"/>"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>
                                聯繫電話&nbsp;<font color="red">*</font>
                            </td>
                            <td class="td_style1">
                                <input id="applytel" name="applytel" class="easyui-validatebox"
                                       data-options="width: 150,required:true,validType:'tel[\'applytel\',\'分機格式不正確\']'"
                                       value="${externalRentalInCarEntity.applytel }"/>
                            </td>
                            <td>
                                聯繫郵箱&nbsp;<font color="red">*</font>
                            </td>
                            <td colspan="7" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       data-options="width: 450,required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"
                                       value="${externalRentalInCarEntity.applyemail }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td>用車事由&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1">
                                <textarea id="theCarFor" name="theCarFor"
                                          class="easyui-validatebox"
                                          oninput="return LessThan(this);"
                                          onchange="return LessThan(this);"
                                          onpropertychange="return LessThan(this);"
                                          maxlength="300"
                                          style="width:90%;height:80px;" data-options="required:true"
                                          rows="5" cols="6"
                                          data-options="required:true,validType:'length[0,300]'">${externalRentalInCarEntity.theCarFor }</textarea><span
                                    id="txtNum"></span></td>
                            </td>
                        </tr>
                        <tr align="center">
                            <td rowspan="7">用車說明</td>
                            <td colspan="9">
                                <table style="width: 100%" class="formList">
                                    <tr align="center">
                                        <td>數量&nbsp;<font color="red">*</font></td>
                                        <td class="td_style1">
                                            <input id="numberOfBus" name="numberOfBus" class="easyui-validatebox"
                                                   data-options="width: 150,required:true,validType:'mone[\'numberOfBus\',\'只能填寫數字\']'"
                                                   value="${externalRentalInCarEntity.numberOfBus }"/>
                                        </td>
                                        <td>負責人&nbsp;<font color="red">*</font></td>
                                        <td class="td_style1">
                                            <input id="principal" name="principal" class="easyui-validatebox"
                                                   data-options="width: 150,required:true"
                                                   value="${externalRentalInCarEntity.principal }"/>
                                        </td>
                                        <td>聯繫方式&nbsp;<font color="red">*</font></td>
                                        <td class="td_style1">
                                            <input id="contactInformation" name="contactInformation" class="easyui-validatebox"
                                                   data-options="width: 150,required:true"
                                                   value="${externalRentalInCarEntity.contactInformation }"/>
                                        </td>

                                    </tr>
                                    <tr align="center">
                                        <td>用車時間&nbsp;<font color="red">*</font></td>
                                        <td colspan="7" align="center">
                                            <input id="transportStartTime" name="transportStartTime" class="easyui-validatebox Wdate"
                                                   data-options="width:200,required:true,prompt:'请选择开始时间'" style="width:180px"
                                                   value="<fmt:formatDate  pattern="yyyy-MM-dd HH:mm:ss"
									value="${externalRentalInCarEntity.transportStartTime}"/>"
                                                   onclick="WdatePicker({onpicked:function(){transportEndTime.click()},doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'%y-%M-%d %H:%m:%s'})"/>
                                            ~<input id="transportEndTime" name="transportEndTime" class="easyui-validatebox Wdate"
                                                    data-options="width:200,required:true,prompt:'请选择結束时间'" style="width:180px"
                                                    value="<fmt:formatDate  pattern="yyyy-MM-dd HH:mm:ss"
									value="${externalRentalInCarEntity.transportEndTime}"/>"
                                                    onclick="WdatePicker({doubleCalendar:true,skin:'whyGreen',minDate:'#F{$dp.$D(\'transportStartTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})"/>
                                        </td>
                                    </tr>
                                    <tr align="center">
                                        <td>
                                            行車路線<br>
                                            （詳細、準確）<font color="red">*</font>
                                        </td>
                                        <td class="td_style1" colspan="7">
                                            <textarea id="driveCircuit" name="driveCircuit"
                                                      class="easyui-validatebox"
                                                      oninput="return LessThanTWO(this);"
                                                      onchange="return LessThanTWO(this);"
                                                      onpropertychange="return LessThan(this);"
                                                      maxlength="200"
                                                      style="width:87%;height:80px;" data-options="required:true"
                                                      rows="5" cols="6"
                                                      data-options="required:true,validType:'length[0,200]'">${externalRentalInCarEntity.driveCircuit }</textarea><span
                                                id="driveCircuit200"></span>
                                        </td>
                                    </tr>
                                    <tr align="center">
                                        <td>特別要求</td>
                                        <td class="td_style1" colspan="7">
                                            <input id="specialRequests" name="specialRequests" class="easyui-validatebox"
                                                   oninput="return LessThanTWO(this);"
                                                   onchange="return LessThanTWO(this);" maxlength="100"
                                                   style="width:87%;"
                                                   value="${externalRentalOutCarEntity.specialRequests }"/><span
                                                id="specialRequests100"></span>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button"
                                                                   value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span> <input type="hidden" id="attachids"
                                               name="attachids" value="${externalRentalInCarEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td align="left" colspan="7" style="padding-top: 10px;padding-left: 25px;padding-bottom: 10px">
                                1.一車一單（如需用兩輛車，則填寫并簽核兩張申請單）</br>

                                2.填單前請閱讀<a href="${ctx}/externalrentalincar/downLoad">園區內班車專案用車乘車須知</a></br>

                                3.用車部門至少提前三個工作日提交有效申請單至車調中心</br>

                                4.太原周邊車調中心聯繫電話：565+63319
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_waizuyongcheshenqingdan','園區內班車專案用車申請單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" id="auditTable"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#affiliationFactoryId').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${externalRentalInCarEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${externalRentalInCarEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#affiliationFactoryId').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${externalRentalInCarEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${externalRentalInCarEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'cchargeno','cchargename',$('#affiliationFactoryId').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${externalRentalInCarEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${externalRentalInCarEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRoleOut($('#applydeptno').val(),'zchargeno','zchargename',$('#affiliationFactoryId').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${externalRentalInCarEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${externalRentalInCarEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cdzxchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">車調中心評估</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(76,'cdzxchargeno','cdzxchargename',$('input[name=\'applyfactoryid\']').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cdzxchargeno" name="cdzxchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cdzxchargeno']}"
                                                               readonly
                                                               value="${externalRentalInCarEntity.cdzxchargeno }"/><c:if
                                                            test="${requiredMap['cdzxchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cdzxchargename" name="cdzxchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cdzxchargeno']}"
                                                                value="${externalRentalInCarEntity.cdzxchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="ghjgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">工會經管審核&nbsp;<a
                                                                        href="javascript:void(0);" onclick="addGhHq();">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ghjgchargeno" name="ghjgchargeno"
                                                               class="easyui-validatebox" onblur='getUserNameByEmpno(this);'
                                                               data-options="width:80,required:${requiredMap['ghjgchargeno']}"
                                                               value="${externalRentalInCarEntity.ghjgchargeno }"/><c:if
                                                            test="${requiredMap['ghjgchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ghjgchargename" name="ghjgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ghjgchargeno']}"
                                                                value="${externalRentalInCarEntity.ghjgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="jgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">經管審核&nbsp;<a href="javascript:void(0);" onclick="addGhShHq();">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jgchargeno" name="jgchargeno"
                                                               class="easyui-validatebox" onblur='getUserNameByEmpno(this);'
                                                               data-options="width:80,required:${requiredMap['jgchargeno']}"
                                                               value="${externalRentalInCarEntity.jgchargeno }"/><c:if
                                                            test="${requiredMap['jgchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="jgchargename" name="jgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jgchargeno']}"
                                                                value="${externalRentalInCarEntity.jgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#applydeptno').val(),'zcchargeno','zcchargename',$('#affiliationFactoryId').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${externalRentalInCarEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${externalRentalInCarEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產品處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#applydeptno').val(),'pcchargeno','pcchargename',$('#affiliationFactoryId').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${externalRentalInCarEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${externalRentalInCarEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cdxdchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">車調中心銷單</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(78,'cdxdchargeno','cdxdchargename',$('input[name=\'applyfactoryid\']').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cdxdchargeno" name="cdxdchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cdxdchargeno']}"
                                                               readonly
                                                               value="${externalRentalInCarEntity.cdxdchargeno }"/><c:if
                                                            test="${requiredMap['cdxdchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cdxdchargename" name="cdxdchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cdxdchargeno']}"
                                                                value="${externalRentalInCarEntity.cdxdchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cdxdshchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">車調中心銷單審核
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(79,'cdxdshchargeno','cdxdshchargename',$('input[name=\'applyfactoryid\']').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cdxdshchargeno" name="cdxdshchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cdxdshchargeno']}"
                                                               readonly
                                                               value="${externalRentalInCarEntity.cdxdshchargeno }"/><c:if
                                                            test="${requiredMap['cdxdshchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cdxdshchargename" name="cdxdshchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cdxdshchargeno']}"
                                                                value="${externalRentalInCarEntity.cdxdshchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cdxdhzchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">車調中心銷單核准
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(80,'cdxdhzchargeno','cdxdhzchargename',$('input[name=\'applyfactoryid\']').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cdxdhzchargeno" name="cdxdhzchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cdxdhzchargeno']}"
                                                               readonly
                                                               value="${externalRentalInCarEntity.cdxdhzchargeno }"/><c:if
                                                            test="${requiredMap['cdxdhzchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cdxdhzchargename" name="cdxdhzchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cdxdhzchargeno']}"
                                                                value="${externalRentalInCarEntity.cdxdhzchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">申請人確認</td>
                                                                <%--<td style="border: none;">--%>
                                                                <%--<div class="float_L qhUserIcon"--%>
                                                                <%--onclick="selectRole($('#applyno').val(),'apcchargeno','apcchargename',$('#applyfactoryid').val())"></div>--%>
                                                                <%--</td>--%>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="apcchargeno" name="apcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['apcchargeno']}"
                                                               readonly
                                                               value="${externalRentalInCarEntity.apcchargeno }"/><c:if
                                                            test="${requiredMap['apcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="apcchargename" name="apcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['apcchargeno']}"
                                                                value="${externalRentalInCarEntity.apcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${externalRentalInCarEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;"
                                   onclick="canelTask('${externalRentalInCarEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
</div>
<script src="${ctx}/static/js/generalAffairs/externalrentalincar.js?random=<%= Math.random()%>"></script>
<script type="text/javascript">
    //設置申請時間
    setApplyDate();
    function setApplyDate() {
        var myDate = new Date();
        var year = myDate.getFullYear(); //获取完整的年份
        var month = myDate.getMonth() + 1; //获取当前月份(0-11,0代表1月)
        if (month < 10) {
            month = "0" + month;
        }
        var day = myDate.getDate(); //获取当前日(1-31)
        if (day < 10) {
            day = "0" + day;
        }
        var sysdate = year + "-" + month + "-" + day;
        $("input[name='createtime']").val(sysdate);
    }
</script>
</body>
</html>