<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>違紀處理申請表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfilegalprocesses/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfIlegalProcessesEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfIlegalProcessesEntity.serialno }"/>
    <input id="workFlowId" name="workFlowId" type="hidden" value="${workFlowId }"/>
    <input id="workstatus" name="workstatus" type="hidden" value="${wfIlegalProcessesEntity.workstatus }"/>
    <div class="commonW">
        <div class="headTitle">違紀處理申請表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfIlegalProcessesEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfIlegalProcessesEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfIlegalProcessesEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <fmt:formatDate value='${wfIlegalProcessesEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：<span
                style="color:#999;">${wfIlegalProcessesEntity.makerno}/${wfIlegalProcessesEntity.makername}</span></div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">違紀者基本信息</td>
                        </tr>
                        <tbody id="info_Body">
                        <c:if test="${wfIlegalProcessesEntity.ilegalItemprocessesEntityList!=null&&wfIlegalProcessesEntity.ilegalItemprocessesEntityList.size()>0}">
                            <c:forEach items="${wfIlegalProcessesEntity.ilegalItemprocessesEntityList}"
                                       var="itemsEntity"
                                       varStatus="status">
                                <tr align="center">
                                    <input id="workAbility${status.index+1}"
                                           name="ilegalItemprocessesEntityList[${status.index+1}].workAbility"
                                           type="hidden" value="${itemsEntity.workAbility }"/>
                                    <td width="10%">工號</td>
                                    <td width="10%" class="td_style1">
                                        <input id="applyno${status.index+1}"
                                               name="ilegalItemprocessesEntityList[${status.index+1}].applyno"
                                               class="easyui-validatebox"
                                               data-options="width: 80,required:true,disabled:true"
                                               onblur="queryUserInfo(${status.index+1});"
                                               value="${itemsEntity.applyno }"/>
                                    </td>
                                    <td width="10%">姓名</td>
                                    <td width="10%" class="td_style1">
                                        <input id="applyname${status.index+1}"
                                               name="ilegalItemprocessesEntityList[${status.index+1}].applyname"
                                               value="${itemsEntity.applyname }" class="easyui-validatebox inputCss"
                                               readonly data-options="width:80"/>
                                    </td>
                                    <td width="10%">單位代碼</td>
                                    <td width="10%" class="td_style1">
                                        <input id="deptno${status.index+1}"
                                               name="ilegalItemprocessesEntityList[${status.index+1}].deptno"
                                               value="${itemsEntity.deptno }" class="easyui-validatebox inputCss"
                                               data-options="width: 90" readonly/>
                                    </td>
                                    <td width="10%">資位</td>
                                    <td width="10%" class="td_style1">
                                        <input id="leveltype${status.index+1}"
                                               name="ilegalItemprocessesEntityList[${status.index+1}].leveltype"
                                               value="${itemsEntity.leveltype }" class="easyui-validatebox inputCss"
                                               data-options="width: 80" readonly/>
                                    </td>
                                    <td width="10%">管理職</td>
                                    <td width="10%" class="td_style1">
                                        <input id="ismanager${status.index+1}"
                                               name="ilegalItemprocessesEntityList[${status.index+1}].ismanager"
                                               value="${itemsEntity.ismanager }" class="easyui-validatebox inputCss"
                                               data-options="width: 80" readonly/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>廠區</td>
                                    <td class="td_style1">
                                        <input id="factoryid${status.index}"
                                               name="ilegalItemprocessesEntityList[${status.index+1}].factoryid"
                                               class="easyui-combobox" value="${itemsEntity.factoryid }"
                                               data-options="disabled:true,valueField:'factoryid',textField:'factoryname',url:'${ctx}/tqhfactoryidconfig/allFactorys/',loadFilter: function (data) { data.unshift({ factoryid: '', factoryname: '請選擇廠區' }); return data; }"/>
                                    </td>
                                    <td>單位</td>
                                    <td colspan="3" class="td_style1">
                                        <input id="deptname${status.index+1}"
                                               name="ilegalItemprocessesEntityList[${status.index+1}].deptname"
                                               value="${itemsEntity.deptname }" class="easyui-validatebox"
                                               style="width:90%;" data-options="required:true,disabled:true"/>
                                    </td>
                                    <td>法人</td>
                                    <td colspan="3">
                                        <c:choose>
                                            <c:when test="${itemsEntity.laypersonname==null || itemsEntity.laypersonname=='' }">
                                                <input id="layperson${status.index+1}"
                                                       name="ilegalItemprocessesEntityList[${status.index+1}].layperson"
                                                       class="easyui-combobox" value="${itemsEntity.layperson }"
                                                       data-options="panelHeight: 400,width: 300,disabled:true,required:true,validType:'comboxValidate[\'layperson${status.index+1}\',\'请选择法人\']',valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/project_entity',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇法人' }); return data; }"/>
                                            </c:when>
                                            <c:otherwise>
                                                <input id="laypersonname${status.index+1}"
                                                       name="ilegalItemprocessesEntityList[${status.index+1}].laypersonname"
                                                       value="${itemsEntity.laypersonname }" class="easyui-validatebox"
                                                       style="width:90%;" data-options="required:true,disabled:true"/>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </tr>
                                <tr align="left">
                                    <td colspan="10" align="left">
                                        當年度該員工違紀：<input id="ilegalnum${status.index+1}"
                                                                name="ilegalItemprocessesEntityList[${status.index+1}].ilegalnum"
                                                                value="${itemsEntity.ilegalnum }"
                                                                class="easyui-validatebox"
                                                                style="width:200px;" readonly/>
                                    </td>
                                </tr>
                                <tr id="bondedgoodsItem${status.index+1}">
                                    <input type="hidden" id="shunxu${status.index+1}"
                                           name="ilegalItemprocessesEntityList[${status.index+1}].shunxu"
                                           value="${status.index+1}"/>
                                    <c:if test="${status.index>0}">
                                        <td colspan="9" align="center" bgcolor="#faebd7">
                                            以上為第${status.index+1}位違紀人員
                                        </td>
                                        <td width="6%" align="center">
                                            <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                   class="deleteBtnStr" disabled
                                                   onclick="bondedgooddeltr(${status.index+1});return false;"/>
                                        </td>
                                    </c:if>
                                    <c:if test="${status.index==0}">
                                        <td colspan="10" align="center" bgcolor="#faebd7">
                                            以上為第${status.index+1}位違紀人員
                                        </td>
                                    </c:if>
                                </tr>
                            </c:forEach>
                        </c:if>
                        </tbody>
                        <tr align="left" class="nottr">
                            <td colspan="10" width="100%" style="text-align:center;padding-left:10px;">
                                <a href="#" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add',text:'添加一筆',disabled:true"
                                   style="width: 100px"></a>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">違紀信息</td>
                        </tr>
                        <tr>
                            <td align="center">違紀發生區域</td>
                            <td class="td_style1">
                                <input id="happenarea" name="happenarea" class="easyui-validatebox"
                                       data-options="width: 80,required:true,disabled:true"
                                       value="${wfIlegalProcessesEntity.happenarea}"/>
                            </td>
                            <td align="center">違紀發生日期</td>
                            <td class="td_style1">
                                <input id="happentime" name="happentime" class="easyui-validatebox Wdate"
                                       data-options="width:150,required:true,prompt:'请选择違紀發生日期',disabled:true"
                                       value="${wfIlegalProcessesEntity.happentime}"
                                       onclick="WdatePicker({skin:'whyGreen',dateFmt:'yyyy-MM-dd'})"/>
                            </td>
                            <td align="center">違紀通報日期</td>
                            <td class="td_style1">
                                <input id="reporttime" name="reporttime" class="easyui-validatebox Wdate"
                                       data-options="width:150,required:true,prompt:'请选择違紀發生日期',disabled:true"
                                       value="${wfIlegalProcessesEntity.reporttime}"
                                       onclick="WdatePicker({skin:'whyGreen',dateFmt:'yyyy-MM-dd'})"/>
                            </td>
                            <td align="center">違紀類別</td>
                            <td class="td_style1" colspan="3">
                                <input id="laytype" name="laytype" class="easyui-validatebox"
                                       data-options="width: 300,required:true,disabled:true"
                                       value="${wfIlegalProcessesEntity.laytype}"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">違紀事實描述（可附件）</td>
                        </tr>
                        <tr>
                            <td colspan="8">
                            <textarea id="laydesc" name="laydesc" data-options="required:true"
                                      style="width:90%;height:80px;" rows="5" cols="6" disabled
                                      maxlength="500">${wfIlegalProcessesEntity.laydesc }</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="8" width="100%" class="td_style1">
                                <c:choose>
                                    <c:when test="${wfIlegalProcessesEntity.workstatus!=3}">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file"
                                           onchange="oosUploadIlegalFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                        <input type="hidden" id="attachids" name="attachids"
                                               value="${wfIlegalProcessesEntity.attachids }"/>
                                        <div id="dowloadUrl">
                                            <c:forEach items="${file}" varStatus="i" var="item">
                                                <div id="${item.id}"
                                                     style="line-height:30px;margin-left:5px;" class="float_L">
                                                    <div class="float_L">
                                                        <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                                    </div>
                                                    <div class="float_L deleteBtn"
                                                         onclick="oosDelIlegalAtt('${item.id}')"></div>
                                                </div>
                                            </c:forEach>
                                        </div>
                                    </c:when>
                                    <c:otherwise>
                                        <div id="dowloadUrl0">
                                            <c:forEach items="${file}" varStatus="i" var="item">
                                                <div id="${item.id}"
                                                     style="line-height:30px;margin-left:5px;" class="float_L">
                                                    <div class="float_L">
                                                        <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                                    </div>
                                                </div>
                                            </c:forEach>
                                        </div>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">違反規定及條款</td>
                        </tr>
                        <tr>
                            <input id="dependtype_" type="hidden" value="${wfIlegalProcessesEntity.dependtype }"/>
                            <td colspan="1"><input type="radio" checked name="dependtype" value="1" disabled
                                                   onchange="dependtypeChange(0);">公司規章：
                            </td>
                            <td colspan="7">
                                <input id="smallType" name="smallType"
                                       class="easyui-combobox" disabled
                                       data-options="width: 500,required:true"
                                       value="${wfIlegalProcessesEntity.smallType}"/>
                            </td>
                        </tr>
                        <tbody id="info_Body_one">
                        <c:if test="${wfIlegalProcessesEntity.ilegalItemlayEntityList!=null&&wfIlegalProcessesEntity.ilegalItemlayEntityList.size()>0}">
                            <c:forEach items="${wfIlegalProcessesEntity.ilegalItemlayEntityList}" var="itemsEntity"
                                       varStatus="status">
                                <tr id="layEntityList${status.index+1}">
                                    <td colspan="8">
                                        第
                                        <input type="text" class="easyui-validatebox"
                                               data-options="width: 40,required:true,disabled:true"
                                               onchange="queryDependtype(${status.index+1});"
                                               id="companybar${status.index+1}"
                                               name="ilegalItemlayEntityList[${status.index+1}].companybar"
                                               value="${itemsEntity.companybar }"/>條，第
                                        <input name="ilegalItemlayEntityList[${status.index+1}].companyfund"
                                               id="companyfund${status.index+1}" disabled
                                               onchange="queryDependtype(${status.index+1});"
                                               value="${itemsEntity.companyfund }" type="text" style="width:40px"/>款，第
                                        <input id="companyitem${status.index+1}" value="${itemsEntity.companyitem }"
                                               disabled onchange="queryDependtype(${status.index+1});"
                                               name="ilegalItemlayEntityList[${status.index+1}].companyitem" type="text"
                                               style="width:40px"/>項
                                        <textarea value="${itemsEntity.companycontent }" rows="3"
                                                  id="companycontent${status.index+1}" disabled
                                                  name="ilegalItemlayEntityList[${status.index+1}].companycontent"
                                                  type="text"
                                                  style="width:75%;vertical-align: middle">${itemsEntity.companycontent }</textarea>
                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                               class="deleteBtnStr" disabled
                                               onclick="dependtypedeltr(${status.index+1});return false;"/>
                                    </td>
                                </tr>
                            </c:forEach>
                        </c:if>
                        </tbody>
                        <tr align="left" class="nottr" id="dependtypeadd">
                            <td colspan="8" width="100%" style="text-align:center;padding-left:10px;">
                                <a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-add',text:'添加一筆'"
                                   disabled style="width: 100px"></a>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8"><input type="radio" name="dependtype" value="2" disabled
                                                   onchange="dependtypeChange(1);">法律法規：&nbsp;&nbsp;<input
                                    value="${wfIlegalProcessesEntity.laycontent }" name="laycontent" id="laycontent"
                                    type="text" disabled
                                    style="width:70%"/></td>
                        </tr>
                        <tr align="center">
                            <td width="8%">建議處理類別</td>
                            <input id="sendpolice_" type="hidden" value="${wfIlegalProcessesEntity.sendpolice }"/>
                            <td width="17%"><input type='checkbox' name='sendpolice' value='1' disabled>送警</td>
                            <input id="fire_" type="hidden" value="${wfIlegalProcessesEntity.fire }"/>
                            <td width="8%"><input type='checkbox' name='fire' value="2" disabled>開除</td>
                            <input id="bigguo_" type="hidden" value="${wfIlegalProcessesEntity.bigguo }"/>
                            <td width="18%" colspan="2"><input type='checkbox' name='bigguo' value='3' disabled>記大過&nbsp;
                                <input id="bigguonum"
                                       name="bigguonum" disabled
                                       class="easyui-combobox" value="${wfIlegalProcessesEntity.bigguonum}"
                                       data-options="panelHeight: 200,width: 60,required:true,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/type_count',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; }"/>
                            </td>
                            <input id="smallguo_" type="hidden" value="${wfIlegalProcessesEntity.smallguo }"/>
                            <td width="25%" colspan="2"><input type='checkbox' name='smallguo'
                                                               value='4' disabled>記小過&nbsp;
                                <input id="smallguonum"
                                       name="smallguonum" disabled
                                       class="easyui-combobox" value="${wfIlegalProcessesEntity.smallguonum}"
                                       data-options="panelHeight: 200,width: 60,required:true,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/type_count',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; }"/>
                            </td>
                            <input id="alarm_" type="hidden" value="${wfIlegalProcessesEntity.alarm }"/>
                            <td width="24%"><input type='checkbox' name='alarm' value='5' disabled>警告&nbsp;
                                <input id="alarmnum"
                                       name="alarmnum" disabled
                                       class="easyui-combobox" value="${wfIlegalProcessesEntity.alarmnum}"
                                       data-options="panelHeight: 200,width: 60,required:true,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/type_count',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">嚴重違紀調查取證見證人（限依公司規定應開除情形）</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">工號</td>
                            <td width="17%" class="td_style1">
                                <input id="surveyno" name="surveyno"
                                       class="easyui-validatebox" data-options="width: 80,disabled:true"
                                       value="${wfIlegalProcessesEntity.surveyno }" onblur="querySurveyUserInfo();"/>
                            </td>
                            <td width="8%">姓名</td>
                            <td width="10%" class="td_style1">
                                <input id="surveyname" name="surveyname"
                                       class="easyui-validatebox" data-options="width:100,disabled:true"
                                       value="${wfIlegalProcessesEntity.surveyname}"/>
                            </td>
                            <td width="8%">聯繫電話</td>
                            <td width="17%" class="td_style1">
                                <input id="surveyphone" name="surveyphone"
                                       class="easyui-validatebox" data-options="width: 160,disabled:true"
                                       value="${wfIlegalProcessesEntity.surveyphone}"/>
                            </td>
                            <td width="8%">單位</td>
                            <td width="24%" class="td_style1">
                                <input id="surveyunit" name="surveyunit"
                                       class="easyui-validatebox" data-options="width: 240,disabled:true"
                                       value="${wfIlegalProcessesEntity.surveyunit}"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">違紀提報單位</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">聯絡人</td>
                            <td width="17%" class="td_style1">
                                <input id="contactno" name="contactno" value="${wfIlegalProcessesEntity.contactno}"
                                       class="easyui-validatebox" data-options="width: 70,required:true,disabled:true"
                                       onblur="queryContactUserInfo();"/>
                                /<input id="contactname" name="contactname"
                                        value="${wfIlegalProcessesEntity.contactname}"
                                        class="easyui-validatebox"
                                        data-options="width: 70,required:true,disabled:true"/>
                            </td>
                            <td width="8%">聯繫電話</td>
                            <td width="10%" class="td_style1">
                                <input id="contactphone" name="contactphone"
                                       value="${wfIlegalProcessesEntity.contactphone}"
                                       class="easyui-validatebox" data-options="width:100,required:true,disabled:true"/>
                            </td>
                            <td width="8%">NOTES</td>
                            <td width="17%" class="td_style1">
                                <input id="contactnotes" name="contactnotes"
                                       value="${wfIlegalProcessesEntity.contactnotes}"
                                       class="easyui-validatebox"
                                       data-options="width: 160,required:true,disabled:true"/>
                            </td>
                            <td width="8%">單位</td>
                            <td width="24%" class="td_style1">
                                <input id="contactunit" name="contactunit"
                                       value="${wfIlegalProcessesEntity.contactunit}"
                                       class="easyui-validatebox"
                                       data-options="width: 240,required:true,disabled:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">案件來源</td>
                            <td width="35%" class="td_style1" colspan="3">
                                <input id="caseresource" name="caseresource"
                                       value="${wfIlegalProcessesEntity.caseresource}"
                                       class="easyui-validatebox" data-options="width: 400,disabled:true"/>
                            </td>
                            <td width="8%">承辦人</td>
                            <td width="17%" class="td_style1">
                                <input id="takeno" name="takeno" value="${wfIlegalProcessesEntity.takeno}"
                                       class="easyui-validatebox" data-options="width:70,required:true,disabled:true"
                                       onblur="queryTakeUserInfo();"/>
                                /<input id="takename" name="takename" value="${wfIlegalProcessesEntity.takename}"
                                        class="easyui-validatebox" data-options="width:70,required:true,disabled:true"/>
                            </td>
                            <td width="8%">承辦人主管</td>
                            <td width="24%" class="td_style1">
                                <input id="takechargeno" name="takechargeno"
                                       value="${wfIlegalProcessesEntity.takechargeno}"
                                       class="easyui-validatebox" data-options="width:70,required:true,disabled:true"
                                       onblur="queryTakeChargeUserInfo();"/>
                                /<input id="takechargename" name="takechargename"
                                        value="${wfIlegalProcessesEntity.takechargename}"
                                        class="easyui-validatebox" data-options="width:70,required:true,disabled:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">人資違紀結案</td>
                            <input id="endsendpolice_" type="hidden" value="${wfIlegalProcessesEntity.endsendpolice }"/>
                            <td width="17%"><input type='checkbox' name='endsendpolice' value='1' disabled>送警</td>
                            <input id="endfire_" type="hidden" value="${wfIlegalProcessesEntity.endfire }"/>
                            <td width="8%"><input type='checkbox' name='endfire' value="2" disabled>開除</td>
                            <input id="endbigguo_" type="hidden" value="${wfIlegalProcessesEntity.endbigguo }"/>
                            <td width="18%" colspan="2"><input type='checkbox' name='endbigguo' value='3' disabled>記大過&nbsp;
                                <input id="endbigguonum"
                                       name="endbigguonum"
                                       class="easyui-combobox" value="${wfIlegalProcessesEntity.endbigguonum}"
                                       data-options="panelHeight: 200,width: 60,disabled:true,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/type_count',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; }"/>
                            </td>
                            <input id="endsmallguo_" type="hidden" value="${wfIlegalProcessesEntity.endsmallguo }"/>
                            <td width="25%" colspan="2"><input type='checkbox' name='endsmallguo'
                                                               value='4' disabled>記小過&nbsp;
                                <input id="endsmallguonum"
                                       name="endsmallguonum"
                                       class="easyui-combobox" value="${wfIlegalProcessesEntity.endsmallguonum}"
                                       data-options="panelHeight: 200,width: 60,disabled:true,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/type_count',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; }"/>
                            </td>
                            <input id="endalarm_" type="hidden" value="${wfIlegalProcessesEntity.endalarm }"/>
                            <td width="24%"><input type='checkbox' name='endalarm' value='5' disabled>警告&nbsp;
                                <input id="endalarmnum"
                                       name="endalarmnum"
                                       class="easyui-combobox" value="${wfIlegalProcessesEntity.endalarmnum}"
                                       data-options="panelHeight: 200,width: 60,disabled:true,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/type_count',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">違紀公告文號</td>
                            <td width="92%" colspan="7" style="text-align: left">
                                <input id="ilegalnumber" name="ilegalnumber" class="easyui-validatebox"
                                       data-options="width: 300" value="${wfIlegalProcessesEntity.ilegalnumber }"/>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="8">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','違紀處理申請表');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="8" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="8" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfIlegalProcessesEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="8" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfIlegalProcessesEntity.workstatus!=null&&wfIlegalProcessesEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<div id="dlg"></div>
<script src='${ctx}/static/js/humanCapital/wfilegalprocesses.js?random=<%= Math.random()%>'></script>
</body>
</html>