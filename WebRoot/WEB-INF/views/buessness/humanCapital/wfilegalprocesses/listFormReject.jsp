<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>違紀處理申請表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfilegalprocesses/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfIlegalProcessesEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfIlegalProcessesEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfIlegalProcessesEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfIlegalProcessesEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfIlegalProcessesEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${wfIlegalProcessesEntity.makerfactoryid }"/>
    <input id="workFlowId" name="workFlowId" type="hidden" value="${workFlowId }"/>
    <input id="workstatus" name="workstatus" type="hidden" value="${wfIlegalProcessesEntity.workstatus }"/>
    <div class="commonW">
        <div class="headTitle">違紀處理申請表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfIlegalProcessesEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfIlegalProcessesEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfIlegalProcessesEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <fmt:formatDate value='${wfIlegalProcessesEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfIlegalProcessesEntity.makerno}">
            <div class="position_R margin_R">填單人：<span style="color:#999;">${user.loginName}/${user.name}</span></div>
        </c:if>
        <c:if test="${not empty wfIlegalProcessesEntity.makerno}">
            <div class="position_R margin_R">
                填單人：<span style="color:#999;">${wfIlegalProcessesEntity.makerno}/${wfIlegalProcessesEntity.makername}</span></div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <table class="formList">
                            <tr>
                                <td colspan="10" class="td_style1">違紀者基本信息</td>
                            </tr>
                            <tbody id="info_Body">
                            <c:if test="${wfIlegalProcessesEntity.ilegalItemprocessesEntityList!=null&&wfIlegalProcessesEntity.ilegalItemprocessesEntityList.size()>0}">
                                <c:forEach items="${wfIlegalProcessesEntity.ilegalItemprocessesEntityList}"
                                           var="itemsEntity"
                                           varStatus="status">
                                    <tr align="center">
                                        <input id="workAbility${status.index}" name="ilegalItemprocessesEntityList[${status.index+1}].workAbility" type="hidden" value="${itemsEntity.workAbility }"/>
                                        <td width="10%">工號&nbsp;<font color="red">*</font></td>
                                        <td width="10%" class="td_style1">
                                            <input id="applyno${status.index}"
                                                   name="ilegalItemprocessesEntityList[${status.index+1}].applyno"
                                                   class="easyui-validatebox" data-options="width: 80,required:true"
                                                   onblur="queryUserInfo(${status.index});"
                                                   value="${itemsEntity.applyno }"/>
                                        </td>
                                        <td width="10%">姓名</td>
                                        <td width="10%" class="td_style1">
                                            <input id="applyname${status.index}"
                                                   name="ilegalItemprocessesEntityList[${status.index+1}].applyname"
                                                   value="${itemsEntity.applyname }" class="easyui-validatebox inputCss"
                                                   readonly data-options="width:80"/>
                                        </td>
                                        <td width="10%">單位代碼</td>
                                        <td width="10%" class="td_style1">
                                            <input id="deptno${status.index}"
                                                   name="ilegalItemprocessesEntityList[${status.index+1}].deptno"
                                                   value="${itemsEntity.deptno }" class="easyui-validatebox inputCss"
                                                   data-options="width: 90" readonly/>
                                        </td>
                                        <td width="10%">資位</td>
                                        <td width="10%" class="td_style1">
                                            <input id="leveltype${status.index}"
                                                   name="ilegalItemprocessesEntityList[${status.index+1}].leveltype"
                                                   value="${itemsEntity.leveltype }" class="easyui-validatebox inputCss"
                                                   data-options="width: 80" readonly/>
                                        </td>
                                        <td width="10%">管理職</td>
                                        <td width="10%" class="td_style1">
                                            <input id="ismanager${status.index}"
                                                   name="ilegalItemprocessesEntityList[${status.index+1}].ismanager"
                                                   value="${itemsEntity.ismanager }" class="easyui-validatebox inputCss"
                                                   data-options="width: 80" readonly/>
                                        </td>
                                    </tr>
                                    <tr align="center">
                                        <td>廠區</td>
                                        <td class="td_style1">
                                            <input id="factoryid${status.index}"
                                                   name="ilegalItemprocessesEntityList[${status.index+1}].factoryid"
                                                   class="easyui-combobox" value="${itemsEntity.factoryid }"
                                                   data-options="disabled:true,valueField:'factoryid',textField:'factoryname',url:'${ctx}/tqhfactoryidconfig/allFactorys/',loadFilter: function (data) { data.unshift({ factoryid: '', factoryname: '請選擇廠區' }); return data; }"/>
                                        </td>
                                        <td>單位&nbsp;<font color="red">*</font></td>
                                        <td colspan="3" class="td_style1">
                                            <input id="deptname${status.index}"
                                                   name="ilegalItemprocessesEntityList[${status.index+1}].deptname"
                                                   value="${itemsEntity.deptname }" class="easyui-validatebox"
                                                   style="width:90%;" data-options="required:true"/>
                                        </td>
                                        <td>法人&nbsp;<font color="red">*</font></td>
                                        <td colspan="3">
                                                <%--<input id="layperson${status.index}"
                                                       name="ilegalItemprocessesEntityList[${status.index+1}].layperson"
                                                       class="easyui-combobox" value="${itemsEntity.layperson }"
                                                       data-options="panelHeight: 400,width: 300,required:true,validType:'comboxValidate[\'layperson${status.index}\',\'请选择法人\']',valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/project_entity',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇法人' }); return data; }"/>--%>
                                            <input id="laypersonname${status.index}"
                                                   name="ilegalItemprocessesEntityList[${status.index+1}].laypersonname"
                                                   value="${itemsEntity.laypersonname }" class="easyui-validatebox"
                                                   style="width:90%;" data-options="required:true"/>
                                        </td>
                                    </tr>
                                    <tr align="center">
                                        <td colspan="10" align="left">
                                            當年度該員工違紀：<input id="ilegalnum${status.index}"
                                                                    name="ilegalItemprocessesEntityList[${status.index+1}].ilegalnum"
                                                                    value="${itemsEntity.ilegalnum }"
                                                                    class="easyui-validatebox"
                                                                    style="width:200px;" readonly/>
                                        </td>
                                    </tr>
                                    <tr id="bondedgoodsItem${status.index+1}">
                                        <input type="hidden" id="shunxu${status.index}"
                                               name="ilegalItemprocessesEntityList[${status.index+1}].shunxu"
                                               value="${status.index+1}"/>
                                        <c:if test="${status.index>0}">
                                        <td colspan="9" align="center" bgcolor="#faebd7">以上為第${status.index+1}位違紀人員</td>
                                        <td width="6%" align="center">
                                                <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                       class="deleteBtnStr"
                                                       onclick="bondedgooddeltr(${status.index+1});return false;"/>
                                        </td>
                                        </c:if>
                                        <c:if test="${status.index==0}">
                                            <td colspan="10" align="center" bgcolor="#faebd7">以上為第${status.index+1}位違紀人員</td>
                                        </c:if>
                                    </tr>
                                </c:forEach>
                            </c:if>
                            <c:if test="${wfIlegalProcessesEntity.ilegalItemprocessesEntityList==null||wfIlegalProcessesEntity.ilegalItemprocessesEntityList.size()==0}">
                                <tr align="center">
                                    <input id="workAbility0" name="ilegalItemprocessesEntityList[0].workAbility" type="hidden"/>
                                    <td width="10%">工號&nbsp;<font color="red">*</font></td>
                                    <td width="10%" class="td_style1">
                                        <input id="applyno0" name="ilegalItemprocessesEntityList[0].applyno"
                                               class="easyui-validatebox" data-options="width: 80,required:true"
                                               onblur="queryUserInfo(0);"/>
                                    </td>
                                    <td width="10%">姓名</td>
                                    <td width="10%" class="td_style1">
                                        <input id="applyname0" name="ilegalItemprocessesEntityList[0].applyname"
                                               class="easyui-validatebox inputCss" readonly data-options="width:80"/>
                                    </td>
                                    <td width="10%">單位代碼</td>
                                    <td width="10%" class="td_style1">
                                        <input id="deptno0" name="ilegalItemprocessesEntityList[0].deptno"
                                               class="easyui-validatebox inputCss" data-options="width: 90" readonly/>
                                    </td>
                                    <td width="10%">資位</td>
                                    <td width="10%" class="td_style1">
                                        <input id="leveltype0" name="ilegalItemprocessesEntityList[0].leveltype"
                                               class="easyui-validatebox inputCss" data-options="width: 80" readonly/>
                                    </td>
                                    <td width="10%">管理職</td>
                                    <td width="10%" class="td_style1">
                                        <input id="ismanager0" name="ilegalItemprocessesEntityList[0].ismanager"
                                               class="easyui-validatebox inputCss" data-options="width: 80" readonly/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td width="10%">廠區</td>
                                    <td width="10%" class="td_style1">
                                        <input id="factoryid0" name="ilegalItemprocessesEntityList[0].factoryid"
                                               class="easyui-combobox"
                                               data-options="disabled:true,valueField:'factoryid',textField:'factoryname',url:'${ctx}/tqhfactoryidconfig/allFactorys/',loadFilter: function (data) { data.unshift({ factoryid: '', factoryname: '請選擇廠區' }); return data; }"/>
                                    </td>
                                    <td width="10%">單位&nbsp;<font color="red">*</font></td>
                                    <td colspan="3" width="30%" class="td_style1">
                                        <input id="deptname0" name="ilegalItemprocessesEntityList[0].deptname"
                                               class="easyui-validatebox" style="width:90%;"
                                               data-options="required:true"/>
                                    </td>
                                    <td width="10%">法人&nbsp;<font color="red">*</font></td>
                                    <td colspan="3" width="30%">
                                        <input id="laypersonname0"
                                               name="ilegalItemprocessesEntityList[0].laypersonname"
                                               class="easyui-validatebox"
                                               style="width:90%;" data-options="required:true"/>
                                            <%--<input id="layperson0" name="ilegalItemprocessesEntityList[0].layperson"
                                                   class="easyui-combobox"
                                                   data-options="panelHeight: 400,width: 300,required:true,validType:'comboxValidate[\'layperson0\',\'请选择法人\']',valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/project_entity',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇法人' }); return data; }"/>--%>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td colspan="10" align="left">
                                        當年度該員工違紀：<input id="ilegalnum0"
                                                                name="ilegalItemprocessesEntityList[0].ilegalnum"
                                                                class="easyui-validatebox"
                                                                style="width:200px;" readonly/>
                                    </td>
                                </tr>
                                <tr id="bondedgoodsItem0">
                                    <input id="shunxu0" type="hidden" name="ilegalItemprocessesEntityList[0].shunxu"
                                           value="1"/>
                                    <td colspan="10" align="center" bgcolor="#faebd7">以上為第1位違紀人員</td>
                                </tr>
                            </c:if>
                            </tbody>
                            <tr align="left" class="nottr">
                                <td colspan="10" width="100%" style="text-align:center;padding-left:10px;">
                                    <a href="#" id="bondedgoodItemAdd" class="easyui-linkbutton" data-options="iconCls:'icon-add',text:'添加一筆'" style="width: 100px"></a>
                                </td>
                            </tr>
                            <tr align="center">
                                <td colspan="2"><a href="${ctx}/wfilegalprocesses/downLoad/batchImportTpl"
                                                   id="btnBatchImportTpl">批量申請模板下載</a></td>
                                <td colspan="8" class="td_style1">&nbsp;&nbsp;&nbsp;&nbsp;<a
                                        href="#" id="batchImport" class="easyui-linkbutton"
                                        data-options="iconCls:'icon-hamburg-cv',text:'批量導入'" style="width: 100px;"
                                        onclick="openBatchImportWin();"></a>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="10" class="td_style1">違紀信息</td>
                            </tr>
                            <tr>
                                <td align="center">違紀發生區域&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input id="happenarea" name="happenarea" class="easyui-validatebox"
                                           data-options="width: 80,required:true"
                                           value="${wfIlegalProcessesEntity.happenarea}"/>
                                </td>
                                <td>違紀發生日期&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input id="happentime" name="happentime" class="easyui-validatebox Wdate"
                                           data-options="width:150,required:true,prompt:'请选择違紀發生日期'"
                                           value="${wfIlegalProcessesEntity.happentime}"
                                           onclick="WdatePicker({onpicked:function(){reporttime.click()},doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd',maxDate:'%y-%M-%d %H:%m:%s'})"/>
                                </td>
                                <td>違紀通報日期&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input id="reporttime" name="reporttime" class="easyui-validatebox Wdate"
                                           data-options="width:150,required:true,prompt:'请选择違紀發生日期'"
                                           value="${wfIlegalProcessesEntity.reporttime}"
                                           onclick="WdatePicker({skin:'whyGreen',dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'happentime\')}',doubleCalendar:true})"/>
                                </td>
                                <td>違紀類別&nbsp;<font color="red">*</font></td>
                                <td class="td_style1" colspan="3">
                                    <input id="laytype" name="laytype" class="easyui-validatebox"
                                           data-options="width: 300,required:true"
                                           value="${wfIlegalProcessesEntity.laytype}"/>
                                </td>
                            </tr>
                        </table>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td>
                                <table class="formList">
                                    <tr>
                                        <td colspan="8" class="td_style1">違紀事實描述（可附件）</td>
                                    </tr>
                                    <tr>
                                        <td colspan="8">
                            <textarea id="laydesc" name="laydesc" data-options="required:true"
                                      style="width:90%;height:80px;" rows="5" cols="6"
                                      oninput="return LessThanTWO(this);"
                                      onchange="return LessThanTWO(this);"
                                      onpropertychange="return LessThanTWO(this);"
                                      maxlength="500">${wfIlegalProcessesEntity.laydesc }</textarea><span
                                                id="laydesc500"></span>
                                        </td>
                                    </tr>
                                    <tr align="center">
                                        <td colspan="8" width="100%" class="td_style1">
                                            <span class="sl-custom-file">
                                                <input type="button" value="点击上传文件" class="btn-file"/>
								                <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								            </span>
                                            <input type="hidden" id="attachids" name="attachids" value="${wfIlegalProcessesEntity.attachids }"/>
                                            <div id="dowloadUrl">
                                                <c:forEach items="${file}" varStatus="i" var="item">
                                                    <div id="${item.id}"
                                                         style="line-height:30px;margin-left:5px;" class="float_L">
                                                        <div class="float_L">
                                                            <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                                        </div>
                                                        <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                                    </div>
                                                </c:forEach>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="8" class="td_style1">違反規定及條款</td>
                                    </tr>
                                    <tr>
                                        <input id="dependtype_" type="hidden"
                                               value="${wfIlegalProcessesEntity.dependtype }"/>
                                        <td colspan="1"><input type="radio" checked name="dependtype" value="1"
                                                               onpropertychange="dependtypeChange(0);"
                                                               onchange="dependtypeChange(0);">公司規章：
                                        </td>
                                        <td colspan="7">
                                            <input id="smallType" name="smallType"
                                                   class="easyui-combobox" readonly
                                                   data-options="width: 500,onSelect:function(){onchangeSmallType();}"
                                                   value="${wfIlegalProcessesEntity.smallType}"/>
                                        </td>
                                    </tr>
                                    <tbody id="info_Body_one">
                                    <c:if test="${wfIlegalProcessesEntity.ilegalItemlayEntityList!=null&&wfIlegalProcessesEntity.ilegalItemlayEntityList.size()>0}">
                                        <c:forEach items="${wfIlegalProcessesEntity.ilegalItemlayEntityList}"
                                                   var="itemsEntity"
                                                   varStatus="status">
                                            <tr id="layEntityList${status.index+1}">
                                                <td colspan="8">
                                                    第<font color="red">*</font>
                                                    <input type="text" class="easyui-validatebox"
                                                           data-options="width: 40,required:true"
                                                           onchange="queryDependtype(${status.index+1});"
                                                           id="companybar${status.index+1}"
                                                           name="ilegalItemlayEntityList[${status.index+1}].companybar"
                                                           value="${itemsEntity.companybar }"/>條，第
                                                    <input name="ilegalItemlayEntityList[${status.index+1}].companyfund"
                                                           id="companyfund${status.index+1}"
                                                           onchange="queryDependtype(${status.index+1});"
                                                           value="${itemsEntity.companyfund }" type="text"
                                                           style="width:40px"/>款，第
                                                    <input id="companyitem${status.index+1}"
                                                           value="${itemsEntity.companyitem }"
                                                           onchange="queryDependtype(${status.index+1});"
                                                           name="ilegalItemlayEntityList[${status.index+1}].companyitem"
                                                           type="text" style="width:40px"/>項
                                                    <textarea value="${itemsEntity.companycontent }"
                                                           id="companycontent${status.index+1}" rows="3"
                                                           name="ilegalItemlayEntityList[${status.index+1}].companycontent"
                                                              type="text" style="width:75%;vertical-align: middle"></textarea>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           class="deleteBtnStr"
                                                           onclick="dependtypedeltr(${status.index+1});return false;"/>
                                                </td>
                                            </tr>
                                        </c:forEach>
                                    </c:if>
                                    <c:if test="${wfIlegalProcessesEntity.ilegalItemlayEntityList==null||wfIlegalProcessesEntity.ilegalItemlayEntityList.size()==0}">
                                        <tr id="layEntityList0">
                                            <td colspan="8">
<%--                                                <c:choose>--%>
<%--                                                    <c:when test="${wfIlegalProcessesEntity.makerfactoryid=='CAATY'||wfIlegalProcessesEntity.makerfactoryid=='IPETY'}">--%>
<%--                                                        第--%>
<%--                                                        <input type="text" class="easyui-validatebox"--%>
<%--                                                               data-options="width: 40,required:true" id="companybar0"--%>
<%--                                                               name="ilegalItemlayEntityList[0].companybar"--%>
<%--                                                               value="${wfIlegalProcessesEntity.companybar }"/>條，第--%>
<%--                                                        <input name="ilegalItemlayEntityList[0].companyfund"--%>
<%--                                                               id="companyfund0"--%>
<%--                                                               value="${wfIlegalProcessesEntity.companyfund }"--%>
<%--                                                               type="text" style="width:40px"/>款，第--%>
<%--                                                        <input id="companyitem0"--%>
<%--                                                               value="${wfIlegalProcessesEntity.companyitem }"--%>
<%--                                                               name="ilegalItemlayEntityList[0].companyitem" type="text"--%>
<%--                                                               style="width:40px"/>項--%>
<%--                                                        <textarea value="${wfIlegalProcessesEntity.companycontent }"--%>
<%--                                                               id="companycontent0" rows="3"--%>
<%--                                                               name="ilegalItemlayEntityList[0].companycontent"--%>
<%--                                                                  type="text" style="width:75%;vertical-align: middle"></textarea>--%>
<%--                                                    </c:when>--%>
<%--                                                    <c:otherwise>--%>
                                                        第
                                                        <input type="text" class="easyui-validatebox"
                                                               data-options="width: 40,required:true"
                                                               onchange="queryDependtype(0);" id="companybar0"
                                                               name="ilegalItemlayEntityList[0].companybar"
                                                               value="${wfIlegalProcessesEntity.companybar }"/>條，第
                                                        <input name="ilegalItemlayEntityList[0].companyfund"
                                                               id="companyfund0" onchange="queryDependtype(0);"
                                                               value="${wfIlegalProcessesEntity.companyfund }"
                                                               type="text" style="width:40px"/>款，第
                                                        <input id="companyitem0"
                                                               value="${wfIlegalProcessesEntity.companyitem }"
                                                               onchange="queryDependtype(0);"
                                                               name="ilegalItemlayEntityList[0].companyitem" type="text"
                                                               style="width:40px"/>項
                                                        <textarea value="${wfIlegalProcessesEntity.companycontent }"
                                                               id="companycontent0"
                                                               name="ilegalItemlayEntityList[0].companycontent"
                                                                  type="text" style="width:75%;vertical-align: middle" rows="3">${wfIlegalProcessesEntity.companycontent }</textarea>
<%--                                                    </c:otherwise>--%>
<%--                                                </c:choose>--%>

                                            </td>
                                        </tr>
                                    </c:if>
                                    </tbody>
                                    <tr align="left" class="nottr" id="dependtypeadd">
                                        <td colspan="8" width="100%" style="text-align:center;padding-left:10px;">
                                            <a href="#" id="dependtypeItemAdd" class="easyui-linkbutton" data-options="iconCls:'icon-add',text:'添加一筆'" style="width: 100px"></a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="8"><input type="radio" name="dependtype" value="2"
                                                               onpropertychange="dependtypeChange(1);"
                                                               onchange="dependtypeChange(1);">法律法規：&nbsp;&nbsp;<input
                                                value="${wfIlegalProcessesEntity.laycontent }" name="laycontent"
                                                id="laycontent"
                                                type="text" disabled
                                                style="width:70%"/></td>
                                    </tr>
                                    <tr align="center">
                                        <td width="8%">建議處理類別&nbsp;<font color="red">*</font></td>
                                        <input id="sendpolice_" type="hidden"
                                               value="${wfIlegalProcessesEntity.sendpolice }"/>
                                        <td width="17%"><input type='checkbox' name='sendpolice' value='1'>送警</td>
                                        <input id="fire_" type="hidden" value="${wfIlegalProcessesEntity.fire }"/>
                                        <td width="8%"><input type='checkbox' name='fire' value="2"
                                                              onchange="checkcheckbox();">開除
                                        </td>
                                        <input id="bigguo_" type="hidden" value="${wfIlegalProcessesEntity.bigguo }"/>
                                        <td width="18%" colspan="2"><input type='checkbox' name='bigguo' value='3'
                                                                           onchange="checkcheckbox();">記大過&nbsp;
                                            <input id="bigguonum"
                                                   name="bigguonum"
                                                   class="easyui-combobox" value="${wfIlegalProcessesEntity.bigguonum}"
                                                   data-options="panelHeight: 200,width: 60,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/type_count',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; }"/>
                                        </td>
                                        <input id="smallguo_" type="hidden"
                                               value="${wfIlegalProcessesEntity.smallguo }"/>
                                        <td width="25%" colspan="2"><input type='checkbox' name='smallguo'
                                                                           value='4' onchange="checkcheckbox();">記小過&nbsp;
                                            <input id="smallguonum"
                                                   name="smallguonum"
                                                   class="easyui-combobox"
                                                   value="${wfIlegalProcessesEntity.smallguonum}"
                                                   data-options="panelHeight: 200,width: 60,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/type_count',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; }"/>
                                        </td>
                                        <input id="alarm_" type="hidden" value="${wfIlegalProcessesEntity.alarm }"/>
                                        <td width="24%"><input type='checkbox' name='alarm' value='5'
                                                               onchange="checkcheckbox();">警告&nbsp;
                                            <input id="alarmnum"
                                                   name="alarmnum"
                                                   class="easyui-combobox" value="${wfIlegalProcessesEntity.alarmnum}"
                                                   data-options="panelHeight: 200,width: 60,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/type_count',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; }"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="8" class="td_style1">
                                            嚴重違紀調查取證見證人（限依公司規定應開除情形）
                                        </td>
                                    </tr>
                                    <tr align="center" id="surey">
                                        <td width="8%" id="surveyno1">工號</td>
                                        <td width="17%" class="td_style1">
                                            <input id="surveyno" name="surveyno"
                                                   class="easyui-validatebox" data-options="width: 80"
                                                   value="${wfIlegalProcessesEntity.surveyno }"
                                                   onblur="querySurveyUserInfo();"/>
                                        </td>
                                        <td width="8%">姓名</td>
                                        <td width="10%" class="td_style1">
                                            <input id="surveyname" name="surveyname"
                                                   class="easyui-validatebox" data-options="width:100"
                                                   value="${wfIlegalProcessesEntity.surveyname}"/>
                                        </td>
                                        <td width="8%">聯繫電話</td>
                                        <td width="17%" class="td_style1">
                                            <input id="surveyphone" name="surveyphone"
                                                   class="easyui-validatebox" data-options="width: 160"
                                                   value="${wfIlegalProcessesEntity.surveyphone}"/>
                                        </td>
                                        <td width="8%">單位</td>
                                        <td width="24%" class="td_style1">
                                            <input id="surveyunit" name="surveyunit"
                                                   class="easyui-validatebox" data-options="width: 240"
                                                   value="${wfIlegalProcessesEntity.surveyunit}"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="8" class="td_style1">違紀提報單位</td>
                                    </tr>
                                    <tr align="center">
                                        <td width="8%">聯絡人&nbsp;<font color="red">*</font></td>
                                        <td width="17%" class="td_style1">
                                            <input id="contactno" name="contactno"
                                                   value="${wfIlegalProcessesEntity.contactno}"
                                                   class="easyui-validatebox" data-options="width: 70,required:true"
                                                   onblur="queryContactUserInfo();"/>
                                            /<input id="contactname" name="contactname"
                                                    value="${wfIlegalProcessesEntity.contactname}"
                                                    class="easyui-validatebox" data-options="width: 70,required:true"/>
                                        </td>
                                        <td width="8%">聯繫電話&nbsp;<font color="red">*</font></td>
                                        <td width="10%" class="td_style1">
                                            <input id="contactphone" name="contactphone"
                                                   value="${wfIlegalProcessesEntity.contactphone}"
                                                   class="easyui-validatebox" data-options="width:100,required:true"/>
                                        </td>
                                        <td width="8%">NOTES&nbsp;<font color="red">*</font></td>
                                        <td width="17%" class="td_style1">
                                            <input id="contactnotes" name="contactnotes"
                                                   value="${wfIlegalProcessesEntity.contactnotes}"
                                                   class="easyui-validatebox" data-options="width: 160,required:true"/>
                                        </td>
                                        <td width="8%">單位&nbsp;<font color="red">*</font></td>
                                        <td width="24%" class="td_style1">
                                            <input id="contactunit" name="contactunit"
                                                   value="${wfIlegalProcessesEntity.contactunit}"
                                                   class="easyui-validatebox" data-options="width: 240,required:true"/>
                                        </td>
                                    </tr>
                                    <tr align="center">
                                        <td width="8%">案件來源</td>
                                        <td width="35%" class="td_style1" colspan="3">
                                            <input id="caseresource" name="caseresource"
                                                   value="${wfIlegalProcessesEntity.caseresource}"
                                                   class="easyui-validatebox" data-options="width: 400"/>
                                        </td>
                                        <td width="8%">承辦人&nbsp;<font color="red">*</font></td>
                                        <td width="17%" class="td_style1">
                                            <input id="takeno" name="takeno" value="${wfIlegalProcessesEntity.takeno}"
                                                   class="easyui-validatebox" data-options="width:70,required:true"
                                                   onblur="queryTakeUserInfo();"/>
                                            /<input id="takename" name="takename"
                                                    value="${wfIlegalProcessesEntity.takename}"
                                                    class="easyui-validatebox" data-options="width:70,required:true"/>
                                        </td>
                                        <td width="8%">承辦人主管&nbsp;<font color="red">*</font></td>
                                        <td width="24%" class="td_style1">
                                            <input id="takechargeno" name="takechargeno"
                                                   value="${wfIlegalProcessesEntity.takechargeno}"
                                                   class="easyui-validatebox" data-options="width:70,required:true"
                                                   onblur="queryTakeChargeUserInfo();"/>
                                            /<input id="takechargename" name="takechargename"
                                                    value="${wfIlegalProcessesEntity.takechargename}"
                                                    class="easyui-validatebox" data-options="width:70,required:true"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th style="text-align:left;" colspan="8">&nbsp;&nbsp;審核路徑及審核記錄
                                            <a href="javascript:void(0)"
                                               onclick="showWfImag(${processId},'違紀處理申請表');">點擊查看簽核流程圖</a>
                                        </th>
                                    </tr>
                                    <tr>
                                        <td colspan="8" style="text-align:left;">
                                            <table class="flowList"
                                                   style="margin-left:5px;margin-top:5px;width:99%">
                                                <tr>
                                                    <td style="border:none">
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="rzcommendilegalTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['rzcommendilegalno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole2(133,'rzcommendilegalTable','rzcommendilegalno','rzcommendilegalname',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="rzcommendilegalno"
                                                                           name="rzcommendilegalno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['rzcommendilegalno']}"
                                                                           readonly
                                                                           value="${wfIlegalProcessesEntity.rzcommendilegalno }"/><c:if
                                                                        test="${requiredMap['rzcommendilegalno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="rzcommendilegalname"
                                                                            name="rzcommendilegalname"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['rzcommendilegalno']}"
                                                                            value="${wfIlegalProcessesEntity.rzcommendilegalname }"/>
                                                                </td>
                                                            </tr>
                                                        </table>

                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="rzychargeTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['rzychargeno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole4(134,'rzychargeno','rzychargename',$('#factoryid0').combobox('getValue'))"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="rzychargeno" name="rzychargeno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['rzychargeno']}"
                                                                           readonly
                                                                           value="${wfIlegalProcessesEntity.rzychargeno }"/><c:if
                                                                        test="${requiredMap['rzychargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="rzychargename" name="rzychargename"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['rzychargeno']}"
                                                                            value="${wfIlegalProcessesEntity.rzychargename }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="hqfwTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">${requiredMap['hqfwno_name']}</td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole2(284,'hqfwTable','hqfwno','hqfwname',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="hqfwno" name="hqfwno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['hqfwno']}"
                                                                           readonly
                                                                           value="${wfIlegalProcessesEntity.hqfwno }"/><c:if
                                                                        test="${requiredMap['hqfwno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="hqfwname" name="hqfwname"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['hqfwno']}"
                                                                            value="${wfIlegalProcessesEntity.hqfwname }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="undertakormanagerTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: center;">
                                                                                ${requiredMap['undertakormanagerno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <%--                                                                    <div class="float_L qhUserIcon"--%>
                                                                                <%--                                                                         onclick="selectRole($('#dealdeptno').val(),'undertakormanager','undertakormanager',$('#dealfactoryid').val())"></div>--%>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="undertakormanagerno"
                                                                           name="undertakormanagerno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['undertakormanagerno']}"
                                                                           readonly
                                                                           value="${wfIlegalProcessesEntity.undertakormanagerno }"/><c:if
                                                                        test="${requiredMap['undertakormanagerno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="undertakormanagername"
                                                                            name="undertakormanagername"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['undertakormanagerno']}"
                                                                            value="${wfIlegalProcessesEntity.undertakormanagername }"/>
                                                                </td>
                                                            </tr>
                                                        </table>

                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="kchargeTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['kchargeno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole($('#deptno0').val(),'kchargeno','kchargename',$('#factoryid0').combobox('getValue'))"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="kchargeno" name="kchargeno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                           readonly
                                                                           value="${wfIlegalProcessesEntity.kchargeno }"/><c:if
                                                                        test="${requiredMap['kchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="kchargename" name="kchargename"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                            value="${wfIlegalProcessesEntity.kchargename }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="border:none">
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="bchargeTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['bchargeno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole($('#deptno0').val(),'bchargeno','bchargename',$('#factoryid0').combobox('getValue'))"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="bchargeno" name="bchargeno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                           readonly
                                                                           value="${wfIlegalProcessesEntity.bchargeno }"/><c:if
                                                                        test="${requiredMap['bchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="bchargename" name="bchargename"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                            value="${wfIlegalProcessesEntity.bchargename }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="cchargeTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['cchargeno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole18('cchargeTable',$('#deptno0').val(),'cchargeno','cchargename',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="cchargeno" name="cchargeno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                           readonly
                                                                           value="${wfIlegalProcessesEntity.cchargeno }"/><c:if
                                                                        test="${requiredMap['cchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="cchargename" name="cchargename"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                            value="${wfIlegalProcessesEntity.cchargename }"/>
                                                                </td>
                                                            </tr>
                                                        </table>

                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="zchargeTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['zchargeno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole3('zchargeTable',$('#deptno0').val(),'zchargeno','zchargename',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="zchargeno" name="zchargeno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                           readonly
                                                                           value="${wfIlegalProcessesEntity.zchargeno }"/><c:if
                                                                        test="${requiredMap['zchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="zchargename" name="zchargename"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                            value="${wfIlegalProcessesEntity.zchargename }"/>
                                                                </td>
                                                            </tr>
                                                        </table>

                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="zcchargeTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['zcchargeno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole3('zcchargeTable',$('#deptno0').val(),'zcchargeno','zcchargename',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="zcchargeno" name="zcchargeno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                           readonly
                                                                           value="${wfIlegalProcessesEntity.zcchargeno }"/><c:if
                                                                        test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="zcchargename" name="zcchargename"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                            value="${wfIlegalProcessesEntity.zcchargename }"/>
                                                                </td>
                                                            </tr>
                                                        </table>

                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="rzbchargeTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['rzbchargeno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole2(18,'rzbchargeTable','rzbchargeno','rzbchargename',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="rzbchargeno" name="rzbchargeno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['rzbchargeno']}"
                                                                           readonly
                                                                           value="${wfIlegalProcessesEntity.rzbchargeno }"/><c:if
                                                                        test="${requiredMap['rzbchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="rzbchargename" name="rzbchargename"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['rzbchargeno']}"
                                                                            value="${wfIlegalProcessesEntity.rzbchargename }"/>
                                                                </td>
                                                            </tr>
                                                        </table>


                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="border:none">
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="yl1Table">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['ylno1_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole2(135,'yl1Table','ylno1','ylname1',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="ylno1" name="ylno1"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['ylno1']}"
                                                                           readonly
                                                                           value="${wfIlegalProcessesEntity.ylno1 }"/><c:if
                                                                        test="${requiredMap['ylno1'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="ylname1" name="ylname1"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['ylno1']}"
                                                                            value="${wfIlegalProcessesEntity.ylname1 }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="pcchargeTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['pcchargeno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole3('pcchargeTable',$('#deptno0').val(),'pcchargeno','pcchargename',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="pcchargeno" name="pcchargeno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                           readonly
                                                                           value="${wfIlegalProcessesEntity.pcchargeno }"/><c:if
                                                                        test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="pcchargename" name="pcchargename"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                            value="${wfIlegalProcessesEntity.pcchargename }"/>
                                                                </td>
                                                            </tr>
                                                        </table>

                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="labourTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['labourno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole2(136,'labourTable','labourno','labourname',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="labourno" name="labourno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['labourno']}"
                                                                           readonly
                                                                           value="${wfIlegalProcessesEntity.labourno }"/><c:if
                                                                        test="${requiredMap['labourno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="labourname" name="labourname"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['labourno']}"
                                                                            value="${wfIlegalProcessesEntity.labourname }"/>
                                                                </td>
                                                            </tr>
                                                        </table>

                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="yl2Table">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['ylno2_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole2(28,'yl2Table','ylno2','ylname2',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="ylno2" name="ylno2"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['ylno2']}"
                                                                           readonly
                                                                           value="${wfIlegalProcessesEntity.ylno2 }"/><c:if
                                                                        test="${requiredMap['ylno2'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="ylname2" name="ylname2"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['ylno2']}"
                                                                            value="${wfIlegalProcessesEntity.ylname2 }"/>
                                                                </td>
                                                            </tr>
                                                        </table>

                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="groupchargeTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['groupchargeno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole2(29,'groupchargeTable','groupchargeno','groupchargename',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="groupchargeno" name="groupchargeno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['groupchargeno']}"
                                                                           readonly
                                                                           value="${wfIlegalProcessesEntity.groupchargeno }"/><c:if
                                                                        test="${requiredMap['groupchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="groupchargename" name="groupchargename"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['groupchargeno']}"
                                                                            value="${wfIlegalProcessesEntity.groupchargename }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="border:none">
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="endchargeTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">${requiredMap['endchargeno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole4(137,'endchargeno','endchargename',$('#factoryid0').combobox('getValue'))"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="endchargeno" name="endchargeno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['endchargeno']}"
                                                                           readonly
                                                                           value="${wfIlegalProcessesEntity.endchargeno }"/><c:if
                                                                        test="${requiredMap['endchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="endchargename" name="endchargename"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['endchargeno']}"
                                                                            value="${wfIlegalProcessesEntity.endchargename }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfIlegalProcessesEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="canelTask('${wfIlegalProcessesEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="onlyKchargeSignle" value="1" />
    <div id="win"></div>
    <div id="dlg"></div>
</form>
</div>
<div id="optionWin" class="easyui-window" title="違紀申請表" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%">
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="file" type="file" style="width: 300px"
                           accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>
                    <input id="isExcel2003" name="isExcel2003" type="hidden" value="">

                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr>
                <td align="left" style="width: 60%; white-space: nowrap;">
                    <span id="tishi">正在導入中，請稍後...</span>
                </td>
            </tr>
            <%--            <tr align="center">--%>
            <%--                <td style="width: 60%; white-space: nowrap;">--%>
            <%--                    <span id="labelListAddResult"></span><a href="${ctx}/wfsslvpnprocess/errorExcel"--%>
            <%--                                                            id="downloadError"--%>
            <%--                                                            plain="true">查看錯誤信息</a>--%>
            <%--                </td>--%>
            <%--            </tr>--%>
        </table>
    </form>
</div>
<script src='${ctx}/static/js/humanCapital/wfilegalprocesses.js?random=<%= Math.random()%>'></script>
</body>
</html>
