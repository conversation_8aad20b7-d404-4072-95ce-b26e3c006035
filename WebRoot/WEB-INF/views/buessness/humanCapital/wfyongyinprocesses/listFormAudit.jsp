<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>行政專用章用印申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<style type="text/css">
    .td_style2 {
        text-align: left;
    }
</style>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/wfyongyinprocesses/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfyongyinprocessesEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfyongyinprocessesEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">行政專用章用印申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfyongyinprocessesEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfyongyinprocessesEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfyongyinprocessesEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfyongyinprocessesEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfyongyinprocessesEntity.makerno}/${wfyongyinprocessesEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
           <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">申請人基本資料</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">工號</td>
                            <td width="10%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox inputCss"
                                       data-options="width: 80" readonly value="${wfyongyinprocessesEntity.applyno}"/>
                                <input type="hidden" id="applyfactoryid" name="factoryid" value="${wfyongyinprocessesEntity.factoryid}"/>
                            </td>
                            <td width="8%">姓名</td>
                            <td width="10%" class="td_style1">
                                <input id="applyname" name="applyname" class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfyongyinprocessesEntity.applyname }"/>
                            </td>
                            <td width="8%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="applydeptno" name="deptno" class="easyui-validatebox inputCss"
                                       data-options="width: 90" value="${wfyongyinprocessesEntity.deptno}" readonly/>
                            </td>
                            <td width="8%">單位名稱</td>
                            <td width="38%" class="td_style1">
                                <input id="deptname" name="deptname" class="easyui-validatebox inputCss"
                                       data-options="width: 350" value="${wfyongyinprocessesEntity.deptname }" readonly/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">手機號碼</td>
                            <td width="10%" class="td_style1">
                                <input id="phone" name="phone" class="easyui-validatebox inputCss"
                                       data-options="width: 100" value="${wfyongyinprocessesEntity.phone}"/>
                            </td>
                            <td width="8%">使用原因說明</td>
                            <td colspan="5" class="td_style2">
                                <div class="reasonDiv"></div>
                                <input id="reason" name="reason" type="hidden" class="easyui-validatebox"
                                       data-options="width: 300" value="${wfyongyinprocessesEntity.reason }"/>
                                <input type="hidden" id="otherreasonAudit"   value="${wfyongyinprocessesEntity.otherreason}" />
                                <input type="hidden" id="disOrEnabled"   value="disabled" />
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">附件</td>
                            <td colspan="7" class="td_style1">
                                <input type="hidden" id="attachids" name="attachids" value="${wfyongyinprocessesEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">附件說明</td>
                            <td colspan="7" class="td_style2">&nbsp;&nbsp;附件為需用印蓋章的材料模板</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">批註</td>
                            <td colspan="7" class="td_style1">
						        <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                          style="width:1000px;height:60px;" rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                    </table>
                </td>
           </tr>
            <tr>
                <td>
                     <table class="formList">
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" serialNo="${wfyongyinprocessesEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','行政專用章用印申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfyongyinprocessesEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
  <div id="dlg"></div>
<script src='${ctx}/static/js/humanCapital/wfyongyinprocesses.js?random=<%= Math.random()%>'></script>
</body>
</html>