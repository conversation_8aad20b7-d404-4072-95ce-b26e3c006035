<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>員工休假期滿複職申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<form id="mainform" action="${ctx}/wffuzhiprocess/${action}"
		method="post">
		<input id="ids" name="ids" type="hidden"
			value="${wffuzhiprocessEntity.id }" /> <input id="serialno"
			name="serialno" type="hidden"
			value="${wffuzhiprocessEntity.serialno }" /> <input id="makerno"
			name="makerno" type="hidden" value="${wffuzhiprocessEntity.makerno }" />
		<input id="makername" name="makername" type="hidden"
			value="${wffuzhiprocessEntity.makername }" /> <input id="makerdeptno"
			name="makerdeptno" type="hidden"
			value="${wffuzhiprocessEntity.makerdeptno }" /> <input
			id="makerfactoryid" name="makerfactoryid" type="hidden"
			value="${wffuzhiprocessEntity.makerfactoryid }" />
		<div class="commonW">
			<div class="headTitle">員工休假期滿複職申請單</div>
			<div class="position_L">
				任務編碼：<span style="color:#999;"> <c:choose>
						<c:when test="${wffuzhiprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
						<c:otherwise>
                           ${wffuzhiprocessEntity.serialno}
                       </c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_L1 margin_L">
				填單時間：<span style="color:#999;"> <c:choose>
						<c:when test="${wffuzhiprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
						<c:otherwise>
							<input style="border:0px" style="width: 120px" readonly="true"
								value="<fmt:formatDate value='${wffuzhiprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>" />
						</c:otherwise>
					</c:choose>
				</span>
			</div>
			<c:if test="${empty wffuzhiprocessEntity.makerno}">
				<div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
			</c:if>
			<c:if test="${not empty wffuzhiprocessEntity.makerno}">
				<div class="position_R margin_R">填單人：${wffuzhiprocessEntity.makerno}/${wffuzhiprocessEntity.makername}</div>
			</c:if>
			<div class="clear"></div>
			<table class="formList">
				<tr align="center">
					<td colspan="10" class="td_style1">申請人基本信息</td>
				</tr>
				<tr align="center">
					<td width="10%">工號&nbsp;<font color="red">*</font></td>
					<td width="10%"><input id="dealno" name="dealno"
						onblur="queryUserInfo(this)" class="easyui-validatebox"
						data-options="width: 80,required:true"
						value="${wffuzhiprocessEntity.dealno}" /></td>
					<td width="10%">姓名</td>
					<td width="10%"><input id="dealname" name="dealname" readonly
						class="easyui-validatebox inputCss" data-options="width:60"
						value="${wffuzhiprocessEntity.dealname}" /><input
						id="dealfactoryid" name="dealfactoryid" type="hidden"
						class="easyui-validatebox inputCss"
						data-options="width:50,required:true"
						value="${wffuzhiprocessEntity.dealfactoryid}" /></td>
					<td width="10%">單位代碼</td>
					<td width="10%"><input id="dealdeptno" name="dealdeptno"
						readonly class="easyui-validatebox inputCss"
						data-options="width:150"
						value="${wffuzhiprocessEntity.dealdeptno}" /></td>
					<td width="7%">單位名稱</td>
					<td colspan="3" width="34%"><input id="dealdeptname"
						style="width:320px" name="dealdeptname" class="easyui-validatebox"
						data-options="width:320,required:true"
						value="${wffuzhiprocessEntity.dealdeptname}" /></td>
				</tr>
				<tr align="center">
					<td>身份證號碼&nbsp;<font color="red">*</font></td>
					<td colspan="2"><input id="idcard" name="idcard"
						class="easyui-validatebox"
						data-options="width: 160,required:true,validType:'idCode[\'idcard\']'"
						value="${wffuzhiprocessEntity.idcard}" /></td>
					<td>現居住地址&nbsp;<font color="red">*</font></td>
					<td colspan="2"><input id="address" name="address"
						class="easyui-validatebox" data-options="width: 240,required:true"
						value="${wffuzhiprocessEntity.address}" /></td>
					<td>工作區域&nbsp;<font color="red">*</font></td>
					<td><input id="workarea" name="workarea"
						class="easyui-validatebox" data-options="width: 90,required:true"
						value="${wffuzhiprocessEntity.workarea}" /></td>
					<td>手機號碼&nbsp;<font color="red">*</font></td>
					<td><input id="phoneno" name="phoneno"
						class="easyui-validatebox"
						data-options="width: 100,required:true,validType:'phone[\'phoneno\',\'手機格式不正確\']'"
						value="${wffuzhiprocessEntity.phoneno}" /></td>
				</tr>
				<tr align="center">
					<td>直屬主管信息</td>
					<td>工號&nbsp;<font color="red">*</font></td>
					<td><input id="chargeno" name="chargeno"
						onblur="queryUserInfo1(this)" class="easyui-validatebox"
						data-options="width: 80,required:true"
						value="${wffuzhiprocessEntity.chargeno}" /></td>
					<td>姓名</td>
					<td><input id="chargename" name="chargename"
						class="easyui-validatebox inputCss"
						data-options="width:80,required:true" readonly
						value="${wffuzhiprocessEntity.chargename}" /></td>
					<td>聯繫方式</td>
					<td colspan="2"><input id="chargephone" name="chargephone"
						class="easyui-validatebox" data-options="width: 150,required:true"
						value="${wffuzhiprocessEntity.chargephone}" /></td>
					<td colspan="2"></td>
				</tr>
				<tr align="center">
					<td>休假信息</td>
					<td>開始日期&nbsp;<font color="red">*</font></td>
					<td><input id="leavebegintime" name="leavebegintime"
						onblur="days()" class="Wdate"
						data-options="width:100,required:true" style="width:100px"
						value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${wffuzhiprocessEntity.leavebegintime}"/>"
						onclick="WdatePicker({maxDate:'#F{$dp.$D(\'leaveendtime\')}',dateFmt:'yyyy-MM-dd'})" /></td>
					<td>結束日期&nbsp;<font color="red">*</font></td>
					<td><input id="leaveendtime" name="leaveendtime" class="Wdate"
						onblur="days()" data-options="width:100,required:true"
						style="width:100px"
						value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${wffuzhiprocessEntity.leaveendtime}"/>"
						onclick="WdatePicker({minDate:'#F{$dp.$D(\'leavebegintime\')}',dateFmt:'yyyy-MM-dd'})" /></td>
					<td>合計天數</td>
					<td><input id="leavedays" name="leavedays" readonly
						class="easyui-validatebox inputCss" data-options="width: 150"
						value="${wffuzhiprocessEntity.leavedays}" /></td>
					<td colspan="3"></td>
				</tr>
				<tr align="center">
					<td>複職原因</td>
					<td colspan="9"><div class="reasonDiv"></div> <input
						id="reason" name="reason" class="easyui-validatebox"
						data-options="width: 150" type="hidden"
						value="${wffuzhiprocessEntity.reason}" /> <input
						id="privateaffairs1" name="privateaffairs1" disabled="disabled"
						type="hidden" class="easyui-validatebox" data-options="width:150"
						value="${wffuzhiprocessEntity.privateaffairs}" /></td>
				</tr>
				<tr align="center">
					<td>住宿申請</td>
					<td colspan="3"><div class="habitancyDiv"></div> <input
						id="habitancy" name="habitancy" class="easyui-validatebox"
						data-options="width: 150" type="hidden"
						value="${wffuzhiprocessEntity.habitancy}" /></td>
					<td colspan="2" align="left"><input id="habitancyfacid"
						name="habitancyfacid" class="easyui-combobox"
						data-options="width: 150,required:true"
						value="${wffuzhiprocessEntity.habitancyfacid }" /></td>
					<td>體檢費用（單位:RMB元）</td>
					<td colspan="3" align="left">
						<input id="moneycount" name="moneycount" class="easyui-validatebox" data-options="width: 150,required:true"
							   value="${wffuzhiprocessEntity.moneycount}" ></input>
					</td>
				</tr>
				<tr align="center">
					<td>附件&nbsp;<font color="red">*</font></td>
					<td colspan="9" class="td_style1">
						<span class="sl-custom-file">
							<input type="button" value="点击上传文件" class="btn-file" />
							<input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file" />
						</span>
						<input type="hidden" id="attachids" name="attachids" value="${wffuzhiprocessEntity.attachids }" />
						<div id="dowloadUrl">
							<c:forEach items="${file}" varStatus="i" var="item">
								<div id="${item.id}" style="line-height:30px;margin-left:5px;"
									class="float_L">
									<div class="float_L">
										<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
									</div>
									<div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
								</div>
							</c:forEach>
						</div></td>
				</tr>
				<tr align="center" style="height: 100%" >
					<td>備註</td>
					<td colspan="9" class="td_style2">
						<input id="applyMemo" type="hidden" value="${applyMemo}"/>
						<textarea id="applyTextareaMemo" class="easyui-validatebox"
								  disabled readonly
								  style="width:99%;resize:none;background-color: #F2F5F7;border: 0px;outline: none;">${applyMemo}</textarea>
					</td>
				</tr>
				<tr>
					<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
						<a href="javascript:void(0)"
						onclick="showWfImag('${processId}','員工複職申請單');">點擊查看簽核流程圖</a>
					</th>
				</tr>
				<tr>
					<td colspan="10" style="text-align:left;">
						<table class="flowList"
							style="margin-left:5px;margin-top:5px;width:99%">
							<tr>
								<td style="border:none">
									<table width="16%" style="float: left;margin-left: 5px;"
										id="yl1Table">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['ylno1_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole4(138,'ylno1','ylname1',$('#dealfactoryid').val())"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="ylno1" name="ylno1"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['ylno1']}"
												readonly value="${wffuzhiprocessEntity.ylno1 }" /><c:if test="${requiredMap['ylno1'].equals('true')}"><font color="red">*</font>
												</c:if>/<input id="ylname1" name="ylname1" readonly
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['ylno1']}"
												value="${wffuzhiprocessEntity.ylname1 }" /></td>
										</tr>
									</table>
									<table width="16%" style="float: left;margin-left: 5px;"
										id="yl2Table">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['ylno2_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole4(139,'ylno2','ylname2',$('#dealfactoryid').val())"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="ylno2" name="ylno2"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['ylno2']}"
												readonly value="${wffuzhiprocessEntity.ylno2 }" /><c:if test="${requiredMap['ylno2'].equals('true')}"><font color="red">*</font>
												</c:if>/<input id="ylname2" name="ylname2" readonly
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['ylno2']}"
												value="${wffuzhiprocessEntity.ylname2 }" /></td>
										</tr>
									</table>
									<table width="16%" style="float: left;margin-left: 5px;"
										id="yl7Table">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td colspan="2" style="border: none;text-align: center;">${requiredMap['ylno7_name']}</td>
													</tr>
												</table>
											</td>
										</tr>
										<c:choose>
											<c:when test="${not empty factoryType&&('IPEJY' eq factoryType||'CAAJY' eq factoryType)}">
												<c:if test="${empty wffuzhiprocessEntity.makerno}">
													<tr>
														<td><input id="ylno7" name="ylno7"
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['ylno7']}"
															readonly value="${user.loginName}" /> <c:if
																test="${requiredMap['ylno7'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname7" name="ylname7" readonly
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['ylno7']}"
															value="${user.name}" /></td>
													</tr>
												</c:if>
												<c:if test="${not empty wffuzhiprocessEntity.makerno}">
													<tr>
														<td><input id="ylno7" name="ylno7"
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['ylno7']}"
															readonly value="${wffuzhiprocessEntity.makerno}" /> <c:if
																test="${requiredMap['ylno7'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname7" name="ylname7" readonly
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['ylno7']}"
															value="${wffuzhiprocessEntity.makername}" /></td>
													</tr>
												</c:if>
											</c:when>
											<c:otherwise>
												<tr>
													<td><input id="ylno7" name="ylno7" readonly
														style="background-color:#D0D0D0"
														class="easyui-validatebox"
														data-options="width:80,required:${requiredMap['ylno7']}"
														readonly value="${wffuzhiprocessEntity.ylno7 }" /> <c:if
															test="${requiredMap['ylno7'].equals('true')}">
															<font color="red">*</font>
														</c:if> /<input id="ylname7" name="ylname7" readonly
														style="background-color:#D0D0D0"
														class="easyui-validatebox"
														data-options="width:80,required:${requiredMap['ylno7']}"
														value="${wffuzhiprocessEntity.ylname7 }" /></td>
												</tr>
											</c:otherwise>
										</c:choose>
									</table>
									<table width="16%" style="float: left;margin-left: 5px;"
										id="yl4Table">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['ylno4_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole4(141,'ylno4','ylname4',$('#dealfactoryid').val())"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="ylno4" name="ylno4"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['ylno4']}"
												readonly value="${wffuzhiprocessEntity.ylno4 }" /> <c:if
													test="${requiredMap['ylno4'].equals('true')}">
													<font color="red">*</font>
												</c:if> /<input id="ylname4" name="ylname4" readonly
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['ylno4']}"
												value="${wffuzhiprocessEntity.ylname4 }" /></td>
										</tr>
									</table>
									<table width="16%" style="float: left;margin-left: 5px;"
										id="yl3Table">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['ylno3_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole4(142,'ylno3','ylname3',$('#dealfactoryid').val())"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="ylno3" name="ylno3"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['ylno3']}"
												readonly value="${wffuzhiprocessEntity.ylno3 }" /><c:if test="${requiredMap['ylno3'].equals('true')}"><font color="red">*</font></c:if>/<input id="ylname3" name="ylname3" readonly
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['ylno3']}"
												value="${wffuzhiprocessEntity.ylname3 }" /></td>
										</tr>
									</table>
									<table width="16%" style="float: left;margin-left: 5px;"
										id="yl6Table">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['ylno6_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole4(143,'ylno6','ylname6',$('#dealfactoryid').val())"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="ylno6" name="ylno6"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['ylno6']}"
												readonly value="${wffuzhiprocessEntity.ylno6 }" /><c:if test="${requiredMap['ylno6'].equals('true')}"><font color="red">*</font></c:if>/<input id="ylname6" name="ylname6" readonly
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['ylno6']}"
												value="${wffuzhiprocessEntity.ylname6 }" /></td>
										</tr>
									</table>

								</td>
							</tr>
							<tr>
								<td style="border:none">
									<table width="16%" style="float: left;margin-left: 5px;"
										id="yl5Table">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td id="ssfpckTd" style="border: none;text-align: right;">${requiredMap['ylno5_name']}</td>
														<td style="border: none;">
															<div id="ssfpckDiv" class="float_L qhUserIcon"
																onclick="selectRole4(144,'ylno5','ylname5',$('#dealfactoryid').val())"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="ylno5" name="ylno5"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['ylno5']}"
												readonly value="${wffuzhiprocessEntity.ylno5 }" /><span id="bitian"></span>
											<c:if test="${requiredMap['ylno5'].equals('true')}">
													<font color="red">*</font>
												</c:if> /<input id="ylname5" name="ylname5" readonly
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['ylno5']}"
												value="${wffuzhiprocessEntity.ylname5 }" /></td>
										</tr>
									</table>
									<table width="16%" style="float: left;margin-left: 5px;"
										id="kchargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealfactoryid').val())"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="kchargeno" name="kchargeno"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['kchargeno']}"
												readonly value="${wffuzhiprocessEntity.kchargeno }" /> <c:if
													test="${requiredMap['kchargeno'].equals('true')}">
													<font color="red">*</font>
												</c:if> /<input id="kchargename" name="kchargename" readonly
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['kchargeno']}"
												value="${wffuzhiprocessEntity.kchargename }" /></td>
										</tr>
									</table>
									<table width="16%" style="float: left;margin-left: 5px;"
										id="bchargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealfactoryid').val())"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="bchargeno" name="bchargeno"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['bchargeno']}"
												readonly value="${wffuzhiprocessEntity.bchargeno }" /> <c:if
													test="${requiredMap['bchargeno'].equals('true')}">
													<font color="red">*</font>
												</c:if> /<input id="bchargename" name="bchargename" readonly
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['bchargeno']}"
												value="${wffuzhiprocessEntity.bchargename }" /></td>
										</tr>
									</table>
									<table width="16%" style="float: left;margin-left: 5px;"
										id="cchargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																 onclick="selectRole18('cchargeTable',$('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="cchargeno" name="cchargeno"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['cchargeno']}"
												readonly value="${wffuzhiprocessEntity.cchargeno }" /> <c:if
													test="${requiredMap['cchargeno'].equals('true')}">
													<font color="red">*</font>
												</c:if> /<input id="cchargename" name="cchargename" readonly
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['cchargeno']}"
												value="${wffuzhiprocessEntity.cchargename }" /></td>
										</tr>
									</table>
									<table width="16%" style="float: left;margin-left: 5px;"
										id="zchargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealfactoryid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="zchargeno" name="zchargeno"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['zchargeno']}"
												readonly value="${wffuzhiprocessEntity.zchargeno }" /> <c:if
													test="${requiredMap['zchargeno'].equals('true')}">
													<font color="red">*</font>
												</c:if> /<input id="zchargename" name="zchargename" readonly
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['zchargeno']}"
												value="${wffuzhiprocessEntity.zchargename }" /></td>
										</tr>
									</table>
									<table width="16%" style="float: left;margin-left: 5px;"
										id="zcchargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealfactoryid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="zcchargeno" name="zcchargeno"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['zcchargeno']}"
												readonly value="${wffuzhiprocessEntity.zcchargeno }" /> <c:if
													test="${requiredMap['zcchargeno'].equals('true')}">
													<font color="red">*</font>
												</c:if> /<input id="zcchargename" name="zcchargename" readonly
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['zcchargeno']}"
												value="${wffuzhiprocessEntity.zcchargename }" /></td>
										</tr>
									</table>
								</td>
							</tr>
							<tr>
								<td style="border:none">
									<table width="16%" style="float: left;margin-left: 5px;"
										id="pcchargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole3('pcchargeTable',$('#dealdeptno').val(),'pcchargeno','pcchargename',$('#dealfactoryid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="pcchargeno" name="pcchargeno"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['pcchargeno']}"
												readonly value="${wffuzhiprocessEntity.pcchargeno }" /> <c:if
													test="${requiredMap['pcchargeno'].equals('true')}">
													<font color="red">*</font>
												</c:if> /<input id="pcchargename" name="pcchargename" readonly
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['pcchargeno']}"
												value="${wffuzhiprocessEntity.pcchargename }" /></td>
										</tr>
									</table>
									<table width="16%" style="float: left;margin-left: 5px;"
										id="yl8Table">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['ylno8_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole2(28,'yl8Table','ylno8','ylname8',$('#dealfactoryid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="ylno8" name="ylno8"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['ylno8']}"
												readonly value="${wffuzhiprocessEntity.ylno8 }" /> <c:if
													test="${requiredMap['ylno8'].equals('true')}">
													<font color="red">*</font>
												</c:if> /<input id="ylname8" name="ylname8" readonly
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['ylno8']}"
												value="${wffuzhiprocessEntity.ylname8 }" /></td>
										</tr>
									</table>
									<table width="16%" style="float: left;margin-left: 5px;"
										id="yl9Table">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['ylno9_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole2(29,'yl9Table','ylno9','ylname9',$('#dealfactoryid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="ylno9" name="ylno9"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['ylno9']}"
												readonly value="${wffuzhiprocessEntity.ylno9 }" /> <c:if
													test="${requiredMap['ylno9'].equals('true')}">
													<font color="red">*</font>
												</c:if> /<input id="ylname9" name="ylname9" readonly
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['ylno9']}"
												value="${wffuzhiprocessEntity.ylname9 }" /></td>
										</tr>
									</table>
									<table width="16%" style="float: left;margin-left: 5px;"
										id="xzchargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['xzchargeno_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole2(128,'xzchargeTable','xzchargeno','xzchargename',$('#dealfactoryid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="xzchargeno" name="xzchargeno"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['xzchargeno']}"
												readonly value="${wffuzhiprocessEntity.xzchargeno }" /><c:if test="${requiredMap['xzchargeno'].equals('true')}"><font color="red">*</font></c:if> /<input id="xzchargename" name="xzchargename" readonly
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['xzchargeno']}"
												value="${wffuzhiprocessEntity.xzchargename }" /></td>
										</tr>
									</table>
									<table width="16%" style="float: left;margin-left: 5px;"
										id="rzbchargeTable">
										<tr>
											<td>
												<table width="100%">
													<tr>
														<td style="border: none;text-align: right;">${requiredMap['rzbchargeno_name']}</td>
														<td style="border: none;">
															<div class="float_L qhUserIcon"
																onclick="selectRole2(18,'rzbchargeTable','rzbchargeno','rzbchargename',$('#dealfactoryid').val(),null)"></div>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><input id="rzbchargeno" name="rzbchargeno"
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['rzbchargeno']}"
												readonly value="${wffuzhiprocessEntity.rzbchargeno }" /><c:if test="${requiredMap['rzbchargeno'].equals('true')}"><font color="red">*</font></c:if>/<input id="rzbchargename" name="rzbchargename" readonly
												class="easyui-validatebox"
												data-options="width:80,required:${requiredMap['xzchargeno']}"
												value="${wffuzhiprocessEntity.rzbchargename }" /></td>
										</tr>
									</table>
								</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td colspan="10" style="text-align:left;"><iframe
							id="qianheLogFrame" name="qianheLogFrame"
							src="${ctx}/wfcontroller/goChargeLog?serialNo=${wffuzhiprocessEntity.serialno}"
							width="100%"></iframe></td>
				</tr>
				<tr>
					<td colspan="10"
						style="border:none;text-align:center;margin-top:10px"><a
						href="javascript:;" id="btnSave" class="easyui-linkbutton"
						data-options="iconCls:'icon-add'" style="width: 100px;"
						onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp; <a
						href="#" id="btnSubmit" class="easyui-linkbutton"
						data-options="iconCls:'icon-ok'" style="width: 100px;"
						onclick="canelTask('${wffuzhiprocessEntity.serialno }');">取消申請</a>
					</td>
				</tr>
			</table>
		</div>
		<input type="hidden" id="deptNo" name="deptNo" value="" /> <input
			type="hidden" id="chargeNo" name="chargeNo" value="" /> <input
			type="hidden" id="chargeName" name="chargeName" value="" /> <input
			type="hidden" id="factoryId" name="factoryId" value="" /> <input
			type="hidden" id="dutyId" name="dutyId" value="" />
		<input type="hidden" id="onlyKchargeSignle" value="1" />
		<div id="win"></div>
		<div id="dlg"></div>
	</form>
	<script src='${ctx}/static/js/humanCapital/wffuzhiprocess.js?random=<%= Math.random()%>'></script>
	<script type="text/javascript">
		applyMemoHeight();
	</script>
</body>
</html>