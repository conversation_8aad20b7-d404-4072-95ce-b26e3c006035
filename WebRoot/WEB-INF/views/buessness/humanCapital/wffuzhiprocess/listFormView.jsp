<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>員工休假期滿複職申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<form id="mainform" action="${ctx}/wffuzhiprocess/${action}"
		method="post">
		<!--
		        workflowid 流程編碼
    serialno 表單流水號
    processid 工單實例id
    dealno 申請人工號
    dealname 申請人名稱
    dealdeptno 單位代碼
    dealdeptname 單位名稱
    makerno 填單人
    makername 填單人名稱
    createtime 創建時間
    phoneno 手機號
    idcard 身份證
    address 現居住地
    workarea 工作區域
    chargeno 主管工號
    chargename 主管姓名
    chargephone 主管手機號
    leavebegintime 休假開始時間
    leaveendtime 休假結束時間
    leavedays 合計天數
    reason 複職原因
    habitancy 住宿申請
    habitancyfacid 住宿廠區
    ylno1 人資複職初核窗口
    ylno2 衛生部體檢窗口
    ylno3 體檢單回收及識別證領取
    ylno4 衛生部主管審核窗口
    ylno5 宿舍分配窗口
    kchargeno 課級主管
    bchargeno 部級主管
    cchargeno 廠級主管
    zchargeno 製造處級主管
    zcchargeno 製造總處級主管
    pcchargeno 產品處級主管
    xzchargeno 人資行政主管
    rzbchargeno 人資主管
    workstatus 表單狀態
    kchargename 課級主管
    bchargename 部級主管
    cchargename 廠級主管
    zchargename 製造處級主管
    zcchargename 製造總處級主管
    pcchargename 產品處級主管
    xzchargename 人資行政主管
    rzbchargename 人資主管
    ylname1 人資複職初核窗口
    ylname2 衛生部體檢窗口
    ylname3 體檢單回收及識別證領取
    ylname4 衛生部主管審核窗口
    ylname5 宿舍分配窗口
    dealfactoryid 申請人廠區ID
    makerip 操作ip
    makerfactoryid 填單人廠區id
    complettime 表單完成時間
    element 公寓號
    apartment 單元號
    isqualified 是否合格
    isreceive 資料是否接收
    attachids 附件ID
    privateaffairs 選擇"私事處理完畢"時填寫的具體原因
    ylno6 住宿情況確認
    ylno7 單位助理上傳附件
    ylname6 住宿情況確認
    ylname7 單位助理上傳附件
    ylno8 產品群級主管
    ylno9 CAA最高管制主管
    ylname8 產品群級主管
    ylname9 CAA最高管制主管
    id 主鍵（舊翻新時新增）
    createBy 創建人（舊翻新時新增）
    createDate 創建時間（舊翻新時新增）
    updateBy 更新者（舊翻新時新增）
    updateDate 更新時間（舊翻新時新增）
    delFlag 刪除標識（舊翻新時新增）
    makerdeptno 填單人單位代碼（舊翻新時新增）
		   -->
		<input id="ids" name="ids" type="hidden"
			value="${wffuzhiprocessEntity.id }" /> <input id="serialno"
			name="serialno" type="hidden"
			value="${wffuzhiprocessEntity.serialno }" />
		<div class="commonW">
			<div class="headTitle">員工休假期滿複職申請單</div>
			<div class="position_L">
				任務編碼：<span style="color:#999;"> <c:choose>
						<c:when test="${wffuzhiprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
						<c:otherwise>
                           ${wffuzhiprocessEntity.serialno}
                       </c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_L1 margin_L">
				填單時間：<span style="color:#999;"> <c:choose>
						<c:when test="${wffuzhiprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
						<c:otherwise>
							<input style="border:0px" style="width: 120px" readonly="true"
								value="<fmt:formatDate value='${wffuzhiprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>" />
						</c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_R margin_R">填單人：${wffuzhiprocessEntity.makerno}/${wffuzhiprocessEntity.makername}</div>
			<div class="clear"></div>
			<table class="formList">
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">申請人基本信息</td>
							</tr>
							<tr align="center">
								<td width="10%">工號</td>
								<td width="10%"><input id="dealno" name="dealno"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 80,required:true"
									value="${wffuzhiprocessEntity.dealno}" /></td>
								<td width="10%">姓名</td>
								<td width="10%"><input id="dealname" name="dealname"
									readonly class="easyui-validatebox inputCss"
									data-options="width:60"
									value="${wffuzhiprocessEntity.dealname}" /><input
									id="dealfactoryid" name="dealfactoryid" type="hidden"
									class="easyui-validatebox inputCss"
									data-options="width:50,required:true"
									value="${wffuzhiprocessEntity.dealfactoryid}" /></td>
								<td width="10%">單位代碼</td>
								<td width="10%"><input id="dealdeptno" name="dealdeptno"
									readonly class="easyui-validatebox inputCss"
									data-options="width:150"
									value="${wffuzhiprocessEntity.dealdeptno}" /></td>
								<td width="6%">單位名稱</td>
								<td colspan="3" width="34%"><input id="dealdeptname"
									style="width:320px" name="dealdeptname" readonly
									class="easyui-validatebox inputCss"
									data-options="width:320,required:true"
									value="${wffuzhiprocessEntity.dealdeptname}" /></td>
							</tr>
							<tr align="center">
								<td>身份證號碼</td>
								<td colspan="2"><input id="idcard" name="idcard"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 160,required:true,validType:'idCode[\'idcard\']'"
									value="${wffuzhiprocessEntity.idcard}" /></td>
								<td>現居住地址</td>
								<td colspan="2"><input id="address" name="address" readonly
									class="easyui-validatebox inputCss"
									data-options="width:240,required:true"
									value="${wffuzhiprocessEntity.address}" /></td>
								<td>工作區域</td>
								<td><input id="workarea" name="workarea" readonly
									class="easyui-validatebox inputCss"
									data-options="width: 100,required:true"
									value="${wffuzhiprocessEntity.workarea}" /></td>
								<td>手機號碼</td>
								<td><input id="phoneno" name="phoneno"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 100,required:true,validType:'phone[\'phoneno\',\'手機格式不正確\']'"
									value="${wffuzhiprocessEntity.phoneno}" /></td>
							</tr>
							<tr align="center">
								<td>直屬主管信息</td>
								<td>工號</td>
								<td><input id="chargeno" name="chargeno"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 80,required:true"
									value="${wffuzhiprocessEntity.chargeno}" /></td>
								<td>姓名</td>
								<td><input id="chargename" name="chargename"
									class="easyui-validatebox inputCss"
									data-options="width:80,required:true" readonly
									value="${wffuzhiprocessEntity.chargename}" /></td>
								<td>聯繫方式</td>
								<td colspan="2"><input id="chargephone" name="chargephone"
									readonly class="easyui-validatebox inputCss"
									data-options="width: 150,required:true"
									value="${wffuzhiprocessEntity.chargephone}" /></td>
								<td colspan="2"></td>
							</tr>
							<tr align="center">
								<td>休假信息</td>
								<td>開始日期</td>
								<td><input id="leavebegintime" name="leavebegintime"
									onblur="days()" class="Wdate" disabled
									data-options="width:100,required:true" style="width:100px"
									value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${wffuzhiprocessEntity.leavebegintime}"/>"
									onclick="WdatePicker({maxDate:'#F{$dp.$D(\'leaveendtime\')}',dateFmt:'yyyy-MM-dd'})" /></td>
								<td>結束日期</td>
								<td><input id="leaveendtime" name="leaveendtime"
									class="Wdate" disabled onblur="days()"
									data-options="width:100,required:true" style="width:100px"
									value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${wffuzhiprocessEntity.leaveendtime}"/>"
									onclick="WdatePicker({minDate:'#F{$dp.$D(\'leavebegintime\')}',dateFmt:'yyyy-MM-dd'})" /></td>
								<td>合計天數</td>
								<td><input id="leavedays" name="leavedays" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wffuzhiprocessEntity.leavedays}" /></td>
								<td colspan="3"></td>
							</tr>
							<tr align="center">
								<td>複職原因</td>
								<td colspan="9"><div class="reasonDiv"></div> <input
									id="reason" name="reason" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${wffuzhiprocessEntity.reason}" /> <input
									id="privateaffairs1" name="privateaffairs1" disabled="disabled"
									type="hidden" class="easyui-validatebox"
									data-options="width:150"
									value="${wffuzhiprocessEntity.privateaffairs}" /></td>
							</tr>
							<tr align="center">
								<td>住宿申請</td>
								<td colspan="3"><div class="habitancyDiv"></div> <input
									id="habitancy" name="habitancy" class="easyui-validatebox"
									data-options="width: 150" type="hidden"
									value="${wffuzhiprocessEntity.habitancy}" /> <input
									id="disOrEnabled" value="disabled" type="hidden" /></td>
								<td colspan="2" align="left"><input id="habitancyfacid"
									name="habitancyfacid" class="easyui-combobox" disabled
									data-options="width: 150,required:true"
									value="${wffuzhiprocessEntity.habitancyfacid }" /></td>
								<td>體檢費用（單位:RMB元）</td>
								<td colspan="3" align="left">
									<input id="moneycount" name="moneycount" class="easyui-validatebox" data-options="width: 150"
										   value="${wffuzhiprocessEntity.moneycount}" readonly></input>
								</td>
							</tr>
							<c:if test="${not empty zhusu && '1' eq zhusu }">
                            <tr align="center">
					             <td>宿舍分配窗口</td>
					             <td colspan="10" align="left">請於(${wffuzhiprocessEntity.element })公寓(${wffuzhiprocessEntity.apartment })樓棟物業管理中心辦理入住手續</td>
				            </tr>
				            </c:if>
							<tr align="center">
								<td>附件</td>
								<td colspan="9" class="td_style1">
									<input type="hidden" id="attachids" name="attachids" value="${wffuzhiprocessEntity.attachids }" />
									<div id="dowloadUrl">
										<c:forEach items="${file}" varStatus="i" var="item">
											<div id="${item.id}"
												style="line-height:30px;margin-left:5px;" class="float_L">
												<div class="float_L">
													<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
												</div>
											</div>
										</c:forEach>
									</div></td>
							</tr>
							<tr>
								<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
									<a href="javascript:void(0)"
									onclick="showWfImag('${processId}','員工複職申請單');">點擊查看簽核流程圖</a>
								</th>
							</tr>
							<tr>
								<td colspan="10" style="text-align:left;">
									${chargeNodeInfo}</td>
							</tr>

							<tr>
								<td colspan="10" style="text-align:left;"><iframe
										id="qianheLogFrame" name="qianheLogFrame"
										src="${ctx}/wfcontroller/goChargeLog?serialNo=${wffuzhiprocessEntity.serialno}"
										width="100%"></iframe></td>
							</tr>
							<tr class="no-print">
								<td colspan="10" style="text-align:center;padding-left:10px;">
									<a href="javascript:;" id="btnClose" class="easyui-linkbutton"
									data-options="iconCls:'icon-cancel'" style="width: 100px;"
									onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp; <c:if
										test="${wffuzhiprocessEntity.workstatus!=null&&wffuzhiprocessEntity.workstatus==3}">
										<a href="#" id="btnPrint" class="easyui-linkbutton"
											data-options="iconCls:'icon-print'" style="width: 100px;"
											onclick="printWindow('btnClose,btnPrint');">列印</a>
									</c:if>
								</td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</div>
	</form>
	<div id="dlg"></div>
	<script
		src='${ctx}/static/js/humanCapital/wffuzhiprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>