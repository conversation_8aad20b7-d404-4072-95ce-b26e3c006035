<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>員工複職申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<form id="mainform" action="${ctx}/wffuzhiprocess/${action}"
		method="post">
		<!--
                   element 公寓號
                   apartment 單元號
                   isqualified 是否合格
                   isreceive 資料是否接收
        		   -->
		<input id="ids" name="ids" type="hidden"
			value="${wffuzhiprocessEntity.id }" /> <input id="serialno"
			name="serialno" type="hidden"
			value="${wffuzhiprocessEntity.serialno }" />
		<div class="commonW">
			<div class="headTitle">員工休假期滿複職申請單</div>
			<div class="position_L">
				任務編碼：<span style="color:#999;"> <c:choose>
						<c:when test="${wffuzhiprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
						<c:otherwise>
                           ${wffuzhiprocessEntity.serialno}
                       </c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_L1 margin_L">
				填單時間：<span style="color:#999;"> <c:choose>
						<c:when test="${wffuzhiprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
						<c:otherwise>
							<input style="border:0px" style="width: 120px" readonly="true"
								value="<fmt:formatDate value='${wffuzhiprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>" />
						</c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_R margin_R">填單人：${wffuzhiprocessEntity.makerno}/${wffuzhiprocessEntity.makername}</div>
			<div class="clear"></div>
			<table class="formList">
				<tr align="center">
					<td colspan="10" class="td_style1">申請人基本信息</td>
				</tr>
				<tr align="center">
					<td width="8%">工號</td>
					<td><input id="dealno" name="dealno"
						class="easyui-validatebox inputCss" readonly
						data-options="width: 80,required:true"
						value="${wffuzhiprocessEntity.dealno}" /></td>
					<td>姓名</td>
					<td width="8%"><input id="dealname" name="dealname" readonly
						class="easyui-validatebox inputCss" data-options="width:60"
						value="${wffuzhiprocessEntity.dealname}" /><input
						id="dealfactoryid" name="dealfactoryid" type="hidden"
						class="easyui-validatebox inputCss"
						data-options="width:50,required:true"
						value="${wffuzhiprocessEntity.dealfactoryid}" /></td>
					<td>單位代碼</td>
					<td><input id="dealdeptno" name="dealdeptno"
						readonly class="easyui-validatebox inputCss"
						data-options="width:130"
						value="${wffuzhiprocessEntity.dealdeptno}" /></td>
					<td width="8%">單位名稱</td>
					<td colspan="3"><input id="dealdeptname"
						style="width:320px" name="dealdeptname" readonly
						class="easyui-validatebox inputCss"
						data-options="width:320,required:true"
						value="${wffuzhiprocessEntity.dealdeptname}" /></td>
				</tr>
				<tr align="center">
					<td>身份證號碼</td>
					<td colspan="2"><input id="idcard" name="idcard"
						class="easyui-validatebox inputCss" readonly
						data-options="width:160,required:true,validType:'idCode[\'idcard\']'"
						value="${wffuzhiprocessEntity.idcard}" /></td>
					<td>現居住地址</td>
					<td colspan="2"><input id="address" name="address" readonly
						class="easyui-validatebox inputCss"
						data-options="width: 240,required:true"
						value="${wffuzhiprocessEntity.address}" /></td>
					<td>工作區域</td>
					<td><input id="workarea" name="workarea" readonly
						class="easyui-validatebox inputCss"
						data-options="width: 80,required:true"
						value="${wffuzhiprocessEntity.workarea}" /></td>
					<td>手機號碼</td>
					<td><input id="phoneno" name="phoneno"
						class="easyui-validatebox inputCss" readonly
						data-options="width: 90,required:true,validType:'phone[\'phoneno\',\'手機格式不正確\']'"
						value="${wffuzhiprocessEntity.phoneno}" /></td>
				</tr>
				<tr align="center">
					<td>直屬主管信息</td>
					<td>工號</td>
					<td><input id="chargeno" name="chargeno"
						class="easyui-validatebox inputCss" readonly
						data-options="width: 80,required:true"
						value="${wffuzhiprocessEntity.chargeno}" /></td>
					<td>姓名</td>
					<td><input id="chargename" name="chargename"
						class="easyui-validatebox inputCss"
						data-options="width:80,required:true" readonly
						value="${wffuzhiprocessEntity.chargename}" /></td>
					<td>聯繫方式</td>
					<td colspan="2"><input id="chargephone" name="chargephone"
						readonly class="easyui-validatebox inputCss"
						data-options="width: 130,required:true"
						value="${wffuzhiprocessEntity.chargephone}" /></td>
					<td colspan="2"></td>
				</tr>
				<tr align="center">
					<td>休假信息</td>
					<td>開始日期</td>
					<td><input id="leavebegintime" name="leavebegintime"
						onblur="days()" class="Wdate" disabled
						data-options="width:100,required:true" style="width:100px"
						value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${wffuzhiprocessEntity.leavebegintime}"/>"
						onclick="WdatePicker({maxDate:'#F{$dp.$D(\'leaveendtime\')}',dateFmt:'yyyy-MM-dd'})" /></td>
					<td>結束日期</td>
					<td><input id="leaveendtime" name="leaveendtime" class="Wdate"
						disabled onblur="days()" data-options="width:100,required:true"
						style="width:100px"
						value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${wffuzhiprocessEntity.leaveendtime}"/>"
						onclick="WdatePicker({minDate:'#F{$dp.$D(\'leavebegintime\')}',dateFmt:'yyyy-MM-dd'})" /></td>
					<td>合計天數</td>
					<td><input id="leavedays" name="leavedays" readonly
						class="easyui-validatebox inputCss" data-options="width: 150"
						value="${wffuzhiprocessEntity.leavedays}" /></td>
					<td colspan="3"></td>
				</tr>
				<tr align="center">
					<td>複職原因</td>
					<td colspan="9"><div class="reasonDiv"></div> <input
						id="reason" name="reason" class="easyui-validatebox"
						data-options="width: 150" type="hidden"
						value="${wffuzhiprocessEntity.reason}" /> <input
						id="privateaffairs1" name="privateaffairs1" disabled="disabled"
						type="hidden" class="easyui-validatebox" data-options="width:150"
						value="${wffuzhiprocessEntity.privateaffairs}" /></td>
				</tr>
				<tr align="center">
					<td>住宿申請</td>
					<td colspan="3"><div class="habitancyDiv"></div>
						<input id="habitancy" name="habitancy" class="easyui-validatebox"
						data-options="width: 150" type="hidden"
						value="${wffuzhiprocessEntity.habitancy}" /> <input
						id="disOrEnabled" value="disabled" type="hidden" /></td>
					<td colspan="2" align="left"><input id="habitancyfacid"
						name="habitancyfacid" class="easyui-combobox" disabled
						data-options="width: 150,required:true"
						value="${wffuzhiprocessEntity.habitancyfacid }" /></td>
					<td>體檢費用（單位:RMB元）</td>
					<td colspan="3" align="left">
						<input id="moneycount" name="moneycount" class="easyui-validatebox" data-options="width: 150"
							   value="${wffuzhiprocessEntity.moneycount}" readonly></input>
					</td>
				</tr>
				<c:if test="${not empty nodeName &&'宿舍分配窗口' eq nodeName}">
                <tr align="center">
					<td>宿舍分配窗口</td>
					<td colspan="10" align="left">請於<input id="element"
						name="element" class="easyui-validatebox"
						data-options="width: 150"
						value="${wffuzhiprocessEntity.element }" />公寓<input id="apartment"
						name="apartment" class="easyui-validatebox"
						data-options="width: 150"
						value="${wffuzhiprocessEntity.apartment }" />樓棟物業管理中心辦理入住手續</td>
				</tr>
				</c:if>
				<c:if test="${not empty nodeOrder && nodeOrder>31}">
                <tr align="center">
					<td>宿舍分配窗口</td>
					<td colspan="10" align="left">請於(${wffuzhiprocessEntity.element })公寓(${wffuzhiprocessEntity.apartment })樓棟物業管理中心辦理入住手續</td>
				</tr>
				</c:if>
				<c:choose>
				<c:when test="${not empty factoryType && 'IPEJY' eq factoryType && not empty nodeName &&'單位助理上傳附件' eq nodeName}">
				<tr align="center">
					<td>附件<font color="red">*</font></td>
					<td colspan="9" class="td_style1">
						<span class="sl-custom-file">
							<input type="button" value="点击上传文件" class="btn-file" />
							<input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file" />
						</span>
						<input type="hidden" id="attachids" name="attachids" value="${wffuzhiprocessEntity.attachids }" />
						<div id="dowloadUrl">
							<c:forEach items="${file}" varStatus="i" var="item">
								<div id="${item.id}" style="line-height:30px;margin-left:5px;"
									class="float_L">
									<div class="float_L">
										<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
									</div>
									<div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
								</div>
							</c:forEach>
						</div>
					</td>
				</tr>
				</c:when>
				<c:otherwise>
				<tr align="center">
					<td>附件</td>
					<td colspan="9" class="td_style1">
						<input type="hidden"  name="attachids" value="${wffuzhiprocessEntity.attachids }" />
						<div>
							<c:forEach items="${file}" varStatus="i" var="item">
								<div id="${item.id}" style="line-height:30px;margin-left:5px;"
									class="float_L">
									<div class="float_L">
										<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
									</div>
								</div>
							</c:forEach>
						</div></td>
				</tr>
				</c:otherwise>
				</c:choose>
<%-- 				<tr align="center">
					<td>附件</td>
					<td colspan="9" class="td_style1"><input type="hidden"
						id="attachids" name="attachids"
						value="${wffuzhiprocessEntity.attachids }" />
						<div id="dowloadUrl">
							<c:forEach items="${file}" varStatus="i" var="item">
								<div id="${item.id}" style="line-height:30px;margin-left:5px;"
									class="float_L">
									<div class="float_L">
										<a href="${ctx}/newEsign/download/${item.id}">${item.name}</a>
									</div>
								</div>
							</c:forEach>
						</div></td>
				</tr> --%>
				<c:choose>
					<c:when test="${not empty nodeName &&'衛生部主管審核窗口' eq nodeName}">
						<tr align="center">
							<td>體檢結果及複職建議</td>
							<td colspan="9"><textarea id="attachidsremark"
									name="attachidsremark" class="easyui-validatebox"
									style="width:1000px;height:60px;" rows="4" cols="4"></textarea>
								<input id="isqualified" name="isqualified" type="hidden" /></td>
						</tr>
					</c:when>
					<c:otherwise>
						<tr align="center">
							<td>批註</td>
							<td colspan="9"><textarea id="attachidsremark"
									name="attachidsremark" class="easyui-validatebox"
									style="width:1000px;height:60px;" rows="4" cols="4"></textarea>
							</td>
						</tr>
					</c:otherwise>
				</c:choose>
				<tr align="center">
					<td colspan="10"
						style="border:none;text-align:center;margin-top:10px"><fox:action
							cssClass="easyui-linkbutton" cssStyle="width: 100px;" perCall="audiPrValid(${nodeOrder})"
							serialNo="${wffuzhiprocessEntity.serialno}"></fox:action></td>
				</tr>
				<tr>
					<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
						<a href="javascript:void(0)"
						onclick="showWfImag('${processId}','員工複職申請單');">點擊查看簽核流程圖</a>
					</th>
				</tr>
				<tr>
					<td colspan="10" style="text-align:left;">${chargeNodeInfo}</td>
				</tr>

				<tr>
					<td colspan="10" style="text-align:left;"><iframe
							id="qianheLogFrame" name="qianheLogFrame"
							src="${ctx}/wfcontroller/goChargeLog?serialNo=${wffuzhiprocessEntity.serialno}"
							width="100%"></iframe></td>
				</tr>
			</table>
		</div>
	</form>
	<div id="dlg"></div>
	<script
		src='${ctx}/static/js/humanCapital/wffuzhiprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>