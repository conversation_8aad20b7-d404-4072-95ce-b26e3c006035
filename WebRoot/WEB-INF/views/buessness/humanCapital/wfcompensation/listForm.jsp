<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>經濟補償申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
            type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfcompensation/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfcompensationEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfcompensationEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfcompensationEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfcompensationEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfcompensationEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${wfcompensationEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">經濟補償申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfcompensationEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfcompensationEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfcompensationEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfcompensationEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfcompensationEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfcompensationEntity.makerno}">
            <div class="position_R margin_R">填單人：${wfcompensationEntity.makerno}/${wfcompensationEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="9%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="dealno" name="dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfcompensationEntity.dealno}"
                                       onchange="queryUserInfo();"/>
                            </td>
                            <td width="9%">申請人</td>
                            <td width="10%" class="td_style1">
                                <input id="dealname" name="dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wfcompensationEntity.dealname }"/>
                            </td>
                            <td width="8%">性別</td>
                            <td width="8%" class="td_style1">
                                <input id="dealsex" name="dealsex"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfcompensationEntity.dealsex }"/>
                            </td>
                            <td width="8%">資位</td>
                            <td width="12%" class="td_style1">
                                <input id="applyleveltype" name="applyleveltype"
                                       class="easyui-validatebox inputCss" style="width:90px;"
                                       readonly value="${wfcompensationEntity.applyleveltype }"/>
                            </td>
                            <td width="8%">入集團日期&nbsp;<font color="red">*</font></td>
                            <td width="18%" class="td_style1">
                               <input id="ingroup" name="ingroup"
                                      class="easyui-validatebox inputCss" style="width:90px;"
                                      readonly value="${wfcompensationEntity.ingroup }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位代碼</td>
                            <td class="td_style1">
                                <input id="dealdeptno" name="dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfcompensationEntity.dealdeptno }" readonly/>
                            </td>
                            <td>部門名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="dealdeptname" name="dealdeptname"
                                       class="easyui-validatebox" data-options="width:400,required:true"
                                       value="${wfcompensationEntity.dealdeptname }"/>
                                <input id="dealfactoryid" name="dealfactoryid" type="hidden"
                                       value="${wfcompensationEntity.dealfactoryid}"/>
                            </td>
                            <td>法人&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="corporateid" name="corporateid"
                                       class="easyui-combobox" data-options="width: 235,validType:'comboxValidate[\'corporateid\',\'请選擇法人\']'"
                                       panelHeight="200" editable="false"
                                       value="${wfcompensationEntity.corporateid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>身份證號碼&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealcard" name="dealcard"
                                       class="easyui-validatebox" data-options="width:150,required:true,validType:'idCode[\'dealcard\']'"
                                       value="${wfcompensationEntity.dealcard }"/>
                            </td>
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealtel" name="dealtel" onblur="validMobilephone('dealtel')"
                                       class="easyui-validatebox" data-options="width:100,required:true"
                                       value="${wfcompensationEntity.dealtel }"/>
                            </td>
                            <td>上上年度績效&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealexjixiao" name="dealexjixiao" class="easyui-combobox"
                                       data-options="width: 70,validType:'comboxValidate[\'dealexjixiao\',\'请選擇上上年度績效\']',
                                       panelHeight:'auto',
									   valueField:'value',
									   textField:'label',
									   editable:false,onBeforeLoad:function(){
									   loadJixiao();}"
                                       value="${wfcompensationEntity.dealexjixiao }" panelHeight="auto"/>
                            </td>
                            <td>上年度績效&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealjixiao" name="dealjixiao" class="easyui-combobox"
                                       data-options="width: 70,validType:'comboxValidate[\'dealjixiao\',\'请選擇上年度績效\']',
                                       panelHeight:'auto',
									   valueField:'value',
									   textField:'label',
									   editable:false,onBeforeLoad:function(){
									   loadJixiao();}"
                                       value="${wfcompensationEntity.dealjixiao }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>醫療期開始日期&nbsp;<font color="red">*</font></td>
                            <td class="td_style1"><input id="curestartdate" name="curestartdate" class="easyui-validatebox Wdate"
                                       data-options="width:100,required:true" style="width:100px"
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd"
									   value="${wfcompensationEntity.curestartdate}"/>"
                                       onclick="WdatePicker({maxDate:'#F{$dp.$D(\'cureenddate\')}',dateFmt:'yyyy-MM-dd'})" /></td>
                            <td>醫療期結束日期&nbsp;<font color="red">*</font></td>
                            <td class="td_style1"><input id="cureenddate" name="cureenddate" class="easyui-validatebox Wdate"
                                       data-options="width:100,required:true"
                                       style="width:100px"
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd"
									   value="${wfcompensationEntity.cureenddate}"/>"
                                       onclick="WdatePicker({minDate:'#F{$dp.$D(\'curestartdate\')}',dateFmt:'yyyy-MM-dd'})" /></td>
                            <td>復檢時間&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="curedate" name="curedate" class="easyui-validatebox Wdate"
                                       data-options="width:100,required:true"
                                       style="width:100px" value="<fmt:formatDate  pattern="yyyy-MM-dd"
									   value="${wfcompensationEntity.curedate}"/>"
                                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" />
                            </td>
                            <td>復檢醫院&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="curehospital" name="curehospital"
                                       style="width:200px;position:relative;" onblur="jiequ('treatreason','50')"
                                       class="easyui-validatebox" data-options="width:430,required:true,prompt:'最多輸入50個字，多餘字符自動截取'"
                                       value="${wfcompensationEntity.curehospital }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">一、病情敘述（詳細見附件）</td>
                        </tr>
                        <tr>
                            <td colspan="10" align="left">
                                <input class="easyui-validatebox Wdate"
                                       data-options="width:100,required:true"
                                       style="width:100px"
                                       value="<fmt:formatDate  pattern="yyyy年MM月"
									   value="${wfcompensationEntity.treatyearmonth}"/>"
                                       onclick="WdatePicker({dateFmt:'yyyy年MM月',vel:'treatyearmonth'})" />
                                <input id="treatyearmonth" name="treatyearmonth"  value="${wfcompensationEntity.treatyearmonth}" type="hidden"/>該員因
                                <input id="treatreason" name="treatreason" onblur="jiequ('treatreason','30')"
                                       style="width:200px;position:relative;"
                                       class="easyui-validatebox" data-options="width:300,required:true,prompt:'最多輸入30個字，多餘字符自動截取'"
                                       value="${wfcompensationEntity.treatreason }"/>
                                需休假治療。</td>
                        </tr>
                        <tr>
                            <td colspan="10" align="left">
                                <input id="start" data-options="width:115,required:true" style="width:115px"
                                       class="easyui-validatebox Wdate"
                                       value="<fmt:formatDate  pattern="yyyy年MM月dd日"
									   value="${wfcompensationEntity.treatstartdate}"/>"
                                       onclick="WdatePicker({maxDate:'#F{$dp.$D(\'end\')}',dateFmt:'yyyy年MM月dd日',vel:'treatstartdate'})" /><input id="treatstartdate" name="treatstartdate" value="${wfcompensationEntity.treatstartdate}" type="hidden"/> -
                                <input id="end" data-options="width:115,required:true" style="width:115px"
                                       class="easyui-validatebox Wdate"
                                       value="<fmt:formatDate  pattern="yyyy年MM月dd日"
									   value="${wfcompensationEntity.treatenddate}"/>"
                                       onclick="WdatePicker({minDate:'#F{$dp.$D(\'start\')}',dateFmt:'yyyy年MM月dd日',vel:'treatenddate'})" /><input id="treatenddate" name="treatenddate" value="${wfcompensationEntity.treatenddate}" type="hidden"/>該員累計休滿
                                <input id="treattotalmonth" name="treattotalmonth" onblur="checkZheng('treattotalmonth')"
                                       class="easyui-validatebox" data-options="width: 80,required:true"
                                       value="${wfcompensationEntity.treattotalmonth }"/>個月醫療期後，仍不能康復。</td>
                        </tr>
                        <tr>
                            <td colspan="10" align="left">
                                <input data-options="width:115,required:true"
                                       style="width:100px"  class="easyui-validatebox Wdate"
                                       value="<fmt:formatDate  pattern="yyyy年MM月dd日"
									   value="${wfcompensationEntity.nottime}"/>"
                                       onclick="WdatePicker({dateFmt:'yyyy年MM月dd日',vel:'nottime'})" />
                                <input id="nottime" name="nottime" value="${wfcompensationEntity.nottime}" type="hidden"/>，
                                <input id="hospitalname" name="hospitalname"
                                       style="width:300px;position:relative;" onblur="jiequ('hospitalname','30')"
                                       class="easyui-validatebox" data-options="width:300,required:true,prompt:'最多輸入30個字，多餘字符自動截取'"
                                       value="${wfcompensationEntity.hospitalname }"/>，該員疾病未愈，不能複職工作，公司依法解除勞動合同。</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">二、員工醫療期相關法律依據</td>
                        </tr>
                        <tr>
                            <td colspan="10">《企業職工患病或非因工負傷醫療期規》<br/>
                                第三條&nbsp;&nbsp;&nbsp;&nbsp;企業職工因患病或非因工負傷，需要停止工作醫療時，根據本人實際參加工作年限和在本單位工作年限，給予3個月到24個月的醫療期：<br/>
                                (一)實際工作年限十年以下的，在本單位工作年限五年以下的為3個月；五年以上的為6個月。<br/>
                                (二)實際工作年限十年以上的，在本單位工作年限五年以下的為6個月，五年以上十年以下的為9個月；十年以上十五年以下為12個月；十五年以上二十年以下的為18個月；二十年以上的為24個月。<br/>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">三、解除劳动合同及经济补偿的法律依据</td>
                        </tr>
                        <tr>
                            <td colspan="10">
                                《中華人民共和國勞動合同法》<br/>
                                第四十條&nbsp;&nbsp;&nbsp;&nbsp;有下列情形之一的，用人單位提前三十日以書面形式通知勞動者本人或者額外支付勞動者一個月工資後，可以解除勞動合同。<br/>
                                (一)勞動者患病或者非因工負傷，在規定的醫療期滿後不能從事原工作也不能從事由用人單位另行安排的工作的。<br/>
                                第四十六條&nbsp;&nbsp;&nbsp;&nbsp;有下列情形之一的，用人單位應當向勞動者支付經濟補償：（三）用人單位依照本法第四十條規定解除勞動合同的；<br/>
                                第四十七條&nbsp;&nbsp;&nbsp;&nbsp;經濟補償按勞動者在本單位工作的年限，每滿一年支付一個月工資的標準向勞動者支付。六個月以上不滿一年的，按一年計算；不滿六個月的，向勞動者支付半個月工資的經濟補償。<br/>
                                勞動者月工資高於用人單位所在直轄市、設區的市級人民政府公佈的本地區上年度職工月平均工資三倍的，向其支付經濟補償的標準按職工月平均工資三倍的數額支付，向其支付經濟補償的年限最高不超過十二年。<br/>
                                本條所稱月工資是指勞動者在勞動合同解除或者終止前十二個月的平均工資。
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">四、公司需支付之費用說明</td>
                        </tr>
                        <tr>
                            <td colspan="10">(1)該員
                                <input data-options="width:100,required:true"
                                       style="width:100px" class="easyui-validatebox Wdate"
                                       value="<fmt:formatDate  pattern="yyyy年MM月"
									   value="${wfcompensationEntity.noticeaveyear}"/>"
                                       onclick="WdatePicker({dateFmt:'yyyy年MM月',vel:'noticeaveyear'})" />
                                <input id="noticeaveyear" name="noticeaveyear" value="${wfcompensationEntity.noticeaveyear}" type="hidden"/>份之前12個月的平均工資為￥
                                <input id="noticeavesalary" name="noticeavesalary" onblur="checkMum2('noticeavesalary')"
                                       class="easyui-validatebox" data-options="width: 80,required:true"
                                       value="${wfcompensationEntity.noticeavesalary }"/>元／月。</td>
                        </tr>
                        <tr>
                            <td colspan="10">
                                (2)根據《中華人民共和國勞動合同法》第四十條規定，應支付該員1個月工資代通知金￥
                                <input id="noticemoney" name="noticemoney" onblur="checkMum2('noticemoney')"
                                       class="easyui-validatebox" data-options="width: 80,required:true"
                                       value="${wfcompensationEntity.noticemoney }"/>元。</td>
                        </tr>
                        <tr>
                            <td colspan="10">
                                (3)根據《中華人民共和國勞動合同法》第四十六條和第四十七條規定，該員年資為
                                <input id="payempyear" name="payempyear"
                                       class="easyui-validatebox inputCss" data-options="width:40,required:true"
                                       value="${wfcompensationEntity.payempyear }" readonly/>年，应支付相当于该员
                                <input id="paymonth" name="paymonth" onblur="checkMum1('paymonth')"
                                       class="easyui-validatebox" data-options="width: 80,required:true"
                                       value="${wfcompensationEntity.paymonth }"/>个月平均工资的经济补偿金￥
                                <input id="paymoney" name="paymoney" onblur="checkMum2('paymoney')"
                                       class="easyui-validatebox" data-options="width: 80,required:true"
                                       value="${wfcompensationEntity.paymoney }"/>元。</td>
                        </tr>
                        <tr>
                            <td colspan="10">
                                (4)合計人民幣￥
                                <input id="totalnum" name="totalnum" onblur="moneyToUppercase()"
                                       class="easyui-validatebox" data-options="width:150,required:true"
                                       value="${wfcompensationEntity.totalnum }"/>元（大寫：
                                <input id="totalchinese" name="totalchinese"
                                       class="easyui-validatebox" data-options="width:300,required:true"
                                       value="${wfcompensationEntity.totalchinese }"/>）。
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件<font color="red">*</font></td>
                            <td width="90%" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="attachids" value="${wfcompensationEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_jingjibuchangshenqingdan','經濟補償申請單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <c:if test="${empty wfcompensationEntity.makerdeptno}">
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole('${userMoreInfo.deptno}','kchargeno','kchargename','${userMoreInfo.factoryid}')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wfcompensationEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfcompensationEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole('${userMoreInfo.deptno}','bchargeno','bchargename','${userMoreInfo.factoryid}')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfcompensationEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfcompensationEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole('${userMoreInfo.deptno}','cchargeno','cchargename','${userMoreInfo.factoryid}')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfcompensationEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfcompensationEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="hchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">會簽主管</td>
                                                                <td style="border: none;">
                                                                    <a href="#" onclick="addRowHQ('hchargeTable')">添加一位</a>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hchargeno" name="hchargeno"
                                                               class="easyui-validatebox" onblur="getNameByNo(this);"
                                                               data-options="width:80,required:${requiredMap['hchargeno']}"
                                                               value="${wfcompensationEntity.hchargeno }"/><c:if
                                                            test="${requiredMap['hchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hchargename" name="hchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hchargeno']}"
                                                                value="${wfcompensationEntity.hchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable','${userMoreInfo.deptno}','zchargeno','zchargename','${userMoreInfo.factoryid}',null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfcompensationEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfcompensationEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable','${userMoreInfo.deptno}','zcchargeno','zcchargename','${userMoreInfo.factoryid}',null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wfcompensationEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfcompensationEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產品處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable','${userMoreInfo.deptno}','pcchargeno','pcchargename','${userMoreInfo.factoryid}',null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${wfcompensationEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wfcompensationEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="pqchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產品群級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(28,'pqchargeTable','pqchargeno','pqchargename','${userMoreInfo.factoryid}',null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pqchargeno" name="pqchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pqchargeno']}"
                                                               readonly
                                                               value="${wfcompensationEntity.pqchargeno }"/><c:if
                                                            test="${requiredMap['pqchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pqchargename" name="pqchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pqchargeno']}"
                                                                value="${wfcompensationEntity.pqchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        </c:if>
                        <c:if test="${not empty wfcompensationEntity.makerdeptno}">
                            <tr>
                                <td colspan="10" style="text-align:left;">
                                    <table class="flowList"
                                           style="margin-left:5px;margin-top:5px;width:99%">
                                        <tr>
                                            <td style="border:none">
                                                <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                    <tr>
                                                        <td>
                                                            <table width="100%">
                                                                <tr>
                                                                    <td style="border: none;text-align: right;">課級主管</td>
                                                                    <td style="border: none;">
                                                                        <div class="float_L qhUserIcon"
                                                                             onclick="selectRole($('#makerdeptno').val(),'kchargeno','kchargename',$('#makerfactoryid').val())"></div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><input id="kchargeno" name="kchargeno"
                                                                   class="easyui-validatebox"
                                                                   data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                   readonly
                                                                   value="${wfcompensationEntity.kchargeno }"/><c:if
                                                                test="${requiredMap['kchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                            /<input id="kchargename" name="kchargename"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                    value="${wfcompensationEntity.kchargename }"/>
                                                        </td>
                                                    </tr>
                                                </table>

                                                <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                    <tr>
                                                        <td>
                                                            <table width="100%">
                                                                <tr>
                                                                    <td style="border: none;text-align: right;">部級主管</td>
                                                                    <td style="border: none;">
                                                                        <div class="float_L qhUserIcon"
                                                                             onclick="selectRole($('#makerdeptno').val(),'bchargeno','bchargename',$('#makerfactoryid').val())"></div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><input id="bchargeno" name="bchargeno"
                                                                   class="easyui-validatebox"
                                                                   data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                   readonly
                                                                   value="${wfcompensationEntity.bchargeno }"/><c:if
                                                                test="${requiredMap['bchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                            /<input id="bchargename" name="bchargename"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                    value="${wfcompensationEntity.bchargename }"/>
                                                        </td>
                                                    </tr>
                                                </table>

                                                <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                    <tr>
                                                        <td>
                                                            <table width="100%">
                                                                <tr>
                                                                    <td style="border: none;text-align: right;">廠級主管</td>
                                                                    <td style="border: none;">
                                                                        <div class="float_L qhUserIcon"
                                                                             onclick="selectRole($('#makerdeptno').val(),'cchargeno','cchargename',$('#makerfactoryid').val())"></div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><input id="cchargeno" name="cchargeno"
                                                                   class="easyui-validatebox"
                                                                   data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                   readonly
                                                                   value="${wfcompensationEntity.cchargeno }"/><c:if
                                                                test="${requiredMap['cchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                            /<input id="cchargename" name="cchargename"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                    value="${wfcompensationEntity.cchargename }"/>
                                                        </td>
                                                    </tr>
                                                </table>

                                                <table width="18%" style="float: left;margin-left: 5px;" id="hchargeTable">
                                                    <tr>
                                                        <td>
                                                            <table width="100%">
                                                                <tr>
                                                                    <td style="border: none;text-align: right;">會簽主管</td>
                                                                    <td style="border: none;">
                                                                        <a href="#" onclick="addRowHQ('hchargeTable')">添加一位</a>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><input id="hchargeno" name="hchargeno"
                                                                   class="easyui-validatebox" onblur="getNameByNo(this);"
                                                                   data-options="width:80,required:${requiredMap['hchargeno']}"
                                                                   value="${wfcompensationEntity.hchargeno }"/><c:if
                                                                test="${requiredMap['hchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                            /<input id="hchargename" name="hchargename"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['hchargeno']}"
                                                                    value="${wfcompensationEntity.hchargename }"/>
                                                        </td>
                                                    </tr>
                                                </table>

                                                <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                    <tr>
                                                        <td>
                                                            <table width="100%">
                                                                <tr>
                                                                    <td style="border: none;text-align: right;">製造處級主管</td>
                                                                    <td style="border: none;">
                                                                        <div class="float_L qhUserIcon"
                                                                             onclick="selectRole3('zchargeTable',$('#makerdeptno').val(),'zchargeno','zchargename',$('#makerfactoryid').val(),null)"></div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><input id="zchargeno" name="zchargeno"
                                                                   class="easyui-validatebox"
                                                                   data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                   readonly
                                                                   value="${wfcompensationEntity.zchargeno }"/><c:if
                                                                test="${requiredMap['zchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                            /<input id="zchargename" name="zchargename"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                    value="${wfcompensationEntity.zchargename }"/>
                                                        </td>
                                                    </tr>
                                                </table>

                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="border:none">
                                                <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                    <tr>
                                                        <td>
                                                            <table width="100%">
                                                                <tr>
                                                                    <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                    <td style="border: none;">
                                                                        <div class="float_L qhUserIcon"
                                                                             onclick="selectRole3('zcchargeTable',$('#makerdeptno').val(),'zcchargeno','zcchargename',$('#makerfactoryid').val(),null)"></div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><input id="zcchargeno" name="zcchargeno"
                                                                   class="easyui-validatebox"
                                                                   data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                   readonly
                                                                   value="${wfcompensationEntity.zcchargeno }"/><c:if
                                                                test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                            /<input id="zcchargename" name="zcchargename"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                    value="${wfcompensationEntity.zcchargename }"/>
                                                        </td>
                                                    </tr>
                                                </table>

                                                <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                    <tr>
                                                        <td>
                                                            <table width="100%">
                                                                <tr>
                                                                    <td style="border: none;text-align: right;">產品處級主管</td>
                                                                    <td style="border: none;">
                                                                        <div class="float_L qhUserIcon"
                                                                             onclick="selectRole3('pcchargeTable',$('#makerdeptno').val(),'pcchargeno','pcchargename',$('#makerfactoryid').val(),null)"></div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><input id="pcchargeno" name="pcchargeno"
                                                                   class="easyui-validatebox"
                                                                   data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                   readonly
                                                                   value="${wfcompensationEntity.pcchargeno }"/><c:if
                                                                test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                            /<input id="pcchargename" name="pcchargename"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                    value="${wfcompensationEntity.pcchargename }"/>
                                                        </td>
                                                    </tr>
                                                </table>

                                                <table width="18%" style="float: left;margin-left: 5px;" id="pqchargeTable">
                                                    <tr>
                                                        <td>
                                                            <table width="100%">
                                                                <tr>
                                                                    <td style="border: none;text-align: right;">產品群級主管</td>
                                                                    <td style="border: none;">
                                                                        <div class="float_L qhUserIcon"
                                                                             onclick="selectRole2(28,'pqchargeTable','pqchargeno','pqchargename',$('#makerfactoryid').val(),null)"></div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><input id="pqchargeno" name="pqchargeno"
                                                                   class="easyui-validatebox"
                                                                   data-options="width:80,required:${requiredMap['pqchargeno']}"
                                                                   readonly
                                                                   value="${wfcompensationEntity.pqchargeno }"/><c:if
                                                                test="${requiredMap['pqchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                            /<input id="pqchargename" name="pqchargename"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['pqchargeno']}"
                                                                    value="${wfcompensationEntity.pqchargename }"/>
                                                        </td>
                                                    </tr>
                                                </table>

                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </c:if>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
</div>
<script src='${ctx}/static/js/humanCapital/wfcompensation.js?random=<%= Math.random()%>'></script>
</body>
</html>