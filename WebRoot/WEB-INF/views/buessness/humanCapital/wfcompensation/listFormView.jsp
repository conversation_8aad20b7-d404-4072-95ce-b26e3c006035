<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>經濟補償申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/wfcompensation/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfcompensationEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfcompensationEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">經濟補償申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfcompensationEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfcompensationEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfcompensationEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfcompensationEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfcompensationEntity.makerno}/${wfcompensationEntity.makername}</div>
        <div class="clear"></div>
        <table  class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">申請人工號</td>
                            <td width="10%" align="left">
                                ${wfcompensationEntity.dealno}
                            </td>
                            <td width="8%">申請人</td>
                            <td width="10%" align="left">
                                ${wfcompensationEntity.dealname}
                            </td>
                            <td width="8%">性別</td>
                            <td width="8%" align="left">
                                ${wfcompensationEntity.dealsex}
                            </td>
                            <td width="8%">資位</td>
                            <td width="12%" align="left">
                                ${wfcompensationEntity.applyleveltype}
                            </td>
                            <td width="8%">入集團日期</td>
                            <td width="20%" align="left">
                                ${wfcompensationEntity.ingroup}
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位代碼</td>
                            <td align="left">
                                ${wfcompensationEntity.dealdeptno}
                            </td>
                            <td>部門名稱</td>
                            <td colspan="5" align="left">
                                ${wfcompensationEntity.dealdeptname}
                                <input id="dealfactoryid" name="dealfactoryid" type="hidden"
                                       value="${wfcompensationEntity.dealfactoryid}"/>
                            </td>
                            <td>法人</td>
                            <td class="td_style1">
                                <input id="corporateid" name="corporateid"
                                       class="easyui-combobox" data-options="width: 250"
                                       panelHeight="auto" editable="false" disabled
                                       value="${wfcompensationEntity.corporateid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>身份證號碼</td>
                            <td colspan="3" align="left">
                                ${wfcompensationEntity.dealcard}
                            </td>
                            <td>聯繫方式</td>
                            <td align="left">
                                ${wfcompensationEntity.dealtel}
                            </td>
                            <td>上上年度績效</td>
                            <td class="td_style1">
                                <input id="dealexjixiao" name="dealexjixiao" class="easyui-combobox" disabled
                                       data-options="width: 70,validType:'comboxValidate[\'dealexjixiao\',\'请選擇上上年度績效\']',
                                       panelHeight:'auto',
									   valueField:'value',
									   textField:'label',
									   editable:false,onBeforeLoad:function(){
									   loadJixiao();}"
                                       value="${wfcompensationEntity.dealexjixiao }" panelHeight="auto"/>
                            </td>
                            <td>上年度績效</td>
                            <td class="td_style1">
                                <input id="dealjixiao" name="dealjixiao" class="easyui-combobox" disabled
                                       data-options="width: 70,validType:'comboxValidate[\'dealjixiao\',\'请選擇上年度績效\']',
                                       panelHeight:'auto',
									   valueField:'value',
									   textField:'label',
									   editable:false,onBeforeLoad:function(){
									   loadJixiao();}"
                                       value="${wfcompensationEntity.dealjixiao }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>醫療期開始日期</td>
                            <td class="td_style1"><input id="curestartdate" name="curestartdate"
                                                         class="easyui-validatebox inputCss" readonly
                                                         data-options="width:100" style="width:100px"
                                                         value="<fmt:formatDate  pattern="yyyy-MM-dd"
									   value="${wfcompensationEntity.curestartdate}"/>"/>
                            </td>
                            <td>醫療期結束日期</td>
                            <td class="td_style1"><input id="cureenddate" name="cureenddate"
                                                         class="easyui-validatebox inputCss" readonly
                                                         data-options="width:100"
                                                         style="width:100px"
                                                         value="<fmt:formatDate  pattern="yyyy-MM-dd"
									   value="${wfcompensationEntity.cureenddate}"/>"/>
                            </td>
                            <td>復檢時間</td>
                            <td class="td_style1">
                                <input id="curedate" name="curedate" class="easyui-validatebox inputCss"
                                       data-options="width:90" readonly
                                       style="width:90px" value="<fmt:formatDate  pattern="yyyy-MM-dd"
									   value="${wfcompensationEntity.curedate}"/>"/>
                            </td>
                            <td>復檢醫院</td>
                            <td colspan="3" align="left">
                                ${wfcompensationEntity.curehospital}
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">一、病情敘述（詳細見附件）</td>
                        </tr>
                        <tr>
                            <td colspan="10" align="left">
                                <input class="easyui-validatebox inputCss"
                                       data-options="width:70"
                                       style="width:70px" readonly
                                       value="<fmt:formatDate  pattern="yyyy年MM月"
									   value="${wfcompensationEntity.treatyearmonth}"/>"
                                       onclick="WdatePicker({dateFmt:'yyyy年MM月',vel:'treatyearmonth'})"/>
                                <input id="treatyearmonth" name="treatyearmonth"
                                       value="${wfcompensationEntity.treatyearmonth}" type="hidden"/>該員因
                                ${wfcompensationEntity.treatreason}
                                需休假治療。
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" align="left">
                                <input id="start" data-options="width:100" style="width:100px"
                                       class="easyui-validatebox inputCss" readonly
                                       value="<fmt:formatDate  pattern="yyyy年MM月dd日"
									   value="${wfcompensationEntity.treatstartdate}"/>"/>
                                -
                                <input id="end" data-options="width:100" style="width:100px"
                                       class="easyui-validatebox inputCss" readonly
                                       value="<fmt:formatDate  pattern="yyyy年MM月dd日"
									   value="${wfcompensationEntity.treatenddate}"/>"/>
                                該員累計休滿
                                ${wfcompensationEntity.treattotalmonth}個月醫療期後，仍不能康復。
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" align="left">
                                <input data-options="width:100" readonly
                                       style="width:100px" class="easyui-validatebox inputCss"
                                       value="<fmt:formatDate  pattern="yyyy年MM月dd日"
									   value="${wfcompensationEntity.nottime}"/>"/>
                                ，${wfcompensationEntity.hospitalname}，該員疾病未愈，不能複職工作，公司依法解除勞動合同。
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">二、員工醫療期相關法律依據</td>
                        </tr>
                        <tr>
                            <td colspan="10">《企業職工患病或非因工負傷醫療期規定》<br/>
                                第三條&nbsp;&nbsp;&nbsp;&nbsp;企業職工因患病或非因工負傷，需要停止工作醫療時，根據本人實際參加工作年限和在本單位工作年限，給予3個月到24個月的醫療期：<br/>
                                (一)實際工作年限十年以下的，在本單位工作年限五年以下的為3個月；五年以上的為6個月。<br/>
                                (二)實際工作年限十年以上的，在本單位工作年限五年以下的為6個月，五年以上十年以下的為9個月；十年以上十五年以下為12個月；十五年以上二十年以下的為18個月；二十年以上的為24個月。<br/>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">三、解除劳动合同及经济补偿的法律依据</td>
                        </tr>
                        <tr>
                            <td colspan="10">
                                《中華人民共和國勞動合同法》<br/>
                                第四十條&nbsp;&nbsp;&nbsp;&nbsp;有下列情形之一的，用人單位提前三十日以書面形式通知勞動者本人或者額外支付勞動者一個月工資後，可以解除勞動合同。<br/>
                                (一)勞動者患病或者非因工負傷，在規定的醫療期滿後不能從事原工作也不能從事由用人單位另行安排的工作的。<br/>
                                第四十六條&nbsp;&nbsp;&nbsp;&nbsp;有下列情形之一的，用人單位應當向勞動者支付經濟補償：（三）用人單位依照本法第四十條規定解除勞動合同的；<br/>
                                第四十七條&nbsp;&nbsp;&nbsp;&nbsp;經濟補償按勞動者在本單位工作的年限，每滿一年支付一個月工資的標準向勞動者支付。六個月以上不滿一年的，按一年計算；不滿六個月的，向勞動者支付半個月工資的經濟補償。<br/>
                                勞動者月工資高於用人單位所在直轄市、設區的市級人民政府公佈的本地區上年度職工月平均工資三倍的，向其支付經濟補償的標準按職工月平均工資三倍的數額支付，向其支付經濟補償的年限最高不超過十二年。<bt/>
                                本條所稱月工資是指勞動者在勞動合同解除或者終止前十二個月的平均工資。
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">四、公司需支付之費用說明</td>
                        </tr>
                        <tr>
                            <td colspan="10">(1)該員
                                <input data-options="width:70" readonly
                                       style="width:70px" class="inputCss"
                                       value="<fmt:formatDate  pattern="yyyy年MM月"
									   value="${wfcompensationEntity.noticeaveyear}"/>"/>
                                份之前12個月的平均工資為￥
                                ${wfcompensationEntity.noticeavesalary}元／月。
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10">
                                (2)根據《中華人民共和國勞動合同法》第四十條規定，應支付該員1個月工資代通知金￥
                                ${wfcompensationEntity.noticemoney}元。
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10">
                                (3)根據《中華人民共和國勞動合同法》第四十六條和第四十七條規定，該員年資為
                                ${wfcompensationEntity.payempyear}年，应支付相当于该员
                                ${wfcompensationEntity.paymonth}个月平均工资的经济补偿金￥
                                ${wfcompensationEntity.paymoney}元。
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10">
                                (4)合計人民幣￥
                                ${wfcompensationEntity.totalnum }元（大寫：
                                ${wfcompensationEntity.totalchinese }）。
                            </td>
                        </tr>
                    </table>
                </td>
           </tr>
            <tr>
                <td>
                    <table class="formList">
                       <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <input type="hidden" id="attachids" name="attachids" value="${wfcompensationEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','經濟補償申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfcompensationEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                               <c:if test="${wfcompensationEntity.workstatus!=null&&wfcompensationEntity.workstatus==3}">
                                  <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                      style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                               </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
<div id="dlg"></div>
<script src='${ctx}/static/js/humanCapital/wfcompensation.js?random=<%= Math.random()%>'></script>
</body>
</html>