<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>員工返住申請</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
<script src="${ctx}/static/js/humanCapital/tqhwfreinliveprocess.js?random=<%= Math.random()%>"></script>
<style type="text/css">
    .td_style2 {
        text-align: left;
    }
    .td_style3{
        border: none 0px;
    }
</style>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfreinliveprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhWfreinliveprocess.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${tQhWfreinliveprocess.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${tQhWfreinliveprocess.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${tQhWfreinliveprocess.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${tQhWfreinliveprocess.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${tQhWfreinliveprocess.makerfactoryid }"/>
    <div class="commonW">
    <div class="headTitle">員工返住申請</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${tQhWfreinliveprocess.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${tQhWfreinliveprocess.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${tQhWfreinliveprocess.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${tQhWfreinliveprocess.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty tQhWfreinliveprocess.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty tQhWfreinliveprocess.makerno}">
            <div class="position_R margin_R">填單人：${tQhWfreinliveprocess.makerno}/${tQhWfreinliveprocess.makername}</div>
        </c:if>
        <div class="clear"></div>
		<table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="4%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${tQhWfreinliveprocess.applyno }" onblur="queryUserInfo('applyno');"/>
                            </td>
                            <td width="4%">申請人&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${tQhWfreinliveprocess.applyname }"/>

                            </td>
                            <td width="4%">性別&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applysex" name="applysex"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${tQhWfreinliveprocess.applysex }" readonly/>
                                <input id="sex" name="sex" type="hidden" value="${tQhWfreinliveprocess.sex }" readonly>
                            </td>
                            <td width="4%">廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${tQhWfreinliveprocess.applyfactoryid }"
                                       data-options="width: 120,required:true,onSelect:function(){onchangeFactory();}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位代碼&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${tQhWfreinliveprocess.applydeptno }"/>
                            </td>
                            <td>申請單位&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox" data-options="width: 350"
                                       value="${tQhWfreinliveprocess.applydeptname }" readonly/>
                            </td>
                            <td>資位&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="applyleveltype" class="easyui-validatebox" data-options="width: 80"
                                       value="${tQhWfreinliveprocess.applyleveltype }" readonly/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>管理職&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applymanager" name="applymanager" class="easyui-validatebox" data-options="width: 80"
                                       value="${tQhWfreinliveprocess.applymanager }" readonly/>
                            </td>
                            <td>入集團日期&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyingroup" name="applyingroup"
                                       class="easyui-validatebox inputCss" datefmt="yyyy-MM-dd" readonly
                                       data-options="width: 100"
                                       value="<fmt:formatDate value="${tQhWfreinliveprocess.applyingroup}"/>" />
                            </td>
                            <td>入廠日期&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyindate" name="applyindate"
                                       class="easyui-validatebox inputCss" datefmt="yyyy-MM-dd" readonly
                                       data-options="width: 100"
                                       value="<fmt:formatDate value="${tQhWfreinliveprocess.applyindate}"/>" />
                            </td>
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applytel" name="applytel" onblur="valdMobilephone(this)" class="easyui-validatebox" data-options="width: 120,required:true,prompt:'請輸入11位手機號碼'"
                                       value="${tQhWfreinliveprocess.applytel }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>退宿原因&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="outreason" name="outreason" class="easyui-validatebox" data-options="width: 350,required:true"
                                       value="${tQhWfreinliveprocess.outreason }"/>
                            </td>
                            <td>返住原因&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="backinreason" name="backinreason" class="easyui-validatebox" data-options="width: 350,required:true"
                                       value="${tQhWfreinliveprocess.backinreason }"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="8%" rowspan="2">已婚者<br/>配偶資料</td>
                            <td class="td_style2">
                                <c:if test="${tQhWfreinliveprocess.isgroup!=null&&tQhWfreinliveprocess.isgroup=='N'}">
                                    <input type="checkbox" value="N" id="isgroupn" name="isgroup" checked onclick="checked_isgroup(this)"/>非集團員工
                                </c:if>
                                <c:if test="${tQhWfreinliveprocess.isgroup!=null&&tQhWfreinliveprocess.isgroup=='Y'}">
                                    <input type="checkbox" value="N" id="isgroupn" name="isgroup" onclick="checked_isgroup(this)"/>非集團員工
                                </c:if>
                                <c:if test="${tQhWfreinliveprocess.isgroup==null}">
                                    <input type="checkbox" value="N" id="isgroupn" name="isgroup" onclick="checked_isgroup(this)"/>非集團員工
                                </c:if>
                            </td>
                            <td>姓名</td>
                            <td colspan="3" class="td_style2">
                                <input id="notmatename" name="notmatename" class="easyui-validatebox inputCss"
                                       data-options="width:100" value="${tQhWfreinliveprocess.notmatename }"/>
                            </td>
                            <td>身份證號</td>
                            <td colspan="5" class="td_style2">
                                <input id="notmatepsnid" name="notmatepsnid" onblur="isCardNo(this)" class="easyui-validatebox inputCss" data-options="width: 150"
                                       value="${tQhWfreinliveprocess.notmatepsnid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td class="td_style2">
                                <c:if test="${tQhWfreinliveprocess.isgroup!=null&&tQhWfreinliveprocess.isgroup=='Y'}">
                                    <input type="checkbox" value="Y" id="isgroupy" name="isgroup" checked onclick="checked_isgroup(this)"/>集團員工
                                </c:if>
                                <c:if test="${tQhWfreinliveprocess.isgroup!=null&&tQhWfreinliveprocess.isgroup=='N'}">
                                    <input type="checkbox" value="Y" id="isgroupy" name="isgroup" onclick="checked_isgroup(this)"/>集團員工
                                </c:if>
                                <c:if test="${tQhWfreinliveprocess.isgroup==null}">
                                    <input type="checkbox" value="Y" id="isgroupy" name="isgroup" onclick="checked_isgroup(this)"/>集團員工
                                </c:if>
                            </td>
                            <td>工號</td>
                            <td class="td_style2">
                                <input id="ismateno" name="ismateno" class="easyui-validatebox" data-options="width: 100"
                                       value="${tQhWfreinliveprocess.ismateno}" onblur="queryUserInfo('ismateno');"/>
                            </td>
                            <td>姓名</td>
                            <td class="td_style2">
                                <input id="ismatename" name="ismatename" class="easyui-validatebox inputCss"
                                       data-options="width:100" readonly value="${tQhWfreinliveprocess.ismatename }"/>
                            </td>
                           <td>身份證號</td>
                            <td class="td_style2">
                                <input id="ismatepsnid" name="ismatepsnid" class="easyui-validatebox" onblur="isCardNo(this)"  data-options="width: 150"
                                       value="${tQhWfreinliveprocess.ismatepsnid }"/>
                            </td>
                            <td>事業群</td>
                            <td class="td_style2">
                                <input id="ismatebg" name="ismatebg" class="easyui-combobox"
                                       panelHeight="auto" value="${tQhWfreinliveprocess.ismatebg }"
                                       data-options="width: 100,required:true"/>
                            </td>
                            <td>資位</td>
                            <td class="td_style2">
                                <input id="ismateleveltype" name="ismateleveltype" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${tQhWfreinliveprocess.ismateleveltype }" readonly/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>代扣代繳聲明&nbsp;<font color="red">*</font></td>
                            <td colspan="11" class="td_style2">
                                每月從本人工資中代扣公寓住宿/水電/物業費共計人民幣<input id="dkcost" name="dkcost" class="easyui-validatebox" data-options="width: 100,required:true"
                                                                 value="${tQhWfreinliveprocess.dkcost }" onblur="valdIsNumber(this)"/> 元
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="8%">附件</td>
                            <td width="92" colspan="8" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="attachids" value="${tQhWfreinliveprocess.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${workFlowId}','員工返住申請','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="10" style="text-align:left;">
                    <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                        <tr>
                            <td style="border:none">
                                <%--<table width="18%" style="float: left;margin-left: 5px;" id="hrchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">人資初核</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(35,'hrchargeTable','hrchargeno','hrchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input id="hrchargeno" name="hrchargeno"
                                                   class="easyui-validatebox" data-options="width: 80,required:true" readonly
                                                   value="${tQhWfreinliveprocess.hrchargeno }"/>
                                            <font color="red">*</font>/
                                            <input id="hrchargename" name="hrchargename"
                                                    class="easyui-validatebox" data-options="width: 80,required:true" readonly
                                                    value="${tQhWfreinliveprocess.hrchargename }"/>
                                        </td>
                                    </tr>
                                </table>--%>
                                <table width="18%" style="float: left;margin-left: 5px;">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">課級主管</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                           <input id="kchargeno" name="kchargeno"
                                                  class="easyui-validatebox" data-options="width:80,required:${requiredMap['kchargeno']}" readonly value="${tQhWfreinliveprocess.kchargeno}"/>
                                            <c:if test="${requiredMap['kchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="kchargename" name="kchargename" readonly class="easyui-validatebox"
                                                    data-options="width:80,required:${requiredMap['kchargeno']}" value="${tQhWfreinliveprocess.kchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">部級主管</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                           <input id="bchargeno" name="bchargeno" class="easyui-validatebox" data-options="width:80,required:${requiredMap['bchargeno']}"
                                                   readonly value="${tQhWfreinliveprocess.bchargeno }"/><c:if test="${requiredMap['bchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="bchargename" name="bchargename" readonly class="easyui-validatebox"
                                                    data-options="width:80,required:${requiredMap['bchargeno']}" value="${tQhWfreinliveprocess.bchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;">
                                        <tr>
                                            <td>
                                                <table width="100%">
                                                    <tr>
                                                        <td style="border: none;text-align: right;">廠級主管</td>
                                                        <td style="border: none;">
                                                            <div class="float_L qhUserIcon"
                                                                 onclick="selectRole($('#applydeptno').val(),'cchargeno','cchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <input id="cchargeno" name="cchargeno" class="easyui-validatebox" data-options="width:80,required:${requiredMap['cchargeno']}"
                                                       readonly value="${tQhWfreinliveprocess.cchargeno }"/><c:if test="${requiredMap['cchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                /<input id="cchargename" name="cchargename" readonly class="easyui-validatebox"
                                                        data-options="width:80,required:${requiredMap['cchargeno']}" value="${tQhWfreinliveprocess.cchargename }"/>
                                            </td>
                                        </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;" id="zwjchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">總務接收入住</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(49,'zwjchargeTable','zwjchargeno','zwjchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input id="zwjchargeno" name="zwjchargeno" class="easyui-validatebox" data-options="width:80,required:${requiredMap['zwjchargeno']}"
                                                   readonly value="${tQhWfreinliveprocess.zwjchargeno }"/><c:if test="${requiredMap['zwjchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="zwjchargename" name="zwjchargename" readonly class="easyui-validatebox"
                                                    data-options="width:80,required:${requiredMap['zwjchargeno']}" value="${tQhWfreinliveprocess.zwjchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;" id="zwqchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%" >
                                                <tr>
                                                    <td style="border: none;text-align: right;">總務確認入住</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(50,'zwqchargeTable','zwqchargeno','zwqchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input id="zwqchargeno" name="zwqchargeno" class="easyui-validatebox" data-options="width:80,required:${requiredMap['zwqchargeno']}"
                                                   readonly value="${tQhWfreinliveprocess.zwqchargeno }"/><c:if test="${requiredMap['zwqchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="zwqchargename" name="zwqchargename" readonly class="easyui-validatebox"
                                                    data-options="width:80,required:${requiredMap['zwqchargeno']}" value="${tQhWfreinliveprocess.zwqchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <%--<tr>
                            <td style="border:none">
                               <table width="18%" style="float: left;margin-left: 5px;" id="xzchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">薪資窗口作業</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(41,'xzchargeTable','xzchargeno','xzchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input id="xzchargeno" name="xzchargeno"
                                                   class="easyui-validatebox" data-options="width: 80,required:true" readonly
                                                   value="${tQhWfreinliveprocess.xzchargeno }"/>
                                            <font color="red">*</font>/
                                            <input id="xzchargename" name="xzchargename"
                                                   class="easyui-validatebox" data-options="width: 80,required:true" readonly
                                                   value="${tQhWfreinliveprocess.xzchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>--%>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="10" style="text-align:left;">
                    <table class="flowList" style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                        <tr>
                            <td>簽核時間</td>
                            <td>簽核節點</td>
                            <td>簽核主管</td>
                            <td>簽核意見</td>
                            <td>批註</td>
                            <td>簽核電腦IP</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                    <a href="javascript:;" id="btnSave" class="easyui-linkbutton" data-options="iconCls:'icon-add'"
                       style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                    <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                       style="width: 100px;" onclick="saveInfo(2);">提交</a>
                </td>
            </tr>
        </table>
	</div>
	<input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
	</form>
  </div>
</body>
</html>