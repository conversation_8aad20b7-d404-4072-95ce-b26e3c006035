<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>員工返住申請</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/js/humanCapital/tqhwfreinliveprocess.js?random=<%= Math.random()%>"></script>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
<style type="text/css">
    .td_style2 {
        text-align: left;
    }
    .td_style3{
        border: none 0px;
    }
</style>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfreinliveprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhWfreinliveprocess.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${tQhWfreinliveprocess.serialno }"/>
    <div class="commonW">
    <div class="headTitle">員工返住申請</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${tQhWfreinliveprocess.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${tQhWfreinliveprocess.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${tQhWfreinliveprocess.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${tQhWfreinliveprocess.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${tQhWfreinliveprocess.makerno}/${tQhWfreinliveprocess.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="4%">申請人工號</td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox inputCss"
                                       data-options="width: 80" value="${tQhWfreinliveprocess.applyno }" readonly/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="applyname" class="easyui-validatebox inputCss"
                                       data-options="width:80" value="${tQhWfreinliveprocess.applyname }" readonly />
                            </td>
                            <td width="4%">性別</td>
                            <td width="6%" class="td_style1">
                                <input id="applysex" name="applysex" class="easyui-validatebox inputCss" data-options="width: 90"
                                       value="${tQhWfreinliveprocess.applysex }" readonly />
                            </td>
                            <td width="4%">廠區</td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${tQhWfreinliveprocess.applyfactoryid }"
                                       data-options="width: 120" disabled/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位代碼</td>
                            <td class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       value="${tQhWfreinliveprocess.applydeptno }" readonly/>
                            </td>
                            <td>申請單位</td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox inputCss" data-options="width: 350"
                                       value="${tQhWfreinliveprocess.applydeptname }" readonly/>
                            </td>
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="applyleveltype" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${tQhWfreinliveprocess.applyleveltype }" readonly/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="applymanager" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${tQhWfreinliveprocess.applymanager }" readonly/>
                            </td>
                            <td>入集團日期</td>
                            <td class="td_style1">
                                <input id="applyingroup" name="applyingroup"
                                       class="easyui-validatebox inputCss" datefmt="yyyy-MM-dd" readonly
                                       data-options="width: 100"
                                       value="<fmt:formatDate value="${tQhWfreinliveprocess.applyingroup}"/>" />
                            </td>
                            <td>入廠日期</td>
                            <td class="td_style1">
                                <input id="applyindate" name="applyindate"
                                       class="easyui-validatebox inputCss" datefmt="yyyy-MM-dd" readonly
                                       data-options="width: 100"
                                       value="<fmt:formatDate value="${tQhWfreinliveprocess.applyindate}"/>" />
                            </td>
                            <td>聯繫方式</td>
                            <td class="td_style1">
                                <input id="applytel" name="applytel" class="easyui-validatebox inputCss" data-options="width: 120"
                                       value="${tQhWfreinliveprocess.applytel }" readonly/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>退宿原因</td>
                            <td colspan="3" class="td_style1">
                                <input id="outreason" name="outreason" class="easyui-validatebox inputCss" data-options="width: 350"
                                       value="${tQhWfreinliveprocess.outreason }" readonly/>
                            </td>
                            <td>返住原因</td>
                            <td colspan="3" class="td_style1">
                                <input id="backinreason" name="backinreason" class="easyui-validatebox inputCss" data-options="width: 350"
                                       value="${tQhWfreinliveprocess.backinreason }" readonly/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="8%" rowspan="2">已婚者<br/>配偶資料</td>
                            <td class="td_style2">
                                <c:if test="${tQhWfreinliveprocess.isgroup!=null&&tQhWfreinliveprocess.isgroup=='N'}">
                                    <input type="checkbox" value="N" id="isgroupn" name="isgroup" checked disabled/>非集團員工
                                </c:if>
                                <c:if test="${tQhWfreinliveprocess.isgroup!=null&&tQhWfreinliveprocess.isgroup=='Y'}">
                                    <input type="checkbox" value="N" id="isgroupn" name="isgroup" disabled/>非集團員工
                                </c:if>
                                <c:if test="${tQhWfreinliveprocess.isgroup ==null}">
                                    <input type="checkbox" value="N" id="isgroupn" name="isgroup" disabled/>非集團員工
                                </c:if>
                            </td>
                            <td>姓名</td>
                            <td colspan="3" class="td_style2">
                                <input id="notmatename" name="notmatename" class="easyui-validatebox inputCss"
                                       data-options="width:100" value="${tQhWfreinliveprocess.notmatename }" readonly/>
                            </td>
                            <td>身份證號</td>
                            <td colspan="5" class="td_style2">
                                <input id="notmatepsnid" name="notmatepsnid" class="easyui-validatebox inputCss" data-options="width: 150"
                                       value="${tQhWfreinliveprocess.notmatepsnid }" readonly/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td class="td_style2">
                                <c:if test="${tQhWfreinliveprocess.isgroup!=null&&tQhWfreinliveprocess.isgroup=='Y'}">
                                    <input type="checkbox" value="Y" id="isgroupy" name="isgroup" checked disabled/>集團員工
                                </c:if>
                                <c:if test="${tQhWfreinliveprocess.isgroup!=null&&tQhWfreinliveprocess.isgroup=='N'}">
                                    <input type="checkbox" value="Y" id="isgroupy" name="isgroup" disabled />集團員工
                                </c:if>
                                <c:if test="${tQhWfreinliveprocess.isgroup==null}">
                                    <input type="checkbox" value="Y" id="isgroupy" name="isgroup" disabled />集團員工
                                </c:if>
                            </td>
                            <td>工號</td>
                            <td class="td_style2">
                                <input id="ismateno" name="ismateno" class="easyui-validatebox inputCss" data-options="width: 100"
                                       value="${tQhWfreinliveprocess.ismateno}" readonly/>
                            </td>
                            <td>姓名</td>
                            <td class="td_style2">
                                <input id="ismatename" name="ismatename" class="easyui-validatebox inputCss"
                                       data-options="width:100" value="${tQhWfreinliveprocess.ismatename }" readonly/>
                            </td>
                            <td>身份證號</td>
                            <td class="td_style2">
                                <input id="ismatepsnid" name="ismatepsnid" class="easyui-validatebox inputCss" data-options="width: 150"
                                       value="${tQhWfreinliveprocess.ismatepsnid }" readonly/>
                            </td>
                            <td>事業群</td>
                            <td class="td_style2">
                                <input id="ismatebg" name="ismatebg" class="easyui-combobox"
                                       panelHeight="auto" value="${tQhWfreinliveprocess.ismatebg }"
                                       data-options="width: 100" disabled/>
                            </td>
                            <td>資位</td>
                            <td class="td_style2">
                                <input id="ismateleveltype" name="ismateleveltype" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${tQhWfreinliveprocess.ismateleveltype }" readonly/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>代扣代繳聲明</td>
                            <td colspan="11" class="td_style2">
                                每月從本人工資中代扣公寓住宿/水電/物業費共計人民幣<input id="dkcost" name="dkcost" class="easyui-validatebox inputCss" data-options="width: 100"
                                                                 value="${tQhWfreinliveprocess.dkcost }" readonly/> 元
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                     <table class="formList">
                         <tr align="center">
                             <td width="8%">附件</td>
                             <td width="92%" class="td_style2">
                                 <c:if test="${tQhWfreinliveprocess.attachids!=null}">
                                     <div id="dowloadUrl">
                                         <c:forEach items="${file}" varStatus="i" var="item">
                                             <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                                 <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                             </div>
                                         </c:forEach>
                                     </div>
                                 </c:if>
                                 <c:if test="${tQhWfreinliveprocess.attachids==null}">無</c:if>
                             </td>
                         </tr>
                         <c:choose>
                             <c:when test="${not empty nodeName&&'總務確認入住' eq nodeName}">
                                 <tr>
                                     <td colspan="8">
                                         <font color="red">總務確認入住日期</font>
                                     </td>
                                 </tr>
                                 <tr align="center">
                                     <td width="8%">入住日期<font color="red">*</font></td>
                                     <td width="92%" class="td_style2">
                                         <input id="zwqdate" name="zwqdate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                                data-options="width: 100"
                                                value="<fmt:formatDate value="${tQhWfreinliveprocess.zwqdate}"/>"/>
                                     </td>
                                 </tr>
                             </c:when>
                             <c:otherwise>
                                 <c:if test="${tQhWfreinliveprocess.zwqdate!=null&&tQhWfreinliveprocess.zwqdate!='' &&(nodeName eq('薪資窗口作業'))}">
                                     <tr>
                                         <td colspan="8">
                                             <font color="red">總務確認退宿日期</font>
                                         </td>
                                     </tr>
                                     <tr align="center">
                                         <td width="8%">入住日期<font color="red">*</font></td>
                                         <td width="92%" class="td_style2">
                                             <input id="zwqdate" name="zwqdate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                                    data-options="width: 100"
                                                    value="<fmt:formatDate value="${tQhWfreinliveprocess.zwqdate}"/>" disabled/>
                                         </td>
                                     </tr>
                                 </c:if>
                             </c:otherwise>
                         </c:choose>
                        <tr align="center">
                            <td width="8%">批註</td>
                            <td width="92%" class="td_style2">
						        <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;" rows="4" cols="4" data-options="required:true"></textarea>
                            </td>
                        </tr>
                         <c:choose>
                             <c:when test="${not empty nodeName&&'總務確認入住' eq nodeName}">
                                 <tr align="center">
                                     <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                         <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" perCall="zwupdate" serialNo="${tQhWfreinliveprocess.serialno}"></fox:action>
                                     </td>
                                 </tr>
                             </c:when>
                             <c:otherwise>
                                 <tr align="center">
                                     <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                         <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" serialNo="${tQhWfreinliveprocess.serialno}"></fox:action>
                                     </td>
                                 </tr>
                             </c:otherwise>
                         </c:choose>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','員工返住申請');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhWfreinliveprocess.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
<div id="dlg"></div>
</body>
</html>