<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>人力資源處系統應用申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
        .td_style3{
            border: none 0px;
        }
        #applyreason::-webkit-input-placeholder::after {
            display:block;
            content:"prompt:'使用目的：\A資料範圍：\A使用信息（欄位）：'";
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfhrsystemuseprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfhrsystemuseprocessEntity.id }"/>
    <input id="serialno" name="wfhrsystemuseprocess.serialno" type="hidden" value="${wfhrsystemuseprocessEntity.serialno }"/>
    <input id="makerno" name="wfhrsystemuseprocess.makerno" type="hidden" value="${wfhrsystemuseprocessEntity.makerno }"/>
    <input id="makername" name="wfhrsystemuseprocess.makername" type="hidden" value="${wfhrsystemuseprocessEntity.makername }"/>
    <input id="makerdeptno" name="wfhrsystemuseprocess.makerdeptno" type="hidden" value="${wfhrsystemuseprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfhrsystemuseprocess.makerfactoryid" type="hidden"
           value="${wfhrsystemuseprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">人力資源處系統應用申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfhrsystemuseprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfhrsystemuseprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfhrsystemuseprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfhrsystemuseprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfhrsystemuseprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfhrsystemuseprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfhrsystemuseprocessEntity.makerno}/${wfhrsystemuseprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">申請系統<br>負責人工號&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="applyno" name="wfhrsystemuseprocess.applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfhrsystemuseprocessEntity.applyno}" onblur="queryUserInfo('apply');" />
                            </td>
                            <td width="8%">申請系統<br>負責人</td>
                            <td width="10%" class="td_style1">
                                <input id="applyname" name="wfhrsystemuseprocess.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wfhrsystemuseprocessEntity.applyname }"/>
                            </td>
                            <td width="7%">費用代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="applycostno" name="wfhrsystemuseprocess.applycostno"
                                       class="easyui-validatebox inputCss" style="width:90px;"
                                       value="${wfhrsystemuseprocessEntity.applycostno }"/>
                            </td>
                            <td width="10%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="applydeptno" name="wfhrsystemuseprocess.applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfhrsystemuseprocessEntity.applydeptno }"/>
                            </td>
                            <td width="10%">廠區&nbsp;<font color="red">*</font></td>
                            <td width="15%" class="td_style1">
                                <input id="applyfactoryid" name="wfhrsystemuseprocess.applyfactoryid"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${wfhrsystemuseprocessEntity.applyfactoryid }" data-options="width: 120,required:true"/>
                                <input id="applynofactoryid" name="wfhrsystemuseprocess.applynofactoryid" type="hidden"
                                       value="${wfhrsystemuseprocessEntity.applynofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="wfhrsystemuseprocess.applydeptname"
                                       class="easyui-validatebox" data-options="width: 320,required:true"
                                       value="${wfhrsystemuseprocessEntity.applydeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="wfhrsystemuseprocess.applyemail" class="easyui-validatebox"
                                       value="${wfhrsystemuseprocessEntity.applyemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applytel" name="wfhrsystemuseprocess.applytel" class="easyui-validatebox"
                                       style="width:120px;"
                                       value="${wfhrsystemuseprocessEntity.applytel}"
                                       data-options="required:true"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">系統應用名稱&nbsp;<font color="red">*</font></td>
                            <td class="td_style1"  colspan="4">
                                <input id="systemapplyname" name="wfhrsystemuseprocess.systemapplyname"
                                       class="easyui-validatebox"  data-options="width: 320,required:true"
                                       value="${wfhrsystemuseprocessEntity.systemapplyname }"/>
                            </td>
                            <td width="10%">系統應用類別&nbsp;<font color="red">*</font></td>
                            <td class="td_style2"  colspan="4">
                                <div class="systemapplytypeDiv"></div>
                                <input id="systemapplytype" name="wfhrsystemuseprocess.systemapplytype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${wfhrsystemuseprocessEntity.systemapplytype }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">
                                使用者信息
                            </td>
                            <td width="85%" colspan="9">
                                <table width="100%">
                                    <tr align="center">
                                        <td width="15%">工號&nbsp;<font color="red">*</font></td>
                                        <td width="15%">姓名&nbsp;<font color="red">*</font></td>
                                        <td width="20%">綁定電腦IP&nbsp;<font color="red">*</font></td>
                                        <td width="20%">工作崗位&nbsp;<font color="red">*</font></td>
                                        <td width="10%">操作</td>
                                    </tr>
                                    <tbody id="info_Body_ip">
                                    <c:if test="${hrsystemuseitems!=null&&hrsystemuseitems.size()>0}">
                                        <c:forEach items="${hrsystemuseitems}" var="itemsEntity" varStatus="status">
                                            <tr id="hrsystemuseitems${status.index}" align="center">
                                                <td>
                                                    <input id="userno${status.index}" name="wfhrsystemuseitems[${status.index}].userno" class="easyui-validatebox"
                                                           onblur="queryIpBindUserInfo('user',${status.index});" data-options="width: 120,required:true" value="${itemsEntity.userno}"/>
                                                </td>
                                                <td>
                                                    <input id="username${status.index}" name="wfhrsystemuseitems[${status.index}].username" class="easyui-validatebox"
                                                           data-options="width: 120" value="${itemsEntity.username}"/>
                                                </td>
                                                <td>
                                                    <input id="userip${status.index}"  name="wfhrsystemuseitems[${status.index}].userip" class="easyui-validatebox"
                                                           data-options="width:120,required:true,validType:'ip[\'userip${status.index}\']'" value="${itemsEntity.userip}"/>
                                                </td>
                                                <td>
                                                    <input id="userjob${status.index}" name="wfhrsystemuseitems[${status.index}].userjob" class="easyui-validatebox"
                                                           data-options="width: 250,required:true" value="${itemsEntity.userjob}"/>
                                                </td>
                                                <td align="center">
                                                    <a href="javascript:void(0)" class="easyui-linkbutton" onclick="addTrInfo();"
                                                       data-options="plain:true,iconCls:'icon-add'"></a>
                                                    <a href="javascript:void(0)" class="easyui-linkbutton" onclick="bondedgooddeltr(${status.index});"
                                                       data-options="plain:true,iconCls:'icon-cancel'"></a>
                                                    <input id="shunxu${status.index}" type="hidden" name="wfhrsystemuseitems[${status.index}].shunxu" value="${itemsEntity.shunxu}"/>
                                                </td>
                                            </tr>
                                        </c:forEach>
                                    </c:if>
                                    <c:if test="${hrsystemuseitems==null||hrsystemuseitems.size()==0}">
                                        <tr id="hrsystemuseitems0" align="center">
                                            <td>
                                                <input id="userno0" onblur="queryIpBindUserInfo('user',0);" name="wfhrsystemuseitems[0].userno"
                                                       class="easyui-validatebox" data-options="width: 120,required:true" value=""/>
                                            </td>
                                            <td>
                                                <input id="username0" name="wfhrsystemuseitems[0].username" class="easyui-validatebox"
                                                       data-options="width: 120" value=""/>
                                            </td>
                                            <td>
                                                <input id="userip0" name="wfhrsystemuseitems[0].userip" class="easyui-validatebox"
                                                       data-options="width: 120,required:true,validType:'ip[\'userip0\']'" value=""/>
                                            </td>
                                            <td>
                                                <input id="userjob0" name="wfhrsystemuseitems[0].userjob" class="easyui-validatebox"
                                                       data-options="width: 250,required:true" value=""/>
                                            </td>
                                            <td align="center">
                                                <a href="javascript:void(0)" class="easyui-linkbutton" onclick="addTrInfo();"
                                                   data-options="plain:true,iconCls:'icon-add'"></a>
                                                <a href="javascript:void(0)" class="easyui-linkbutton"
                                                   onclick="bondedgooddeltr(0);" data-options="plain:true,iconCls:'icon-cancel'"></a>
                                                <input id="shunxu0" type="hidden" name="wfhrsystemuseitems[0].shunxu" value=""/>
                                            </td>
                                        </tr>
                                    </c:if>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">申請信息來源&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style2">
                                <div class="applysourceDiv"></div>
                                <input id="applysource" name="wfhrsystemuseprocess.applysource"
                                       type="hidden" class="easyui-validatebox" data-options="width: 300"
                                       value="${wfhrsystemuseprocessEntity.applysource }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">使用目的、範圍<br>及使用信息&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style2">
                                <textarea id="applyreason" name="wfhrsystemuseprocess.applyreason"
                                          class="easyui-validatebox"
                                          oninput="return LessThan(this);"
                                          onchange="return LessThan(this);"
                                          onpropertychange="return LessThan(this);"
                                          maxlength="300" onblur="onblurr();"  onfocus="onfocuss();"
                                          style="width:99%;height:80px;" data-options="required:true,validType:'length[0,300]'"
                                          rows="5" cols="6">${wfhrsystemuseprocessEntity.applyreason }</textarea><span id="txtNum"></span>
                           <%-- ,prompt:'使用目的：\n資料範圍：\n使用信息（欄位）：'--%>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">
                                <input type="checkbox" id="agree" name="agree"/>申請單位已閱讀<br>并同意注意事項
                            </td>
                            <td colspan="9" class="td_style2">
                                <p>
                                    本單位承諾所申請之資料依集團資安要求僅限本次申請之系統應用功能模塊使用，不做他用，并對相關資料的使用安全性負責。<br>
                                    若因系統管理不善導致信息泄露 ，責任人及其主管承擔由此產生的一切責任。<br>
                                    為了保障人事整合資料庫人事信息的安全性，同時為人事整合資料庫系統管理員開通終端賬號，利於人事信息數據使用的日常監管。
                                </p>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_renliziyuanxitongyingyongshenqingdan_v1','人力資源處系統應用申請單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="wfhrsystemuseprocess.kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wfhrsystemuseprocessEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="wfhrsystemuseprocess.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfhrsystemuseprocessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="wfhrsystemuseprocess.bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfhrsystemuseprocessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="wfhrsystemuseprocess.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfhrsystemuseprocessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'cchargeno','cchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wfhrsystemuseprocess.cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfhrsystemuseprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wfhrsystemuseprocess.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfhrsystemuseprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applynofactoryid').val(),null)"></div>

                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wfhrsystemuseprocess.zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfhrsystemuseprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="wfhrsystemuseprocess.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfhrsystemuseprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#applydeptno').val(),'zcchargeno','zcchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="wfhrsystemuseprocess.zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wfhrsystemuseprocessEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="wfhrsystemuseprocess.zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfhrsystemuseprocessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產品處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#applydeptno').val(),'pcchargeno','pcchargename',$('#applynofactoryid').val(),'wfhrsystemuseprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="wfhrsystemuseprocess.pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${wfhrsystemuseprocessEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="wfhrsystemuseprocess.pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wfhrsystemuseprocessEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="rzjgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">人資監管窗口</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(213,'rzjgchargeno','rzjgchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="rzjgchargeno" name="wfhrsystemuseprocess.rzjgchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['rzjgchargeno']}"
                                                               readonly
                                                               value="${wfhrsystemuseprocessEntity.rzjgchargeno }"/><c:if
                                                            test="${requiredMap['rzjgchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="rzjgchargename" name="wfhrsystemuseprocess.rzjgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['rzjgchargeno']}"
                                                                value="${wfhrsystemuseprocessEntity.rzjgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="rzb1chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">人資主管1</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(215,'rzb1chargeno','rzb1chargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="rzb1chargeno" name="wfhrsystemuseprocess.rzb1chargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['rzb1chargeno']}"
                                                               readonly
                                                               value="${wfhrsystemuseprocessEntity.rzb1chargeno }"/><c:if
                                                            test="${requiredMap['rzb1chargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="rzb1chargename" name="wfhrsystemuseprocess.rzb1chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['rzb1chargeno']}"
                                                                value="${wfhrsystemuseprocessEntity.rzb1chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="rzb2chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">人資主管2</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(216,'rzb2chargeno','rzb2chargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="rzb2chargeno" name="wfhrsystemuseprocess.rzb2chargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['rzb2chargeno']}"
                                                               readonly
                                                               value="${wfhrsystemuseprocessEntity.rzb2chargeno }"/><c:if
                                                            test="${requiredMap['rzb2chargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="rzb2chargename" name="wfhrsystemuseprocess.rzb2chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['rzb2chargeno']}"
                                                                value="${wfhrsystemuseprocessEntity.rzb2chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="kfgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">系統開發工程師評估
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(214,'kfgchargeno','kfgchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kfgchargeno" name="wfhrsystemuseprocess.kfgchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kfgchargeno']}"
                                                               readonly
                                                               value="${wfhrsystemuseprocessEntity.kfgchargeno }"/><c:if
                                                            test="${requiredMap['kfgchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kfgchargename" name="wfhrsystemuseprocess.kfgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kfgchargeno']}"
                                                                value="${wfhrsystemuseprocessEntity.kfgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>



                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="kfkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">系統開發課級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(65,'kfkchargeTable','kfkchargeno','kfkchargename',$('#applyfactoryid').combobox('getValue'),'wfhrsystemuseprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kfkchargeno" name="wfhrsystemuseprocess.kfkchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kfkchargeno']}"
                                                               readonly
                                                               value="${wfhrsystemuseprocessEntity.kfkchargeno }"/><c:if
                                                            test="${requiredMap['kfkchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kfkchargename" name="wfhrsystemuseprocess.kfkchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kfkchargeno']}"
                                                                value="${wfhrsystemuseprocessEntity.kfkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="kfbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">系統開發部級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(66,'kfbchargeTable','kfbchargeno','kfbchargename',$('#applyfactoryid').combobox('getValue'),'wfhrsystemuseprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kfbchargeno" name="wfhrsystemuseprocess.kfbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kfbchargeno']}"
                                                               readonly
                                                               value="${wfhrsystemuseprocessEntity.kfbchargeno }"/><c:if
                                                            test="${requiredMap['kfbchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kfbchargename" name="wfhrsystemuseprocess.kfbchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kfbchargeno']}"
                                                                value="${wfhrsystemuseprocessEntity.kfbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"  id="xtkfzxjzgTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">系統開發中心級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(261,'xtkfzxjzgTable','xtkfzxjzgno','xtkfzxjzgname',$('#applyfactoryid').combobox('getValue'),'wfhrsystemuseprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="xtkfzxjzgno" name="wfhrsystemuseprocess.xtkfzxjzgno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['xtkfzxjzgno']}"
                                                               readonly
                                                               value="${wfhrsystemuseprocess.xtkfzxjzgno }"/><c:if test="${requiredMap['xtkfzxjzgno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="xtkfzxjzgname" name="wfhrsystemuseprocess.xtkfzxjzgname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['xtkfzxjzgno']}"
                                                                value="${wfhrsystemuseprocess.xtkfzxjzgname }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <input type="hidden" id="applysourceotherAudit"   value="${wfhrsystemuseprocessEntity.applysourceother}" />
    <div id="win"></div>
</form>
</div>
<script src='${ctx}/static/js/humanCapital/wfhrsystemuseprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>
