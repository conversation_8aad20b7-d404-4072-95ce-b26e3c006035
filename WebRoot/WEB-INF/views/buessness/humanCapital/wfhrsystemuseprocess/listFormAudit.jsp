<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>人力資源處系統應用申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfhrsystemuseprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfhrsystemuseprocessEntity.id }"/>
    <input id="serialno" name="wfhrsystemuseprocess.serialno" type="hidden" value="${wfhrsystemuseprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">人力資源處系統應用申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfhrsystemuseprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfhrsystemuseprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfhrsystemuseprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfhrsystemuseprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfhrsystemuseprocessEntity.makerno}/${wfhrsystemuseprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">申請系統<br>負責人工號</td>
                            <td width="10%" class="td_style2">${wfhrsystemuseprocessEntity.applyno}</td>
                            <td width="8%">申請系統<br>負責人</td>
                            <td width="10%" class="td_style2">${wfhrsystemuseprocessEntity.applyname}</td>
                            <td width="7%">費用代碼</td>
                            <td width="10%" class="td_style2">${wfhrsystemuseprocessEntity.applycostno}</td>
                            <td width="10%">單位代碼</td>
                            <td width="10%" class="td_style2">${wfhrsystemuseprocessEntity.applydeptno }</td>
                            <td width="10%">廠區</td>
                            <td width="15%" class="td_style1">
                                <input id="applyfactoryid" name="wfhrsystemuseprocess.applyfactoryid"
                                       class="easyui-combobox" disabled
                                       panelHeight="auto" value="${wfhrsystemuseprocessEntity.applyfactoryid }"
                                       data-options="width: 120,required:true,onSelect:function(){onchangeFactory('applyfactoryid');}"/>
                                <input id="applynofactoryid" name="wfhrsystemuseprocess.applynofactoryid" type="hidden"
                                       value="${wfhrsystemuseprocessEntity.applynofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="3" class="td_style2">${wfhrsystemuseprocessEntity.applydeptname }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfhrsystemuseprocessEntity.applyemail }</td>
                            <td>聯繫方式</td>
                            <td class="td_style2">${wfhrsystemuseprocessEntity.applytel}</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">系統應用名稱</td>
                            <td class="td_style2" colspan="4">
                                ${wfhrsystemuseprocessEntity.systemapplyname}
                            </td>
                            <td width="10%">系統應用類別</td>
                            <td class="td_style2" colspan="4">${wfhrsystemuseprocessEntity.systemapplytype }</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">
                                使用者信息
                            </td>
                            <td width="85%" colspan="9">
                                <table width="100%">
                                    <tr align="center">
                                        <td width="15%">工號</td>
                                        <td width="15%">姓名</td>
                                        <td width="20%">綁定電腦IP</td>
                                        <td width="20%">工作崗位</td>
                                    </tr>
                                    <tbody id="info_Body_ip">
                                    <c:if test="${hrsystemuseitems!=null&&hrsystemuseitems.size()>0}">
                                        <c:forEach items="${hrsystemuseitems}" var="itemsEntity" varStatus="status">
                                            <tr id="hrsystemuseitems${status.index}" align="center">
                                                <td>${itemsEntity.userno}</td>
                                                <td>${itemsEntity.username}</td>
                                                <td>${itemsEntity.userip}</td>
                                                <td>${itemsEntity.userjob}</td>
                                            </tr>
                                        </c:forEach>
                                    </c:if>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">申請信息來源</td>
                            <td colspan="9" class="td_style2">
                                ${wfhrsystemuseprocessEntity.applysource }
                                ${wfhrsystemuseprocessEntity.applysourceother}
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">使用目的、範圍<br>及使用信息</td>
                            <td colspan="9" class="td_style2">
                                <textarea name="wfhrsystemuseprocess.applyreason"
                                          class="easyui-validatebox"
                                          style="width:99%;height:80px;" readonly
                                          rows="5" cols="6">${wfhrsystemuseprocessEntity.applyreason }</textarea>
                            </td>
                        </tr>
                        <c:if test="${nodeOrder ge 7}">
                            <tr>
                                <td colspan="10" class="td_style1">審核主管評估意見</td>
                            </tr>
                        </c:if>
                        <c:choose>
                            <c:when test="${not empty nodeName&&'人資監管窗口' eq nodeName}">
                                <tr align="center">
                                    <td width="10%">人資監管窗口評估意見<font color="red">*</font></td>
                                    <td colspan="9" class="td_style2">
                                        <textarea id="assessidea" name="wfhrsystemuseprocess.assessidea"
                                          class="easyui-validatebox" style="width:99%;height:60px;"
                                          rows="2" cols="2">${wfhrsystemuseprocessEntity.assessidea }</textarea>
                                        <br><br>
                                        <div style="float: left;height: 20px;line-height: 20px">自核准日起使用期限:</div>
                                        <div class="servicelifeDiv"></div>
                                        <div style="float: left;"><input id="servicelife" name="wfhrsystemuseprocess.servicelife"
                                                    type="hidden" class="easyui-validatebox" data-options="width: 150"
                                                    value="${wfhrsystemuseprocessEntity.servicelife }"/>
                                        </div>
                                    </td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <c:if test="${wfhrsystemuseprocessEntity.assessidea!=null&&wfhrsystemuseprocessEntity.assessidea!='' &&(nodeOrder ge 7)}">
                                <tr align="center">
                                    <td width="10%">人資監管窗口評估意見</td>
                                    <td colspan="9" class="td_style2">
                                        <textarea name="wfhrsystemuseprocess.assessidea"
                                                  class="easyui-validatebox" style="width:99%;height:60px;" readonly
                                                  rows="2" cols="3">${wfhrsystemuseprocessEntity.assessidea }</textarea>
                                        <br>
                                        <div style="float: left;height: 20px;line-height: 20px">自核准日起使用期限:${wfhrsystemuseprocessEntity.servicelife }</div>
                                    </td>
                                </tr>
                                </c:if>
                            </c:otherwise>
                        </c:choose>
                        <c:choose>
                            <c:when test="${not empty nodeName&&'人資主管1' eq nodeName}">
                                <tr align="center">
                                    <td width="10%">人資主管意見</td>
                                    <td colspan="9" class="td_style2">
                                        <div style="float: left;height: 20px;line-height: 20px">人資主管1意見</div><br>
                                        <textarea id="rzb1idea" name="wfhrsystemuseprocess.rzb1idea" class="easyui-validatebox"
                                                  style="width:99%;height:40px;"
                                                  rows="2" cols="2">${wfhrsystemuseprocessEntity.rzb1idea}</textarea>
                                    </td>
                                </tr>
                            </c:when>
                            <c:when test="${not empty nodeName&&'人資主管2' eq nodeName}">
                                <tr align="center">
                                    <td width="10%">人資主管意見</td>
                                    <td colspan="9" class="td_style2">
                                        <div style="float: left;height: 20px;line-height: 20px">人資主管1意見</div><br>
                                        <textarea name="wfhrsystemuseprocess.rzb1idea" class="easyui-validatebox"
                                                  style="width:99%;height:40px;" readonly
                                                  rows="2" cols="2">${wfhrsystemuseprocessEntity.rzb1idea}</textarea>
                                        <br>
                                        <div style="float: left;height: 20px;line-height: 20px">人資主管2意見</div><br>
                                        <textarea id="rzb2idea" name="wfhrsystemuseprocess.rzb2idea" class="easyui-validatebox"
                                                  style="width:99%;height:40px;"
                                                  rows="2" cols="2">${wfhrsystemuseprocessEntity.rzb2idea}</textarea>
                                    </td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <c:if test="${(wfhrsystemuseprocessEntity.rzb1idea!=null&&wfhrsystemuseprocessEntity.rzb1idea!='')&& (wfhrsystemuseprocessEntity.rzb2idea!=null&&wfhrsystemuseprocessEntity.rzb2idea!='') &&(nodeOrder ge 10)}">
                                <tr align="center">
                                    <td width="10%">人資主管意見</td>
                                    <td colspan="9" class="td_style2">
                                        <div style="float: left;height: 20px;line-height: 20px">人資主管1意見</div><br>
                                        <textarea name="wfhrsystemuseprocess.rzb1idea" class="easyui-validatebox"
                                                  style="width:99%;height:40px;" readonly
                                                  rows="2" cols="2">${wfhrsystemuseprocessEntity.rzb1idea}</textarea>
                                        <br>
                                        <div style="float: left;height: 20px;line-height: 20px">人資主管2意見</div><br>
                                        <textarea name="wfhrsystemuseprocess.rzb2idea" class="easyui-validatebox"
                                                  style="width:99%;height:40px;"
                                                  rows="2" cols="2">${wfhrsystemuseprocessEntity.rzb2idea}</textarea>
                                    </td>
                                </tr>
                                </c:if>
                            </c:otherwise>
                        </c:choose>
                        <c:choose>
                            <c:when test="${not empty nodeName&&'系統開發工程師評估' eq nodeName}">
                                <tr align="center">
                                    <td width="10%">系統開發工程師評估<font color="red">*</font></td>
                                    <td colspan="9" class="td_style2">
                                        <textarea id="kfgidea" name="wfhrsystemuseprocess.kfgidea" class="easyui-validatebox"
                                                  style="width:99%;height:40px;"
                                                  rows="3" cols="3">${wfhrsystemuseprocessEntity.kfgidea}</textarea>
                                    </td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <c:if test="${wfhrsystemuseprocessEntity.kfgidea!=null&&wfhrsystemuseprocessEntity.kfgidea!='' &&(nodeOrder ge 11)}">
                                    <td width="10%">系統開發工程師評估</td>
                                    <td colspan="9" class="td_style2">
                                        <textarea name="wfhrsystemuseprocess.kfgidea" class="easyui-validatebox"
                                                  style="width:99%;height:40px;" readonly
                                                  rows="3" cols="3">${wfhrsystemuseprocessEntity.kfgidea}</textarea>
                                    </td>
                                </c:if>
                            </c:otherwise>
                        </c:choose>
                        <c:if test="${nodeOrder le 6}">
                            <tr align="center">
                                <td width="10%">
                                    <input type="checkbox" id="agree" name="agree"/>申請單位已閱讀<br>并同意注意事項
                                </td>
                                <td colspan="9" class="td_style2">
                                    <p>
                                        本單位承諾所申請之資料依集團資安要求僅限本次申請之系統應用功能模塊使用，不做他用，并對相關資料的使用安全性負責。<br>
                                        若因系統管理不善導致信息泄露 ，責任人及其主管承擔由此產生的一切責任。<br>
                                        為了保障人事整合資料庫人事信息的安全性，同時為人事整合資料庫系統管理員開通終端賬號，利於人事信息數據使用的日常監管。
                                    </p>
                                </td>
                            </tr>
                        </c:if>
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td colspan="9" width="90%">
						        <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                          style="width:99%;height:60px;"
                                          rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">

                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <c:choose>
                                    <c:when test="${not empty nodeName&&'人資監管窗口' eq nodeName}">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"  perCall="rzjgupdate"
                                                    serialNo="${wfhrsystemuseprocessEntity.serialno}"></fox:action>
                                    </c:when>
                                    <c:when test="${not empty nodeName&&'人資主管1' eq nodeName}">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"  perCall="rzb1update"
                                                    serialNo="${wfhrsystemuseprocessEntity.serialno}"></fox:action>
                                    </c:when>
                                    <c:when test="${not empty nodeName&&'人資主管2' eq nodeName}">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"  perCall="rzb2update"
                                                    serialNo="${wfhrsystemuseprocessEntity.serialno}"></fox:action>
                                    </c:when>
                                    <c:when test="${not empty nodeName&&'系統開發工程師評估' eq nodeName}">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"  perCall="kfgupdate"
                                                    serialNo="${wfhrsystemuseprocessEntity.serialno}"></fox:action>
                                    </c:when>
                                    <c:when test="${nodeOrder le 6}">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"  perCall="dwzgupdate"
                                                    serialNo="${wfhrsystemuseprocessEntity.serialno}"></fox:action>
                                    </c:when>
                                    <c:otherwise>
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                                    serialNo="${wfhrsystemuseprocessEntity.serialno}"></fox:action>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','人力資源處系統應用申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfhrsystemuseprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<input type="hidden" id="nodeName"  value="${nodeName}"/>
<input type="hidden" id="applysourceotherAudit"   value="${wfhrsystemuseprocessEntity.applysourceother}" />
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/humanCapital/wfhrsystemuseprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>