<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>人力資源處系統應用申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfhrsystemuseprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfhrsystemuseprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfhrsystemuseprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">人力資源處系統應用申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfhrsystemuseprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfhrsystemuseprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfhrsystemuseprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfhrsystemuseprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfhrsystemuseprocessEntity.makerno}/${wfhrsystemuseprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">申請系統<br>負責人工號</td>
                            <td width="10%" class="td_style2">${wfhrsystemuseprocessEntity.applyno}</td>
                            <td width="8%">申請系統<br>負責人</td>
                            <td width="10%" class="td_style2">${wfhrsystemuseprocessEntity.applyname}</td>
                            <td width="7%">費用代碼</td>
                            <td width="10%" class="td_style2">${wfhrsystemuseprocessEntity.applycostno}</td>
                            <td width="10%">單位代碼</td>
                            <td width="10%" class="td_style2">${wfhrsystemuseprocessEntity.applydeptno }</td>
                            <td width="10%">廠區</td>
                            <td width="15%" class="td_style1">
                                <input id="applyfactoryid" name="wfhrsystemuseprocess.applyfactoryid"
                                       class="easyui-combobox" disabled
                                       panelHeight="auto" value="${wfhrsystemuseprocessEntity.applyfactoryid }"
                                       data-options="width: 120,required:true,onSelect:function(){onchangeFactory('applyfactoryid');}"/>
                                <input id="applynofactoryid" name="wfhrsystemuseprocess.applynofactoryid" type="hidden"
                                       value="${wfhrsystemuseprocessEntity.applynofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="3" class="td_style2">${wfhrsystemuseprocessEntity.applydeptname }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfhrsystemuseprocessEntity.applyemail }</td>
                            <td>聯繫方式</td>
                            <td class="td_style2">${wfhrsystemuseprocessEntity.applytel}</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">系統應用名稱</td>
                            <td class="td_style2" colspan="4">
                                ${wfhrsystemuseprocessEntity.systemapplyname}
                            </td>
                            <td width="10%">系統應用類別</td>
                            <td class="td_style2" colspan="4">${wfhrsystemuseprocessEntity.systemapplytype }</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">
                                使用者信息
                            </td>
                            <td width="85%" colspan="9">
                                <table width="100%">
                                    <tr align="center">
                                        <td width="15%">工號</td>
                                        <td width="15%">姓名</td>
                                        <td width="20%">綁定電腦IP</td>
                                        <td width="20%">工作崗位</td>
                                    </tr>
                                    <tbody id="info_Body_ip">
                                    <c:if test="${hrsystemuseitems!=null&&hrsystemuseitems.size()>0}">
                                        <c:forEach items="${hrsystemuseitems}" var="itemsEntity" varStatus="status">
                                            <tr id="hrsystemuseitems${status.index}" align="center">
                                                <td>${itemsEntity.userno}</td>
                                                <td>${itemsEntity.username}</td>
                                                <td>${itemsEntity.userip}</td>
                                                <td>${itemsEntity.userjob}</td>
                                            </tr>
                                        </c:forEach>
                                    </c:if>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">申請信息來源</td>
                            <td colspan="9" class="td_style2">
                                ${wfhrsystemuseprocessEntity.applysource}
                                ${wfhrsystemuseprocessEntity.applysourceother}
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">使用目的、範圍<br>及使用信息</td>
                            <td colspan="9" class="td_style2">
                                <textarea name="wfhrsystemuseprocess.applyreason"
                                          class="easyui-validatebox"
                                          style="width:99%;height:80px;"
                                          rows="5" cols="6">${wfhrsystemuseprocessEntity.applyreason }</textarea></td>
                            </td>
                        </tr>
                        <c:if test="${wfhrsystemuseprocessEntity.assessidea!=null&&wfhrsystemuseprocessEntity.assessidea!=''}">
                            <tr>
                                <td colspan="10" class="td_style1">審核主管評估意見</td>
                            </tr>
                        </c:if>
                        <c:if test="${wfhrsystemuseprocessEntity.assessidea!=null&&wfhrsystemuseprocessEntity.assessidea!=''}">
                            <tr align="center">
                                <td width="10%">人資監管窗口評估意見</td>
                                <td colspan="9" class="td_style2">
                                <textarea name="wfhrsystemuseprocess.assessidea"
                                          class="easyui-validatebox"
                                          style="width:99%;height:60px;" data-options="required:true" readonly
                                          rows="2" cols="3">${wfhrsystemuseprocessEntity.assessidea }</textarea>
                                    <br>
                                    <div style="float: left;height: 20px;line-height: 20px">自核准日起使用期限:${wfhrsystemuseprocessEntity.servicelife}</div>
                                </td>
                            </tr>
                        </c:if>
                        <c:if test="${wfhrsystemuseprocessEntity.rzb1idea!=null&&wfhrsystemuseprocessEntity.rzb1idea!=''}">
                            <tr align="center">
                                <td width="10%">人資主管意見</td>
                                <td colspan="9" class="td_style2">
                                    <div style="float: left;height: 20px;line-height: 20px">人資主管1意見</div><br>
                                    <textarea id="rzb1idea" name="wfhrsystemuseprocess.rzb1idea" class="easyui-validatebox"
                                              style="width:99%;height:40px;" readonly
                                              rows="2" cols="2">${wfhrsystemuseprocessEntity.rzb1idea}</textarea>
                                    <c:if test="${wfhrsystemuseprocessEntity.rzb1idea!=null&&wfhrsystemuseprocessEntity.rzb1idea!=''}">
                                    <br>
                                    <div style="float: left;height: 20px;line-height: 20px">人資主管2意見</div><br>
                                    <textarea id="rzb2idea" name="wfhrsystemuseprocess.rzb2idea" class="easyui-validatebox"
                                              style="width:99%;height:40px;" readonly
                                              rows="2" cols="2">${wfhrsystemuseprocessEntity.rzb2idea}</textarea>
                                    </c:if>
                                </td>
                            </tr>
                        </c:if>
                        <c:if test="${wfhrsystemuseprocessEntity.kfgidea!=null&&wfhrsystemuseprocessEntity.kfgidea!=''}">
                            <td width="10%">系統開發工程師評估</td>
                            <td colspan="9" class="td_style2">
                                        <textarea name="wfhrsystemuseprocess.kfgidea" class="easyui-validatebox"
                                                  style="width:99%;height:40px;" readonly
                                                  rows="3" cols="3">${wfhrsystemuseprocessEntity.kfgidea}</textarea>
                            </td>
                        </c:if>
                    <%--<c:if test="${nodeOrder le 6}">
                            <tr align="center">
                                <td width="10%">
                                    <input type="checkbox" id="agree" name="agree" checked="checked"  disabled/>申請單位已閱讀并<br>同意注意事項
                                </td>
                                <td colspan="9" class="td_style2">
                                    <p>
                                        本單位承諾所申請之資料依集團資安要求僅限本次申請之系統應用功能模塊使用，不做他用，并對相關資料的使用安全性負責。<br>
                                        若因系統管理不善導致信息泄露 ，責任人及其主管承擔由此產生的一切責任。
                                    </p>
                                </td>
                            </tr>
                        </c:if>--%>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','人力資源處系統應用申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfhrsystemuseprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfhrsystemuseprocessEntity.workstatus!=null&&wfhrsystemuseprocessEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<input type="hidden" id="nodeName"  value="${nodeName}"/>
<input type="hidden" id="applysourceotherAudit"   value="${wfhrsystemuseprocessEntity.applysourceother}" />
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/humanCapital/wfhrsystemuseprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>