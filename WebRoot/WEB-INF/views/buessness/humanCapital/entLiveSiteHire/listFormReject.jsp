<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>生活服務區場地佔用申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/entlivesitehire/${action}" method="post">
          <!--
		        applyEmpName 活動申請人姓名
    applyTel 聯繫電話
    applyDeptNo 申請人部門代碼
    applyDeptName 申請人部門名稱
    activityDateStart 活動開始時間
    activityDateEnd 活動結束時間
    positionCount 展位個數
    activityCost 費用
    activityPurport 活動主旨
    activityDescribe 場地佈置簡述
    activityOtherLayout 是否改動原有公共實施?是否接電、接水、接網線等？
    kchargeno 課級主管
    kchargename 課級主管
    bchargeno 部級主管
    bchargename 部級主管
    cchargeno 廠級主管
    cchargename 廠級主管
    checkChargeno 運行稽查課
    checkChargename 運行稽查課
    businessManageChargeno 商戶管理課
    businessManageChargename 商戶管理課
    businessServiceChargeno 商業服務部
    businessServiceChargename 商業服務部
    znffChargeno 治安防範課
    znffChargename 治安防範課
    tyzbzcChargeno 太原周邊總處處級
    tyzbzcChargename 太原周邊總處處級
    fyshChargeno 費用審核課
    fyshChargename 費用審核課
    tyzbzcjgcChargeno 太原周邊總處周邊經管處級
    tyzbzcjgcChargename 太原周邊總處周邊經管處級
    wyglcChargeno 物業管理處處級
    wyglcChargename 物業管理處處級
    zbjgcChargeno 周邊經管處處級
    zbjgcChargename 周邊經管處處級
    zbjczhChargeno 敬呈知會
    zbjczhChargename 敬呈知會
    activityDateScope 活動時間範圍
    id 编号
    createBy 創建人
    createTime 創建時間
    updateBy 更新人
    updateTime 更新時間
    remarks 備註
    delFlag 刪除標識（0-正常，1-删除）
    deptId 機構ID
    processId 流程定義標示
    workStatus 審核狀態
    serialno 表單編號
    makerNo 填單人工號
    makerName 填單人姓名
    completTime 完成時間
    attachids 附件ids
    signPerson 當前簽核人
    signNode 當前簽核節點
    makerdeptno 填單人所在部門
    makerfactoryid 填單人廠區Id
    applyDate 申請使用時間
    applyFactory 申請部門（處）
    applyActivityName 活動名稱
    businessType 商戶類型
    applyEmpNo 活動申請人工號
		   -->
    <input id="ids" name="ids" type="hidden" value="${entLiveSiteHireEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${entLiveSiteHireEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${entLiveSiteHireEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${entLiveSiteHireEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${entLiveSiteHireEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${entLiveSiteHireEntity.makerfactoryid }"/>
    <div class="commonW">
    <div class="headTitle">生活服務區場地佔用申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${entLiveSiteHireEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${entLiveSiteHireEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${entLiveSiteHireEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${entLiveSiteHireEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty entLiveSiteHireEntity.makerno}">
			  <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty entLiveSiteHireEntity.makerno}">
                <div class="position_R margin_R">填單人：${entLiveSiteHireEntity.makerno}/${entLiveSiteHireEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table  class="formList">
            <tr>
                <td>
                    <table class="formList">
                    </table>
                </td>
            </tr>
           <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件<font color="red">*</font></td>
                            <td width="90%" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button"
                                                                   value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="uploadFile();" class="ui-input-file"/>
								</span>
								<c:choose>
									<c:when test="${file.size()>0}">
									  <input type="hidden" id="attachids" name="attachids" value="${entLiveSiteHireEntity.attachids }" />
									</c:when>
									<c:otherwise>
										<input type="hidden" id="attachids" name="attachids" value="" />
									</c:otherwise>
								</c:choose>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/newEsign/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="delAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','生活服務區場地佔用申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                <tr>
                <td>
                    <table class="flowList"
                           style="margin-left:5px;margin-top:5px;width:99%">
                                	                       <tr>
                            <td style="border:none">
                                                                    <table width="18%" style="float: left;margin-left: 5px;"  id="ywqchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">資訊運維責任人確認</td>
                                                    <td style="border: none;">
                                                                                                                  <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(100000,'ywqchargeTable','ywqchargeno','ywqchargename',$('#dealfactoryid').val(),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                                                        <tr>
                                        <td><input id="ywqchargeno" name="ywqchargeno"
                                                   class="easyui-validatebox" data-options="width:80,required:${requiredMap['ywqchargeno']}"
                                                   readonly
                                                   value="${entLiveSiteHireEntity.ywqchargeno }"/><c:if test="${requiredMap['ywqchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="ywqchargename" name="ywqchargename"
                                                    readonly class="easyui-validatebox"
                                                    data-options="width:80,required:${requiredMap['ywqchargeno']}"
                                                    value="${entLiveSiteHireEntity.ywqchargename }"/>
                                        </td>
                                    </tr>
                                </table>

                                                                            <table width="18%" style="float: left;margin-left: 5px;"  id="kchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">課級主管</td>
                                                    <td style="border: none;">
                                                                                                                  <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(100000,'kchargeTable','kchargeno','kchargename',$('#dealfactoryid').val(),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                                                        <tr>
                                        <td><input id="kchargeno" name="kchargeno"
                                                   class="easyui-validatebox" data-options="width:80,required:${requiredMap['kchargeno']}"
                                                   readonly
                                                   value="${entLiveSiteHireEntity.kchargeno }"/><c:if test="${requiredMap['kchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="kchargename" name="kchargename"
                                                    readonly class="easyui-validatebox"
                                                    data-options="width:80,required:${requiredMap['kchargeno']}"
                                                    value="${entLiveSiteHireEntity.kchargename }"/>
                                        </td>
                                    </tr>
                                </table>

                                                                            <table width="18%" style="float: left;margin-left: 5px;"  id="bchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">部級主管</td>
                                                    <td style="border: none;">
                                                                                                                  <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(100000,'bchargeTable','bchargeno','bchargename',$('#dealfactoryid').val(),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                                                        <tr>
                                        <td><input id="bchargeno" name="bchargeno"
                                                   class="easyui-validatebox" data-options="width:80,required:${requiredMap['bchargeno']}"
                                                   readonly
                                                   value="${entLiveSiteHireEntity.bchargeno }"/><c:if test="${requiredMap['bchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="bchargename" name="bchargename"
                                                    readonly class="easyui-validatebox"
                                                    data-options="width:80,required:${requiredMap['bchargeno']}"
                                                    value="${entLiveSiteHireEntity.bchargename }"/>
                                        </td>
                                    </tr>
                                </table>

                                                                            <table width="18%" style="float: left;margin-left: 5px;"  id="cchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">廠級主管</td>
                                                    <td style="border: none;">
                                                                                                                  <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(100000,'cchargeTable','cchargeno','cchargename',$('#dealfactoryid').val(),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                                                        <tr>
                                        <td><input id="cchargeno" name="cchargeno"
                                                   class="easyui-validatebox" data-options="width:80,required:${requiredMap['cchargeno']}"
                                                   readonly
                                                   value="${entLiveSiteHireEntity.cchargeno }"/><c:if test="${requiredMap['cchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="cchargename" name="cchargename"
                                                    readonly class="easyui-validatebox"
                                                    data-options="width:80,required:${requiredMap['cchargeno']}"
                                                    value="${entLiveSiteHireEntity.cchargename }"/>
                                        </td>
                                    </tr>
                                </table>

                                                                            <table width="18%" style="float: left;margin-left: 5px;"  id="zachargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">資安人員</td>
                                                    <td style="border: none;">
                                                                                                                  <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(100000,'zachargeTable','zachargeno','zachargename',$('#dealfactoryid').val(),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                                                        <tr>
                                        <td><input id="zachargeno" name="zachargeno"
                                                   class="easyui-validatebox" data-options="width:80,required:${requiredMap['zachargeno']}"
                                                   readonly
                                                   value="${entLiveSiteHireEntity.zachargeno }"/><c:if test="${requiredMap['zachargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="zachargename" name="zachargename"
                                                    readonly class="easyui-validatebox"
                                                    data-options="width:80,required:${requiredMap['zachargeno']}"
                                                    value="${entLiveSiteHireEntity.zachargename }"/>
                                        </td>
                                    </tr>
                                </table>

                                	                       </td>
                        </tr>
                                	                       <tr>
                            <td style="border:none">
                                                                    <table width="18%" style="float: left;margin-left: 5px;"  id="dbazyTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">DBA作業</td>
                                                    <td style="border: none;">
                                                                                                                  <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(100000,'dbazyTable','dbazyno','dbazyname',$('#dealfactoryid').val(),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                                                        <tr>
                                        <td><input id="dbazyno" name="dbazyno"
                                                   class="easyui-validatebox" data-options="width:80,required:${requiredMap['dbazyno']}"
                                                   readonly
                                                   value="${entLiveSiteHireEntity.dbazyno }"/><c:if test="${requiredMap['dbazyno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="dbazyname" name="dbazyname"
                                                    readonly class="easyui-validatebox"
                                                    data-options="width:80,required:${requiredMap['dbazyno']}"
                                                    value="${entLiveSiteHireEntity.dbazyname }"/>
                                        </td>
                                    </tr>
                                </table>

                                	                       </td>
                        </tr>
                            
                    </table>
                </td>
           </tr>
            <tr>
                <td colspan="10" style="text-align:left;">
                    <iframe id="qianheLogFrame" name="qianheLogFrame"
                            src="${ctx}/wfcontroller/goChargeLog?serialNo=${entLiveSiteHireEntity.serialno}"
                            width="100%"></iframe>
                </td>
            </tr>
            <tr>
                <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                    <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                       data-options="iconCls:'icon-add'"
                       style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                    <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                       style="width: 100px;" onclick="canelTask('${entLiveSiteHireEntity.serialno }');">取消申請</a>
                </td>
            </tr>
        </table>
        </td>
            </tr>
        </table>
	</div>
	<input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
    <div id="dlg"></div>
	</form>
  </div>
<script src='${ctx}/static/js/information/entlivesitehire.js?random=<%= Math.random()%>'></script>
</body>
</html>