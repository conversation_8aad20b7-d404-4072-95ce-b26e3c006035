<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>生活服務區場地佔用申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
        var entfrmUrl = "${entfrmUrl}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input type="text" name="filter_EQS_applyEmpNo" class="easyui-validatebox"
               data-options="width:150,prompt: '申請人工號'"/>
        <input type="text" name="filter_EQS_applyDeptNo" class="easyui-validatebox"
               data-options="width:150,prompt: '申請人單位代碼'"/>
        <input type="text" name="filter_EQS_serialno" class="easyui-validatebox"
               data-options="width:150,prompt: '任務編碼'"/>
        <input type="text" name="filter_GED_createTime" class="easyui-my97" datefmt="yyyy-MM-dd"
               data-options="width:150,prompt: '填單开始日期'"/>
        - <input type="text" name="filter_LED_createTime" class="easyui-my97" datefmt="yyyy-MM-dd"
                 data-options="width:150,prompt: '填單结束日期'"/>
        <input type="text" name="filter_GED_completTime" class="easyui-my97" datefmt="yyyy-MM-dd"
               data-options="width:150,prompt: '簽核完成开始日期'"/>
        - <input type="text" name="filter_LED_completTime" class="easyui-my97" datefmt="yyyy-MM-dd"
                 data-options="width:150,prompt: '簽核完成结束日期'"/>
        <input id="qysjzt" style="width:100px" class="easyui-validatebox" name="filter_EQS_workstatus"/>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
           onclick="listSearchReset()">重置</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
           onclick="exportExcel()">导出Excel</a>
        <input type="text" hidden="true" id="test" value="<shiro:principal property="loginName"/>"/>
        <span style="display: none"><shiro:principal property="loginName" /></span>
        <!--導出用，請勿刪除-->
        <input id="page" name="page" type="hidden" value="1"/>
        <input id="rows" name="rows" type="hidden" value="30"/>
    </form>

</div>
<table id="dg"></table>
<div id="dlg"></div>

<script type="text/javascript">
    //創建下拉查詢條件
    $.ajax({
        url: ctx + "/system/dict/getDictByType/audit_status",
        dataType: "json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#qysjzt").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
    $(function () {
        dg = $('#dg').datagrid({
            method: "get",
            url: ctx + '/entlivesitehire/list',
            fit: true,
            fitColumns: true,
            //fitColumns: false,  //如需要底部出現滾動條，修改爲false
            border: false,
            idField: 'id',
            striped: true,
            cache: false,  //关闭AJAX相应的缓存
            pagination: true,
            rownumbers: true,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: true,
            columns: [[
                //{ field: 'applyEmpName', title: '活動申請人姓名',sortable:true,width:100},
                //{ field: 'applyTel', title: '聯繫電話',sortable:true,width:100},
                //{ field: 'applyDeptNo', title: '申請人部門代碼',sortable:true,width:100},
                //{ field: 'applyDeptName', title: '申請人部門名稱',sortable:true,width:100},
                //{ field: 'activityDateStart', title: '活動開始時間',sortable:true,width:100},
                //{ field: 'activityDateEnd', title: '活動結束時間',sortable:true,width:100},
                //{ field: 'positionCount', title: '展位個數',sortable:true,width:100},
                //{ field: 'activityCost', title: '費用',sortable:true,width:100},
                //{ field: 'activityPurport', title: '活動主旨',sortable:true,width:100},
                //{ field: 'activityDescribe', title: '場地佈置簡述',sortable:true,width:100},
                //{ field: 'activityOtherLayout', title: '是否改動原有公共實施?是否接電、接水、接網線等？',sortable:true,width:100},
                //{ field: 'kchargeno', title: '課級主管',sortable:true,width:100},
                //{ field: 'kchargename', title: '課級主管',sortable:true,width:100},
                //{ field: 'bchargeno', title: '部級主管',sortable:true,width:100},
                //{ field: 'bchargename', title: '部級主管',sortable:true,width:100},
                //{ field: 'cchargeno', title: '廠級主管',sortable:true,width:100},
                //{ field: 'cchargename', title: '廠級主管',sortable:true,width:100},
                //{ field: 'checkChargeno', title: '運行稽查課',sortable:true,width:100},
                //{ field: 'checkChargename', title: '運行稽查課',sortable:true,width:100},
                //{ field: 'businessManageChargeno', title: '商戶管理課',sortable:true,width:100},
                //{ field: 'businessManageChargename', title: '商戶管理課',sortable:true,width:100},
                //{ field: 'businessServiceChargeno', title: '商業服務部',sortable:true,width:100},
                //{ field: 'businessServiceChargename', title: '商業服務部',sortable:true,width:100},
                //{ field: 'znffChargeno', title: '治安防範課',sortable:true,width:100},
                //{ field: 'znffChargename', title: '治安防範課',sortable:true,width:100},
                //{ field: 'tyzbzcChargeno', title: '太原周邊總處處級',sortable:true,width:100},
                //{ field: 'tyzbzcChargename', title: '太原周邊總處處級',sortable:true,width:100},
                //{ field: 'fyshChargeno', title: '費用審核課',sortable:true,width:100},
                //{ field: 'fyshChargename', title: '費用審核課',sortable:true,width:100},
                //{ field: 'tyzbzcjgcChargeno', title: '太原周邊總處周邊經管處級',sortable:true,width:100},
                //{ field: 'tyzbzcjgcChargename', title: '太原周邊總處周邊經管處級',sortable:true,width:100},
                //{ field: 'wyglcChargeno', title: '物業管理處處級',sortable:true,width:100},
                //{ field: 'wyglcChargename', title: '物業管理處處級',sortable:true,width:100},
                //{ field: 'zbjgcChargeno', title: '周邊經管處處級',sortable:true,width:100},
                //{ field: 'zbjgcChargename', title: '周邊經管處處級',sortable:true,width:100},
                //{ field: 'zbjczhChargeno', title: '敬呈知會',sortable:true,width:100},
                //{ field: 'zbjczhChargename', title: '敬呈知會',sortable:true,width:100},
                //{ field: 'activityDateScope', title: '活動時間範圍',sortable:true,width:100},
                //{ field: 'makerNo', title: '填單人工號',sortable:true,width:100},
                //{ field: 'makerName', title: '填單人姓名',sortable:true,width:100},
                //{ field: 'completTime', title: '完成時間',sortable:true,width:100},
                //{ field: 'attachids', title: '附件ids',sortable:true,width:100},
                //{ field: 'signPerson', title: '當前簽核人',sortable:true,width:100},
                //{ field: 'signNode', title: '當前簽核節點',sortable:true,width:100},
                //{ field: 'makerdeptno', title: '填單人所在部門',sortable:true,width:100},
                //{ field: 'makerfactoryid', title: '填單人廠區Id',sortable:true,width:100},
                //{ field: 'applyDate', title: '申請使用時間',sortable:true,width:100},
                //{ field: 'applyFactory', title: '申請部門（處）',sortable:true,width:100},
                //{ field: 'applyActivityName', title: '活動名稱',sortable:true,width:100},
                // { field: 'applyEmpName', title: '活動申請人姓名',sortable:true,width:100},
                {field: 'id', title: '编号', hidden: true},
                {field: 'serialno', title: '表單編號', sortable: true, width: 180, formatter: operation},
                { field: 'businessType', title: '商戶類型',sortable:true,width:80},
                { field: 'applyEmpNo', title: '活動申請人',sortable:true,width:100, formatter: operationname},
                { field: 'applyDeptName', title: '申請部門',sortable:true,width:100},
                { field: 'makerno', title: '填單人工號',sortable:true,width:100},
                { field: 'makername', title: '填單人姓名',sortable:true,width:100},
                { field: 'createTime', title: '填单时间',sortable:true,width:100},
                { field: 'workstatus', title: '審核狀態',sortable:true,width:100},
                { field: 'nodeName', title: '當前審核節點', sortable: true, width: 150},
                { field: 'auditUser', title: '當前簽核人', sortable: true, width: 120},
                { field: 'completTime', title: '簽核完成時間', sortable: true, width: 100}
                // ,
                // { field: ' ', title: '修改操作', sortable: true, width: 100, formatter:editForm}
            ]],
            onLoadSuccess: function () {
                $(".easyui-tooltip").tooltip({
                    onShow: function () {
                        $(this).tooltip('tip').css({
                            borderColor: '#000'
                        });
                    }
                });
                //$(this).datagrid("fixRownumber");
            },
            rowStyler: rowStylerFun,
            enableHeaderClickMenu: true,
            enableHeaderContextMenu: true,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });

    });

    //任務編號查看頁面
    function operation(value, row, index) {
        // alert($("#test").val());
        // if(sureIsIEAndLower8()) {
        //     if ((row.workstatus == '草稿') && ($("#test").val() == row.makerno)) {
        //         // return "<a href=\"#\" onclick=\"openInChrome("http://************/caaesign/entLiveSiteHire/EntLiveSiteHireAdd?id=" + row.id + "&appId=57bcf7ea8d6c92412447ade7d0eceb2c&secretKey=45E2BB7BC92A15DF3A6389FFBB728CAA0DEC3C6DC536E6BB3E40F114244CEF783AB5EE1DDED2EC313D83468D6BD01BEF96555A8DB84ADB7D6A8620E86C1BDA2A20C9BB8EE04372200D5666D9C5A9D78B8C51D2ABC832C2DC4D664BE692921E5AAF9EE26DDD74885D76ADCBEED5A0375C931164ACA1009E4E9FC911AADA63DC7F&empNo=" + row.makerno + "')\">" + row.serialno + "</a>";
        //         var addUrl = "http://************/caaesign/entLiveSiteHire/EntLiveSiteHireAdd?id=" + row.id + "&appId=57bcf7ea8d6c92412447ade7d0eceb2c&secretKey=45E2BB7BC92A15DF3A6389FFBB728CAA0DEC3C6DC536E6BB3E40F114244CEF783AB5EE1DDED2EC313D83468D6BD01BEF96555A8DB84ADB7D6A8620E86C1BDA2A20C9BB8EE04372200D5666D9C5A9D78B8C51D2ABC832C2DC4D664BE692921E5AAF9EE26DDD74885D76ADCBEED5A0375C931164ACA1009E4E9FC911AADA63DC7F&empNo=" + $("#test").val();
        //         return "<a href=\"#\" onclick=\"openInChrome('" + addUrl + "')\">" + row.serialno + "</a>";
        //     } else {
        //         // return "<a href=\"#\" onclick=\"openInChrome("http://************/caaesign/entLiveSiteHire/EntLiveSiteHireDetail?id=" + row.id + "&appId=57bcf7ea8d6c92412447ade7d0eceb2c&secretKey=45E2BB7BC92A15DF3A6389FFBB728CAA0DEC3C6DC536E6BB3E40F114244CEF783AB5EE1DDED2EC313D83468D6BD01BEF96555A8DB84ADB7D6A8620E86C1BDA2A20C9BB8EE04372200D5666D9C5A9D78B8C51D2ABC832C2DC4D664BE692921E5AAF9EE26DDD74885D76ADCBEED5A0375C931164ACA1009E4E9FC911AADA63DC7F&empNo=" + row.makerno + "')\">" + value + "</a>";
        //         var addUrl = "http://************/caaesign/entLiveSiteHire/EntLiveSiteHireDetail?id=" + row.id + "&appId=57bcf7ea8d6c92412447ade7d0eceb2c&secretKey=45E2BB7BC92A15DF3A6389FFBB728CAA0DEC3C6DC536E6BB3E40F114244CEF783AB5EE1DDED2EC313D83468D6BD01BEF96555A8DB84ADB7D6A8620E86C1BDA2A20C9BB8EE04372200D5666D9C5A9D78B8C51D2ABC832C2DC4D664BE692921E5AAF9EE26DDD74885D76ADCBEED5A0375C931164ACA1009E4E9FC911AADA63DC7F&empNo=" + $("#test").val();
        //         return "<a href=\"#\" onclick=\"openInChrome('" + addUrl + "')\">" + row.serialno + "</a>";
        //     }
        // }else {
            if ((row.workstatus == '草稿')&&($("#test").val()==row.makerno)) {
                return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('詳情','" + entfrmUrl + "caaesign/entLiveSiteHire/EntLiveSiteHireAdd?id=" + row.id + "&appId=57bcf7ea8d6c92412447ade7d0eceb2c&secretKey=45E2BB7BC92A15DF3A6389FFBB728CAA0DEC3C6DC536E6BB3E40F114244CEF783AB5EE1DDED2EC313D83468D6BD01BEF96555A8DB84ADB7D6A8620E86C1BDA2A20C9BB8EE04372200D5666D9C5A9D78B8C51D2ABC832C2DC4D664BE692921E5AAF9EE26DDD74885D76ADCBEED5A0375C931164ACA1009E4E9FC911AADA63DC7F&empNo=" + $("#test").val() + "','icon-hamburg-basket')\">" + row.serialno + "</a>";
            } else {
                return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('詳情','" + entfrmUrl + "caaesign/entLiveSiteHire/EntLiveSiteHireDetail?id=" + row.id + "&appId=57bcf7ea8d6c92412447ade7d0eceb2c&secretKey=45E2BB7BC92A15DF3A6389FFBB728CAA0DEC3C6DC536E6BB3E40F114244CEF783AB5EE1DDED2EC313D83468D6BD01BEF96555A8DB84ADB7D6A8620E86C1BDA2A20C9BB8EE04372200D5666D9C5A9D78B8C51D2ABC832C2DC4D664BE692921E5AAF9EE26DDD74885D76ADCBEED5A0375C931164ACA1009E4E9FC911AADA63DC7F&empNo=" + $("#test").val() + "','icon-hamburg-basket')\">" + value + "</a>";
            }
        // }
    };
    function openInChrome(url)
    {
        console.log(url);
        // ActiveObject仅在IE下可创建
        var objShell = new ActiveXObject("WScript.Shell");
        // 注意这里是/c，不可使用/k，否则资源不会释放
        var cmd = "cmd.exe /c start chrome " + url;
        objShell.Run(cmd, 0, true);
    }
    function IEVersion() {
        var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
        var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器
        var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器
        var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
        if (isIE) {
            var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
            reIE.test(userAgent);
            var fIEVersion = parseFloat(RegExp["$1"]);
            if (fIEVersion == 7) {
                return 7;
            } else if (fIEVersion == 8) {
                return 8;
            } else if (fIEVersion == 9) {
                return 9;
            } else if (fIEVersion == 10) {
                return 10;
            } else {
                return 6; //IE版本<=7
            }
        } else if (isEdge) {
            return 'edge'; //edge
        } else if (isIE11) {
            return 11; //IE11
        } else {
            return -1; //不是ie浏览器
        }
    }
    var sureIsIEAndLower8 = function() {
        var version = IEVersion();
        if (-1 == version) {
            return false;
        } else if (8 < version || "edge" == version) {
            return false;
        }else{
            return true;
        }
    }
    function operationname(value, row, index){
        return row.applyEmpNo+"/"+row.applyEmpName
    };

    //创建查询对象并查询
    function cx() {
        var obj = $("#searchFrom").serializeObject();
        dg.datagrid('load', obj);
    }

    //表單進度查詢頁面查詢條件重置
    function listSearchReset() {
        $("#searchFrom").form("reset");
    }

    //导出excel
    function exportExcel() {
        var options = $('#dg').datagrid('getPager').data("pagination").options;
        var page = options.pageNumber;//当前页数  
        var total = options.total;
        var rows = options.pageSize;//每页的记录数（行数）  
        var form = document.getElementById("searchFrom");
        $('#page').val(page);
        $('#rows').val(total);
        var form = document.getElementById("searchFrom");
        searchFrom.action = ctx + '/entlivesitehire/exportExcel';
        form.submit();
    }
</script>
</body>
</html>