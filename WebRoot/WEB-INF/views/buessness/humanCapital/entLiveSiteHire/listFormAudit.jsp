<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>生活服務區場地佔用申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/entlivesitehire/${action}" method="post">
       <!--
		               applyEmpName 活動申請人姓名
                   applyTel 聯繫電話
                   applyDeptNo 申請人部門代碼
                   applyDeptName 申請人部門名稱
                   activityDateStart 活動開始時間
                   activityDateEnd 活動結束時間
                   positionCount 展位個數
                   activityCost 費用
                   activityPurport 活動主旨
                   activityDescribe 場地佈置簡述
                   activityOtherLayout 是否改動原有公共實施?是否接電、接水、接網線等？
                   kchargeno 課級主管
                   kchargename 課級主管
                   bchargeno 部級主管
                   bchargename 部級主管
                   cchargeno 廠級主管
                   cchargename 廠級主管
                   checkChargeno 運行稽查課
                   checkChargename 運行稽查課
                   businessManageChargeno 商戶管理課
                   businessManageChargename 商戶管理課
                   businessServiceChargeno 商業服務部
                   businessServiceChargename 商業服務部
                   znffChargeno 治安防範課
                   znffChargename 治安防範課
                   tyzbzcChargeno 太原周邊總處處級
                   tyzbzcChargename 太原周邊總處處級
                   fyshChargeno 費用審核課
                   fyshChargename 費用審核課
                   tyzbzcjgcChargeno 太原周邊總處周邊經管處級
                   tyzbzcjgcChargename 太原周邊總處周邊經管處級
                   wyglcChargeno 物業管理處處級
                   wyglcChargename 物業管理處處級
                   zbjgcChargeno 周邊經管處處級
                   zbjgcChargename 周邊經管處處級
                   zbjczhChargeno 敬呈知會
                   zbjczhChargename 敬呈知會
                   activityDateScope 活動時間範圍
                   id 编号
                   createBy 創建人
                   createTime 創建時間
                   updateBy 更新人
                   updateTime 更新時間
                   remarks 備註
                   delFlag 刪除標識（0-正常，1-删除）
                   deptId 機構ID
                   processId 流程定義標示
                   workStatus 審核狀態
                   serialno 表單編號
                   makerNo 填單人工號
                   makerName 填單人姓名
                   completTime 完成時間
                   attachids 附件ids
                   signPerson 當前簽核人
                   signNode 當前簽核節點
                   makerdeptno 填單人所在部門
                   makerfactoryid 填單人廠區Id
                   applyDate 申請使用時間
                   applyFactory 申請部門（處）
                   applyActivityName 活動名稱
                   businessType 商戶類型
                   applyEmpNo 活動申請人工號
        		   -->
    <input id="ids" name="ids" type="hidden" value="${entLiveSiteHireEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${entLiveSiteHireEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">生活服務區場地佔用申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${entLiveSiteHireEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${entLiveSiteHireEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${entLiveSiteHireEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${entLiveSiteHireEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${entLiveSiteHireEntity.makerno}/${entLiveSiteHireEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
           <tr>
                <td>
                    <table class="formList">
                    </table>
                </td>
           </tr>
            <tr>
                <td>
                     <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${entLiveSiteHireEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/newEsign/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%">
						<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${entLiveSiteHireEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','生活服務區場地佔用申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${entLiveSiteHireEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
  <div id="dlg"></div>
<script src='${ctx}/static/js/information/entlivesitehire.js?random=<%= Math.random()%>'></script>
</body>
</html>