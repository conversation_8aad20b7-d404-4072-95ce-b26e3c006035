<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>員工外住申請及切結書</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
<script src='${ctx}/static/js/humanCapital/tqhwfoutliveprocess.js?random=<%= Math.random()%>'></script>
<style type="text/css">
    .td_style2 {
        text-align: left;
    }
    .td_style3{
        border: none 0px;
    }
</style>
</head>
<body>
<form id="mainform" action="${ctx}/tQhWfoutliveprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhWfoutliveprocess.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${tQhWfoutliveprocess.serialno }"/>
    <div class="commonW">
    <div class="headTitle">員工外住申請及切結書</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${tQhWfoutliveprocess.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${tQhWfoutliveprocess.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${tQhWfoutliveprocess.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${tQhWfoutliveprocess.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${tQhWfoutliveprocess.makerno}/${tQhWfoutliveprocess.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="4%">申請人工號</td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox inputCss" data-options="width: 80,required:true"
                                       value="${tQhWfoutliveprocess.applyno }" readonly/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="applyname" class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${tQhWfoutliveprocess.applyname }"/>
                            </td>
                            <td width="4%">性別</td>
                            <td width="6%" class="td_style1">
                                <input id="applysex" name="applysex" class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${tQhWfoutliveprocess.applysex }"/>
                            </td>
                            <td width="4%">廠區</td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${tQhWfoutliveprocess.applyfactoryid }"
                                       data-options="width: 120" disabled/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位代碼</td>
                            <td class="td_style1">
                                <input id="applydeptno" name="applydeptno" class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${tQhWfoutliveprocess.applydeptno }"/>
                            </td>
                            <td>申請單位</td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox inputCss" data-options="width: 350"
                                       value="${tQhWfoutliveprocess.applydeptname }" readonly/>
                            </td>
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="applyleveltype" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${tQhWfoutliveprocess.applyleveltype }" readonly/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="applymanager" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${tQhWfoutliveprocess.applymanager }" readonly/>
                            </td>
                            <td>身份證號碼</td>
                            <td colspan="3" class="td_style1">
                                <input id="applyidnumber" name="applyidnumber" class="easyui-validatebox inputCss" data-options="width: 350"
                                       value="${tQhWfoutliveprocess.applyidnumber }" readonly/>
                            </td>
                            <td>入集團日期</td>
                            <td class="td_style1">
                                <input id="applyingroup" name="applyingroup"
                                       class="easyui-validatebox inputCss" datefmt="yyyy-MM-dd" readonly
                                       data-options="width: 100"
                                       value="<fmt:formatDate value="${tQhWfoutliveprocess.applyingroup}"/>" />
                            </td>
                        </tr>
                        <tr align="center">
                            <td>入廠日期</td>
                            <td class="td_style1">
                                <input id="applyindate" name="applyindate"
                                       class="easyui-validatebox inputCss" datefmt="yyyy-MM-dd" readonly
                                       data-options="width:100"
                                       value="<fmt:formatDate value="${tQhWfoutliveprocess.applyindate}"/>" />
                            </td>
                            <td>外住地址</td>
                            <td colspan="3" class="td_style1">
                                <input id="liveoutaddress" name="liveoutaddress" class="easyui-validatebox inputCss" data-options="width: 350"
                                       value="${tQhWfoutliveprocess.liveoutaddress }" readonly/>
                            </td>
                            <td>聯繫方式</td>
                            <td class="td_style1">
                                <input id="applytel" name="applytel"  class="easyui-validatebox inputCss" data-options="width: 120"
                                       value="${tQhWfoutliveprocess.applytel }" readonly/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>類別</td>
                            <td colspan="7" class="td_style2">
                                <div class="liveouttypeDiv"></div>
                                <input id="liveouttype" name="liveouttype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${tQhWfoutliveprocess.liveouttype }"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%" rowspan="2">已婚者<br/>配偶資料</td>
                            <td class="td_style2">
                                <c:if test="${tQhWfoutliveprocess.isgroup!=null&&tQhWfoutliveprocess.isgroup=='N'}">
                                    <input type="checkbox" value="N" id="isgroupn" name="isgroup" checked disabled/>非集團員工
                                </c:if>
                                <c:if test="${tQhWfoutliveprocess.isgroup!=null&&tQhWfoutliveprocess.isgroup=='Y'}">
                                    <input type="checkbox" value="N" id="isgroupn" name="isgroup" disabled/>非集團員工
                                </c:if>
                                <c:if test="${tQhWfoutliveprocess.isgroup ==null}">
                                    <input type="checkbox" value="N" id="isgroupn" name="isgroup" disabled/>非集團員工
                                </c:if>
                            </td>
                            <td width="6%">姓名</td>
                            <td colspan="3" class="td_style2">
                                <input id="notmatename" name="notmatename" class="easyui-validatebox inputCss"
                                       data-options="width:100" value="${tQhWfoutliveprocess.notmatename }" readonly/>
                            </td>
                            <td>身份證號</td>
                            <td colspan="5" class="td_style2">
                                <input id="notmatepsnid" name="notmatepsnid" class="easyui-validatebox inputCss" data-options="width: 150"
                                       value="${tQhWfoutliveprocess.notmatepsnid }" readonly/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td class="td_style2">
                                <c:if test="${tQhWfoutliveprocess.isgroup!=null&&tQhWfoutliveprocess.isgroup=='Y'}">
                                    <input type="checkbox" value="Y" id="isgroupy" name="isgroup" checked disabled/>集團員工
                                </c:if>
                                <c:if test="${tQhWfoutliveprocess.isgroup!=null&&tQhWfoutliveprocess.isgroup=='N'}">
                                    <input type="checkbox" value="Y" id="isgroupy" name="isgroup" disabled />集團員工
                                </c:if>
                                <c:if test="${tQhWfoutliveprocess.isgroup==null}">
                                    <input type="checkbox" value="Y" id="isgroupy" name="isgroup" disabled />集團員工
                                </c:if>

                            </td>
                            <td>工號</td>
                            <td class="td_style2">
                                <input id="ismateno" name="ismateno" class="easyui-validatebox inputCss" data-options="width: 100"
                                       value="${tQhWfoutliveprocess.ismateno}" readonly/>
                            </td>
                            <td>姓名</td>
                            <td class="td_style2">
                                <input id="ismatename" name="ismatename" class="easyui-validatebox inputCss"
                                       data-options="width:100" value="${tQhWfoutliveprocess.ismatename }" readonly/>
                            </td>
                            <td>身份證號</td>
                            <td class="td_style2">
                                <input id="ismatepsnid" name="ismatepsnid" class="easyui-validatebox inputCss" data-options="width: 150"
                                       value="${tQhWfoutliveprocess.ismatepsnid }" readonly/>
                            </td>
                            <td>事業群</td>
                            <td class="td_style2">
                                <input id="ismatebg" name="ismatebg" class="easyui-combobox"
                                       panelHeight="auto" value="${tQhWfoutliveprocess.ismatebg }"
                                       data-options="width: 100" disabled/>
                            </td>
                            <td>資位</td>
                            <td class="td_style2">
                                <input id="ismateleveltype" name="ismateleveltype" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${tQhWfoutliveprocess.ismateleveltype }" readonly/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                     <table class="formList">
                         <tr align="center">
                             <td width="10%">附件</td>
                             <td width="90%" class="td_style2">
                                 <c:if test="${tQhWfoutliveprocess.attachids!=null}">
                                     <div id="dowloadUrl">
                                         <c:forEach items="${file}" varStatus="i" var="item">
                                             <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                                 <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                             </div>
                                         </c:forEach>
                                     </div>
                                 </c:if>
                                 <c:if test="${tQhWfoutliveprocess.attachids==null}">無</c:if>
                             </td>
                         </tr>
                         <tr>
                             <td colspan="8">
                                 員工外住切結書：<br/>
                                 &nbsp;&nbsp;&nbsp;&nbsp;1、 本人承诺在外住期间自行负责安全事宜，在公司外所发生一切安全事故均与公司无关，公司不承担法律规定之外任何赔偿责任<br/>
                                 &nbsp;&nbsp;&nbsp;&nbsp;2、 本人承诺在外住期间遵守以下之特别约定事项，若有违反，本人自行承担一切后果，与公司无关﹕<br/>
                                 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A、 遵守交通规则，自觉行走人行天桥/人行横道，不随意乱穿马路<br/>
                                 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;B、 不进入非法网吧、发廊、歌舞厅、OK厅、影视厅、酒吧等其它休闲娱乐场所<br/>
                                 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;C、 不到非法诊所就医，不到路边摊点消费<br/>
                                 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;D、 不搭乘非法营运车辆(如摩的、黑巴、黑的等)
                             </td>
                         </tr>
                         <c:if test="${tQhWfoutliveprocess.applyfactoryid!=null&&tQhWfoutliveprocess.applyfactoryid=='IPETY'}">
                             <tr>
                                 <td colspan="8">
                                     <font color="red">★太原廠區同仁請注意：<br/>
                                         &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;申請外住后，公司不再受理申請提供返回公司宿舍租住福利，請慎重選擇是否外住。<br/>
                                         &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;且外住切結書每月15日（含）前簽核完成，當月生效，15日之後簽核完成，次月生效。<br/></font>
                                 </td>
                             </tr>
                         </c:if>
                         <c:choose>
                             <c:when test="${not empty nodeName&&'總務確認退宿' eq nodeName}">
                                 <tr>
                                     <td colspan="8">
                                         <font color="red">總務確認退宿日期</font>
                                     </td>
                                 </tr>
                                 <tr align="center">
                                     <td width="10%">退宿日期<font color="red">*</font></td>
                                     <td width="90%"  class="td_style2">
                                         <input id="zwqdate" name="zwqdate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                                data-options="width: 100"
                                                value="<fmt:formatDate value="${tQhWfoutliveprocess.zwqdate}"/>"/>
                                     </td>
                                 </tr>
                             </c:when>
                             <c:otherwise>
                                 <c:if test="${tQhWfoutliveprocess.zwqdate!=null&&tQhWfoutliveprocess.zwqdate!=''&&(nodeName eq('薪資窗口作業'))}">
                                     <tr>
                                         <td colspan="8">
                                             <font color="red">總務確認退宿日期</font>
                                         </td>
                                     </tr>
                                     <tr align="center">
                                         <td width="10%">退宿日期<font color="red">*</font></td>
                                         <td width="90%"  class="td_style2">
                                             <input id="zwqdate" name="zwqdate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                                    data-options="width: 100"
                                                    value="<fmt:formatDate value="${tQhWfoutliveprocess.zwqdate}"/>" disabled/>
                                         </td>
                                     </tr>
                                 </c:if>
                             </c:otherwise>
                         </c:choose>
                         <c:choose>
                             <c:when test="${not empty nodeName&&'宿管安排退宿' eq nodeName}">
                                 <tr>
                                     <td colspan="8">
                                         <font color="red">宿管確認退宿日期</font>
                                     </td>
                                 </tr>
                                 <tr align="center">
                                     <td width="10%">退宿日期<font color="red">*</font></td>
                                     <td width="90%"  class="td_style2">
                                         <input id="sgqdate" name="sgqdate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                                data-options="width: 100"
                                                value="<fmt:formatDate value="${tQhWfoutliveprocess.sgqdate}"/>"/>
                                     </td>
                                 </tr>
                             </c:when>
                             <c:otherwise>
                                 <c:if test="${tQhWfoutliveprocess.sgqdate!=null&&tQhWfoutliveprocess.sgqdate!='' &&(nodeName eq('物業管理部'))}">
                                 <tr>
                                     <td colspan="8">
                                         <font color="red">宿管確認退宿日期</font>
                                     </td>
                                 </tr>
                                 <tr align="center">
                                     <td width="10%">退宿日期<font color="red">*</font></td>
                                     <td width="90%"  class="td_style2">
                                         <input id="sgqdate" name="sgqdate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                                data-options="width: 100"
                                                value="<fmt:formatDate value="${tQhWfoutliveprocess.sgqdate}"/>" disabled/>
                                     </td>
                                 </tr>
                                 </c:if>
                             </c:otherwise>
                         </c:choose>
                         <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%"  class="td_style2">
						         <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;" rows="4" cols="4" data-options="required:true"></textarea>
                            </td>
                         </tr>
                         <c:choose>
                             <c:when test="${not empty nodeName&&'總務確認退宿' eq nodeName}">
                                 <tr align="center">
                                     <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                         <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" perCall="zwupdate" serialNo="${tQhWfoutliveprocess.serialno}"></fox:action>
                                     </td>
                                 </tr>
                             </c:when>
                             <c:when test="${not empty nodeName&&'宿管安排退宿' eq nodeName}">
                                 <tr align="center">
                                     <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                         <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" perCall="sgupdate" serialNo="${tQhWfoutliveprocess.serialno}"></fox:action>
                                     </td>
                                 </tr>
                             </c:when>
                             <c:otherwise>
                                 <tr align="center">
                                     <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                         <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" serialNo="${tQhWfoutliveprocess.serialno}"></fox:action>
                                     </td>
                                 </tr>
                             </c:otherwise>
                         </c:choose>

                         <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','員工外住申請及切結書');">點擊查看簽核流程圖</a>
                            </th>
                         </tr>
                         <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                         </tr>

                         <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhWfoutliveprocess.serialno}" width="100%"></iframe>
                            </td>
                         </tr>
                     </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
<div id="dlg"></div>
</body>
</html>