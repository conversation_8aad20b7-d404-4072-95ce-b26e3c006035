<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>員工外住申請及切結書</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
<script src='${ctx}/static/js/humanCapital/tqhwfoutliveprocess.js?random=<%= Math.random()%>'></script>
<style type="text/css">
    .td_style2 {
        text-align: left;
    }

</style>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfoutliveprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhWfoutliveprocess.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${tQhWfoutliveprocess.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${tQhWfoutliveprocess.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${tQhWfoutliveprocess.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${tQhWfoutliveprocess.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${tQhWfoutliveprocess.makerfactoryid }"/>
    <div class="commonW">
    <div class="headTitle">員工外住申請及切結書</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${tQhWfoutliveprocess.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${tQhWfoutliveprocess.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${tQhWfoutliveprocess.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${tQhWfoutliveprocess.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty tQhWfoutliveprocess.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty tQhWfoutliveprocess.makerno}">
            <div class="position_R margin_R">填單人：${tQhWfoutliveprocess.makerno}/${tQhWfoutliveprocess.makername}</div>
        </c:if>
        <div class="clear"></div>
		<table class="formList">
		   <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="4%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox" data-options="width: 80,required:true"
                                       value="${tQhWfoutliveprocess.applyno }" onblur="queryUserInfo('applyno');"/>
                            </td>
                            <td width="4%">申請人&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="applyname" class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${tQhWfoutliveprocess.applyname }"/>
                            </td>
                            <td width="4%">性別&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applysex" name="applysex" class="easyui-validatebox inputCss" data-options="width: 90"
                                       value="${tQhWfoutliveprocess.applysex }" readonly/>
                                <input type="hidden" id="sex" name="sex" value="${tQhWfoutliveprocess.sex }"/>
                            </td>
                            <td width="4%">廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${tQhWfoutliveprocess.applyfactoryid }"
                                       data-options="width: 120,required:true,onSelect:function(){onchangeFactory();}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位代碼&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applydeptno" name="applydeptno" class="easyui-validatebox inputCss" data-options="width: 90"
                                       value="${tQhWfoutliveprocess.applydeptno }" readonly/>
                            </td>
                            <td>申請單位&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox" data-options="width: 350"
                                       value="${tQhWfoutliveprocess.applydeptname }"/>
                            </td>
                            <td>資位&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="applyleveltype" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${tQhWfoutliveprocess.applyleveltype }" readonly/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>管理職&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applymanager" name="applymanager" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${tQhWfoutliveprocess.applymanager }" readonly/>
                            </td>
                            <td>身份證號碼&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyidnumber" name="applyidnumber" class="easyui-validatebox" onblur="isCardNo(this)" data-options="width: 350,required:true"
                                       value="${tQhWfoutliveprocess.applyidnumber }"/>
                            </td>
                            <td>入集團日期&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyingroup" name="applyingroup"
                                       class="easyui-validatebox inputCss" datefmt="yyyy-MM-dd" readonly
                                       data-options="width: 100"
                                       value="<fmt:formatDate value="${tQhWfoutliveprocess.applyingroup}"/>" />
                            </td>
                        </tr>
                        <tr align="center">
                            <td>入廠日期&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyindate" name="applyindate"
                                       class="easyui-validatebox inputCss" datefmt="yyyy-MM-dd" readonly
                                       data-options="width: 100"
                                       value="<fmt:formatDate value="${tQhWfoutliveprocess.applyindate}"/>" />
                            </td>
                            <td>外住地址&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="liveoutaddress" name="liveoutaddress" class="easyui-validatebox" data-options="width: 350,required:true,prompt:'__________市__________區__________村/街/道__________號'"
                                       value="${tQhWfoutliveprocess.liveoutaddress }"/>
                            </td>
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applytel" name="applytel" onblur="valdMobilephone(this)" class="easyui-validatebox" data-options="width: 120,required:true,prompt:'請輸入11位手機號碼'"
                                       value="${tQhWfoutliveprocess.applytel }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>類別&nbsp;<font color="red">*</font></td>
                            <td colspan="7" class="td_style2">
                                <div class="liveouttypeDiv"></div>
                                <input id="liveouttype" name="liveouttype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${tQhWfoutliveprocess.liveouttype }"/>
                            </td>
                        </tr>
                    </table>
                </td>
           </tr>
           <tr>
               <td>
                   <table class="formList">
                       <tr align="center">
                           <td width="10%" rowspan="2">已婚者<br/>配偶資料</td>
                           <td class="td_style2">

                               <c:if test="${tQhWfoutliveprocess.isgroup!=null&&tQhWfoutliveprocess.isgroup=='N'}">
                                   <input type="checkbox" value="N" id="isgroupn" name="isgroup" checked onclick="checked_isgroup(this)"/>非集團員工
                               </c:if>
                               <c:if test="${tQhWfoutliveprocess.isgroup!=null&&tQhWfoutliveprocess.isgroup=='Y'}">
                                   <input type="checkbox" value="N" id="isgroupn" name="isgroup" onclick="checked_isgroup(this)"/>非集團員工
                               </c:if>
                               <c:if test="${tQhWfoutliveprocess.isgroup==null}">
                                   <input type="checkbox" value="N" id="isgroupn" name="isgroup" onclick="checked_isgroup(this)"/>非集團員工
                               </c:if>
                           </td>
                           <td width="6%">姓名</td>
                           <td colspan="3" class="td_style2">
                               <input id="notmatename" name="notmatename" class="easyui-validatebox inputCss"
                                      data-options="width:100" value="${tQhWfoutliveprocess.notmatename }"/>
                           </td>
                           <td>身份證號</td>
                           <td colspan="5" class="td_style2">
                               <input id="notmatepsnid" name="notmatepsnid" onblur="isCardNo(this)" class="easyui-validatebox inputCss" data-options="width: 150"
                                      value="${tQhWfoutliveprocess.notmatepsnid }"/>
                           </td>
                       </tr>
                       <tr align="center">
                           <td class="td_style2">
                               <c:if test="${tQhWfoutliveprocess.isgroup!=null&&tQhWfoutliveprocess.isgroup=='Y'}">
                                   <input type="checkbox" value="Y" id="isgroupy" name="isgroup" checked onclick="checked_isgroup(this)"/>集團員工
                               </c:if>
                               <c:if test="${tQhWfoutliveprocess.isgroup!=null&&tQhWfoutliveprocess.isgroup=='N'}">
                                   <input type="checkbox" value="Y" id="isgroupy" name="isgroup" onclick="checked_isgroup(this)"/>集團員工
                               </c:if>
                               <c:if test="${tQhWfoutliveprocess.isgroup==null}">
                                   <input type="checkbox" value="Y" id="isgroupy" name="isgroup" onclick="checked_isgroup(this)"/>集團員工
                               </c:if>
                           </td>
                           <td>工號</td>
                           <td class="td_style2">
                               <input id="ismateno" name="ismateno" class="easyui-validatebox" data-options="width: 100"
                                      value="${tQhWfoutliveprocess.ismateno}" onblur="queryUserInfo('ismateno');"/>
                           </td>
                           <td>姓名</td>
                           <td class="td_style2">
                               <input id="ismatename" name="ismatename" class="easyui-validatebox inputCss"
                                      data-options="width:100" readonly value="${tQhWfoutliveprocess.ismatename }"/>
                           </td>
                           <td>身份證號</td>
                           <td class="td_style2">
                               <input id="ismatepsnid" name="ismatepsnid" class="easyui-validatebox" onblur="isCardNo(this)" data-options="width: 150"
                                      value="${tQhWfoutliveprocess.ismatepsnid }"/>
                           </td>
                           <td>事業群</td>
                           <td class="td_style2">
                               <input id="ismatebg" name="ismatebg" class="easyui-combobox"
                                      panelHeight="auto" value="${tQhWfoutliveprocess.ismatebg }"
                                      data-options="width: 100,required:true"/>
                           </td>
                           <td>資位</td>
                           <td class="td_style2">
                               <input id="ismateleveltype" name="ismateleveltype" class="easyui-validatebox inputCss" data-options="width: 80"
                                      value="${tQhWfoutliveprocess.ismateleveltype }" readonly/>
                           </td>
                       </tr>
                   </table>
               </td>
           </tr>
		   <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="attachids" value="${tQhWfoutliveprocess.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8">
                                員工外住切結書：<br/>
                                &nbsp;&nbsp;&nbsp;&nbsp;1、 本人承诺在外住期间自行负责安全事宜，在公司外所发生一切安全事故均与公司无关，公司不承担法律规定之外任何赔偿责任<br/>
                                &nbsp;&nbsp;&nbsp;&nbsp;2、 本人承诺在外住期间遵守以下之特别约定事项，若有违反，本人自行承担一切后果，与公司无关﹕<br/>
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A、 遵守交通规则，自觉行走人行天桥/人行横道，不随意乱穿马路<br/>
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;B、 不进入非法网吧、发廊、歌舞厅、OK厅、影视厅、酒吧等其它休闲娱乐场所<br/>
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;C、 不到非法诊所就医，不到路边摊点消费<br/>
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;D、 不搭乘非法营运车辆(如摩的、黑巴、黑的等)
                            </td>
                        </tr>
                        <c:if test="${tQhWfoutliveprocess.applyfactoryid!=null&&tQhWfoutliveprocess.applyfactoryid=='IPETY'}">
                            <tr id="tyshow">
                                <td colspan="8">
                                    <font color="red">★太原廠區同仁請注意：<br/>
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;申請外住后，公司不再受理申請提供返回公司宿舍租住福利，請慎重選擇是否外住。<br/>
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;且外住切結書每月15日（含）前簽核完成，當月生效，15日之後簽核完成，次月生效。<br/></font>
                                </td>
                            </tr>
                        </c:if>
                        <c:if test="${tQhWfoutliveprocess.applyfactoryid==null}">
                            <tr id="tyshow">
                                <td colspan="8">
                                    <font color="red">★太原廠區同仁請注意：<br/>
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;申請外住后，公司不再受理申請提供返回公司宿舍租住福利，請慎重選擇是否外住。<br/>
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;且外住切結書每月15日（含）前簽核完成，當月生效，15日之後簽核完成，次月生效。<br/></font>
                                </td>
                            </tr>
                        </c:if>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${workFlowId}','員工外住申請及切結書','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="10" style="text-align:left;">
                    <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                        <tr>
                            <td style="border:none">
                                <table width="18%" style="float: left;margin-left: 5px;" id="hrchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">人資初核</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(35,'hrchargeTable','hrchargeno','hrchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input id="hrchargeno" name="hrchargeno"
                                                   class="easyui-validatebox" data-options="width: 80" readonly
                                                   value="${tQhWfoutliveprocess.hrchargeno }"/>
                                            /<input id="hrchargename" name="hrchargename"
                                                    class="easyui-validatebox" data-options="width: 80" readonly
                                                    value="${tQhWfoutliveprocess.hrchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;" id="zwchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">總務初核</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(36,'zwchargeTable','zwchargeno','zwchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input id="zwchargeno" name="zwchargeno"
                                                   class="easyui-validatebox" data-options="width: 80" readonly
                                                   value="${tQhWfoutliveprocess.zwchargeno }"/>
                                            /<input id="zwchargename" name="zwchargename"
                                                    class="easyui-validatebox" data-options="width: 80" readonly
                                                    value="${tQhWfoutliveprocess.zwchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">課級主管</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input id="kchargeno" name="kchargeno"
                                                   class="easyui-validatebox" data-options="width: 80" readonly
                                                   value="${tQhWfoutliveprocess.kchargeno }"/>
                                            /<input id="kchargename" name="kchargename"
                                                    class="easyui-validatebox" data-options="width: 80" readonly
                                                    value="${tQhWfoutliveprocess.kchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">部級主管</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input id="bchargeno" name="bchargeno"
                                                   class="easyui-validatebox" data-options="width: 80" readonly
                                                   value="${tQhWfoutliveprocess.bchargeno }"/>
                                            /<input id="bchargename" name="bchargename"
                                                    class="easyui-validatebox" data-options="width: 80" readonly
                                                    value="${tQhWfoutliveprocess.bchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">廠級主管</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole($('#applydeptno').val(),'cchargeno','cchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input id="cchargeno" name="cchargeno"
                                                   class="easyui-validatebox" data-options="width: 80" readonly
                                                   value="${tQhWfoutliveprocess.cchargeno }"/>
                                            <font color="red" id="jyccharge">*</font>
                                            /<input id="cchargename" name="cchargename"
                                                    class="easyui-validatebox" data-options="width: 80" readonly
                                                    value="${tQhWfoutliveprocess.cchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td style="border:none">
                                <table width="18%" style="float: left;margin-left: 5px;"  id="zchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">製造(總)處級主管</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole3('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input id="zchargeno" name="zchargeno"
                                                   class="easyui-validatebox" data-options="width: 80" readonly
                                                   value="${tQhWfoutliveprocess.zchargeno }"/>
                                            <font color="red" id="tyzcharge">*</font>
                                            /<input id="zchargename" name="zchargename"
                                                   class="easyui-validatebox" data-options="width: 80" readonly
                                                   value="${tQhWfoutliveprocess.zchargename}"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;" id="zwjchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">總務安排退宿</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(37,'zwjchargeTable','zwjchargeno','zwjchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input id="zwjchargeno" name="zwjchargeno"
                                                   class="easyui-validatebox" data-options="width: 80" readonly
                                                   value="${tQhWfoutliveprocess.zwjchargeno }"/>/
                                            <input id="zwjchargename" name="zwjchargename"
                                                   class="easyui-validatebox" data-options="width: 80" readonly
                                                   value="${tQhWfoutliveprocess.zwjchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;" id="zwqchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%" >
                                                <tr>
                                                    <td style="border: none;text-align: right;">總務確認退宿</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(38,'zwqchargeTable','zwqchargeno','zwqchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input id="zwqchargeno" name="zwqchargeno"
                                                   class="easyui-validatebox" data-options="width: 80" readonly
                                                   value="${tQhWfoutliveprocess.zwqchargeno }"/>/
                                            <input id="zwqchargename" name="zwqchargename"
                                                   class="easyui-validatebox" data-options="width: 80" readonly
                                                   value="${tQhWfoutliveprocess.zwqchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;" id="sgchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%"  >
                                                <tr>
                                                    <td style="border: none;text-align: right;">宿管安排退宿</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(39,'sgchargeTable','sgchargeno','sgchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input id="sgchargeno" name="sgchargeno"
                                                   class="easyui-validatebox" data-options="width: 80" readonly
                                                   value="${tQhWfoutliveprocess.sgchargeno }"/>/
                                            <input id="sgchargename" name="sgchargename"
                                                   class="easyui-validatebox" data-options="width: 80" readonly
                                                   value="${tQhWfoutliveprocess.sgchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;" id="wychargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">物業管理部</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(40,'wychargeTable','wychargeno','wychargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input id="wychargeno" name="wychargeno"
                                                   class="easyui-validatebox" data-options="width: 80" readonly
                                                   value="${tQhWfoutliveprocess.wychargeno }"/>/
                                            <input id="wychargename" name="wychargename"
                                                   class="easyui-validatebox" data-options="width: 80" readonly
                                                   value="${tQhWfoutliveprocess.wychargename }"/>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td style="border:none">
                                <table width="18%" style="float: left;margin-left: 5px;" id="xzchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">薪資窗口作業</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(41,'xzchargeTable','xzchargeno','xzchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input id="xzchargeno" name="xzchargeno"
                                                   class="easyui-validatebox" data-options="width: 80" readonly
                                                   value="${tQhWfoutliveprocess.xzchargeno }"/>/
                                            <input id="xzchargename" name="xzchargename"
                                                   class="easyui-validatebox" data-options="width: 80" readonly
                                                   value="${tQhWfoutliveprocess.xzchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>

                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="10" style="text-align:left;">
                    <table class="flowList" style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                        <tr>
                            <td>簽核時間</td>
                            <td>簽核節點</td>
                            <td>簽核主管</td>
                            <td>簽核意見</td>
                            <td>批註</td>
                            <td>簽核電腦IP</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                    <a href="javascript:;" id="btnSave" class="easyui-linkbutton" data-options="iconCls:'icon-add'"
                       style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                    <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                       style="width: 100px;" onclick="saveInfo(2);">提交</a>
                </td>
            </tr>
        </table>
	</div>
	<input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
	</form>
  </div>
</body>
</html>