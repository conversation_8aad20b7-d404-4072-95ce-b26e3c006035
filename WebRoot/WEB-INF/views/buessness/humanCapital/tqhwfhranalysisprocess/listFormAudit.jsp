<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>人事分析資料需求申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfhranalysisprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhWfhranalysisprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${tQhWfhranalysisprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">人事分析資料需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${tQhWfhranalysisprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${tQhWfhranalysisprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${tQhWfhranalysisprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${tQhWfhranalysisprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${tQhWfhranalysisprocessEntity.makerno}/${tQhWfhranalysisprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">申請人工號</td>
                            <td width="12%" class="td_style2">${tQhWfhranalysisprocessEntity.applyno}</td>
                            <td width="8%">申請人</td>
                            <td width="12%" class="td_style2">${tQhWfhranalysisprocessEntity.applyname }</td>
                            <td width="8%">資位</td>
                            <td width="12%" class="td_style2">${tQhWfhranalysisprocessEntity.applyleveltype }</td>
                            <td width="8%">管理職</td>
                            <td width="12%" class="td_style2">${tQhWfhranalysisprocessEntity.applymanager }</td>
                            <td width="8%">所在廠區</td>
                            <td width="12%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox" data-options="width:100" disabled
                                       value="${tQhWfhranalysisprocessEntity.applyfactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位代碼</td>
                            <td class="td_style2">${tQhWfhranalysisprocessEntity.applydeptno }</td>
                            <td>單位</td>
                            <td colspan="5" class="td_style2">${tQhWfhranalysisprocessEntity.applydeptname }</td>
                            <td>申請日期</td>
                            <td class="td_style1">
                                <input id="applydate" name="applydate" class="easyui-validatebox"  data-options="width:100" readonly
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${tQhWfhranalysisprocessEntity.applydate}"/>"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫方式</td>
                            <td class="td_style2">${tQhWfhranalysisprocessEntity.applytel}</td>
                            <td>聯繫郵箱</td>
                            <td colspan="5" class="td_style2">${tQhWfhranalysisprocessEntity.applyemail }</td>
                            <td>需求日期</td>
                            <td class="td_style1">
                                <input id="expectdate" name="expectdate" class="Wdate" disabled
                                       style="width:100px"  value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${tQhWfhranalysisprocessEntity.expectdate}"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                            </td>
                        </tr>
                        <tr align="center">
                            <td>申請類別</td>
                            <td colspan="9" class="td_style2">
                                <div class="applytypeDiv" style="width: 250px;float: left;"></div>
                                <input id="applytype" name="applytype" type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${tQhWfhranalysisprocessEntity.applytype }"/>
                                <div style="float: left;">
                                    <input id="applytypeother" name="applytypeother" class="easyui-validatebox" data-options="width: 200" readonly="readonly"
                                           value="${tQhWfhranalysisprocessEntity.applytypeother }"/>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>申請內容</td>
                            <td class="td_style1" colspan="9">
                                <textarea style="width:99%;" rows="7" cols="6" id="applyreason" name="applyreason"
                                          maxlength="300"  data-options="required:true" class="easyui-validatebox">${tQhWfhranalysisprocessEntity.applyreason}</textarea></td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${tQhWfhranalysisprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>批註</td>
                            <td colspan="9" class="td_style1">
						        <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                          style="width:1000px;height:60px;"
                                          rows="4" cols="4"></textarea>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${tQhWfhranalysisprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','人事分析資料需求申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhWfhranalysisprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/humanCapital/tqhwfhranalysisprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>