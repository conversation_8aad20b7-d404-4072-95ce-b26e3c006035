<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>人事分析資料需求申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfhranalysisprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhWfhranalysisprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${tQhWfhranalysisprocessEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${tQhWfhranalysisprocessEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${tQhWfhranalysisprocessEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${tQhWfhranalysisprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden"
           value="${tQhWfhranalysisprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">人事分析資料需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${tQhWfhranalysisprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${tQhWfhranalysisprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${tQhWfhranalysisprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${tQhWfhranalysisprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty tQhWfhranalysisprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty tQhWfhranalysisprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${tQhWfhranalysisprocessEntity.makerno}/${tQhWfhranalysisprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">申請人工號<font color="red">*</font></td>
                            <td width="12%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox" data-options="width: 80,required:true"
                                       value="${tQhWfhranalysisprocessEntity.applyno}" onchange="queryUserInfo('apply');"/>
                            </td>
                            <td width="8%">申請人</td>
                            <td width="12%" class="td_style1">
                                <input id="applyname" name="applyname" class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${tQhWfhranalysisprocessEntity.applyname }"/>
                            </td>
                            <td width="8%">資位</td>
                            <td width="12%" class="td_style1">
                                <input id="applyleveltype" name="applyleveltype" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${tQhWfhranalysisprocessEntity.applyleveltype }" readonly/>
                            </td>
                            <td width="8%">管理職</td>
                            <td width="12%" class="td_style1">
                                <input id="applymanager" name="applymanager" class="easyui-validatebox inputCss" data-options="width:100"
                                       value="${tQhWfhranalysisprocessEntity.applymanager }"/>
                            </td>
                            <td width="8%">所在廠區<font color="red">*</font></td>
                            <td width="12%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox" data-options="width:100,required:true"
                                       value="${tQhWfhranalysisprocessEntity.applyfactoryid }"/>
                                <input id="applynofactoryid" name="applynofactoryid"
                                       value="${tQhWfhranalysisprocessEntity.applynofactoryid}" type="hidden"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位代碼</td>
                            <td class="td_style1">
                                <input id="applydeptno" name="applydeptno" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${tQhWfhranalysisprocessEntity.applydeptno }" readonly/>
                            </td>
                            <td>單位<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox" data-options="width:400,required:true"
                                       value="${tQhWfhranalysisprocessEntity.applydeptname }"/>
                            </td>
                            <td>申請日期<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applydate" name="applydate" class="easyui-validatebox"  data-options="width:100" readonly
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${tQhWfhranalysisprocessEntity.applydate}"/>"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫方式<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applytel" name="applytel" class="easyui-validatebox" style="width:100px;"
                                       value="${tQhWfhranalysisprocessEntity.applytel}" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>聯繫郵箱<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"data-options="width: 250,required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"
                                       value="${tQhWfhranalysisprocessEntity.applyemail }"/>
                            </td>
                            <td>需求日期<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="expectdate" name="expectdate" class="Wdate"
                                       style="width:100px"  value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${tQhWfhranalysisprocessEntity.expectdate}"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-{%d+1}'})" />
                            </td>
                        </tr>
                        <tr align="center">
                            <td>申請類別<font color="red">*</font></td>
                            <td colspan="9" class="td_style2">
                                <div class="applytypeDiv" style="width: 250px;float: left;"></div>
                                <input id="applytype" name="applytype" type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${tQhWfhranalysisprocessEntity.applytype }"/>
                                <div style="float: left;">
                                    <input id="applytypeother" name="applytypeother" class="easyui-validatebox" data-options="width: 200" readonly="readonly"
                                           value="${tQhWfhranalysisprocessEntity.applytypeother }"/>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>申請內容<font color="red">*</font></td>
                            <td class="td_style1" colspan="9">
                                <textarea style="width:99%;" rows="7" cols="6" id="applyreason" name="applyreason"
                                          oninput="return LessThanAuto(this,'txtNum1');"
                                          onchange="return LessThanAuto(this,'txtNum1');"
                                          onpropertychange="return LessThanAuto(this,'txtNum1');" maxlength="300"
                                          data-options="required:true,prompt:'具體需求原因、需求內容、需求時間等'" class="easyui-validatebox">${tQhWfhranalysisprocessEntity.applyreason}</textarea><span id="txtNum1"></span></td>
                        </tr>
                        <tr align="center">
                            <td>附件<font color="red">*</font><br>（需求資料電子檔格式）</td>
                            <td colspan="9" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="attachids" value="${tQhWfhranalysisprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)" onclick="showWfImag('dzqh_renshifenxiziliaoshenqingdan','人事分析資料需求申請單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${tQhWfhranalysisprocessEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${tQhWfhranalysisprocessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${tQhWfhranalysisprocessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${tQhWfhranalysisprocessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'cchargeno','cchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${tQhWfhranalysisprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${tQhWfhranalysisprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${tQhWfhranalysisprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${tQhWfhranalysisprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#applydeptno').val(),'zcchargeno','zcchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${tQhWfhranalysisprocessEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${tQhWfhranalysisprocessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產品處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#applydeptno').val(),'pcchargeno','pcchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${tQhWfhranalysisprocessEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${tQhWfhranalysisprocessEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="rzchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">人資作業窗口</td>
                                                                <td style="border: none;">
                                                                    <%--<div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(27,'rzchargeTable','rzchargeno','rzchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>--%>
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole5('27','rzchargeno','rzchargename','rzychargeno','rzychargename','','',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="rzchargeno" name="rzchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['rzchargeno']}"
                                                               readonly
                                                               value="${tQhWfhranalysisprocessEntity.rzchargeno }"/><c:if
                                                            test="${requiredMap['rzchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="rzchargename" name="rzchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['rzchargeno']}"
                                                                value="${tQhWfhranalysisprocessEntity.rzchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="rzbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">人資主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(18,'rzbchargeTable','rzbchargeno','rzbchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="rzbchargeno" name="rzbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['rzbchargeno']}"
                                                               readonly
                                                               value="${tQhWfhranalysisprocessEntity.rzbchargeno }"/><c:if
                                                            test="${requiredMap['rzbchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="rzbchargename" name="rzbchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['rzbchargeno']}"
                                                                value="${tQhWfhranalysisprocessEntity.rzbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="rzychargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">人資作業</td>
                                                                <td style="border: none;">
                                                                    <%--<div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(100000,'rzychargeTable','rzychargeno','rzychargename',$('#dealfactoryid').val(),null)"></div>--%>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="rzychargeno" name="rzychargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['rzychargeno']}"
                                                               readonly
                                                               value="${tQhWfhranalysisprocessEntity.rzychargeno }"/><c:if
                                                            test="${requiredMap['rzychargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="rzychargename" name="rzychargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['rzychargeno']}"
                                                                value="${tQhWfhranalysisprocessEntity.rzychargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
</form>
</div>
<script src='${ctx}/static/js/humanCapital/tqhwfhranalysisprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>