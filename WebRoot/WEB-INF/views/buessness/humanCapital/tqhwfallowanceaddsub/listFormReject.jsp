<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>津貼加減項申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfallowanceaddsub/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhWfallowanceaddsubEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${tQhWfallowanceaddsubEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${tQhWfallowanceaddsubEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${tQhWfallowanceaddsubEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${tQhWfallowanceaddsubEntity.makerdeptno }"/>
    <div class="commonW">
        <div class="headTitle">津貼加減項申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${tQhWfallowanceaddsubEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${tQhWfallowanceaddsubEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${tQhWfallowanceaddsubEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${tQhWfallowanceaddsubEntity.createtime}' pattern='yyyy-MM-dd HH:mm:ss'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${tQhWfallowanceaddsubEntity.makerno}/${tQhWfallowanceaddsubEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td>承辦人工號&nbsp;<font color="red">*</font></td>
                            <td><input id="dealno" name="dealno"
                                       class="easyui-validatebox" data-options="width:150,required:true"
                                       value="${tQhWfallowanceaddsubEntity.dealno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td>承辦人</td>
                            <td><input id="dealname" name="dealname"
                                       class="easyui-validatebox inputCss" data-options="width: 150" readonly
                                       value="${tQhWfallowanceaddsubEntity.dealname }"/></td>
                            <td>單位代碼</td>
                            <td><input id="dealdeptno" name="dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 80" readonly
                                       value="${tQhWfallowanceaddsubEntity.dealdeptno }"/></td>
                            <td>廠區</td>
                            <td><input id="dealfactoryid" name="dealfactoryid"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${tQhWfallowanceaddsubEntity.dealfactoryid }"
                                       data-options="width: 120,required:true" disabled/>
                                <input id="dealfactoryidForSelect" value="${tQhWfallowanceaddsubEntity.dealfactoryid }" name="dealfactoryidForSelect" type="hidden"/>
                            </td>
                            <td>申請類型&nbsp;<font color="red">*</font></td>
                            <td><input id="applyType" name="applyType"
                                       class="easyui-combobox" readonly
                                       data-options="width: 150,required:true"
                                       value="${tQhWfallowanceaddsubEntity.applyType}"/>
                            </td>
                        </tr>
                        <tr align="center">

                            <td>單位名稱</td>
                            <td colspan="3"><input id="dealdeptname"
                                                   name="dealdeptname" class="easyui-validatebox"
                                                   data-options="width: 350"
                                                   value="${tQhWfallowanceaddsubEntity.dealdeptname }"/></td>
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td><input id="dealtel" name="dealtel"
                                       class="easyui-validatebox" data-options="width: 100,required:true,prompt:'579+66666'"
                                       value="${tQhWfallowanceaddsubEntity.dealtel }"/></td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3"><input id="dealemail"
                                                   name="dealemail" class="easyui-validatebox"
                                                   data-options="width: 350,required:true"
                                                   value="${tQhWfallowanceaddsubEntity.dealemail }"/></td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td>申請內容&nbsp;<font color="red">*</font></td>
                            <td align="left" colspan="9"><textarea id="applyContent" name="applyContent"
                                                                   class="easyui-validatebox"
                                                                   oninput="return LessThan(this);"
                                                                   onchange="return LessThan(this);"
                                                                   onpropertychange="return LessThan(this);"
                                                                   maxlength="200" style="width:1000px;height:80px;"
                                                                   rows="5" cols="6"
                                                                   data-options="required:true,validType:'length[0,200]'"
                            >${tQhWfallowanceaddsubEntity.applyContent }</textarea><span id="txtNum"></span></td>
                        </tr>
                        <tr align="center">
                            <td>附件&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button"
                                                                   value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="watermarkUploadFile();" class="ui-input-file"/>
								</span> <input type="hidden" id="attachids"
                                               name="attachids" value="${tQhWfallowanceaddsubEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/newEsign/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="delAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','津貼加減項申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="10" style="text-align:left;">
                    <table class="flowList"
                           style="margin-left:5px;margin-top:5px;width:99%">
                        <tr>
                            <td style="border:none">
                                <table width="18%" style="float: left;margin-left: 5px;" id="rzchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['rzchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon" class="easyui-validatebox"
                                                             data-options="width: 80,required:true"
                                                             onclick="selectRole4(27,'rzchargeno','rzchargename',$('#dealfactoryidForSelect').val())"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="rzchargeno" name="rzchargeno"
                                                   class="easyui-validatebox" data-options="width:80,required:true"
                                                   readonly
                                                   value="${tQhWfallowanceaddsubEntity.rzchargeno }"/><font color="red">*</font>
                                            /<input id="rzchargename" name="rzchargename"
                                                    readonly class="easyui-validatebox"
                                                    data-options="width:80,required:true"
                                                    value="${tQhWfallowanceaddsubEntity.rzchargename }"/>
                                        </td>
                                    </tr>
                                </table>

                                <table width="18%" style="float: left;margin-left: 5px;">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealfactoryidForSelect').val())"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="kchargeno" name="kchargeno"
                                                   class="easyui-validatebox" data-options="width:80"
                                                   readonly
                                                   value="${tQhWfallowanceaddsubEntity.kchargeno }"/>
                                            /<input id="kchargename" name="kchargename"
                                                    readonly class="easyui-validatebox"
                                                    data-options="width:80"
                                                    value="${tQhWfallowanceaddsubEntity.kchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealfactoryidForSelect').val())"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="bchargeno"
                                                   name="bchargeno"
                                                   class="easyui-validatebox" data-options="width: 80"
                                                   readonly
                                                   value="${tQhWfallowanceaddsubEntity.bchargeno }"/>/<input
                                                id="bchargename" name="bchargename"
                                                class="easyui-validatebox" data-options="width: 80"
                                                readonly
                                                value="${tQhWfallowanceaddsubEntity.bchargename }"/></td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryidForSelect').val())"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="cchargeno"
                                                   name="cchargeno"
                                                   class="easyui-validatebox" data-options="width: 80" readonly
                                                   value="${tQhWfallowanceaddsubEntity.cchargeno }"/> /<input
                                                id="cchargename" name="cchargename"
                                                class="easyui-validatebox" data-options="width: 80" readonly
                                                value="${tQhWfallowanceaddsubEntity.cchargename }"/></td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;"
                                       id="zchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealfactoryidForSelect').val(),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="zchargeno"
                                                   name="zchargeno"
                                                   class="easyui-validatebox" data-options="width: 80"
                                                   readonly
                                                   value="${tQhWfallowanceaddsubEntity.zchargeno }"/>
                                            /<input id="zchargename" name="zchargename"
                                                    class="easyui-validatebox"
                                                    data-options="width: 80"
                                                    readonly
                                                    value="${tQhWfallowanceaddsubEntity.zchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td style="border:none">
                                <table width="18%" style="float: left;margin-left: 5px;"
                                       id="hchargenoTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: center;">${requiredMap['hchargeno_name']} <a
                                                            href="#" onclick="addHq();">添加一位</a></td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="hchargeno" onblur="getUserNameByEmpno(this);"
                                                   name="hchargeno"
                                                   class="easyui-validatebox" data-options="width: 80"
                                                   value="${tQhWfallowanceaddsubEntity.hchargeno }"/>
                                            /<input id="hchargename" name="hchargename"
                                                    class="easyui-validatebox"
                                                    data-options="width: 80"
                                                    readonly
                                                    value="${tQhWfallowanceaddsubEntity.hchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;"
                                       id="zcchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealfactoryidForSelect').val(),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="zcchargeno"
                                                   name="zcchargeno"
                                                   class="easyui-validatebox" data-options="width: 80"
                                                   readonly
                                                   value="${tQhWfallowanceaddsubEntity.zcchargeno }"/>
                                            /<input id="zcchargename" name="zcchargename"
                                                    class="easyui-validatebox"
                                                    data-options="width: 80"
                                                    readonly
                                                    value="${tQhWfallowanceaddsubEntity.zcchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;"
                                       id="pcchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole3('pcchargeTable',$('#dealdeptno').val(),'pcchargeno','pcchargename',$('#dealfactoryidForSelect').val(),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="pcchargeno"
                                                   name="pcchargeno"
                                                   class="easyui-validatebox"
                                                   data-options="width: 80" readonly
                                                   value="${tQhWfallowanceaddsubEntity.pcchargeno }"/>/ <input
                                                id="pcchargename"
                                                name="pcchargename"
                                                class="easyui-validatebox"
                                                data-options="width: 80"
                                                readonly
                                                value="${tQhWfallowanceaddsubEntity.pcchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;"
                                       id="pqchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['pqchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(28,'pqchargeTable','pqchargeno','pqchargename',$('#dealfactoryidForSelect').val(),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="pqchargeno"
                                                   name="pqchargeno"
                                                   class="easyui-validatebox"
                                                   data-options="width: 80" readonly
                                                   value="${tQhWfallowanceaddsubEntity.pqchargeno }"/>/ <input
                                                id="pqchargename"
                                                name="pqchargename"
                                                class="easyui-validatebox"
                                                data-options="width: 80"
                                                readonly
                                                value="${tQhWfallowanceaddsubEntity.pqchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="18%" style="float: left;margin-left: 5px;"
                                       id="caacchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['caachargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(29,'caacchargeTable','caachargeno','caachargename',$('#dealfactoryidForSelect').val(),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="caachargeno"
                                                   name="caachargeno"
                                                   class="easyui-validatebox"
                                                   data-options="width: 80" readonly
                                                   value="${tQhWfallowanceaddsubEntity.caachargeno }"/>
                                            / <input id="caachargename" name="caachargename"
                                                     class="easyui-validatebox" data-options="width: 80" readonly
                                                     value="${tQhWfallowanceaddsubEntity.caachargename }"/></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="10" style="text-align:left;">
                    <iframe id="qianheLogFrame" name="qianheLogFrame"
                            src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhWfallowanceaddsubEntity.serialno}"
                            width="100%"></iframe>
                </td>
            </tr>
            <tr>
                <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                    <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                       data-options="iconCls:'icon-add'"
                       style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                    <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                       style="width: 100px;" onclick="canelTask('${tQhWfallowanceaddsubEntity.serialno }');">取消申請</a>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
</div>
<div id="dlg"></div>
<script src='${ctx}/static/js/humanCapital/tqhwfallowanceaddsub.js?random=<%= Math.random()%>'></script>
<script type="text/javascript">
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            successTip(data, dg, d);
        }
    });
    if ("${tQhWfallowanceaddsubEntity.hchargeno}" != "") {
        var nostr = "${tQhWfallowanceaddsubEntity.hchargeno}";
        var namestr = "${tQhWfallowanceaddsubEntity.hchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hchargenoTable tr:eq(" + (i + 2) + ")").find("#hchargeno").val(notr[i]);
            $("#hchargenoTable tr:eq(" + (i + 2) + ")").find("#hchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hchargenoTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hchargeno' name='hchargeno' style='width: 80px' value='' onblur='getUserNameByEmpno(this);'/>/<input id='hchargename' name='hchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
</script>
</body>
</html>