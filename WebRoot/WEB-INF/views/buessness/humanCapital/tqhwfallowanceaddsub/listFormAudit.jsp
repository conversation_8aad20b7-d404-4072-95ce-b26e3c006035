<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>津貼加減項申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfallowanceaddsub/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tQhWfallowanceaddsubEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${tQhWfallowanceaddsubEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">津貼加減項申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${tQhWfallowanceaddsubEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${tQhWfallowanceaddsubEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${tQhWfallowanceaddsubEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${tQhWfallowanceaddsubEntity.createtime}' pattern='yyyy-MM-dd HH:mm:ss'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${tQhWfallowanceaddsubEntity.makerno}/${tQhWfallowanceaddsubEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td>承辦人工號</td>
                            <td><input id="dealno" name="dealno"
                                       class="easyui-validatebox" data-options="width:150,required:true,disabled:true"
                                       value="${tQhWfallowanceaddsubEntity.dealno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td>承辦人</td>
                            <td><input id="dealname" name="dealname"
                                       class="easyui-validatebox inputCss" data-options="width: 150" readonly
                                       value="${tQhWfallowanceaddsubEntity.dealname }"/></td>
                            <td>單位代碼</td>
                            <td><input id="dealdeptno" name="dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 80" readonly
                                       value="${tQhWfallowanceaddsubEntity.dealdeptno }"/></td>
                            <td>廠區</td>
                            <td><input id="dealfactoryid" name="dealfactoryid"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${tQhWfallowanceaddsubEntity.dealfactoryid }"
                                       data-options="width: 120,required:true" disabled/>
                                <input id="dealfactoryidForSelect" name="dealfactoryidForSelect" type="hidden"/>
                            </td>
                            <td>申請類型</td>
                            <td><input id="applyType" name="applyType"
                                       class="easyui-combobox" readonly disabled
                                       data-options="width: 150,required:true"
                                       value="${tQhWfallowanceaddsubEntity.applyType}"/>
                            </td>
                        </tr>
                        <tr align="center">

                            <td>單位名稱</td>
                            <td colspan="3"><input id="dealdeptname" disabled
                                                   name="hurtdeptname" class="easyui-validatebox"
                                                   data-options="width: 350,disabled:true"
                                                   value="${tQhWfallowanceaddsubEntity.dealdeptname }"/></td>
                            <td>聯繫方式</td>
                            <td><input id="dealtel" name="dealtel"
                                       class="easyui-validatebox inputCss" data-options="width: 80" readonly
                                       value="${tQhWfallowanceaddsubEntity.dealtel }"/></td>
                            <td>聯繫郵箱</td>
                            <td colspan="3"><input id="dealemail" disabled
                                                   name="dealemail" class="easyui-validatebox"
                                                   data-options="width: 350,disabled:true"
                                                   value="${tQhWfallowanceaddsubEntity.dealemail }"/></td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td>申請內容</td>
                            <td align="left" colspan="9"><textarea id="applyContent" name="applyContent" disabled
                                                                   style="width:1000px;height:80px;" rows="5" cols="6"
                            >${tQhWfallowanceaddsubEntity.applyContent }</textarea></td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${tQhWfallowanceaddsubEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/newEsign/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>批註</td>
                            <td colspan="9" align="left">
						<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${tQhWfallowanceaddsubEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','津貼加減項申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhWfallowanceaddsubEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
<div id="dlg"></div>
</div>
<script src='${ctx}/static/js/humanCapital/tqhwfallowanceaddsub.js?random=<%= Math.random()%>'></script>
</body>
</html>