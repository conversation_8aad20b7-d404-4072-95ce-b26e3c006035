<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>SUPERNOTES申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfsupernotes/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfSupernotesEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfSupernotesEntity.serialno }"/>
    <input id="applytype" name="applytype" type="hidden" value="${wfSupernotesEntity.applytype }"/>
    <input id="workstatus" name="workstatus" type="hidden" value="${wfSupernotesEntity.workstatus }"/>
    <div class="commonW">
        <div class="headTitle">Super Notes帳號服務申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfSupernotesEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfSupernotesEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfSupernotesEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfSupernotesEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfSupernotesEntity.makerno}/${wfSupernotesEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">承辦人工號</td>
                            <td width="4%" class="td_style1">
                                <input id="dealno" name="dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true,disabled:true"
                                       value="${wfSupernotesEntity.dealno}" onblur="queryUserInfo('deal');"/>
                            </td>
                            <td width="4%">承辦人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfSupernotesEntity.dealname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfSupernotesEntity.dealdeptno }"/>
                            </td>
                            <td width="5%">廠區</td>
                            <td width="5%" class="td_style1">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfSupernotesEntity.dealfactoryid }" disabled
                                       data-options="width: 120,required:true"/>
                            </td>
                            <td width="4%">聯繫分機</td>
                            <td width="6%" class="td_style1">
                                <input id="dealtel" name="dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfSupernotesEntity.dealtel }"
                                       data-options="required:true,prompt:'579+66666',disabled:true"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="5" class="td_style1">
                                <input id="dealdeptname" name="dealdeptname" class="easyui-validatebox"
                                       data-options="width: 410,required:true,disabled:true"
                                       value="${wfSupernotesEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="dealemail" class="easyui-validatebox"
                                       value="${wfSupernotesEntity.dealemail }" style="width:400px;"
                                       data-options="disabled:true"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">郵箱負責人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">郵箱負責人工號</td>
                            <td width="4%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true,disabled:true"
                                       value="${wfSupernotesEntity.applyno}" onblur="queryUserInfo('apply');"/>
                            </td>
                            <td width="4%">郵箱負責人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfSupernotesEntity.applyname}"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfSupernotesEntity.applydeptno }"/>
                            </td>
                            <td width="5%">費用代碼</td>
                            <td width="5%" class="td_style1">
                                <input id="applycostno" name="applycostno"
                                       class="easyui-validatebox" data-options="width: 90,disabled:true"
                                       value="${wfSupernotesEntity.applycostno }"/>
                            </td>
                            <td width="3%">所在廠區</td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfSupernotesEntity.applyfactoryid }"
                                       data-options="width: 120,disabled:true,required:true,onSelect:function(){onchangeFactory();}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="applyleveltype" class="easyui-validatebox inputCss"
                                       data-options="width: 80"
                                       value="${wfSupernotesEntity.applyleveltype }" readonly/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="applymanager" class="easyui-validatebox inputCss"
                                       data-options="width: 80"
                                       value="${wfSupernotesEntity.applymanager }" readonly/>
                            </td>
                            <td>法人</td>
                            <td colspan="3" class="td_style1">
                                <input id="artificialperson" name="artificialperson" class="easyui-validatebox inputCss"
                                       data-options="width: 200"
                                       value="${wfSupernotesEntity.artificialperson }" readonly/>
                            </td>
                            <td>使用區域</td>
                            <td class="td_style1">
                                <input id="applyarea" name="applyarea" class="easyui-combobox"
                                       data-options="width: 70,disabled:true,onSelect:function(){onchangeArea();}"
                                       value="${wfSupernotesEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="applybuilding" class="easyui-combobox"
                                       data-options="width: 70,disabled:true"
                                       value="${wfSupernotesEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="5%">聯繫分機</td>
                            <td width="5%" class="td_style1">
                                <input id="applyphone" name="applyphone" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfSupernotesEntity.applyphone }"
                                       data-options="required:true,prompt:'579+66666',disabled:true"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>手機</td>
                            <td class="td_style1">
                                <input id="mobilePhone" name="mobilePhone" class="easyui-validatebox"
                                       value="${wfSupernotesEntity.mobilePhone }" style="width:200px;"
                                       data-options="disabled:true"/>
                            </td>
                            <td>單位</td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox"
                                       data-options="width: 410,required:true,disabled:true"
                                       value="${wfSupernotesEntity.applydeptname }"/>
                            </td>
                            <td>是否FII</td>
                            <td class="td_style2">
                                <div class="isfiiDiv"></div>
                                <input id="isfiiValue" name="isfiiValue"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100,disabled:true"
                                       value="${wfSupernotesEntity.isfii }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>需作業郵箱</td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       oninput="onApplyMailChanged()" onpropertychange="onApplyMailChanged()"
                                       value="${wfSupernotesEntity.applyemail }" style="width:300px;"
                                       data-options="required:true,disabled:true"/>
                            </td>
                            <td>外部郵箱地址</td>
                            <td class="td_style1" colspan="5">
                                <input id="externalmail" name="externalmail" class="easyui-validatebox" readonly
                                       data-options="width: 300,disabled:true" value="${wfSupernotesEntity.externalmail }"/>
                            </td>
                        </tr>
<%--                        <tr align="center">--%>
<%--                            <td>直屬主管工號&nbsp;<font color="red">*</font></td>--%>
<%--                            <td class="td_style1">--%>
<%--                                <input id="directleaderno" name="directleaderno" class="easyui-validatebox"--%>
<%--                                       data-options="width: 80,disabled:true"--%>
<%--                                       value="${wfSupernotesEntity.directleaderno}" onblur="queryUserInfo('directleader');"/>--%>
<%--                            </td>--%>
<%--                            <td>直屬主管姓名</td>--%>
<%--                            <td class="td_style1">--%>
<%--                                <input id="directleadername" name="directleadername"--%>
<%--                                       class="easyui-validatebox inputCss"--%>
<%--                                       data-options="width:80,disabled:true" readonly value="${wfSupernotesEntity.directleadername}"/>--%>
<%--                            </td>--%>
<%--                            <td >聯繫方式&nbsp;<font color="red">*</font></td>--%>
<%--                            <td class="td_style1">--%>
<%--                                <input id="directleaderphone" name="directleaderphone" class="easyui-validatebox"--%>
<%--                                       style="width:90px;"--%>
<%--                                       value="${wfSupernotesEntity.directleaderphone }"--%>
<%--                                       data-options="prompt:'579+66666',disabled:true"--%>
<%--                                       onblur="valdApplyTel(this)"/>--%>
<%--                            </td>--%>
<%--                            <td>直屬主管郵箱</td>--%>
<%--                            <td class="td_style1" colspan="5">--%>
<%--                                <input id="directleaderemail" name="directleaderemail" class="easyui-validatebox" readonly--%>
<%--                                       data-options="width: 300" value="${wfSupernotesEntity.directleaderemail }"/>--%>
<%--                            </td>--%>
<%--                        </tr>--%>
<%--                        <tr align="center">--%>
<%--                            <td>直屬主管工號</td>--%>
<%--                            <td class="td_style1">--%>
<%--                                <input id="directleaderno" name="directleaderno" class="easyui-validatebox"--%>
<%--                                       data-options="width: 80,disabled:true"--%>
<%--                                       value="${wfSupernotesEntity.directleaderno}" onblur="queryUserInfo('directleader');"/>--%>
<%--                            </td>--%>
<%--                            <td>直屬主管姓名</td>--%>
<%--                            <td class="td_style1">--%>
<%--                                <input id="directleadername" name="directleadername"--%>
<%--                                       class="easyui-validatebox inputCss"--%>
<%--                                       data-options="width:80,disabled:true" readonly value="${wfSupernotesEntity.directleadername}"/>--%>
<%--                            </td>--%>
<%--                            <td >聯繫方式</td>--%>
<%--                            <td class="td_style1">--%>
<%--                                <input id="directleaderphone" name="directleaderphone" class="easyui-validatebox"--%>
<%--                                       style="width:90px;"--%>
<%--                                       value="${wfSupernotesEntity.directleaderphone }"--%>
<%--                                       data-options="prompt:'579+66666',disabled:true"--%>
<%--                                       onblur="valdApplyTel(this)"/>--%>
<%--                            </td>--%>
<%--                            <td>直屬主管郵箱</td>--%>
<%--                            <td class="td_style1" colspan="5">--%>
<%--                                <input id="directleaderemail" name="directleaderemail" class="easyui-validatebox" readonly--%>
<%--                                       data-options="width: 300" value="${wfSupernotesEntity.directleaderemail }"/>--%>
<%--                            </td>--%>
<%--                        </tr>--%>
                        <tr>
                            <td colspan="10" class="td_style1"><a
                                    href="${ctx}/static/resources/download/SuperNotesApplyStandard.pdf"
                                    id="btnBatchImportTpl" target="_blank">SuperNotes相關申請作業規範</a></td>
                        </tr>
                        <tr>
                            <td colspan="11">
                                <div id="tt" class="easyui-tabs" style="width: 1200px">
                                    <div id="tb" style="height:auto" title="新增"
                                         data-options="refreshable: false,disabled:true">
                                        <table width="100%" class="formList">
                                            <tr>
                                                <td colspan="11">
                                                    申請詳細信息
                                                </td>
                                            </tr>
                                            <tr>
                                                <td width="10%" align="center">
                                                    申請郵箱類型
                                                </td>
                                                <td width="15%" colspan="2">
                                                    <div class="applynotestypeDiv"></div>
                                                    <input id="applynotestypeValue" name="applynotestypeValue"
                                                           type="hidden"
                                                           value="${wfSupernotesEntity.applynotestype }"/>
                                                </td>
                                                <td width="10%" align="center">
                                                    郵箱名稱
                                                </td>
                                                <td width="65%" colspan="7">
                                                    <div style="float: left" id="notesfactoryDiv">
                                                        <input id="notesfactory" name="notesfactory"
                                                               class="easyui-combobox"
                                                               panelHeight="auto"
                                                               value="${wfSupernotesEntity.notesfactory }"
                                                               data-options="width: 100,required:true,disabled:true"/>-
                                                        <input id="notesinfo" name="notesinfo" class="easyui-combobox"
                                                               panelHeight="auto"
                                                               value="${wfSupernotesEntity.notesinfo }"
                                                               data-options="width: 150,required:true,disabled:true"/>-
                                                    </div>
                                                    <div style="float: left">
                                                        <input id="applynotesname" name="applynotesname"
                                                               class="easyui-validatebox"
                                                               data-options="width: 150,required:true,disabled:true"
                                                               value="${wfSupernotesEntity.applynotesname}"/><label
                                                            id="applynotesnameLabel">@mail.foxconn.com</label>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr align="center">
                                                <td width="10%">使用區域</td>
                                                <td width="15%" colspan="2">
                                                    <div class="securityareaDiv"></div>
                                                    <input id="securityareaValue" name="securityareaValue"
                                                           type="hidden" class="easyui-validatebox" data-options="width: 100"
                                                           value="${wfSupernotesEntity.securityarea }"/>
                                                </td>
                                                <td width="5%">NPI群組</td>
                                                <td width="20%" colspan="2">
                                                    <input id="npigroup" name="npigroup"
                                                           class="easyui-combobox"
                                                           panelHeight="auto" disabled
                                                           value="${wfSupernotesEntity.npigroup }"
                                                           data-options="width: 100,required:true,validType:'comboxValidate[\'npigroup\',\'请选择NPI群組\']',novalidate:true"/>
                                                </td>
                                                <td width="10%">發送附件權限</td>
                                                <td width="45%" colspan="4">
                                                    <input id="sendjurisdiction" name="sendjurisdiction"
                                                           class="easyui-combobox"
                                                           panelHeight="auto" disabled
                                                           value="${wfSupernotesEntity.sendjurisdiction }"
                                                           data-options="width: 100,required:true,validType:'comboxValidate[\'sendjurisdiction\',\'请选择發送附件權限\']',novalidate:true"/>
                                                </td>
                                            </tr>
                                            <tr align="center">
                                                <td width="10%">安保區審核主管</td>
                                                <td width="5%">工號</td>
                                                <td width="10%">
                                                    <input id="securitycheckno" name="securitycheckno" class="easyui-validatebox"
                                                           data-options="width: 80,disabled:true"
                                                           value="${wfSupernotesEntity.securitycheckno}" onblur="queryUserInfo('securitycheck');"/>
                                                </td>
                                                <td width="5%">姓名</td>
                                                <td width="10%">
                                                    <input id="securitycheckname" name="securitycheckname"
                                                           class="easyui-validatebox inputCss"
                                                           data-options="width:80" readonly value="${wfSupernotesEntity.securitycheckname}"/>
                                                </td>
                                                <td width="10%">管理職</td>
                                                <td width="10%">
                                                    <input id="securitycheckmanager" name="securitycheckmanager" class="easyui-validatebox inputCss"
                                                           data-options="width: 80"
                                                           value="${wfSupernotesEntity.securitycheckmanager }" readonly/>
                                                </td>
                                                <td width="10%">郵箱地址</td>
                                                <td width="30%" colspan="3">
                                                    <input id="securitycheckemail" name="securitycheckemail" class="easyui-validatebox"
                                                           value="${wfSupernotesEntity.securitycheckemail }" style="width:220px"
                                                           data-options="validType:'email[\'securitycheckemail\',\'郵箱的格式不正確\']',disabled:true"/>
                                                </td>
                                            </tr>
                                            <tr align="center">
                                                <td width="10%">附件/組織內部審核主管郵箱</td>
                                                <td width="5%">工號</td>
                                                <td width="10%">
                                                    <input id="innercheckno" name="innercheckno" class="easyui-validatebox"
                                                           data-options="width: 80,disabled:true"
                                                           value="${wfSupernotesEntity.innercheckno}" onblur="queryUserInfo('innercheck');"/>
                                                </td>
                                                <td width="5%">姓名</td>
                                                <td width="10%">
                                                    <input id="innercheckname" name="innercheckname"
                                                           class="easyui-validatebox inputCss"
                                                           data-options="width:80" readonly value="${wfSupernotesEntity.innercheckname}"/>
                                                </td>
                                                <td width="10%">管理職</td>
                                                <td width="10%">
                                                    <input id="innercheckmanager" name="innercheckmanager" class="easyui-validatebox inputCss"
                                                           data-options="width: 80"
                                                           value="${wfSupernotesEntity.innercheckmanager }" readonly/>
                                                </td>
                                                <td width="10%">郵箱地址</td>
                                                <td width="30%" colspan="3">
                                                    <input id="innercheckemail" name="innercheckemail" class="easyui-validatebox"
                                                           value="${wfSupernotesEntity.innercheckemail }" style="width:220px"
                                                           data-options="validType:'email[\'innercheckemail\',\'郵箱的格式不正確\']',disabled:true"/>
                                                </td>
                                            </tr>
                                            <tr align="center">
                                                <td width="10%">跨組織審核主管郵箱</td>
                                                <td width="5%">工號</td>
                                                <td width="10%">
                                                    <input id="crosscheckno" name="crosscheckno" class="easyui-validatebox"
                                                           data-options="width: 80,disabled:true"
                                                           value="${wfSupernotesEntity.crosscheckno}" onblur="queryUserInfo('crosscheck');"/>
                                                </td>
                                                <td width="5%">姓名</td>
                                                <td width="10%">
                                                    <input id="crosscheckname" name="crosscheckname"
                                                           class="easyui-validatebox inputCss"
                                                           data-options="width:80" readonly value="${wfSupernotesEntity.crosscheckname}"/>
                                                </td>
                                                <td width="10%">管理職</td>
                                                <td width="10%">
                                                    <input id="crosscheckmanager" name="crosscheckmanager" class="easyui-validatebox inputCss"
                                                           data-options="width: 80"
                                                           value="${wfSupernotesEntity.crosscheckmanager }" readonly/>
                                                </td>
                                                <td width="10%">郵箱地址</td>
                                                <td width="30%" colspan="3">
                                                    <input id="crosscheckemail" name="crosscheckemail" class="easyui-validatebox"
                                                           value="${wfSupernotesEntity.crosscheckemail }" style="width:220px"
                                                           data-options="validType:'email[\'crosscheckemail\',\'郵箱的格式不正確\']',disabled:true"/>
                                                </td>
                                            </tr>
                                            <tr align="center">
                                                <td width="10%">關鍵字審核主管郵箱</td>
                                                <td width="15%">工號</td>
                                                <td width="10%">
                                                    <input id="keycheckno" name="keycheckno" class="easyui-validatebox"
                                                           data-options="width: 80,disabled:true"
                                                           value="${wfSupernotesEntity.keycheckno}" onblur="queryUserInfo('keycheck');"/>
                                                </td>
                                                <td width="10%">姓名</td>
                                                <td width="10%">
                                                    <input id="keycheckname" name="keycheckname"
                                                           class="easyui-validatebox inputCss"
                                                           data-options="width:80" readonly value="${wfSupernotesEntity.keycheckname}"/>
                                                </td>
                                                <td width="10%">管理職</td>
                                                <td width="10%">
                                                    <input id="keycheckmanager" name="keycheckmanager" class="easyui-validatebox inputCss"
                                                           data-options="width: 80"
                                                           value="${wfSupernotesEntity.keycheckmanager }" readonly/>
                                                </td>
                                                <td width="10%">郵箱地址</td>
                                                <td width="15%" colspan="3">
                                                    <input id="keycheckemail" name="keycheckemail" class="easyui-validatebox"
                                                           value="${wfSupernotesEntity.keycheckemail }" style="width:220px"
                                                           data-options="validType:'email[\'keycheckemail\',\'郵箱的格式不正確\']',disabled:true"/>
                                                </td>
                                            </tr>
                                            <tr align="center">
                                                <td width="10%">郵箱管控</td>
                                                <td width="25%" colspan="2">
                                                    <div class="controltypeDiv"></div>
                                                    <input id="controltypeValue" name="controltypeValue" type="hidden"
                                                           value="${wfSupernotesEntity.controltype }"/>
                                                </td>
                                                <td width="10%">郵箱配額</td>
                                                <td width="20%"  colspan="2">
                                                    <input id="mailbox"
                                                           name="mailbox"
                                                           class="easyui-combobox" value="${wfSupernotesEntity.mailbox}"
                                                           data-options="panelHeight: 200,disabled:true,width: 60,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/type_mailbox',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; }"/>*50M
                                                </td>
                                                <td width="10%"></td>
                                                <td width="10%">郵箱歸檔配額</td>
                                                <td width="15%" colspan="3">
                                                    <input id="filebox" name="filebox"
                                                           class="easyui-numberbox"
                                                           data-options="width: 80,required:true,min:0,max:5,disabled:true"
                                                           value="${wfSupernotesEntity.filebox}"/>&nbsp;G (不能大於5G)
                                                </td>
                                            </tr>
                                            <tr>
                                                <td width="15%">
                                                    郵箱綁定信息
                                                </td>
                                                <td width="85%" colspan="10">
                                                    <table width="100%">
                                                        <tr align="center">
                                                            <td width="8%">申請人工號</td>
                                                            <td width="8%">申請人</td>
                                                            <td width="10%">綁定類別</td>
                                                            <td width="14%">IP地址/電腦名稱</td>
                                                            <td width="10%">廠區</td>
                                                            <td width="12%">網域名稱</td>
                                                            <td width="14%">郵箱綁定信息</td>
                                                            <td width="10%">是否信箱負責人</td>
                                                            <td width="6%">是否NPI</td>
                                                            <td width="8%">操作</td>
                                                        </tr>
                                                        <tbody id="info_Body_ip">
                                                        <c:if test="${wfSupernotesEntity.itemsEntityList!=null&&wfSupernotesEntity.itemsEntityList.size()>0}">
                                                            <c:forEach items="${wfSupernotesEntity.itemsEntityList}"
                                                                       var="itemsEntity"
                                                                       varStatus="status">
                                                                <tr id="bondedgoodsItem${status.index}" align="center">
                                                                    <td>
                                                                        <input id="notesapplyno${status.index}"
                                                                               name="itemsEntityList[${status.index}].notesapplyno"
                                                                               class="easyui-validatebox"
                                                                               data-options="width: 70,disabled:true"
                                                                               value="${itemsEntity.notesapplyno}"/>
                                                                    </td>
                                                                    <td>
                                                                        <input id="notesapplyname${status.index}"
                                                                               name="itemsEntityList[${status.index}].notesapplyname"
                                                                               class="easyui-validatebox"
                                                                               data-options="width: 70,disabled:true"
                                                                               value="${itemsEntity.notesapplyname}"/>
                                                                    </td>
                                                                    <td>
                                                                        <input id="boundtypeid${status.index}"
                                                                               name="itemsEntityList[${status.index}].boundtypeid"
                                                                               class="easyui-combobox"
                                                                               value="${itemsEntity.boundtypeid}"
                                                                               data-options="panelHeight: 60,disabled:true,width: 80,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/BOUND_TYPE'"/>
                                                                    </td>
                                                                    <td>
                                                                        <input id="ipbind${status.index}"
                                                                               onblur="isNpiCheckUpdate(${status.index});"
                                                                               name="itemsEntityList[${status.index}].ipbind"
                                                                               class="easyui-validatebox"
                                                                               data-options="width: 120,disabled:true,required:true"
                                                                               value="${itemsEntity.ipbind}"/>
                                                                    </td>
                                                                    <td>
                                                                        <input id="superfactoryid${status.index}" name="itemsEntityList[${status.index}].superfactoryid"
                                                                               data-options="disabled:true,panelHeight:'auto',valueField:'value', textField:'label',validType:'comboxValidate[\'superfactoryid${status.index}\',\'请選擇所在廠區\']',onBeforeLoad:function(){loadSuperfactoryid(${status.index});},onSelect:function(){superOnchangeFactory('superfactoryid${status.index}',${status.index});}" style="width:80px;"
                                                                               class="easyui-combobox" editable="false" value="${itemsEntity.superfactoryid}"/>
                                                                    </td>
                                                                    <td>
                                                                        <input id="wyid${status.index}" name="itemsEntityList[${status.index}].wyid"
                                                                               data-options="disabled:true,validType:'comboxValidate[\'wyid${status.index}\',\'请選擇網域名稱\']',onSelect:function(){changeWyid('${status.index}');}" style="width:100px;" panelHeight="auto" class="easyui-combobox" editable="false" value="${itemsEntity.wyid}"/>
                                                                    </td>
                                                                    <td>
                                                                        <input id="superboundinfo${status.index}"
                                                                               name="itemsEntityList[${status.index}].superboundinfo"
                                                                               class="easyui-validatebox"
                                                                               data-options="width: 130,disabled:true," readonly
                                                                               value="${itemsEntity.superboundinfo}"/>
                                                                    </td>
                                                                    <td>
                                                                        <input id="ismailboxperson${status.index}"
                                                                               name="itemsEntityList[${status.index}].ismailboxperson"
                                                                               class="easyui-combobox"
                                                                               value="${itemsEntity.ismailboxperson}"
                                                                               data-options="panelHeight: 60,disabled:true,width: 60,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/dict_supernotesSet'"/>
                                                                    </td>
                                                                    <td>
                                                                        <input id="isnpi${status.index}" disabled
                                                                               name="itemsEntityList[${status.index}].isnpi"
                                                                               class="easyui-combobox"
                                                                               data-options="panelHeight:60,width: 50,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/dict_supernotesSet'"
                                                                               value="${itemsEntity.isnpi}"/>
                                                                    </td>
                                                                    <td align="center">
                                                                        <a href="javascript:void(0)"
                                                                           class="easyui-linkbutton"
                                                                           onclick="addTrInfo();"
                                                                           data-options="plain:true,disabled:true,iconCls:'icon-add'"></a>
                                                                        <a href="javascript:void(0)"
                                                                           class="easyui-linkbutton"
                                                                           onclick="bondedgooddeltr(${status.index});"
                                                                           data-options="plain:true,disabled:true,iconCls:'icon-cancel'"></a>
                                                                    </td>
                                                                </tr>
                                                            </c:forEach>
                                                        </c:if>
                                                        </tbody>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div id="tb1" style="height:auto" title="變更" data-options="refreshable: false">
                                        <table width="100%" class="formList">
                                            <tr>
                                                <td width="15%">
                                                    郵箱類型
                                                </td>
                                                <td width="40%" colspan="4">
                                                    <div class="applynoteschatypeDiv"></div>
                                                    <input id="applynoteschatypeValue" name="applynoteschatypeValue"
                                                           type="hidden"
                                                           value="${wfSupernotesEntity.applynoteschatype}"/>
                                                </td>
                                                <td width="10%">
                                                    郵箱使用區域
                                                </td>
                                                <td width="35%" colspan="3">
                                                    <div class="mailusageareaDiv"></div>
                                                    <input id="mailusageareaValue" name="mailusageareaValue"
                                                           type="hidden" class="easyui-validatebox" data-options="width: 100"
                                                           value="${wfSupernotesEntity.mailusagearea }"/>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td width="15%"><input type="checkbox" name="applynotestypecheck"
                                                                       value="0" disabled
                                                                       onchange="onCheckboxChecked(this);"></input>郵箱更名
                                                </td>
                                                <td colspan="11">
                                                    <table>
                                                        <tr align="center">
                                                            <td width="3%">序號</td>
                                                            <td width="7%">工號</td>
                                                            <td width="7%">姓名</td>
                                                            <td width="5%">資位</td>
                                                            <td width="10%">分機</td>
                                                            <td width="10%">手機</td>
                                                            <td width="7%">費用代碼</td>
                                                            <td width="15%">更名前郵箱地址</td>
                                                            <td width="15%">更名後郵箱地址</td>
                                                            <td width="6%">操作</td>
                                                        </tr>
                                                        <tbody id="info_Body_b">
                                                        <c:if test="${wfSupernotesEntity.itemsEntityChbList!=null&&wfSupernotesEntity.itemsEntityChbList.size()>0}">
                                                        <c:forEach items="${wfSupernotesEntity.itemsEntityChbList}"
                                                                   var="itemsEntity"
                                                                   varStatus="status">
                                                        <tr id="bondedItemChangeb${status.index}"
                                                            align="center">
                                                            <td>${status.index+1}</td>
                                                            <td><input id="applynoChb${status.index}"
                                                                       name="itemsEntityChbList[${status.index}].applyno"
                                                                       class="easyui-validatebox"
                                                                       onblur="queryChange_UserInfo(${status.index});"
                                                                       data-options="width: 70,disabled:true"
                                                                       value="${itemsEntity.applyno}"/></td>
                                                            <td><input id="applynameChb${status.index}"
                                                                       name="itemsEntityChbList[${status.index}].applyname"
                                                                       class="easyui-validatebox"
                                                                       data-options="width: 70,disabled:true"
                                                                       value="${itemsEntity.applyname}"/></td>
                                                            <td><input id="applyleveltypeChb${status.index}"
                                                                       name="itemsEntityChbList[${status.index}].applyleveltype"
                                                                       class="easyui-validatebox"
                                                                       data-options="width: 50,disabled:true"
                                                                       value="${itemsEntity.applyleveltype}"/></td>
                                                            <td><input id="applytelChb${status.index}"
                                                                       name="itemsEntityChbList[${status.index}].applytel"
                                                                       class="easyui-validatebox"
                                                                       data-options="width: 100,disabled:true"
                                                                       value="${itemsEntity.applytel}"/></td>
                                                            <td><input id="applyphoneChb${status.index}"
                                                                       name="itemsEntityChbList[${status.index}].applyphone"
                                                                       class="easyui-validatebox"
                                                                       data-options="width: 100,disabled:true"
                                                                       value="${itemsEntity.applyphone}"/></td>
                                                            <td><input id="applycostnoChb${status.index}"
                                                                       name="itemsEntityChbList[${status.index}].applycostno"
                                                                       class="easyui-validatebox"
                                                                       data-options="width: 70,disabled:true"
                                                                       value="${itemsEntity.applycostno}"/></td>
                                                            <td><input id="originalEmailChb${status.index}"
                                                                       name="itemsEntityChbList[${status.index}].originalEmail"
                                                                       class="easyui-validatebox"
                                                                       data-options="width: 160,disabled:true"
                                                                       value="${itemsEntity.originalEmail}"/></td>
                                                            <td><input id="nowEmailChb${status.index}"
                                                                       name="itemsEntityChbList[${status.index}].nowEmail"
                                                                       class="easyui-validatebox"
                                                                       data-options="width: 160,disabled:true"
                                                                       value="${itemsEntity.nowEmail}"/></td>
                                                            <td align="center">
                                                                <a href="javascript:void(0)"
                                                                   class="easyui-linkbutton"
                                                                   onclick="addTrInfoChange_b();"
                                                                   data-options="plain:true,disabled:true,iconCls:'icon-add'"></a>
                                                                <a href="javascript:void(0)"
                                                                   class="easyui-linkbutton"
                                                                   onclick="bondedgooddeltrChange_b(${status.index});"
                                                                   data-options="plain:true,disabled:true,iconCls:'icon-cancel'"></a>
                                                            </td>
                                                        </tr>
                                                        </c:forEach>
                                                        </c:if>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td width="15%"><input type="checkbox" name="applynotestypecheck"
                                                                       value="1" disabled
                                                                       onchange="onCheckboxChecked(this);"></input>變更郵箱配額
                                                </td>
                                                <td width="9%">原郵箱配額：</td>
                                                <td colspan="3">
                                                    <input id="mailchabox" name="mailchabox"
                                                           class="easyui-numberbox" readonly
                                                           data-options="width: 150,min:0,max:10,disabled:true"
                                                           value="${wfSupernotesEntity.mailchabox}"/>&nbsp;*50M &nbsp;&nbsp;<font
                                                        color="red">(只能填寫2，6和10)</font>
                                                </td>
                                                <td>變更后郵箱配額：</td>
                                                <td colspan="3">
                                                    <input id="newmailbox" name="newmailbox"
                                                           class="easyui-numberbox" readonly
                                                           data-options="width: 150,min:0,max:10,disabled:true"
                                                           value="${wfSupernotesEntity.newmailbox}"/>&nbsp;*50M&nbsp;&nbsp;<font
                                                        color="red">(只能填寫2，6和10)</font>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><input type="checkbox" name="applynotestypecheck" value="2" disabled
                                                           onchange="onCheckboxChecked(this);"></input>變更歸檔配額
                                                </td>
                                                <td>原歸檔配額：</td>
                                                <td colspan="3">
                                                    <input id="filechabox" name="filechabox"
                                                           class="easyui-numberbox" readonly
                                                           data-options="width: 150,min:0,max:5,disabled:true"
                                                           value="${wfSupernotesEntity.filechabox}"/>&nbsp;G
                                                </td>
                                                <td>變更后歸檔配額：</td>
                                                <td colspan="3">
                                                    <input id="newfilebox" name="newfilebox"
                                                           class="easyui-numberbox" readonly
                                                           data-options="width: 150,min:0,max:5,disabled:true"
                                                           value="${wfSupernotesEntity.newfilebox}"/>&nbsp;G
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><input type="checkbox" name="applynotestypecheck" value="3" disabled
                                                           onchange="onCheckboxChecked(this);"></input>遷移
                                                </td>
                                                <td>原廠區：</td>
                                                <td colspan="3">
                                                    <input id="oldfactory" name="oldfactory"
                                                           class="easyui-combobox"
                                                           panelHeight="400"
                                                           value="${wfSupernotesEntity.oldfactory }"
                                                           data-options="width: 120,disabled:true"/>
                                                </td>
                                                <td>目的廠區/BG：</td>
                                                <td colspan="5">
                                                    <input id="newfactory" name="newfactory"
                                                           class="easyui-combobox"
                                                           panelHeight="400"
                                                           data-options="width: 120,disabled:true"
                                                           value="${wfSupernotesEntity.newfactory}"/>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><input type="checkbox" name="applynotestypecheck" value="4" disabled
                                                           onchange="onCheckboxChecked(this);"></input>SuperNotes郵箱密碼重置
                                                </td>
                                                <td>需重置密碼郵箱：</td>
                                                <td colspan="4">
                                                    <input id="faxto" name="faxto"
                                                           class="easyui-validatebox"
                                                           data-options="width: 350,disabled:true,validType:'email[\'faxto\',\'郵箱的格式不正確\']'"
                                                           value="${wfSupernotesEntity.faxto}"/>
                                                </td>
                                                <td>通知郵箱</td>
                                                <td colspan="5">
                                                    <input id="faxtoMail" name="faxtoMail"
                                                           class="easyui-validatebox"
                                                           data-options="width: 350,disabled:true,validType:'email[\'faxtoMail\',\'郵箱的格式不正確\']'"
                                                           value="${wfSupernotesEntity.faxtoMail}"/>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><input type="checkbox" id="applynotestypecheckLose"
                                                           name="applynotestypecheck" value="5" disabled
                                                           onchange="onCheckboxChecked(this);"
                                                           disabled="disabled"></input>Notes損壞/遺失
                                                </td>
                                                <td></td>
                                                <td colspan="3"></td>
                                                <td></td>
                                                <td colspan="3"></td>
                                            </tr>
                                            <tr>
                                                <td><input type="checkbox" id="applynotestypecheckAdd"
                                                           onchange="onCheckboxChecked(this);" disabled
                                                           name="applynotestypecheck" value="7"></input>增加收件人數
                                                </td>
                                                <td colspan="8" class="numberOfRecipientsClass">
                                                    <input name="numberOfRecipients" id="numberOfRecipients" class="easyui-numberbox" value="${wfSupernotesEntity.numberOfRecipients}" data-options="min:30,max:99,disabled:true" readonly />&nbsp;
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><input type="checkbox" id="mailQuotaChangecheck"
                                                           onchange="onCheckboxChecked(this);" disabled
                                                           name="applynotestypecheck" value="8"></input>超大外部郵件發送配額變更
                                                </td>
                                                <td colspan="8" class="mailQuotaChangeClass">
                                                    <input name="mailQuotaChange" id="mailQuotaChange" class="easyui-validatebox" onchange="valdIsNumber(this)" value="${wfSupernotesEntity.mailQuotaChange}" data-options="disabled:true" readonly/>
                                                    <span>&nbsp;M&nbsp;</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td rowspan="5"><input type="checkbox" id="changemailsecurityarea"
                                                                       onchange="onCheckboxChecked(this);" disabled
                                                                       name="applynotestypecheck" value="9"></input>變更郵箱安保區域
                                                </td>
                                                <td>原使用區域</td>
                                                <td colspan="3">
                                                    <input id="oldusagearea" name="oldusagearea"
                                                           class="easyui-combobox"
                                                           panelHeight="auto" disabled
                                                           value="${wfSupernotesEntity.oldusagearea }"
                                                           data-options="width: 100,required:true,validType:'comboxValidate[\'oldusagearea\',\'请选择原使用區域\']',novalidate:true"/>
                                                </td>
                                                <td>變更后使用區域</td>
                                                <td colspan="3">
                                                    <input id="newusagearea" name="newusagearea"
                                                           class="easyui-combobox" disabled
                                                           panelHeight="auto"
                                                           value="${wfSupernotesEntity.newusagearea }"
                                                           data-options="width: 100,required:true,validType:'comboxValidate[\'newusagearea\',\'请选择變更后使用區域\']',novalidate:true,onSelect:function(){onchangeusage();}"/>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>原NPI群組</td>
                                                <td colspan="3">
                                                    <input id="oldnpigroup" name="oldnpigroup"
                                                           class="easyui-combobox"
                                                           panelHeight="auto" disabled
                                                           value="${wfSupernotesEntity.oldnpigroup }"
                                                           data-options="width: 150,required:true,validType:'comboxValidate[\'oldnpigroup\',\'请选择原NPI群組\']',novalidate:true"/>
                                                </td>
                                                <td>變更后NPI群組</td>
                                                <td colspan="3">
                                                    <input id="newnpigroup" name="newnpigroup"
                                                           class="easyui-combobox"
                                                           panelHeight="auto"  disabled
                                                           value="${wfSupernotesEntity.newnpigroup }"
                                                           data-options="width: 150,required:true,validType:'comboxValidate[\'newnpigroup\',\'请选择變更后NPI群組\']',novalidate:true"/>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>原發送附件權限</td>
                                                <td colspan="3">
                                                    <input id="oldsendpermission" name="oldsendpermission"
                                                           class="easyui-combobox"
                                                           panelHeight="auto" disabled
                                                           value="${wfSupernotesEntity.oldsendpermission }"
                                                           data-options="width: 100,required:true,validType:'comboxValidate[\'oldsendpermission\',\'请选择原發送附件權限\']',novalidate:true"/>
                                                </td>
                                                <td>變更后發送附件權限</td>
                                                <td colspan="3">
                                                    <input id="newsendpermission" name="newsendpermission"
                                                           class="easyui-combobox"
                                                           panelHeight="auto" disabled
                                                           value="${wfSupernotesEntity.newsendpermission }"
                                                           data-options="width: 100,required:true,validType:'comboxValidate[\'newsendpermission\',\'请选择變更后發送附件權限\']',novalidate:true"/>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>安保區審核主管</td>
                                                <td colspan="7">
                                                    <div class="securityshtypeDiv"></div>
                                                    <input id="securityshtypeValue" name="securityshtypeValue"
                                                           type="hidden" class="easyui-validatebox" data-options="width: 100"
                                                           value="${wfSupernotesEntity.securityshtype }"/>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>工號</td>
                                                <td width="10%">
                                                    <input id="securityshno" name="securityshno" class="easyui-validatebox"
                                                           data-options="width: 80,disabled:true"
                                                           value="${wfSupernotesEntity.securityshno}" onblur="queryUserInfo('securitysh');"/>
                                                </td>
                                                <td width="10%">姓名</td>
                                                <td width="10%">
                                                    <input id="securityshname" name="securityshname"
                                                           class="easyui-validatebox inputCss"
                                                           data-options="width:80" readonly value="${wfSupernotesEntity.securityshname}"/>
                                                </td>
                                                <td>管理職</td>
                                                <td width="8%">
                                                    <input id="securityshmanager" name="securityshmanager" class="easyui-validatebox inputCss"
                                                           data-options="width: 80"
                                                           value="${wfSupernotesEntity.securityshmanager }" readonly/>
                                                </td>
                                                <td width="8%">郵箱地址</td>
                                                <td width="20%">
                                                    <input id="securityshemail" name="securityshemail" class="easyui-validatebox"
                                                           value="${wfSupernotesEntity.securityshemail }" style="width:200px;"
                                                           data-options="validType:'email[\'securityshemail\',\'郵箱的格式不正確\']',disabled:true"/>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td colspan="9"><input type="checkbox" name="applynotestypecheck"
                                                                       id="deleteApplynoCheck" disabled
                                                                       onchange="onCheckboxChecked(this);"
                                                                       value="6"></input>變更IP或郵箱負責人
                                                </td>
                                            </tr>
                                            <tr id="deleteApplynoId" style="display: none">
                                                <td colspan="10">
                                                    <table width="100%">
                                                        <tr align="center">
                                                            <td width="3%">序號</td>
                                                            <td width="7%">申請人工號</td>
                                                            <td width="7%">申請人</td>
                                                            <td width="8%">綁定類別</td>
                                                            <td width="12%">IP地址/電腦名稱</td>
                                                            <td width="9%">廠區</td>
                                                            <td width="11%">網域名稱</td>
                                                            <td width="12%">邮箱绑定信息</td>
                                                            <td width="9%">是否郵箱負責人</td>
                                                            <td width="6%">是否NPI</td>
                                                            <td width="9%">變更類別</td>
                                                            <td width="8%">操作</td>
                                                        </tr>
                                                        <tbody id="info_Body">
                                                        <c:if test="${wfSupernotesEntity.itemsEntityChaList!=null&&wfSupernotesEntity.itemsEntityChaList.size()>0}">
                                                            <c:forEach items="${wfSupernotesEntity.itemsEntityChaList}"
                                                                       var="itemsEntity" varStatus="status">
                                                                <tr id="bondedgoodsItemChange${status.index}" align="center">
                                                                    <td>
                                                                            ${status.index+1}
                                                                    </td>
                                                                    <td><input id="notesapplynoCha${status.index}"
                                                                               name="itemsEntityChaList[${status.index}].notesapplyno"
                                                                               class="easyui-validatebox"
                                                                               data-options="width: 70,disabled:true"
                                                                               value="${itemsEntity.notesapplyno}"/>
                                                                    </td>
                                                                    <td>
                                                                        <input id="notesapplynameCha${status.index}"
                                                                               name="itemsEntityChaList[${status.index}].notesapplyname"
                                                                               class="easyui-validatebox"
                                                                               data-options="width: 70,disabled:true"
                                                                               value="${itemsEntity.notesapplyname}"/>
                                                                    </td>
                                                                    <td>
                                                                        <input id="boundtypeidCha${status.index}"
                                                                               name="itemsEntityChaList[${status.index}].boundtypeid"
                                                                               class="easyui-combobox"
                                                                               value="${itemsEntity.boundtypeid}"
                                                                               data-options="disabled:true,panelHeight: 60,width: 80,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/BOUND_TYPE',onSelect:function(){changeBoundType('boundtypeid','Cha${status.index}');}"/>
                                                                    </td>
                                                                    <td><input id="ipbindCha${status.index}"
                                                                               onblur="isNpiCheck(this,${status.index});"
                                                                               name="itemsEntityChaList[${status.index}].ipbind"
                                                                               class="easyui-validatebox"
                                                                               data-options="width: 120,disabled:true,validType:'ip'"
                                                                               value="${itemsEntity.ipbind}"/>
                                                                    </td>
                                                                    <td>
                                                                        <input id="superfactoryidCha${status.index}" name="itemsEntityChaList[${status.index}].superfactoryid"
                                                                               data-options="disabled:true,panelHeight:'auto',valueField:'value', textField:'label',validType:'comboxValidate[\'superfactoryidCha${status.index}\',\'请選擇所在廠區\']',onBeforeLoad:function(){loadSuperfactoryid('Cha${status.index}');},onSelect:function(){superOnchangeFactory('superfactoryidCha${status.index}','Cha${status.index}');}" style="width:80px;"
                                                                               class="easyui-combobox" editable="false" value="${itemsEntity.superfactoryid}"/>
                                                                    </td>
                                                                    <td>
                                                                        <input id="wyidCha${status.index}" name="itemsEntityChaList[${status.index}].wyid"
                                                                               data-options="disabled:true,validType:'comboxValidate[\'wyidCha${status.index}\',\'请選擇網域名稱\']',onSelect:function(){changeWyid('Cha${status.index}');}" style="width:100px;" panelHeight="auto" class="easyui-combobox" editable="false" value="${itemsEntity.wyid}"/>
                                                                    </td>
                                                                    <td>
                                                                        <input id="superboundinfoCha${status.index}"
                                                                               name="itemsEntityChaList[${status.index}].superboundinfo"
                                                                               class="easyui-validatebox"
                                                                               data-options="disabled:true,width: 130" readonly
                                                                               value="${itemsEntity.superboundinfo}"/>
                                                                    </td>
                                                                    <td>
                                                                        <input id="ismailboxpersonCha${status.index}"
                                                                               name="itemsEntityChaList[${status.index}].ismailboxperson"
                                                                               class="easyui-combobox"
                                                                               value="${itemsEntity.ismailboxperson}"
                                                                               data-options="panelHeight: 60,disabled:true,width: 60,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/dict_supernotesSet'"/>
                                                                    </td>
                                                                    <td>
                                                                        <input id="isnpiCha${status.index}" disabled
                                                                               name="itemsEntityChaList[${status.index}].isnpi"
                                                                               class="easyui-combobox"
                                                                               data-options="panelHeight: 60,disabled:true,width: 50,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/dict_supernotesSet'"
                                                                               value="${itemsEntity.isnpi}"/>
                                                                    </td>
                                                                    <c:if test="${itemsEntity.exchangetype!=null&&itemsEntity.exchangetype==0}">
                                                                    <td bgcolor="#c7ffc4">
                                                                        <input id="exchangetypeCha${status.index}"
                                                                               name="itemsEntityChaList[${status.index}].exchangetype"
                                                                               class="easyui-combobox"
                                                                               value="${itemsEntity.exchangetype}"
                                                                               data-options="panelHeight: 80,disabled:true,width: 80,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/dict_ismate'"/>
                                                                        </c:if>
                                                                        <c:if test="${itemsEntity.exchangetype!=null&&itemsEntity.exchangetype==1}">
                                                                    <td bgcolor="#d6b5b2">
                                                                        <input id="exchangetypeCha${status.index}"
                                                                               name="itemsEntityChaList[${status.index}].exchangetype"
                                                                               class="easyui-combobox"
                                                                               value="${itemsEntity.exchangetype}"
                                                                               data-options="panelHeight: 80,disabled:true,width: 80,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/dict_ismate'"/>
                                                                        </c:if>
                                                                        <c:if test="${itemsEntity.exchangetype!=null&&itemsEntity.exchangetype==2}">
                                                                    <td>
                                                                        <input id="exchangetypeCha${status.index}"
                                                                               name="itemsEntityChaList[${status.index}].exchangetype"
                                                                               class="easyui-combobox"
                                                                               value="${itemsEntity.exchangetype}"
                                                                               data-options="panelHeight:80,disabled:true,width: 80,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/dict_ismate'"/>
                                                                        </c:if>
                                                                    </td>
                                                                    <td align="center">
                                                                        <a href="javascript:void(0)"
                                                                           class="easyui-linkbutton"
                                                                           onclick="addTrInfoChange();"
                                                                           data-options="plain:true,disabled:true,iconCls:'icon-add'"></a>
                                                                        <a href="javascript:void(0)"
                                                                           class="easyui-linkbutton"
                                                                           onclick="bondedgooddeltrChange(${status.index});"
                                                                           data-options="plain:true,disabled:true,iconCls:'icon-cancel'"></a>
                                                                    </td>
                                                                </tr>
                                                            </c:forEach>
                                                        </c:if>
                                                        </tbody>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><input type="checkbox" name="applynotestypecheck"
                                                           id="changeotherdescribtion" disabled
                                                           onchange="onCheckboxChecked(this);"
                                                           value="10"></input>其他需求

                                                </td>
                                                <td colspan="8">
                                                    <input id="otherdescribtion" name="otherdescribtion"
                                                           class="easyui-validatebox"
                                                           data-options="width:500,disabled:true" readonly value="${wfSupernotesEntity.otherdescribtion}"/>
                                                </td>
                                            </tr>
                                            <tr align="center">
                                                <td width="10%">附件</td>
                                                <td width="90%" class="td_style1" colspan="8">
                                                    <input type="hidden" id="attachids"
                                                           name="attachids" value="${wfSupernotesEntity.attachids }"/>
                                                    <div id="dowloadUrl">
                                                        <c:forEach items="${file}" varStatus="i" var="item">
                                                            <div id="${item.id}"
                                                                 style="line-height:30px;margin-left:5px;" class="float_L">
                                                                <div class="float_L">
                                                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                                                </div>
                                                            </div>
                                                        </c:forEach>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div id="tb2" style="height:auto" title="刪除" data-options="refreshable: false">
                                        <table width="100%">
                                            <tr>
                                                <td width="15%">
                                                    郵箱類型
                                                </td>
                                                <td width="35%">
                                                    <div class="applynotesdeltypeDiv"></div>
                                                    <input id="applynotesdeltypeValue" name="applynotesdeltypeValue"
                                                           type="hidden"
                                                           value="${wfSupernotesEntity.applynotesdeltype }"/>
                                                </td>
                                                <td width="15%">
                                                    郵箱名稱
                                                </td>
                                                <td width="35%">
                                                    <input id="applynotesdelname" name="applynotesdelname"
                                                           class="easyui-validatebox"
                                                           data-options="width: 400,required:true,disabled:true"
                                                           value="${wfSupernotesEntity.applynotesdelname }"/><font
                                                        color="red">*</font>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>需求說明</td>
                            <td colspan="9" class="td_style2" align="left">
                                <textarea id="describtion" name="describtion"
                                          class="easyui-validatebox" disabled
                                          oninput="return LessThan(this);"
                                          onchange="return LessThan(this);"
                                          onpropertychange="return LessThan(this);"
                                          maxlength="300"
                                          style="width:90%;height:80px;"
                                          rows="5" cols="6"
                                          data-options="required:true,disabled:true,validType:'length[0,300]'">${wfSupernotesEntity.describtion }</textarea><span
                                    id="txtNum"></span></td>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style2">
                                <input type="hidden" id="attachids2"
                                       name="attachids2" value="${wfSupernotesEntity.attachids2 }"/>
                                <div id="dowloadUrl2">
                                    <c:forEach items="${file2}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="left">
                            <td width="100%" class="td_style1" colspan="10" id="descId">
                                1、經理級及以上主管，方可申請notes郵箱，郵箱命名均使用小寫字母；</br>
                                2、郵箱配額最大為500M，歸檔配額最高5G；</br>
                                3、非特保區郵箱審核主管限定：關鍵字審核必須為製造處或以上主管，跨事業群必須為副理或以上主管，跨製造處/附件審核必須為課級或郵箱使用人上一級或以上主管；</br>
                                4、特保區郵箱審核主管設定需處級或中心級主管審核；</br>
                                5、新增郵箱需核准至處級以上主管。</br>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','SUPERNOTES申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfSupernotesEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfSupernotesEntity.workstatus!=null&&wfSupernotesEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
<div id="dlg"></div>
<input type="hidden" name="applynotestypecheckId" id="applynotestypecheckId"
       value="${wfSupernotesEntity.applynotestypecheck }">
<input id="disOrEnabled" type="hidden" value="disabled"/>
<input id="disOrEnabledReject" type="hidden" value="disabled"/>
<script src='${ctx}/static/js/humanCapital/wfsupernotes.js?random=<%= Math.random()%>'></script>
</body>
</html>
