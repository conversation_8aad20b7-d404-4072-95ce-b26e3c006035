<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>嘉獎申請表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfcommendprocesses/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfCommendProcessesEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfCommendProcessesEntity.serialno }"/>
    <input id="workFlowId" name="workFlowId" type="hidden" value="${workFlowId }"/>
    <div class="commonW">
        <div class="headTitle">嘉獎申請表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfCommendProcessesEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfCommendProcessesEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfCommendProcessesEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <fmt:formatDate value='${wfCommendProcessesEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：<span style="color:#999;">${wfCommendProcessesEntity.makerno}/${wfCommendProcessesEntity.makername}</span></div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">嘉獎者基本信息</td>
                        </tr>
                        <tbody id="info_Body">
                        <c:if test="${wfCommendProcessesEntity.commendItemEntityList!=null&&wfCommendProcessesEntity.commendItemEntityList.size()>0}">
                            <c:forEach items="${wfCommendProcessesEntity.commendItemEntityList}"
                                       var="itemsEntity"
                                       varStatus="status">
                                <tr align="center">
                                    <input id="workAbility${status.index}" name="commendItemEntityList[${status.index+1}].workAbility" type="hidden" value="${itemsEntity.workAbility }"/>
                                    <td width="10%">工號</td>
                                    <td width="10%" class="td_style1">
                                        <input id="applyno${status.index}"
                                               name="commendItemEntityList[${status.index+1}].applyno"
                                               class="easyui-validatebox"
                                               data-options="width: 80,required:true,disabled:true"
                                               onblur="queryUserInfo(${status.index+1});"
                                               value="${itemsEntity.applyno }"/>
                                    </td>
                                    <td width="10%">姓名</td>
                                    <td width="10%" class="td_style1">
                                        <input id="commendItemEntityList${status.index}"
                                               name="itemsEntity[${status.index+1}].applyname"
                                               value="${itemsEntity.applyname }" class="easyui-validatebox inputCss"
                                               readonly data-options="width:80"/>
                                    </td>
                                    <td width="10%">單位代碼</td>
                                    <td width="10%" class="td_style1">
                                        <input id="deptno${status.index}"
                                               name="commendItemEntityList[${status.index+1}].deptno"
                                               value="${itemsEntity.deptno }" class="easyui-validatebox inputCss"
                                               data-options="width: 90" readonly/>
                                    </td>
                                    <td width="10%">資位</td>
                                    <td width="10%" class="td_style1">
                                        <input id="leveltype${status.index}"
                                               name="commendItemEntityList[${status.index+1}].leveltype"
                                               value="${itemsEntity.leveltype }" class="easyui-validatebox inputCss"
                                               data-options="width: 80" readonly/>
                                    </td>
                                    <td width="10%">管理職</td>
                                    <td width="10%" class="td_style1">
                                        <input id="ismanager${status.index}"
                                               name="commendItemEntityList[${status.index+1}].ismanager"
                                               value="${itemsEntity.ismanager }" class="easyui-validatebox inputCss"
                                               data-options="width: 80" readonly/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>廠區</td>
                                    <td class="td_style1">
                                        <input id="factoryid${status.index}"
                                               name="commendItemEntityList[${status.index+1}].factoryid"
                                               class="easyui-combobox" value="${itemsEntity.factoryid }"
                                               data-options="disabled:true,valueField:'factoryid',textField:'factoryname',url:'${ctx}/tqhfactoryidconfig/allFactorys/',loadFilter: function (data) { data.unshift({ factoryid: '', factoryname: '請選擇廠區' }); return data; }"/>
                                    </td>
                                    <td>單位</td>
                                    <td colspan="3" class="td_style1">
                                        <input id="deptname${status.index}" disabled
                                               name="commendItemEntityList[${status.index+1}].deptname"
                                               value="${itemsEntity.deptname }" class="easyui-validatebox"
                                               style="width:90%;" data-options="required:true,disabled:true"/>
                                    </td>
                                    <td>法人</td>
                                    <td colspan="3">
                                        <c:choose>
                                            <c:when test="${itemsEntity.laypersonname==null || itemsEntity.laypersonname=='' }">
                                                <input id="layperson${status.index+1}"
                                                       name="commendItemEntityList[${status.index+1}].layperson"
                                                       class="easyui-combobox" value="${itemsEntity.layperson }"
                                                       data-options="panelHeight: 400,width: 300,disabled:true,required:true,validType:'comboxValidate[\'layperson${status.index+1}\',\'请选择法人\']',valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/project_entity',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇法人' }); return data; }"/>
                                            </c:when>
                                            <c:otherwise>
                                                <input id="laypersonname${status.index+1}"
                                                       name="commendItemEntityList[${status.index+1}].laypersonname"
                                                       value="${itemsEntity.laypersonname }" class="easyui-validatebox"
                                                       style="width:90%;" data-options="required:true,disabled:true"/>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td colspan="10" align="left">
                                        當年度該員工嘉獎：<input id="commendnum${status.index}"
                                                                name="commendItemEntityList[${status.index+1}].commendnum"
                                                                value="${itemsEntity.commendnum }"
                                                                class="easyui-validatebox"
                                                                style="width:200px;" readonly/>
                                    </td>
                                </tr>
                                <tr id="bondedgoodsItem${status.index+1}">
                                    <input type="hidden" id="shunxu${status.index+1}"
                                           name="commendItemEntityList[${status.index+1}].shunxu"
                                           value="${status.index+1}"/>
                                    <c:if test="${status.index>0}">
                                    <td colspan="9" align="center" bgcolor="#faebd7">以上為第位${status.index+1}位嘉獎人員</td>
                                    <td width="6%" align="center">
                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                               class="deleteBtnStr" disabled
                                               onclick="bondedgooddeltr(${status.index+1});return false;"/>
                                    </td>
                                    </c:if>
                                    <c:if test="${status.index==0}">
                                        <td colspan="10" align="center" bgcolor="#faebd7">以上為第位${status.index+1}位嘉獎人員</td>
                                    </c:if>
                                </tr>
                            </c:forEach>
                        </c:if>
                        </tbody>
                        <tr align="left" class="nottr">
                            <td colspan="10" width="100%" style="text-align:center;padding-left:10px;">
                                <a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-add',text:'添加一筆'" disabled style="width: 100px"></a>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">嘉獎信息</td>
                        </tr>
                        <tr>
                            <td align="center">發生區域</td>
                            <td class="td_style1">
                                <input id="happenarea" name="happenarea" class="easyui-validatebox"
                                       data-options="width: 80,required:true,disabled:true"
                                       value="${wfCommendProcessesEntity.happenarea}"/>
                            </td>
                            <td align="center">嘉獎發生日期</td>
                            <td class="td_style1">
                                <input id="happentime" name="happentime" class="easyui-validatebox Wdate"
                                       data-options="width:150,required:true,disabled:true,prompt:'请选择嘉獎發生日期'"
                                       value="${wfCommendProcessesEntity.happentime}"
                                       onclick="WdatePicker({onpicked:function(){reporttime.click()},doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd',maxDate:'%y-%M-%d %H:%m:%s'})"/>
                            </td>
                            <td align="center">嘉獎通報日期</td>
                            <td class="td_style1">
                                <input id="reporttime" name="reporttime" class="easyui-validatebox Wdate"
                                       data-options="width:150,required:true,disabled:true,prompt:'请选择嘉獎通報日期'"
                                       value="${wfCommendProcessesEntity.reporttime}"
                                       onclick="WdatePicker({skin:'whyGreen',dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'happentime\')}',doubleCalendar:true})"/>
                            </td>
                            <td align="center">嘉獎事由</td>
                            <td class="td_style1" colspan="3">
                                <c:choose>
                                    <c:when test="${not empty nodeName &&('人資獎懲窗口' eq nodeName || '人資獎懲結案窗口' eq nodeName)}">
                                        <input id="reason" name="reason" class="easyui-validatebox"
                                               data-options="width: 300,required:true"
                                               value="${wfCommendProcessesEntity.reason}"/>
                                    </c:when>
                                    <c:otherwise>
                                        <input id="reason" name="reason" class="easyui-validatebox"
                                               data-options="width: 300,required:true,disabled:true"
                                               value="${wfCommendProcessesEntity.reason}"/>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">嘉獎事實描述（可附件）</td>
                        </tr>
                        <c:choose>
                            <c:when test="${not empty nodeName &&'人資獎懲窗口' eq nodeName}">
                                <tr>
                                    <td colspan="8">
                                        <textarea id="reasondesc" name="reasondesc" data-options="required:true"
                                                  style="width:90%;height:80px;" rows="5" cols="6"
                                                  oninput="return LessThanTWO(this);"
                                                  onchange="return LessThanTWO(this);"
                                                  onpropertychange="return LessThanTWO(this);"
                                                  maxlength="500">${wfCommendProcessesEntity.reasondesc }</textarea><span
                                            id="reasondesc500"></span>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td colspan="8" width="100%" class="td_style1">
                                        <span class="sl-custom-file">
                                            <input type="button" value="点击上传文件" class="btn-file"/>
                                            <input id="attachidsUpload" name="attachidsUpload" type="file"
                                                   onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
                                        </span>
                                        <input type="hidden" id="attachids" name="attachids"
                                               value="${wfCommendProcessesEntity.attachids }"/>
                                        <div id="dowloadUrl">
                                            <c:forEach items="${file}" varStatus="i" var="item">
                                                <div id="${item.id}"
                                                     style="line-height:30px;margin-left:5px;" class="float_L">
                                                    <div class="float_L">
                                                        <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                                    </div>
                                                    <div class="float_L deleteBtn"
                                                         onclick="oosDelAtt('${item.id}')"></div>
                                                </div>
                                            </c:forEach>
                                        </div>
                                    </td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <tr>
                                    <td colspan="8">
                                        <textarea id="reasondesc0" name="reasondesc" data-options="required:true"
                                                  style="width:90%;height:80px;" rows="5" cols="6" disabled
                                                  oninput="return LessThanTWO(this);"
                                                  onchange="return LessThanTWO(this);"
                                                  onpropertychange="return LessThanTWO(this);"
                                                  maxlength="500">${wfCommendProcessesEntity.reasondesc }</textarea><span
                                            id="reasondesc50"></span>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td colspan="8" width="100%" class="td_style1">
                                        <div id="dowloadUrl0">
                                            <c:forEach items="${file}" varStatus="i" var="item">
                                                <div id="${item.id}"
                                                     style="line-height:30px;margin-left:5px;" class="float_L">
                                                    <div class="float_L">
                                                        <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                                    </div>
                                                </div>
                                            </c:forEach>
                                        </div>
                                    </td>
                                </tr>
                            </c:otherwise>
                        </c:choose>
                        <tr>
                            <td colspan="8" class="td_style1">符合公司規定及條款</td>
                        </tr>
                        <tbody id="info_Body_one">
                        <tr>
                            <td colspan="1">公司規章：
                            </td>
                            <td colspan="7">
                                <input id="smallType" name="smallType"
                                       class="easyui-combobox" disabled
                                       data-options="width: 500,required:true"
                                       value="${wfCommendProcessesEntity.smallType}"/>
                            </td>
                        </tr>
                        <c:if test="${wfCommendProcessesEntity.commendItemlayEntityList!=null&&wfCommendProcessesEntity.commendItemlayEntityList.size()>0}">
                            <c:forEach items="${wfCommendProcessesEntity.commendItemlayEntityList}" var="itemsEntity"
                                       varStatus="status">
                                <tr id="layEntityList${status.index+1}">
                                    <td colspan="8">
                                        第<input type="text"
                                                 class="easyui-validatebox"
                                                 data-options="width: 40,required:true,disabled:true"
                                                 onchange="queryDependtype(${status.index+1});"
                                                 id="companybar${status.index+1}"
                                                 name="commendItemlayEntityList[${status.index+1}].companybar"
                                                 value="${itemsEntity.companybar }"/>條，第<input
                                            name="commendItemlayEntityList[${status.index+1}].companyfund"
                                            id="companyfund${status.index+1}" disabled
                                            onchange="queryDependtype(${status.index+1});"
                                            value="${itemsEntity.companyfund }"
                                            type="text" style="width:40px"/>款，第
                                        <input id="companyitem${status.index+1}"
                                               name="commendItemlayEntityList[${status.index+1}].companyitem"
                                               class="easyui-validatebox" data-options="width: 40,disabled:true"
                                               value="${itemsEntity.companyitem }"
                                        />
                                        項<textarea
                                            value="${itemsEntity.companycontent }" id="companycontent${status.index+1}"
                                            name="commendItemlayEntityList[${status.index+1}].companycontent"
                                            type="text" rows="3" disabled
                                            style="width:75%;vertical-align: middle">${itemsEntity.companycontent }</textarea>
                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                               class="deleteBtnStr" disabled
                                               onclick="dependtypedeltr(${status.index+1});return false;"/>
                                    </td>
                                </tr>
                            </c:forEach>
                        </c:if>
                        </tbody>
                        <%--                        <tr align="left" class="nottr" id="dependtypeadd">--%>
                        <%--                            <td colspan="8" width="100%" style="text-align:left;padding-left:10px;">--%>
                        <%--                                <input type="button" id="dependtypeItemAdd" style="width:100px;float:left;"--%>
                        <%--                                       value="添加一筆"/>--%>
                        <%--                            </td>--%>
                        <%--                        </tr>--%>
                        <tr>
                            <td width="8%" colspan="2" align="center">建議嘉奖類別</td>
                            <input id="commend_" type="hidden" value="${wfCommendProcessesEntity.commend }"/>
                            <td width="24%" colspan="2" align="center"><input type='checkbox' name='commend' value='1'
                                                                              disabled onchange="checkcheckbox();">嘉奖&nbsp;
                                <input id="commendcount"
                                       name="commendcount"
                                       class="easyui-combobox" value="${wfCommendProcessesEntity.commendcount}"
                                       data-options="panelHeight: 200,width: 60,disabled:true,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/type_count',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; }"/>
                                </select>
                            </td>
                            <input id="smallgain_" type="hidden" value="${wfCommendProcessesEntity.smallgain }"/>
                            <td width="18%" colspan="2" align="center"><input type='checkbox' name='smallgain' disabled
                                                                              value='2' onchange="checkcheckbox();">記小功&nbsp;
                                <input id="smallgaincount"
                                       name="smallgaincount"
                                       class="easyui-combobox" value="${wfCommendProcessesEntity.smallgaincount}"
                                       data-options="panelHeight: 200,width: 60,disabled:true,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/type_count',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; }"/>
                            </td>
                            <input id="biggain_" type="hidden" value="${wfCommendProcessesEntity.biggain }"/>
                            <td width="25%" colspan="2" align="center"><input type='checkbox' name='biggain' disabled
                                                                              value='3' onchange="checkcheckbox();">記大功&nbsp;
                                <input id="biggaincount"
                                       name="biggaincount"
                                       class="easyui-combobox" value="${wfCommendProcessesEntity.biggaincount}"
                                       data-options="panelHeight: 200,width: 60,disabled:true,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/type_count',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">聯絡人</td>
                            <td width="17%" class="td_style1">
                                <input id="contactno" name="contactno" value="${wfCommendProcessesEntity.contactno}"
                                       class="easyui-validatebox" data-options="width: 70,required:true,disabled:true"
                                       onblur="queryContactUserInfo();"/>
                                /<input id="contactname" name="contactname"
                                        value="${wfCommendProcessesEntity.contactname}"
                                        class="easyui-validatebox"
                                        data-options="width: 70,required:true,disabled:true"/>
                            </td>
                            <td width="8%">聯繫電話</td>
                            <td width="10%" class="td_style1">
                                <input id="contactphone" name="contactphone"
                                       value="${wfCommendProcessesEntity.contactphone}"
                                       class="easyui-validatebox" data-options="width:100,required:true,disabled:true"/>
                            </td>
                            <td width="8%">NOTES</td>
                            <td width="17%" class="td_style1">
                                <input id="contactnotes" name="contactnotes"
                                       value="${wfCommendProcessesEntity.contactnotes}"
                                       class="easyui-validatebox"
                                       data-options="width: 160,required:true,disabled:true"/>
                            </td>
                            <td width="8%">單位</td>
                            <td width="24%" class="td_style1">
                                <input id="contactunit" name="contactunit"
                                       value="${wfCommendProcessesEntity.contactunit}"
                                       class="easyui-validatebox"
                                       data-options="width: 240,required:true,disabled:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">案件來源</td>
                            <td width="35%" class="td_style1" colspan="3">
                                <input id="caseresource" name="caseresource"
                                       value="${wfCommendProcessesEntity.caseresource}"
                                       class="easyui-validatebox" data-options="width: 400,disabled:true"/>
                            </td>
                            <td width="8%">承辦人</td>
                            <td width="17%" class="td_style1">
                                <input id="takeno" name="takeno" value="${wfCommendProcessesEntity.takeno}"
                                       class="easyui-validatebox" data-options="width:70,required:true,disabled:true"
                                       onblur="queryTakeUserInfo();"/>
                                /<input id="takename" name="takename" value="${wfCommendProcessesEntity.takename}"
                                        class="easyui-validatebox" data-options="width:70,required:true,disabled:true"/>
                            </td>
                            <td width="8%" align="center">承辦人主管</td>
                            <td width="24%" class="td_style1">
                                <input id="takechargeno" name="takechargeno"
                                       value="${wfCommendProcessesEntity.takechargeno}"
                                       class="easyui-validatebox" data-options="width:70,required:true,disabled:true"
                                       onblur="queryTakeChargeUserInfo();"/>
                                /<input id="takechargename" name="takechargename"
                                        value="${wfCommendProcessesEntity.takechargename}"
                                        class="easyui-validatebox" data-options="width:70,required:true,disabled:true"/>
                            </td>
                        </tr>
                        <c:if test="${not empty nodeName&&'人資獎懲結案窗口' eq nodeName}">
                            <tr>
                                <td width="8%" colspan="2" align="center">人資嘉奖結案<font color="red">*</font></td>
                                <input id="endcommend_" type="hidden" value="${wfCommendProcessesEntity.commend }"/>
                                <td width="24%" colspan="2" align="center"><input type='checkbox' name='endcommend'
                                                                                  value='1' onchange="checkcheckbox();">嘉奖&nbsp;
                                    <input id="endcommendnum"
                                           name="endcommendnum"
                                           class="easyui-combobox" value="${wfCommendProcessesEntity.commendcount}"
                                           data-options="panelHeight: 200,width: 60,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/type_count',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; }"/>
                                    </select>
                                </td>
                                <input id="endsmallgain_" type="hidden" value="${wfCommendProcessesEntity.smallgain }"/>
                                <td width="18%" colspan="2" align="center"><input type='checkbox' name='endsmallgain'
                                                                                  value='2' onchange="checkcheckbox();">記小功&nbsp;
                                    <input id="endsmallgainnum"
                                           name="endsmallgainnum"
                                           class="easyui-combobox" value="${wfCommendProcessesEntity.smallgaincount}"
                                           data-options="panelHeight: 200,width: 60,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/type_count',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; }"/>
                                </td>
                                <input id="endbiggain_" type="hidden" value="${wfCommendProcessesEntity.biggain }"/>
                                <td width="25%" colspan="2" align="center"><input type='checkbox' name='endbiggain'
                                                                                  value='3' onchange="checkcheckbox();">記大功&nbsp;
                                    <input id="endbiggainnum"
                                           name="endbiggainnum"
                                           class="easyui-combobox" value="${wfCommendProcessesEntity.biggaincount}"
                                           data-options="panelHeight: 200,width: 60,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/type_count',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; }"/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td width="8%">嘉獎公告文號<font color="red">*</font></td>
                                <td width="92%" colspan="7" style="text-align: left">
                                    <input id="commendnumber" name="commendnumber" class="easyui-validatebox"
                                           data-options="width: 300,required:true"
                                           value="${wfCommendProcessesEntity.commendnumber}"/>
                                </td>
                            </tr>
                        </c:if>
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%" colspan="7">
						<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            perCall="audiPrValid(${nodeOrder})"
                                            serialNo="${wfCommendProcessesEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','嘉獎申請表');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfCommendProcessesEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<div id="dlg"></div>
<script src='${ctx}/static/js/humanCapital/wfcommendprocesses.js?random=<%= Math.random()%>'></script>
</body>
</html>