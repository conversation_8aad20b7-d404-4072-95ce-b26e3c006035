<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>嘉獎申請表</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
	<form id="searchFrom" action="">
		<input type="text" name="filt er_EQS_applyno" class="easyui-validatebox"
			   data-options="width:150,prompt: '嘉獎人工號'"/>
		<input type="text" name="filter_EQS_deptno" class="easyui-validatebox"
			   data-options="width:150,prompt: '嘉獎人單位代碼'"/>
		<input type="text" name="filter_EQS_makerno" class="easyui-validatebox"
			   data-options="width:150,prompt: '填單人工號'"/>
		<input type="text" name="filter_EQS_serialno" class="easyui-validatebox"
			   data-options="width:150,prompt: '任務編碼'"/>
		<input type="text" name="filter_LIKES_rzcommendilegalno" class="easyui-validatebox"
			   data-options="width:150,prompt: '人資獎懲窗口工號'"/>
		<input type="text" name="filter_LIKES_rzcommendilegalname" class="easyui-validatebox"
			   data-options="width:150,prompt: '人資獎懲窗口姓名'"/>
		<input type="text" name="filter_GED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd"
			   data-options="width:150,prompt: '填單开始日期'"/>
		- <input type="text" name="filter_LED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd"
				 data-options="width:150,prompt: '填單结束日期'"/>
		<input type="text" name="filter_GED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"
			   data-options="width:150,prompt: '簽核完成开始日期'"/>
		- <input type="text" name="filter_LED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"
				 data-options="width:150,prompt: '簽核完成结束日期'"/>
		<input type="text" name="filter_GED_fromdate" class="easyui-my97" datefmt="yyyy-MM-dd"
			   data-options="width:150,prompt: '到達人資獎懲結案窗口开始日期'"/>
		- <input type="text" name="filter_LED_todate" class="easyui-my97" datefmt="yyyy-MM-dd"
				 data-options="width:150,prompt: '到達人資獎懲結案窗口结束日期'"/>
		<input id="qysjzt" style="width:100px" class="easyui-validatebox" name="filter_EQS_workstatus"/>
		<span class="toolbar-item dialog-tool-separator"></span>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
		   onclick="listSearchReset()">重置</a>
		<a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
		   onclick="exportExcel()" id="exportButton">导出Excel</a>
		<!--導出用，請勿刪除-->
		<input id="page" name="page" type="hidden" value="1"/>
		<input id="rows" name="rows" type="hidden" value="30"/>
	</form>
	<div>
		<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" id="deleteButton"
		   data-options="disabled:true" onclick="del()">删除</a>
	</div>
  </div>
<table id="dg"></table>
<div id="dlg"></div>

<script src="${ctx}/static/js/humanCapital/wfcommendprocesses.js?random=<%= Math.random()%>"></script>
<script type="text/javascript">
	var dg;
	var d;
	$(function () {
		dg = $('#dg').datagrid({
			method: "get",
			url: ctx + '/commendprocessall/list',
			fit: true,
			// fitColumns: true,
			fitColumns: false,  //如需要底部出現滾動條，修改爲false
			border: false,
			idField: 'id',
			striped: true,
			cache: false,  //关闭AJAX相应的缓存
			pagination: true,
			rownumbers: true,
			pageNumber: 1,
			pageSize: 20,
			pageList: [10, 20, 30, 40, 50],
			singleSelect: true,
			frozenColumns: [[
				{field: 'serialno', title: '工單流水號', sortable: true, width: 180, formatter: operation},
				{field: 'applyno', title: '嘉獎人工號', sortable: true, width: 100},
				{field: 'applyname', title: '嘉獎人姓名', sortable: true, width: 100},
			]],
			columns: [[
				{field: 'id', title: '主鍵', hidden: true},
				{field: 'deptno', title: '單位代碼', sortable: true, width: 100},
				{field: 'leveltype', title: '資位', sortable: true, width: 100},
				{field: 'ismanager', title: '管理職', sortable: true, width: 100},
				{field: 'deptname', title: '單位', sortable: true, width: 250, formatter: cellTextTip},
				{field: 'factoryid', title: '廠區', sortable: true, width: 100},
				{field: 'laypersonname', title: '法人', sortable: true, width: 200, formatter: cellTextTipPerson},
				{field: 'daeltype', title: '建議嘉獎類別', sortable: true, width: 100},
				{field: 'confiredaeltype', title: '結案嘉獎類別', sortable: true, width: 100},
				{field: 'happentime', title: '嘉獎發生日期', sortable: true, width: 100},
				{field: 'reporttime', title: '嘉獎通報日期', sortable: true, width: 100},
				{field: 'reason', title: '嘉獎事由', sortable: true, width: 100},
				{field: 'validtime', title: '嘉獎生效日期', sortable: true, width: 100},
				{field: 'contactunit', title: '嘉獎提報單位', sortable: true, width: 100},
				{field: 'maker', title: '填單人', sortable: true, width: 100},
				{field: 'createDate', title: '填單時間', sortable: true, width: 150},
				{field: 'workstatus', title: '表單狀態', sortable: true, width: 100},
				{field: 'nodeName', title: '當前審核節點', sortable: true, width: 150, formatter: formatProgress},
				{field: 'auditUser', title: '當前簽核人', sortable: true, width: 120},
				{field: 'complettime', title: '簽核完成時間', sortable: true, width: 150}
			]],
			onLoadSuccess: function () {
				$(".easyui-tooltip").tooltip({
					onShow: function () {
						$(this).tooltip('tip').css({
							borderColor: '#000'
						});
					}
				});

			},
			onClickRow:function (rowIndex, rowData) {
				if (rowData.workstatus == '取消') {
					$("#deleteButton").linkbutton("enable");
					//選項不可編輯
				} else {
					$("#deleteButton").linkbutton("disable");
				}
			},
			rowStyler: rowStylerFun,
			enableHeaderClickMenu: true,
			enableHeaderContextMenu: true,
			enableRowContextMenu: false,
			toolbar: '#tb'
		});
		//創建下拉查詢條件
		$.ajax({
			url: ctx + "/system/dict/getDictByType/audit_status",
			dataType: "json",
			type: "GET",
			success: function (data) {
				//绑定第一个下拉框
				$("#qysjzt").combobox({
					data: data,
					valueField: "value",
					textField: "label",
					editable: false,
					panelHeight: 400,
					loadFilter: function (data) {
						data.unshift({value: '', label: '審核狀態'});
						return data;
					}
				});
			},
			error: function (error) {
				alert("初始化下拉控件失败");
			}
		});
	});

	//任務編號查看頁面
	function operation(value, row, index) {
		return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('詳情',ctx+'/wfcommendprocesses/view/"
				+ row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
	};

	function cellTextTipPerson(value, row, index) {
		if (value == null || value == '') {
			value = row.layperson;
		}
		if (value != null && value != '' && value != undefined) {
			return '<span title=\"' + value + '\" class=\"easyui-tooltip\">' + value + '</span>';
		}
	}
	//导出excel
	function exportExcel() {
		var options = $('#dg').datagrid('getPager').data("pagination").options;
		var page = options.pageNumber;//当前页数
		var total = options.total;
		var rows = options.pageSize;//每页的记录数（行数）
		var form = document.getElementById("searchFrom");
		$('#page').val(page);
		$('#rows').val(total);
		var form = document.getElementById("searchFrom");
		searchFrom.action = ctx + '/commendprocessall/exportExcel';
		form.submit();
		$('#exportButton').unbind('onclick');
		$("#exportButton").removeAttr("onclick");
		parent.$.messager.alert("溫馨提示","正在導出，請耐心等待，不要重複導出！","warning");
	}
	function del() {
		var row = dg.datagrid('getSelected');
		if (rowIsNull(row)) return;
		parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
			if (data) {
				$.ajax({
					type: 'get',
					url: ctx+"/wfcommendprocesses/delete/" + row.serialno,
					success: function (data) {
						successTip(data, dg);
					}
				});
			}
		});
	}
</script>
</body>
</html>