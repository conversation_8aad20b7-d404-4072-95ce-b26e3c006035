<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>嘉獎申請表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfcommendprocesses/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfCommendProcessesEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfCommendProcessesEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfCommendProcessesEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfCommendProcessesEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfCommendProcessesEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${wfCommendProcessesEntity.makerfactoryid }"/>
    <input id="workFlowId" name="workFlowId" type="hidden" value="${workFlowId }"/>
    <div class="commonW">
        <div class="headTitle">嘉獎申請表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfCommendProcessesEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfCommendProcessesEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfCommendProcessesEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <fmt:formatDate value='${wfCommendProcessesEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfCommendProcessesEntity.makerno}">
            <div class="position_R margin_R">填單人：<span style="color:#999;">${user.loginName}/${user.name}</span></div>
        </c:if>
        <c:if test="${not empty wfCommendProcessesEntity.makerno}">
            <div class="position_R margin_R">
                填單人：<span style="color:#999;">${wfCommendProcessesEntity.makerno}/${wfCommendProcessesEntity.makername}</span></div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">嘉獎者基本信息</td>
                        </tr>
                        <tbody id="info_Body">
                        <c:if test="${wfCommendProcessesEntity.commendItemEntityList!=null&&wfCommendProcessesEntity.commendItemEntityList.size()>0}">
                            <c:forEach items="${wfCommendProcessesEntity.commendItemEntityList}"
                                       var="itemsEntity"
                                       varStatus="status">
                                <tr align="center">
                                    <input id="workAbility${status.index}" name="commendItemEntityList[${status.index+1}].workAbility" type="hidden" value="${itemsEntity.workAbility }"/>
                                    <td width="10%">工號&nbsp;<font color="red">*</font></td>
                                    <td width="10%" class="td_style1">
                                        <input id="applyno${status.index}"
                                               name="commendItemEntityList[${status.index+1}].applyno"
                                               class="easyui-validatebox" data-options="width: 80,required:true"
                                               onblur="queryUserInfo(${status.index});"
                                               value="${itemsEntity.applyno }"/>
                                    </td>
                                    <td width="10%">姓名</td>
                                    <td width="10%" class="td_style1">
                                        <input id="applyname${status.index}"
                                               name="commendItemEntityList[${status.index+1}].applyname"
                                               value="${itemsEntity.applyname }" class="easyui-validatebox inputCss"
                                               readonly data-options="width:80"/>
                                    </td>
                                    <td width="10%">單位代碼</td>
                                    <td width="10%" class="td_style1">
                                        <input id="deptno${status.index}"
                                               name="commendItemEntityList[${status.index+1}].deptno"
                                               value="${itemsEntity.deptno }" class="easyui-validatebox inputCss"
                                               data-options="width: 90" readonly/>
                                    </td>
                                    <td width="10%">資位</td>
                                    <td width="10%" class="td_style1">
                                        <input id="leveltype${status.index}"
                                               name="commendItemEntityList[${status.index+1}].leveltype"
                                               value="${itemsEntity.leveltype }" class="easyui-validatebox inputCss"
                                               data-options="width: 80" readonly/>
                                    </td>
                                    <td width="10%">管理職</td>
                                    <td width="10%" class="td_style1">
                                        <input id="ismanager${status.index}"
                                               name="commendItemEntityList[${status.index+1}].ismanager"
                                               value="${itemsEntity.ismanager }" class="easyui-validatebox inputCss"
                                               data-options="width: 80" readonly/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>廠區</td>
                                    <td class="td_style1">
                                        <input id="factoryid${status.index}"
                                               name="commendItemEntityList[${status.index+1}].factoryid"
                                               class="easyui-combobox" value="${itemsEntity.factoryid }"
                                               data-options="disabled:true,valueField:'factoryid',textField:'factoryname',url:'${ctx}/tqhfactoryidconfig/allFactorys/',loadFilter: function (data) { data.unshift({ factoryid: '', factoryname: '請選擇廠區' }); return data; }"/>
                                    </td>
                                    <td>單位&nbsp;<font color="red">*</font></td>
                                    <td colspan="3" class="td_style1">
                                        <input id="deptname${status.index}"
                                               name="commendItemEntityList[${status.index+1}].deptname"
                                               value="${itemsEntity.deptname }" class="easyui-validatebox"
                                               style="width:90%;" data-options="required:true"/>
                                    </td>
                                    <td>法人&nbsp;<font color="red">*</font></td>
                                    <td colspan="3">
                                        <input id="laypersonname${status.index}"
                                               name="commendItemEntityList[${status.index+1}].laypersonname"
                                               value="${itemsEntity.laypersonname }" class="easyui-validatebox"
                                               style="width:90%;" data-options="required:true"/>
                                            <%--<input id="layperson${status.index}"
                                                   name="commendItemEntityList[${status.index+1}].layperson"
                                                   class="easyui-combobox" value="${itemsEntity.layperson }"
                                                   data-options="panelHeight: 400,width: 300,required:true,validType:'comboxValidate[\'layperson${status.index}\',\'请选择法人\']',valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/project_entity',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇法人' }); return data; }"/>--%>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td colspan="10">
                                        當年度該員工嘉獎：<input id="commendnum${status.index}"
                                                                name="commendItemEntityList[${status.index+1}].commendnum"
                                                                value="${itemsEntity.commendnum }"
                                                                class="easyui-validatebox"
                                                                style="width:200px;" readonly/>
                                    </td>
                                </tr>
                                <tr id="bondedgoodsItem${status.index+1}">
                                    <input type="hidden" id="shunxu${status.index+1}"
                                           name="commendItemEntityList[${status.index+1}].shunxu"
                                           value="${status.index+1}"/>
                                    <c:if test="${status.index>0}">
                                    <td colspan="9" align="center" bgcolor="#faebd7">以上為第位${status.index+1}位嘉獎人員</td>
                                    <td width="6%" align="center">
                                            <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                   class="deleteBtnStr"
                                                   onclick="bondedgooddeltr(${status.index+1});return false;"/>
                                    </td>
                                    </c:if>
                                    <c:if test="${status.index==0}">
                                        <td colspan="10" align="center" bgcolor="#faebd7">以上為第位${status.index+1}位嘉獎人員</td>
                                    </c:if>
                                </tr>
                            </c:forEach>
                        </c:if>
                        <c:if test="${wfCommendProcessesEntity.commendItemEntityList==null||wfCommendProcessesEntity.commendItemEntityList.size()==0}">
                            <tr align="center">
                                <input id="workAbility0" name="commendItemEntityList[0].workAbility" type="hidden"/>
                                <td width="10%">工號&nbsp;<font color="red">*</font></td>
                                <td width="10%" class="td_style1">
                                    <input id="applyno0" name="commendItemEntityList[0].applyno"
                                           class="easyui-validatebox" data-options="width: 80,required:true"
                                           onblur="queryUserInfo(0);"/>
                                </td>
                                <td width="10%">姓名</td>
                                <td width="10%" class="td_style1">
                                    <input id="applyname0" name="commendItemEntityList[0].applyname"
                                           class="easyui-validatebox inputCss" readonly data-options="width:80"/>
                                </td>
                                <td width="10%">單位代碼</td>
                                <td width="10%" class="td_style1">
                                    <input id="deptno0" name="commendItemEntityList[0].deptno"
                                           class="easyui-validatebox inputCss" data-options="width: 90" readonly/>
                                </td>
                                <td width="10%">資位</td>
                                <td width="10%" class="td_style1">
                                    <input id="leveltype0" name="commendItemEntityList[0].leveltype"
                                           class="easyui-validatebox inputCss" data-options="width: 80" readonly/>
                                </td>
                                <td width="10%">管理職</td>
                                <td width="10%" class="td_style1">
                                    <input id="ismanager0" name="commendItemEntityList[0].ismanager"
                                           class="easyui-validatebox inputCss" data-options="width: 80" readonly/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td width="10%">廠區</td>
                                <td width="10%" class="td_style1">
                                    <input id="factoryid0" name="commendItemEntityList[0].factoryid"
                                           class="easyui-combobox"
                                           data-options="disabled:true,valueField:'factoryid',textField:'factoryname',url:'${ctx}/tqhfactoryidconfig/allFactorys/',loadFilter: function (data) { data.unshift({ factoryid: '', factoryname: '請選擇廠區' }); return data; }"/>
                                </td>
                                <td width="10%">單位&nbsp;<font color="red">*</font></td>
                                <td colspan="3" width="30%" class="td_style1">
                                    <input id="deptname0" name="commendItemEntityList[0].deptname"
                                           class="easyui-validatebox" style="width:90%;" data-options="required:true"/>
                                </td>
                                <td width="10%">法人&nbsp;<font color="red">*</font></td>
                                <td colspan="3" width="30%">
                                    <input id="laypersonname0"
                                           name="commendItemEntityList[0].laypersonname"
                                           class="easyui-validatebox"
                                           style="width:90%;" data-options="required:true"/>
                                        <%--<input id="layperson0" name="commendItemEntityList[0].layperson"
                                               class="easyui-combobox"
                                               data-options="panelHeight: 400,width: 300,required:true,validType:'comboxValidate[\'layperson0\',\'请选择法人\']',valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/project_entity',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇法人' }); return data; }"/>--%>
                                </td>
                            </tr>
                            <tr align="center">
                                <td colspan="10" align="left">
                                    當年度該員工嘉獎：<input id="commendnum0"
                                                            name="commendItemEntityList[0].commendnum"
                                                            class="easyui-validatebox"
                                                            style="width:200px;" readonly/>
                                </td>
                            </tr>
                            <tr id="bondedgoodsItem0">
                                <input id="shunxu0" type="hidden" name="commendItemEntityList[0].shunxu"
                                       value="1"/>
                                <td colspan="9" align="center" bgcolor="#faebd7">以上為第位1位嘉獎人員</td>
                                <td></td>
                            </tr>
                        </c:if>
                        </tbody>
                        <tr align="left" class="nottr">
                            <td colspan="10" width="100%" style="text-align:center;padding-left:10px;">
                                <a href="#" id="bondedgoodItemAdd" class="easyui-linkbutton" data-options="iconCls:'icon-add',text:'添加一筆'" style="width: 100px"></a>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2"><a href="${ctx}/wfcommendprocesses/downLoad/batchImportTpl"
                                               id="btnBatchImportTpl">批量申請模板下載</a></td>
                            <td colspan="8" class="td_style1">&nbsp;&nbsp;&nbsp;&nbsp;<a
                                    href="#" id="batchImport" class="easyui-linkbutton"
                                    data-options="iconCls:'icon-hamburg-cv',text:'批量導入'" style="width: 100px;"
                                    onclick="openBatchImportWin();"></a>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">嘉獎信息</td>
                        </tr>
                        <tr>
                            <td align="center">發生區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="happenarea" name="happenarea" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfCommendProcessesEntity.happenarea}"/>
                            </td>
                            <td align="center">嘉獎發生日期&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="happentime" name="happentime" class="easyui-validatebox Wdate"
                                       data-options="width:150,required:true,prompt:'请选择嘉獎發生日期'"
                                       value="${wfCommendProcessesEntity.happentime}"
                                       onclick="WdatePicker({onpicked:function(){reporttime.click()},doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd',maxDate:'%y-%M-%d %H:%m:%s'})"/>
                            </td>
                            <td align="center">嘉獎通報日期&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="reporttime" name="reporttime" class="easyui-validatebox Wdate"
                                       data-options="width:150,required:true,prompt:'请选择嘉獎通報日期'"
                                       value="${wfCommendProcessesEntity.reporttime}"
                                       onclick="WdatePicker({skin:'whyGreen',dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'happentime\')}',doubleCalendar:true})"/>
                            </td>
                            <td align="center">嘉獎事由&nbsp;<font color="red">*</font></td>
                            <td class="td_style1" colspan="3">
                                <input id="reason" name="reason" class="easyui-validatebox"
                                       data-options="width: 300,required:true"
                                       value="${wfCommendProcessesEntity.reason}"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">嘉獎事實描述（可附件）</td>
                        </tr>
                        <tr>
                            <td colspan="8">
                            <textarea id="reasondesc" name="reasondesc" data-options="required:true"
                                      style="width:90%;height:80px;" rows="5" cols="6"
                                      oninput="return LessThanTWO(this);"
                                      onchange="return LessThanTWO(this);"
                                      onpropertychange="return LessThanTWO(this);"
                                      maxlength="500">${wfCommendProcessesEntity.reasondesc }</textarea><span
                                    id="reasondesc500"></span>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="8" width="100%" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="attachids" value="${wfCommendProcessesEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">符合公司規定及條款</td>
                        </tr>
                        <tr>
                            <td colspan="1">公司規章：
                            </td>
                            <td colspan="7">
                                <input id="smallType" name="smallType"
                                       class="easyui-combobox"
                                       data-options="width: 500,onSelect:function(){onchangeSmallType();}"
                                       value="${wfCommendProcessesEntity.smallType}"/>
                            </td>
                        </tr>
                        <tbody id="info_Body_one">
                        <c:if test="${wfCommendProcessesEntity.commendItemlayEntityList!=null&&wfCommendProcessesEntity.commendItemlayEntityList.size()>0}">
                            <c:forEach items="${wfCommendProcessesEntity.commendItemlayEntityList}" var="itemsEntity"
                                       varStatus="status">
                                <tr id="layEntityList${status.index+1}">
                                    <td colspan="8">
                                        第<font color="red">*</font><input type="text"
                                                                           class="easyui-validatebox"
                                                                           data-options="width: 40,required:true"
                                                                           onchange="queryDependtype(${status.index+1});"
                                                                           id="companybar${status.index+1}"
                                                                           name="commendItemlayEntityList[${status.index+1}].companybar"
                                                                           value="${itemsEntity.companybar }"/>條，第<input
                                            name="commendItemlayEntityList[${status.index+1}].companyfund"
                                            id="companyfund${status.index+1}"
                                            onchange="queryDependtype(${status.index+1});"
                                            value="${itemsEntity.companyfund }"
                                            type="text" style="width:40px"/>款，第
                                        <input id="companyitem${status.index+1}"
                                               name="commendItemlayEntityList[${status.index+1}].companyitem"
                                               class="easyui-validatebox" data-options="width: 40"
                                               value="${itemsEntity.companyitem }"
                                        />
                                        項<textarea
                                            value="${itemsEntity.companycontent }" id="companycontent${status.index+1}"
                                            name="commendItemlayEntityList[${status.index+1}].companycontent"
                                            type="text" rows="3"
                                            style="width:75%;vertical-align: middle">${itemsEntity.companycontent }</textarea>
                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                               class="deleteBtnStr"
                                               onclick="dependtypedeltr(${status.index+1});return false;"/>
                                    </td>
                                </tr>
                            </c:forEach>
                        </c:if>
                        <c:if test="${wfCommendProcessesEntity.commendItemlayEntityList==null||wfCommendProcessesEntity.commendItemlayEntityList.size()==0}">
                            <tr id="layEntityList0">
                                <td colspan="8">第<input type="text" class="easyui-validatebox"
                                                         data-options="width: 40,required:true"
                                                         onchange="queryDependtype(0);" id="companybar0"
                                                         name="commendItemlayEntityList[0].companybar"
                                                         value="${wfCommendProcessesEntity.companybar }"/>條，第<input
                                        name="commendItemlayEntityList[0].companyfund" id="companyfund0"
                                        onchange="queryDependtype(0);" value="${wfCommendProcessesEntity.companyfund }"
                                        type="text" style="width:40px"/>款，第
                                    <input id="companyitem0"
                                           name="commendItemlayEntityList[0].companyitem"
                                           class="easyui-validatebox" data-options="width: 40"
                                    />項<textarea
                                            value="${wfCommendProcessesEntity.companycontent }" id="companycontent0"
                                            name="commendItemlayEntityList[0].companycontent" rows="3"
                                            style="width:75%;vertical-align: middle">${wfCommendProcessesEntity.companycontent }</textarea>
                                </td>
                            </tr>
                        </c:if>
                        </tbody>
                        <%--                        <tr align="left" class="nottr" id="dependtypeadd">--%>
                        <%--                            <td colspan="8" width="100%" style="text-align:left;padding-left:10px;">--%>
                        <%--                                <input type="button" id="dependtypeItemAdd" style="width:100px;float:left;"--%>
                        <%--                                       value="添加一筆"/>--%>
                        <%--                            </td>--%>
                        <%--                        </tr>--%>
                        <tr>
                            <td width="8%" colspan="2" align="center">建議嘉奖類別&nbsp;<font color="red">*</font></td>
                            <input id="commend_" type="hidden" value="${wfCommendProcessesEntity.commend }"/>
                            <td width="24%" colspan="2" align="center"><input type='checkbox' name='commend' value='1'
                                                                              onchange="checkcheckbox();">嘉奖&nbsp;
                                <input id="commendcount"
                                       name="commendcount"
                                       class="easyui-combobox" value="${wfCommendProcessesEntity.commendcount}"
                                       data-options="panelHeight: 200,width: 60,textField:'label',url:'${ctx}/system/dict/getDictByType/type_count',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; }"/>
                                </select>
                            </td>
                            <input id="smallgain_" type="hidden" value="${wfCommendProcessesEntity.smallgain }"/>
                            <td width="18%" colspan="2" align="center"><input type='checkbox' name='smallgain'
                                                                              value='2' onchange="checkcheckbox();">記小功&nbsp;
                                <input id="smallgaincount"
                                       name="smallgaincount"
                                       class="easyui-combobox" value="${wfCommendProcessesEntity.smallgaincount}"
                                       data-options="panelHeight: 200,width: 60,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/type_count',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; },onSelect:function(){queryDependtype(0);}"/>
                            </td>
                            <input id="biggain_" type="hidden" value="${wfCommendProcessesEntity.biggain }"/>
                            <td width="25%" colspan="2" align="center"><input type='checkbox' name='biggain'
                                                                              value='3' onchange="checkcheckbox();">記大功&nbsp;
                                <input id="biggaincount"
                                       name="biggaincount"
                                       class="easyui-combobox" value="${wfCommendProcessesEntity.biggaincount}"
                                       data-options="panelHeight: 200,width: 60,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/type_count',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">聯絡人&nbsp;<font color="red">*</font></td>
                            <td width="17%" class="td_style1">
                                <input id="contactno" name="contactno" value="${wfCommendProcessesEntity.contactno}"
                                       class="easyui-validatebox" data-options="width: 70,required:true"
                                       onblur="queryContactUserInfo();"/>
                                /<input id="contactname" name="contactname"
                                        value="${wfCommendProcessesEntity.contactname}"
                                        class="easyui-validatebox" data-options="width: 70,required:true"/>
                            </td>
                            <td width="8%">聯繫電話&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="contactphone" name="contactphone"
                                       value="${wfCommendProcessesEntity.contactphone}"
                                       class="easyui-validatebox" data-options="width:100,required:true"/>
                            </td>
                            <td width="8%">NOTES&nbsp;<font color="red">*</font></td>
                            <td width="17%" class="td_style1">
                                <input id="contactnotes" name="contactnotes"
                                       value="${wfCommendProcessesEntity.contactnotes}"
                                       class="easyui-validatebox" data-options="width: 160,required:true"/>
                            </td>
                            <td width="8%">單位&nbsp;<font color="red">*</font></td>
                            <td width="24%" class="td_style1">
                                <input id="contactunit" name="contactunit"
                                       value="${wfCommendProcessesEntity.contactunit}"
                                       class="easyui-validatebox" data-options="width: 240,required:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">案件來源</td>
                            <td width="35%" class="td_style1" colspan="3">
                                <input id="caseresource" name="caseresource"
                                       value="${wfCommendProcessesEntity.caseresource}"
                                       class="easyui-validatebox" data-options="width: 400"/>
                            </td>
                            <td width="8%">承辦人&nbsp;<font color="red">*</font></td>
                            <td width="17%" class="td_style1">
                                <input id="takeno" name="takeno" value="${wfCommendProcessesEntity.takeno}"
                                       class="easyui-validatebox" data-options="width:70,required:true"
                                       onblur="queryTakeUserInfo();"/>
                                /<input id="takename" name="takename" value="${wfCommendProcessesEntity.takename}"
                                        class="easyui-validatebox" data-options="width:70,required:true"/>
                            </td>
                            <td width="8%">承辦人主管&nbsp;<font color="red">*</font></td>
                            <td width="24%" class="td_style1">
                                <input id="takechargeno" name="takechargeno"
                                       value="${wfCommendProcessesEntity.takechargeno}"
                                       class="easyui-validatebox" data-options="width:70,required:true"
                                       onblur="queryTakeChargeUserInfo();"/>
                                /<input id="takechargename" name="takechargename"
                                        value="${wfCommendProcessesEntity.takechargename}"
                                        class="easyui-validatebox" data-options="width:70,required:true"/>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_yuangongjiajiang_v1','嘉獎申請表','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="rzcommendilegalTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['rzcommendilegalno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(133,'rzcommendilegalTable','rzcommendilegalno','rzcommendilegalname',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="rzcommendilegalno" name="rzcommendilegalno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['rzcommendilegalno']}"
                                                               readonly
                                                               value="${wfCommendProcessesEntity.rzcommendilegalno }"/><c:if
                                                            test="${requiredMap['rzcommendilegalno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="rzcommendilegalname" name="rzcommendilegalname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['rzcommendilegalno']}"
                                                                value="${wfCommendProcessesEntity.rzcommendilegalname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="rzychargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['rzychargeno_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(134,'rzychargeno','rzychargename',$('#factoryid0').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="rzychargeno" name="rzychargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['rzychargeno']}"
                                                               readonly
                                                               value="${wfCommendProcessesEntity.rzychargeno }"/><c:if
                                                            test="${requiredMap['rzychargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="rzychargename" name="rzychargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['rzychargeno']}"
                                                                value="${wfCommendProcessesEntity.rzychargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="undertakormanagerTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align:center;"
                                                                    align="center">${requiredMap['undertakormanagerno_name']}
                                                                </td>
                                                                <%--                                                                <td style="border: none;">--%>
                                                                <%--                                                                    <div class="float_L qhUserIcon"--%>
                                                                <%--                                                                         onclick="selectRole($('#dealdeptno').val(),'undertakormanager','undertakormanagername',$('#dealfactoryid').val())"></div>--%>
                                                                <%--                                                                </td>--%>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="undertakormanagerno" name="undertakormanagerno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['undertakormanagerno']}"
                                                               readonly
                                                               value="${wfCommendProcessesEntity.undertakormanagerno }"/><c:if
                                                            test="${requiredMap['undertakormanagerno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="undertakormanagername" name="undertakormanagername"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['undertakormanagerno']}"
                                                                value="${wfCommendProcessesEntity.undertakormanagername }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#deptno0').val(),'kchargeno','kchargename',$('#factoryid0').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wfCommendProcessesEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfCommendProcessesEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#deptno0').val(),'bchargeno','bchargename',$('#factoryid0').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfCommendProcessesEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfCommendProcessesEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#deptno0').val(),'cchargeno','cchargename',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfCommendProcessesEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfCommendProcessesEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#deptno0').val(),'zchargeno','zchargename',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfCommendProcessesEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfCommendProcessesEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#deptno0').val(),'zcchargeno','zcchargename',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wfCommendProcessesEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfCommendProcessesEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="rzbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['rzbchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(18,'rzbchargeTable','rzbchargeno','rzbchargename',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="rzbchargeno" name="rzbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['rzbchargeno']}"
                                                               readonly
                                                               value="${wfCommendProcessesEntity.rzbchargeno }"/><c:if
                                                            test="${requiredMap['rzbchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="rzbchargename" name="rzbchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['rzbchargeno']}"
                                                                value="${wfCommendProcessesEntity.rzbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl1Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno1_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(135,'yl1Table','ylno1','ylname1',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno1" name="ylno1"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno1']}"
                                                               readonly
                                                               value="${wfCommendProcessesEntity.ylno1 }"/><c:if
                                                            test="${requiredMap['ylno1'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname1" name="ylname1"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno1']}"
                                                                value="${wfCommendProcessesEntity.ylname1 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#deptno0').val(),'pcchargeno','pcchargename',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${wfCommendProcessesEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wfCommendProcessesEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno2_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(28,'yl2Table','ylno2','ylname2',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="ylno2"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}"
                                                               readonly
                                                               value="${wfCommendProcessesEntity.ylno2 }"/><c:if
                                                            test="${requiredMap['ylno2'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname2" name="ylname2"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}"
                                                                value="${wfCommendProcessesEntity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="groupchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['groupchargeno_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(29,'groupchargeTable','groupchargeno','groupchargename',$('#factoryid0').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="groupchargeno" name="groupchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['groupchargeno']}"
                                                               readonly
                                                               value="${wfCommendProcessesEntity.groupchargeno }"/><c:if
                                                            test="${requiredMap['groupchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="groupchargename" name="groupchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['groupchargeno']}"
                                                                value="${wfCommendProcessesEntity.groupchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="endchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['endchargeno_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(137,'endchargeno','endchargename',$('#factoryid0').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="endchargeno" name="endchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['endchargeno']}"
                                                               readonly
                                                               value="${wfCommendProcessesEntity.endchargeno }"/><c:if
                                                            test="${requiredMap['endchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="endchargename" name="endchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['endchargeno']}"
                                                                value="${wfCommendProcessesEntity.endchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfCommendProcessesEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="canelTask('${wfCommendProcessesEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="onlyKchargeSignle" value="1" />
    <div id="win"></div>
</form>
</div>
<div id="optionWin" class="easyui-window" title="嘉獎申請表" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%">
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="file" type="file" style="width: 300px"
                           accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>
                    <input id="isExcel2003" name="isExcel2003" type="hidden" value="">

                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr>
                <td align="left" style="width: 60%; white-space: nowrap;">
                    <span id="tishi">正在導入中，請稍後...</span>
                </td>
            </tr>
            <%--            <tr align="center">--%>
            <%--                <td style="width: 60%; white-space: nowrap;">--%>
            <%--                    <span id="labelListAddResult"></span><a href="${ctx}/wfsslvpnprocess/errorExcel"--%>
            <%--                                                            id="downloadError"--%>
            <%--                                                            plain="true">查看錯誤信息</a>--%>
            <%--                </td>--%>
            <%--            </tr>--%>
        </table>
    </form>
</div>
<script src='${ctx}/static/js/humanCapital/wfcommendprocesses.js?random=<%= Math.random()%>'></script>
</body>
</html>