<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>PushMail郵件服務申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/wfpushmailprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfPushmailProcessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfPushmailProcessEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">PushMail郵件服務申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfPushmailProcessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfPushmailProcessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfPushmailProcessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfPushmailProcessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfPushmailProcessEntity.makerno}/${wfPushmailProcessEntity.makername}</div>
        <div class="clear"></div>
        <table  class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">承辦人工號</td>
                            <td width="4%" class="td_style1">
                                <input id="dealno" name="dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true,disabled:true"
                                       value="<c:if test="${wfPushmailProcessEntity.dealno!=null&&wfPushmailProcessEntity.dealno!=''}">${wfPushmailProcessEntity.dealno}</c:if><c:if test="${wfPushmailProcessEntity.dealno==null||wfPushmailProcessEntity.dealno==''}">${user.loginName}</c:if>"
                                       onblur="queryUserInfo('deal');"/>
                            </td>
                            <td width="4%">承辦人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfPushmailProcessEntity.dealname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfPushmailProcessEntity.dealdeptno }"/>
                            </td>
                            <td width="5%">廠區</td>
                            <td width="5%" class="td_style1">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfPushmailProcessEntity.dealfactoryid }" disabled
                                       data-options="width: 120,required:true"/>
                            </td>
                            <td width="3%">聯繫分機</td>
                            <td width="7%" class="td_style1">
                                <input id="dealtel" name="dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfPushmailProcessEntity.dealtel }"
                                       data-options="required:true,prompt:'579+66666',disabled:true"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="5" class="td_style1">
                                <input id="dealdeptname" name="dealdeptname" class="easyui-validatebox"
                                       data-options="width: 410,required:true,disabled:true"
                                       value="${wfPushmailProcessEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="dealemail" class="easyui-validatebox"
                                       value="${wfPushmailProcessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,disabled:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">申請人工號</td>
                            <td width="4%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true,disabled:true"
                                       value="${wfPushmailProcessEntity.applyno}" onblur="queryUserInfo('apply');"/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfPushmailProcessEntity.applyname}"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfPushmailProcessEntity.applydeptno }"/>
                            </td>
                            <td width="5%">費用代碼</td>
                            <td width="5%" class="td_style1">
                                <input id="applycostno" name="applycostno"
                                       class="easyui-validatebox" data-options="width: 90,disabled:true"
                                       value="${wfPushmailProcessEntity.applycostno }"/>
                            </td>
                            <td width="3%">所在廠區</td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfPushmailProcessEntity.applyfactoryid }"
                                       data-options="width: 120,disabled:true,required:true,validType:'comboxValidate[\'applyfactoryid\',\'请选择所在廠區\']',onSelect:function(){onchangeFactory();}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="applyleveltype" class="easyui-validatebox inputCss"
                                       data-options="width: 80"
                                       value="${wfPushmailProcessEntity.applyleveltype }" readonly/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="applymanager" class="easyui-validatebox inputCss"
                                       data-options="width: 80"
                                       value="${wfPushmailProcessEntity.applymanager }" readonly/>
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       value="${wfPushmailProcessEntity.applyemail }" style="width:300px;"
                                       data-options="required:true,disabled:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td>使用區域</td>
                            <td class="td_style1">
                                <input id="applyarea" name="applyarea" class="easyui-combobox"
                                       data-options="width: 70,disabled:true,onSelect:function(){onchangeArea();},validType:'comboxValidate[\'applyarea\',\'請選擇使用區域\']'"
                                       value="${wfPushmailProcessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="applybuilding" class="easyui-combobox"
                                       data-options="width: 70,disabled:true,validType:'comboxValidate[\'applybuilding\',\'請選擇使用區域\']'"
                                       value="${wfPushmailProcessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="5%">聯繫分機</td>
                            <td width="5%" class="td_style1">
                                <input id="applyphone" name="applyphone" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfPushmailProcessEntity.applyphone }"
                                       data-options="required:true,disabled:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>單位</td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox"
                                       data-options="width: 410,required:true,disabled:true"
                                       value="${wfPushmailProcessEntity.applydeptname }"/>
                            </td>
                            <td>安保區域</td>
                            <td class="td_style2">
                                <div class="securityareaDiv"></div>
                                <input id="securityareaValue" name="securityareaValue"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfPushmailProcessEntity.securityarea }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">PushMail郵件服務申請單</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">Notes郵件地址</td>
                            <td width="5%" class="td_style1" colspan="3">
                                <input id="pushmail" name="pushmail" class="easyui-validatebox"
                                       style="width:90%;"
                                       value="${wfPushmailProcessEntity.pushmail }"
                                       data-options="required:true,disabled:true"/>
                            </td>
                            <td width="5%">設備型號</td>
                            <td width="5%" class="td_style1" colspan="2">
                                <input id="pushequtype" name="pushequtype" class="easyui-validatebox"
                                       style="width:90%;"
                                       value="${wfPushmailProcessEntity.pushequtype }"
                                       data-options="required:true,disabled:true"/>
                            </td>
                            <td width="5%">主要使用地點</td>
                            <td width="5%" class="td_style1" colspan="2">
                                <input id="pushuseplace" name="pushuseplace" class="easyui-validatebox"
                                       style="width:90%;"
                                       value="${wfPushmailProcessEntity.pushuseplace }"
                                       data-options="required:true,disabled:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="5%">申請類型</td>
                            <td width="5%" colspan="3">
                                <%--<input id="pushtype" name="pushtype" class="easyui-validatebox"--%>
                                <%--style="width:90%;"--%>
                                <%--value="${wfPushmailProcessEntity.pushtype }"--%>
                                <%--data-options="required:true"/>--%>
                                <div class="pushtypeDiv"></div>
                                <input id="pushtypeValue" name="pushtypeValue"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfPushmailProcessEntity.pushtype }"/>
                            </td>
                            <td width="5%">開始時間</td>
                            <td width="5%" class="td_style1" colspan="2">
                                <input id="pushstarttime" name="pushstarttime" class="easyui-validatebox Wdate"
                                       data-options="width:200,disabled:true,required:true,prompt:'请选择开始时间'" style="width:180px"
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd HH:mm:ss" value="${wfPushmailProcessEntity.pushstarttime}"/>"
                                       onclick="WdatePicker({doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'%y-%M-%d %H:%m:%s'})"/>
                            </td>
                            <td width="5%">結束時間</td>
                            <td width="5%" class="td_style1" colspan="2">
                                <input id="pushendtime" name="pushendtime" class="easyui-validatebox Wdate"
                                       data-options="width:200,disabled:true,required:true,prompt:'请选择結束时间'" style="width:180px"
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd HH:mm:ss" value="${wfPushmailProcessEntity.pushendtime}"/>"
                                       onclick="WdatePicker({doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'%y-%M-%d %H:%m:%s'})"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>需求說明</td>
                            <td colspan="9" class="td_style2" align="left">
                                <textarea id="describtion" name="describtion"
                                          class="easyui-validatebox"
                                          oninput="return LessThan(this);"
                                          onchange="return LessThan(this);"
                                          onpropertychange="return LessThan(this);"
                                          maxlength="300"
                                          style="width:90%;height:80px;"
                                          rows="5" cols="6"
                                          data-options="required:true,disabled:true,validType:'length[0,300]'">${wfPushmailProcessEntity.describtion }</textarea><span
                                    id="txtNum"></span></td>
                            </td>
                        </tr>
                    </table>
                </td>
           </tr>
            <tr>
                <td>
                    <table class="formList">
                       <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${wfPushmailProcessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="left">
                            <td align="center">備註&nbsp;</td>
                            <td width="100%" class="td_style1" colspan="9" id="descId">
                                因PushMail用戶的不正當使用可以會使公司的智慧財產或其他機密信息蒙受損失，會請各</br>
                                階主管務必嚴格審核 <a href="${ctx}/wfpushmailprocess/downlimitURL">承諾書下載</a>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','PushMail郵件服務申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfPushmailProcessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                               <c:if test="${wfPushmailProcessEntity.workstatus!=null&&wfPushmailProcessEntity.workstatus==3}">
                                  <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                      style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                               </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/humanCapital/wfpushmailprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>