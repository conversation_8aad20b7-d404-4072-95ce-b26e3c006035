<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>簽核路徑申請表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
            color: black;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfpathprocesses/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfpathprocessesEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfpathprocessesEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">簽核路徑申請表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfpathprocessesEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfpathprocessesEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfpathprocessesEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfpathprocessesEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfpathprocessesEntity.makerno}/${wfpathprocessesEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">承辦人工號</td>
                            <td width="12%" class="td_style2">${wfpathprocessesEntity.dealno}</td>
                            <td width="8%">承辦人</td>
                            <td width="12%" class="td_style2">${wfpathprocessesEntity.dealname}</td>
                            <td width="8%">單位代碼</td>
                            <td width="12%" class="td_style2">${wfpathprocessesEntity.dealdeptno}</td>
                            <td width="8%">聯繫方式</td>
                            <td width="12%" class="td_style2">${wfpathprocessesEntity.dealtel }</td>
                            <td width="8%">所在廠區</td>
                            <td width="12%" class="td_style1">
                                <input id="dealfactoryid" name="wfpathprocesses.dealfactoryid" class="easyui-combobox" disabled data-options="width:100"
                                       value="${wfpathprocessesEntity.dealfactoryid }"/>
                                <input id="dealnofactoryid" name="wfpathprocesses.dealnofactoryid"
                                       value="${wfpathprocessesEntity.dealnofactoryid}" type="hidden"/>
                                <input id="dealnofactoryname" name="wfpathprocesses.dealnofactoryname"
                                       value="${wfpathprocessesEntity.dealnofactoryname}" type="hidden"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="5" class="td_style2">${wfpathprocessesEntity.dealdeptname }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfpathprocessesEntity.dealemail }</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table width="150%">
                                        <tr>
                                            <th width="40px">序號</th>
                                            <th width="60px">廠區</th>
                                            <th width="60px">單位代碼</th>
                                            <th width="80px">${nameMap['kchargeno_name']}</th>
                                            <th width="80px">${nameMap['bchargeno_name']}</th>
                                            <th width="80px">${nameMap['cchargeno_name']}</th>
                                            <th width="80px">${nameMap['czchargeno_name']}</th>
                                            <th width="80px">${nameMap['zchargeno_name']}</th>
                                            <th width="80px">${nameMap['czcchargeno_name']}</th>
                                            <th width="80px">${nameMap['zcchargeno_name']}</th>
                                            <th width="80px">${nameMap['pcchargeno_name']}</th>
                                            <th width="80px">${nameMap['cqchargeno_name']}</th>
                                            <th width="80px">${nameMap['sychargeno_name']}</th>
                                            <th width="60px">申請類型</th>
                                        </tr>
                                        <tbody id="updatePathList">
                                        <c:if test="${wfpathitems!=null&&wfpathitems.size()>0}">
                                            <c:forEach items="${wfpathitems}" var="wfpathitems" varStatus="status">
                                                <tr align="center" id="pathItem${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>${wfpathitems.factoryname}</td>
                                                    <td>${wfpathitems.deptno}</td>
                                                    <td>
                                                        <c:if test="${wfpathitems.kchargeno!=''&& wfpathitems.kchargeno!=null}">${wfpathitems.kchargeno}</c:if>/<c:if
                                                            test="${wfpathitems.kchargename!=''&& wfpathitems.kchargename!=null}">${wfpathitems.kchargename}</c:if>
                                                    </td>
                                                    <td>
                                                        <c:if test="${wfpathitems.bchargeno!=''&& wfpathitems.bchargeno!=null}">${wfpathitems.bchargeno}</c:if>/<c:if
                                                            test="${wfpathitems.bchargename!=''&& wfpathitems.bchargename!=null}">${wfpathitems.bchargename}</c:if>
                                                    </td>
                                                    <td>
                                                        <c:if test="${wfpathitems.cchargeno!=''&& wfpathitems.cchargeno!=null}">${wfpathitems.cchargeno} </c:if>/<c:if
                                                            test="${wfpathitems.cchargename!=''&& wfpathitems.cchargename!=null}">${wfpathitems.cchargename} </c:if>
                                                    </td>
                                                    <td>
                                                        <c:if test="${wfpathitems.czchargeno!=''&& wfpathitems.czchargeno!=null}">${wfpathitems.czchargeno}</c:if>/<c:if
                                                            test="${wfpathitems.czchargename!=''&& wfpathitems.czchargename!=null}">${wfpathitems.czchargename}</c:if>
                                                    </td>
                                                    <td>
                                                        <c:if test="${wfpathitems.zchargeno!=''&& wfpathitems.zchargeno!=null}">${wfpathitems.zchargeno}</c:if>/<c:if
                                                            test="${wfpathitems.zchargename!=''&& wfpathitems.zchargename!=null}">${wfpathitems.zchargename}</c:if>
                                                    </td>
                                                    <td>
                                                        <c:if test="${wfpathitems.czcchargeno!=''&& wfpathitems.czcchargeno!=null}">${wfpathitems.czcchargeno} </c:if>/<c:if
                                                            test="${wfpathitems.czcchargename!=''&& wfpathitems.czcchargename!=null}">${wfpathitems.czcchargename} </c:if>
                                                    </td>
                                                    <td>
                                                        <c:if test="${wfpathitems.zcchargeno!=''&& wfpathitems.zcchargeno!=null}">${wfpathitems.zcchargeno} </c:if>/<c:if
                                                            test="${wfpathitems.zcchargename!=''&& wfpathitems.zcchargename!=null}">${wfpathitems.zcchargename} </c:if>
                                                    </td>
                                                    <td>
                                                        <c:if test="${wfpathitems.pcchargeno!=''&& wfpathitems.pcchargeno!=null}">${wfpathitems.pcchargeno}</c:if>/<c:if
                                                            test="${wfpathitems.pcchargename!=''&& wfpathitems.pcchargename!=null}">${wfpathitems.pcchargename}</c:if>
                                                    </td>
                                                    <td>
                                                        <c:if test="${wfpathitems.cqchargeno!=''&& wfpathitems.cqchargeno!=null}">${wfpathitems.cqchargeno}</c:if>/<c:if
                                                            test="${wfpathitems.cqchargename!=''&& wfpathitems.cqchargename!=null}">${wfpathitems.cqchargename}</c:if>
                                                    </td>
                                                    <td>
                                                        <c:if test="${wfpathitems.sychargeno!=''&& wfpathitems.sychargeno!=null}">${wfpathitems.sychargeno}</c:if>/<c:if
                                                            test="${wfpathitems.sychargename!=''&& wfpathitems.sychargename!=null}">${wfpathitems.sychargename}</c:if>
                                                    </td>
                                                    <td>
                                                        <c:if test="${wfpathitems.operatetype=='0'}">新增</c:if>
                                                        <c:if test="${wfpathitems.operatetype=='1'}">
                                                            <c:if test="${wfpathitems.updateflag=='0'}">修改前</c:if>
                                                            <c:if test="${wfpathitems.updateflag=='1'}">修改后</c:if>
                                                        </c:if>
                                                        <c:if test="${wfpathitems.operatetype=='2'}">删除</c:if>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>需求說明</td>
                            <td colspan="9" class="td_style2">
						    <textarea id="applyreason" name="wfpathprocesses.applyreason"
                                      class="easyui-validatebox"
                                      maxlength="300"
                                      style="width:99%;height:80px;" rows="5"
                                      cols="6">${wfpathprocessesEntity.applyreason }</textarea></td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style2">
                                <c:if test="${wfpathprocessesEntity.attachids!=null}">
                                    <input type="hidden" id="attachids" name="wfpathprocesses.attachids" value="${wfpathprocessesEntity.attachids }"/>
                                    <div id="dowloadUrl">
                                        <c:forEach items="${file}" varStatus="i" var="item">
                                            <div id="${item.id}"
                                                 style="line-height:30px;margin-left:5px;" class="float_L">
                                                <div class="float_L">
                                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                                </div>
                                            </div>
                                        </c:forEach>
                                    </div>
                                </c:if>
                                <c:if test="${wfpathprocessesEntity.attachids==null}">無</c:if>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>批註</td>
                            <td colspan="9" class="td_style2">
                               <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                         style="width:1000px;height:60px;"
                                         rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','簽核路徑申請表');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfpathprocessesEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfpathprocessesEntity.workstatus!=null&&wfpathprocessesEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/humanCapital/wfpathprocesses.js?random=<%= Math.random()%>'></script>
</body>
</html>