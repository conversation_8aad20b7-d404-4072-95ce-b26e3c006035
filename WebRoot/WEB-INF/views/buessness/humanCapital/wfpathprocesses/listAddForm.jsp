<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title></title>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>

</head>
<body>
<div>
    <table class="formTable" id="addPageTable">
        <tr>
            <td>廠區：</td>
            <td>
                <input id="add_factoryid" name="add_factoryid" class="easyui-combobox" disabled
                       data-options="width: 150,url:'${ctx}/tqhfactoryidconfig/allFactorys/', valueField:'factoryid', textField:'factoryname',validType:'comboxValidate[\'add_factoryid\',\'请選擇所在廠區\']'"
                       value=""/>
                <input id="add_factoryname" name="add_factoryname" type="hidden" value="">
                <font color="red">*</font>
            </td>
            <td></td>
        </tr>
        <tr>
            <td>單位代碼：</td>
            <td>
                <input id="add_deptno" name="add_deptno" class="easyui-validatebox"
                       data-options="width: 150,required:true"
                       value=""/>
                <font color="red">*</font>
            </td>
        </tr>
        <tr>
            <td>${nameMap['kchargeno_name']}：</td>
            <td>
                <input id="add_kchargeno" name="add_kchargeno" class="easyui-validatebox managerNoadd"
                       data-options="width: 150"
                       value="" onblur="getUserNameByEmpnoAdd(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="add_kchargename" name="add_kchargename" class="easyui-validatebox managerNameadd"
                       data-options="width: 150"
                       readonly="true" value=""/></td>
        </tr>
        <tr>
            <td>${nameMap['bchargeno_name']}：</td>
            <td>
                <input id="add_bchargeno" name="add_bchargeno" class="easyui-validatebox managerNoadd"
                       data-options="width: 150"
                       value="" onblur="getUserNameByEmpnoAdd(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="add_bchargename" name="add_bchargename" class="easyui-validatebox managerNameadd"
                       data-options="width: 150"
                       readonly="true" value=""/></td>
        </tr>
        <tr>
            <td>${nameMap['cchargeno_name']}：</td>
            <td>
                <input id="add_cchargeno" name="add_cchargeno" class="easyui-validatebox managerNoadd"
                       data-options="width: 150"
                       value="" onblur="getUserNameByEmpnoAdd(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="add_cchargename" name="add_cchargename" class="easyui-validatebox managerNameadd"
                       data-options="width: 150"
                       readonly="true" value=""/></td>
        </tr>
        <tr>
            <td>${nameMap['czchargeno_name']}：</td>
            <td>
                <input id="add_czchargeno" name="add_czchargeno" class="easyui-validatebox managerNoadd"
                       data-options="width: 150"
                       value="" onblur="getUserNameByEmpnoAdd(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="add_czchargename" name="add_czchargename" class="easyui-validatebox managerNameadd"
                       data-options="width: 150"
                       readonly="true" value=""/></td>
        </tr>
        <tr>
            <td>${nameMap['zchargeno_name']}：</td>
            <td>
                <input id="add_zchargeno" name="add_zchargeno" class="easyui-validatebox managerNoadd"
                       data-options="width: 150"
                       value="" onblur="getUserNameByEmpnoAdd(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="add_zchargename" name="add_zchargename" class="easyui-validatebox managerNameadd"
                       data-options="width: 150"
                       readonly="true" value=""/></td>
        </tr>
        <tr>
            <td>${nameMap['czcchargeno_name']}：</td>
            <td>
                <input id="add_czcchargeno" name="add_czcchargeno" class="easyui-validatebox managerNoadd"
                       data-options="width: 150"
                       value="" onblur="getUserNameByEmpnoAdd(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="add_czcchargename" name="add_czcchargename" class="easyui-validatebox managerNameadd"
                       data-options="width: 150"
                       readonly="true" value=""/></td>
        </tr>
        <tr>
            <td>${nameMap['zcchargeno_name']}：</td>
            <td>
                <input id="add_zcchargeno" name="add_zcchargeno" class="easyui-validatebox managerNoadd"
                       data-options="width: 150"
                       value="" onblur="getUserNameByEmpnoAdd(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="add_zcchargename" name="add_zcchargename" class="easyui-validatebox managerNameadd"
                       data-options="width: 150"
                       readonly="true" value=""/></td>
        </tr>
        <tr>
            <td>${nameMap['pcchargeno_name']}：</td>
            <td>
                <input id="add_pcchargeno" name="add_pcchargeno" class="easyui-validatebox managerNoadd"
                       data-options="width: 150"
                       value="" onblur="getUserNameByEmpnoAdd(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="add_pcchargename" name="add_pcchargename" class="easyui-validatebox managerNameadd"
                       data-options="width: 150"
                       readonly="true" value=""/></td>
        </tr>
        <tr>
            <td>${nameMap['cqchargeno_name']}：</td>
            <td>
                <input id="add_cqchargeno" name="add_cqchargeno" class="easyui-validatebox managerNoadd"
                       data-options="width: 150"
                       value="" onblur="getUserNameByEmpnoAdd(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="add_cqchargename" name="add_cqchargename" class="easyui-validatebox managerNameadd"
                       data-options="width: 150"
                       readonly="true" value=""/></td>
        </tr>
        <tr>
            <td>${nameMap['sychargeno_name']}：</td>
            <td>
                <input id="add_sychargeno" name="add_sychargeno" class="easyui-validatebox managerNoadd"
                       data-options="width: 150"
                       value="" onblur="getUserNameByEmpnoAdd(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="add_sychargename" name="add_sychargename" class="easyui-validatebox managerNameadd"
                       data-options="width: 150"
                       readonly="true" value=""/></td>
        </tr>
        <tr>
            <td>申請人工號：</td>
            <td>
                <input id="add_applyempno" name="add_applyempno" class="easyui-validatebox" data-options="width: 150"
                       readonly="true" value=""/>
            </td>
            <td>姓名：</td>
            <td><input id="add_applyusername" name="add_applyusername" class="easyui-validatebox" data-options="width: 150"
                       readonly="true" value=""/></td>
        </tr>
    </table>
</div>
<script type="text/javascript">
    $(function(){
        $("#add_factoryid").val($('#noFactoryId').val());//新增時廠區不能修改為填單人歸屬廠區
        $("#add_applyempno").val($("#addApplyNo").val());//新增時申請人工號、申請人姓名為承辦人信息不能修改
        $("#add_applyusername").val($("#addApplyName").val());
        queryFacnamByFacid($('#noFactoryId').val());
    });
    function getUserNameByEmpnoAdd(obj) {
        if (obj.value != null && obj.value != "") {
            $.post(ctx + '/system/user/getUserInfo/', {
                empno: obj.value
            }, function (data) {
                if (!data) {
                    $.messager.alert("溫馨提示", "工號不存在", "error");
                    $("#" + obj.name.replace("no", "no")).val("");
                    $("#" + obj.name.replace("no", "name")).val("");
                } else {
                    if (obj.name == 'applyempno') {
                        $("#applyusername").val(data.empname);
                    } else {
                        $("#" + obj.name.replace("no", "name")).val(data.empname);
                    }
                }
            }, 'json');
        }else{
            $("#" + obj.name.replace("no", "name")).val('');
        }
    }
    //獲取廠區名稱
    function queryFacnamByFacid(facid) {
        $.get(ctx + '/tqhfactoryidconfig/getFactryNameById/' + facid, function(
            result) {
            $('#add_factoryname').val(result.factoryname);
        },'json');
    }
</script>
</body>
</html>