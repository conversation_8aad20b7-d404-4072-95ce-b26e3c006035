<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>簽核路徑申請表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
        .td_style3{
            border: none 0px;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfpathprocesses/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfpathprocessesEntity.id }"/>
    <input id="serialno" name="wfpathprocesses.serialno" type="hidden" value="${wfpathprocessesEntity.serialno }"/>
    <input id="makerno" name="wfpathprocesses.makerno" type="hidden" value="${wfpathprocessesEntity.makerno }"/>
    <input id="makername" name="wfpathprocesses.makername" type="hidden" value="${wfpathprocessesEntity.makername }"/>
    <input id="makerdeptno" name="wfpathprocesses.makerdeptno" type="hidden" value="${wfpathprocessesEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfpathprocesses.makerfactoryid" type="hidden" value="${wfpathprocessesEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">簽核路徑申請表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfpathprocessesEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfpathprocessesEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfpathprocessesEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfpathprocessesEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfpathprocessesEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfpathprocessesEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfpathprocessesEntity.makerno}/${wfpathprocessesEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="12%" class="td_style1">
                                <input id="dealno" name="wfpathprocesses.dealno" class="easyui-validatebox" readonly
                                       data-options="width: 80,required:true"
                                       value="<c:if test="${wfpathprocessesEntity.dealno!=null&&wfpathprocessesEntity.dealno!=''}">${wfpathprocessesEntity.dealno}</c:if>
                                       <c:if test="${wfpathprocessesEntity.dealno==null||wfpathprocessesEntity.dealno==''}">${user.loginName}</c:if>"/>
                            </td>
                            <td width="8%">承辦人</td>
                            <td width="12%" class="td_style1">
                                <input id="dealname" name="wfpathprocesses.dealname" class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfpathprocessesEntity.dealname }"/>
                            </td>
                            <td width="8%">單位代碼</td>
                            <td width="12%" class="td_style1">
                                <input id="dealdeptno" name="wfpathprocesses.dealdeptno" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfpathprocessesEntity.dealdeptno }" readonly/>
                            </td>
                            <td width="8%">聯繫方式&nbsp;<font color="red">*</font></td>
                            <td width="12%" class="td_style1">
                                <input id="dealtel" name="wfpathprocesses.dealtel" class="easyui-validatebox" data-options="width:100,required:true"
                                       value="${wfpathprocessesEntity.dealtel }"/>
                            </td>
                            <td width="8%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="12%" class="td_style1">
                                <input id="dealfactoryid" name="wfpathprocesses.dealfactoryid" disabled class="easyui-combobox" data-options="width:100,required:true"
                                       value="${wfpathprocessesEntity.dealfactoryid }"/>
                                <input id="dealnofactoryid" name="wfpathprocesses.dealnofactoryid"
                                       value="${wfpathprocessesEntity.dealnofactoryid}" type="hidden"/>
                                <input id="dealnofactoryname" name="wfpathprocesses.dealnofactoryname"
                                       value="${wfpathprocessesEntity.dealnofactoryname}" type="hidden"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="dealdeptname" name="wfpathprocesses.dealdeptname" class="easyui-validatebox" data-options="width:400,required:true"
                                       value="${wfpathprocessesEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="wfpathprocesses.dealemail" class="easyui-validatebox"data-options="width: 250,required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"
                                       value="${wfpathprocessesEntity.dealemail }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>簽核路徑<br>單位代碼</td>
                            <td width="12%" class="td_style1">
                                <input id="signdeptno" name="wfpathprocesses.signdeptno" class="easyui-validatebox" data-options="width:100,"
                                       value="${wfpathprocessesEntity.signdeptno }"/>
                            </td>
                            <td colspan="8" class="td_style1">
                                <a href="javascript:;" id="btnCxlj" class="easyui-linkbutton"
                                   style="width: 80px;float:left;margin-right: 8px;margin-left: 8px;"
                                   onclick="getPathListInfo()">查詢路徑</a>
                                <a href="javascript:;" id="btnXglj" class="easyui-linkbutton"
                                   style="width: 80px;float:left;margin-right: 8px;" onclick="updatePath()">修改路徑</a>
                                <a href="javascript:;" id="btnSclj" class="easyui-linkbutton"
                                   style="width: 80px;float:left;margin-right: 8px;" onclick="deletePath()">刪除路徑</a>
                                <a href="javascript:;" id="btnXzlj" class="easyui-linkbutton"
                                   style="width: 80px;float:left;margin-right: 8px;"
                                   onclick="addPathInfo()">新增路徑</a>
                                <a href="javascript:;" id="btnPllj" class="easyui-linkbutton"
                                   style="width: 80px;float:left;margin-right: 8px;"
                                   onclick="pathimportExcel();">批量新增</a>
                                <%--<a href="javascript:;" id="btnmblj" class="easyui-linkbutton" style="width: 100px;float:left;margin-right: 8px;" plain="true" iconCls="icon-standard-page-excel" onclick="mbdownLoad();">批量導入模板下載</a>--%>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="15" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table width="150%">
                                        <tr>
                                            <th width="40px">序號</th>
                                            <th width="60px">廠區</th>
                                            <th width="60px">單位代碼</th>
                                            <th width="80px">${nameMap['kchargeno_name']}</th>
                                            <th width="80px">${nameMap['bchargeno_name']}</th>
                                            <th width="80px">${nameMap['cchargeno_name']}</th>
                                            <th width="80px">${nameMap['czchargeno_name']}</th>
                                            <th width="80px">${nameMap['zchargeno_name']}</th>
                                            <th width="80px">${nameMap['czcchargeno_name']}</th>
                                            <th width="80px">${nameMap['zcchargeno_name']}</th>
                                            <th width="80px">${nameMap['pcchargeno_name']}</th>
                                            <th width="80px">${nameMap['cqchargeno_name']}</th>
                                            <th width="80px">${nameMap['sychargeno_name']}</th>
                                            <th width="60px">更新人</th>
                                            <th width="60px">更新時間</th>
                                        </tr>
                                        <tbody id="pathListInfo">

                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="15" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="15" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table width="150%">
                                        <tr>
                                            <th width="40px">序號</th>
                                            <th width="60px">廠區</th>
                                            <th width="60px">單位代碼</th>
                                            <th width="80px">${nameMap['kchargeno_name']}</th>
                                            <th width="80px">${nameMap['bchargeno_name']}</th>
                                            <th width="80px">${nameMap['cchargeno_name']}</th>
                                            <th width="80px">${nameMap['czchargeno_name']}</th>
                                            <th width="80px">${nameMap['zchargeno_name']}</th>
                                            <th width="80px">${nameMap['czcchargeno_name']}</th>
                                            <th width="80px">${nameMap['zcchargeno_name']}</th>
                                            <th width="80px">${nameMap['pcchargeno_name']}</th>
                                            <th width="80px">${nameMap['cqchargeno_name']}</th>
                                            <th width="80px">${nameMap['sychargeno_name']}</th>
                                            <th width="60px">申請類型</th>
                                            <th width="60px">操作</th>
                                        </tr>
                                        <tbody id="updatePathList">
                                        <c:if test="${wfpathitems!=null&&wfpathitems.size()>0}">
                                            <c:forEach items="${wfpathitems}" var="wfpathitems" varStatus="status">
                                                <tr align="center" id="pathItem${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <input id='add_factoryid${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].factoryid'
                                                               type='hidden'
                                                               value='${wfpathitems.factoryid}'/>${wfpathitems.factoryname}
                                                        <input id='add_factoryname${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].factoryname'
                                                               type='hidden' value='${wfpathitems.factoryname}'/>
                                                    </td>
                                                    <td>
                                                        <input id='add_deptno${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].deptno'
                                                               type='hidden'
                                                               value='${wfpathitems.deptno}'/>${wfpathitems.deptno}
                                                    </td>
                                                    <td>
                                                        <input id='add_kchargeno${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].kchargeno'
                                                               type='hidden' value='${wfpathitems.kchargeno}'/>
                                                        <input id='add_kchargename${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].kchargename'
                                                               type='hidden' value='${wfpathitems.kchargename}'/>
                                                        <c:if test="${wfpathitems.kchargeno!=''&& wfpathitems.kchargeno!=null}">${wfpathitems.kchargeno}</c:if>/<c:if
                                                            test="${wfpathitems.kchargename!=''&& wfpathitems.kchargename!=null}">${wfpathitems.kchargename}</c:if>
                                                    </td>
                                                    <td>
                                                        <input id='add_bchargeno${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].bchargeno'
                                                               type='hidden' value='${wfpathitems.bchargeno}'/>
                                                        <input id='add_bchargename${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].bchargename'
                                                               type='hidden' value='${wfpathitems.bchargename}'/>
                                                        <c:if test="${wfpathitems.bchargeno!=''&& wfpathitems.bchargeno!=null}">${wfpathitems.bchargeno}</c:if>/<c:if
                                                            test="${wfpathitems.bchargename!=''&& wfpathitems.bchargename!=null}">${wfpathitems.bchargename}</c:if>
                                                    </td>
                                                    <td>
                                                        <input id='add_cchargeno${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].cchargeno'
                                                               type='hidden' value='${wfpathitems.cchargeno}'/>
                                                        <input id='add_cchargename${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].cchargename'
                                                               type='hidden' value='${wfpathitems.cchargename}'/>
                                                        <c:if test="${wfpathitems.cchargeno!=''&& wfpathitems.cchargeno!=null}">${wfpathitems.cchargeno} </c:if>/<c:if
                                                            test="${wfpathitems.cchargename!=''&& wfpathitems.cchargename!=null}">${wfpathitems.cchargename} </c:if>
                                                    </td>
                                                    <td>
                                                        <input id='add_czchargeno${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].czchargeno'
                                                               type='hidden' value='${wfpathitems.czchargeno}'/>
                                                        <input id='add_czchargename${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].czchargename'
                                                               type='hidden' value='${wfpathitems.czchargename}'/>
                                                        <c:if test="${wfpathitems.czchargeno!=''&& wfpathitems.czchargeno!=null}">${wfpathitems.czchargeno}</c:if>/<c:if
                                                            test="${wfpathitems.czchargename!=''&& wfpathitems.czchargename!=null}">${wfpathitems.czchargename}</c:if>
                                                    </td>
                                                    <td>
                                                        <input id='add_zchargeno${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].zchargeno'
                                                               type='hidden' value='${wfpathitems.zchargeno}'/>
                                                        <input id='add_zchargename${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].zchargename'
                                                               type='hidden' value='${wfpathitems.zchargename}'/>
                                                        <c:if test="${wfpathitems.zchargeno!=''&& wfpathitems.zchargeno!=null}">${wfpathitems.zchargeno}</c:if>/<c:if
                                                            test="${wfpathitems.zchargename!=''&& wfpathitems.zchargename!=null}">${wfpathitems.zchargename}</c:if>
                                                    </td>
                                                    <td>
                                                        <input id='add_czcchargeno${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].czcchargeno'
                                                               type='hidden' value='${wfpathitems.czcchargeno}'/>
                                                        <input id='add_czcchargename${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].czcchargename'
                                                               type='hidden' value='${wfpathitems.czcchargename}'/>
                                                        <c:if test="${wfpathitems.czcchargeno!=''&& wfpathitems.czcchargeno!=null}">${wfpathitems.czcchargeno} </c:if>/<c:if
                                                            test="${wfpathitems.czcchargename!=''&& wfpathitems.czcchargename!=null}">${wfpathitems.czcchargename} </c:if>
                                                    </td>
                                                    <td>
                                                        <input id='add_zcchargeno${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].zcchargeno'
                                                               type='hidden' value='${wfpathitems.zcchargeno}'/>
                                                        <input id='add_zcchargename${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].zcchargename'
                                                               type='hidden' value='${wfpathitems.zcchargename}'/>
                                                        <c:if test="${wfpathitems.zcchargeno!=''&& wfpathitems.zcchargeno!=null}">${wfpathitems.zcchargeno} </c:if>/<c:if
                                                            test="${wfpathitems.zcchargename!=''&& wfpathitems.zcchargename!=null}">${wfpathitems.zcchargename} </c:if>
                                                    </td>
                                                    <td>
                                                        <input id='add_pcchargeno${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].pcchargeno'
                                                               type='hidden' value='${wfpathitems.pcchargeno}'/>
                                                        <input id='add_pcchargename${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].pcchargename'
                                                               type='hidden' value='${wfpathitems.pcchargename}'/>
                                                        <c:if test="${wfpathitems.pcchargeno!=''&& wfpathitems.pcchargeno!=null}">${wfpathitems.pcchargeno}</c:if>/<c:if
                                                            test="${wfpathitems.pcchargename!=''&& wfpathitems.pcchargename!=null}">${wfpathitems.pcchargename}</c:if>
                                                    </td>
                                                    <td>
                                                        <input id='add_cqchargeno${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].cqchargeno'
                                                               type='hidden' value='${wfpathitems.cqchargeno}'/>
                                                        <input id='add_cqchargename${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].cqchargename'
                                                               type='hidden' value='${wfpathitems.cqchargename}'/>
                                                        <c:if test="${wfpathitems.cqchargeno!=''&& wfpathitems.cqchargeno!=null}">${wfpathitems.cqchargeno}</c:if>/<c:if
                                                            test="${wfpathitems.cqchargename!=''&& wfpathitems.cqchargename!=null}">${wfpathitems.cqchargename}</c:if>
                                                    </td>
                                                    <td>
                                                        <input id='add_sychargeno${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].sychargeno'
                                                               type='hidden' value='${wfpathitems.sychargeno}'/>
                                                        <input id='add_sychargename${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].sychargename'
                                                               type='hidden' value='${wfpathitems.sychargename}'/>
                                                        <c:if test="${wfpathitems.sychargeno!=''&& wfpathitems.sychargeno!=null}">${wfpathitems.sychargeno}</c:if>/<c:if
                                                            test="${wfpathitems.sychargename!=''&& wfpathitems.sychargename!=null}">${wfpathitems.sychargename}</c:if>
                                                    </td>
                                                    <td>
                                                        <input id='add_shunxu${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].shunxu'
                                                               type='hidden' value='${status.index+1}'/>
                                                        <input id='add_applyno${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].applyno'
                                                               type='hidden' value='${wfpathitems.applyno}'/>
                                                        <input id='add_applyname${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].applyname'
                                                               type='hidden' value='${wfpathitems.applyname}'/>
                                                        <input id='add_updateBy${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].updateBy'
                                                               type='hidden' value='${wfpathitems.updateBy}'/>
                                                        <input id='add_oldpathid${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].oldpathid'
                                                               type='hidden' value='${wfpathitems.oldpathid}'/>
                                                        <input id='add_updateflag${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].updateflag'
                                                               type='hidden' value='${wfpathitems.updateflag}'/>
                                                        <input id='add_operatetype${status.index+1}'
                                                               name='wfpathitems[${status.index+1}].operatetype'
                                                               type='hidden' value='${wfpathitems.operatetype}'/>
                                                        <c:if test="${wfpathitems.operatetype=='0'}">新增</c:if>
                                                        <c:if test="${wfpathitems.operatetype=='1'}">
                                                            <c:if test="${wfpathitems.updateflag=='0'}">修改前</c:if>
                                                            <c:if test="${wfpathitems.updateflag=='1'}">修改后</c:if>
                                                        </c:if>
                                                        <c:if test="${wfpathitems.operatetype=='2'}">删除</c:if>
                                                    </td>
                                                    <c:if test="${wfpathitems.operatetype=='0'||wfpathitems.operatetype=='2'}">
                                                        <td>
                                                            <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                                   onclick="removeTr(this);return false;"/>
                                                        </td>
                                                    </c:if>
                                                    <c:if test="${wfpathitems.operatetype=='1'}">
                                                        <c:if test="${wfpathitems.updateflag=='0'}">
                                                            <td rowspan='2'>
                                                                <input type="image"
                                                                       src="${ctx}/static/images/deleteRow.png"
                                                                       onclick="removeTr2(this);return false;"/>
                                                            </td>
                                                        </c:if>
                                                    </c:if>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>需求說明&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1">
						    <textarea id="applyreason" name="wfpathprocesses.applyreason"
                                      class="easyui-validatebox"
                                      oninput="return LessThan(this);"
                                      onchange="return LessThan(this);"
                                      onpropertychange="return LessThan(this);"
                                      maxlength="300"
                                      style="width:99%;height:80px;" data-options="required:true,prompt:'請需求單位詳細說明'"
                                      rows="5" cols="6"
                                      data-options="required:true,validType:'length[0,300]'">${wfpathprocessesEntity.applyreason }</textarea><span id="txtNum"></span></td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');
                                            " class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="wfpathprocesses.attachids" value="${wfpathprocessesEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="9" class="td_style2">
                                <p>
                                    1.修改路徑時核准主管級別必須高於路徑變更主管級別；<br>
                                    2.刪除路徑和新增路徑時主管核准須核准至部級或以上主管。
                                </p>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)" onclick="showWfImag('dzqh_qianhelujingshenqingdan','簽核路徑申請表','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zgshchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">主管審核
                                                                    <a href="#" onclick="addHq('zgshcharge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zgshchargeno" onblur="getUserNameByEmpno(this,'zgshcharge');" name="wfpathprocesses.zgshchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zgshchargeno']}"
                                                               value="${wfpathprocessesEntity.zgshchargeno }"/><c:if
                                                            test="${requiredMap['zgshchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zgshchargename" name="wfpathprocesses.zgshchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zgshchargeno']}"
                                                                value="${wfpathprocessesEntity.zgshchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zghzchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">主管核准
                                                                    <a href="#" onclick="addHq('zghzcharge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zghzchargeno" onblur="getUserNameByEmpno(this,'zghzcharge');" name="wfpathprocesses.zghzchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zghzchargeno']}"
                                                               value="${wfpathprocessesEntity.zghzchargeno }"/><c:if
                                                            test="${requiredMap['zghzchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zghzchargename" name="wfpathprocesses.zghzchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zghzchargeno']}"
                                                                value="${wfpathprocessesEntity.zghzchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="rzqrchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">人資確認</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(224,'rzqrchargeTable','rzqrchargeno','rzqrchargename',$('#dealfactoryid').combobox('getValue'),'wfpathprocesses')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="rzqrchargeno" name="wfpathprocesses.rzqrchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['rzqrchargeno']}"
                                                               readonly
                                                               value="${wfpathprocessesEntity.rzqrchargeno }"/><c:if
                                                            test="${requiredMap['rzqrchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="rzqrchargename" name="wfpathprocesses.rzqrchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['rzqrchargeno']}"
                                                                value="${wfpathprocessesEntity.rzqrchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zxrychargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">資訊人員確認</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(222,'zxrychargeTable','zxrychargeno','zxrychargename',$('#dealfactoryid').combobox('getValue'),'wfpathprocesses')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxrychargeno" name="wfpathprocesses.zxrychargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxrychargeno']}"
                                                               readonly
                                                               value="${wfpathprocessesEntity.zxrychargeno }"/><c:if
                                                            test="${requiredMap['zxrychargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxrychargename" name="wfpathprocesses.zxrychargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxrychargeno']}"
                                                                value="${wfpathprocessesEntity.zxrychargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <input type="hidden" id="noFactoryId" name="noFactoryId" value=""/>
    <input type="hidden" id="addApplyNo" name="addApplyNo" value=""/>
    <input type="hidden" id="addApplyName" name="addApplyName" value=""/>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
    <div id="winupdate"></div>
</form>
<div id="optionWin" class="easyui-window" title="簽核路徑批量導入" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%">
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="file" type="file" style="width: 300px"
                           accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>

                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="javascript:;" id="btnmblj" class="easyui-linkbutton" style="width: 150px;float:left;margin-right: 8px;" plain="true" iconCls="icon-standard-page-excel" onclick="mbdownLoad();">批量導入模板下載</a>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <span ID="labelListAddResult"></span><a href="${ctx}/wfpathprocesses/downLoad/errorExcel" id="downloadError"
                                                            plain="true">查看錯誤信息</a>
                </td>
            </tr>
        </table>
    </form>
</div>
<script src='${ctx}/static/js/humanCapital/wfpathprocesses.js?random=<%= Math.random()%>'></script>
<script type="text/javascript">
    if ("${wfpathprocessesEntity.zgshchargeno}" != "") {
        var nostr = "${wfpathprocessesEntity.zgshchargeno}";
        var namestr = "${wfpathprocessesEntity.zgshchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#zgshchargeTable tr:eq(" + (i + 2) + ")").find("#zgshchargeno").val(notr[i]);
            $("#zgshchargeTable tr:eq(" + (i + 2) + ")").find("#zgshchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#zgshchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='zgshchargeno' name='wfpathprocesses.zgshchargeno' style='width: 80px' value='' onblur='getUserNameByEmpno(this,'hqocharge');'/>/<input id='zgshchargename' name='wfpathprocesses.zgshchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wfpathprocessesEntity.zghzchargeno}" != "") {
        var nostr = "${wfpathprocessesEntity.zghzchargeno}";
        var namestr = "${wfpathprocessesEntity.zghzchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#zghzchargeTable tr:eq(" + (i + 2) + ")").find("#zghzchargeno").val(notr[i]);
            $("#zghzchargeTable tr:eq(" + (i + 2) + ")").find("#zghzchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#zghzchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='zghzchargeno' name='wfpathprocesses.zghzchargeno' style='width: 80px' value='' onblur='getUserNameByEmpno(this,'hqtcharge');'/>/<input id='zghzchargename' name='wfpathprocesses.zghzchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
</script>
</body>
</html>