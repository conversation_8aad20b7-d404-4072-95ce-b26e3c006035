<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title></title>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>

</head>
<body>
<div>
    <table class="formTable" id="updatePageTable">
        <tr>
            <td>廠區：</td>
            <td>
                <input id="update_factoryid" name="update_factoryid" class="easyui-combobox" disabled
                       data-options="width: 150,url:'${ctx}/tqhfactoryidconfig/allFactorys/', valueField:'factoryid', textField:'factoryname',validType:'comboxValidate[\'update_factoryid\',\'请選擇所在廠區\']'"
                       value="${tQhChargepath.factoryid}"/>
                <input id="update_factoryname" name="update_factoryname" type="hidden" value="">
                <input id="update_id" name="update_id" type="hidden" value="${tQhChargepath.id}">
                <font color="red">*</font>
            </td>
            <td></td>
        </tr>
        <tr>
            <td>單位代碼：</td>
            <td>
                <input id="update_deptno" name="update_deptno" class="easyui-validatebox"
                       data-options="width: 150,required:true"
                       value="${tQhChargepath.deptno }"/>
                <font color="red">*</font>
            </td>
        </tr>
        <tr>
            <td>${nameMap['kchargeno_name']}</td>
            <td>
                <input id="update_kchargeno" name="update_kchargeno" class="easyui-validatebox managerNoupdate"
                       data-options="width: 150"
                       value="${tQhChargepath.kchargeno}" onblur="getUserNameByEmpnoUpdate(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="update_kchargename" name="update_kchargename" class="easyui-validatebox managerNameupdate"
                       data-options="width: 150"
                       readonly="true" value="${tQhChargepath.kchargename}"/></td>
        </tr>
        <tr>
            <td>${nameMap['bchargeno_name']}：</td>
            <td>
                <input id="update_bchargeno" name="update_bchargeno" class="easyui-validatebox managerNoupdate"
                       data-options="width: 150"
                       value="${tQhChargepath.bchargeno}" onblur="getUserNameByEmpnoUpdate(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="update_bchargename" name="update_bchargename" class="easyui-validatebox managerNameupdate"
                       data-options="width: 150"
                       readonly="true" value="${tQhChargepath.bchargename}"/></td>
        </tr>
        <tr>
            <td>${nameMap['cchargeno_name']}：</td>
            <td>
                <input id="update_cchargeno" name="update_cchargeno" class="easyui-validatebox managerNoupdate"
                       data-options="width: 150"
                       value="${tQhChargepath.cchargeno}" onblur="getUserNameByEmpnoUpdate(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="update_cchargename" name="update_cchargename" class="easyui-validatebox managerNameupdate"
                       data-options="width: 150"
                       readonly="true" value="${tQhChargepath.cchargename}"/></td>
        </tr>
        <tr>
            <td>${nameMap['czchargeno_name']}：</td>
            <td>
                <input id="update_czchargeno" name="update_czchargeno" class="easyui-validatebox managerNoupdate"
                       data-options="width: 150"
                       value="${tQhChargepath.czchargeno}" onblur="getUserNameByEmpnoUpdate(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="update_czchargename" name="update_czchargename" class="easyui-validatebox managerNameupdate"
                       data-options="width: 150"
                       readonly="true" value="${tQhChargepath.czchargename}"/></td>
        </tr>
        <tr>
            <td>${nameMap['zchargeno_name']}：</td>
            <td>
                <input id="update_zchargeno" name="update_zchargeno" class="easyui-validatebox managerNoupdate"
                       data-options="width: 150"
                       value="${tQhChargepath.zchargeno}" onblur="getUserNameByEmpnoUpdate(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="update_zchargename" name="update_zchargename" class="easyui-validatebox managerNameupdate"
                       data-options="width: 150"
                       readonly="true" value="${tQhChargepath.zchargename}"/></td>
        </tr>
        <tr>
            <td>${nameMap['czcchargeno_name']}：</td>
            <td>
                <input id="update_czcchargeno" name="update_czcchargeno" class="easyui-validatebox managerNoupdate"
                       data-options="width: 150"
                       value="${tQhChargepath.czcchargeno}" onblur="getUserNameByEmpnoUpdate(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="update_czcchargename" name="update_czcchargename"
                       class="easyui-validatebox managerNameupdate" data-options="width: 150"
                       readonly="true" value="${tQhChargepath.czcchargename}"/></td>
        </tr>
        <tr>
            <td>${nameMap['zcchargeno_name']}：</td>
            <td>
                <input id="update_zcchargeno" name="update_zcchargeno" class="easyui-validatebox managerNoupdate"
                       data-options="width: 150"
                       value="${tQhChargepath.zcchargeno}" onblur="getUserNameByEmpnoUpdate(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="update_zcchargename" name="update_zcchargename" class="easyui-validatebox managerNameupdate"
                       data-options="width: 150"
                       readonly="true" value="${tQhChargepath.zcchargename}"/></td>
        </tr>
        <tr>
            <td>${nameMap['pcchargeno_name']}：</td>
            <td>
                <input id="update_pcchargeno" name="update_pcchargeno" class="easyui-validatebox managerNoupdate"
                       data-options="width: 150"
                       value="${tQhChargepath.pcchargeno}" onblur="getUserNameByEmpnoUpdate(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="update_pcchargename" name="update_pcchargename" class="easyui-validatebox managerNameupdate"
                       data-options="width: 150"
                       readonly="true" value="${tQhChargepath.pcchargename}"/></td>
        </tr>
        <tr>
            <td>${nameMap['cqchargeno_name']}：</td>
            <td>
                <input id="update_cqchargeno" name="update_cqchargeno" class="easyui-validatebox managerNoupdate"
                       data-options="width: 150"
                       value="${tQhChargepath.cqchargeno}" onblur="getUserNameByEmpnoUpdate(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="update_cqchargename" name="update_cqchargename" class="easyui-validatebox managerNameupdate"
                       data-options="width: 150"
                       readonly="true" value="${tQhChargepath.cqchargename}"/></td>
        </tr>
        <tr>
            <td>${nameMap['sychargeno_name']}：</td>
            <td>
                <input id="update_sychargeno" name="update_sychargeno" class="easyui-validatebox managerNoupdate"
                       data-options="width: 150"
                       value="${tQhChargepath.sychargeno}" onblur="getUserNameByEmpnoUpdate(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="update_sychargename" name="update_sychargename" class="easyui-validatebox managerNameupdate"
                       data-options="width: 150"
                       readonly="true" value="${tQhChargepath.sychargename}"/></td>
        </tr>
        <tr>
            <td>申請人工號：</td>
            <td>
                <input id="update_applyempno" name="update_applyempno" class="easyui-validatebox" data-options="width: 150"
                       readonly="true" value=""/>
            </td>
            <td>姓名：</td>
            <td><input id="update_applyusername" name="update_applyusername" class="easyui-validatebox" data-options="width: 150"
                       readonly="true" value=""/></td>
        </tr>
    </table>
</div>
<script type="text/javascript">
    $(function(){
       //查詢修改的頁面如果申請人工號為空則設置為承辦人的信息
        if($("#update_applyempno").val()==''){
            $("#update_applyempno").val($("#addApplyNo").val());
            $("#update_applyusername").val($("#addApplyName").val());
        }
        queryFacnamByFacid($('#update_factoryid').val());
    });
    function getUserNameByEmpnoUpdate(obj) {
        if (obj.value != null && obj.value != "") {
            $.post(ctx + '/system/user/getUserInfo/', {
                empno: obj.value
            }, function (data) {
                if (!data) {
                    $.messager.alert("溫馨提示", "工號不存在", "error");
                    $("#" + obj.name.replace("no", "no")).val("");
                    $("#" + obj.name.replace("no", "name")).val("");
                } else {
                    if (obj.name == 'applyempno') {
                        $("#applyusername").val(data.empname);
                    } else {
                        $("#" + obj.name.replace("no", "name")).val(data.empname);
                    }
                }
            }, 'json');
        }else{
            $("#" + obj.name.replace("no", "name")).val('');
        }
    }
    //獲取廠區名稱
    function queryFacnamByFacid(facid) {
        $.get(ctx + '/tqhfactoryidconfig/getFactryNameById/' + facid, function(
            result) {
            $('#update_factoryname').val(result.factoryname);
        },'json');
    }
</script>
</body>
</html>