<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>專案請假申請表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/humanCapital/wfleaveprocesses.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfleaveprocesses/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfleaveprocessesEntity.id }"/>
    <input id="serialno" name="wfleaveprocesses.serialno" type="hidden" value="${wfleaveprocessesEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">專案請假申請表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfleaveprocessesEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfleaveprocessesEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfleaveprocessesEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfleaveprocessesEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfleaveprocessesEntity.makerno}/${wfleaveprocessesEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">請假申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">工號</td>
                            <td width="12%" class="td_style1">
                                <input id="applyno" name="wfleaveprocesses.applyno" class="easyui-validatebox inputCss"
                                       data-options="width: 80" readonly value="${wfleaveprocessesEntity.applyno}"/>
                                <input type="hidden" id="applyfactoryid" name="wfleaveprocesses.factoryid" value="${wfleaveprocessesEntity.factoryid}"/>
                            </td>
                            <td width="8%">姓名</td>
                            <td width="12%" class="td_style1">
                                <input id="applyname" name="wfleaveprocesses.applyname" class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfleaveprocessesEntity.applyname }"/>
                            </td>
                            <td width="8%">單位代碼</td>
                            <td width="12%" class="td_style1">
                                <input id="applydeptno" name="wfleaveprocesses.deptno" class="easyui-validatebox inputCss"
                                       data-options="width: 90" readonly value="${wfleaveprocessesEntity.deptno}"/>
                            </td>
                            <td width="8%">資位</td>
                            <td width="12%" class="td_style1">
                                <input id="leveltype" name="wfleaveprocesses.leveltype" class="easyui-validatebox inputCss"
                                       data-options="width: 90" readonly value="${wfleaveprocessesEntity.leveltype}"/>
                            </td>
                            <td width="8%">管理職</td>
                            <td width="12%" class="td_style1">
                                <input id="ismanager" name="wfleaveprocesses.ismanager" class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfleaveprocessesEntity.ismanager}" />
                            </td>
                        </tr>
                        <tr align="center">
                            <td>入廠日期</td>
                            <td class="td_style1">
                                <input id="indate" name="wfleaveprocesses.indate" class="easyui-validatebox inputCss"
                                       data-options="width: 100" readonly value="${wfleaveprocessesEntity.indate }"/>
                            </td>
                            <td>聯絡電話</td>
                            <td class="td_style1">
                                <input id="phone" name="wfleaveprocesses.phone" class="easyui-validatebox inputCss"
                                       style="width:100px;" readonly value="${wfleaveprocessesEntity.phone}"/>
                            </td>
                            <td>代理人工號</td>
                            <td class="td_style1">
                                <input id="supplyno" name="wfleaveprocesses.supplyno" class="easyui-validatebox inputCss"
                                       value="${wfleaveprocessesEntity.supplyno}" style="width:80px;" readonly/>
                            </td>
                            <td>代理人姓名</td>
                            <td colspan="3" class="td_style1">
                                <input id="supplyname" name="wfleaveprocesses.supplyname" class="easyui-validatebox inputCss"
                                       value="${wfleaveprocessesEntity.supplyname}" style="width:80px;" readonly/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">請假信息</td>
                        </tr>
                        <tr  align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;">
                                    <table id="leaveItem0Table" width="100%">
                                        <tr align="center">
                                            <td width="15%">假別</td>
                                            <td width="15%">開始日期</td>
                                            <td width="15%">結束日期</td>
                                            <td width="10%">連續天數</td>
                                            <td width="10%">實際天數</td>
                                            <td width="30%">請假原因</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${leaveitems0!=null&&leaveitems0.size()>0}">
                                            <c:forEach items="${leaveitems0}" var="leaveitems0" varStatus="status">
                                                <tr align="center" id="leave0Items${status.index+1}">
                                                    <td><input id="leave0_leavetype${status.index+1}" name="wfleaveitem0[${status.index}].leavetype"  data-options="required:true,validType:'comboxValidate[\'leave0_leavetype${status.index+1}\',\'请选择假別\']',textField:'label',editable:false,onBeforeLoad:function(){leaveTypeList0(${status.index+1});}" style="width:150px;"
                                                               class="easyui-combobox" value="${leaveitems0.leavetype}" disabled /></td>
                                                    <td><input id="leave0_begintime${status.index+1}" name="wfleaveitem0[${status.index}].begintime" class="easyui-validatebox Wdate" style="width:150px"
                                                               value="<fmt:formatDate  pattern="yyyy/MM/dd" value="${leaveitems0.begintime}"/>" readonly /></td>
                                                    <td><input id="leave0_endtime${status.index+1}" name="wfleaveitem0[${status.index}].endtime" class="easyui-validatebox Wdate" style="width:150px"
                                                               value="<fmt:formatDate  pattern="yyyy/MM/dd" value="${leaveitems0.endtime}"/>" readonly /></td>
                                                    <td><input id="leave0_linkdays${status.index+1}" name="wfleaveitem0[${status.index}].linkdays"
                                                               class="easyui-validatebox" style="width:60px;text-align: center;"  value="${leaveitems0.linkdays}" readonly/></td>
                                                    <td><input id="leave0_realdays${status.index+1}" name="wfleaveitem0[${status.index}].realdays"
                                                               class="easyui-validatebox" style="width:60px;text-align: center;" value="${leaveitems0.realdays}" readonly/></td>
                                                    <td>${leaveitems0.leavereason}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                        <tr class="nottr0" align="center">
                                            <td colspan="3" style="text-align: center;">合計:&nbsp;&nbsp;</td>
                                            <td>
                                                <input id="link0_days" name="" class="easyui-validatebox inputCss" style="text-align: center;"
                                                       data-options="width:100"  readonly value=""/>

                                            </td>
                                            <td>
                                                <input id="real0_days" name="" class="easyui-validatebox inputCss" style="text-align: center;"
                                                       data-options="width:100" readonly value=""/>
                                            </td>
                                            <td colspan="2">&nbsp;</td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">附件</td>
                            <td width="92%" colspan="9" class="td_style1">
                                <input type="hidden" id="attachids" name="wfleaveprocesses.attachids" value="${wfleaveprocessesEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">歷史請假記錄（與本次請假連續之請假信息）</td>
                        </tr>
                        <tr  align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;">
                                    <input id="leaveItem1TableIndex" type="hidden"
                                           value="<c:if test="${leaveitems1!=null && leaveitems1.size()>0}">${leaveitems1.size() +1}</c:if>
                                        <c:if test="${leaveitems1==null}">1</c:if>">
                                    </input>
                                    <table id="leaveItem1Table" width="100%">
                                        <tr align="center">
                                            <td width="15%">假別</td>
                                            <td width="15%">開始日期</td>
                                            <td width="15%">結束日期</td>
                                            <td width="10%">連續天數</td>
                                            <td width="10%">實際天數</td>
                                            <td width="30%">請假原因</td>
                                        </tr>
                                        <tbody id="info_Body1">
                                        <c:if test="${leaveitems1!=null&&leaveitems1.size()>0}">
                                            <c:forEach items="${leaveitems1}" var="leaveitems1" varStatus="status">
                                                <tr align="center" id="leave1Items${status.index+1}">
                                                    <td><input id="leave1_leavetype${status.index+1}" name="wfleaveitem1[${status.index}].leavetype" panelHeight="400" data-options="required:true,validType:'comboxValidate[\'leave1_leavetype${status.index+1}\',\'请选择假別\']',textField:'label',editable:false,onBeforeLoad:function(){leaveTypeList1(${status.index+1});}" style="width:150px;"
                                                               class="easyui-combobox" editable="false" disabled value="${leaveitems1.leavetype}"/></td>
                                                    <td><input id="leave1_begintime${status.index+1}" name="wfleaveitem1[${status.index}].begintime" class="easyui-validatebox Wdate" style="width:150px"
                                                               value="<fmt:formatDate  pattern="yyyy/MM/dd" value="${leaveitems1.begintime}"/>" readonly /></td>
                                                    <td><input id="leave1_endtime${status.index+1}" name="wfleaveitem1[${status.index}].endtime" class="easyui-validatebox Wdate" style="width:150px"
                                                               value="<fmt:formatDate  pattern="yyyy/MM/dd" value="${leaveitems1.endtime}"/>" readonly /></td>
                                                    <td><input id="leave1_linkdays${status.index+1}" name="wfleaveitem1[${status.index}].linkdays"
                                                               class="easyui-validatebox" style="width:60px;text-align: center;" value="${leaveitems1.linkdays}" readonly/></td>
                                                    <td><input id="leave1_realdays${status.index+1}" name="wfleaveitem1[${status.index}].realdays"
                                                               class="easyui-validatebox" style="width:60px;text-align: center;" value="${leaveitems1.realdays}" readonly/></td>
                                                    <td>${leaveitems1.leavereason}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                        <tr class="nottr1" align="center">
                                            <td colspan="3" style="text-align: center;">合計:&nbsp;&nbsp;</td>
                                            <td>
                                                <input id="link1_days" name="" class="easyui-validatebox inputCss" style="text-align: center;"
                                                       data-options="width:100" readonly value=""/>

                                            </td>
                                            <td>
                                                <input id="real1_days" name="" class="easyui-validatebox inputCss" style="text-align: center;"
                                                       data-options="width:100" readonly value=""/>
                                            </td>
                                            <td colspan="2">&nbsp;</td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','專案請假申請表');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfleaveprocessesEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfleaveprocessesEntity.workstatus!=null&&wfleaveprocessesEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<div id="dlg"></div>
</body>
</html>