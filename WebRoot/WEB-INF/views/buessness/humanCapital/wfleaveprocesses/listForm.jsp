<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>專案請假申請表</title>
    <script type="text/javascript">var ctx = "${pageContext.request.contextPath}";</script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/humanCapital/wfleaveprocesses.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfleaveprocesses/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfleaveprocessesEntity.id }"/>
    <input id="serialno" name="wfleaveprocesses.serialno" type="hidden" value="${wfleaveprocessesEntity.serialno }"/>
    <input id="makerno" name="wfleaveprocesses.makerno" type="hidden" value="${wfleaveprocessesEntity.makerno }"/>
    <input id="makername" name="wfleaveprocesses.makername" type="hidden" value="${wfleaveprocessesEntity.makername }"/>
    <input id="makerdeptno" name="wfleaveprocesses.makerdeptno" type="hidden" value="${wfleaveprocessesEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfleaveprocesses.makerfactoryid" type="hidden" value="${wfleaveprocessesEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">專案請假申請表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfleaveprocessesEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfleaveprocessesEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfleaveprocessesEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfleaveprocessesEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfleaveprocessesEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfleaveprocessesEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfleaveprocessesEntity.makerno}/${wfleaveprocessesEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">請假申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">工號&nbsp;<font color="red">*</font></td>
                            <td width="12%" class="td_style1">
                                <input id="applyno" name="wfleaveprocesses.applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfleaveprocessesEntity.applyno}" onblur="queryUserInfo();"/>
                                <input type="hidden" id="applyfactoryid" name="wfleaveprocesses.factoryid" value="${wfleaveprocessesEntity.factoryid}"/>
                            </td>
                            <td width="8%">姓名</td>
                            <td width="12%" class="td_style1">
                                <input id="applyname" name="wfleaveprocesses.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfleaveprocessesEntity.applyname }"/>
                            </td>
                            <td width="8%">單位代碼</td>
                            <td width="12%" class="td_style1">
                                <input id="applydeptno" name="wfleaveprocesses.deptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       value="${wfleaveprocessesEntity.deptno}" readonly/>
                            </td>
                            <td width="8%">資位</td>
                            <td width="12%" class="td_style1">
                                <input id="leveltype" name="wfleaveprocesses.leveltype"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       value="${wfleaveprocessesEntity.leveltype }" readonly/>
                            </td>
                            <td width="8%">管理職</td>
                            <td width="12%" class="td_style1">
                                <input id="ismanager" name="wfleaveprocesses.ismanager"
                                       class="easyui-validatebox inputCss" data-options="width:80"
                                       value="${wfleaveprocessesEntity.ismanager }" readonly/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>入廠日期</td>
                            <td class="td_style1">
                                <input id="indate" name="wfleaveprocesses.indate" class="easyui-validatebox inputCss"
                                       data-options="width: 100"
                                       value="${wfleaveprocessesEntity.indate }"/>
                            </td>
                            <td>聯絡電話&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="phone" name="wfleaveprocesses.phone" class="easyui-validatebox"
                                       style="width:100px;" value="${wfleaveprocessesEntity.phone}" data-options="required:true"/>
                            </td>
                            <td>代理人工號&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="supplyno" name="wfleaveprocesses.supplyno" class="easyui-validatebox"
                                       value="${wfleaveprocessesEntity.supplyno}" style="width:80px;"
                                       data-options="required:true" onblur="queryUserInfo2();"/>
                            </td>
                            <td>代理人姓名</td>
                            <td colspan="3" class="td_style1">
                                <input id="supplyname" name="wfleaveprocesses.supplyname" class="easyui-validatebox inputCss"
                                       value="${wfleaveprocessesEntity.supplyname}" style="width:80px;"
                                       data-options="required:true"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">請假信息</td>
                        </tr>
                        <tr  align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <input id="leaveItem0TableIndex" type="hidden"
                                           value="<c:if test="${leaveitems0!=null && leaveitems0.size()>0}">${leaveitems0.size() +1}</c:if>
                                        <c:if test="${leaveitems0.size()==0 || leaveitems0==null}">2</c:if>">
                                    </input>
                                    <table id="leaveItem0Table" width="100%">
                                        <tr align="center">
                                            <td width="15%">假別&nbsp;<font color="red">*</font></td>
                                            <td width="15%">開始日期&nbsp;<font color="red">*</font></td>
                                            <td width="15%">結束日期&nbsp;<font color="red">*</font></td>
                                            <td width="10%">連續天數&nbsp;<font color="red">*</font></td>
                                            <td width="10%">實際天數&nbsp;<font color="red">*</font></td>
                                            <td width="30%">請假原因&nbsp;<font color="red">*</font></td>
                                            <td width="5%">&nbsp;操作&nbsp;</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${leaveitems0!=null&&leaveitems0.size()>0}">
                                            <c:forEach items="${leaveitems0}" var="leaveitems0" varStatus="status">
                                                <tr align="center" id="leave0Items${status.index+1}">
                                                    <td>
                                                        <input id="leave0_leavetype${status.index+1}" name="wfleaveitem0[${status.index}].leavetype"  data-options="required:true,validType:'comboxValidate[\'leave0_leavetype${status.index+1}\',\'请选择假別\']',textField:'label',editable:false,onBeforeLoad:function(){leaveTypeList0(${status.index+1});}" style="width:150px;"
                                                               class="easyui-combobox" value="${leaveitems0.leavetype}"/></td>
                                                    <td><input id="leave0_begintime${status.index+1}" name="wfleaveitem0[${status.index}].begintime" class="easyui-validatebox Wdate" data-options="width:150,required:true,prompt:'请选择开始时间'" style="width:150px"
                                                               value="<fmt:formatDate  pattern="yyyy/MM/dd" value="${leaveitems0.begintime}"/>" onclick="WdatePicker({onpicked:function(){leave0_endtime${status.index+1}.click()},doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy/MM/dd'})"
                                                               onchange="countWorkdays0(${status.index+1})"/></td>
                                                    <td><input id="leave0_endtime${status.index+1}" name="wfleaveitem0[${status.index}].endtime" class="easyui-validatebox Wdate" data-options="width:150,required:true,prompt:'请选择結束时间'" style="width:150px"
                                                               value="<fmt:formatDate  pattern="yyyy/MM/dd" value="${leaveitems0.endtime}"/>" onclick="WdatePicker({doubleCalendar:true,skin:'whyGreen',minDate:'#F{$dp.$D(\'leave0_begintime${status.index+1}\')}',dateFmt:'yyyy/MM/dd'})"
                                                               onchange="countWorkdays0(${status.index+1})"/></td>
                                                    <td><input id="leave0_linkdays${status.index+1}" name="wfleaveitem0[${status.index}].linkdays"
                                                               class="easyui-validatebox" style="width:60px;text-align: center;" onchange="countAll();" value="${leaveitems0.linkdays}"/></td>
                                                    <td><input id="leave0_realdays${status.index+1}" name="wfleaveitem0[${status.index}].realdays"
                                                               class="easyui-validatebox" style="width:60px;text-align: center;" onchange="countAll();" value="${leaveitems0.realdays}"/></td>
                                                    <td><input id="leave0_leavereason${status.index+1}" name="wfleaveitem0[${status.index}].leavereason"
                                                               class="easyui-validatebox" data-options="required:true" style="width:300px;text-align: center;" value="${leaveitems0.leavereason}"/></td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="leave0deltr(${status.index+1});return false;"/>
                                                        <input id="leave0_shunxu${status.index+1}" type="hidden" name="wfleaveitem0[${status.index}].shunxu" value="${status.index+1}"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${leaveitems0.size()==0 || leaveitems0==null}">
                                            <tr align="center" id="leave0Items1">
                                                <td>
                                                    <input id="leave0_leavetype1" name="wfleaveitem0[0].leavetype" data-options="required:true,validType:'comboxValidate[\'leave0_leavetype1\',\'请选择假別\']',textField:'label',editable:false,onBeforeLoad:function(){leaveTypeList0(1);}" style="width:150px;"
                                                           class="easyui-combobox" editable="false" value=""/>
                                                </td>
                                                <td>
                                                    <input id="leave0_begintime1" name="wfleaveitem0[0].begintime" class="easyui-validatebox Wdate"
                                                           data-options="width:150,required:true,prompt:'请选择开始时间'" style="width:150px" value=""
                                                           onclick="WdatePicker({onpicked:function(){leave0_endtime1.click()},doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy/MM/dd'})"
                                                           onchange="countWorkdays0(1)" />
                                                </td>
                                                <td>
                                                    <input id="leave0_endtime1" name="wfleaveitem0[0].endtime" class="easyui-validatebox Wdate"
                                                           data-options="width:150,required:true,prompt:'请选择結束时间'" style="width:150px" value=""
                                                           onclick="WdatePicker({doubleCalendar:true,skin:'whyGreen',minDate:'#F{$dp.$D(\'leave0_begintime1\')}',dateFmt:'yyyy/MM/dd'})"
                                                           onchange="countWorkdays0(1)"/>
                                                </td>
                                                <td><input id="leave0_linkdays1" name="wfleaveitem0[0].linkdays" data-options="required:true"
                                                           class="easyui-validatebox" onchange="countAll();" style="width:60px;text-align: center;" value=""/></td>
                                                <td><input id="leave0_realdays1" name="wfleaveitem0[0].realdays" data-options="required:true"
                                                           class="easyui-validatebox" onchange="countAll();"style="width:60px;text-align: center;" value=""/></td>
                                                <td><input id="leave0_leavereason1" name="wfleaveitem0[0].leavereason"
                                                           class="easyui-validatebox" data-options="required:true" style="width:300px;text-align: center;" value=""/></td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="leave0deltr(1);return false;"/>
                                                    <input id="leave0_shunxu1" type="hidden" name="wfleaveitem0[0].shunxu" value="1"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr class="nottr0" align="center">
                                            <td colspan="3" style="text-align: center;">合計:&nbsp;&nbsp;</td>
                                            <td>
                                                <input id="link0_days" name="" class="easyui-validatebox inputCss" style="text-align: center;"
                                                       data-options="width:100"  readonly value=""/>

                                            </td>
                                            <td>
                                                <input id="real0_days" name="" class="easyui-validatebox inputCss" style="text-align: center;"
                                                       data-options="width:100" readonly value=""/>
                                            </td>
                                            <td colspan="2">&nbsp;</td>
                                        </tr>
                                        <tr align="left" class="nottr0">
                                            <td colspan="12" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="leave0ItemAdd" style="width:100px;float:left;" value="添加一筆"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">附件&nbsp;<font color="red">*</font></td>
                            <td width="92%" colspan="9" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="wfleaveprocesses.attachids" value="${wfleaveprocessesEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">歷史請假記錄（與本次請假連續之請假信息）</td>
                        </tr>
                        <tr  align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <input id="leaveItem1TableIndex" type="hidden"
                                           value="<c:if test="${leaveitems1!=null && leaveitems1.size()>0}">${leaveitems1.size() +1}</c:if>
                                        <c:if test="${leaveitems1.size()==0 || leaveitems1==null}">1</c:if>">
                                    </input>
                                    <table id="leaveItem1Table" width="100%">
                                        <tr align="center">
                                            <td width="15%">假別</td>
                                            <td width="15%">開始日期</td>
                                            <td width="15%">結束日期</td>
                                            <td width="10%">連續天數</td>
                                            <td width="10%">實際天數</td>
                                            <td width="30%">請假原因</td>
                                            <td width="5%">&nbsp;操作&nbsp;</td>
                                        </tr>
                                        <tbody id="info_Body1">
                                        <c:if test="${leaveitems1!=null&&leaveitems1.size()>0}">
                                            <c:forEach items="${leaveitems1}" var="leaveitems1" varStatus="status">
                                                <tr align="center" id="leave1Items${status.index+1}">
                                                    <td>
                                                        <input id="leave1_leavetype${status.index+1}" name="wfleaveitem1[${status.index}].leavetype" data-options="required:true,validType:'comboxValidate[\'leave1_leavetype${status.index+1}\',\'请选择假別\']',textField:'label',editable:false,onBeforeLoad:function(){leaveTypeList1(${status.index+1});}" style="width:150px;"
                                                               class="easyui-combobox" editable="false" value="${leaveitems1.leavetype}"/></td>
                                                    <td><input id="leave1_begintime${status.index+1}" name="wfleaveitem1[${status.index}].begintime" class="easyui-validatebox Wdate" data-options="width:150,required:true,prompt:'请选择开始时间'" style="width:150px"
                                                               value="<fmt:formatDate  pattern="yyyy/MM/dd" value="${leaveitems1.begintime}"/>" onclick="WdatePicker({onpicked:function(){leave1_endtime${status.index+1}.click()},doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy/MM/dd'})"
                                                               onchange="countWorkdays1(${status.index+1})"/></td>
                                                    <td><input id="leave1_endtime${status.index+1}" name="wfleaveitem1[${status.index}].endtime" class="easyui-validatebox Wdate" data-options="width:150,required:true,prompt:'请选择結束时间'" style="width:150px"
                                                               value="<fmt:formatDate  pattern="yyyy/MM/dd" value="${leaveitems1.endtime}"/>" onclick="WdatePicker({doubleCalendar:true,skin:'whyGreen',minDate:'#F{$dp.$D(\'leave1_begintime${status.index+1}\')}',dateFmt:'yyyy/MM/dd'})"
                                                               onchange="countWorkdays1(${status.index+1})"/></td>
                                                    <td><input id="leave1_linkdays${status.index+1}" name="wfleaveitem1[${status.index}].linkdays"
                                                               class="easyui-validatebox" onchange="countAll();" style="width:60px;text-align: center;" value="${leaveitems1.linkdays}"/></td>
                                                    <td><input id="leave1_realdays${status.index+1}" name="wfleaveitem1[${status.index}].realdays"
                                                               class="easyui-validatebox" onchange="countAll();" style="width:60px;text-align: center;" value="${leaveitems1.realdays}"/></td>
                                                    <td><input id="leave1_leavereason${status.index+1}" name="wfleaveitem1[${status.index}].leavereason"
                                                               class="easyui-validatebox" data-options="required:true" style="width:300px;text-align: center;" value="${leaveitems1.leavereason}"/></td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="leave1deltr(${status.index+1});return false;"/>
                                                        <input id="leave1_shunxu${status.index+1}" type="hidden" name="wfleaveitem1[${status.index}].shunxu" value="${status.index+1}"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                        <tr class="nottr1" align="center">
                                            <td colspan="3" style="text-align: center;">合計:&nbsp;&nbsp;</td>
                                            <td>
                                                <input id="link1_days" name="" class="easyui-validatebox inputCss" style="text-align: center;"
                                                       data-options="width:100" readonly value=""/>

                                            </td>
                                            <td>
                                                <input id="real1_days" name="" class="easyui-validatebox inputCss" style="text-align: center;"
                                                       data-options="width:100" readonly value=""/>
                                            </td>
                                            <td colspan="2">&nbsp;</td>
                                        </tr>
                                        <tr align="left" class="nottr1">
                                            <td colspan="12" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="leave1ItemAdd" style="width:100px;float:left;" value="添加一筆"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_zhuananqingjiashenqing_v1','專案請假申請表','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="casechargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['casechargeno_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(127,'casechargeTable','casechargeno','casechargename',$('#applyfactoryid').val(),'wfleaveprocesses')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="casechargeno" name="wfleaveprocesses.casechargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['casechargeno']}" readonly
                                                               value="${wfleaveprocessesEntity.casechargeno }"/><c:if
                                                            test="${requiredMap['casechargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="casechargename" name="wfleaveprocesses.casechargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['casechargeno']}"
                                                                value="${wfleaveprocessesEntity.casechargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="xzchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['xzchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(128,'xzchargeno','xzchargename',$('#applyfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="xzchargeno" name="wfleaveprocesses.xzchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['xzchargeno']}" readonly
                                                               value="${wfleaveprocessesEntity.xzchargeno }"/><c:if
                                                            test="${requiredMap['xzchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="xzchargename" name="wfleaveprocesses.xzchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['xzchargeno']}"
                                                                value="${wfleaveprocessesEntity.xzchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl1Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno1_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(129,'yl1Table','ylno1','ylname1',$('#applyfactoryid').val(),'wfleaveprocesses')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno1" name="wfleaveprocesses.ylno1" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno1']}" readonly
                                                               value="${wfleaveprocessesEntity.ylno1 }"/><c:if
                                                            test="${requiredMap['ylno1'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname1" name="wfleaveprocesses.ylname1"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno1']}"
                                                                value="${wfleaveprocessesEntity.ylname1 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno2_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(130,'yl2Table','ylno2','ylname2',$('#applyfactoryid').val(),'wfleaveprocesses')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="wfleaveprocesses.ylno2" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}" readonly
                                                               value="${wfleaveprocessesEntity.ylno2 }"/><c:if
                                                            test="${requiredMap['ylno2'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname2" name="wfleaveprocesses.ylname2"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}"
                                                                value="${wfleaveprocessesEntity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#applyfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="wfleaveprocesses.kchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}" readonly
                                                               value="${wfleaveprocessesEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="wfleaveprocesses.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfleaveprocessesEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#applyfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="wfleaveprocesses.bchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}" readonly
                                                               value="${wfleaveprocessesEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="wfleaveprocesses.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfleaveprocessesEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#applydeptno').val(),'cchargeno','cchargename',$('#applyfactoryid').val(),'wfleaveprocesses')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wfleaveprocesses.cchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}" readonly
                                                               value="${wfleaveprocessesEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wfleaveprocesses.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfleaveprocessesEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applyfactoryid').val(),'wfleaveprocesses')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wfleaveprocesses.zchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}" readonly
                                                               value="${wfleaveprocessesEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="wfleaveprocesses.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfleaveprocessesEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#applydeptno').val(),'zcchargeno','zcchargename',$('#applyfactoryid').val(),'wfleaveprocesses')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="wfleaveprocesses.zcchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}" readonly
                                                               value="${wfleaveprocessesEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="wfleaveprocesses.zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfleaveprocessesEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#applydeptno').val(),'pcchargeno','pcchargename',$('#applyfactoryid').val(),'wfleaveprocesses')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="wfleaveprocesses.pcchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}" readonly
                                                               value="${wfleaveprocessesEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="wfleaveprocesses.pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wfleaveprocessesEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="rzbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['rzbchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(18,'rzbchargeTable','rzbchargeno','rzbchargename',$('#applyfactoryid').val(),'wfleaveprocesses')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="rzbchargeno" name="wfleaveprocesses.rzbchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['rzbchargeno']}" readonly
                                                               value="${wfleaveprocessesEntity.rzbchargeno }"/><c:if
                                                            test="${requiredMap['rzbchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="rzbchargename" name="wfleaveprocesses.rzbchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['rzbchargeno']}"
                                                                value="${wfleaveprocessesEntity.rzbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl3Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno3_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(28,'yl3Table','ylno3','ylname3',$('#applyfactoryid').val(),'wfleaveprocesses')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno3" name="wfleaveprocesses.ylno3" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno3']}" readonly
                                                               value="${wfleaveprocessesEntity.ylno3 }"/><c:if
                                                            test="${requiredMap['ylno3'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname3" name="wfleaveprocesses.ylname3"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno3']}"
                                                                value="${wfleaveprocessesEntity.ylname3 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl4Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno4_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(29,'yl4Table','ylno4','ylname4',$('#applyfactoryid').val(),'wfleaveprocesses')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno4" name="wfleaveprocesses.ylno4" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno4']}" readonly
                                                               value="${wfleaveprocessesEntity.ylno4 }"/><c:if
                                                            test="${requiredMap['ylno4'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname4" name="wfleaveprocesses.ylname4"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno4']}"
                                                                value="${wfleaveprocessesEntity.ylname4 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="onlyKchargeSignle" value="1" />
    <div id="win"></div>
</form>
</body>
</html>