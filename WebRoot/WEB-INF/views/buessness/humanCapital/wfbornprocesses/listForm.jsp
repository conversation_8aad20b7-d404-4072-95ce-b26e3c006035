<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>生育（初育）禮金申請</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
	type="text/javascript"></script>
</head>
<body>
	<form id="mainform" action="${ctx}/wfbornprocesses/${action}"
		method="post">
		<input id="ids" name="ids" type="hidden"
			value="${wfbornprocessesEntity.id }" /> <input id="serialno"
			name="serialno" type="hidden"
			value="${wfbornprocessesEntity.serialno }" /> <input id="makerno"
			name="makerno" type="hidden"
			value="${wfbornprocessesEntity.makerno }" /> <input id="makername"
			name="makername" type="hidden"
			value="${wfbornprocessesEntity.makername }" /> <input
			id="makerdeptno" name="makerdeptno" type="hidden"
			value="${wfbornprocessesEntity.makerdeptno }" /> <input
			id="makerfactoryid" name="makerfactoryid" type="hidden"
			value="${wfbornprocessesEntity.makerfactoryid }" />
		<div class="commonW">
			<div class="headTitle">生育（初育）禮金申請</div>
			<div class="position_L">
				任務編碼：<span style="color:#999;"> <c:choose>
						<c:when test="${wfbornprocessesEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
						<c:otherwise>
                           ${wfbornprocessesEntity.serialno}
                       </c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_L1 margin_L">
				填單時間：<span style="color:#999;"> <c:choose>
						<c:when test="${wfbornprocessesEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
						<c:otherwise>
							<input style="border:0px" style="width: 120px" readonly="true"
								value="<fmt:formatDate value='${wfbornprocessesEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>" />
						</c:otherwise>
					</c:choose>
				</span>
			</div>
			<c:if test="${empty wfbornprocessesEntity.makerno}">
				<div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
			</c:if>
			<c:if test="${not empty wfbornprocessesEntity.makerno}">
				<div class="position_R margin_R">填單人：${wfbornprocessesEntity.makerno}/${wfbornprocessesEntity.makername}</div>
			</c:if>
			<div class="clear"></div>
			<table class="formList">
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">申請人基本資料</td>
							</tr>
							<tr align="center">
								<td width="9%">工號&nbsp;<font color="red">*</font></td>
								<td width="10%" align="left"><input id="dealno" name="dealno"
									onchange="queryUserInfo(this)" class="easyui-validatebox"
									data-options="width: 100,required:true"
									value="${wfbornprocessesEntity.dealno}" /> <input
									id="dealfactoryid" name="dealfactoryid"
									class="easyui-validatebox"
									data-options="width: 150,required:true" type="hidden" readonly
									value="${wfbornprocessesEntity.dealfactoryid}" /> <input
									id="applyidcard" name="applyidcard" class="easyui-validatebox"
									data-options="width: 150,required:true" type="hidden" readonly
									value="${wfbornprocessesEntity.applyidcard}" /></td>
								<td width="6%">姓名</td>
								<td align="left"><input id="dealname" name="dealname" readonly
									class="easyui-validatebox inputCss" data-options="width: 100"
									value="${wfbornprocessesEntity.dealname}" /></td>
								<td width="10%">性別</td>
								<td align="left"><input id="issex" name="issex" readonly
									class="easyui-validatebox inputCss" data-options="width: 100"
									value="${wfbornprocessesEntity.issex}" /></td>
								<%--<td>出生日期</td>
								<td><input id="birthday" name="birthday" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfbornprocessesEntity.birthday}" /></td>--%>
								<td width="6%">資位</td>
								<td align="left" colspan="3"><input id="leveltype" name="leveltype"
									readonly type="hidden" class="easyui-validatebox inputCss"
									data-options="width: 150"
									value="${wfbornprocessesEntity.leveltype}" /> <input
									id="leveltypename" name="leveltypename" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfbornprocessesEntity.leveltypename}" />
									<input id="birthday" name="birthday" readonly type="hidden"
										   class="easyui-validatebox inputCss" data-options="width: 150"
										   value="${wfbornprocessesEntity.birthday}" /></td>
							</tr>
							<tr align="center">
								<td>單位代碼</td>
								<td align="left"><input id="dealdeptno" name="dealdeptno" readonly
									class="easyui-validatebox inputCss" data-options="width: 100"
									value="${wfbornprocessesEntity.dealdeptno}" /></td>
								<td>單位名稱</td>
								<td colspan="7" align="left"><input id="dealdeptname"
									name="dealdeptname" class="easyui-validatebox"
									data-options="width: 450,required:true"
									value="${wfbornprocessesEntity.dealdeptname}" /></td>
							</tr>
							<tr align="center">
								<td>管理職</td>
								<td align="left"><input id="ismanager" name="ismanager"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 100"
									value="${wfbornprocessesEntity.ismanager}" /></td>
								<td>入集團日期</td>
								<td align="left"><input id="indate" name="indate"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 100"
									value="${wfbornprocessesEntity.indate}" /></td>
								<td>聯絡電話&nbsp;<font color="red">*</font></td>
								<td colspan="5" align="left"><input id="phone" name="phone"
									class="easyui-validatebox"
									data-options="width: 150,required:true"
									value="${wfbornprocessesEntity.phone}" /></td>
							</tr>
							<tr align="center">
								<td colspan="10" class="td_style1">申請條件</td>
							</tr>
							<tr align="center">
							    <td>子女姓名&nbsp;<font color="red">*</font></td>
								<td align="left"><input id="childname" name="childname"
									class="easyui-validatebox"
									data-options="width: 100,required:true"
									value="${wfbornprocessesEntity.childname}" /></td>
								<td>子女出生日期&nbsp;<font color="red">*</font></td>
								<td align="left"><input id="childbirthday" name="childbirthday" class="Wdate"
									onblur="getAge()" onchange="checkGrade()" data-options="width:100,required:true"
									style="width:100px"
									value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${wfbornprocessesEntity.childbirthday}"/>"
								    onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'%y-%M-%d',minDate:'#F{$dp.$DV(\'%y-%M-%d\',{M:-7});}'})" /></td>
								<td>上年績效符合條件</td>
								<td align="left"><input id="commentresultnew" name="commentresultnew"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 50"
									value="${wfbornprocessesEntity.commentresultnew}" /></td>
								<td>年資</td>
								<td align="left"><input id="worktime" name="worktime"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 80"
									value="${wfbornprocessesEntity.worktime}" /></td>
								<td>年齡</td>
								<td align="left"><input id="age" name="age"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 80" value="${wfbornprocessesEntity.age}" /></td>
							</tr>
							<tr align="center">
								<td colspan="10" class="td_style1">配偶基本資料</td>
							</tr>
							<tr align="center">
								<td><input type="radio" id="emp" name="ismateradio"
									onclick="check()" value="0" />集團員工</td>
								<td>工號&nbsp;<span id="is1"></span></td>
								<td align="left"><input id="mateempno" name="mateempno"
									onchange="checkExsitIdCard(this)" class="easyui-validatebox"
									data-options="width: 100,required:true"
									value="${wfbornprocessesEntity.mateempno}" /></td>
								<td>身份證號&nbsp;<span id="is2"></span></td>
								<td align="left" colspan="2">&nbsp;&nbsp; <input id="mateidcard"
									name="mateidcard" onchange = "checkExsitIdCard(this)" class="easyui-validatebox"
									data-options="width: 150,required:true,validType:'idCode[\'mateidcard\']'"
									value="${wfbornprocessesEntity.mateidcard}" />
								</td>
								<td>事業群&nbsp;<span id="is3"></span></td>
								<td align="left" colspan="3">&nbsp;&nbsp; <input id="enterprise"
									name="enterprise" class="easyui-validatebox"
									data-options="width: 150,required:true"
									value="${wfbornprocessesEntity.enterprise}" />
								</td>
							</tr>
							<tr align="center">
								<td><input type="radio" id="notemp" name="ismateradio"
									onclick="check()" value="1" />非集團員工 
									<input id="ismate" name ="ismate" type="hidden" value="${wfbornprocessesEntity.ismate}"/></td>
								<td>姓名&nbsp;<span id="not1"></span></td>
								<td align="left"><input id="mateempname" name="mateempname"
									class="easyui-validatebox"
									data-options="width: 100,required:true"
									value="${wfbornprocessesEntity.mateempname}" /></td>
								<td>身份證號&nbsp;<span id="not2"></span></td>
								<td align="left" colspan="2" style="border-right-width: 0;">&nbsp;&nbsp;
									<input id="idcard" name="idcard" onchange = "checkExsitIdCard(this)" class="easyui-validatebox"
									data-options="width: 150,required:true,validType:'idCode[\'idcard\']'"
									value="${wfbornprocessesEntity.idcard}" />
								</td>
								<td colspan="4" style="border-left-width: 0;"></td>
							</tr>
							<tr align="center">
								<td>核給金額</br>（單位:RMB元）
								</td>
								<td colspan="9" align="left">&nbsp;&nbsp; <input
									id="moneycount" name="moneycount"
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfbornprocessesEntity.moneycount}" readonly></input>
								</td>
							</tr>
							<!-- 						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList"> -->
							<tr align="center">
								<td>附件&nbsp;<font color="red">*</font></td>
								<td colspan="9" class="td_style1">
									<span class="sl-custom-file">
										<input type="button" value="点击上传文件" class="btn-file" />
										<input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file" />
									</span>
									<input type="hidden" id="attachids" name="attachids" value="${wfbornprocessesEntity.attachids }" />
									<div id="dowloadUrl">
										<c:forEach items="${file}" varStatus="i" var="item">
											<div id="${item.id}"
												style="line-height:30px;margin-left:5px;" class="float_L">
												<div class="float_L">
													<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
												</div>
												<div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
											</div>
										</c:forEach>
									</div>
								</td>
							</tr>
							<tr align="center">
								<td>備註</td>
								<td colspan="9" style="text-align:left;">
									1.本表依禮金核給管理作業規範辦理之。</br>
									2.附證明文件（審原件留複印件）。</br>
									3.年資及考績不符者，不享有禮金申請資格。</br>
									4.當月表單簽核完成者當月薪資作業發放，反之次月薪資作業發放，禮金并入薪資依法納稅。
								</td>
							</tr>
							<tr>
								<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
									<a href="javascript:void(0)"
									onclick="showWfImag('dzqh_shengyulijinshenqing_v2','初育禮金申請','');">點擊查看簽核流程圖</a>
								</th>
							</tr>
							<tr>
								<td colspan="10" style="text-align:left;">
									<table class="flowList"
										style="margin-left:5px;margin-top:5px;width:99%">
										<tr>
											<td style="border:none">
												<table width="18%" style="float: left;margin-left: 5px;"
													id="qachargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">${requiredMap['qachargeno_name']}</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole4(1,'qachargeno','qachargename',$('#dealfactoryid').val())"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="qachargeno" name="qachargeno"
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['qachargeno']}"
															readonly value="${wfbornprocessesEntity.qachargeno }" />
															<c:if test="${requiredMap['qachargeno'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="qachargename" name="qachargename" readonly
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['qachargeno']}"
															value="${wfbornprocessesEntity.qachargename }" /></td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="kchargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealfactoryid').val())"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="kchargeno" name="kchargeno"
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['kchargeno']}"
															readonly value="${wfbornprocessesEntity.kchargeno }" />
															<c:if test="${requiredMap['kchargeno'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="kchargename" name="kchargename" readonly
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['kchargeno']}"
															value="${wfbornprocessesEntity.kchargename }" /></td>
													</tr>
												</table>

												<table width="18%" style="float: left;margin-left: 5px;"
													id="bchargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole3('bchargeTable',$('#dealdeptno').val(),'bchargeno','bchargename',$('#dealfactoryid').val(),null)"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="bchargeno" name="bchargeno"
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['bchargeno']}"
															readonly value="${wfbornprocessesEntity.bchargeno }" />
															<c:if test="${requiredMap['bchargeno'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="bchargename" name="bchargename" readonly
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['bchargeno']}"
															value="${wfbornprocessesEntity.bchargename }" /></td>
													</tr>
												</table>

												<table width="18%" style="float: left;margin-left: 5px;"
													id="cchargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole3('cchargeTable',$('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').val(),null)"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="cchargeno" name="cchargeno"
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['cchargeno']}"
															readonly value="${wfbornprocessesEntity.cchargeno }" />
															<c:if test="${requiredMap['cchargeno'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="cchargename" name="cchargename" readonly
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['cchargeno']}"
															value="${wfbornprocessesEntity.cchargename }" /></td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="zchargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealfactoryid').val(),null)"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="zchargeno" name="zchargeno"
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['zchargeno']}"
															readonly value="${wfbornprocessesEntity.zchargeno }" />
															<c:if test="${requiredMap['zchargeno'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="zchargename" name="zchargename" readonly
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['zchargeno']}"
															value="${wfbornprocessesEntity.zchargename }" /></td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td style="border:none">
												<table width="18%" style="float: left;margin-left: 5px;"
													id="zcchargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealfactoryid').val(),null)"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="zcchargeno" name="zcchargeno"
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['zcchargeno']}"
															readonly value="${wfbornprocessesEntity.zcchargeno }" />
															<c:if test="${requiredMap['zcchargeno'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="zcchargename" name="zcchargename" readonly
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['zcchargeno']}"
															value="${wfbornprocessesEntity.zcchargename }" /></td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="pcchargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole3('pcchargeTable',$('#dealdeptno').val(),'pcchargeno','pcchargename',$('#dealfactoryid').val(),null)"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="pcchargeno" name="pcchargeno"
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['pcchargeno']}"
															readonly value="${wfbornprocessesEntity.pcchargeno }" />
															<c:if test="${requiredMap['pcchargeno'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="pcchargename" name="pcchargename" readonly
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['pcchargeno']}"
															value="${wfbornprocessesEntity.pcchargename }" /></td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="rzbchargeTable">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">${requiredMap['rzbchargeno_name']}</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole2(18,'rzbchargeTable','rzbchargeno','rzbchargename',$('#dealfactoryid').val(),null)"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="rzbchargeno" name="rzbchargeno"
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['rzbchargeno']}"
															readonly value="${wfbornprocessesEntity.rzbchargeno }" />
															<c:if test="${requiredMap['rzbchargeno'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="rzbchargename" name="rzbchargename" readonly
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['rzbchargeno']}"
															value="${wfbornprocessesEntity.rzbchargename }" /></td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="yl1Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">${requiredMap['ylno1_name']}</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole2(28,'yl1Table','ylno1','ylname1',$('#dealfactoryid').val(),null)"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno1" name="ylno1"
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['ylno1']}"
															readonly value="${wfbornprocessesEntity.ylno1 }" /> <c:if
																test="${requiredMap['ylno1'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname1" name="ylname1" readonly
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['ylno1']}"
															value="${wfbornprocessesEntity.ylname1 }" /></td>
													</tr>
												</table>
												<table width="18%" style="float: left;margin-left: 5px;"
													id="yl2Table">
													<tr>
														<td>
															<table width="100%">
																<tr>
																	<td style="border: none;text-align: right;">${requiredMap['ylno2_name']}</td>
																	<td style="border: none;">
																		<div class="float_L qhUserIcon"
																			onclick="selectRole2(29,'yl2Table','ylno2','ylname2',$('#dealfactoryid').val(),null)"></div>
																	</td>
																</tr>
															</table>
														</td>
													</tr>
													<tr>
														<td><input id="ylno2" name="ylno2"
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['ylno2']}"
															readonly value="${wfbornprocessesEntity.ylno2 }" /> <c:if
																test="${requiredMap['ylno2'].equals('true')}">
																<font color="red">*</font>
															</c:if> /<input id="ylname2" name="ylname2" readonly
															class="easyui-validatebox"
															data-options="width:80,required:${requiredMap['ylno2']}"
															value="${wfbornprocessesEntity.ylname2 }" /></td>
													</tr>
												</table>
											</td>
										</tr>
									</table>
								</td>
							</tr>

							<tr>
								<td colspan="10" style="text-align:left;">
									<table class="flowList"
										style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
										<tr>
											<td>簽核時間</td>
											<td>簽核節點</td>
											<td>簽核主管</td>
											<td>簽核意見</td>
											<td>批註</td>
											<td>簽核電腦IP</td>
										</tr>
									</table>
								</td>
							</tr>
							<tr>
								<td colspan="10"
									style="border:none;text-align:center;margin-top:10px"><a
									href="javascript:;" id="btnSave" class="easyui-linkbutton"
									data-options="iconCls:'icon-add'" style="width: 100px;"
									onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp; <a
									href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
									data-options="iconCls:'icon-ok'" style="width: 100px;"
									onclick="saveInfo(2);">提交</a></td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</div>
		<input type="hidden" id="deptNo" name="deptNo" value="" /> 
		<input type="hidden" id="chargeNo" name="chargeNo" value="" /> 
		<input type="hidden" id="chargeName" name="chargeName" value="" /> 
		<input type="hidden" id="factoryId" name="factoryId" value="" /> 
		<input type="hidden" id="dutyId" name="dutyId" value="" />
		<input type="hidden" id="onlyKchargeSignle" value="1" />
		<div id="win"></div>
	</form>
	<script
		src='${ctx}/static/js/humanCapital/wfbornprocesses.js?random=<%= Math.random()%>'></script>
</body>
</html>