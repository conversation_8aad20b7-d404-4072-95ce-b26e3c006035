<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>生育（初育）禮金申請</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<form id="mainform" action="${ctx}/wfbornprocesses/${action}"
		method="post">
		<!--
		        workflowid 流程編碼
    serialno 工單流水號
    processid 工單實例ID
    dealfactoryid 廠區代碼
    makerno 填單人
    makername 填單人名稱
    createtime 創建時間
    dealdeptno 單位代碼
    dealdeptname 單位名稱
    dealno 申請人工號
    dealname 申請人名稱
    issex 字典：DICT_ISSEX
    birthday 出生日期
    leveltype 字典：DICT_LEVELTYPE
    ismanager 管理職
    indate 入廠日期
    phone 聯繫電話
    worktime 年資
    age 年齡
    commentresult 字典：DICT_COMMENTRESULT
    childname 子女名稱
    childbirthday 子女出生日期
    ismate 字典：DICT_ISMATE
    mateempno 配偶工號
    enterprise 事業群
    mateempname 配偶姓名
    idcard 身份證號碼
    moneycount 核給金額
    attachids 附件ID
    rkchargeno 人資考績確認窗口
    rkchargename 人資考績確認窗口名稱
    qachargeno 人資禮金檢查窗口
    qachargename 人資禮金檢查窗口名稱
    kchargeno 課級主管
    kchargename 課級主管名稱
    bchargeno 部級主管
    bchargename 部級主管名稱
    cchargeno 廠級主管
    cchargename 廠級主管名稱
    zchargeno 製造處級主管
    zchargename 製造處級主管名稱
    zcchargeno 製造總處級主管
    zcchargename 製造總處級主管名稱
    pcchargeno 產品處級主管
    pcchargename 產品處級主管名稱
    rzbchargeno 人資部主管
    rzbchargename 人資部主管名稱
    workstatus 字典：DICT_WORKSTATUS
    complettime 完成時間
    makerfactoryid 填單人廠編碼
    applyidcard 申請人身份證號
    mateidcard 集團內配偶身份證號
    ylno1 產品群級主管
    ylname1 ${column.comments}
    ylno2 CAA最高管制主管
    ylname2 ${column.comments}
    makerdeptno 填單人單位代碼（舊翻新時新增）
    id 主鍵（舊翻新時新增）
    createBy 創建人（舊翻新時新增）
    createDate 創建時間（舊翻新時新增）
    updateBy 更新者（舊翻新時新增）
    updateDate 更新時間（舊翻新時新增）
    delFlag 刪除標識（舊翻新時新增）
    leveltypename 資位名稱
		   -->
		<input id="ids" name="ids" type="hidden"
			value="${wfbornprocessesEntity.id }" /> <input id="serialno"
			name="serialno" type="hidden"
			value="${wfbornprocessesEntity.serialno }" />
		<div class="commonW">
			<div class="headTitle">生育（初育）禮金申請</div>
			<div class="position_L">
				任務編碼：<span style="color:#999;"> <c:choose>
						<c:when test="${wfbornprocessesEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
						<c:otherwise>
                           ${wfbornprocessesEntity.serialno}
                       </c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_L1 margin_L">
				填單時間：<span style="color:#999;"> <c:choose>
						<c:when test="${wfbornprocessesEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
						<c:otherwise>
							<input style="border:0px" style="width: 120px" readonly="true"
								value="<fmt:formatDate value='${wfbornprocessesEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>" />
						</c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_R margin_R">填單人：${wfbornprocessesEntity.makerno}/${wfbornprocessesEntity.makername}</div>
			<div class="clear"></div>
			<table class="formList">
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td colspan="10" class="td_style1">申請人基本資料</td>
							</tr>
							<tr align="center">
								<td width="9%">工號</td>
								<td width="10%" align="left"><input id="dealno" name="dealno" readonly
									onchange="queryUserInfo(this)"
									class="easyui-validatebox  inputCss" data-options="width: 100"
									value="${wfbornprocessesEntity.dealno}" /> <input
									id="dealfactoryid" name="dealfactoryid"
									class="easyui-validatebox" data-options="width: 150"
									type="hidden" readonly
									value="${wfbornprocessesEntity.dealfactoryid}" /> <input
									id="applyidcard" name="applyidcard" class="easyui-validatebox"
									data-options="width: 150" type="hidden" readonly
									value="${wfbornprocessesEntity.applyidcard}" /></td>
								<td width="6%">姓名</td>
								<td align="left"><input id="dealname" name="dealname" readonly
									class="easyui-validatebox inputCss" data-options="width: 100"
									value="${wfbornprocessesEntity.dealname}" /></td>
								<td width="10%">性別</td>
								<td align="left"><input id="issex" name="issex" readonly
									class="easyui-validatebox inputCss" data-options="width: 100"
									value="${wfbornprocessesEntity.issex}" /></td>
								<%--<td>出生日期</td>
								<td><input id="birthday" name="birthday" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfbornprocessesEntity.birthday}" /></td>--%>
								<td width="6%">資位</td>
								<td align="left" colspan="3"><input id="leveltype" name="leveltype" readonly
									type="hidden" class="easyui-validatebox inputCss"
									data-options="width: 150"
									value="${wfbornprocessesEntity.leveltype}" /> <input
									id="leveltypename" name="leveltypename" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfbornprocessesEntity.leveltypename}" />
									<input id="birthday" name="birthday" readonly type="hidden"
										   class="easyui-validatebox inputCss" data-options="width: 150"
										   value="${wfbornprocessesEntity.birthday}" />
								</td>
							</tr>
							<tr align="center">
								<td>單位代碼</td>
								<td align="left"><input id="dealdeptno" name="dealdeptno" readonly
									class="easyui-validatebox inputCss" data-options="width: 100"
									value="${wfbornprocessesEntity.dealdeptno}" /></td>
								<td>單位名稱</td>
								<td colspan="7" align="left"><input id="dealdeptname"
									name="dealdeptname" class="easyui-validatebox inputCss"
									data-options="width: 450"
									value="${wfbornprocessesEntity.dealdeptname}" /></td>
							</tr>
							<tr align="center">
								<td>管理職</td>
								<td align="left"><input id="ismanager" name="ismanager"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 100"
									value="${wfbornprocessesEntity.ismanager}" /></td>
								<td>入集團日期</td>
								<td align="left"><input id="indate" name="indate"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 100"
									value="${wfbornprocessesEntity.indate}" /></td>
								<td>聯絡電話</td>
								<td colspan="5" align="left"><input id="phone" name="phone"
									readonly onblur="validApplyTel('phone')"
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfbornprocessesEntity.phone}" /></td>
							</tr>
							<tr align="center">
								<td colspan="10" class="td_style1">申請條件</td>
							</tr>
							<tr align="center">
								<td>子女姓名</td>
								<td align="left"><input id="childname" name="childname"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 100"
									value="${wfbornprocessesEntity.childname}" /></td>
								<td>子女出生日期</td>
								<td align="left"><input id="childbirthday" name="childbirthday"
									class="Wdate" onblur="getAge()" data-options="width:100"
									style="width:100px" disabled
									value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${wfbornprocessesEntity.childbirthday}"/>"
									onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'%y-%M-%d',minDate:'%y-\#{%M-7}-%d'})" /></td>
								<td>上年績效符合條件</td>
								<td align="left"><input id="commentresultnew" name="commentresultnew"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 100"
									value="${wfbornprocessesEntity.commentresultnew}" /></td>
								<td>年資</td>
								<td align="left"><input id="worktime" name="worktime"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 100"
									value="${wfbornprocessesEntity.worktime}" /></td>
								<td>年齡</td>
								<td align="left" colspan="3" align="left"><input id="age" name="age"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 100" value="${wfbornprocessesEntity.age}" /></td>
							</tr>
							<tr align="center">
								<td colspan="10" class="td_style1">配偶基本資料</td>
							</tr>
							<tr align="center">
								<td><input type="radio" id="emp" name="ismateradio"
									onclick="check()" value="0" />集團員工<input id="disabled"
									value="disabled" type="hidden" /></td>
								<td>工號<span id="is1"></span></td>
								<td align="left"><input id="mateempno" name="mateempno" readonly
									class="easyui-validatebox inputCss" data-options="width: 100"
									value="${wfbornprocessesEntity.mateempno}" /></td>
								<td>身份證號<span id="is2"></span></td>
								<td align="left" colspan="2">&nbsp;&nbsp; <input id="mateidcard"
									readonly name="mateidcard" class="easyui-validatebox inputCss"
									data-options="width: 150,idCode:'tel[\'mateidcard\']'"
									value="${wfbornprocessesEntity.mateidcard}" />
								</td>
								<td>事業群<span id="is3"></span></td>
								<td align="left" colspan="3">&nbsp;&nbsp; <input id="enterprise"
									readonly name="enterprise" class="easyui-validatebox inputCss"
									data-options="width: 150"
									value="${wfbornprocessesEntity.enterprise}" />
								</td>
							</tr>
							<tr align="center">
								<td><input type="radio" id="notemp" name="ismateradio"
									onclick="check()" value="1" />非集團員工 <input id="ismate" name ="ismate" type="hidden" value="${wfbornprocessesEntity.ismate}"/></td>
								<td>姓名<span id="not1"></span></td>
								<td align="left"><input id="mateempname" name="mateempname" readonly
									class="easyui-validatebox inputCss" data-options="width: 100"
									value="${wfbornprocessesEntity.mateempname}" /></td>
								<td>身份證號<span id="not2"></span></td>
								<td align="left" colspan="2" style="border-right-width: 0;">&nbsp;&nbsp;
									<input id="idcard" name="idcard"
									class="easyui-validatebox inputCss" readonly
									data-options="width: 150,idCode:'tel[\'idcard\']'"
									value="${wfbornprocessesEntity.idcard}" />
								</td>
								<td colspan="4" style="border-left-width: 0;"></td>
							</tr>
							<tr align="center">
								<td>核給金額</br>（單位:RMB元）
								</td>
								<td colspan="9" align="left">&nbsp;&nbsp; <input
									id="moneycount" name="moneycount"
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfbornprocessesEntity.moneycount}" readonly></input>
								</td>
							</tr>
							<!--  </table>
                </td>
           </tr>
            <tr>
                <td>
                    <table class="formList"> -->
							<tr align="center">
								<td>附件</td>
								<td colspan="9" class="td_style1">
									<input type="hidden" id="attachids" name="attachids" value="${wfbornprocessesEntity.attachids }" />
									<div id="dowloadUrl">
										<c:forEach items="${file}" varStatus="i" var="item">
											<div id="${item.id}"
												style="line-height:30px;margin-left:5px;" class="float_L">
												<div class="float_L">
													<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
												</div>
											</div>
										</c:forEach>
									</div></td>
							</tr>
							<tr align="center">
								<td>備註</td>
								<td colspan="9" style="text-align:left;">
									1.本表依禮金核給管理作業規範辦理之。</br>
									2.附證明文件（審原件留複印件）。</br>
									3.年資及考績不符者，不享有禮金申請資格。</br>
									4.當月表單簽核完成者當月薪資作業發放，反之次月薪資作業發放，禮金并入薪資依法納稅。
								</td>
							</tr>
							<tr>
								<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
									<a href="javascript:void(0)"
									onclick="showWfImag('${processId}','初育禮金申請');">點擊查看簽核流程圖</a>
								</th>
							</tr>
							<tr>
								<td colspan="10" style="text-align:left;">
									${chargeNodeInfo}</td>
							</tr>

							<tr>
								<td colspan="10" style="text-align:left;"><iframe
										id="qianheLogFrame" name="qianheLogFrame"
										src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfbornprocessesEntity.serialno}"
										width="100%"></iframe></td>
							</tr>
							<tr class="no-print">
								<td colspan="10" style="text-align:center;padding-left:10px;">
									<a href="javascript:;" id="btnClose" class="easyui-linkbutton"
									data-options="iconCls:'icon-cancel'" style="width: 100px;"
									onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp; <c:if
										test="${wfbornprocessesEntity.workstatus!=null&&wfbornprocessesEntity.workstatus==3}">
										<a href="#" id="btnPrint" class="easyui-linkbutton"
											data-options="iconCls:'icon-print'" style="width: 100px;"
											onclick="printWindow('btnClose,btnPrint');">列印</a>
									</c:if>
								</td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</div>
	</form>
	</div>
	<div id="dlg"></div>
	<script
		src='${ctx}/static/js/humanCapital/wfbornprocesses.js?random=<%= Math.random()%>'></script>
</body>
</html>