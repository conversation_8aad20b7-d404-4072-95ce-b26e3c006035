<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>VOIP專線密碼申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfvoipprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfvoipprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfvoipprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">VOIP專線密碼申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfvoipprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfvoipprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfvoipprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfvoipprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfvoipprocessEntity.makerno}/${wfvoipprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <table class="formList">
                            <tr>
                                <td colspan="10" class="td_style1">承辦人基本信息</td>
                            </tr>
                            <tr align="center">
                                <td width="5%">承辦人工號</td>
                                <td width="5%" class="td_style1">
                                    <input id="dealno" name="dealno" class="easyui-validatebox inputCss"
                                           data-options="width: 80,required:true" readonly
                                           value="${wfvoipprocessEntity.dealno}"/>
                                </td>
                                <td width="3%">承辦人</td>
                                <td width="6%" class="td_style1">
                                    <input id="dealname" name="dealname"
                                           class="easyui-validatebox inputCss"
                                           data-options="width:80" readonly value="${wfvoipprocessEntity.dealname }"/>
                                </td>
                                <td width="4%">單位代碼</td>
                                <td width="6%" class="td_style1">
                                    <input id="dealdeptno" name="dealdeptno"
                                           class="easyui-validatebox inputCss" data-options="width: 90"
                                           readonly value="${wfvoipprocessEntity.dealdeptno }"/>
                                </td>
                                <td width="5%">廠區</td>
                                <td width="5%" class="td_style1">
                                    <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                           panelHeight="auto" value="${wfvoipprocessEntity.dealfactoryid }" disabled
                                           data-options="width: 120,required:true,validType:'comboxValidate[\'dealfactoryid\',\'请選擇承辦人廠區\']'"/>
                                </td>
                                <td width="4%">聯繫分機</td>
                                <td width="7%" class="td_style1">
                                    <input id="dealtel" name="dealtel" class="easyui-validatebox inputCss"
                                           style="width:90px;" readonly
                                           value="${wfvoipprocessEntity.dealtel }"
                                           data-options="required:true,prompt:'579+66666'"/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>單位</td>
                                <td colspan="5" class="td_style1">
                                    <input id="dealdeptname" name="dealdeptname" class="easyui-validatebox inputCss"
                                           data-options="width: 410,required:true" readonly
                                           value="${wfvoipprocessEntity.dealdeptname }"/>
                                </td>
                                <td>聯繫郵箱</td>
                                <td colspan="3" class="td_style1">
                                    <input id="dealemail" name="dealemail" class="easyui-validatebox inputCss"
                                           value="${wfvoipprocessEntity.dealemail }" style="width:300px;" readonly
                                           data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="10" class="td_style1">申請人基本信息</td>
                            </tr>
                            <tr align="center">
                                <td>申請人工號</td>
                                <td class="td_style1">
                                    <input id="applyno" name="applyno" class="easyui-validatebox inputCss"
                                           data-options="width: 80,required:true" readonly
                                           value="${wfvoipprocessEntity.applyno}"/>
                                </td>
                                <td>申請人</td>
                                <td class="td_style1">
                                    <input id="applyname" name="applyname"
                                           class="easyui-validatebox inputCss"
                                           data-options="width:80" readonly value="${wfvoipprocessEntity.applyname}"/>
                                </td>
                                <td>單位代碼</td>
                                <td class="td_style1">
                                    <input id="applydeptno" name="applydeptno"
                                           class="easyui-validatebox inputCss" data-options="width: 90"
                                           readonly value="${wfvoipprocessEntity.applydeptno }"/>
                                </td>
                                <td>費用代碼</td>
                                <td class="td_style1">
                                    <input id="applycostno" name="applycostno" readonly
                                           class="easyui-validatebox inputCss" data-options="width: 90,required:true"
                                           value="${wfvoipprocessEntity.applycostno }"/>
                                </td>
                                <td>所在廠區</td>
                                <td class="td_style1">
                                    <input id="applychoosefactoryid" name="applychoosefactoryid" class="easyui-combobox"
                                           panelHeight="auto" value="${wfvoipprocessEntity.applychoosefactoryid }"
                                           disabled
                                           data-options="width: 120,required:true,validType:'comboxValidate[\'applychoosefactoryid\',\'请選擇申請人廠區\']',onSelect:function(){onchangeFactory('applychoosefactoryid');}"/>
                                    <input id="applyfactoryid" name="applyfactoryid" type="hidden"
                                           value="${wfvoipprocessEntity.applyfactoryid }"/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>資位</td>
                                <td class="td_style1">
                                    <input id="applyleveltype" name="applyleveltype" class="easyui-validatebox inputCss"
                                           data-options="width: 80"
                                           value="${wfvoipprocessEntity.applyleveltype }" readonly/>
                                </td>
                                <td>管理職</td>
                                <td class="td_style1">
                                    <input id="applymanager" name="applymanager" class="easyui-validatebox inputCss"
                                           data-options="width: 80"
                                           value="${wfvoipprocessEntity.applymanager }" readonly/>
                                </td>
                                <td>聯繫郵箱</td>
                                <td colspan="3" class="td_style1">
                                    <input id="applyemail" name="applyemail" class="easyui-validatebox inputCss"
                                           value="${wfvoipprocessEntity.applyemail }" style="width:300px;" readonly
                                           data-options="required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                                </td>
                                <td>使用區域</td>
                                <td class="td_style1">
                                    <input id="applyarea" name="applyarea" class="easyui-combobox" disabled
                                           data-options="width: 70,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                           value="${wfvoipprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                    <input id="applybuilding" name="applybuilding" class="easyui-combobox" disabled
                                           data-options="width: 70,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                           value="${wfvoipprocessEntity.applybuilding }" panelHeight="auto"/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>聯繫分機</td>
                                <td class="td_style1">
                                    <input id="applyphone" name="applyphone" class="easyui-validatebox inputCss"
                                           style="width:90px;" readonly
                                           value="${wfvoipprocessEntity.applyphone }"
                                           data-options="required:true,prompt:'579+66666'"
                                           onblur="valdApplyTel(this)"/>
                                </td>
                                <td>單位</td>
                                <td colspan="5" class="td_style1">
                                    <input id="applydeptname" name="applydeptname" class="easyui-validatebox inputCss"
                                           data-options="width: 410,required:true" readonly
                                           value="${wfvoipprocessEntity.applydeptname }"/>
                                </td>
                                <td>安保區域</td>
                                <td class="td_style2">
                                    <div class="securityareaDiv"></div>
                                    <input id="securityarea" name="securityarea"
                                           type="hidden" class="easyui-validatebox" data-options="width: 100"
                                           value="${wfvoipprocessEntity.securityarea }"/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>法人代碼</td>
                                <td align="left">
                                    <input id="applycompanycode" name="applycompanycode"
                                           class="easyui-validatebox"
                                           data-options="width:80" value="${wfvoipprocessEntity.applycompanycode}"/>
                                </td>
                                <td>法人名稱</td>
                                <td align="left" colspan="7">
                                    <input id="applycompanyname" name="applycompanyname"
                                           class="easyui-validatebox"
                                           data-options="width:300" value="${wfvoipprocessEntity.applycompanyname}"/>
                                </td>
                            </tr>
                        </table>
                        <table class="formList">
                            <tr>
                                <td colspan="10" class="td_style1">申請详细信息</td>
                            </tr>
                            <tr align="center">
                                <td width="10%">申請類型</td>
                                <td width="90%" align="left" colspan="9">
                                    <div class="applytypeDiv"></div>
                                    <input id="applytype" name="applytype"
                                           type="hidden" class="easyui-validatebox" data-options="width: 100"
                                           value="${wfvoipprocessEntity.applytype }"/></td>
                            </tr>
                            <tr align="center">
                                <td>密碼功能</td>
                                <td align="left" colspan="9">
                                    <div class="applypwdtypeDiv"></div>
                                    <input id="applypwdtype" name="applypwdtype"
                                           type="hidden" class="easyui-validatebox" data-options="width: 100"
                                           value="${wfvoipprocessEntity.applypwdtype }"/></td>
                            </tr>
                            <tr align="center">
                                <td>費用金額</td>
                                <td colspan="9">
                                    <table style="width:100%;height: 100%" class="formList">
                                        <tr class="usd_tr" align="left">
                                            <td>
                                                美國密碼:申請金額${wfvoipprocessEntity.usdapplymoney}USD (建議金額20USD,至少可通話200分鐘)
                                            </td>
                                        </tr>
                                        <tr class="usd_tr" align="left">
                                            <td>
                                                <div class="usdapplytypeDiv"></div>
                                                <input id="usdapplytype" name="usdapplytype"
                                                       type="hidden" class="easyui-validatebox"
                                                       data-options="width: 100"
                                                       value="${wfvoipprocessEntity.usdapplytype }"/></td>
                                        </tr>
                                        <tr class="rmb_tr" align="left">
                                            <td>
                                                大陸密碼:申請金額${wfvoipprocessEntity.rmbapplymoney}RMB (建議金額10RMB,至少可通話100分鐘)
                                            </td>
                                        </tr>
                                        <tr class="rmb_tr" align="left">
                                            <td>
                                                <div class="rmbapplytypeDiv"></div>
                                                <input id="rmbapplytype" name="rmbapplytype"
                                                       type="hidden" class="easyui-validatebox"
                                                       data-options="width: 100"
                                                       value="${wfvoipprocessEntity.rmbapplytype }"/></td>
                                        </tr>
                                        <tr class="nbt_tr" align="left">
                                            <td>
                                                臺灣密碼:申請金額${wfvoipprocessEntity.ntbapplymoney}NTB (建議金額250NTB,至少可通話125分鐘)
                                            </td>
                                        </tr>
                                        <tr class="irn_tr" align="left">
                                            <td>
                                                印度密碼:申請金額${wfvoipprocessEntity.irnapplymoney}INR (建議金額100IRN,至少可通話100分鐘)
                                            </td>
                                        </tr>
                                        <tr class="vnd_tr" align="left">
                                            <td>
                                                越南密碼:申請金額${wfvoipprocessEntity.vndapplymoney}VND
                                                (建議金額100000VND,至少可通話100分鐘)
                                            </td>
                                        </tr>
                                        <tr class="vnd_tr" align="left">
                                            <td>
                                                <div class="vndapplytypeDiv"></div>
                                                <input id="vndapplytype" name="vndapplytype"
                                                       type="hidden" class="easyui-validatebox"
                                                       data-options="width: 100"
                                                       value="${wfvoipprocessEntity.vndapplytype }"/></td>
                                        </tr>
                                        <tr class="brl_tr" align="left">
                                            <td>
                                                巴西密碼:申請金額${wfvoipprocessEntity.brlapplymoney}BRL (建議金額100BRL,至少可通話100分鐘)
                                            </td>
                                        </tr>
                                        <tr class="aud_tr" align="left">
                                            <td>
                                                澳大利亞密碼:申請金額${wfvoipprocessEntity.audapplymoney}AUD
                                                (建議金額20AUD,至少可通話100分鐘)
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>密碼領取方式</td>
                                <td align="left" colspan="9">
                                    <div style="float: left" class="getpwdtypeDiv"></div>
                                    <div><input id="getpwdtype" name="getpwdtype"
                                                type="hidden" class="easyui-validatebox" data-options="width: 80"
                                                value="${wfvoipprocessEntity.getpwdtype }"/></div>
                                    <span id="getpwdtelphoneSpan"
                                          style="display: none">手機號碼${wfvoipprocessEntity.getpwdtelphone}</span></td>
                            </tr>
                            <tr align="center">
                                <td>撥打最多區域</td>
                                <td align="left" colspan="9">
                                    ${wfvoipprocessEntity.calluparea}
                                </td>
                            </tr>
                            <tr align="center">
                                <td>需求說明</td>
                                <td align="left" colspan="9"><textarea
                                        id="describtion"
                                        name="describtion" data-options="required:true"
                                        maxlength="100" readonly
                                        class="easyui-validatebox inputCss" style="width:900px;height:40px;"
                                        rows="5" cols="6">${wfvoipprocessEntity.describtion}</textarea><span
                                        id="txtNum"></span></td>
                            </tr>
                            <tr align="center">
                                <td>附件</td>
                                <td colspan="9" class="td_style1">
                                    <input type="hidden" id="attachids"
                                           name="attachids" value="${wfvoipprocessEntity.attachids }"/>
                                    <div id="dowloadUrl">
                                        <c:forEach items="${file}" varStatus="i" var="item">
                                            <div id="${item.id}"
                                                 style="line-height:30px;margin-left:5px;" class="float_L">
                                                <div class="float_L">
                                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                                </div>
                                            </div>
                                        </c:forEach>
                                    </div>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>附件說明</td>
                                <td align="left" colspan="9">VOIP專線密碼申請承諾書<a
                                        href="${ctx}/wfvoipprocess/downLoad/voipbook">承諾書下載</a></td>
                            </tr>
                        </table>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','VOIP專線密碼申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfvoipprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfvoipprocessEntity.workstatus!=null&&wfvoipprocessEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<div id="dlg"></div>
<script src='${ctx}/static/js/information/wfvoipprocess.min.js?random=20241014'></script>
</body>
</html>