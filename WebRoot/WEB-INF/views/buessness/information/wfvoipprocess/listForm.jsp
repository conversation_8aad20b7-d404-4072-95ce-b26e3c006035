<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>VOIP專線密碼申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfvoipprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfvoipprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfvoipprocessEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfvoipprocessEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfvoipprocessEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfvoipprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${wfvoipprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">VOIP專線密碼申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfvoipprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfvoipprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfvoipprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfvoipprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfvoipprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfvoipprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${wfvoipprocessEntity.makerno}/${wfvoipprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="dealno" name="dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfvoipprocessEntity.dealno}" onblur="queryUserInfo('deal');"/>
                            </td>
                            <td width="3%">承辦人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfvoipprocessEntity.dealname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfvoipprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="5%">廠區&nbsp;</td>
                            <td width="5%" class="td_style1">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfvoipprocessEntity.dealfactoryid }" disabled
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'dealfactoryid\',\'请選擇承辦人廠區\']'"/>
                            </td>
                            <td width="4%">聯繫分機&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="dealtel" name="dealtel" class="easyui-validatebox"
                                       style="width:90px;position:relative;"
                                       value="${wfvoipprocessEntity.dealtel }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="dealdeptname" name="dealdeptname" class="easyui-validatebox"
                                       data-options="width: 410,required:true"
                                       value="${wfvoipprocessEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="dealemail" class="easyui-validatebox"
                                       value="${wfvoipprocessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td>申請人工號&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfvoipprocessEntity.applyno}" onblur="queryUserInfo('apply');"/>
                            </td>
                            <td>申請人</td>
                            <td class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfvoipprocessEntity.applyname}"/>
                            </td>
                            <td>單位代碼</td>
                            <td class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfvoipprocessEntity.applydeptno }"/>
                            </td>
                            <td>費用代碼&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applycostno" name="applycostno"
                                       class="easyui-validatebox" data-options="width: 90,required:true"
                                       value="${wfvoipprocessEntity.applycostno }"/>
                            </td>
                            <td>所在廠區&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applychoosefactoryid" name="applychoosefactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfvoipprocessEntity.applychoosefactoryid }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applychoosefactoryid\',\'请選擇申請人廠區\']',onSelect:function(){onchangeFactory('applychoosefactoryid');}"/>
                                <input id="applyfactoryid" name="applyfactoryid" type="hidden"
                                       value="${wfvoipprocessEntity.applyfactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="applyleveltype" class="easyui-validatebox inputCss"
                                       data-options="width: 80"
                                       value="${wfvoipprocessEntity.applyleveltype }" readonly/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="applymanager" class="easyui-validatebox inputCss"
                                       data-options="width: 80"
                                       value="${wfvoipprocessEntity.applymanager }" readonly/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       value="${wfvoipprocessEntity.applyemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td>使用區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyarea" name="applyarea" class="easyui-combobox"
                                       data-options="width: 70,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wfvoipprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="applybuilding" class="easyui-combobox"
                                       data-options="width: 70,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wfvoipprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyphone" name="applyphone" class="easyui-validatebox"
                                       style="width:90px;position:relative;"
                                       value="${wfvoipprocessEntity.applyphone }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox"
                                       data-options="width: 410,required:true"
                                       value="${wfvoipprocessEntity.applydeptname }"/>
                            </td>
                            <td>安保區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style2">
                                <div class="securityareaDiv"></div>
                                <input id="securityarea" name="securityarea"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfvoipprocessEntity.securityarea }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>法人代碼</td>
                            <td class="td_style1">
                                <input id="applycompanycode" name="applycompanycode"
                                       class="easyui-validatebox"
                                       data-options="width:80,required:true" value="${wfvoipprocessEntity.applycompanycode}"/>
                            </td>
                            <td>法人名稱</td>
                            <td class="td_style1" colspan="7">
                                <input id="applycompanyname" name="applycompanyname"
                                       class="easyui-validatebox"
                                       data-options="width:300,required:true" value="${wfvoipprocessEntity.applycompanyname}"/>
                            </td>
                        </tr>
                    </table>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請详细信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">申請類型&nbsp;<font color="red">*</font></td>
                            <td width="90%" align="left" colspan="9">
                                <div class="applytypeDiv"></div>
                                <input id="applytype" name="applytype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfvoipprocessEntity.applytype }"/></td>
                        </tr>
                        <tr align="center">
                            <td>密碼功能&nbsp;<font color="red">*</font></td>
                            <td align="left" colspan="9">
                                <div class="applypwdtypeDiv"></div>
                                <input id="applypwdtype" name="applypwdtype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfvoipprocessEntity.applypwdtype }"/></td>
                        </tr>
                        <tr align="center">
                            <td>費用金額&nbsp;<font color="red">*</font> </td>
                            <td colspan="9">
                               <table style="width:100%;height: 100%" class="formList">
                                   <tr class="usd_tr" align="left">
                                       <td>
                                           美國密碼:申請金額<input id="usdapplymoney" name="usdapplymoney" class="easyui-validatebox" data-options="width:80,validType:'number[\'usdapplymoney\',\'请填寫數字\']'" value="${wfvoipprocessEntity.usdapplymoney}"/>USD (建議金額20USD,至少可通話200分鐘)
                                       </td>
                                   </tr>
                                   <tr class="usd_tr" align="left">
                                       <td>
                                           <div class="usdapplytypeDiv"></div>
                                           <input id="usdapplytype" name="usdapplytype"
                                                  type="hidden" class="easyui-validatebox" data-options="width: 100"
                                                  value="${wfvoipprocessEntity.usdapplytype }"/></td>
                                   </tr>
                                   <tr class="rmb_tr" align="left">
                                       <td>
                                           大陸密碼:申請金額<input id="rmbapplymoney" name="rmbapplymoney" class="easyui-validatebox" data-options="width:80,validType:'number[\'usdapplymoney\',\'请填寫數字\']'" value="${wfvoipprocessEntity.rmbapplymoney}"/>RMB (建議金額10RMB,至少可通話100分鐘)
                                       </td>
                                   </tr>
                                   <tr class="rmb_tr" align="left">
                                       <td>
                                           <div class="rmbapplytypeDiv"></div>
                                           <input id="rmbapplytype" name="rmbapplytype"
                                                  type="hidden" class="easyui-validatebox" data-options="width: 100"
                                                  value="${wfvoipprocessEntity.rmbapplytype }"/></td>
                                   </tr>
                                   <tr class="nbt_tr" align="left">
                                       <td>
                                           臺灣密碼:申請金額<input id="ntbapplymoney" name="ntbapplymoney" class="easyui-validatebox" data-options="width:80,validType:'number[\'usdapplymoney\',\'请填寫數字\']'" value="${wfvoipprocessEntity.ntbapplymoney}"/>NTB (建議金額250NTB,至少可通話125分鐘)
                                       </td>
                                   </tr>
                                   <tr class="irn_tr" align="left">
                                       <td>
                                           印度密碼:申請金額<input id="irnapplymoney" name="irnapplymoney" class="easyui-validatebox" data-options="width:80,validType:'number[\'usdapplymoney\',\'请填寫數字\']'" value="${wfvoipprocessEntity.irnapplymoney}"/>INR (建議金額100IRN,至少可通話100分鐘)
                                       </td>
                                   </tr>
                                   <tr class="vnd_tr" align="left">
                                       <td>
                                           越南密碼:申請金額<input id="vndapplymoney" name="vndapplymoney" class="easyui-validatebox" data-options="width:80,validType:'number[\'usdapplymoney\',\'请填寫數字\']'" value="${wfvoipprocessEntity.vndapplymoney}"/>VND (建議金額100000VND,至少可通話100分鐘)
                                       </td>
                                   </tr>
                                   <tr class="vnd_tr" align="left">
                                       <td>
                                           <div class="vndapplytypeDiv"></div>
                                           <input id="vndapplytype" name="vndapplytype"
                                                  type="hidden" class="easyui-validatebox" data-options="width: 100"
                                                  value="${wfvoipprocessEntity.vndapplytype }"/></td>
                                   </tr>
                                   <tr class="brl_tr" align="left">
                                       <td>
                                           巴西密碼:申請金額<input id="brlapplymoney" maxlength="7" name="brlapplymoney" class="easyui-validatebox" data-options="width:80,validType:'number[\'usdapplymoney\',\'请填寫數字\']'" value="${wfvoipprocessEntity.brlapplymoney}"/>BRL (建議金額100BRL,至少可通話100分鐘)
                                       </td>
                                   </tr>
                                   <tr class="aud_tr" align="left">
                                       <td>
                                           澳大利亞密碼:申請金額<input id="audapplymoney" name="audapplymoney" class="easyui-validatebox" data-options="width:80,validType:'number[\'usdapplymoney\',\'请填寫數字\']'" value="${wfvoipprocessEntity.audapplymoney}"/>AUD (建議金額20AUD,至少可通話100分鐘)
                                       </td>
                                   </tr>
                               </table>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>密碼領取方式&nbsp;<font color="red">*</font> </td>
                            <td align="left" colspan="9">
                                <div style="float: left" class="getpwdtypeDiv"></div>
                                <div><input id="getpwdtype" name="getpwdtype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 80"
                                       value="${wfvoipprocessEntity.getpwdtype }"/></div>
                                <span id="getpwdtelphoneSpan" style="display: none">手機號碼<font color="red">*</font><input id="getpwdtelphone" name="getpwdtelphone" class="easyui-validatebox" data-options="width:100" value="${wfvoipprocessEntity.getpwdtelphone}" onblur="validMobilephone(this)"/></span></td>
                        </tr>
                        <tr align="center">
                            <td>撥打最多區域</td>
                            <td align="left" colspan="9">
                                <input id="calluparea" name="calluparea" class="easyui-validatebox"
                                       style="width:900px;" value="${wfvoipprocessEntity.calluparea }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>需求說明&nbsp;<font color="red">*</font></td>
                            <td align="left" colspan="9"><textarea
                                    id="describtion"
                                    name="describtion" data-options="required:true"
                                    oninput="return LessThanAuto(this,'txtNum');"
                                    onchange="return LessThanAuto(this,'txtNum');"
                                    onpropertychange="return LessThanAuto(this,'txtNum');"
                                    maxlength="100"
                                    class="easyui-validatebox" style="width:900px;height:40px;"
                                    rows="5" cols="6">${wfvoipprocessEntity.describtion}</textarea><span
                                    id="txtNum"></span></td>
                        </tr>
                        <tr align="center">
                            <td>附件&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button"
                                                                   value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span> <input type="hidden" id="attachids"
                                               name="attachids" value="${wfvoipprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件說明</td>
                            <td align="left" colspan="9">VOIP專線密碼申請承諾書<a href="${ctx}/wfvoipprocess/downLoad/voipbook">承諾書下載</a></td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_voipzhuanxianmimashenqing','VOIP專線密碼申請單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td colspan="10" style="text-align:left;">
                                            <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                                <tr>
                                                    <td style="border:none">
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="yl5Table">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['ylno5_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole2(22,'yl5Table','ylno5','ylname5',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="ylno5" name="ylno5"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['ylno5']}"
                                                                           readonly
                                                                           value="${wfvoipprocessEntity.ylno5 }"/><c:if
                                                                        test="${requiredMap['ylno5'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="ylname5" name="ylname5"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['ylno5']}"
                                                                            value="${wfvoipprocessEntity.ylname5 }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="cchargeTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['cchargeno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole($('#applydeptno').val(),'cchargeno','cchargename',$('#applyfactoryid').val())"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="cchargeno" name="cchargeno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                           readonly
                                                                           value="${wfvoipprocessEntity.cchargeno }"/><c:if
                                                                        test="${requiredMap['cchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="cchargename" name="cchargename"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                            value="${wfvoipprocessEntity.cchargename }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="zchargeTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['zchargeno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole3('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applyfactoryid').val(),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="zchargeno" name="zchargeno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                           readonly
                                                                           value="${wfvoipprocessEntity.zchargeno }"/><c:if
                                                                        test="${requiredMap['zchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="zchargename" name="zchargename"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                            value="${wfvoipprocessEntity.zchargename }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="zcchargeTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['zcchargeno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole3('zcchargeTable',$('#applydeptno').val(),'zcchargeno','zcchargename',$('#applyfactoryid').val(),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="zcchargeno" name="zcchargeno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                           readonly
                                                                           value="${wfvoipprocessEntity.zcchargeno }"/><c:if
                                                                        test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="zcchargename" name="zcchargename"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                            value="${wfvoipprocessEntity.zcchargename }"/>
                                                                </td>
                                                            </tr>
                                                        </table>

                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="yl2Table">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['ylno2_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole2(62,'yl2Table','ylno2','ylname2',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="ylno2" name="ylno2"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['ylno2']}"
                                                                           readonly
                                                                           value="${wfvoipprocessEntity.ylno2 }"/><c:if
                                                                        test="${requiredMap['ylno2'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="ylname2" name="ylname2"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['ylno2']}"
                                                                            value="${wfvoipprocessEntity.ylname2 }"/>
                                                                </td>
                                                            </tr>
                                                        </table>

                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="border:none">
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="yl6Table">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['ylno6_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole2(25,'yl6Table','ylno6','ylname6',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="ylno6" name="ylno6"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['ylno6']}"
                                                                           readonly
                                                                           value="${wfvoipprocessEntity.ylno6 }"/><c:if
                                                                        test="${requiredMap['ylno6'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="ylname6" name="ylname6"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['ylno6']}"
                                                                            value="${wfvoipprocessEntity.ylname6 }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="pcchargeTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole3('pcchargeTable',$('#applydeptno').val(),'pcchargeno','pcchargename',$('#applyfactoryid').val(),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="pcchargeno" name="pcchargeno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                           readonly
                                                                           value="${wfvoipprocessEntity.pcchargeno }"/>
                                                                    <c:if test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                                            color="red">*</font></c:if>
                                                                    /<input id="pcchargename" name="pcchargename"
                                                                            readonly
                                                                            class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                            value="${wfvoipprocessEntity.pcchargename }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <%--<tr>
                                                    <td style="border:none">
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="yl1Table">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['ylno1_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole5(21,'ylno1','ylname1','ylno8','ylname8','','',$('#applychoosefactoryid').combobox('getValue'),$('#applyarea').combobox('getValue'),$('#applybuilding').combobox('getValue'))"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="ylno1" name="ylno1"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['ylno1']}"
                                                                           readonly
                                                                           value="${wfvoipprocessEntity.ylno1 }"/><c:if
                                                                        test="${requiredMap['ylno1'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="ylname1" name="ylname1"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['ylno1']}"
                                                                            value="${wfvoipprocessEntity.ylname1 }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="kchargeTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['kchargeno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#applyfactoryid').val())"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="kchargeno" name="kchargeno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                           readonly
                                                                           value="${wfvoipprocessEntity.kchargeno }"/><c:if
                                                                        test="${requiredMap['kchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="kchargename" name="kchargename"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                            value="${wfvoipprocessEntity.kchargename }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="bchargeTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['bchargeno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#applyfactoryid').val())"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="bchargeno" name="bchargeno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                           readonly
                                                                           value="${wfvoipprocessEntity.bchargeno }"/><c:if
                                                                        test="${requiredMap['bchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="bchargename" name="bchargename"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                            value="${wfvoipprocessEntity.bchargename }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="cchargeTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['cchargeno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole($('#applydeptno').val(),'cchargeno','cchargename',$('#applyfactoryid').val())"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="cchargeno" name="cchargeno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                           readonly
                                                                           value="${wfvoipprocessEntity.cchargeno }"/><c:if
                                                                        test="${requiredMap['cchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="cchargename" name="cchargename"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                            value="${wfvoipprocessEntity.cchargename }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="zchargeTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['zchargeno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole3('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applyfactoryid').val(),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="zchargeno" name="zchargeno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                           readonly
                                                                           value="${wfvoipprocessEntity.zchargeno }"/><c:if
                                                                        test="${requiredMap['zchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="zchargename" name="zchargename"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                            value="${wfvoipprocessEntity.zchargename }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="border:none">
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="zcchargeTable">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['zcchargeno_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole3('zcchargeTable',$('#applydeptno').val(),'zcchargeno','zcchargename',$('#applyfactoryid').val(),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="zcchargeno" name="zcchargeno"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                           readonly
                                                                           value="${wfvoipprocessEntity.zcchargeno }"/><c:if
                                                                        test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="zcchargename" name="zcchargename"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                            value="${wfvoipprocessEntity.zcchargename }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="yl2Table">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['ylno2_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole2(62,'yl2Table','ylno2','ylname2',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="ylno2" name="ylno2"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['ylno2']}"
                                                                           readonly
                                                                           value="${wfvoipprocessEntity.ylno2 }"/><c:if
                                                                        test="${requiredMap['ylno2'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="ylname2" name="ylname2"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['ylno2']}"
                                                                            value="${wfvoipprocessEntity.ylname2 }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="yl5Table">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['ylno5_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole2(22,'yl5Table','ylno5','ylname5',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="ylno5" name="ylno5"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['ylno5']}"
                                                                           readonly
                                                                           value="${wfvoipprocessEntity.ylno5 }"/><c:if
                                                                        test="${requiredMap['ylno5'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="ylname5" name="ylname5"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['ylno5']}"
                                                                            value="${wfvoipprocessEntity.ylname5 }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table width="18%" style="float: left;margin-left: 5px;"
                                                               id="yl6Table">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: right;">
                                                                                ${requiredMap['ylno6_name']}
                                                                            </td>
                                                                            <td style="border: none;">
                                                                                <div class="float_L qhUserIcon"
                                                                                     onclick="selectRole2(25,'yl6Table','ylno6','ylname6',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="ylno6" name="ylno6"
                                                                           class="easyui-validatebox"
                                                                           data-options="width:80,required:${requiredMap['ylno6']}"
                                                                           readonly
                                                                           value="${wfvoipprocessEntity.ylno6 }"/><c:if
                                                                        test="${requiredMap['ylno6'].equals('true')}"><font
                                                                        color="red">*</font></c:if>
                                                                    /<input id="ylname6" name="ylname6"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['ylno6']}"
                                                                            value="${wfvoipprocessEntity.ylname6 }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <table width="18%" style="float: left;margin-left: 5px;" id="yl8Table">
                                                            <tr>
                                                                <td>
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td style="border: none;text-align: center;">${requiredMap['ylno8_name']}</td>
                                                                            &lt;%&ndash;<td style="border: none;"><div class="float_L qhUserIcon" onclick="selectRole($('#dealdeptno').val(),'ylno8','ylname8',$('#dealfactoryid').val())"></div></td>&ndash;%&gt;
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><input id="ylno8" name="ylno8"
                                                                           class="easyui-validatebox" data-options="width:80,required:${requiredMap['ylno8']}"
                                                                           readonly
                                                                           value="${wfvoipprocessEntity.ylno8 }"/><c:if test="${requiredMap['ylno8'].equals('true')}"><font color="red">*</font></c:if>
                                                                    /<input id="ylname8" name="ylname8"
                                                                            readonly class="easyui-validatebox"
                                                                            data-options="width:80,required:${requiredMap['ylno8']}"
                                                                            value="${wfvoipprocessEntity.ylname8 }"/>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>--%>
                                            </table>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td colspan="10" style="text-align:left;">
                                            <table class="flowList"
                                                   style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                                <tr>
                                                    <td>簽核時間</td>
                                                    <td>簽核節點</td>
                                                    <td>簽核主管</td>
                                                    <td>簽核意見</td>
                                                    <td>批註</td>
                                                    <td>簽核電腦IP</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="text-align: left" colspan="10"><font color="red">溫馨提示：如果您在填單過程中有任何疑問，請聯繫本廠區資訊人員：</font><a href="${ctx}/wfvoipprocess/downLoad/contact">各廠區資訊聯繫方式</a></td>
                                    </tr>
                                    <tr align="center">
                                        <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                            <input type="checkbox" id="agree" name="agree"/><a href="${ctx}/requisitionlist/downLoad/commitmentTpl" plain="true" id="btnCommitmentTpl">本人已閱讀并同意服務條款</a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                            <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                               data-options="iconCls:'icon-add'"
                                               style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                            <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                               data-options="iconCls:'icon-ok'"
                                               style="width: 100px;" onclick="saveInfo(2);">提交</a>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
        <input type="hidden" id="chargeName" name="chargeName" value=""/>
        <input type="hidden" id="factoryId" name="factoryId" value=""/>
        <input type="hidden" id="dutyId" name="dutyId" value=""/>
        <input type="hidden" id="areaId" name="areaId" value=""/>
        <input type="hidden" id="buildingId" name="buildingId" value=""/>
        <input id="disOrEnabled" type="hidden" value=""/>
        <input type="hidden" id="loginname" name="loginname" value="${user.loginName}"/>
        <div id="win"></div>
</form>
</div>
<script src='${ctx}/static/js/information/wfvoipprocess.min.js?random=20241014'></script>
</body>
</html>
