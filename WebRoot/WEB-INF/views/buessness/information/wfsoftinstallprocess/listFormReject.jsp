<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>軟體安裝申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfsoftinstallprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfSoftinstallProcessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfSoftinstallProcessEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfSoftinstallProcessEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfSoftinstallProcessEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfSoftinstallProcessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden"
           value="${wfSoftinstallProcessEntity.makerfactoryid }"/>
    <input id="applynofactoryid" name="applynofactoryid" type="hidden"
           value="${wfSoftinstallProcessEntity.applynofactoryid }"/>
    <div class="commonW">
        <div class="headTitle">軟體安裝申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfSoftinstallProcessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfSoftinstallProcessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfSoftinstallProcessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfSoftinstallProcessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfSoftinstallProcessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfSoftinstallProcessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfSoftinstallProcessEntity.makerno}/${wfSoftinstallProcessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="dealno" name="dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="<c:if test="${wfSoftinstallProcessEntity.dealno!=null&&wfSoftinstallProcessEntity.dealno!=''}">${wfSoftinstallProcessEntity.dealno}</c:if><c:if test="${wfSoftinstallProcessEntity.dealno==null||wfSoftinstallProcessEntity.dealno==''}">${user.loginName}</c:if>"
                                       onblur="queryUserInfo('deal');"/>
                            </td>
                            <td width="5%">承辦人</td>
                            <td width="5%" class="td_style1">
                                <input id="dealname" name="dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wfSoftinstallProcessEntity.dealname }"/>
                            </td>
                            <td width="5%">單位代碼</td>
                            <td width="5%" class="td_style1">
                                <input id="dealdeptno" name="dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfSoftinstallProcessEntity.dealdeptno }"/>
                            </td>
                            <td width="5%">廠區&nbsp;</td>
                            <td width="5%" class="td_style1">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfSoftinstallProcessEntity.dealfactoryid }" disabled
                                       data-options="width: 120,required:true"/>
                                <input id="dealfactoryname" name="dealfactoryname" type="hidden"
                                       value="${wfSoftinstallProcessEntity.dealfactoryname}"/>
                            </td>
                            <td width="5%">聯繫分機&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="dealtel" name="dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfSoftinstallProcessEntity.dealtel }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="dealdeptname" name="dealdeptname" class="easyui-validatebox"
                                       data-options="width: 410,required:true"
                                       value="${wfSoftinstallProcessEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="dealemail" class="easyui-validatebox"
                                       value="${wfSoftinstallProcessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfSoftinstallProcessEntity.applyno}" onblur="queryUserInfo('apply');"/>
                            </td>
                            <td width="5%">申請人</td>
                            <td width="5%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wfSoftinstallProcessEntity.applyname}"/>
                            </td>
                            <td width="5%">單位代碼</td>
                            <td width="5%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfSoftinstallProcessEntity.applydeptno }"/>
                            </td>
                            <td width="5%">費用代碼</td>
                            <td width="5%" class="td_style1">
                                <input id="applycostno" name="applycostno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       value="${wfSoftinstallProcessEntity.applycostno }"/>
                            </td>
                            <td width="3%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfSoftinstallProcessEntity.applyfactoryid }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applyfactoryid\',\'请选择所在廠區\']',onSelect:function(){onchangeFactory('apply');}"/>
                                <input id="applyfactoryname" name="applyfactoryname" type="hidden"
                                       value="${wfSoftinstallProcessEntity.applyfactoryname}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="applyleveltype" class="easyui-validatebox inputCss"
                                       data-options="width: 80"
                                       value="${wfSoftinstallProcessEntity.applyleveltype }" readonly/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="applymanager" class="easyui-validatebox inputCss"
                                       data-options="width: 80"
                                       value="${wfSoftinstallProcessEntity.applymanager }" readonly/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       value="${wfSoftinstallProcessEntity.applyemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td>使用區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyarea" name="applyarea" class="easyui-combobox"
                                       data-options="width: 70,onSelect:function(){onchangeArea('apply');},validType:'comboxValidate[\'applyarea\',\'請選擇使用區域\']'"
                                       value="${wfSoftinstallProcessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="applybuilding" class="easyui-combobox"
                                       data-options="width: 70,validType:'comboxValidate[\'applybuilding\',\'請選擇使用區域\']',onSelect:function(){onchangeBuilding('apply');}"
                                       value="${wfSoftinstallProcessEntity.applybuilding }" panelHeight="auto"/>
                                <input id="applyareaname" name="applyareaname" type="hidden"
                                       value="${wfSoftinstallProcessEntity.applyareaname}"/>
                                <input id="applybuildingname" name="applybuildingname" type="hidden"
                                       value="${wfSoftinstallProcessEntity.applybuildingname}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="5%">聯繫分機&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="applyphone" name="applyphone" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfSoftinstallProcessEntity.applyphone }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox"
                                       data-options="width: 410,required:true"
                                       value="${wfSoftinstallProcessEntity.applydeptname }"/>
                            </td>
                            <td>安保區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style2">
                                <div class="securityareaDiv"></div>
                                <input id="securityareaValue" name="securityareaValue"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfSoftinstallProcessEntity.securityarea }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">單位軟體管理信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">管理員工號<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="adminno" name="adminno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfSoftinstallProcessEntity.adminno}" onblur="queryUserInfo('admin');"/>
                            </td>
                            <td width="5%">管理員姓名</td>
                            <td width="5%" class="td_style1">
                                <input id="adminname" name="adminname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wfSoftinstallProcessEntity.adminname}"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="5" style="text-align: left">
                                <input id="adminemail" name="adminemail" class="easyui-validatebox"
                                       value="${wfSoftinstallProcessEntity.adminemail }" style="width:250px;"
                                       data-options="required:true,validType:'email[\'adminemail\',\'郵箱的格式不正確\']'"/>
                                （軟件授權到期前3個月郵件通知單位軟體資產管理員）
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="5%">處級主管工號<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="chujino" name="chujino" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfSoftinstallProcessEntity.chujino}" onblur="queryUserInfo('chuji');"/>
                            </td>
                            <td width="5%">處級主管姓名</td>
                            <td width="5%" class="td_style1">
                                <input id="chujiname" name="chujiname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wfSoftinstallProcessEntity.chujiname}"/>
                            </td>
                            <td>處級主管郵箱<font color="red">*</font></td>
                            <td colspan="5" style="text-align: left">
                                <input id="chujiemail" name="chujiemail" class="easyui-validatebox"
                                       value="${wfSoftinstallProcessEntity.chujiemail }" style="width:250px;"
                                       data-options="required:true,validType:'email[\'chujiemail\',\'郵箱的格式不正確\']'"/>
                                （軟件授權到期前1個月郵件通知單位處級主管）
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">軟體安裝明細</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <table class="formList">
                                    <tbody id="info_Body">
                                    <c:if test="${wfSoftinstallProcessEntity.itemsEntitys!=null&&wfSoftinstallProcessEntity.itemsEntitys.size()>0}">
                                        <c:forEach items="${wfSoftinstallProcessEntity.itemsEntitys}" var="itemsEntity"
                                                   varStatus="status">
                                            <tr id="itemsEntitysTitle${status.index+1}">
                                                <input type="hidden" id="xuhao${status.index+1}"
                                                       name="itemsEntitys[${status.index+1}].xuhao"
                                                       value="${status.index+1}"/>
                                                <c:if test="${status.index>0}">
                                                    <td colspan="9" align="center" bgcolor="#faebd7">第${status.index+1}位使用人信息</td>
                                                    <td width="6%" align="center">
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               class="deleteBtnStr"
                                                               onclick="bondedgooddeltr(${status.index+1});return false;"/>
                                                    </td>
                                                </c:if>
                                                <c:if test="${status.index==0}">
                                                    <td colspan="10" align="center" bgcolor="#faebd7">
                                                        第${status.index+1}位使用人信息
                                                    </td>
                                                </c:if>
                                            </tr>
                                            <tr align="center">
                                                <td width="10%">使用人工號<font color="red">*</font></td>
                                                <td width="10%" class="td_style1">
                                                    <input id="userno${status.index}"
                                                           onblur="getUserNameByEmpno2(this,${status.index});"
                                                           name="itemsEntitys[${status.index}].userno"
                                                           class="easyui-validatebox" style="width:80px;"
                                                           data-options="required:true"
                                                           value="${itemsEntity.userno}"/>
                                                </td>
                                                <td width="8%">使用人姓名</td>
                                                <td width="10%" class="td_style1">
                                                    <input id="username${status.index}"
                                                           name="itemsEntitys[${status.index}].username"
                                                           class="easyui-validatebox inputCss" style="width:80px;"
                                                           readonly value="${itemsEntity.username}"/>
                                                </td>
                                                <td width="10%">使用廠區<font color="red">*</font></td>
                                                <td width="10%" class="td_style1">
                                                    <input id="userfactoryid${status.index}"
                                                           name="itemsEntitys[${status.index}].userfactoryid"
                                                           data-options="panelHeight:300,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'userfactoryid${status.index}\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(${status.index});},onSelect:function(){pcOnchangeFactory('user',${status.index});}"
                                                           style="width:100px;" class="easyui-combobox"
                                                           value="${itemsEntity.userfactoryid}"/>
                                                    <input id="userfactoryname${status.index}"
                                                           name="itemsEntitys[${status.index}].userfactoryname"
                                                           type="hidden" value="${itemsEntity.userfactoryname}"/>
                                                </td>
                                                <td width="8%">使用區域<font color="red">*</font></td>
                                                <td colspan="3" class="td_style1">
                                                    <input id="arealist${status.index}"
                                                           name="itemsEntitys[${status.index}].arealist"
                                                           data-options="validType:'comboxValidate[\'arealist${status.index}\',\'请選擇區域\']',onSelect:function(){pcOnchangeArea('arealist${status.index}',${status.index});}"
                                                           class="easyui-combobox" style="width:80px;"
                                                           value="${itemsEntity.arealist}" panelHeight="auto"/>/
                                                    <input id="buildinglist${status.index}"
                                                           name="itemsEntitys[${status.index}].buildinglist"
                                                           data-options="validType:'comboxValidate[\'buildinglist${status.index}\',\'请選擇樓棟\']',onSelect:function(){pcOnchangeBuilding(${status.index});}"
                                                           class="easyui-combobox" style="width:80px;"
                                                           value="${itemsEntity.buildinglist}" panelHeight="auto"/>
                                                    <input id="arealistname${status.index}"
                                                           name="itemsEntitys[${status.index}].arealistname"
                                                           type="hidden" value="${itemsEntity.arealistname}"/>
                                                    <input id="buildinglistname${status.index}"
                                                           name="itemsEntitys[${status.index}].buildinglistname"
                                                           type="hidden" value="${itemsEntity.buildinglistname}"/>
                                                </td>
                                            </tr>
                                            <tr align="center">
                                                <td width="8%">單位<font color="red">*</font></td>
                                                <td colspan="3" class="td_style1">
                                                    <input id="userdept${status.index}"
                                                           name="itemsEntitys[${status.index}].userdept"
                                                           class="easyui-validatebox" style="width:300px;"
                                                           data-options="required:true"
                                                           value="${itemsEntity.userdept}"/>
                                                </td>
                                                <td width="8%">電腦名稱<font color="red">*</font></td>
                                                <td class="td_style1">
                                                    <input id="pcname${status.index}"
                                                           name="itemsEntitys[${status.index}].pcname"
                                                           class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true"
                                                           value="${itemsEntity.pcname}"/>
                                                </td>
                                                <td width="8%">IP地址<font color="red">*</font></td>
                                                <td class="td_style1">
                                                    <input id="pcip${status.index}"
                                                           name="itemsEntitys[${status.index}].pcip"
                                                           onblur="isNpiCheck(this,${status.index});"
                                                           class="easyui-validatebox"
                                                           data-options="required:true,validType:'ip[\'pcip${status.index}\']'"
                                                           style="width:100px" value="${itemsEntity.pcip}"/>
                                                </td>
                                                <td width="10%">是否安保區域<font color="red">*</font></td>
                                                <td class="td_style1">
                                                    <input id="usersecurityarea${status.index}"
                                                           name="itemsEntitys[${status.index}].usersecurityarea"
                                                           class="easyui-validatebox inputCss" readonly
                                                           style="width:60px"/>
                                                </td>
                                            </tr>
                                            <tr align="center">
                                                <td width="8%">郵箱地址<font color="red">*</font></td>
                                                <td colspan="3" class="td_style1">
                                                    <input id="useremail${status.index}"
                                                           name="itemsEntitys[${status.index}].useremail"
                                                           class="easyui-validatebox" style="width:300px;"
                                                           data-options="required:true"
                                                           value="${itemsEntity.useremail}"/>
                                                </td>
                                                <td width="8%">申请類型<font color="red">*</font></td>
                                                <td class="td_style1">
                                                    <input id="applytype${status.index}"
                                                           name="itemsEntitys[${status.index}].applytype"
                                                           class="easyui-combobox" style="width:100px"
                                                           data-options="panelHeight:'auto',valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/dict_softApplytype',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; },validType:'comboxValidate[\'applytype${status.index}\',\'请選擇申請類型\']',onSelect:function(){onchangeApplytype(${status.index});}"/>
                                                    <input id="applytypename${status.index}"
                                                           name="itemsEntitys[${status.index}].applytypename"
                                                           type="hidden" value="${itemsEntity.applytypename}"/>
                                                </td>
                                                <td width="8%">軟件類型<font color="red">*</font></td>
                                                <td class="td_style1">
                                                    <input id="softtype${status.index}"
                                                           name="itemsEntitys[${status.index}].softtype"
                                                           class="easyui-combobox" style="width:100px"
                                                           data-options="panelHeight:'auto',valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/dict_softtype',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; },validType:'comboxValidate[\'softtype${status.index}\',\'请選擇軟件類型\']'"/>
                                                    <input id="softtypename${status.index}"
                                                           name="itemsEntitys[${status.index}].softtypename"
                                                           type="hidden" value="${itemsEntity.softtypename}"/>
                                                </td>
                                                <td width="10%">是否有外網權限<font color="red">*</font></td>
                                                <td class="td_style1">
                                                    <input id="ifnetwork${status.index}"
                                                           name="itemsEntitys[${status.index}].ifnetwork"
                                                           class="easyui-validatebox inputCss"
                                                           data-options="required:true"
                                                           style="width:60px" value="${itemsEntity.ifnetwork}"/>
                                                </td>
                                            </tr>
                                            <tr align="center">
                                                <td width="8%">軟體資產編號<font color="red">*</font></td>
                                                <td class="td_style1">
                                                    <input id="softnumber${status.index}"
                                                           name="itemsEntitys[${status.index}].softnumber"
                                                           class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true"
                                                           value="${itemsEntity.softnumber}"/>
                                                </td>
                                                <td width="8%">軟體名稱<font color="red">*</font></td>
                                                <td class="td_style1">
                                                    <input id="softname${status.index}"
                                                           name="itemsEntitys[${status.index}].softname"
                                                           class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true"
                                                           value="${itemsEntity.softname}"/>
                                                </td>
                                                <td width="8%">軟體版本<font color="red">*</font></td>
                                                <td class="td_style1">
                                                    <input id="softversion${status.index}"
                                                           name="itemsEntitys[${status.index}].softversion"
                                                           class="easyui-validatebox" data-options="required:true"
                                                           style="width:100px" value="${itemsEntity.softversion}"/>
                                                </td>
                                                <td width="8%">採購數量<font color="red">*</font></td>
                                                <td class="td_style1">
                                                    <input id="caigounum${status.index}"
                                                           name="itemsEntitys[${status.index}].caigounum"
                                                           class="easyui-validatebox" data-options="required:true"
                                                           style="width:100px" value="${itemsEntity.caigounum}"/>
                                                </td>
                                                <td width="8%">可配發數量<font color="red">*</font></td>
                                                <td class="td_style1">
                                                    <input id="kepeifanum${status.index}"
                                                           name="itemsEntitys[${status.index}].kepeifanum"
                                                           class="easyui-validatebox" data-options="required:true"
                                                           style="width:60px" value="${itemsEntity.kepeifanum}"/>
                                                </td>
                                            </tr>
                                            <tr align="center" id="itemsEntitys${status.index+1}">
                                                <td width="10%">軟件授權開始日期<font color="red">*</font></td>
                                                <td colspan="3" class="td_style1">
                                                    <input id="authorizestart${status.index}"
                                                           name="itemsEntitys[${status.index}].authorizestart"
                                                           class="easyui-validatebox Wdate"
                                                           data-options="required:true" style="width:150px"
                                                           value="${itemsEntity.authorizestart}"
                                                           onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"/>
                                                </td>
                                                <td width="8%">軟件到期日期<font color="red">*</font></td>
                                                <td colspan="5" class="td_style1">
                                                    <input id="authorizeend${status.index}"
                                                           name="itemsEntitys[${status.index}].authorizeend"
                                                           class="easyui-validatebox Wdate"
                                                           data-options="required:true" style="width:150px"
                                                           value="${itemsEntity.authorizeend}"
                                                           onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'authorizestart${status.index}\')}',maxDate:'#F{$dp.$D(\'authorizestart${status.index}\',{M:6})}'})"/>
                                                </td>
                                            </tr>
                                        </c:forEach>
                                    </c:if>
                                    <c:if test="${wfSoftinstallProcessEntity.itemsEntitys==null||wfSoftinstallProcessEntity.itemsEntitys.size()==0}">
                                        <tr id="itemsEntitysTitle0">
                                            <input id="xuhao0" type="hidden" name="itemsEntitys[0].xuhao"
                                                   value="1"/>
                                            <td colspan="10" align="center" bgcolor="#faebd7">第1位使用人信息</td>
                                        </tr>
                                        <tr align="center">
                                            <td width="10%">使用人工號<font color="red">*</font></td>
                                            <td width="10%" class="td_style1">
                                                <input id="userno0" onblur="getUserNameByEmpno2(this,0);"
                                                       name="itemsEntitys[0].userno" class="easyui-validatebox"
                                                       style="width:80px;"
                                                       data-options="required:true"/>
                                            </td>
                                            <td width="8%">使用人姓名</td>
                                            <td width="10%" class="td_style1">
                                                <input id="username0" name="itemsEntitys[0].username"
                                                       class="easyui-validatebox inputCss" style="width:80px;"
                                                       readonly/>
                                            </td>
                                            <td width="10%">使用廠區<font color="red">*</font></td>
                                            <td width="10%" class="td_style1">
                                                <input id="userfactoryid0" class="easyui-combobox"
                                                       name="itemsEntitys[0].userfactoryid"
                                                       data-options="panelHeight:300,valueField:'factoryid',textField:'factoryname',editable:false,validType:'comboxValidate[\'userfactoryid0\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(0)},onSelect:function(){pcOnchangeFactory('user',0)}"
                                                       style="width:100px;"/>
                                                <input id="userfactoryname0" name="itemsEntitys[0].userfactoryname"
                                                       type="hidden"/>
                                            </td>
                                            <td width="8%">使用區域<font color="red">*</font></td>
                                            <td colspan="3" class="td_style1">
                                                <input id="arealist0" name="itemsEntitys[0].arealist"
                                                       data-options="validType:'comboxValidate[\'arealist0\',\'请選擇區域\']',onSelect:function(){pcOnchangeArea('arealist0',0);}"
                                                       class="easyui-combobox" style="width:80px;"
                                                       panelHeight="auto"/>/
                                                <input id="buildinglist0" name="itemsEntitys[0].buildinglist"
                                                       data-options="validType:'comboxValidate[\'buildinglist0\',\'请選擇樓棟\']',onSelect:function(){pcOnchangeBuilding(0);}"
                                                       class="easyui-combobox" style="width:80px;"
                                                       panelHeight="auto"/>
                                                <input id="arealistname0" name="itemsEntitys[0].arealistname"
                                                       type="hidden"/>
                                                <input id="buildinglistname0"
                                                       name="itemsEntitys[0].buildinglistname" type="hidden"/>
                                            </td>
                                        </tr>
                                        <tr align="center">
                                            <td width="8%">單位<font color="red">*</font></td>
                                            <td colspan="3" class="td_style1">
                                                <input id="userdept0" name="itemsEntitys[0].userdept"
                                                       class="easyui-validatebox" style="width:300px;"/>
                                            </td>
                                            <td width="8%">電腦名稱<font color="red">*</font></td>
                                            <td class="td_style1">
                                                <input id="pcname0" name="itemsEntitys[0].pcname"
                                                       class="easyui-validatebox" style="width:100px;"
                                                       data-options="required:true"/>
                                            </td>
                                            <td width="8%">IP地址<font color="red">*</font></td>
                                            <td class="td_style1">
                                                <input id="pcip0" name="itemsEntitys[0].pcip"
                                                       onblur="isNpiCheck(this,0);"
                                                       class="easyui-validatebox"
                                                       data-options="required:true,validType:'ip[\'pcip0\']'"
                                                       style="width:100px"/>
                                            </td>
                                            <td width="10%">是否安保區域<font color="red">*</font></td>
                                            <td class="td_style1">
                                                <input id="usersecurityarea0"
                                                       name="itemsEntitys[0].usersecurityarea"
                                                       class="easyui-validatebox inputCss" readonly
                                                       style="width:60px"/>
                                            </td>
                                        </tr>
                                        <tr align="center">
                                            <td width="8%">郵箱地址<font color="red">*</font></td>
                                            <td colspan="3" class="td_style1">
                                                <input id="useremail0" name="itemsEntitys[0].useremail"
                                                       class="easyui-validatebox" style="width:300px;"/>
                                            </td>
                                            <td width="8%">申请類型<font color="red">*</font></td>
                                            <td class="td_style1">
                                                <input id="applytype0"
                                                       name="itemsEntitys[0].applytype"
                                                       class="easyui-combobox" style="width:100px"
                                                       data-options="panelHeight:'auto',valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/dict_softApplytype',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; },validType:'comboxValidate[\'applytype0\',\'请選擇申請類型\']',onSelect:function(){onchangeApplytype(0);}"/>
                                                <input id="applytypename0" name="itemsEntitys[0].applytypename"
                                                       type="hidden"/>
                                            </td>
                                            <td width="8%">軟件類型<font color="red">*</font></td>
                                            <td class="td_style1">
                                                <input id="softtype0"
                                                       name="itemsEntitys[0].softtype"
                                                       class="easyui-combobox" style="width:100px"
                                                       data-options="panelHeight:'auto',valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/dict_softtype',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; },validType:'comboxValidate[\'softtype0\',\'请選擇軟件類型\']',onSelect:function(){onchangeSofttype(0);}"/>
                                                <input id="softtypename0" name="itemsEntitys[0].softtypename"
                                                       type="hidden"/>
                                            </td>
                                            <td width="10%">是否有外網權限<font color="red">*</font></td>
                                            <td class="td_style1">
                                                <input id="ifnetwork0" name="itemsEntitys[0].ifnetwork"
                                                       class="easyui-validatebox inputCss"
                                                       data-options="required:true"
                                                       style="width:60px"/>
                                            </td>
                                        </tr>
                                        <tr align="center">
                                            <td width="8%">軟體資產編號<font color="red">*</font></td>
                                            <td class="td_style1">
                                                <input id="softnumber0" name="itemsEntitys[0].softnumber"
                                                       class="easyui-validatebox" style="width:100px;"
                                                       data-options="required:true"/>
                                            </td>
                                            <td width="8%">軟體名稱<font color="red">*</font></td>
                                            <td class="td_style1">
                                                <input id="softname0" name="itemsEntitys[0].softname"
                                                       class="easyui-validatebox" style="width:100px;"
                                                       data-options="required:true"/>
                                            </td>
                                            <td width="8%">軟體版本<font color="red">*</font></td>
                                            <td class="td_style1">
                                                <input id="softversion0" name="itemsEntitys[0].softversion"
                                                       class="easyui-validatebox" data-options="required:true"
                                                       style="width:100px"/>
                                            </td>
                                            <td width="8%">採購數量<font color="red">*</font></td>
                                            <td class="td_style1">
                                                <input id="caigounum0" name="itemsEntitys[0].caigounum"
                                                       class="easyui-validatebox" data-options="required:true"
                                                       style="width:100px"/>
                                            </td>
                                            <td width="8%">可配發數量<font color="red">*</font></td>
                                            <td class="td_style1">
                                                <input id="kepeifanum0" name="itemsEntitys[0].kepeifanum"
                                                       class="easyui-validatebox" data-options="required:true"
                                                       style="width:60px"/>
                                            </td>
                                        </tr>
                                        <tr align="center" id="itemsEntitys0">
                                            <td width="10%">軟件授權開始日期<font color="red">*</font></td>
                                            <td colspan="3" class="td_style1">
                                                <input id="authorizestart0" name="itemsEntitys[0].authorizestart"
                                                       class="easyui-validatebox Wdate"
                                                       data-options="required:true" style="width:150px"
                                                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"/>
                                            </td>
                                            <td width="8%">軟件到期日期<font color="red">*</font></td>
                                            <td colspan="5" class="td_style1">
                                                <input id="authorizeend0" name="itemsEntitys[0].authorizeend"
                                                       class="easyui-validatebox Wdate"
                                                       data-options="required:true" style="width:150px"
                                                />
                                            </td>
                                        </tr>
                                    </c:if>
                                    </tbody>
                                    <tr align="left" class="nottr">
                                        <td colspan="10" width="100%" style="text-align:center;padding-left:10px;">
                                            <a href="#" id="pcItemAdd" class="easyui-linkbutton"
                                               data-options="iconCls:'icon-add',text:'添加一筆'"
                                               style="width: 100px"></a>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>需求說明&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style2" align="left">
                                <textarea id="describtion" name="describtion"
                                          class="easyui-validatebox"
                                          oninput="return LessThan(this);"
                                          onchange="return LessThan(this);"
                                          onpropertychange="return LessThan(this);"
                                          maxlength="300"
                                          style="width:90%;height:80px;" data-options="required:true"
                                          rows="5" cols="6"
                                          data-options="required:true,validType:'length[0,300]'">${wfSoftinstallProcessEntity.describtion }</textarea><span
                                    id="txtNum"></span></td>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件<font color="red">*</font></td>
                            <td width="90%" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button"
                                                                   value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <c:choose>
                                    <c:when test="${file.size()>0}">
                                        <input type="hidden" id="attachids" name="attachids"
                                               value="${wfSoftinstallProcessEntity.attachids }"/>
                                    </c:when>
                                    <c:otherwise>
                                        <input type="hidden" id="attachids" name="attachids" value=""/>
                                    </c:otherwise>
                                </c:choose>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">備註</td>
                            <td colspan="9" class="td_style2">
                                <font color="red">1.軟件安裝申請單需核准至產品處級/機能總處級/製造總處級權限主管；</font><br/>
                                2.軟件資產編號、可配發數量、授權信息等欄位可聯繫資安部進行咨詢；<br/>
                                3.軟件授權到期前3個月郵件通知單位軟體管理員，軟件授權到期前1個月郵件通知單位單位處級主管，請提前進行軟件續期或採購運作；<br/>
                                4.試用軟件申請最長期限為6個月。
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','軟體安裝申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="5" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno2_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'yl2Table','ylno2','ylname2',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="ylno2"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}"
                                                               readonly
                                                               value="${wfSoftinstallProcessEntity.ylno2 }"/><c:if
                                                            test="${requiredMap['ylno2'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname2" name="ylname2"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}"
                                                                value="${wfSoftinstallProcessEntity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#applydeptno').val(),'cchargeno','cchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfSoftinstallProcessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfSoftinstallProcessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="czchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['czchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('czchargeTable',$('#applydeptno').val(),'czchargeno','czchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="czchargeno" name="czchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['czchargeno']}"
                                                               readonly
                                                               value="${wfSoftinstallProcessEntity.czchargeno }"/><c:if
                                                            test="${requiredMap['czchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="czchargename" name="czchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['czchargeno']}"
                                                                value="${wfSoftinstallProcessEntity.czchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole33('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfSoftinstallProcessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfSoftinstallProcessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="20%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#applydeptno').val(),'zcchargeno','zcchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wfSoftinstallProcessEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfSoftinstallProcessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl15Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno15_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(25,'yl15Table','ylno15','ylname15',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno15" name="ylno15"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno15']}"
                                                               readonly
                                                               value="${wfSoftinstallProcessEntity.ylno15 }"/><c:if
                                                            test="${requiredMap['ylno15'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname15" name="ylname15"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno15']}"
                                                                value="${wfSoftinstallProcessEntity.ylname15 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl14Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno14_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(303,'yl14Table','ylno14','ylname14',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno14" name="ylno14"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno14']}"
                                                               readonly
                                                               value="${wfSoftinstallProcessEntity.ylno14 }"/><c:if
                                                            test="${requiredMap['ylno14'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname14" name="ylname14"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno14']}"
                                                                value="${wfSoftinstallProcessEntity.ylname14 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl6Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno6_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(155,'yl6Table','ylno6','ylname6',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno6" name="ylno6"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno6']}"
                                                               readonly
                                                               value="${wfSoftinstallProcessEntity.ylno6 }"/><c:if
                                                            test="${requiredMap['ylno6'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname6" name="ylname6"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno6']}"
                                                                value="${wfSoftinstallProcessEntity.ylname6 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zacwchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zacwchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(193,'zacwchargeTable','zacwchargeno','zacwchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zacwchargeno" name="zacwchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zacwchargeno']}"
                                                               readonly
                                                               value="${wfSoftinstallProcessEntity.zacwchargeno }"/>
                                                        <c:if test="${requiredMap['zacwchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        /<input id="zacwchargename" name="zacwchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zacwchargeno']}"
                                                                value="${wfSoftinstallProcessEntity.zacwchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="20%" style="float: left;margin-left: 5px;" id="sychargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['sychargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('sychargeTable',$('#applydeptno').val(),'sychargeno','sychargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="sychargeno" name="sychargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['sychargeno']}"
                                                               readonly
                                                               value="${wfSoftinstallProcessEntity.sychargeno }"/><c:if
                                                            test="${requiredMap['sychargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="sychargename" name="sychargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['sychargeno']}"
                                                                value="${wfSoftinstallProcessEntity.sychargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl8Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno8_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole5(26,'ylno8','ylname8','','','','',$('#applyfactoryid').combobox('getValue'),$('#applyarea').combobox('getValue'),$('#applybuilding').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno8" name="ylno8"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno8']}"
                                                               readonly
                                                               value="${wfSoftinstallProcessEntity.ylno8 }"/><c:if
                                                            test="${requiredMap['ylno8'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname8" name="ylname8"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno8']}"
                                                                value="${wfSoftinstallProcessEntity.ylname8 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfSoftinstallProcessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;"
                                   onclick="canelTask('${wfSoftinstallProcessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="areaId" name="areaId" value=""/>
    <input type="hidden" id="onlyKchargeSignle" value="1" />
    <input type="hidden" id="buildingId" name="buildingId" value=""/>
    <div id="win"></div>
    <div id="dlg"></div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value=""/>
<script src='${ctx}/static/js/information/wfsoftinstallprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>