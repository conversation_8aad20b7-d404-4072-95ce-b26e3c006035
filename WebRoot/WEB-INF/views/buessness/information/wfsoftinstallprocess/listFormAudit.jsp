<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>軟體安裝申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfsoftinstallprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfSoftinstallProcessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfSoftinstallProcessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">軟體安裝申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfSoftinstallProcessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfSoftinstallProcessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfSoftinstallProcessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfSoftinstallProcessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfSoftinstallProcessEntity.makerno}/${wfSoftinstallProcessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">承辦人工號</td>
                            <td width="5%" class="td_style2">${wfSoftinstallProcessEntity.dealno}</td>
                            <td width="5%">承辦人</td>
                            <td width="5%" class="td_style2">${wfSoftinstallProcessEntity.dealname}</td>
                            <td width="5%">單位代碼</td>
                            <td width="5%" class="td_style2">${wfSoftinstallProcessEntity.dealdeptno}</td>
                            <td width="5%">廠區</td>
                            <td width="5%" class="td_style2">
                                <c:choose>
                                    <c:when test="${wfSoftinstallProcessEntity.dealfactoryname==null||wfSoftinstallProcessEntity.dealfactoryname==''}">
                                        <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                               panelHeight="auto" value="${wfSoftinstallProcessEntity.dealfactoryid }"
                                               disabled
                                               data-options="width: 120,required:true"/>
                                    </c:when>
                                    <c:otherwise>
                                        ${wfSoftinstallProcessEntity.dealfactoryname}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td width="5%">聯繫分機</td>
                            <td width="5%" class="td_style2">${wfSoftinstallProcessEntity.dealtel}</td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="5" class="td_style2">${wfSoftinstallProcessEntity.dealdeptname}</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfSoftinstallProcessEntity.dealemail}</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">申請人工號</td>
                            <td width="5%" class="td_style2">${wfSoftinstallProcessEntity.applyno}</td>
                            <td width="5%">申請人</td>
                            <td width="5%" class="td_style2">${wfSoftinstallProcessEntity.applyname}</td>
                            <td width="5%">單位代碼</td>
                            <td width="5%" class="td_style2">${wfSoftinstallProcessEntity.applydeptno}</td>
                            <td width="5%">費用代碼</td>
                            <td width="5%" class="td_style2">${wfSoftinstallProcessEntity.applycostno}</td>
                            <td width="3%">所在廠區</td>
                            <td width="7%" class="td_style2">
                                <c:choose>
                                    <c:when test="${wfSoftinstallProcessEntity.applyfactoryname==null||wfSoftinstallProcessEntity.applyfactoryname==''}">
                                        <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                               panelHeight="auto" value="${wfSoftinstallProcessEntity.applyfactoryid }"
                                               data-options="width: 120,disabled:true,required:true,validType:'comboxValidate[\'applyfactoryid\',\'请选择所在廠區\']',onSelect:function(){onchangeFactory('applyfactoryid');}"/>
                                    </c:when>
                                    <c:otherwise>
                                        ${wfSoftinstallProcessEntity.applyfactoryname}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style2">${wfSoftinstallProcessEntity.applyleveltype}</td>
                            <td>管理職</td>
                            <td class="td_style2">${wfSoftinstallProcessEntity.applymanager}</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfSoftinstallProcessEntity.applyemail}</td>
                            <td>使用區域</td>
                            <td class="td_style2">
                                <c:choose>
                                    <c:when test="${wfSoftinstallProcessEntity.applyareaname==null||wfSoftinstallProcessEntity.applyareaname==''}">
                                        <input id="applyarea" name="applyarea" class="easyui-combobox"
                                               data-options="width: 70,disabled:true,onSelect:function(){onchangeArea();},validType:'comboxValidate[\'applyarea\',\'請選擇使用區域\']'"
                                               value="${wfSoftinstallProcessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                        <input id="applybuilding" name="applybuilding" class="easyui-combobox"
                                               data-options="width: 70,disabled:true,validType:'comboxValidate[\'applybuilding\',\'請選擇使用區域\']'"
                                               value="${wfSoftinstallProcessEntity.applybuilding }" panelHeight="auto"/>
                                    </c:when>
                                    <c:otherwise>
                                        ${wfSoftinstallProcessEntity.applyareaname}/${wfSoftinstallProcessEntity.applybuildingname}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="5%">聯繫分機</td>
                            <td width="5%" class="td_style2">${wfSoftinstallProcessEntity.applyphone}</td>
                            <td>單位</td>
                            <td colspan="5" class="td_style2">${wfSoftinstallProcessEntity.applydeptname}</td>
                            <td>安保區域</td>
                            <td class="td_style2">
                                <c:if test="${wfSoftinstallProcessEntity.securityarea.equals('1')}">非特保區</c:if>
                                <c:if test="${wfSoftinstallProcessEntity.securityarea.equals('0')}">特保區</c:if>
                            </td>
                        </tr>
                        <c:choose>
                            <c:when test="${wfSoftinstallProcessEntity.adminno!=null&&wfSoftinstallProcessEntity.adminno!=''}">
                                <tr>
                                    <td colspan="10" class="td_style1">單位軟體管理信息</td>
                                </tr>
                                <tr align="center">
                                    <td width="5%">管理員工號</td>
                                    <td width="5%" class="td_style2">${wfSoftinstallProcessEntity.adminno}</td>
                                    <td width="5%">管理員姓名</td>
                                    <td width="5%" class="td_style2">${wfSoftinstallProcessEntity.adminname}</td>
                                    <td>聯繫郵箱</td>
                                    <td colspan="5" class="td_style2">${wfSoftinstallProcessEntity.adminemail}（軟件授權到期前3個月郵件通知單位軟體資產管理員）</td>
                                </tr>
                                <tr align="center">
                                    <td width="5%">處級主管工號</td>
                                    <td width="5%" class="td_style2">${wfSoftinstallProcessEntity.chujino}</td>
                                    <td width="5%">處級主管姓名</td>
                                    <td width="5%" class="td_style2">${wfSoftinstallProcessEntity.chujiname}</td>
                                    <td>處級主管郵箱</td>
                                    <td colspan="5" class="td_style2">${wfSoftinstallProcessEntity.chujiemail}（軟件授權到期前1個月郵件通知單位處級主管）</td>
                                </tr>
                            </c:when>
                        </c:choose>
                        <c:choose>
                            <c:when test="${wfSoftinstallProcessEntity.adminno!=null&&wfSoftinstallProcessEntity.adminno!=''}">
                                <tr>
                                    <td colspan="10" class="td_style1">軟體安裝明細</td>
                                </tr>
                                <tr align="center">
                                    <td colspan="10" width="100%">
                                        <table class="formList">
                                            <tbody id="info_Body">
                                            <c:if test="${wfSoftinstallProcessEntity.itemsEntitys!=null&&wfSoftinstallProcessEntity.itemsEntitys.size()>0}">
                                                <c:forEach items="${wfSoftinstallProcessEntity.itemsEntitys}"
                                                           var="itemsEntity"
                                                           varStatus="status">
                                                    <tr>
                                                        <td colspan="10" align="center" bgcolor="#faebd7">
                                                            第${status.index+1}位使用人信息
                                                        </td>
                                                    </tr>
                                                    <tr align="center">
                                                        <td width="10%">使用人工號</td>
                                                        <td width="10%" class="td_style2">${itemsEntity.userno}</td>
                                                        <td width="8%">使用人姓名</td>
                                                        <td width="10%" class="td_style2">${itemsEntity.username}</td>
                                                        <td width="10%">使用廠區</td>
                                                        <td width="10%"
                                                            class="td_style2">${itemsEntity.userfactoryname}</td>
                                                        <td width="8%">使用區域</td>
                                                        <td colspan="3"
                                                            class="td_style2">${itemsEntity.arealistname}/${itemsEntity.buildinglistname}</td>
                                                    </tr>
                                                    <tr align="center">
                                                        <td width="8%">單位</td>
                                                        <td colspan="3" class="td_style2">${itemsEntity.userdept}</td>
                                                        <td width="8%">電腦名稱</td>
                                                        <td class="td_style2">${itemsEntity.pcname}</td>
                                                        <td width="8%">IP地址</td>
                                                        <td class="td_style2">${itemsEntity.pcip}</td>
                                                        <td width="10%">是否安保區域</td>
                                                        <td class="td_style2">${itemsEntity.usersecurityarea}</td>
                                                    </tr>
                                                    <tr align="center">
                                                        <td width="8%">郵箱地址</td>
                                                        <td colspan="3" class="td_style2">${itemsEntity.useremail}</td>
                                                        <td width="8%">申请類型</td>
                                                        <td class="td_style2">${itemsEntity.applytypename}</td>
                                                        <td width="8%">軟件類型</td>
                                                        <td class="td_style2">${itemsEntity.softtypename}</td>
                                                        <td width="10%">是否有外網權限</td>
                                                        <td class="td_style2">${itemsEntity.ifnetwork}</td>
                                                    </tr>
                                                    <tr align="center">
                                                        <td width="8%">軟體資產編號</td>
                                                        <td class="td_style2">
                                                            <c:if test="${itemsEntity.softnumber==null || itemsEntity.softnumber==''}">/</c:if>
                                                            <c:if test="${itemsEntity.softnumber!=null}">${itemsEntity.softnumber}</c:if>
                                                        </td>
                                                        <td width="8%">軟體名稱</td>
                                                        <td class="td_style2">${itemsEntity.softname}</td>
                                                        <td width="8%">軟體版本</td>
                                                        <td class="td_style2">${itemsEntity.softversion}</td>
                                                        <td width="8%">採購數量</td>
                                                        <td class="td_style2">
                                                            <c:if test="${itemsEntity.caigounum==null || itemsEntity.caigounum==''}">/</c:if>
                                                            <c:if test="${itemsEntity.caigounum!=null}">${itemsEntity.caigounum}</c:if>
                                                        </td>
                                                        <td width="8%">可配發數量</td>
                                                        <td class="td_style2">
                                                            <c:if test="${itemsEntity.kepeifanum==null || itemsEntity.kepeifanum==''}">/</c:if>
                                                            <c:if test="${itemsEntity.kepeifanum!=null}">${itemsEntity.kepeifanum}</c:if>
                                                        </td>
                                                    </tr>
                                                    <tr align="center" id="itemsEntitys${status.index+1}">
                                                        <td width="10%">軟件授權開始日期</td>
                                                        <td colspan="3" class="td_style2">
                                                            <fmt:formatDate value='${itemsEntity.authorizestart}'
                                                                            pattern='yyyy-MM-dd'/>
                                                        </td>
                                                        <td width="8%">軟件到期日期</td>
                                                        <td colspan="5" class="td_style2">
                                                            <fmt:formatDate value='${itemsEntity.authorizeend}'
                                                                            pattern='yyyy-MM-dd'/>
                                                        </td>
                                                    </tr>
                                                </c:forEach>
                                            </c:if>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <tr>
                                    <td colspan="10" class="td_style1">軟體安裝明細</td>
                                </tr>
                                <tr align="center">
                                    <td colspan="10" width="100%">
                                        <div style="overflow-x: auto;width: 1200px;">
                                            <table id="softItemTable1" width="100%">
                                                <tr align="center">
                                                    <td width="100px;">申請人工號</td>
                                                    <td width="100px;">申請人</td>
                                                    <td width="150px;">所在廠區</td>
                                                    <td width="200px;">使用區域</td>
                                                    <td width="100px;">安保區域</td>
                                                    <td width="100px;">電腦名稱</td>
                                                    <td width="100px;">IP地址</td>
                                                    <td width="100px;">軟體名稱</td>
                                                    <td width="100px;">軟體版本</td>
                                                </tr>
                                                <tbody id="info_Body1">
                                                <c:if test="${wfSoftinstallProcessEntity.itemsEntitys!=null&&wfSoftinstallProcessEntity.itemsEntitys.size()>0}">
                                                    <c:forEach items="${wfSoftinstallProcessEntity.itemsEntitys}"
                                                               var="itemsEntity" varStatus="status">
                                                        <tr align="center" id="itemsEntitys${status.index}">
                                                            <td>${itemsEntity.userno}</td>
                                                            <td>${itemsEntity.username}</td>
                                                            <td>
                                                                <input id="userfactoryid${status.index}"
                                                                       name="itemsEntitys[${status.index}].userfactoryid"
                                                                       data-options="panelHeight:300,disabled:true,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'userfactoryid${status.index}\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(${status.index});},onSelect:function(){pcOnchangeFactory('userfactoryid${status.index}',${status.index});}"
                                                                       style="width:100px;" class="easyui-combobox"
                                                                       value="${itemsEntity.userfactoryid}"/>
                                                            </td>
                                                            <td>
                                                                <input id="arealist${status.index}"
                                                                       name="itemsEntitys[${status.index}].arealist"
                                                                       data-options="disabled:true,validType:'comboxValidate[\'arealist${status.index}\',\'请選擇區域\']',onSelect:function(){pcOnchangeArea('arealist${status.index+1}',${status.index});}"
                                                                       class="easyui-combobox" style="width:80px;"
                                                                       value="${itemsEntity.arealist}"
                                                                       panelHeight="auto"/>/
                                                                <input id="buildinglist${status.index}"
                                                                       name="itemsEntitys[${status.index}].buildinglist"
                                                                       data-options="disabled:true,validType:'comboxValidate[\'buildinglist${status.index}\',\'请選擇樓棟\']'"
                                                                       class="easyui-combobox" style="width:80px;"
                                                                       value="${itemsEntity.buildinglist}"
                                                                       panelHeight="auto"/>
                                                            </td>
                                                            <td>
                                                                <input id="usersecurityarea${status.index}"
                                                                       name="itemsEntitys[${status.index}].usersecurityarea"
                                                                       class="easyui-combobox" style="width:80px"
                                                                       value="${itemsEntity.usersecurityarea}"
                                                                       data-options="disabled:true,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/security_area',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; },validType:'comboxValidate[\'usersecurityarea0\',\'请選擇安保區域\']'"/>
                                                            </td>
                                                            <td>${itemsEntity.pcname}</td>
                                                            <td>${itemsEntity.pcip}</td>
                                                            <td>${itemsEntity.softname}</td>
                                                            <td>${itemsEntity.softversion}</td>
                                                        </tr>
                                                    </c:forEach>
                                                </c:if>
                                                </tbody>
                                            </table>
                                        </div>
                                    </td>
                                </tr>
                            </c:otherwise>
                        </c:choose>
                        <tr align="center">
                            <td>需求說明</td>
                            <td colspan="9" class="td_style2" align="left">
                                <textarea id="describtion" name="describtion"
                                          class="easyui-validatebox"
                                          oninput="return LessThan(this);"
                                          onchange="return LessThan(this);"
                                          onpropertychange="return LessThan(this);"
                                          maxlength="300"
                                          style="width:90%;height:80px;"
                                          rows="5" cols="6"
                                          data-options="disabled:true,required:true,validType:'length[0,300]'">${wfSoftinstallProcessEntity.describtion }</textarea><span
                                    id="txtNum"></span></td>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style2">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${wfSoftinstallProcessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="9" class="td_style2">
                                <font color="red">1.軟件安裝申請單需簽核至機能總處/製造總處級及以上主管；</font><br/>
                                2.軟件資產編號、可配發數量、授權信息等欄位可聯繫資安部進行咨詢；<br/>
                                3.軟件授權到期前3個月郵件通知單位軟體管理員，軟件授權到期前1個月郵件通知單位單位處級主管，請提前進行軟件續期或採購運作；<br/>
                                4.試用軟件申請最長期限為6個月。
                            </td>
                        </tr>
                        <tr align="center">
                            <td>批註</td>
                            <td colspan="9" class="td_style2">
                                <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                          style="width:1000px;height:60px;"
                                          rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wfSoftinstallProcessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','軟體安裝申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfSoftinstallProcessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<div id="dlg"></div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<script src='${ctx}/static/js/information/wfsoftinstallprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>