<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>2FA指紋權限需求申請</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wf2faprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wf2faprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wf2faprocessEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wf2faprocessEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wf2faprocessEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wf2faprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${wf2faprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">2FA指紋權限需求申請</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wf2faprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wf2faprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wf2faprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wf2faprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wf2faprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wf2faprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${wf2faprocessEntity.makerno}/${wf2faprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="4%" class="td_style1">
                                <input id="dealno" name="dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wf2faprocessEntity.dealno}" onblur="queryUserInfo('deal');"/>

                            </td>
                            <td width="4%">承辦人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wf2faprocessEntity.dealname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wf2faprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="5%">費用代碼&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="dealcostno" name="dealcostno"
                                       class="easyui-validatebox" data-options="width: 90"
                                       value="${wf2faprocessEntity.dealcostno }"/>
                            </td>
                            <td width="4%">所在廠區</td>
                            <td width="6%" class="td_style1">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wf2faprocessEntity.dealfactoryid}"
                                       data-options="width: 120,validType:'comboxValidate[\'dealfactoryid\',\'请選擇所在廠區\']',onSelect:function(){onchangeFactory('deal');}"/>
                                <input id="dealnofactoryid" name="dealnofactoryid" type="hidden" value="${wf2faprocessEntity.dealnofactoryid}"/>
                                <input id="dealfactoryname" name="dealfactoryname" type="hidden" value="${wf2faprocessEntity.dealfactoryname}"/>

                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="dealleveltype" name="dealleveltype" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wf2faprocessEntity.dealleveltype }" readonly/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="dealmanager" name="dealmanager" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wf2faprocessEntity.dealmanager }" readonly/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="dealemail" class="easyui-validatebox"
                                       value="${wf2faprocessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td>使用區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealarea" name="dealarea" class="easyui-combobox" data-options="width: 70,validType:'comboxValidate[\'dealarea\',\'请選擇區域\']',onSelect:function(){onchangeArea('deal');}"
                                       value="${wf2faprocessEntity.dealarea }" panelHeight="auto"/>&nbsp;/
                                <input id="dealbuilding" name="dealbuilding" class="easyui-combobox" data-options="width: 70,validType:'comboxValidate[\'dealbuilding\',\'请選擇樓棟\']',onSelect:function(){onchangeBuilding('deal');}"
                                       value="${wf2faprocessEntity.dealbuilding }" panelHeight="auto"/>
                                <input id="dealareaname" name="dealareaname" type="hidden" value="${wf2faprocessEntity.dealareaname}"/>
                                <input id="dealbuildingname" name="dealbuildingname" type="hidden" value="${wf2faprocessEntity.dealbuildingname}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealtel" name="dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wf2faprocessEntity.dealtel }" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="7" class="td_style1">
                                <input id="dealdeptname" name="dealdeptname" class="easyui-validatebox" data-options="width: 410,required:true"
                                       value="${wf2faprocessEntity.dealdeptname }"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="wfsecurityapplyitemsTable" width="100%">
                                        <tr align="center">
                                            <td width="5%">序號</td>
                                            <td width="10%">申請人工號&nbsp;<font color="red">*</font></td>
                                            <td width="10%">申請人姓名&nbsp;<font color="red">*</font></td>
                                            <td width="12%">電腦編號&nbsp;<font color="red">*</font></td>
                                            <td width="12%">電腦IP&nbsp;<font color="red">*</font></td>
                                            <td width="12%">是否已錄入指紋&nbsp;<font color="red">*</font></td>
                                            <td width="15%">申請動作&nbsp;<font color="red">*</font></td>
                                            <td width="12%">合併後的工號&nbsp;<font color="red">*</font></td>
                                            <td width="5%">操作</td>
                                        </tr>
                                        <tbody id="info_Body">
                                        <c:if test="${wf2faprocessEntity.itemsEntitys!=null&&wf2faprocessEntity.itemsEntitys.size()>0}">
                                            <c:forEach items="${wf2faprocessEntity.itemsEntitys}" var="itemsEntity" varStatus="status">
                                                <tr align="center" id="itemsEntitys${status.index}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <input id="applyno${status.index}" name="itemsEntitys[${status.index}].applyno" class="easyui-validatebox"
                                                               data-options="width: 80,required:true" value="${itemsEntity.applyno}"
                                                               onblur="getUserNameByEmpno('apply',${status.index});"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyname${status.index}" name="itemsEntitys[${status.index}].applyname"
                                                               class="easyui-validatebox inputCss" value="${itemsEntity.applyname}"
                                                               data-options="width:80" readonly/>
                                                    </td>
                                                    <td>
                                                        <input id="pcno${status.index}" name="itemsEntitys[${status.index}].pcno" class="easyui-validatebox" style="width:120px;" data-options="required:true" value="${itemsEntity.pcno}"/>
                                                    </td>
                                                    <td>
                                                        <input id="pcip${status.index}" name="itemsEntitys[${status.index}].pcip" class="easyui-validatebox" style="width:120px;" data-options="required:true,validType:'ip[\'pcip${status.index}\']'" value="${itemsEntity.pcip}"/>
                                                    </td>
                                                    <td>
                                                        <div class="isinput${status.index}Div"></div>
                                                        <input id="isinput${status.index}Value"
                                                               name="isinput${status.index}Value"
                                                               type="hidden" class="easyui-validatebox" data-options="width: 100"
                                                               value="${itemsEntity.isinput }"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyaction${status.index}" name="itemsEntitys[${status.index}].applyaction"
                                                               data-options="onSelect:function(){adOnchangeAction('${status.index}');},valueField:'value',textField:'label',editable:false,url:'${ctx}/system/dict/getDictByType/dict_2faApplyaction',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; },validType:'comboxValidate[\'applyaction${status.index}\',\'请選擇申請動作\']'"
                                                               class="easyui-combobox"  value="${itemsEntity.applyaction}"/>
                                                    </td>
                                                    <td>
                                                        <input id="mergeempno${status.index}" name="itemsEntitys[${status.index}].mergeempno" class="easyui-validatebox" style="width:100px;"   value="${itemsEntity.mergeempno}"/>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="pcdeltr(${status.index});return false;"/>
                                                        <input id="shunxu${status.index}" type="hidden" name="itemsEntitys[${status.index}].shunxu" value="${status.index}"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${wf2faprocessEntity.itemsEntitys.size()==0 || wf2faprocessEntity.itemsEntitys==null}">
                                            <tr align="center" id="itemsEntitys0">
                                                <td>1</td>
                                                <td>
                                                    <input id="applyno0" name="itemsEntitys[0].applyno" class="easyui-validatebox"
                                                           data-options="width: 80,required:true" value=""
                                                           onblur="getUserNameByEmpno('apply',0);"/>
                                                </td>
                                                <td>
                                                    <input id="applyname0" name="itemsEntitys[0].applyname"
                                                           class="easyui-validatebox inputCss" value=""
                                                           data-options="width:80" readonly/>
                                                </td>
                                                <td>
                                                    <input id="pcno0" name="itemsEntitys[0].pcno" class="easyui-validatebox" style="width:120px;" data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="pcip0" name="itemsEntitys[0].pcip" class="easyui-validatebox" style="width:120px;" data-options="required:true,validType:'ip[\'pcip0\']'" value=""/>
                                                </td>
                                                <td>
                                                    <div class="isinput0Div"></div>
                                                    <input id="isinput0Value" name="isinput0Value"
                                                           type="hidden" class="easyui-validatebox" data-options="width: 100"
                                                           value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyaction0" name="itemsEntitys[0].applyaction"
                                                           data-options="onSelect:function(){adOnchangeAction('0');},valueField:'value',textField:'label',editable:false,url:'${ctx}/system/dict/getDictByType/dict_2faApplyaction',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; },validType:'comboxValidate[\'applyaction0\',\'请選擇申請動作\']'"
                                                           class="easyui-combobox"  value=""/>
                                                </td>
                                                <td>
                                                    <input id="mergeempno0" name="itemsEntitys[0].mergeempno" class="easyui-validatebox"  style="width:100px;"  value=""/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="pcdeltr(0);return false;"/>
                                                    <input id="shunxu0" type="hidden" name="itemsEntitys[0].shunxu" value="1"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr0">
                                            <td colspan="9" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="pcItemAdd" style="width:150px;float:left;" value="添加一行"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%"><a href="${ctx}/wf2faprocess/downLoad" id="btnBatchImportTpl">模板下載</a></td>
                            <td width="90%" class="td_style1">&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="batchImport" class="easyui-linkbutton" data-options="iconCls:'icon-hamburg-cv'" style="width: 100px;" onclick="openBatchImportWin();">批量導入</a>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>需求說明&nbsp;<font color="red">*</font></td>
                            <td width="90%" class="td_style2">
                                <textarea id="applyreason" name="applyreason"
                                          class="easyui-validatebox"
                                          oninput="return LessThan(this);"
                                          onchange="return LessThan(this);"
                                          onpropertychange="return LessThan(this);"
                                          maxlength="300"
                                          style="width:99%;height:80px;" data-options="required:true"
                                          rows="5" cols="6"
                                          data-options="required:true,validType:'length[0,300]'">${wf2faprocessEntity.applyreason}</textarea><span id="txtNum"></span>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">附件<font color="red">*</font></td>
                            <td width="90%" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <c:choose>
                                    <c:when test="${file.size()>0}">
                                        <input type="hidden" id="attachids" name="attachids"
                                               value="${wf2faprocessEntity.attachids }"/>
                                    </c:when>
                                    <c:otherwise>
                                        <input type="hidden" id="attachids" name="attachids" value=""/>
                                    </c:otherwise>
                                </c:choose>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">備註&nbsp;</td>
                            <td width="90%" class="td_style2">
                                <span>1.指紋權限需求申請需廠部級主管核准；</span></br>
                                <span>2.申請動作為合并指紋時，需填寫合并后的工號；</span></br>
                                <span>3.指紋信息導出，適用于資安制作離線指紋包使用。</span><br/>
                            </td>
                        </tr>

                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)" onclick="showWfImag('${processId}','2FA指紋權限需求申請');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zachargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zachargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(23,'zachargeTable','zachargeno','zachargename',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zachargeno" name="zachargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zachargeno']}"
                                                               readonly
                                                               value="${wf2faprocessEntity.zachargeno }"/><c:if
                                                            test="${requiredMap['zachargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zachargename" name="zachargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zachargeno']}"
                                                                value="${wf2faprocessEntity.zachargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealnofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wf2faprocessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wf2faprocessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#dealnofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wf2faprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wf2faprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zxywkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zxywkchargeno_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'zxywkchargeTable','zxywkchargeno','zxywkchargename',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxywkchargeno" name="zxywkchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxywkchargeno']}"
                                                               readonly
                                                               value="${wf2faprocessEntity.zxywkchargeno }"/><c:if
                                                            test="${requiredMap['zxywkchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxywkchargename" name="zxywkchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxywkchargeno']}"
                                                                value="${wf2faprocessEntity.zxywkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="dbazyTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['dbazyno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(176,'dbazyTable','dbazyno','dbazyname',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="dbazyno" name="dbazyno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['dbazyno']}"
                                                               readonly
                                                               value="${wf2faprocessEntity.dbazyno }"/><c:if
                                                            test="${requiredMap['dbazyno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="dbazyname" name="dbazyname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['dbazyno']}"
                                                                value="${wf2faprocessEntity.dbazyname }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wf2faprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;"
                                   onclick="canelTask('${wf2faprocessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="areaId" name="areaId" value="" />
    <input type="hidden" id="buildingId" name="buildingId" value="" />
    <input id="disOrEnabled" type="hidden" value=""/>
    <input id="loadOrNot" type="hidden" value="1"/>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
    <div id="dlg"></div>
</form>
<div id="optionWin" class="easyui-window" title="2FA指紋需求申請單信息導入" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%">
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="file" type="file" style="width: 300px"
                           accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <span id="labelListAddResult"></span><a href="${ctx}/wf2faprocess/errorExcel" id="downloadError"
                                                            plain="true">查看錯誤信息</a>
                </td>
            </tr>
        </table>
    </form>
</div>
<script src='${ctx}/static/js/information/wf2faprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>