<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>2FA指紋權限需求申請</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wf2faprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wf2faprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wf2faprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">2FA指紋權限需求申請</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wf2faprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wf2faprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wf2faprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wf2faprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wf2faprocessEntity.makerno}/${wf2faprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">承辦人工號</td>
                            <td width="4%" class="td_style2">${wf2faprocessEntity.dealno}</td>
                            <td width="4%">承辦人</td>
                            <td width="6%" class="td_style2">${wf2faprocessEntity.dealname}</td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style2">${wf2faprocessEntity.dealdeptno }</td>
                            <td width="5%">費用代碼</td>
                            <td width="5%" class="td_style2">${wf2faprocessEntity.dealcostno }</td>
                            <td width="4%">所在廠區</td>
                            <td width="6%" class="td_style2">${wf2faprocessEntity.dealfactoryname}</td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style2">${wf2faprocessEntity.dealleveltype }</td>
                            <td>管理職</td>
                            <td class="td_style2">${wf2faprocessEntity.dealmanager}</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wf2faprocessEntity.dealemail}</td>
                            <td>使用區域</td>
                            <td class="td_style2">
                               ${wf2faprocessEntity.dealareaname}/${wf2faprocessEntity.dealbuildingname}
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫方式</td>
                            <td class="td_style2">${wf2faprocessEntity.dealtel }</td>
                            <td>單位</td>
                            <td colspan="7" class="td_style2">${wf2faprocessEntity.dealdeptname }</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="wfsecurityapplyitemsTable" width="100%">
                                        <tr align="center">
                                            <td width="5%">序號</td>
                                            <td width="10%">申請人工號</td>
                                            <td width="10%">申請人姓名</td>
                                            <td width="12%">電腦編號</td>
                                            <td width="12%">電腦IP</td>
                                            <td width="12%">是否已錄入指紋</td>
                                            <td width="15%">申請動作</td>
                                            <td width="12%">合併後的工號</td>
                                        </tr>
                                        <tbody id="info_Body">
                                        <c:if test="${wf2faprocessEntity.itemsEntitys!=null&&wf2faprocessEntity.itemsEntitys.size()>0}">
                                            <c:forEach items="${wf2faprocessEntity.itemsEntitys}" var="itemsEntity" varStatus="status">
                                                <tr align="center" id="itemsEntitys${status.index}">
                                                    <td>${status.index+1}</td>
                                                    <td>${itemsEntity.applyno}</td>
                                                    <td>${itemsEntity.applyname}</td>
                                                    <td>${itemsEntity.pcno}</td>
                                                    <td>${itemsEntity.pcip}</td>
                                                    <td>${itemsEntity.isinput}</td>
                                                    <td>${itemsEntity.applyaction}</td>
                                                    <td>${itemsEntity.mergeempno}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">需求說明</td>
                            <td width="90%" class="td_style2">
                                <textarea id="applyreason" name="applyreason" class="easyui-validatebox" maxlength="300"
                                          style="width:99%;height:80px;" rows="5" cols="6"
                                          data-options="validType:'length[0,300]'">${wf2faprocessEntity.applyreason}</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <input type="hidden" id="attachids" name="attachids" value="${wf2faprocessEntity.attachids}"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%">
						        <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;" rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" serialNo="${wf2faprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','2FA指紋權限需求申請');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wf2faprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<input id="loadOrNot" type="hidden" value="0"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/information/wf2faprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>