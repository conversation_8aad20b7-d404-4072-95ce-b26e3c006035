<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>物聯網卡申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/iotcardprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${iotCardProcessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${iotCardProcessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">物聯網卡申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${iotCardProcessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${iotCardProcessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${iotCardProcessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${iotCardProcessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty iotCardProcessEntity.dealno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty iotCardProcessEntity.dealno}">
            <div class="position_R margin_R">
                填單人：${iotCardProcessEntity.dealno}/${iotCardProcessEntity.dealname}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td colspan="10" class="td_style1">承辦人基本信息</td>
            </tr>
            <c:choose>
                <c:when test="${not empty iotCardProcessEntity.makerno}">
                    <input type="hidden" id="isNew" value="0"/>
                    <tr align="center">
                        <td width="6%">承辦人工號</td>
                        <td width="6%" class="td_style1">
                            <input id="makerno" name="makerno" class="easyui-validatebox"
                                   data-options="width: 80,required:true"
                                   value="${iotCardProcessEntity.makerno}" onblur="loadDealUserInfo();"/>
                            <font color="red">*</font>
                        </td>
                        <td width="4%">承辦人</td>
                        <td width="6%" class="td_style1">
                            <input id="makername" name="makername"
                                   class="easyui-validatebox inputCss"
                                   data-options="width:80" readonly
                                   value="${iotCardProcessEntity.makername}"/>
                        </td>
                        <td width="4%">單位代碼</td>
                        <td width="6%" class="td_style1">
                            <input id="makerdeptno" name="makerdeptno"
                                   class="easyui-validatebox inputCss"
                                   data-options="width:80" readonly
                                   value="${iotCardProcessEntity.makerdeptno}"/>
                        </td>
                        <td width="4%">所在廠區</td>
                        <td width="7%" class="td_style1">
                            <input id="makerfactoryid" name="makerfactoryid" class="easyui-combobox"
                                   disabled
                                   panelHeight="auto" value="${iotCardProcessEntity.applyFactoryId }"
                                   data-options="width: 120,required:true,validType:'comboxValidate[\'applyFactoryId\',\'请选择所在廠區\']'"/>
                        </td>
                        <td width="4%">聯繫分機</td>
                        <td width="10%" class="td_style1">
                            <input id="makertel" name="makertel" class="easyui-validatebox"
                                   style="width:90px;"
                                   value="${iotCardProcessEntity.makertel}" data-options="required:true"
                                   onblur="valdApplyTel(this)"/>
                            <font color="red">*</font>
                        </td>
                    </tr>
                    <tr align="center">
                        <td width="6%">單位</td>
                        <td colspan="5" class="td_style1">
                            <input id="makerdeptname" name="makerdeptname" class="easyui-validatebox"
                                   data-options="width: 450,required:true"
                                   value="${iotCardProcessEntity.makerdeptname}"/>
                            <font color="red">*</font>
                        </td>
                        <td width="4%">聯繫郵箱</td>
                        <td colspan="3" class="td_style1">
                            <input id="makeremail" name="makeremail" class="easyui-validatebox"
                                   data-options="width: 300,required:true"
                                   value="${iotCardProcessEntity.makeremail}"/>
                            <font color="red">*</font>
                        </td>
                    </tr>
                </c:when>
                <c:otherwise>
                    <input type="hidden" id="isNew" value="1"/>
                    <tr align="center">
                        <td width="6%">承辦人工號<font color="red">*</font></td>
                        <td width="6%" class="td_style1">
                            <input id="makerno" name="makerno" class="easyui-validatebox"
                                   data-options="width: 80,required:true"
                                   value="${user.loginName}" onblur="loadDealUserInfo();"/>
                        </td>
                        <td width="4%">承辦人</td>
                        <td width="6%" class="td_style1">
                            <input id="makername" name="makername"
                                   class="easyui-validatebox inputCss"
                                   data-options="width:80" readonly value="${user.name}"/>
                        </td>
                        <td width="4%">單位代碼</td>
                        <td width="6%" class="td_style1">
                            <input id="makerdeptno" name="makerdeptno"
                                   class="easyui-validatebox inputCss"
                                   data-options="width:80" readonly value=""/>
                        </td>
                        <td width="4%">所在廠區</td>
                        <td width="7%" class="td_style1">
                            <input id="makerfactoryid" name="makerfactoryid" class="easyui-combobox"
                                   panelHeight="auto" value="" disabled
                                   data-options="width: 120,required:true,validType:'comboxValidate[\'applyFactoryId\',\'请选择所在廠區\']'"/>
                        </td>
                        <td width="4%">聯繫分機<font color="red">*</font></td>
                        <td width="10%" class="td_style1">
                            <input id="makertel" name="makertel" class="easyui-validatebox"
                                   style="width:90px;"
                                   value="${user.phone}" data-options="required:true"/>
                        </td>
                    </tr>
                    <tr align="center">
                        <td width="6%">單位<font color="red">*</font></td>
                        <td colspan="5" class="td_style1">
                            <input id="makerdeptname" name="makerdeptname" class="easyui-validatebox"
                                   data-options="width: 450,required:true"
                                   value=""/>
                        </td>
                        <td width="4%">聯繫郵箱<font color="red">*</font></td>
                        <td colspan="3" class="td_style1">
                            <input id="makeremail" name="makeremail" class="easyui-validatebox"
                                   data-options="width: 300,required:true"
                                   value="${user.email}"/>
                        </td>
                    </tr>
                </c:otherwise>
            </c:choose>
            <tr>
                <td colspan="10" class="td_style1">申請人基本信息</td>
            </tr>
            <tr align="center">
                <td width="6%">申請人工號<font color="red">*</font></td>
                <td width="6%" class="td_style1">
                    <input id="applyno" name="applyno" class="easyui-validatebox"
                           data-options="width: 80,required:true"
                           value="${iotCardProcessEntity.applyno}" onblur="queryUserInfo(this);"/>
                </td>
                <td width="4%">申請人</td>
                <td width="6%" class="td_style1">
                    <input id="applyname" name="applyname"
                           class="easyui-validatebox inputCss"
                           data-options="width:80" readonly value="${iotCardProcessEntity.applyname}"/>
                </td>
                <td width="4%">單位代碼</td>
                <td width="6%" class="td_style1">
                    <input id="applydepartno" name="applydepartno"
                           class="easyui-validatebox inputCss" data-options="width: 90"
                           readonly value="${iotCardProcessEntity.applydepartno}"/>
                </td>
                <td width="4%">費用代碼<font color="red">*</font></td>
                <td width="6%" class="td_style1">
                    <input id="applycostno" name="applycostno"
                           class="easyui-validatebox" data-options="width: 90,required:true"
                           value="${iotCardProcessEntity.applycostno}"/>
                </td>
                <td width="4%">所在廠區&nbsp;<font color="red">*</font></td>
                <td width="10%" class="td_style1">
                    <input id="applyFactoryId" name="applyFactoryId" class="easyui-combobox"
                           panelHeight="auto" value="${iotCardProcessEntity.applyFactoryId }"
                           data-options="width: 120,required:true,onSelect:function(){onchangeFactory();},validType:'comboxValidate[\'applyFactoryId\',\'请选择所在廠區\']'"/>
                </td>
            </tr>
            <tr align="center">
                <td width="6%">資位</td>
                <td width="6%" class="td_style1">
                    <input id="applyLevel" name="applyLevel" class="easyui-validatebox"
                           style="width:90px;"
                           value="${iotCardProcessEntity.applyLevel}" data-options="required:true"
                    />
                </td>
                <td width="4%">管理職</td>
                <td width="6%" class="td_style1">
                    <input id="applyPost" name="applyPost" class="easyui-validatebox"
                           style="width:90px;"
                           value="${iotCardProcessEntity.applyPost}" data-options="required:true"
                    />
                </td>
                <td width="4%">聯繫郵箱<font color="red">*</font></td>
                <td colspan="3" class="td_style1">
                    <input id="applyemail" name="applyemail" class="easyui-validatebox"
                           style="width:350px;"
                           value="${iotCardProcessEntity.applyemail}" data-options="required:true"
                    />
                </td>
                <td width="4%">使用區域<font color="red">*</font></td>
                <td width="10%" class="td_style1">
                    <input id="applyArea" name="applyArea" class="easyui-combobox"
                           data-options="width: 90,required:true,onSelect:function(){onchangeArea();},validType:'comboxValidate[\'applyArea\',\'请选择區域\']'"
                           value="${iotCardProcessEntity.applyArea}" panelHeight="auto"/>&nbsp;/
                    <input id="applyFloor" name="applyFloor"
                           class="easyui-combobox"
                           data-options="width: 60,required:true,validType:'comboxValidate[\'applyFloor\',\'请选择樓層\']'"
                           value="${iotCardProcessEntity.applyFloor}" panelHeight="auto"/>
                </td>
            </tr>
            <tr align="center">
                <td width="6%">聯繫分機<font color="red">*</font></td>
                <td width="6%" class="td_style1">
                    <input id="applytel" name="applytel" class="easyui-validatebox"
                           style="width:90px;"
                           value="${iotCardProcessEntity.applytel}" data-options="required:true"
                    />
                </td>
                <td width="4%">單位</td>
                <td colspan="5" class="td_style1">
                    <input id="applydepartname" name="applydepartname" class="easyui-validatebox"
                           style="width:500px;"
                           value="${iotCardProcessEntity.applydepartname}" data-options="required:true"/>
                </td>
                <td width="4%">安保區域<font color="red">*</font></td>
                <td class="td_style2">
                    <div class="isProtectDiv"></div>
                    <input id="isProtect" name="isProtect"
                           type="hidden" class="easyui-validatebox" data-options="width: 100"
                           value="${iotCardProcessEntity.isProtect}"/>
                </td>
            </tr>
            <tr align="center">
                <td>法人代碼<font color="red">*</font></td>
                <td class="td_style1">
                    <input id="applycompanycode" name="applycompanycode"
                           class="easyui-validatebox"
                           data-options="width:80,required:true"
                           value="${iotCardProcessEntity.applycompanycode}"/>
                </td>
                <td>法人名稱<font color="red">*</font></td>
                <td class="td_style1" colspan="7">
                    <input id="applycompanyname" name="applycompanyname"
                           class="easyui-validatebox"
                           data-options="width:300,required:true"
                           value="${iotCardProcessEntity.applycompanyname}"/>
                </td>
            </tr>
            <tr>
                <td colspan="10" class="td_style1">申請詳細信息</td>
            </tr>
            <tr align="center">
                <td>申請數量<font color="red">*</font></td>
                <td class="td_style1" colspan="2">
                    <input id="applyQuantity" name="applyQuantity"
                           class="easyui-validatebox"
                           data-options="width:80,required:true"
                           value="${iotCardProcessEntity.applyQuantity}"/> <span style="color: #0d3349;font-size: 12px">張</span>
                </td>
                <td>申請規格<font color="red">*</font></td>
                <td class="td_style1" colspan="3">
                    <input id="applySpecifications" name="applySpecifications"
                           class="easyui-validatebox"
                           data-options="width:300,required:true"
                           value="${iotCardProcessEntity.applySpecifications}"/>
                </td>
                <td>申請動作<font color="red">*</font></td>
                <td colspan="2">
                    <div class="applyActionDiv"></div>
                    <input id="applyAction" name="applyAction"
                           type="hidden" class="easyui-validatebox" data-options="width: 100"
                           value="${iotCardProcessEntity.applyAction}"/>
                </td>
            </tr>
            <tr>
                <td colspan="10" class="td_style1">使用設備信息</td>
            </tr>
            <tr align="center">
                <td colspan="10" width="100%">
                    <div style="overflow-x: auto;width: 1200px;">
                        <table id="IotCardItemsTable" width="100%">
                            <tr align="center">
                                <td width="50px;">序號</td>
                                <td width="100px;">SN<font color="red">*</font></td>
                                <td width="100px;">MAC<font color="red">*</font></td>
                                <td width="100px;">綁定人員工號<font color="red">*</font></td>
                                <td width="200px;" id="qxlb">綁定人員姓名<font color="red">*</font></td>
                                <td width="200px;">使用地點 *（精確至樓層）<font color="red">*</font></td>
                                <td width="200px;">備註</td>
                                <td width="50px;">&nbsp;操作&nbsp;</td>
                                <tbody id="info_Body">
                                <c:if test="${iotCardProcessEntity.itemsEntity!=null&&iotCardProcessEntity.itemsEntity.size()>0}">
                                <c:forEach items="${iotCardProcessEntity.itemsEntity}"
                                           var="itemsEntity"
                                           varStatus="status">
                            <tr align="center" id="itemsEntity${status.index}">
                                <td>${status.index+1}</td>
                                <td>
                                    <input id="equipmentSn${status.index}" onblur="queryEquipmentInformation(this,'${status.index}');"
                                           name="itemsEntity[${status.index}].equipmentSn"
                                           class="easyui-validatebox" style="width:80px;"
                                           data-options="required:true"
                                           value="${itemsEntity.equipmentSn}"/>
                                </td>
                                <td>
                                    <input id="equipmentMac${status.index}"
                                           name="itemsEntity[${status.index}].equipmentMac"
                                           class="easyui-validatebox" style="width:80px;"
                                           data-options="required:true"
                                           value="${itemsEntity.equipmentMac}"/>
                                </td>
                                <td>
                                    <input id="bondedNumber${status.index}"
                                           name="itemsEntity[${status.index}].bondedNumber"
                                        <%--                                                               data-options="panelHeight:300,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'userfactoryid${status.index}\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(${status.index});},onSelect:function(){pcOnchangeFactory('userfactoryid${status.index}',${status.index});}"--%>
                                           style="width:100px;" class="easyui-validatebox"
                                           data-options="required:true"
                                           value="${itemsEntity.bondedNumber}"/>
                                </td>
                                <td>
                                    <input id="bondedName${status.index}"
                                           name="itemsEntity[${status.index}].bondedName"
                                           class="easyui-validatebox" style="width:150px"
                                           value="${itemsEntity.bondedName}"
                                           data-options="required:true"/>
                                </td>
                                <td>
                                    <input id="location${status.index}"
                                           name="itemsEntity[${status.index}].location"
                                           class="easyui-validatebox" style="width:150px"
                                           value="${itemsEntity.location}"
                                           data-options="required:true"/>
                                </td>
                                <td>
                                    <input id="remark${status.index}"
                                           name="itemsEntity[${status.index}].remark"
                                           class="easyui-validatebox" style="width:150px"
                                           value="${itemsEntity.remark}"/>
                                </td>
                                <td>
                                    <input type="image" src="${ctx}/static/images/deleteRow.png"
                                           onclick="pcdeltr(${status.index});return false;"/>
                                </td>
                            </tr>
                            </c:forEach>
                            </c:if>
                            <c:if test="${iotCardProcessEntity.itemsEntity.size()==0 || iotCardProcessEntity.itemsEntity==null}">
                                <tr align="center" id="itemsEntity0">
                                    <td>1</td>
                                    <td>
                                        <input id="equipmentSn0" onblur="queryEquipmentInformation(this,'0');"
                                               name="itemsEntity[0].equipmentSn"
                                               class="easyui-validatebox" style="width:80px;"
                                               data-options="required:true"
                                               value="${itemsEntity.equipmentSn}"/>
                                    </td>
                                    <td>
                                        <input id="equipmentMac0"
                                               name="itemsEntity[0].equipmentMac"
                                               class="easyui-validatebox" style="width:80px;"
                                               data-options="required:true"
                                               value="${itemsEntity.equipmentMac}"/>
                                    </td>
                                    <td>
                                        <input id="bondedNumber0"
                                               name="itemsEntity[0].bondedNumber"
                                            <%--                                                               data-options="panelHeight:300,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'userfactoryid${status.index}\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(${status.index});},onSelect:function(){pcOnchangeFactory('userfactoryid${status.index}',${status.index});}"--%>
                                               style="width:100px;" class="easyui-validatebox"
                                               data-options="required:true"
                                               value="${itemsEntity.bondedNumber}"/>
                                    </td>
                                    <td>
                                        <input id="bondedName0"
                                               name="itemsEntity[0].bondedName"
                                               class="easyui-validatebox" style="width:150px"
                                               value="${itemsEntity.bondedName}"
                                               data-options="required:true"/>
                                    </td>
                                    <td>
                                        <input id="location0"
                                               name="itemsEntity[0].location"
                                               class="easyui-validatebox" style="width:150px"
                                               value="${itemsEntity.location}"
                                               data-options="required:true"/>
                                    </td>
                                    <td>
                                        <input id="remark0"
                                               name="itemsEntity[0].remark"
                                               class="easyui-validatebox" style="width:150px"
                                               value="${itemsEntity.remark}"/>
                                    </td>
                                    <td>
                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                               onclick="pcdeltr(0);return false;"/>
                                    </td>
                                </tr>
                            </c:if>
                            </tbody>
                            <tr align="left" class="nottr">
                                <td colspan="10" width="100%"
                                    style="text-align:left;padding-left:10px;">
                                    <input type="button" id="pcItemAdd" style="width:100px;"
                                           value="添加一筆"/>&nbsp;
                                    <input type="button" style="width:100px;"
                                           onclick="openBatchImportWin();"
                                           value="批量導入"/>&nbsp;
                                    <a href="${ctx}/admin/download/f336a5d60c074be680ec3e08e63fa5ac"
                                       id="btnBatchImportTpl">模板下載</a>
                                </td>
                            </tr>
                        </table>
                    </div>
                </td>
            </tr>
            <tr align="center">
                <td>需求說明</td>
                <td class="td_style1" colspan="9">
                                <textarea id="requireDetail" name="requireDetail"
                                          oninput="return LessThanAuto(this,'txtNum');"
                                          onchange="return LessThanAuto(this,'txtNum');"
                                          onpropertychange="return LessThanAuto(this,'txtNum');"
                                          maxlength="300" class="easyui-validatebox" style="width:85%;height:80px;"
                                          data-options="required:true"
                                          rows="5" cols="6">${iotCardProcessEntity.requireDetail}</textarea><span
                        id="txtNum"></span>
                </td>
            </tr>
            <tr align="center" style="height: 70px">
                <td>備註/要求</td>
                <td align="left" colspan="9">
                    1、本單適用於申請訪問六流應用特殊管制網段之物聯網卡使用；<br/>
                    2、流量資費為2元1G；<br/>
                    3、物聯網卡費用結算默認至申請人所在單位費用代碼下。
                </td>
            </tr>
            <tr>
                <td align="left" colspan="10" style="color: red">
                    &nbsp;&nbsp;&nbsp;溫馨提示：如果您在填單過程中有任何疑問，請聯繫物聯網卡管理作業人員579+30655
                </td>
            </tr>
            <tr>
                <td colspan="10">
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','物聯網卡申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zxywzrrqrTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    資訊運維責任人確認
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole5(21,'zxywzrrqrno','zxywzrrqrname','zxywzrrzyno','zxywzrrzyname','','',$('#applyFactoryId').combobox('getValue'),$('#applyArea').combobox('getValue'),$('#applyFloor').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxywzrrqrno" name="zxywzrrqrno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxywzrrqrno']}"
                                                               readonly
                                                               value="${iotCardProcessEntity.zxywzrrqrno }"/><c:if
                                                            test="${requiredMap['zxywzrrqrno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxywzrrqrname" name="zxywzrrqrname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxywzrrqrno']}"
                                                                value="${iotCardProcessEntity.zxywzrrqrname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zakchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    資安課級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(153,'zakchargeTable','zakchargeno','zakchargename',$('#makerfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zakchargeno" name="zakchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zakchargeno']}"
                                                               readonly
                                                               value="${iotCardProcessEntity.zakchargeno }"/><c:if
                                                            test="${requiredMap['zakchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zakchargename" name="zakchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zakchargeno']}"
                                                                value="${iotCardProcessEntity.zakchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#makerdeptno').val(),'kchargeno','kchargename',$('#makerfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${iotCardProcessEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${iotCardProcessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#makerdeptno').val(),'bchargeno','bchargename',$('#makerfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${iotCardProcessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${iotCardProcessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#makerdeptno').val(),'cchargeno','cchargename',$('#makerfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${iotCardProcessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${iotCardProcessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    製造處級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#makerdeptno').val(),'zchargeno','zchargename',$('#makerfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${iotCardProcessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${iotCardProcessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    製造總處級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#makerdeptno').val(),'zcchargeno','zcchargename',$('#makerfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${iotCardProcessEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${iotCardProcessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="jgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">經管主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(62,'jgzgTable','jgchargeno','jgchargename',$('#makerfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jgchargeno" name="jgchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['jgchargeno']}"
                                                               readonly
                                                               value="${iotCardProcessEntity.jgchargeno }"/><c:if
                                                            test="${requiredMap['jgchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="jgchargename" name="jgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jgchargeno']}"
                                                                value="${iotCardProcessEntity.jgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zxywkjzgTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    資訊運維課級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'zxywkjzgTable','zxywkjzgno','zxywkjzgname',$('#makerfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxywkjzgno" name="zxywkjzgno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxywkjzgno']}"
                                                               readonly
                                                               value="${iotCardProcessEntity.zxywkjzgno }"/><c:if
                                                            test="${requiredMap['zxywkjzgno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxywkjzgname" name="zxywkjzgname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxywkjzgno']}"
                                                                value="${iotCardProcessEntity.zxywkjzgname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zxywcjzgTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    資訊運維廠區主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(25,'zxywcjzgTable','zxywcjzgno','zxywcjzgname',$('#makerfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxywcjzgno" name="zxywcjzgno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxywcjzgno']}"
                                                               readonly
                                                               value="${iotCardProcessEntity.zxywcjzgno }"/><c:if
                                                            test="${requiredMap['zxywcjzgno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxywcjzgname" name="zxywcjzgname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxywcjzgno']}"
                                                                value="${iotCardProcessEntity.zxywcjzgname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl14Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    資安部級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(154,'yl14Table','ylno14','ylname14',$('#makerfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno14" name="ylno14"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno14']}"
                                                               readonly
                                                               value="${iotCardProcessEntity.ylno14 }"/><c:if
                                                            test="${requiredMap['ylno14'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname14" name="ylname14"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno14']}"
                                                                value="${iotCardProcessEntity.ylname14 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zxywzgTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    資訊運維主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(305,'zxywzgTable','zxywzgno','zxywzgname',$('#makerfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxywzgno" name="zxywzgno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxywzgno']}"
                                                               readonly
                                                               value="${iotCardProcessEntity.zxywzgno }"/><c:if
                                                            test="${requiredMap['zxywzgno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxywzgname" name="zxywzgname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxywzgno']}"
                                                                value="${iotCardProcessEntity.zxywzgname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="glyzyTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">管理員作業
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(650,'glyzyTable','glyzyno','glyzyname',$('#makerfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="glyzyno" name="glyzyno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['glyzyno']}"
                                                               readonly
                                                               value="${iotCardProcessEntity.glyzyno }"/><c:if
                                                            test="${requiredMap['glyzyno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="glyzyname" name="glyzyname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['glyzyno']}"
                                                                value="${iotCardProcessEntity.glyzyname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${iotCardProcessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;"
                                   onclick="canelTask('${iotCardProcessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
    <div id="dlg"></div>
</form>
<div id="optionWin" class="easyui-window" title="物聯網卡申請單信息導入" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%">
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="file" type="file" style="width: 300px"
                           accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>
                    <input id="isExcel2003" name="isExcel2003" type="hidden" value="">

                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <span ID="labelListAddResult"></span><a href="${ctx}/iotcardprocess/errorExcel" id="downloadError"
                                                            plain="true">查看錯誤信息</a>
                </td>
            </tr>
        </table>
    </form>
</div>
</div>
<script src='${ctx}/static/js/information/iotcardprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>