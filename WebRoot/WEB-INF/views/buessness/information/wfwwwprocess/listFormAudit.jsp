<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>WWW上網賬號申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfwwwprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfwwwprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfwwwprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">WWW上網賬號申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfwwwprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfwwwprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfwwwprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfwwwprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfwwwprocessEntity.makerno}/${wfwwwprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">承辦人工號</td>
                            <td width="7%" class="td_style2">${wfwwwprocessEntity.dealno}</td>
                            <td width="4%">承辦人</td>
                            <td width="6%" class="td_style2">${wfwwwprocessEntity.dealname }</td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style2">${wfwwwprocessEntity.dealdeptno }</td>
                            <td width="5%">廠區</td>
                            <td width="5%" class="td_style1">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfwwwprocessEntity.dealfactoryid }" disabled
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'dealfactoryid\',\'请選擇承辦人廠區\']'"/>
                            </td>
                            <td width="3%">聯繫分機</td>
                            <td width="7%" class="td_style2">${wfwwwprocessEntity.dealtel }</td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="5" class="td_style2">${wfwwwprocessEntity.dealdeptname }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfwwwprocessEntity.dealemail }</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">申請人工號</td>
                            <td width="4%" class="td_style2">${wfwwwprocessEntity.applyno}</td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style2">${wfwwwprocessEntity.applyname}</td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style2">${wfwwwprocessEntity.applydeptno }</td>
                            <td width="5%">費用代碼</td>
                            <td width="5%" class="td_style2">${wfwwwprocessEntity.applycostno }</td>
                            <td width="3%">所在廠區</td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" disabled class="easyui-combobox"
                                       panelHeight="auto" value="${wfwwwprocessEntity.applyfactoryid }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applyfactoryid\',\'请選擇申請人廠區\']',onSelect:function(){onchangeFactory('applyfactoryid');}"/>
                                <input id="applynofactoryid" name="applynofactoryid" type="hidden" value="${wfwwwprocessEntity.applynofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style2">${wfwwwprocessEntity.applyleveltype }</td>
                            <td>管理職</td>
                            <td class="td_style2">${wfwwwprocessEntity.applymanager }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfwwwprocessEntity.applyemail }</td>
                            <td>使用區域</td>
                            <td class="td_style1">
                                <input id="applyarea" name="applyarea" class="easyui-combobox" disabled data-options="width: 70,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wfwwwprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="applybuilding" class="easyui-combobox" disabled data-options="width: 70,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wfwwwprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="5%">聯繫分機</td>
                            <td width="5%" class="td_style2">${wfwwwprocessEntity.applyphone }</td>
                            <td>單位</td>
                            <td colspan="5" class="td_style2">${wfwwwprocessEntity.applydeptname }</td>
                            <td>安保區域</td>
                            <td class="td_style2">
                                <div class="securityareaDiv"></div>
                                <input id="securityarea" name="securityarea"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfwwwprocessEntity.securityarea }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="5%">手機號碼</td>
                            <td width="5%" class="td_style2">${wfwwwprocessEntity.applymobile }</td>
                            <td>法人代碼</td>
                            <td class="td_style2">${wfwwwprocessEntity.applycompanycode}</td>
                            <td>法人名稱</td>
                            <td class="td_style2" colspan="5">${wfwwwprocessEntity.applycompanyname}</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">WWW上網帳號申請單</td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">用戶名</td>
                            <td colspan="3" class="td_style2">${wfwwwprocessEntity.wwwip }</td>
                            <td colspan="2">申請類型</td>
                            <td colspan="3" class="td_style2">
                                <div class="wwwtypeDiv"></div>
                                <input id="wwwtype" name="wwwtype" type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfwwwprocessEntity.wwwtype }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">開始日期</td>
                            <td class="td_style1" colspan="3">
                                <input class="Wdate" data-options="width:150" readonly
                                       style="width:150px" value="<fmt:formatDate  pattern="yyyy-MM-dd"
								       value="${wfwwwprocessEntity.wwwstarttime}"/>"/>
                            </td>
                            <td colspan="2">結束日期</td>
                            <td class="td_style1" colspan="3">
                                <input class="Wdate" data-options="width:150" readonly
                                       style="width:150px" value="<fmt:formatDate  pattern="yyyy-MM-dd"
								       value="${wfwwwprocessEntity.wwwendtime}"/>"/>

                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">申請項目</td>
                            <td colspan="8" class="td_style2">
                                <div class="wwwitemDiv"></div>
                                <input id="wwwitem" name="wwwitem" type="hidden" class="easyui-validatebox" data-options="width: 150" value="${wfwwwprocessEntity.wwwitem }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">需求說明</td></td>
                            <td colspan="8" class="td_style1">
                                <textarea id="describtion" name="describtion" class="easyui-validatebox" style="width:900px;height:40px;"
                                          rows="5" cols="6">${wfwwwprocessEntity.describtion}</textarea></td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">附件</td>
                            <td colspan="8" class="td_style2">
                                <input type="hidden" id="attachids" name="attachids" value="${wfwwwprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">批註</td>
                            <td colspan="8" class="td_style2">
						        <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                          style="width:1000px;height:60px;" rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wfwwwprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','WWW上網賬號申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfwwwprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<div id="dlg"></div>
<input type="hidden" id="disOrEnabled" value="disabled"/>
<script src='${ctx}/static/js/information/wfwwwprocess.min.js?random=2025052802'></script>
</body>
</html>