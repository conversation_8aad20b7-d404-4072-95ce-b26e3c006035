<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>WWW上網賬號申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfwwwprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfwwwprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfwwwprocessEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfwwwprocessEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfwwwprocessEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfwwwprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${wfwwwprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">WWW上網賬號申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfwwwprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfwwwprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfwwwprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfwwwprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfwwwprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfwwwprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${wfwwwprocessEntity.makerno}/${wfwwwprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="4%" class="td_style1">
                                <input id="dealno" name="dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="<c:if test="${wfwwwprocessEntity.dealno!=null&&wfwwwprocessEntity.dealno!=''}">${wfwwwprocessEntity.dealno}</c:if>
                                       <c:if test="${wfwwwprocessEntity.dealno==null||wfwwwprocessEntity.dealno==''}">${user.loginName}</c:if>" onchange="queryUserInfo('deal');"/>
                            </td>
                            <td width="4%">承辦人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfwwwprocessEntity.dealname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfwwwprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="5%">廠區&nbsp;</td>
                            <td width="5%" class="td_style1">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfwwwprocessEntity.dealfactoryid }" disabled
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'dealfactoryid\',\'请選擇承辦人廠區\']'"/>
                            </td>
                            <td width="3%">聯繫分機&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="dealtel" name="dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfwwwprocessEntity.dealtel }" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="dealdeptname" name="dealdeptname" class="easyui-validatebox" data-options="width: 410,required:true"
                                       value="${wfwwwprocessEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="dealemail" class="easyui-validatebox"
                                       value="${wfwwwprocessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="4%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfwwwprocessEntity.applyno}" onchange="queryUserInfo('apply');"/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfwwwprocessEntity.applyname}"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfwwwprocessEntity.applydeptno }"/>
                            </td>
                            <td width="5%">費用代碼&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="applycostno" name="applycostno"
                                       class="easyui-validatebox" data-options="width: 90"
                                       value="${wfwwwprocessEntity.applycostno }"/>
                            </td>
                            <td width="3%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfwwwprocessEntity.applyfactoryid }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applyfactoryid\',\'请選擇申請人廠區\']',onSelect:function(){onchangeFactory('applyfactoryid');}"/>
                                <input id="applynofactoryid" name="applynofactoryid" type="hidden" value="${wfwwwprocessEntity.applynofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="applyleveltype" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfwwwprocessEntity.applyleveltype }" readonly/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="applymanager" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfwwwprocessEntity.applymanager }" readonly/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       value="${wfwwwprocessEntity.applyemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td>使用區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyarea" name="applyarea" class="easyui-combobox" data-options="width: 70,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wfwwwprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="applybuilding" class="easyui-combobox" data-options="width: 70,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wfwwwprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="5%">聯繫分機&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="applyphone" name="applyphone" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfwwwprocessEntity.applyphone }" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox" data-options="width: 410,required:true"
                                       value="${wfwwwprocessEntity.applydeptname }"/>
                            </td>
                            <td>安保區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style2">
                                <div class="securityareaDiv"></div>
                                <input id="securityarea" name="securityarea"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfwwwprocessEntity.securityarea }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="5%">手機號碼&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="applymobile" name="applymobile" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfwwwprocessEntity.applymobile }" data-options="required:true,prompt:'13166666666',validType:'phone[\'applymobile\',\'手機號的格式不正確\']'"/>
                            </td>
                            <td>法人代碼</td>
                            <td class="td_style1">
                                <input id="applycompanycode" name="applycompanycode"
                                       class="easyui-validatebox"
                                       data-options="width:80,required:true" value="${wfwwwprocessEntity.applycompanycode}"/>
                            </td>
                            <td>法人名稱</td>
                            <td class="td_style1" colspan="5">
                                <input id="applycompanyname" name="applycompanyname"
                                       class="easyui-validatebox"
                                       data-options="width:300,required:true" value="${wfwwwprocessEntity.applycompanyname}"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">WWW上網帳號申請單</td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">用戶名&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="wwwip" name="wwwip" class="easyui-validatebox" data-options="width: 250,required:true" value="${wfwwwprocessEntity.wwwip }"/>
                            </td>
                            <td colspan="2">申請類型&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style2">
                                <div class="wwwtypeDiv"></div>
                                <input id="wwwtype" name="wwwtype" type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfwwwprocessEntity.wwwtype }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">開始日期&nbsp;<font color="red">*</font></td>
                            <td class="td_style1" colspan="3">
                                <input id="wwwstarttime" name="wwwstarttime" class="Wdate" data-options="width:150,required:true"
                                       style="width:150px"  value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfwwwprocessEntity.wwwstarttime}"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                            </td>
                            <td colspan="2">結束日期&nbsp;<font color="red">*</font></td>
                            <td class="td_style1" colspan="3">
                                <input id="wwwendtime" name="wwwendtime" class="Wdate" data-options="width:150,required:true"
                                       style="width:150px" value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfwwwprocessEntity.wwwendtime}"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'wwwstarttime\')}',maxDate:'#F{$dp.$D(\'wwwstarttime\',{M:6})}'})" />

                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">申請項目&nbsp;<font color="red">*</font></td>
                            <td colspan="8" class="td_style2">
                                <div class="wwwitemDiv"></div>
                                <input id="wwwitem" name="wwwitem" type="hidden" class="easyui-validatebox" data-options="width: 150" value="${wfwwwprocessEntity.wwwitem }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">需求說明&nbsp;<font color="red">*</font></td></td>
                            <td colspan="8" class="td_style1">
                                <textarea id="describtion" name="describtion" oninput="return LessThanAuto(this,'txtNum');"
                                    onchange="return LessThanAuto(this,'txtNum');" onpropertychange="return LessThanAuto(this,'txtNum');"
                                    maxlength="60" class="easyui-validatebox" style="width:99%;height:80px;" data-options="required:true"
                                    rows="5" cols="6">${wfwwwprocessEntity.describtion}</textarea><span id="txtNum"></span></td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">附件&nbsp;<font color="red">*</font></td>
                            <td colspan="8" class="td_style2">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="attachids" value="${wfwwwprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">個人承諾書</td>
                            <td class="td_style2" colspan="8"><a href="${ctx}/wfinternetemailprocess/internetWwwFile">承諾書下載</a>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">備註</td>
                            <td colspan="8" class="td_style2">
                                1.“權限變更”需求作業時帳號和密碼不變，無需重新領取；<br>
                                2.修改密碼請登錄網站http://eimc.efoxconn.com/進行操作；<br>
                                3.如需申請“限客戶及合作廠商網頁瀏覽”【限制瀏覽客戶及合作廠商的網站名單】
                                或“FTP上傳下載（有限制）”【FTP(有限制)客戶及合作廠商網站列表】，煩請確認需訪問的網站是否在可訪問網站列表中
                                <a href="${ctx}/wfwwwprocess/downlimitURL">限制網址名單</a><br>
                                4.此單據須簽核并上傳承諾書，同時要對申請權限之電腦進行資安管控，并簽核<a
                                    href="${ctx}/wfspecialnetprocess/downLoad/InformationSecurityReview">外網資安點檢表</a>；
                            </td>
                            <%--<font color="red">4.賬號新增/變更/續期核准至：IT最高主管，帳號刪除核准至：資訊運維處級主管/級用戶處主管；</font><br>--%>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_wwwswzhshenqing_v4','WWW上網賬號申請單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="5" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl5Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno5_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'yl5Table','ylno5','ylname5',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno5" name="ylno5" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno5']}"
                                                               readonly value="${wfwwwprocessEntity.ylno5 }"/><c:if
                                                            test="${requiredMap['ylno5'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname5" name="ylname5" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno5']}"
                                                                value="${wfwwwprocessEntity.ylname5 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#applydeptno').val(),'cchargeno','cchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly value="${wfwwwprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfwwwprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="czchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['czchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('czchargeTable',$('#applydeptno').val(),'czchargeno','czchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="czchargeno" name="czchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['czchargeno']}"
                                                               readonly value="${wfwwwprocessEntity.czchargeno }"/><c:if
                                                            test="${requiredMap['czchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="czchargename" name="czchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['czchargeno']}"
                                                                value="${wfwwwprocessEntity.czchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole33('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly value="${wfwwwprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="zchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfwwwprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="20%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#applydeptno').val(),'zcchargeno','zcchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly value="${wfwwwprocessEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="zcchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfwwwprocessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno2_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(62,'yl2Table','ylno2','ylname2',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="ylno2" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}"
                                                               readonly value="${wfwwwprocessEntity.ylno2 }"/><c:if
                                                            test="${requiredMap['ylno2'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname2" name="ylname2" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}"
                                                                value="${wfwwwprocessEntity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl6Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno6_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(25,'yl6Table','ylno6','ylname6',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno6" name="ylno6" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno6']}"
                                                               readonly value="${wfwwwprocessEntity.ylno6 }"/><c:if
                                                            test="${requiredMap['ylno6'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname6" name="ylname6" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno6']}"
                                                                value="${wfwwwprocessEntity.ylname6 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl14Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno14_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(154,'yl14Table','ylno14','ylname14',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno14" name="ylno14" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno14']}"
                                                               readonly value="${wfwwwprocessEntity.ylno14 }"/><c:if
                                                            test="${requiredMap['ylno14'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname14" name="ylname14" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno14']}"
                                                                value="${wfwwwprocessEntity.ylname14 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl3Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno3_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(155,'yl3Table','ylno3','ylname3',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno3" name="ylno3" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno3']}"
                                                               readonly value="${wfwwwprocessEntity.ylno3 }"/><c:if
                                                            test="${requiredMap['ylno3'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname3" name="ylname3" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno3']}"
                                                                value="${wfwwwprocessEntity.ylname3 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="20%" style="float: left;margin-left: 5px;"
                                                   id="zacwchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zacwchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(193,'zacwchargeTable','zacwchargeno','zacwchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zacwchargeno"
                                                               name="zacwchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zacwchargeno']}"
                                                               readonly
                                                               value="${wfwwwprocessEntity.zacwchargeno }"/>
                                                        <c:if test="${requiredMap['zacwchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        <%--<span id="zacwchargeredDiv"><font color="red">*</font></span>--%>
                                                        /<input id="zacwchargename"
                                                                name="zacwchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zacwchargeno']}"
                                                                value="${wfwwwprocessEntity.zacwchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <%--<table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#applydeptno').val(),'pcchargeno','pcchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly value="${wfwwwprocessEntity.pcchargeno }"/>
                                                        <c:if test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="pcchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wfwwwprocessEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>--%>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl4Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno4_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(188,'yl4Table','ylno4','ylname4',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno4" name="ylno4" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno4']}"
                                                               readonly value="${wfwwwprocessEntity.ylno4 }"/>
                                                        <c:if test="${requiredMap['ylno4'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        <%--<span id="ylno4redDiv"><font color="red">*</font></span>--%>
                                                        /<input id="ylname4" name="ylname4" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno4']}"
                                                                value="${wfwwwprocessEntity.ylname4 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl7Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno7_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(187,'yl7Table','ylno7','ylname7',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno7" name="ylno7" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno7']}"
                                                               readonly value="${wfwwwprocessEntity.ylno7 }"/>
                                                        <c:if test="${requiredMap['ylno7'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        /<input id="ylname7" name="ylname7" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno7']}"
                                                                value="${wfwwwprocessEntity.ylname7 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="sychargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['sychargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('sychargeTable',$('#applydeptno').val(),'sychargeno','sychargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="sychargeno" name="sychargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['sychargeno']}"
                                                               readonly value="${wfwwwprocessEntity.sychargeno }"/>
                                                        <c:if test="${requiredMap['sychargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        <%-- <span id="sychargeredDiv"><font color="red">*</font></span>--%>
                                                        /<input id="sychargename" name="sychargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['sychargeno']}"
                                                                value="${wfwwwprocessEntity.sychargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10"><font color="red">溫馨提示：如果您在填單過程中有任何疑問，請聯繫本廠區資訊人員：</font><a href="${ctx}/wfvlanprocess/downLoadcontact">各廠區資訊聯繫方式</a></td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <input type="checkbox" id="agree" name="agree"/><a href="${ctx}/requisitionlist/downLoad/commitmentTpl" plain="true" id="btnCommitmentTpl">本人已閱讀并同意服務條款</a>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="areaId" name="areaId" value=""/>
    <input type="hidden" id="buildingId" name="buildingId" value=""/>
    <input type="hidden" id="onlyKchargeSignle" value="1"/>
    <div id="win"></div>
</form>
<script src='${ctx}/static/js/information/wfwwwprocess.min.js?random=2025052802'></script>
</body>
</html>
