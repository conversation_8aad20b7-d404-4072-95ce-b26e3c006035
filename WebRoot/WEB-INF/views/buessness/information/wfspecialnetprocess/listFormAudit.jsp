<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>特殊網絡服務申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<style type="text/css">
    .td_style2 {
        text-align: left;
    }
</style>
</head>
<body>
<form id="mainform" action="${ctx}/wfspecialnetprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfspecialnetprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfspecialnetprocessEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">特殊網絡服務申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfspecialnetprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfspecialnetprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfspecialnetprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfspecialnetprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfspecialnetprocessEntity.makerno}/${wfspecialnetprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
           <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">承辦人工號</td>
                            <td align="left" width="5%"><input id="dealno" name="dealno" class="easyui-validatebox inputCss"
                                                               data-options="width: 80,required:true" readonly
                                                               value="${wfspecialnetprocessEntity.dealno}"/>
                            </td>
                            <td width="4%">承辦人</td>
                            <td align="left" width="6%">${wfspecialnetprocessEntity.dealname }
                            </td>
                            <td width="4%">單位代碼</td>
                            <td align="left" width="6%">${wfspecialnetprocessEntity.dealdeptno }
                            </td>
                            <td width="5%">廠區</td>
                            <td width="5%" class="td_style1">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox" disabled
                                       panelHeight="auto" value="${wfspecialnetprocessEntity.dealfactoryid }" disabled
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'dealfactoryid\',\'请選擇承辦人廠區\']'"/>
                            </td>
                            <td width="3%">聯繫分機</td>
                            <td align="left" width="7%">${wfspecialnetprocessEntity.dealtel}
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td align="left" colspan="5">${wfspecialnetprocessEntity.dealdeptname }
                            </td>
                            <td>聯繫郵箱</td>
                            <td align="left" colspan="3">${wfspecialnetprocessEntity.dealemail }
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td>申請人工號</td>
                            <td align="left">${wfspecialnetprocessEntity.applyno}
                            </td>
                            <td>申請人</td>
                            <td align="left">${wfspecialnetprocessEntity.applyname}
                            </td>
                            <td>單位代碼</td>
                            <td align="left">${wfspecialnetprocessEntity.applydeptno }
                            </td>
                            <td>費用代碼</td>
                            <td align="left">${wfspecialnetprocessEntity.applycostno }
                            </td>
                            <td>所在廠區</td>
                            <td class="td_style1">
                                <input id="applychoosefactoryid" name="applychoosefactoryid" class="easyui-combobox" disabled
                                       panelHeight="auto" value="${wfspecialnetprocessEntity.applychoosefactoryid }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applychoosefactoryid\',\'请選擇申請人廠區\']',onSelect:function(){onchangeFactory('applychoosefactoryid');}"/>
                                <input id="applyfactoryid" name="applyfactoryid" type="hidden"
                                       value="${wfspecialnetprocessEntity.applyfactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td align="left">${wfspecialnetprocessEntity.applyleveltype }</td>
                            <td>管理職</td>
                            <td align="left">${wfspecialnetprocessEntity.applymanager }</td>
                            <td>聯繫郵箱</td>
                            <td align="left" colspan="3">${wfspecialnetprocessEntity.applyemail }</td>
                            <td>使用區域</td>
                            <td class="td_style1">
                                <input id="applyarea" name="applyarea" class="easyui-combobox" disabled data-options="width: 70,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wfspecialnetprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="applybuilding" class="easyui-combobox" disabled data-options="width: 70,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wfspecialnetprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機</td>
                            <td align="left">${wfspecialnetprocessEntity.applyphone}</td>
                            <td>單位</td>
                            <td align="left" colspan="5">${wfspecialnetprocessEntity.applydeptname }</td>
                            <td>安保區域</td>
                            <td class="td_style2">
                                <div class="securityareaDiv"></div>
                                <input id="securityarea" name="securityarea"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfspecialnetprocessEntity.securityarea }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>法人代碼</td>
                            <td align="left">${wfspecialnetprocessEntity.applycompanycode}</td>
                            <td>法人名稱</td>
                            <td align="left" colspan="7">${wfspecialnetprocessEntity.applycompanyname}</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">特殊網絡服務需求申請單</td>
                        </tr>
                        <tr align="center">
                            <td>業務類型</td>
                            <td class="td_style2" colspan="4">
                                <div class="businesstypeDiv"></div>
                                <input id="businesstype" name="businesstype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfspecialnetprocessEntity.businesstype }"/>
                            </td>
                            <td>申請動作</td>
                            <td class="td_style2" colspan="4">
                                <div class="applyactionDiv"></div>
                                <input id="applyaction" name="applyaction"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfspecialnetprocessEntity.applyaction }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>開始日期</td>
                            <td class="td_style1" colspan="4">
                                <input class="Wdate" data-options="width:150" readonly
                                       style="width:100px" value="<fmt:formatDate  pattern="yyyy-MM-dd"
								       value="${wfspecialnetprocessEntity.specialnetstartdate}"/>"/>
                            </td>
                            <td>結束日期</td>
                            <td class="td_style1" colspan="4">
                                <input class="Wdate" data-options="width:150,required:true" readonly
                                       style="width:100px" value="<fmt:formatDate  pattern="yyyy-MM-dd"
								        value="${wfspecialnetprocessEntity.specialnetenddate}"/>"/>

                            </td>
                        </tr>
                        <tr align="center">
                            <td>需求說明</td>
                            <td align="left" colspan="9"><textarea
                                    id="describtion" name="describtion" readonly
                                    class="easyui-validatebox" style="width:900px;height:40px;"
                                    rows="5" cols="6">${wfspecialnetprocessEntity.describtion}</textarea>
                            </td>
                        </tr>
                    </table>
                </td>
           </tr>
            <tr>
                <td>
                     <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${wfspecialnetprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%">
						<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wfspecialnetprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','特殊網絡服務申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfspecialnetprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                     </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<div id="dlg"></div>

<script src='${ctx}/static/js/information/wfspecialnetprocess.min.js?random=2025052601'></script>
</body>
</html>