<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>特殊網絡服務申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<style type="text/css">
    .td_style2 {
        text-align: left;
    }
</style>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfspecialnetprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfspecialnetprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfspecialnetprocessEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfspecialnetprocessEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfspecialnetprocessEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfspecialnetprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${wfspecialnetprocessEntity.makerfactoryid }"/>
    <div class="commonW">
    <div class="headTitle">特殊網絡需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfspecialnetprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfspecialnetprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfspecialnetprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfspecialnetprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfspecialnetprocessEntity.makerno}">
			  <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfspecialnetprocessEntity.makerno}">
                <div class="position_R margin_R">填單人：${wfspecialnetprocessEntity.makerno}/${wfspecialnetprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
		<table class="formList">
		   <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="dealno" name="dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfspecialnetprocessEntity.dealno}" onblur="queryUserInfo('deal');"/>
                            </td>
                            <td width="3%">承辦人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfspecialnetprocessEntity.dealname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfspecialnetprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="5%">廠區</td>
                            <td width="5%" class="td_style1">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfspecialnetprocessEntity.dealfactoryid }" disabled
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'dealfactoryid\',\'请選擇承辦人廠區\']'"/>
                            </td>
                            <td width="4%">聯繫分機&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="dealtel" name="dealtel" class="easyui-validatebox"
                                       style="width:90px;position:relative;"
                                       value="${wfspecialnetprocessEntity.dealtel }" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="dealdeptname" name="dealdeptname" class="easyui-validatebox" data-options="width: 410,required:true,validType:['maxLength[65]']" invalidMessage="超出富鴻網能接收長度，請修改"
                                       value="${wfspecialnetprocessEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="dealemail" class="easyui-validatebox"
                                       value="${wfspecialnetprocessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td>申請人工號&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfspecialnetprocessEntity.applyno}" onblur="queryUserInfo('apply');"/>
                            </td>
                            <td>申請人</td>
                            <td class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfspecialnetprocessEntity.applyname}"/>
                            </td>
                            <td>單位代碼</td>
                            <td class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfspecialnetprocessEntity.applydeptno }"/>
                            </td>
                            <td>費用代碼&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applycostno" name="applycostno"
                                       class="easyui-validatebox" data-options="width: 90,required:true"
                                       value="${wfspecialnetprocessEntity.applycostno }"/>
                            </td>
                            <td>所在廠區&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applychoosefactoryid" name="applychoosefactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfspecialnetprocessEntity.applychoosefactoryid }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applychoosefactoryid\',\'请選擇申請人廠區\']',onSelect:function(){onchangeFactory('applychoosefactoryid');}"/>
                                <input id="applyfactoryid" name="applyfactoryid" type="hidden"
                                       value="${wfspecialnetprocessEntity.applyfactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="applyleveltype" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfspecialnetprocessEntity.applyleveltype }" readonly/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="applymanager" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfspecialnetprocessEntity.applymanager }" readonly/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       value="${wfspecialnetprocessEntity.applyemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td>使用區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyarea" name="applyarea" class="easyui-combobox" data-options="width: 70,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wfspecialnetprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="applybuilding" class="easyui-combobox" data-options="width: 70,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wfspecialnetprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyphone" name="applyphone" class="easyui-validatebox"
                                       style="width:90px;position:relative;"
                                       value="${wfspecialnetprocessEntity.applyphone }" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox" data-options="width: 410,required:true"
                                       value="${wfspecialnetprocessEntity.applydeptname }"/>
                            </td>
                            <td>安保區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style2">
                                <div class="securityareaDiv"></div>
                                <input id="securityarea" name="securityarea"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfspecialnetprocessEntity.securityarea }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>法人代碼</td>
                            <td class="td_style1">
                                <input id="applycompanycode" name="applycompanycode"
                                       class="easyui-validatebox"
                                       data-options="width:80,required:true" value="${wfspecialnetprocessEntity.applycompanycode}"/>
                            </td>
                            <td>法人名稱</td>
                            <td class="td_style1" colspan="7">
                                <input id="applycompanyname" name="applycompanyname"
                                       class="easyui-validatebox"
                                       data-options="width:300,required:true" value="${wfspecialnetprocessEntity.applycompanyname}"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">特殊網絡服務需求申請單</td>
                        </tr>
                        <tr align="center">
                            <td>業務類型&nbsp;<font color="red">*</font></td>
                            <td class="td_style2" colspan="4">
                                <div class="businesstypeDiv"></div>
                                <input id="businesstype" name="businesstype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfspecialnetprocessEntity.businesstype }"/>
                            </td>
                            <td>申請動作&nbsp;<font color="red">*</font></td>
                            <td class="td_style2" colspan="4">
                                <div class="applyactionDiv"></div>
                                <input id="applyaction" name="applyaction"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfspecialnetprocessEntity.applyaction }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>開始日期&nbsp;<font color="red">*</font></td>
                            <td class="td_style1" colspan="4"><input id="specialnetstartdate" name="specialnetstartdate"
                                       class="Wdate" data-options="width:150,required:true"
                                       style="width:150px"
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd"
								       value="${wfspecialnetprocessEntity.specialnetstartdate}"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                            </td>
                            <td>結束日期&nbsp;<font color="red">*</font></td>
                            <td class="td_style1" colspan="4"><input id="specialnetenddate" name="specialnetenddate"
                                        class="Wdate" data-options="width:150,required:true"
                                        style="width:150px"
                                        value="<fmt:formatDate  pattern="yyyy-MM-dd"
								        value="${wfspecialnetprocessEntity.specialnetenddate}"/>"
                                        onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'specialnetstartdate\')}',maxDate:'#F{$dp.$D(\'specialnetstartdate\',{M:6})}'})" />

                            </td>
                        </tr>
                        <tr align="center">
                            <td>需求說明&nbsp;<font color="red">*</font></td>
                            <td align="left" colspan="9"><textarea
                                    id="describtion"
                                    name="describtion" data-options="required:true"
                                    oninput="return LessThanAuto(this,'txtNum');"
                                    onchange="return LessThanAuto(this,'txtNum');"
                                    onpropertychange="return LessThanAuto(this,'txtNum');"
                                    maxlength="300"
                                    class="easyui-validatebox" style="width:900px;height:40px;"
                                    rows="5" cols="6">${wfspecialnetprocessEntity.describtion}</textarea><span
                                    id="txtNum"></span></td>
                        </tr>
                        <tr align="center">
                            <td>備註/要求</td>
                            <td align="left" colspan="9">
                                1.本單適用于訪問集團特殊管制網段及集團外部網站使用；<br/>
                                2.申請訪問集團內部特殊管制網段,請選擇特殊網絡訪問權限; <br/>
                                3.申請訪問集團外部網站,請選擇固定IP上網，最長使用期限六個月，到期后請填單續期; <br/>
                                4.申請固定IP上網，須簽核并上傳承諾書，同時要對申請權限之電腦進行資安管控，并簽核<a href="${ctx}/wfspecialnetprocess/downLoad/InformationSecurityReview" >外網資安點檢表</a>；<br/>
                                5.本單據申請須上傳<a href="${ctx}/wfspecialnetprocess/downLoad/NetworkReq">網絡訪問需求調查表;</a><br/>
                                <font color="red">6.特殊網絡訪問權限需核准至部分機能總處/部分製造總處級權限主管，固定IP上網需核准至產品處級/機能總處級/製造總處級權限主管；<br/></font>
                                7.申請動作選擇“續期”時不拋轉112系統不會作業，僅作為於資安要求，新申請權限時申請動作選擇“新增”;<br/>
                                8.申請特殊網絡訪問權限時，系統規劃主管為必選項，網絡管理員為必選項；申請固定IP上網時，資訊運維課級主管為必選項、經管主管為必選項。
                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button" value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span> <input type="hidden" id="attachids"
                                               name="attachids" value="${wfspecialnetprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>個人承諾書</td>
                            <td align="left" colspan="9">
                                <%--特殊網絡服務需求申請單承諾書<a href="${ctx}/wfspecialnetprocess/downLoad/SpecialNetCommitment">承諾書下載</a>--%>
                                固定IP上網申請單承諾書<a href="${ctx}/wfspecialnetprocess/downLoad/IPInternetCommitment">承諾書下載</a>
                            </td>
                        </tr>
                    </table>
                </td>
           </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_teshuwangluoshenqing_v6','特殊網絡服務申請單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="5" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="20%" style="float: left;margin-left: 5px;"
                                                   id="wlglychargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['wlglychargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         id="wlglychargenoDiv_"
                                                                         onclick="selectRole2(667,'wlglychargeTable','wlglychargeno','wlglychargename',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="wlglychargeno" name="wlglychargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['wlglychargeno']}"
                                                               readonly
                                                               value="${wfspecialnetprocessEntity.wlglychargeno }"/><c:if
                                                            test="${requiredMap['wlglychargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="wlglychargename" name="wlglychargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['wlglychargeno']}"
                                                                value="${wfspecialnetprocessEntity.wlglychargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="19%" style="float: left;margin-left: 5px;" id="yl5Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno5_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon" id="ylno5Div_"
                                                                         onclick="selectRole2(22,'yl5Table','ylno5','ylname5',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno5" name="ylno5"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno5']}"
                                                               readonly
                                                               value="${wfspecialnetprocessEntity.ylno5 }"/><c:if
                                                            test="${requiredMap['ylno5'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname5" name="ylname5"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno5']}"
                                                                value="${wfspecialnetprocessEntity.ylname5 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="19%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#applydeptno').val(),'cchargeno','cchargename',$('#applyfactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfspecialnetprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfspecialnetprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="19%" style="float: left;margin-left: 5px;" id="czchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['czchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('czchargeTable',$('#applydeptno').val(),'czchargeno','czchargename',$('#applyfactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="czchargeno" name="czchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['czchargeno']}"
                                                               readonly
                                                               value="${wfspecialnetprocessEntity.czchargeno }"/><c:if
                                                            test="${requiredMap['czchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="czchargename" name="czchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['czchargeno']}"
                                                                value="${wfspecialnetprocessEntity.czchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="19%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole33('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applyfactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfspecialnetprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfspecialnetprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="20%" style="float: left;margin-left: 5px;"
                                                   id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
                                                                    <td style="border: none;">
                                                                        <div class="float_L qhUserIcon"
                                                                             onclick="selectRole3('zcchargeTable',$('#applydeptno').val(),'zcchargeno','zcchargename',$('#applyfactoryid').val(),null)"></div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><input id="zcchargeno" name="zcchargeno"
                                                                   class="easyui-validatebox"
                                                                   data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                   readonly
                                                                   value="${wfspecialnetprocessEntity.zcchargeno }"/><c:if
                                                                test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                            /<input id="zcchargename" name="zcchargename"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                    value="${wfspecialnetprocessEntity.zcchargename }"/>
                                                        </td>
                                                    </tr>
                                            </table>
                                            <table width="19%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno2_name']}</td>
                                                                    <td style="border: none;">
                                                                        <div class="float_L qhUserIcon"
                                                                             onclick="selectRole2(62,'yl2Table','ylno2','ylname2',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><input id="ylno2" name="ylno2"
                                                                   class="easyui-validatebox"
                                                                   data-options="width:80,required:${requiredMap['ylno2']}"
                                                                   readonly
                                                                   value="${wfspecialnetprocessEntity.ylno2 }"/><c:if
                                                                test="${requiredMap['ylno2'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                            /<input id="ylname2" name="ylname2"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['ylno2']}"
                                                                    value="${wfspecialnetprocessEntity.ylname2 }"/>
                                                        </td>
                                                    </tr>
                                            </table>
                                            <table width="19%" style="float: left;margin-left: 5px;" id="yl6Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno6_name']}</td>
                                                                    <td style="border: none;">
                                                                        <div class="float_L qhUserIcon"
                                                                             onclick="selectRole2(25,'yl6Table','ylno6','ylname6',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><input id="ylno6" name="ylno6"
                                                                   class="easyui-validatebox"
                                                                   data-options="width:80,required:${requiredMap['ylno6']}"
                                                                   readonly
                                                                   value="${wfspecialnetprocessEntity.ylno6 }"/><c:if
                                                                test="${requiredMap['ylno6'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                            /<input id="ylname6" name="ylname6"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['ylno6']}"
                                                                    value="${wfspecialnetprocessEntity.ylname6 }"/>
                                                        </td>
                                                    </tr>
                                            </table>
                                            <table width="19%" style="float: left;margin-left: 5px;" id="yl14Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno14_name']}</td>
                                                                    <td style="border: none;">
                                                                        <div class="float_L qhUserIcon"
                                                                             onclick="selectRole2(154,'yl14Table','ylno14','ylname14',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><input id="ylno14" name="ylno14"
                                                                   class="easyui-validatebox"
                                                                   data-options="width:80,required:${requiredMap['ylno14']}"
                                                                   readonly
                                                                   value="${wfspecialnetprocessEntity.ylno14 }"/><c:if
                                                                test="${requiredMap['ylno14'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                            /<input id="ylname14" name="ylname14"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['ylno14']}"
                                                                    value="${wfspecialnetprocessEntity.ylname14 }"/>
                                                        </td>
                                                    </tr>
                                            </table>
                                            <table width="19%" style="float: left;margin-left: 5px;"
                                                   id="xtghzgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['xtghzgchargeno_name']}</td>
                                                                    <td style="border: none;">
                                                                        <div class="float_L qhUserIcon"
                                                                             onclick="selectRole2(666,'xtghzgchargeTable','xtghzgchargeno','xtghzgchargename',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><input id="xtghzgchargeno" name="xtghzgchargeno"
                                                                   class="easyui-validatebox"
                                                                   data-options="width:80,required:${requiredMap['xtghzgchargeno']}"
                                                                   readonly
                                                                   value="${wfspecialnetprocessEntity.xtghzgchargeno }"/><c:if
                                                                test="${requiredMap['xtghzgchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                            /<input id="xtghzgchargename" name="xtghzgchargename"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['xtghzgchargeno']}"
                                                                    value="${wfspecialnetprocessEntity.xtghzgchargename }"/>
                                                        </td>
                                                    </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="20%" style="float: left;margin-left: 5px;" id="yl3Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno3_name']}</td>
                                                                    <td style="border: none;">
                                                                        <div class="float_L qhUserIcon"
                                                                             onclick="selectRole2(155,'yl3Table','ylno3','ylname3',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><input id="ylno3" name="ylno3"
                                                                   class="easyui-validatebox"
                                                                   data-options="width:80,required:${requiredMap['ylno3']}"
                                                                   readonly
                                                                   value="${wfspecialnetprocessEntity.ylno3 }"/><c:if
                                                                test="${requiredMap['ylno3'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                            /<input id="ylname3" name="ylname3"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['ylno3']}"
                                                                    value="${wfspecialnetprocessEntity.ylname3 }"/>
                                                        </td>
                                                    </tr>
                                            </table>
                                            <table width="19%" style="float: left;margin-left: 5px;"
                                                   id="zacwchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zacwchargeno_name']}</td>
                                                                    <td style="border: none;">
                                                                        <div class="float_L qhUserIcon"
                                                                             onclick="selectRole2(193,'zacwchargeTable','zacwchargeno','zacwchargename',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><input id="zacwchargeno" name="zacwchargeno"
                                                                   class="easyui-validatebox"
                                                                   data-options="width:80,required:${requiredMap['zacwchargeno']}"
                                                                   readonly
                                                                   value="${wfspecialnetprocessEntity.zacwchargeno }"/><c:if
                                                                test="${requiredMap['zacwchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                            /<input id="zacwchargename" name="zacwchargename"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['zacwchargeno']}"
                                                                    value="${wfspecialnetprocessEntity.zacwchargename }"/>
                                                        </td>
                                                    </tr>
                                            </table>
                                            <table width="19%" style="float: left;margin-left: 5px;" id="yl4Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno4_name']}</td>
                                                                    <td style="border: none;">
                                                                        <div class="float_L qhUserIcon"
                                                                             onclick="selectRole2(188,'yl4Table','ylno4','ylname4',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><input id="ylno4" name="ylno4"
                                                                   class="easyui-validatebox"
                                                                   data-options="width:80,required:${requiredMap['ylno4']}"
                                                                   readonly
                                                                   value="${wfspecialnetprocessEntity.ylno4 }"/><c:if
                                                                test="${requiredMap['ylno4'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                            /<input id="ylname4" name="ylname4"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['ylno4']}"
                                                                    value="${wfspecialnetprocessEntity.ylname4 }"/>
                                                        </td>
                                                    </tr>
                                            </table>
                                            <table width="19%" style="float: left;margin-left: 5px;" id="yl7Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno7_name']}</td>
                                                                    <td style="border: none;">
                                                                        <div class="float_L qhUserIcon"
                                                                             onclick="selectRole2(187,'yl7Table','ylno7','ylname7',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><input id="ylno7" name="ylno7"
                                                                   class="easyui-validatebox"
                                                                   data-options="width:80,required:${requiredMap['ylno7']}"
                                                                   readonly
                                                                   value="${wfspecialnetprocessEntity.ylno7 }"/><c:if
                                                                test="${requiredMap['ylno7'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                            /<input id="ylname7" name="ylname7"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['ylno7']}"
                                                                    value="${wfspecialnetprocessEntity.ylname7 }"/>
                                                        </td>
                                                    </tr>
                                            </table>
                                            <table width="19%" style="float: left;margin-left: 5px;"
                                                   id="sychargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['sychargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('sychargeTable',$('#applydeptno').val(),'sychargeno','sychargename',$('#applyfactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="sychargeno" name="sychargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['sychargeno']}"
                                                               readonly
                                                               value="${wfspecialnetprocessEntity.sychargeno }"/><c:if
                                                            test="${requiredMap['sychargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="sychargename" name="sychargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['sychargeno']}"
                                                                value="${wfspecialnetprocessEntity.sychargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10"><font color="red">溫馨提示：如果您在填單過程中有任何疑問，請聯繫本廠區資訊人員：</font><a href="${ctx}/wfvlanprocess/downLoadcontact">各廠區資訊聯繫方式</a></td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <input type="checkbox" id="agree" name="agree"/><a href="${ctx}/requisitionlist/downLoad/commitmentTpl" plain="true" id="btnCommitmentTpl">本人已閱讀并同意服務條款</a>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton" data-options="iconCls:'icon-add'" style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'" style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
           </tr>
        </table>
	</div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="areaId" name="areaId" value=""/>
    <input type="hidden" id="buildingId" name="buildingId" value=""/>
    <input type="hidden" id="loginname" name="loginname" value="${user.loginName}"/>
    <input type="hidden" id="onlyKchargeSignle" value="1"/>
    <div id="win"></div>
    <input id="disOrEnabled" type="hidden" value=""/>
</form>
</div>
<script src='${ctx}/static/js/information/wfspecialnetprocess.min.js?random=2025052601'></script>
</body>
</html>
