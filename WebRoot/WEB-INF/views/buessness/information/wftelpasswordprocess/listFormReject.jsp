<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>電話密碼申請</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
        .td_style3{
            border: none 0px;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wftelpasswordprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wftelpasswordprocessEntity.id }"/>
    <input id="serialno" name="wftelpasswordprocess.serialno" type="hidden" value="${wftelpasswordprocessEntity.serialno }"/>
    <input id="makerno" name="wftelpasswordprocess.makerno" type="hidden" value="${wftelpasswordprocessEntity.makerno }"/>
    <input id="makername" name="wftelpasswordprocess.makername" type="hidden" value="${wftelpasswordprocessEntity.makername }"/>
    <input id="makerdeptno" name="wftelpasswordprocess.makerdeptno" type="hidden" value="${wftelpasswordprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wftelpasswordprocess.makerfactoryid" type="hidden" value="${wftelpasswordprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">電話密碼申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wftelpasswordprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wftelpasswordprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wftelpasswordprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wftelpasswordprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wftelpasswordprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wftelpasswordprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wftelpasswordprocessEntity.makerno}/${wftelpasswordprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList" >
            <tr>
                <td>
                    <table class="formList" id="validForm1">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="dealno" name="wftelpasswordprocess.dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="<c:if test="${wftelpasswordprocessEntity.dealno!=null || wftelpasswordprocessEntity.dealno!=''}">${wftelpasswordprocessEntity.dealno}</c:if><c:if test="${wftelpasswordprocessEntity.dealno==null||wftelpasswordprocessEntity.dealno==''}">${user.loginName}</c:if>" onchange="queryUserInfo('deal');"/>
                            </td>
                            <td width="8%">承辦人</td>
                            <td width="10%" class="td_style1">
                                <input id="dealname" name="wftelpasswordprocess.dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wftelpasswordprocessEntity.dealname }"/>
                            </td>
                            <td width="10%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="dealdeptno" name="wftelpasswordprocess.dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wftelpasswordprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="10%">廠區</td>
                            <td width="10%" class="td_style1">
                                <input id="dealfactoryid" name="wftelpasswordprocess.dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wftelpasswordprocessEntity.dealfactoryid }" disabled
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'dealfactoryid\',\'请選擇承辦人廠區\']'"/>
                            </td>
                            <td width="8%">聯繫分機&nbsp;<font color="red">*</font></td>
                            <td width="16%" class="td_style1">
                                <input id="dealtel" name="wftelpasswordprocess.dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wftelpasswordprocessEntity.dealtel }" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="dealdeptname" name="wftelpasswordprocess.dealdeptname" class="easyui-validatebox" data-options="width: 410,required:true"
                                       value="${wftelpasswordprocessEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="wftelpasswordprocess.dealemail" class="easyui-validatebox"
                                       value="${wftelpasswordprocessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td>申請人工號&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyno" name="wftelpasswordprocess.applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wftelpasswordprocessEntity.applyno}" onchange="queryUserInfo('apply');"/>
                            </td>
                            <td>申請人</td>
                            <td class="td_style1">
                                <input id="applyname" name="wftelpasswordprocess.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wftelpasswordprocessEntity.applyname}"/>
                            </td>
                            <td>單位代碼</td>
                            <td class="td_style1">
                                <input id="applydeptno" name="wftelpasswordprocess.applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wftelpasswordprocessEntity.applydeptno }"/>
                            </td>
                            <td>費用代碼&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applycostno" name="wftelpasswordprocess.applycostno"
                                       class="easyui-validatebox" data-options="width: 90"
                                       value="${wftelpasswordprocessEntity.applycostno }"/>
                            </td>
                            <td>所在廠區&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyfactoryid" name="wftelpasswordprocess.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wftelpasswordprocessEntity.applyfactoryid }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applyfactoryid\',\'请選擇申請人廠區\']',onSelect:function(){onchangeFactory('applyfactoryid');}"/>
                                <input id="applynofactoryid" name="wftelpasswordprocess.applynofactoryid" type="hidden" value="${wftelpasswordprocessEntity.applynofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="wftelpasswordprocess.applyleveltype" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wftelpasswordprocessEntity.applyleveltype }" readonly/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="wftelpasswordprocess.applymanager" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wftelpasswordprocessEntity.applymanager }" readonly/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="wftelpasswordprocess.applyemail" class="easyui-validatebox"
                                       value="${wftelpasswordprocessEntity.applyemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td>使用區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyarea" name="wftelpasswordprocess.applyarea" class="easyui-combobox" data-options="width: 70,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wftelpasswordprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="wftelpasswordprocess.applybuilding" class="easyui-combobox" data-options="width: 70,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wftelpasswordprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyphone" name="wftelpasswordprocess.applyphone" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wftelpasswordprocessEntity.applyphone }" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="wftelpasswordprocess.applydeptname" class="easyui-validatebox" data-options="width: 410,required:true"
                                       value="${wftelpasswordprocessEntity.applydeptname }"/>
                            </td>
                            <td>安保區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style2">
                                <div class="securityareaDiv"></div>
                                <input id="securityarea" name="wftelpasswordprocess.securityarea"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wftelpasswordprocessEntity.securityarea }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>法人代碼&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applycompanycode" name="wftelpasswordprocess.applycompanycode"
                                       class="easyui-validatebox"
                                       data-options="width:80,required:true" value="${wftelpasswordprocessEntity.applycompanycode}"/>
                            </td>
                            <td>法人名稱&nbsp;<font color="red">*</font></td>
                            <td class="td_style1" colspan="7">
                                <input id="applycompanyname" name="wftelpasswordprocess.applycompanyname"
                                       class="easyui-validatebox"
                                       data-options="width:300,required:true" value="${wftelpasswordprocessEntity.applycompanyname}"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <table class="formList" id="validForm2">
                    <tr>
                        <td colspan="6" class="td_style1">電話密碼申請</td>
                    </tr>
                    <tr align="center">
                        <td width="10%">申請原因&nbsp;<font color="red">*</font></td>
                        <td colspan="5" class="td_style2">
                            <div class="applytypeDiv"></div>
                            <input id="applytype" name="wftelpasswordprocess.applytype"
                                   type="hidden" class="easyui-validatebox" data-options="width: 150"
                                   value="${wftelpasswordprocessEntity.applytype }"/>
                        </td>
                    </tr>
                    <tr align="center">
                        <td>區域&nbsp;<font color="red">*</font></td>
                        <td class="td_style2">
                            <div class="regiontypeDiv"></div>
                            <input id="regiontype" name="wftelpasswordprocess.regiontype"
                                   type="hidden" class="easyui-validatebox" data-options="width: 100"
                                   value="${wftelpasswordprocessEntity.regiontype }"/>
                        </td>
                        <td>使用區域</td>
                        <td class="td_style2">
                            <input id="useregion" name="wftelpasswordprocess.useregion" class="easyui-combobox"
                                   panelHeight="auto" value="${wftelpasswordprocessEntity.useregion }"
                                   data-options="width: 120,required:true,validType:'comboxValidate[\'useregion\',\'请選擇使用區域\']'"/>
                        </td>
                        <td>區域分機字頭</td>
                        <td class="td_style2">
                            <input id="regiontelbegin" name="wftelpasswordprocess.regiontelbegin" class="easyui-validatebox" data-options="width: 150"
                                   value="${wftelpasswordprocessEntity.regiontelbegin }"/>
                        </td>
                    </tr>
                    <tr align="center">
                        <td>使用限額&nbsp;<font color="red">*</font></td>
                        <td class="td_style2">
                            <input id="usenorm" name="wftelpasswordprocess.usenorm" class="easyui-validatebox" onblur="isNum(this)" data-options="width: 150,required:true"
                                   value="${wftelpasswordprocessEntity.usenorm }"/>
                        </td>
                        <td>每日使用分機數&nbsp;<font color="red">*</font></td>
                        <td class="td_style2" align="center">
                            <input id="usetelnum" name="wftelpasswordprocess.usetelnum" class="easyui-validatebox" onblur="isNum(this)" data-options="width: 150,required:true"
                                   value="<c:if test="${wftelpasswordprocessEntity.usetelnum!=null ||wftelpasswordprocessEntity.usetelnum!=''}">${wftelpasswordprocessEntity.usetelnum}</c:if><c:if test="${wftelpasswordprocessEntity.usetelnum==null ||wftelpasswordprocessEntity.usetelnum==''}">6</c:if>" />
                        </td>
                        <td>主要撥打城市&nbsp;<font color="red">*</font></td>
                        <td class="td_style2">
                            <input id="mainpullter" name="wftelpasswordprocess.mainpullter" class="easyui-validatebox" data-options="width: 150,required:true,prompt:'多城市以逗號“,”隔開'"
                                   value="${wftelpasswordprocessEntity.mainpullter }"/>
                        </td>
                    </tr>
                    <tr align="center">
                        <td>密碼領取方式&nbsp;<font color="red">*</font></td>
                        <td colspan="2" class="td_style2">
                            <div class="pwdgetDiv"></div>
                            <input id="pwdget" name="wftelpasswordprocess.pwdget"
                                   type="hidden" class="easyui-validatebox" data-options="width: 100"
                                   value="${wftelpasswordprocessEntity.pwdget }"/>
                        </td>
                        <td>手機號碼</td>
                        <td colspan="2" class="td_style2">
                            <input id="phone" name="wftelpasswordprocess.phone" class="easyui-validatebox"
                                   style="width:150px;"
                                   value="${wftelpasswordprocessEntity.phone }" data-options="prompt:'13166666666',validType:'phone[\'phone\',\'手機號的格式不正確\']'"/>
                        </td>
                    </tr>
                    <tr align="center">
                        <td width="10%">預期級別&nbsp;<font color="red">*</font></td>
                        <td colspan="5" class="td_style2">
                            <div class="expecttypeDiv"></div>
                            <input id="expecttype" name="wftelpasswordprocess.expecttype"
                                   type="hidden" class="easyui-validatebox" data-options="width: 100"
                                   value="${wftelpasswordprocessEntity.expecttype }"/>
                        </td>
                    </tr>
                    <tr align="center">
                        <td width="10%">需求說明&nbsp;<font color="red">*</font></td>
                        <td colspan="5" class="td_style2">
                             <textarea id="describtion" name="wftelpasswordprocess.describtion" oninput="return LessThanAuto(this,'txtNum');"
                                       onchange="return LessThanAuto(this,'txtNum');" onpropertychange="return LessThanAuto(this,'txtNum');"
                                       maxlength="100" class="easyui-validatebox" style="width:99%;height:80px;" data-options="required:true"
                                       rows="5" cols="6">${wftelpasswordprocessEntity.describtion}</textarea><span id="txtNum"></span>
                        </td>
                    </tr>
                    <tr align="center">
                        <td width="10%">附件</td>
                        <td colspan="5" class="td_style2">
                            <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                            <input type="hidden" id="attachids" name="wftelpasswordprocess.attachids" value="${wftelpasswordprocessEntity.attachids }"/>
                            <div id="dowloadUrl">
                                <c:forEach items="${file}" varStatus="i" var="item">
                                    <div id="${item.id}"
                                         style="line-height:30px;margin-left:5px;" class="float_L">
                                        <div class="float_L">
                                            <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                        </div>
                                        <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                    </div>
                                </c:forEach>
                            </div>
                        </td>
                    </tr>
                    <tr align="center">
                        <td width="10%">附件說明</td>
                        <td colspan="5" class="td_style2">
                            <div style="float: left">附件為電話密碼申請的材料模板</div>
                            <div style="float: left"><a href="${ctx}/wftelpasswordprocess/downLoad/telpwdFile">承諾書下載</a></div>
                        </td>
                    </tr>
                    <tr align="center">
                        <td width="10%">備註</td>
                        <td colspan="5" class="td_style2">
                            1.如需經常撥打國際長途，請申請VOIP專線密碼；<br>
                            2.“密碼升級”和“變更使用區域”需求作業時密碼不變，請直接于http://tel.efoxconn.com/查詢相關信息；<br>
                            3.新增及升級密碼的金額和級別由用戶預申請再由主管復核；<br>
                            4.申請的使用限額是指每月使用最大金額；<br>
                            5.可登錄電話查詢網站http://tel.efoxconn.com/進行如下業務：帳號餘額查詢，電話清單查詢，通話分析，密碼變更（用戶在網上可變更自己的電話密碼）。
                        </td>
                    </tr>
                </table>
            </tr>
            <tr>
                <td>
                    <table class="formList" id="validForm3">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','電話密碼申請');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl5Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno5_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'yl5Table','ylno5','ylname5',$('#applyfactoryid').combobox('getValue'),'wftelpasswordprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno5" name="wftelpasswordprocess.ylno5"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno5']}"
                                                               readonly
                                                               value="${wftelpasswordprocessEntity.ylno5 }"/><c:if
                                                            test="${requiredMap['ylno5'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname5" name="wftelpasswordprocess.ylname5"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno5']}"
                                                                value="${wftelpasswordprocessEntity.ylname5 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon" onclick="selectRole($('#applydeptno').val(),'cchargeno','cchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wftelpasswordprocess.cchargeno" class="easyui-validatebox" data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly value="${wftelpasswordprocessEntity.cchargeno }"/><c:if test="${requiredMap['cchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wftelpasswordprocess.cchargename" readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}" value="${wftelpasswordprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon" onclick="selectRole3('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applynofactoryid').val(),'wftelpasswordprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wftelpasswordprocess.zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wftelpasswordprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="wftelpasswordprocess.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wftelpasswordprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#applydeptno').val(),'zcchargeno','zcchargename',$('#applynofactoryid').val(),'wftelpasswordprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="wftelpasswordprocess.zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wftelpasswordprocessEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename"
                                                                name="wftelpasswordprocess.zcchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wftelpasswordprocessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno2_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(62,'yl2Table','ylno2','ylname2',$('#applyfactoryid').combobox('getValue'),'wftelpasswordprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="wftelpasswordprocess.ylno2" class="easyui-validatebox" data-options="width:80,required:${requiredMap['ylno2']}"
                                                               readonly value="${wftelpasswordprocessEntity.ylno2 }"/><c:if test="${requiredMap['ylno2'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="ylname2" name="wftelpasswordprocess.ylname2" readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}" value="${wftelpasswordprocessEntity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl6Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno6_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon" onclick="selectRole2(25,'yl6Table','ylno6','ylname6',$('#applyfactoryid').combobox('getValue'),'wftelpasswordprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno6" name="wftelpasswordprocess.ylno6"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno6']}"
                                                               readonly
                                                               value="${wftelpasswordprocessEntity.ylno6 }"/><c:if
                                                            test="${requiredMap['ylno6'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname6" name="wftelpasswordprocess.ylname6"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno6']}"
                                                                value="${wftelpasswordprocessEntity.ylname6 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#applydeptno').val(),'pcchargeno','pcchargename',$('#applynofactoryid').val(),'wftelpasswordprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="wftelpasswordprocess.pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${wftelpasswordprocessEntity.pcchargeno }"/>
                                                        <c:if test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        /<input id="pcchargename"
                                                                name="wftelpasswordprocess.pcchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wftelpasswordprocessEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wftelpasswordprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10"><font color="red">溫馨提示：如果您在填單過程中有任何疑問，請聯繫本廠區資訊人員：</font><a href="${ctx}/wfvlanprocess/downLoadcontact">各廠區資訊聯繫方式</a></td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <input type="checkbox" id="agree" name="agree"/><a href="${ctx}/requisitionlist/downLoad/commitmentTpl" plain="true" id="btnCommitmentTpl">本人已閱讀并同意服務條款</a>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;"
                                   onclick="canelTask('${wftelpasswordprocessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <div style="clear: both;"></div>
        <div id="reAllotDiv" style="z-index: 100; position: absolute; width:480px; top: 410px; right: 230px;background: #fff ; display: none;">
            <table class="formList">
                <th colspan="4" ><font>多區域分配</font></th>
                <tr align="center">
                    <td style="width:30%;">使用區域</td>
                    <td style="width:30%;">金額分配比例（%）</td>
                    <td style="width:30%;">廠區分機字頭</td>
                    <td style="width:10%;">操作</td>
                </tr>
                <tbody id="reAllotTbody">
                <c:if test="${wftelpwditems!=null&&wftelpwditems.size()>0}">
                    <c:forEach items="${wftelpwditems}" var="wftelpwditem" varStatus="status">
                        <tr align="center" id="telpwditems${status.index+1}">
                            <td>
                                <input id="useregionitems${status.index+1}" name="wftelpwditems[${status.index+1}].useregionitems"
                                       data-options="panelHeight:300,required:true,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'useregionitems${status.index+1}\',\'请選擇區域\']',onBeforeLoad:function(){loadapplyfactory(${status.index+1});},onSelect:function(){checkCodeUnique(${status.index+1});}"
                                       style="width:120px;" class="easyui-combobox"  value="${wftelpwditem.useregionitems}"/>
                                <input type="hidden" name="wftelpwditems[${status.index+1}].useregionitemsname" id="useregionitemsname${status.index+1}" value="${wftelpwditem.useregionitemsname}">
                            </td>
                            <td>
                                <input id="allocationscale${status.index+1}" type="text" onblur="isNum(this)" onchange='sumMoney(this)' name="wftelpwditems[${status.index+1}].allocationscale" class="easyui-validatebox" data-options="width: 100,required:true"
                                       value="${wftelpwditem.allocationscale }"/><font color="red">*</font>
                            </td>
                            <td>
                                <input id="regiontelbeginitems${status.index+1}" type="text" name="wftelpwditems[${status.index+1}].regiontelbeginitems" class="easyui-validatebox" data-options="width: 100,required:true"
                                       value="${wftelpwditem.regiontelbeginitems }"/><font color="red">*</font>
                            </td>
                            <td>
                                <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="telpwddeltr(${status.index+1});return false;"/>
                            </td>
                        </tr>
                    </c:forEach>
                </c:if>
                <c:if test="${wftelpwditems.size()==0 || wftelpwditems==null}">
                    <tr align="center" id="telpwditems1">
                        <td>
                            <input id="useregionitems1" name="wftelpwditems[1].useregionitems"
                                   data-options="panelHeight:300,required:true,valueField:'factoryid',textField:'factoryname',editable:false,validType:'comboxValidate[\'useregionitems1\',\'请選擇區域\']',onBeforeLoad:function(){loadapplyfactory(1);},onSelect:function(){checkCodeUnique(1);}"
                                   style="width:120px;" class="easyui-combobox"  value=""/>
                            <input type="hidden" name="wftelpwditems[1].useregionitemsname" id="useregionitemsname1" value="">
                        </td>
                        <td>
                            <input id="allocationscale1" type="text"  name="wftelpwditems[1].allocationscale" onblur="isNum(this)" onchange='sumMoney(this)' class="easyui-validatebox" data-options="width: 100,required:true"
                                   value=""/><font color="red">*</font>
                        </td>
                        <td>
                            <input id="regiontelbeginitems1"  type="text" name="wftelpwditems[1].regiontelbeginitems" class="easyui-validatebox" data-options="width: 100,required:true"
                                   value=""/><font color="red">*</font>
                        </td>
                        <td>
                            <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="telpwddeltr(1);return false;"/>
                        </td>
                    </tr>
                </c:if>
                </tbody>
                <tr align="center">
                    <th colspan="4">
                        <input type="button" id="telpwdAdd" style="width:80px;" value="添加"/>
                    </th>
                </tr>
                <tr align="center">
                    <td colspan="4" style="border:none;line-height: 25px;" >
                        <a href="javascript:;" class="easyui-linkbutton" style="width: 80px;" onclick="coleDiv();">確定</a>&nbsp;&nbsp;&nbsp;&nbsp;
                        <a href="javascript:;" class="easyui-linkbutton" style="width: 80px;" onclick="hiddenDiv();">取消</a>
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="areaId" name="areaId" value=""/>
    <input type="hidden" id="buildingId" name="buildingId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <div id="win"></div>
    <div id="dlg"></div>
</form>
<script src='${ctx}/static/js/information/wftelpasswordprocess.min.js?random=20241011'></script>
</body>
</html>
