<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>電話密碼申請</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
        .td_style3{
            border: none 0px;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wftelpasswordprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wftelpasswordprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wftelpasswordprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">電話密碼申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wftelpasswordprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wftelpasswordprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wftelpasswordprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wftelpasswordprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wftelpasswordprocessEntity.makerno}/${wftelpasswordprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">承辦人工號</td>
                            <td width="10%" class="td_style2">
                                <input id="dealno" name="dealno" class="easyui-validatebox  inputCss" readonly data-options="width: 80"
                                       value="${wftelpasswordprocessEntity.dealno}"/>
                            </td>
                            <td width="8%">承辦人</td>
                            <td width="10%" class="td_style2">${wftelpasswordprocessEntity.dealname }</td>
                            <td width="10%">單位代碼</td>
                            <td width="10%" class="td_style2">${wftelpasswordprocessEntity.dealdeptno }</td>
                            <td width="10%">廠區</td>
                            <td width="10%" class="td_style1">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wftelpasswordprocessEntity.dealfactoryid }" disabled
                                       data-options="width: 80,required:true,validType:'comboxValidate[\'dealfactoryid\',\'请選擇承辦人廠區\']'"/>
                            </td>
                            <td width="8%">聯繫分機</td>
                            <td width="16%" class="td_style2">${wftelpasswordprocessEntity.dealtel}</td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="5" class="td_style2">${wftelpasswordprocessEntity.dealdeptname }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wftelpasswordprocessEntity.dealemail }</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td>申請人工號</td>
                            <td class="td_style2">${wftelpasswordprocessEntity.applyno}</td>
                            <td>申請人</td>
                            <td class="td_style2">${wftelpasswordprocessEntity.applyname}</td>
                            <td>單位代碼</td>
                            <td class="td_style2">${wftelpasswordprocessEntity.applydeptno }</td>
                            <td>費用代碼</td>
                            <td class="td_style2">${wftelpasswordprocessEntity.applycostno }</td>
                            <td>所在廠區</td>
                            <td class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wftelpasswordprocessEntity.applyfactoryid }" disabled
                                       data-options="width: 100,required:true,validType:'comboxValidate[\'applyfactoryid\',\'请選擇申請人廠區\']',onSelect:function(){onchangeFactory('applyfactoryid');}"/>
                                <input id="applynofactoryid" name="applynofactoryid" type="hidden" value="${wftelpasswordprocessEntity.applynofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style2">${wftelpasswordprocessEntity.applyleveltype }</td>
                            <td>管理職</td>
                            <td class="td_style2">${wftelpasswordprocessEntity.applymanager }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wftelpasswordprocessEntity.applyemail }</td>
                            <td>使用區域</td>
                            <td class="td_style1">
                                <input id="applyarea" name="applyarea" class="easyui-combobox" disabled data-options="width: 80,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wftelpasswordprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="applybuilding" class="easyui-combobox" disabled data-options="width: 80,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wftelpasswordprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機</td>
                            <td class="td_style2">${wftelpasswordprocessEntity.applyphone }</td>
                            <td>單位</td>
                            <td colspan="5" class="td_style2">${wftelpasswordprocessEntity.applydeptname }</td>
                            <td>安保區域</td>
                            <td class="td_style2">
                                <div class="securityareaDiv"></div>
                                <input id="securityarea" name="securityarea"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wftelpasswordprocessEntity.securityarea }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>法人代碼</td>
                            <td class="td_style2">
                                ${wftelpasswordprocessEntity.applycompanycode}
                            </td>
                            <td>法人名稱</td>
                            <td class="td_style2" colspan="7">
                                ${wftelpasswordprocessEntity.applycompanyname}
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
            <table class="formList">
                <tr>
                    <td colspan="6" class="td_style1">電話密碼申請</td>
                </tr>
                <tr align="center">
                    <td width="10%">申請原因</td>
                    <td colspan="5" class="td_style2">
                        <div class="applytypeDiv"></div>
                        <input id="applytype" name="wftelpasswordprocess.applytype"
                               type="hidden" class="easyui-validatebox" data-options="width: 150"
                               value="${wftelpasswordprocessEntity.applytype }"/>
                    </td>
                </tr>
                <tr align="center">
                    <td width="10%">區域</td>
                    <td class="td_style2" width="20%">
                        <div class="regiontypeDiv"></div>
                        <input id="regiontype" name="wftelpasswordprocess.regiontype"
                               type="hidden" class="easyui-validatebox" data-options="width: 100"
                               value="${wftelpasswordprocessEntity.regiontype }"/>
                    </td>
                    <td width="10%">使用區域</td>
                    <c:choose>
                        <c:when test="${wftelpasswordprocessEntity.regiontype=='1'}">
                            <td class="td_style2" width="20%">
                                <c:if test="${wftelpwditems!=null&&wftelpwditems.size()>0}">
                                    <c:forEach items="${wftelpwditems}" var="wftelpwditem" varStatus="status">
                                        ${wftelpwditem.useregionitemsname}，
                                    </c:forEach>
                                </c:if>
                            </td>
                            <td width="10%">區域分機字頭</td>
                            <td class="td_style2" width="20%">
                                <c:if test="${wftelpwditems!=null&&wftelpwditems.size()>0}">
                                    <c:forEach items="${wftelpwditems}" var="wftelpwditem" varStatus="status">
                                        ${wftelpwditem.regiontelbeginitems};
                                    </c:forEach>
                                </c:if>
                            </td>
                        </c:when>
                        <c:otherwise>
                            <td class="td_style2" width="20%">
                                <input id="useregion" name="wftelpasswordprocess.useregion" class="easyui-combobox"
                                       panelHeight="auto" value="${wftelpasswordprocessEntity.useregion }" disabled
                                       data-options="width: 120"/>
                            </td>
                            <td width="10%">區域分機字頭</td>
                            <td class="td_style2" width="20%">${wftelpasswordprocessEntity.regiontelbegin}</td>
                        </c:otherwise>

                    </c:choose>
                </tr>
                <tr align="center">
                    <td>使用限額</td>
                    <td class="td_style2">${wftelpasswordprocessEntity.usenorm }</td>
                    <td>每日使用分機數</td>
                    <td class="td_style2">${wftelpasswordprocessEntity.usetelnum }</td>
                    <td>主要撥打城市</td>
                    <td class="td_style2">${wftelpasswordprocessEntity.mainpullter }</td>
                </tr>
                <tr align="center">
                    <td>密碼領取方式</td>
                    <td colspan="2" class="td_style2">
                        <div class="pwdgetDiv"></div>
                        <input id="pwdget" name="wftelpasswordprocess.pwdget"
                               type="hidden" class="easyui-validatebox" data-options="width: 100"
                               value="${wftelpasswordprocessEntity.pwdget }"/>
                    </td>
                    <td>手機號碼</td>
                    <td colspan="2" class="td_style2">${wftelpasswordprocessEntity.phone }</td>
                </tr>
                <tr align="center">
                    <td width="10%">預期級別</td>
                    <td colspan="5" class="td_style2">
                        <div class="expecttypeDiv"></div>
                        <input id="expecttype" name="wftelpasswordprocess.expecttype"
                               type="hidden" class="easyui-validatebox" data-options="width: 100"
                               value="${wftelpasswordprocessEntity.expecttype }"/>
                    </td>
                </tr>
                <tr align="center">
                    <td width="10%">需求說明</td></td>
                    <td colspan="5" class="td_style2">
                                <textarea id="describtion" name="describtion" class="easyui-validatebox" style="width:1000px;height:40px;"
                                          rows="5" cols="6">${wftelpasswordprocessEntity.describtion}</textarea></td>
                    </td>
                </tr>
                <tr align="center">
                    <td width="10%">附件</td>
                    <td colspan="5" class="td_style2">
                        <input type="hidden" id="attachids" name="attachids" value="${wftelpasswordprocessEntity.attachids }"/>
                        <div id="dowloadUrl">
                            <c:forEach items="${file}" varStatus="i" var="item">
                                <div id="${item.id}"
                                     style="line-height:30px;margin-left:5px;" class="float_L">
                                    <div class="float_L">
                                        <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                    </div>
                                </div>
                            </c:forEach>
                        </div>
                    </td>
                </tr>
                <tr align="center">
                    <td width="10%">批註</td>
                    <td colspan="5" class="td_style2">
						        <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                          style="width:1000px;height:60px;" rows="4" cols="4"></textarea>
                        <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                    </td>
                </tr>
            </table>
        </tr>
            <tr>
                <td>
                    <table class="formList">

                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wftelpasswordprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','電話密碼申請');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wftelpasswordprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<div id="dlg"></div>
<script src='${ctx}/static/js/information/wftelpasswordprocess.min.js?random=20241011'></script>
</body>
</html>