<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>人才房簽約人員離職房款補差單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/compensationforhouse/${action}" method="post">
          <!--
		        id 主鍵
    createBy 創建人
    createDate 創建時間
    updateBy 更新者
    updateDate 更新時間
    delFlag 刪除標識
    makerdeptno 填單人所在部門
    serialno 任務編碼
    makerno 填單人工號
    makername 填單人名稱
    workstatus 表單狀態
    attachids 附件Id
    applyno 申請人工號
    applyname 申請人姓名
    applydeptno 申請人單位代碼
    applydeptname 申請 人單位名稱
    layperson 法人
    applyphone 申請人電話
    applyfactoryid 申請人廠區id
    talentCategory 人才類別
    areaOfTalent 人才房分配面積
    agreedPurchasePrice 協議購房價格
    talentInfoArea 人才房信息小區
    talentInfoBuilding 人才房信息棟
    talentInfoUnit 人才房信息單元
    talentInfoNumber 人才房信息號
    priceTalentHouse 對外銷售平均價格

    housePayment 應補房款
    ylno1 申请人确认
    ylname1 申请人确认
    ylno2 產品處人資核查窗口
    ylname2 產品處人資核查窗口
    ylno3 產品處人資主管
    ylname3 產品處人資主管
    ylno4 周邊人力資源處主管
    ylname4 周邊人力資源處主管
    ylno5 周邊總處主管
    ylname5 周邊總處主管
    pcchargeno 產品處級主管
    pcchargename 產品處級主管
		   -->
    <input id="ids" name="ids" type="hidden" value="${compensationForHouseEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${compensationForHouseEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${compensationForHouseEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${compensationForHouseEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${compensationForHouseEntity.makerdeptno }"/>
<%--    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${compensationForHouseEntity.makerfactoryid }"/>--%>
    <div class="commonW">
    <div class="headTitle">人才房簽約人員離職房款補差單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${compensationForHouseEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${compensationForHouseEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${compensationForHouseEntity.createDate==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${compensationForHouseEntity.createDate}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty compensationForHouseEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty compensationForHouseEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${compensationForHouseEntity.makerno}/${compensationForHouseEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="10" class="td_style1">签约人员基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${compensationForHouseEntity.applyno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="8%">申請人</td>
                            <td width="10%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${compensationForHouseEntity.applyname }"/>
                            </td>
                            <td width="8%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${compensationForHouseEntity.applydeptno }"/>
                            </td>
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">&nbsp;
                                <input id="applyphone" name="applyphone" class="easyui-validatebox"
                                       style="width:100px;"
                                       value="${compensationForHouseEntity.applyphone}"
                                       data-options="required:true"/>
                            </td>
                            <td width="80">廠區</td>
                            <td class="td_style1"><input id="applyfactoryid" name="applyfactoryid"
                                                         class="easyui-combobox"
                                                         data-options="width: 150,disabled:true"
                                                         value="${compensationForHouseEntity.applyfactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>法人&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="layperson" name="layperson" class="easyui-combobox"
                                       panelHeight="auto" value="${compensationForHouseEntity.layperson }"
                                       data-options="width: 300,required:true,validType:'comboxValidate[\'layperson\',\'请选择法人\']'"/>
                            </td>
                            <td width="8%">單位&nbsp;<font color="red">*</font></td>
                            <td class="td_style1" colspan="3">
                                <input id="applydeptname" name="applydeptname"
                                       class="easyui-validatebox" style="width:90%;" data-options="required:true"
                                       value="${compensationForHouseEntity.applydeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox inputCss"
                                       style="width:90%;" data-options="required:true" readonly
                                       value="${compensationForHouseEntity.applyemail }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>人才房類別&nbsp;<font color="red">*</font></td>
                            <td colspan="4">
                                <div class="talentCategoryDiv"></div>
                                <input id="talentCategoryValue" name="talentCategoryValue"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${compensationForHouseEntity.talentCategory }"/>
                            </td>
                            <td>人才房信息&nbsp;<font color="red">*</font></td>
                            <td colspan="4">
                                <input id="talentInfoArea" name="talentInfoArea" style="text-align: center"
                                       class="easyui-validatebox" data-options="width:150,required:true"
                                       value="${compensationForHouseEntity.talentInfoArea }"/>小區
                                <input id="talentInfoBuilding" name="talentInfoBuilding" style="text-align: center"
                                       class="easyui-validatebox" data-options="width:50,required:true"
                                       value="${compensationForHouseEntity.talentInfoBuilding }"/>棟
                                <input id="talentInfoUnit" name="talentInfoUnit" style="text-align: center"
                                       class="easyui-validatebox" data-options="width:50,required:true"
                                       value="${compensationForHouseEntity.talentInfoUnit }"/>單元
                                <input id="talentInfoNumber" name="talentInfoNumber" style="text-align: center"
                                       class="easyui-validatebox" data-options="width:50,required:true"
                                       value="${compensationForHouseEntity.talentInfoNumber }"/>號
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" class="td_style1">人才房房款補差</td>
                        </tr>
                        <tr align="center">
                            <td colspan="4">人才房分配面積&nbsp;<font color="red">*</font></td>
                            <td colspan="6">
                                <input id="areaOfTalent"
                                       oninput="calcPrice();"
                                       onchange="calcPrice();"
                                       onpropertychange="calcPrice();"
                                       style="text-align: center" class="easyui-numberbox" name="areaOfTalent"
                                       data-options="precision:2,groupSeparator:',',width: 300,required:true"
                                       value="${compensationForHouseEntity.areaOfTalent }"/>&nbsp;&nbsp;平方米
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="4">協議購房價格&nbsp;<font color="red">*</font></td>
                            <td colspan="6">
                                <input id="agreedPurchasePrice"
                                       oninput="calcPrice();"
                                       onchange="calcPrice();"
                                       onpropertychange="calcPrice();"
                                       style="text-align: center" class="easyui-numberbox"
                                       name="agreedPurchasePrice"
                                       data-options="precision:2,groupSeparator:',',width: 300,required:true"
                                       value="${compensationForHouseEntity.agreedPurchasePrice }"/>&nbsp;&nbsp;元/平方米
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="4">人才房對外銷售平均價格&nbsp;<font color="red">*</font></td>
                            <td colspan="6">
                                <input id="priceTalentHouse"
                                       oninput="calcPrice();"
                                       onchange="calcPrice();"
                                       onpropertychange="calcPrice();"
                                       style="text-align: center" class="easyui-numberbox"
                                       name="priceTalentHouse"
                                       data-options="precision:2,groupSeparator:',',width: 300,required:true"
                                       value="${compensationForHouseEntity.priceTalentHouse }"/>&nbsp;&nbsp;元/平方米
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="4">應補房款&nbsp;<font color="red">*</font></td>
                            <td colspan="6">
                                人民幣&nbsp;&nbsp;<input style="text-align: center" id="housePayment"
                                                      class="easyui-numberbox" name="housePayment"
                                                      data-options="precision:0,groupSeparator:',',width: 150,required:true"
                                                      value="${compensationForHouseEntity.housePayment }"/>&nbsp;&nbsp;元&nbsp;&nbsp;（<label
                                    id="housePaymentUp"> 拾 萬 千 百 拾 元 角 分</label>）
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" class="td_style1">人才房補差房款由山西財務委託太原周邊經管處代收（DF2 一層）</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_rencaifanglizhifangkuanbuchadan_v3','人才房簽約人員離職房款補差單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl1Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">申請人確認</td>
                                                                <td style="border: none;">

                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno1" name="ylno1"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno1']}"
                                                               readonly
                                                               value="${compensationForHouseEntity.ylno1 }"/><c:if
                                                            test="${requiredMap['ylno1'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname1" name="ylname1"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno1']}"
                                                                value="${compensationForHouseEntity.ylname1 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產品處人資核查窗口
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(218,'yl2Table','ylno2','ylname2',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="ylno2"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}"
                                                               readonly
                                                               value="${compensationForHouseEntity.ylno2 }"/><c:if
                                                            test="${requiredMap['ylno2'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname2" name="ylname2"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}"
                                                                value="${compensationForHouseEntity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl3Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產品處人資主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(219,'yl3Table','ylno3','ylname3',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno3" name="ylno3"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno3']}"
                                                               readonly
                                                               value="${compensationForHouseEntity.ylno3 }"/><c:if
                                                            test="${requiredMap['ylno3'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname3" name="ylname3"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno3']}"
                                                                value="${compensationForHouseEntity.ylname3 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產品處級主管核准
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(225,'pcchargeTable','pcchargeno','pcchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${compensationForHouseEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${compensationForHouseEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl4Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">周邊人力資源處主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(220,'yl4Table','ylno4','ylname4',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno4" name="ylno4"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno4']}"
                                                               readonly
                                                               value="${compensationForHouseEntity.ylno4 }"/><c:if
                                                            test="${requiredMap['ylno4'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname4" name="ylname4"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno4']}"
                                                                value="${compensationForHouseEntity.ylname4 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl5Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">周邊總處主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(221,'yl5Table','ylno5','ylname5',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno5" name="ylno5"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno5']}"
                                                               readonly
                                                               value="${compensationForHouseEntity.ylno5 }"/><c:if
                                                            test="${requiredMap['ylno5'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname5" name="ylname5"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno5']}"
                                                                value="${compensationForHouseEntity.ylname5 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="jczhchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">敬呈知會</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(269,'jczhchargeTable','jczhchargeno','jczhchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="jczhchargeno" name="jczhchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width: 80,required:${requiredMap['jczhchargeno']}" readonly
                                                               value="${compensationForHouseEntity.jczhchargeno }"/><c:if test="${requiredMap['jczhchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /
                                                        <input id="jczhchargename" name="jczhchargename"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['jczhchargeno']}"
                                                               value="${compensationForHouseEntity.jczhchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
    <div id="dlg"></div>
</form>
</div>
<script src='${ctx}/static/js/information/compensationforhouse.js?random=<%= Math.random()%>'></script>
</body>
</html>