<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>人才房簽約人員離職房款補差單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/compensationforhouse/${action}" method="post">
<!--
		        id 主鍵
    createBy 創建人
    createDate 創建時間
    updateBy 更新者
    updateDate 更新時間
    delFlag 刪除標識
    makerdeptno 填單人所在部門
    serialno 任務編碼
    makerno 填單人工號
    makername 填單人名稱
    workstatus 表單狀態
    attachids 附件Id
    applyno 申請人工號
    applyname 申請人姓名
    applydeptno 申請人單位代碼
    applydeptname 申請 人單位名稱
    layperson 法人
    applyphone 申請人電話
    applyfactoryid 申請人廠區id
    talentCategory 人才類別
    areaOfTalent 人才房分配面積
    agreedPurchasePrice 協議購房價格
    talentInfoArea 人才房信息小區
    talentInfoBuilding 人才房信息棟
    talentInfoUnit 人才房信息單元
    talentInfoNumber 人才房信息號
    priceTalentHouse 對外銷售平均價格

    housePayment 應補房款
    ylno1 申请人确认
    ylname1 申请人确认
    ylno2 產品處人資核查窗口
    ylname2 產品處人資核查窗口
    ylno3 產品處人資主管
    ylname3 產品處人資主管
    ylno4 周邊人力資源處主管
    ylname4 周邊人力資源處主管
    ylno5 周邊總處主管
    ylname5 周邊總處主管
    pcchargeno 產品處級主管
    pcchargename 產品處級主管
		   -->
    <input id="ids" name="ids" type="hidden" value="${compensationForHouseEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${compensationForHouseEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">人才房簽約人員離職房款補差單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${compensationForHouseEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${compensationForHouseEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${compensationForHouseEntity.createDate==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${compensationForHouseEntity.createDate}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${compensationForHouseEntity.makerno}/${compensationForHouseEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="10" class="td_style1">签约人员基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">申請人工號</td>
                            <td width="10%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true,disabled:true"
                                       value="${compensationForHouseEntity.applyno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="8%">申請人</td>
                            <td width="10%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${compensationForHouseEntity.applyname }"/>
                            </td>
                            <td width="8%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${compensationForHouseEntity.applydeptno }"/>
                            </td>
                            <td>聯繫方式</td>
                            <td class="td_style1">&nbsp;
                                <input id="applyphone" name="applyphone" class="easyui-validatebox"
                                       style="width:100px;"
                                       value="${compensationForHouseEntity.applyphone}"
                                       data-options="required:true,disabled:true"/>
                            </td>
                            <td width="80">廠區</td>
                            <td class="td_style1"><input id="applyfactoryid" name="applyfactoryid"
                                                         class="easyui-combobox"
                                                         data-options="width: 150,disabled:true"
                                                         value="${compensationForHouseEntity.applyfactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>法人</td>
                            <td colspan="3" class="td_style1">
                                <input id="layperson" name="layperson" class="easyui-combobox"
                                       panelHeight="auto" value="${compensationForHouseEntity.layperson }"
                                       data-options="width: 300,disabled:true,required:true,validType:'comboxValidate[\'layperson\',\'请选择法人\']'"/>
                            </td>
                            <td width="8%">單位</td>
                            <td class="td_style1" colspan="3">
                                <input id="applydeptname" name="applydeptname"
                                       class="easyui-validatebox" style="width:90%;" data-options="required:true,disabled:true"
                                       value="${compensationForHouseEntity.applydeptname }"/>
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="2" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox inputCss"
                                       style="width:90%;" data-options="required:true" readonly
                                       value="${compensationForHouseEntity.applyemail }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>人才房類別</td>
                            <td colspan="4">
                                <div class="talentCategoryDiv"></div>
                                <input id="talentCategoryValue" name="talentCategoryValue"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${compensationForHouseEntity.talentCategory }"/>
                            </td>
                            <td>人才房信息</td>
                            <td colspan="4">
                                <input id="talentInfoArea" name="talentInfoArea" style="text-align: center"
                                       class="easyui-validatebox" data-options="width:150,required:true,disabled:true"
                                       value="${compensationForHouseEntity.talentInfoArea }"/>小區
                                <input id="talentInfoBuilding" name="talentInfoBuilding" style="text-align: center"
                                       class="easyui-validatebox" data-options="width:50,required:true,disabled:true"
                                       value="${compensationForHouseEntity.talentInfoBuilding }"/>棟
                                <input id="talentInfoUnit" name="talentInfoUnit" style="text-align: center"
                                       class="easyui-validatebox" data-options="width:50,required:true,disabled:true"
                                       value="${compensationForHouseEntity.talentInfoUnit }"/>單元
                                <input id="talentInfoNumber" name="talentInfoNumber" style="text-align: center"
                                       class="easyui-validatebox" data-options="width:50,required:true,disabled:true"
                                       value="${compensationForHouseEntity.talentInfoNumber }"/>號
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" class="td_style1">人才房房款補差</td>
                        </tr>
                        <tr align="center">
                            <td colspan="4">人才房分配面積</td>
                            <td colspan="6">
                                <input id="areaOfTalent"
                                       oninput="calcPrice();"
                                       onchange="calcPrice();"
                                       onpropertychange="calcPrice();"
                                       style="text-align: center" class="easyui-numberbox" name="areaOfTalent"
                                       data-options="precision:2,groupSeparator:',',width: 300,required:true,disabled:true"
                                       value="${compensationForHouseEntity.areaOfTalent }"/>&nbsp;&nbsp;平方米
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="4">協議購房價格</td>
                            <td colspan="6">
                                <input id="agreedPurchasePrice"
                                       oninput="calcPrice();"
                                       onchange="calcPrice();"
                                       onpropertychange="calcPrice();"
                                       style="text-align: center" class="easyui-numberbox"
                                       name="agreedPurchasePrice"
                                       data-options="precision:2,groupSeparator:',',width: 300,required:true,disabled:true"
                                       value="${compensationForHouseEntity.agreedPurchasePrice }"/>&nbsp;&nbsp;元/平方米
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="4">人才房對外銷售平均價格</td>
                            <td colspan="6">
                                <input id="priceTalentHouse"
                                       oninput="calcPrice();"
                                       onchange="calcPrice();"
                                       onpropertychange="calcPrice();"
                                       style="text-align: center" class="easyui-numberbox"
                                       name="priceTalentHouse"
                                       data-options="precision:2,groupSeparator:',',width: 300,required:true,disabled:true"
                                       value="${compensationForHouseEntity.priceTalentHouse }"/>&nbsp;&nbsp;元/平方米
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="4">應補房款</td>
                            <td colspan="6">
                                人民幣&nbsp;&nbsp;<input style="text-align: center" id="housePayment"
                                                      class="easyui-numberbox" name="housePayment"
                                                      data-options="precision:0,groupSeparator:',',width: 150,required:true,disabled:true"
                                                      value="${compensationForHouseEntity.housePayment }"/>&nbsp;&nbsp;元&nbsp;&nbsp;（<label
                                    id="housePaymentUp"> 拾 萬 千 百 拾 元 角 分</label>）
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" class="td_style1">人才房補差房款由山西財務委託太原周邊經管處代收（DF2 一層）</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','人才房簽約人員離職房款補差單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${compensationForHouseEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                               <c:if test="${compensationForHouseEntity.workstatus!=null&&compensationForHouseEntity.workstatus==3}">
                                  <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                      style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                               </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/information/compensationforhouse.js?random=<%= Math.random()%>'></script>
</body>
</html>