<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>AD域賬號申請</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input type="text" name="filter_EQS_dealno" class="easyui-validatebox" data-options="width:150,prompt: '申請人工號'"/>
        <input type="text" name="filter_EQS_dealdeptno" class="easyui-validatebox" data-options="width:150,prompt: '申請人單位代碼'"/>
        <input type="text" name="filter_EQS_makerno" class="easyui-validatebox" data-options="width:150,prompt: '填單人工號'"/>
        <input type="text" name="filter_EQS_serialno" class="easyui-validatebox" data-options="width:150,prompt: '任務編碼'"/>
        <input id="cxFactory" name="filter_EQS_applyfactoryid" class="easyui-combobox"
               panelHeight="auto" data-options="width:150"/>
        <input type="text" name="filter_GED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width:150,prompt: '填單开始日期'"/>
        - <input type="text" name="filter_LED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width:150,prompt: '填單结束日期'"/>
        <input type="text" name="filter_GED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width:150,prompt: '簽核完成开始日期'"/>
        - <input type="text" name="filter_LED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width:150,prompt: '簽核完成结束日期'"/>
        <input id="qysjzt" style="width:100px" class="easyui-validatebox" name="filter_EQS_workstatus"/>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
           onclick="listSearchReset()">重置</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
           onclick="exportExcel()">导出Excel</a>
        <!--導出用，請勿刪除-->
        <input id="page" name="page" type="hidden" value="1"/>
        <input id="rows" name="rows" type="hidden" value="30"/>
    </form>

</div>
<table id="dg"></table>
<div id="dlg"></div>
<script type="text/javascript">
    var dg;
    var d;
    $(function () {
        dg = $('#dg').datagrid({
            method: "get",
            url: ctx + '/wfadaccountprocess/list',
            fit: true,
            fitColumns: true,
            //fitColumns: false,  //如需要底部出現滾動條，修改爲false
            border: false,
            idField: 'id',
            striped: true,
            cache: false,  //关闭AJAX相应的缓存
            pagination: true,
            rownumbers: true,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: true,
            columns: [[
                {field: 'id', title: '主鍵', hidden: true},
                {field: 'serialno', title: '任務編號', sortable: true, width: 150, formatter: operation},
                {field: 'dealno', title: '工號', sortable: true, width: 80},
                {field: 'dealname', title: '姓名', sortable: true, width: 80},
                {field: 'dealdeptno', title: '單位代碼', sortable: true, width: 100},
                {field: 'applycostno', title: '費用代碼', sortable: true, width: 100},
                {field: 'makerno', title: '填單人', sortable: true, width: 120,
                    formatter : function(value, row) {
                        return row.makerno + "/" + row.makername;
                    }},
                {field: 'createtime', title: '填單日期', sortable: true, width: 100},
                {field: 'workstatus', title: '任務狀態', sortable: true, width: 100},
                {field: 'auditUser', title: '當前簽核人', sortable: true, width: 120},
                {field: 'complettime', title: '簽核完成時間', sortable: true, width: 100}
            ]],
            onLoadSuccess: function () {
                $(".easyui-tooltip").tooltip({
                    onShow: function () {
                        $(this).tooltip('tip').css({
                            borderColor: '#000'
                        });
                    }
                });

            },
            rowStyler: rowStylerFun,
            enableHeaderClickMenu: true,
            enableHeaderContextMenu: true,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });
        //創建下拉查詢條件
        $.ajax({
            url: ctx + "/system/dict/getDictByType/audit_status",
            dataType: "json",
            type: "GET",
            success: function (data) {
                //绑定第一个下拉框
                $("#qysjzt").combobox({
                    data: data,
                    valueField: "value",
                    textField: "label",
                    editable: false,
                    panelHeight: 400,
                    loadFilter: function (data) {
                        data.unshift({value: '', label: '請選擇'});
                        return data;
                    }
                });
            },
            error: function (error) {
                alert("初始化下拉控件失败");
            }
        });
        //創建表單查詢任務廠區下拉查詢條件
        $.get(ctx + '/tqhfactoryidconfig/allFactorys/', function(result) {
            var requestFactoryResult = JSON.parse(JSON.stringify(result));
            requestFactoryResult.unshift({
                factoryid : '',
                factoryname : '請選擇承辦人所在廠區'
            });
            $("#cxFactory").combobox({
                data : requestFactoryResult,
                valueField : "factoryid",
                textField : "factoryname",
                editable : false,
                panelHeight : 350
            });
        },"json");
    })
    //格式化
    function operation(value, row, index) {
        if (row.workstatus == 0) {
            return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('AD域賬號申請單',ctx+'/wfadaccountprocess/create/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
        } else if (row.workstatus == 4) {
            return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('AD域賬號申請單',ctx+'/wfadaccountprocess/reject/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
        }
        return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('AD域賬號申請單',ctx+'/wfadaccountprocess/view/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
    };
    //创建查询对象并查询
    function cx() {
        var obj = $("#searchFrom").serializeObject();
        dg.datagrid('load', obj);
    }

    //表單進度查詢頁面查詢條件重置
    function listSearchReset() {
        $("#searchFrom").form("reset");
    }

    //导出excel
    function exportExcel() {
        var options = $('#dg').datagrid('getPager').data("pagination").options;
        var page = options.pageNumber;//当前页数  
        var total = options.total;
        var rows = options.pageSize;//每页的记录数（行数）  
        var form = document.getElementById("searchFrom");
        $('#page').val(page);
        $('#rows').val(total);
        var form = document.getElementById("searchFrom");
        searchFrom.action = ctx + '/wftelpasswordprocess/exportExcel';
        form.submit();
    }
</script>
</body>
</html>