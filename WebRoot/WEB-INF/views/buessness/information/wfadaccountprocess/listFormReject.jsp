<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>AD域賬號申請</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfadaccountprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfadaccountprocessEntity.id }"/>
    <input id="serialno" name="wfadaccountprocess.serialno" type="hidden" value="${wfadaccountprocessEntity.serialno}"/>
    <input id="makerno" name="wfadaccountprocess.makerno" type="hidden" value="${wfadaccountprocessEntity.makerno}"/>
    <input id="makername" name="wfadaccountprocess.makername" type="hidden" value="${wfadaccountprocessEntity.makername}"/>
    <input id="makerdeptno" name="wfadaccountprocess.makerdeptno" type="hidden" value="${wfadaccountprocessEntity.makerdeptno}"/>
    <input id="makerfactoryid" name="wfadaccountprocess.makerfactoryid" type="hidden" value="${wfadaccountprocessEntity.makerfactoryid}"/>
    <div class="commonW">
        <div class="headTitle">AD域賬號申請</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfadaccountprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfadaccountprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfadaccountprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfadaccountprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfadaccountprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfadaccountprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfadaccountprocessEntity.makerno}/${wfadaccountprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="dealno" name="wfadaccountprocess.dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="<c:if test="${wfadaccountprocessEntity.dealno!=null||wfadaccountprocessEntity.dealno!=''}">${wfadaccountprocessEntity.dealno}</c:if><c:if test="${wfadaccountprocessEntity.dealno==null||wfadaccountprocessEntity.dealno==''}">${user.loginName}</c:if>"
                                       onchange="queryUserInfo(this);"/>
                            </td>
                            <td width="8%">承辦人</td>
                            <td width="10%" class="td_style1">
                                <input id="dealname" name="wfadaccountprocess.dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wfadaccountprocessEntity.dealname }"/>
                            </td>
                            <td width="10%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="dealdeptno" name="wfadaccountprocess.dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfadaccountprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="7%">費用代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="applycostno" name="wfadaccountprocess.applycostno"
                                       class="easyui-validatebox inputCss" style="width:90px;"
                                       value="${wfadaccountprocessEntity.applycostno }"/>
                            </td>
                            <td width="10%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="15%" class="td_style1">
                                <input id="applyfactoryid" name="wfadaccountprocess.applyfactoryid"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${wfadaccountprocessEntity.applyfactoryid }"
                                       data-options="width: 120,required:true,onSelect:function(){onchangeAdFactory('applyfactoryid');}"/>
                                <input id="applynofactoryid" name="wfadaccountprocess.applynofactoryid" type="hidden"
                                       value="${wfadaccountprocessEntity.applynofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="wfadaccountprocess.applyleveltype"
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfadaccountprocessEntity.applyleveltype }" readonly/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="wfadaccountprocess.applymanager"
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfadaccountprocessEntity.applymanager }" readonly/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="wfadaccountprocess.dealemail" class="easyui-validatebox"
                                       value="${wfadaccountprocessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td>使用區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyarea" name="wfadaccountprocess.applyarea" class="easyui-combobox"
                                       data-options="width: 70,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeAdArea();}"
                                       value="${wfadaccountprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="wfadaccountprocess.applybuilding"
                                       class="easyui-combobox"
                                       data-options="width: 70,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']',onSelect:function(){onchangeAdBuilding();}"
                                       value="${wfadaccountprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealtel" name="wfadaccountprocess.dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfadaccountprocessEntity.dealtel}"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="7" class="td_style1">
                                <input id="dealdeptname" name="wfadaccountprocess.dealdeptname"
                                       class="easyui-validatebox" data-options="width: 410,required:true"
                                       value="${wfadaccountprocessEntity.dealdeptname }"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請明細</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <input id="pcprivilegeItemTableIndex" type="hidden"
                                           value="<c:if test="${pcprivilegeitems!=null && pcprivilegeitems.size()>0}">${pcprivilegeitems.size() +1}</c:if>
                                        <c:if test="${pcprivilegeitems.size()==0 || pcprivilegeitems==null}">2</c:if>"/>
                                    </input>
                                    <table id="pcprivilegeItemTable" width="100%">
                                        <tr align="center">
                                            <td width="6%">申請人工號&nbsp;<font color="red">*</font></td>
                                            <td width="6%">申請人</td>
                                            <td width="6%">所在廠區&nbsp;<font color="red">*</font></td>
                                            <td width="8%">網域名稱&nbsp;<font color="red">*</font></td>
                                            <td width="6%">申請動作&nbsp;<font color="red">*</font></td>
                                            <td width="8%">原使用賬號</td>
                                            <td width="8%">電腦名稱&nbsp;<font color="red">*</font></td>
                                            <td width="8%">IP地址&nbsp;<font color="red">*</font></td>
                                            <td width="5%">是否NPI</td>
                                            <td width="5%">&nbsp;操作&nbsp;</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wfadaccountitems!=null&&wfadaccountitems.size()>0}">
                                            <c:forEach items="${wfadaccountitems}" var="adaccountitems" varStatus="status">
                                                <tr align="center" id="adaccountitems${status.index+1}">
                                                    <td>
                                                        <input id="userno${status.index+1}" onblur="getUserNameByEmpno2(this,status.index+1);"
                                                               name="wfadaccountitems[${status.index+1}].userno" class="easyui-validatebox" style="width:80px;"
                                                               data-options="required:true" value="${adaccountitems.userno}"/>
                                                    </td>
                                                    <td>
                                                        <input id="username${status.index+1}" name="wfadaccountitems[${status.index+1}].username"
                                                               class="easyui-validatebox inputCss" style="width:80px;" readonly value="${adaccountitems.username}"/>
                                                    </td>
                                                    <td>
                                                        <input id="adfactoryid${status.index+1}" name="wfadaccountitems[${status.index+1}].adfactoryid" data-options="panelHeight:'auto',valueField:'value', textField:'label',validType:'comboxValidate[\'adfactoryid${status.index+1}\',\'请選擇所在廠區\']',onBeforeLoad:function(){loadAdfactoryid(${status.index+1});},onSelect:function(){adOnchangeFactory('adfactoryid${status.index+1}',${status.index+1});}" style="width:80px;"
                                                               class="easyui-combobox" editable="false" value="${adaccountitems.adfactoryid}"/>
                                                    </td>
                                                    <td>
                                                        <input id="wyname${status.index+1}" name="wfadaccountitems[${status.index+1}].wyname" data-options="validType:'comboxValidate[\'wyname${status.index+1}\',\'请選擇網域名稱\']'" style="width:120px;" panelHeight="auto" class="easyui-combobox" editable="false" value="${adaccountitems.wyname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyaction${status.index+1}" name="wfadaccountitems[${status.index+1}].applyaction" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadApplyaction(${status.index+1});},onSelect:function(){adOnchangeAction(${status.index+1});}" style="width:80px;"
                                                               class="easyui-combobox" editable="false" value="${adaccountitems.applyaction}"/>
                                                    </td>
                                                    <td>
                                                        <input id="account${status.index+1}" name="wfadaccountitems[${status.index+1}].account"
                                                               class="easyui-validatebox" style="width:100px;"  value="${adaccountitems.account}"/>
                                                    </td>
                                                    <td>
                                                        <input id="pcname${status.index+1}" name="wfadaccountitems[${status.index+1}].pcname"
                                                               class="easyui-validatebox" style="width:100px;" data-options="required:true" value="${adaccountitems.pcname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyip${status.index+1}" name="wfadaccountitems[${status.index+1}].applyip" class="easyui-validatebox" onblur="isNpiCheck(this,${status.index+1});"
                                                               data-options="required:true,validType:'ip[\'applyip${status.index+1}\']'" style="width:90px;" value="${adaccountitems.applyip}"/>
                                                    </td>
                                                    <td>
                                                        <input id="isnpi${status.index+1}" name="wfadaccountitems[${status.index+1}].applynpi" class="easyui-validatebox inputCss" readonly
                                                               data-options="width: 60" value="${adaccountitems.isnpi}"/>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="addeltr(${status.index+1});return false;"/>
                                                        <input id="shunxu${status.index+1}" type="hidden" name="wfadaccountitems[${status.index+1}].shunxu" value="${status.index+1}"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${wfadaccountitems.size()==0 || wfadaccountitems==null}">
                                            <tr align="center" id="adaccountitems1">
                                                <td>
                                                    <input id="userno1" onblur="getUserNameByEmpno2(this,1);" data-options="required:true" name="wfadaccountitems[1].userno" class="easyui-validatebox" style="width:80px;" value=""/>
                                                </td>
                                                <td>
                                                    <input id="username1" name="wfadaccountitems[1].username" class="easyui-validatebox inputCss" style="width:80px;" readonly value=""/>
                                                </td>
                                                <td>
                                                    <input id="adfactoryid1" name="wfadaccountitems[1].adfactoryid" data-options="panelHeight:'auto',valueField:'value', textField:'label',validType:'comboxValidate[\'adfactoryid1\',\'请選擇所在廠區\']',onBeforeLoad:function(){loadAdfactoryid(1);},onSelect:function(){adOnchangeFactory('adfactoryid1',1);}" style="width:80px;"
                                                           class="easyui-combobox" editable="false" value=""/>
                                                </td>
                                                <td>
                                                    <input id="wyname1" name="wfadaccountitems[1].wyname" data-options="validType:'comboxValidate[\'wyname1\',\'请選擇網域名稱\']'" style="width:120px;" panelHeight="auto" class="easyui-combobox" editable="false" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyaction1" name="wfadaccountitems[1].applyaction" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadApplyaction(1);},onSelect:function(){adOnchangeAction(1);}" style="width:80px;"
                                                           class="easyui-combobox" editable="false" value=""/>
                                                </td>
                                                <td>
                                                    <input id="account1" name="wfadaccountitems[1].account" class="easyui-validatebox" style="width:100px;"  value=""/>
                                                </td>
                                                <td>
                                                    <input id="pcname1" name="wfadaccountitems[1].pcname" class="easyui-validatebox" style="width:100px;" data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyip1" name="wfadaccountitems[1].applyip" class="easyui-validatebox" onblur="isNpiCheck(this,1);"
                                                           data-options="required:true,validType:'ip[\'applyip1\']'" style="width:90px;" value=""/>
                                                </td>
                                                <td>
                                                    <input id="isnpi1" name="wfadaccountitems[1].isnpi" class="easyui-validatebox inputCss" readonly
                                                           data-options="width: 60" value=""/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="addeltr(1);return false;"/>
                                                    <input id="shunxu1" type="hidden" name="wfadaccountitems[1].shunxu" value="1"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr0">
                                            <td colspan="11" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="adItemAdd" style="width:150px;float:left;" value="添加一筆申請信息"/>
                                            </td>
                                        </tr>

                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">需求說明&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1">
                             <textarea id="describtion" name="wfadaccountprocess.describtion" oninput="return LessThanAuto(this,'txtNum');"
                                       onchange="return LessThanAuto(this,'txtNum');" onpropertychange="return LessThanAuto(this,'txtNum');"
                                       maxlength="300" class="easyui-validatebox" style="width:99%;height:80px;" data-options="required:true"
                                       rows="5" cols="6">${wfadaccountprocessEntity.describtion}</textarea><span id="txtNum"></span>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td colspan="9" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="wfadaccountprocess.attachids" value="${wfadaccountprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="left">
                            <td colspan="10">
                                如需要批量申請AD域帳號請填寫<a href="${ctx}/wfadaccountprocess/downLoad/adaccountFile">AD域帳號批量申請表</a>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','AD域賬號申請單業務表');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl3Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno3_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(23,'yl3Table','ylno3','ylname3',$('#applyfactoryid').combobox('getValue'),'wfadaccountprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno3" name="wfadaccountprocess.ylno3"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno3']}"
                                                               readonly
                                                               value="${wfadaccountprocessEntity.ylno3 }"/><c:if
                                                            test="${requiredMap['ylno3'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname3" name="wfadaccountprocess.ylname3"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno3']}"
                                                                value="${wfadaccountprocessEntity.ylname3 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="wfadaccountprocess.bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfadaccountprocessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="wfadaccountprocess.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfadaccountprocessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#dealdeptno').val(),'cchargeno','cchargename',$('#applynofactoryid').val(),'wfadaccountprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wfadaccountprocess.cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfadaccountprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wfadaccountprocess.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfadaccountprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zxywkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zxywkchargeno_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'zxywkchargeTable','zxywkchargeno','zxywkchargename',$('#applyfactoryid').combobox('getValue'),'wfadaccountprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxywkchargeno"
                                                               name="wfadaccountprocess.zxywkchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxywkchargeno']}"
                                                               readonly
                                                               value="${wfadaccountprocessEntity.zxywkchargeno }"/><c:if
                                                            test="${requiredMap['zxywkchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxywkchargename"
                                                                name="wfadaccountprocess.zxywkchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxywkchargeno']}"
                                                                value="${wfadaccountprocessEntity.zxywkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno2_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(176,'yl2Table','ylno2','ylname2',$('#applyfactoryid').combobox('getValue'),'wfadaccountprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="wfadaccountprocess.ylno2"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}"
                                                               readonly
                                                               value="${wfadaccountprocessEntity.ylno2 }"/><c:if
                                                            test="${requiredMap['ylno2'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname2" name="wfadaccountprocess.ylname2"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}"
                                                                value="${wfadaccountprocessEntity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfadaccountprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="canelTask('${wfadaccountprocessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="areaId" name="areaId" value="" />
    <input type="hidden" id="buildingId" name="buildingId" value="" />
    <input id="disOrEnabled" type="hidden" value=""/>
    <input type="hidden" id="onlyKchargeSignle" value="1" />
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
    <div id="dlg"></div>
</form>
</div>
<script src='${ctx}/static/js/information/wfadaccountprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>