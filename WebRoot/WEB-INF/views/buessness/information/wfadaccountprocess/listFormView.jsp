<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>AD域賬號申請</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfadaccountprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfadaccountprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfadaccountprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">AD域賬號申請</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfadaccountprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfadaccountprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfadaccountprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfadaccountprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfadaccountprocessEntity.makerno}/${wfadaccountprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">承辦人工號</td>
                            <td width="10%" class="td_style2">${wfadaccountprocessEntity.dealno}</td>
                            <td width="8%">承辦人</td>
                            <td width="10%" class="td_style2">${wfadaccountprocessEntity.dealname }</td>
                            <td width="10%">單位代碼</td>
                            <td width="10%" class="td_style2">${wfadaccountprocessEntity.dealdeptno }</td>
                            <td width="7%">費用代碼</td>
                            <td width="10%" class="td_style2">${wfadaccountprocessEntity.applycostno }</td>
                            <td width="10%">所在廠區</td>
                            <td width="15%" class="td_style1">
                                <input id="applyfactoryid" name="wfadaccountprocess.applyfactoryid"
                                       class="easyui-combobox" disabled
                                       panelHeight="auto" value="${wfadaccountprocessEntity.applyfactoryid }"
                                       data-options="width: 120"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style2">${wfadaccountprocessEntity.applyleveltype }</td>
                            <td>管理職</td>
                            <td class="td_style2">${wfadaccountprocessEntity.applymanager }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfadaccountprocessEntity.dealemail }</td>
                            <td>使用區域</td>
                            <td class="td_style1">
                                <input id="applyarea" name="wfadaccountprocess.applyarea" class="easyui-combobox"
                                       data-options="width: 70" disabled value="${wfadaccountprocessEntity.applyarea }" />&nbsp;/
                                <input id="applybuilding" name="wfadaccountprocess.applybuilding"
                                       class="easyui-combobox" data-options="width: 70" disabled
                                       value="${wfadaccountprocessEntity.applybuilding }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機</td>
                            <td class="td_style2">${wfadaccountprocessEntity.dealtel}</td>
                            <td>單位</td>
                            <td colspan="7" class="td_style2">${wfadaccountprocessEntity.dealdeptname}</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請明細</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="pcprivilegeItemTable" width="100%">
                                        <tr align="center">
                                            <td width="6%">申請人工號</td>
                                            <td width="6%">申請人</td>
                                            <td width="6%">所在廠區</td>
                                            <td width="8%">網域名稱</td>
                                            <td width="6%">申請動作</td>
                                            <td width="8%">原使用賬號</td>
                                            <td width="8%">電腦名稱</td>
                                            <td width="8%">IP地址</td>
                                            <td width="8%">是否NPI</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wfadaccountitems!=null&&wfadaccountitems.size()>0}">
                                            <c:forEach items="${wfadaccountitems}" var="adaccountitems" varStatus="status">
                                                <tr align="center" id="adaccountitems${status.index+1}">
                                                    <td>${adaccountitems.userno}</td>
                                                    <td>${adaccountitems.username}</td>
                                                    <td>
                                                        <input id="adfactoryid${status.index+1}" name="wfadaccountitems[${status.index+1}].adfactoryid" data-options="panelHeight:'auto',valueField:'value', textField:'label',validType:'comboxValidate[\'adfactoryid${status.index+1}\',\'请選擇所在廠區\']',onBeforeLoad:function(){loadAdfactoryid(${status.index+1});},onSelect:function(){adOnchangeFactory('adfactoryid${status.index+1}',${status.index+1});}" style="width:80px;"
                                                               class="easyui-combobox" editable="false" value="${adaccountitems.adfactoryid}" disabled/>
                                                    </td>
                                                    <td>
                                                            ${adaccountitems.wyname}
                                                    </td>
                                                    <td>
                                                        <input id="applyaction${status.index+1}" name="wfadaccountitems[${status.index+1}].applyaction" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadApplyaction(${status.index+1});},onSelect:function(){adOnchangeAction(${status.index+1});}" style="width:80px;"
                                                               class="easyui-combobox" editable="false" disabled value="${adaccountitems.applyaction}"/>
                                                    </td>
                                                    <td>${adaccountitems.account}</td>
                                                    <td>${adaccountitems.pcname}</td>
                                                    <td>${adaccountitems.applyip}</td>
                                                    <td>${adaccountitems.isnpi}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">需求說明</td>
                            <td colspan="9" class="td_style2">
                             <textarea id="describtion" name="wfadaccountprocess.describtion" class="easyui-validatebox" style="width:99%;height:80px;"
                                       rows="5" cols="6">${wfadaccountprocessEntity.describtion}</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td colspan="9" class="td_style1">
                                <input type="hidden" id="attachids" name="attachids" value="${wfadaccountprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','AD域賬號申請單業務表');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfadaccountprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfadaccountprocessEntity.workstatus!=null&&wfadaccountprocessEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/information/wfadaccountprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>