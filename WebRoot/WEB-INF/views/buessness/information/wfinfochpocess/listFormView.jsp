<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>資料異動申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/wfinfochpocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfInfochpocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfInfochpocessEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">系統資料異動申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfInfochpocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfInfochpocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfInfochpocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfInfochpocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfInfochpocessEntity.makerno}/${wfInfochpocessEntity.makername}</div>
        <div class="clear"></div>
        <table  class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="8%">工號</td>
                            <td width="10%" class="td_style2">
                                ${wfInfochpocessEntity.applyno}
                            </td>
                            <td width="8%">姓名</td>
                            <td width="10%" class="td_style2">
                                ${wfInfochpocessEntity.applyname }
                            </td>
                            <td width="10%">職責</td>
                            <td width="10%" class="td_style2">
                                ${wfInfochpocessEntity.duty }
                            </td>
                            <td width="8%">廠區</td>
                            <td width="12%" class="td_style2">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfInfochpocessEntity.dealfactoryid }" disabled
                                       data-options="width: 120,required:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">單位代碼</td>
                            <td width="10%" class="td_style2">
                                ${wfInfochpocessEntity.deptno}
                            </td>
                            <td>單位</td>
                            <td colspan="3" class="td_style2">
                                ${wfInfochpocessEntity.deptname }
                            </td>
                            <td>聯絡電話</td>
                            <td class="td_style2">
                                ${wfInfochpocessEntity.phone }
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">系統名稱</td>
                            <td colspan="3" class="td_style2">
                                <input id="systemname" name="systemname" type="text" value="${wfInfochpocessEntity.systemname}" class="easyui-combotree"
                                       data-options="width: 120" disabled/>
                            </td>
                            <td>需求廠區</td>
                            <td>
                                <input id="needfactory" name="needfactory" class="easyui-combobox"
                                       panelHeight="auto" value="${wfInfochpocessEntity.needfactory }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'needfactory\',\'请選擇需求廠區\']'"
                                       disabled/>
                            </td>
                            <td>緊急程度</td>
                            <td>
                                <%--<div class="rgcydegreeDiv"></div>
                                <input id="rgcydegree" name="rgcydegree"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfInfochpocessEntity.rgcydegree }" disabled/>--%>
                                <c:if test="${wfInfochpocessEntity.urgencyLevel=='0'}">
                                    特急
                                </c:if>
                                <c:if test="${wfInfochpocessEntity.urgencyLevel=='1'}">
                                    緊急
                                </c:if>
                                <c:if test="${wfInfochpocessEntity.urgencyLevel=='2'}">
                                    一般
                                </c:if>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">異動類別及原因</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">賬號異動</td>
                            <td colspan="7" class="td_style2">
                                <div class="accreasonDiv" style="width: 250px;float: left;"></div>
                                <input id="accreason" name="accreason"
                                       type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${wfInfochpocessEntity.accreason }"/>
                                <div style="float: left;">
                                    <input id="accreasonother" name="accreasonother" class="easyui-validatebox" data-options="width: 400" readonly="readonly"
                                           value="${wfInfochpocessEntity.accreasonother }"/>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">工站異動</td>
                            <td colspan="7" class="td_style2">
                                <div class="workreasonDiv"  style="width: 360px;float: left;"></div>
                                <input id="workreason" name="workreason"
                                       type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${wfInfochpocessEntity.workreason }"/>
                                <div style="float: left;">
                                    <input id="workreasonother" name="workreasonother" class="easyui-validatebox" data-options="width: 400" readonly="readonly"
                                           value="${wfInfochpocessEntity.workreasonother }"/>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">數據異動</td>
                            <td colspan="7" class="td_style2">
                                <div class="inforeasonDiv" style="width: 360px;float: left;"></div>
                                <input id="inforeason" name="inforeason"
                                       type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${wfInfochpocessEntity.inforeason }"/>
                                <div style="float: left;">
                                    <input id="inforeasonother" name="inforeasonother" class="easyui-validatebox" data-options="width: 400" readonly="readonly"
                                           value="${wfInfochpocessEntity.inforeasonother }"/>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">其它類</td>
                            <td colspan="7" class="td_style2">${wfInfochpocessEntity.otherreason }
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">需求說明</td>
                            <td colspan="7" class="td_style1">${wfInfochpocessEntity.needexplain}
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" colspan="7" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${wfInfochpocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style2">
                                備註：<br>
                                1.如涉及其他部門單位需會簽有關部門<br>
                                2.如該廠部有對應的推動幹事，則所有系統數據異動申請單必須會簽至廠部推動幹事<br>
                                3.表單流程：申請單位人員承辦->申請單位課級主管審核->申請單位廠部級別主管核准->會簽系統應用部級主管->會簽系統開發部級->申請單位驗收
                            </td>
                        </tr>
                    </table>
                </td>
           </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','資料異動申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfInfochpocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                               <c:if test="${wfInfochpocessEntity.workstatus!=null&&wfInfochpocessEntity.workstatus==3}">
                                  <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                      style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                               </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>

    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value="disabled"/>
	</form>
  </div>
<div id="dlg"></div>
<script src='${ctx}/static/js/information/wfinfochpocess.js?random=<%= Math.random()%>'></script>
</body>
</html>