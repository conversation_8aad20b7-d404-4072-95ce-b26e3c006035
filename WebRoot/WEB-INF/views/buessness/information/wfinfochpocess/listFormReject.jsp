<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>資料異動申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/wfinfochpocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfInfochpocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfInfochpocessEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfInfochpocessEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfInfochpocessEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfInfochpocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${wfInfochpocessEntity.makerfactoryid }"/>
    <div class="commonW">
    <div class="headTitle">系統資料異動申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfInfochpocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfInfochpocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfInfochpocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfInfochpocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfInfochpocessEntity.makerno}">
			  <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfInfochpocessEntity.makerno}">
                <div class="position_R margin_R">填單人：${wfInfochpocessEntity.makerno}/${wfInfochpocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="8%">工號&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox" data-options="width: 80,required:true"
                                       value="<c:if test="${wfInfochpocessEntity.applyno!=null || wfInfochpocessEntity.applyno!=''}">${wfInfochpocessEntity.applyno}</c:if><c:if test="${wfInfochpocessEntity.applyno==null||wfInfochpocessEntity.applyno==''}">${user.loginName}</c:if>" onchange="queryUserInfo('apply');"/>
                            </td>
                            <td width="8%">姓名</td>
                            <td width="10%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfInfochpocessEntity.applyname }"/>
                            </td>
                            <td width="10%">職責&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="duty" name="duty"
                                       class="easyui-validatebox" data-options="width: 90,required:true"
                                       value="${wfInfochpocessEntity.duty }"/>
                            </td>
                            <td width="8%">廠區</td>
                            <td width="12%" class="td_style1">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfInfochpocessEntity.dealfactoryid }" disabled
                                       data-options="width: 120,required:true"/>
                                <input id="dealfactoryname" name="dealfactoryname" type="hidden"
                                       value="${wfInfochpocessEntity.dealfactoryname}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="dealdeptno" name="deptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfInfochpocessEntity.deptno}"/>
                            </td>
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="deptname" name="deptname" class="easyui-validatebox" data-options="width: 410,required:true"
                                       value="${wfInfochpocessEntity.deptname }"/>
                            </td>
                            <td>聯絡電話&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="phone" name="phone" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfInfochpocessEntity.phone }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">系統名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="systemname" name="systemname" type="text"
                                       value="${wfInfochpocessEntity.systemname}" class="easyui-combotree"
                                       data-options="required:'required',validType:'combotreeValidate[\'systemname\',\'请选择系統\']'"/>
                                <input id="systemnamename" name="systemnamename" type="hidden"
                                       value="${wfInfochpocessEntity.systemnamename}"/>
                            </td>
                            <td>需求廠區</td>
                            <td>
                                <input id="needfactory" name="needfactory" class="easyui-combobox"
                                       panelHeight="auto" value="${wfInfochpocessEntity.needfactory }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'needfactory\',\'请選擇需求廠區\']'"/>
                                <input id="needfactoryname" name="needfactoryname" type="hidden"
                                       value="${wfInfochpocessEntity.needfactoryname}"/>
                            </td>
                            <td>緊急程度</td>
                            <td>
                                <%--<div class="rgcydegreeDiv"></div>
                                <input id="rgcydegree" name="rgcydegree"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfInfochpocessEntity.rgcydegree }"/>--%>
                                <label><input type="radio" name="urgencyLevelRadio"
                                              <c:if test="${wfInfochpocessEntity.urgencyLevel=='0'}">checked</c:if>
                                              value="0"/>特急</label>
                                <label><input type="radio" name="urgencyLevelRadio"
                                              <c:if test="${wfInfochpocessEntity.urgencyLevel=='1'}">checked</c:if>
                                              value="1"/>緊急</label>
                                <label><input type="radio" name="urgencyLevelRadio"
                                              <c:if test="${wfInfochpocessEntity.urgencyLevel=='2'}">checked</c:if>
                                              value="2"/>一般</label>
                                <input type="hidden" name="urgencyLevel" value="${wfInfochpocessEntity.urgencyLevel}"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">異動類別及原因</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">賬號異動</td>
                            <td colspan="7" class="td_style2">
                                <div class="accreasonDiv" style="width: 250px;float: left;"></div>
                                <input id="accreason" name="accreason"
                                       type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${wfInfochpocessEntity.accreason }"/>
                                <input id="accreasonname" name="accreasonname" type="hidden"
                                       value="${wfInfochpocessEntity.accreasonname}"/>
                                <div style="float: left;">
                                    <input id="accreasonother" name="accreasonother" class="easyui-validatebox"
                                           data-options="width: 400" readonly="readonly"
                                           value="${wfInfochpocessEntity.accreasonother }"/>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">工站異動</td>
                            <td colspan="7" class="td_style2">
                                <div class="workreasonDiv" style="width: 360px;float: left;"></div>
                                <input id="workreason" name="workreason"
                                       type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${wfInfochpocessEntity.workreason }"/>
                                <input id="workreasonname" name="workreasonname" type="hidden"
                                       value="${wfInfochpocessEntity.workreasonname}"/>
                                <div style="float: left;">
                                    <input id="workreasonother" name="workreasonother" class="easyui-validatebox"
                                           data-options="width: 400" readonly="readonly"
                                           value="${wfInfochpocessEntity.workreasonother }"/>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">數據異動</td>
                            <td colspan="7" class="td_style2">
                                <div class="inforeasonDiv" style="width: 360px;float: left;"></div>
                                <input id="inforeason" name="inforeason"
                                       type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${wfInfochpocessEntity.inforeason }"/>
                                <input id="inforeasonname" name="inforeasonname" type="hidden"
                                       value="${wfInfochpocessEntity.inforeasonname}"/>
                                <div style="float: left;">
                                    <input id="inforeasonother" name="inforeasonother" class="easyui-validatebox"
                                           data-options="width: 400" readonly="readonly"
                                           value="${wfInfochpocessEntity.inforeasonother }"/>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">其它類</td>
                            <td colspan="7" class="td_style2">
                                <input id="otherreason" name="otherreason" class="easyui-validatebox"
                                       data-options="width: 600"
                                       value="${wfInfochpocessEntity.otherreason }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">需求說明</td>
                            <td colspan="7" class="td_style1">
                                <textarea id="needexplain" name="needexplain" oninput="return LessThanAuto(this,'txtNum');"
                                          onchange="return LessThanAuto(this,'txtNum');" onpropertychange="return LessThanAuto(this,'txtNum');"
                                          maxlength="1000" class="easyui-validatebox" style="width:99%;height:80px;" data-options="required:true"
                                          rows="5" cols="6">${wfInfochpocessEntity.needexplain}</textarea><span id="txtNum"></span>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">附件</td>
                            <td colspan="7" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="attachids" value="${wfInfochpocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style2">
                                備註：<br>
                                1.如涉及其他部門單位需會簽有關部門<br>
                                2.如該廠部有對應的推動幹事，則所有系統數據異動申請單必須會簽至廠部推動幹事<br>
                                3.表單流程：申請單位人員承辦->申請單位課級主管審核->申請單位廠部級別主管核准->會簽系統應用部級主管->會簽系統開發部級->申請單位驗收
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">

                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_ziliaoyidongshenqing','資料異動申請單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl1Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno1_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole8(197,'ylno1','ylname1','ylno2','ylname2','','',$('#dealfactoryid').combobox('getValue'),$('#systemname').combotree('getValue'))">
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno1" name="ylno1"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno1']}"
                                                               readonly
                                                               value="${wfInfochpocessEntity.ylno1 }"/><c:if
                                                            test="${requiredMap['ylno1'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname1" name="ylname1"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno1']}"
                                                                value="${wfInfochpocessEntity.ylname1 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealfactoryid').combobox('getValue'))">

                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wfInfochpocessEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfInfochpocessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfInfochpocessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfInfochpocessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfInfochpocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfInfochpocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="hchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['hchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <a href="javascript:addHq('hcharge');" >添加一位</a>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hchargeno" name="hchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['hchargeno']}"
                                                               onblur="getUserNameByEmpno(this,'hcharge');"
                                                               value="${wfInfochpocessEntity.hchargeno }"/><c:if test="${requiredMap['hchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hchargename" name="hchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hchargeno']}"
                                                                value="${wfInfochpocessEntity.hchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl3Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno3_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'yl3Table','ylno3','ylname3',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno3" name="ylno3"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno3']}"
                                                               readonly
                                                               value="${wfInfochpocessEntity.ylno3 }"/><c:if
                                                            test="${requiredMap['ylno3'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname3" name="ylname3"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno3']}"
                                                                value="${wfInfochpocessEntity.ylname3 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl4Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno4_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(25,'yl4Table','ylno4','ylname4',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno4" name="ylno4"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno4']}"
                                                               readonly
                                                               value="${wfInfochpocessEntity.ylno4 }"/><c:if
                                                            test="${requiredMap['ylno4'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname4" name="ylname4"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno4']}"
                                                                value="${wfInfochpocessEntity.ylname4 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl5Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno5_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole9(64,'yl5Table','ylno5','ylname5',$('#dealfactoryid').combobox('getValue'),$('#systemname').combotree('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno5" name="ylno5"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno5']}"
                                                               readonly
                                                               value="${wfInfochpocessEntity.ylno5 }"/><c:if
                                                            test="${requiredMap['ylno5'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname5" name="ylname5"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno5']}"
                                                                value="${wfInfochpocessEntity.ylname5 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl6Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno6_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole9(65,'yl6Table','ylno6','ylname6',$('#dealfactoryid').combobox('getValue'),$('#systemname').combotree('getValue'),null)">
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno6" name="ylno6"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno6']}"
                                                               readonly
                                                               value="${wfInfochpocessEntity.ylno6 }"/><c:if
                                                            test="${requiredMap['ylno6'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname6" name="ylname6"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno6']}"
                                                                value="${wfInfochpocessEntity.ylname6 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl7Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno7_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole9(66,'yl7Table','ylno7','ylname7',$('#dealfactoryid').combobox('getValue'),$('#systemname').combotree('getValue'),null)">

                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno7" name="ylno7"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno7']}"
                                                               readonly
                                                               value="${wfInfochpocessEntity.ylno7 }"/><c:if
                                                            test="${requiredMap['ylno7'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname7" name="ylname7"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno7']}"
                                                                value="${wfInfochpocessEntity.ylname7 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl8Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno8_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole9(67,'yl8Table','ylno8','ylname8',$('#dealfactoryid').combobox('getValue'),$('#systemname').combotree('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno8" name="ylno8"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno8']}"
                                                               readonly
                                                               value="${wfInfochpocessEntity.ylno8 }"/><c:if
                                                            test="${requiredMap['ylno8'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname8" name="ylname8"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno8']}"
                                                                value="${wfInfochpocessEntity.ylname8 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">${requiredMap['ylno2_name']}
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="ylno2"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}"
                                                               readonly
                                                               value="${wfInfochpocessEntity.ylno2 }"/><c:if
                                                            test="${requiredMap['ylno2'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname2" name="ylname2"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}"
                                                                value="${wfInfochpocessEntity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl9Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">${requiredMap['ylno9_name']}</td>
                                                               <%-- <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'ylno9','ylname9',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>--%>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno9" name="ylno9"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno9']}"
                                                               readonly
                                                               value="${wfInfochpocessEntity.ylno9 }"/><c:if
                                                            test="${requiredMap['ylno9'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname9" name="ylname9"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno9']}"
                                                                value="${wfInfochpocessEntity.ylname9 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfInfochpocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;"
                                   onclick="canelTask('${wfInfochpocessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	<input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
    <div id="dlg"></div>
	</form>
  </div>
<script src='${ctx}/static/js/information/wfinfochpocess.js?random=<%= Math.random()%>'></script>
<script type="text/javascript">
    if ("${wfInfochpocessEntity.hchargeno}" != "") {
        var nostr = "${wfInfochpocessEntity.hchargeno}";
        var namestr = "${wfInfochpocessEntity.hchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hchargeTable tr:eq(" + (i + 2) + ")").find("#hchargeno").val(notr[i]);
            $("#hchargeTable tr:eq(" + (i + 2) + ")").find("#hchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hchargeno' name='hchargeno' style='width: 80px' value='' onblur='getUserNameByEmpno(this,\"hcharge\");'/>/<input id='hchargename' name='hchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
</script>
</body>
</html>
