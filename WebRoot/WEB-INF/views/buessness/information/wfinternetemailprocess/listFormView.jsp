<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>Internet E-Mail賬號申請</title>
    <script type="text/javascript">var ctx = "${pageContext.request.contextPath}";</script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
        .internalmailDiv{
            float: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfinternetemailprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfinternetemailprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfinternetemailprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">Internet E-Mail賬號申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfinternetemailprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfinternetemailprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfinternetemailprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfinternetemailprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfinternetemailprocessEntity.makerno}/${wfinternetemailprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">承辦人工號</td>
                            <td width="4%" class="td_style2">${wfinternetemailprocessEntity.dealno}</td>
                            <td width="4%">承辦人</td>
                            <td width="6%" class="td_style2">${wfinternetemailprocessEntity.dealname }</td>
                            <td width="5%">單位代碼</td>
                            <td width="5%" class="td_style2">${wfinternetemailprocessEntity.dealdeptno }</td>
                            <td width="5%">廠區</td>
                            <td width="5%" class="td_style1">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfinternetemailprocessEntity.dealfactoryid }" disabled
                                       data-options="width: 120"/>
                            </td>
                            <td width="3%">聯繫分機</td>
                            <td width="7%" class="td_style2">${wfinternetemailprocessEntity.dealtel }</td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="5" class="td_style2">${wfinternetemailprocessEntity.dealdeptname }
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfinternetemailprocessEntity.dealemail }</td>
                        </tr>
                        <tr align="center">
                            <td>使用區域</td>
                            <td class="td_style1" colspan="3">
                                <input id="applyarea" name="applyarea" class="easyui-combobox"
                                       data-options="width: 100,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wfinternetemailprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="applybuilding" class="easyui-combobox"
                                       data-options="width: 100,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wfinternetemailprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                            <td>安保區域</td>
                            <td class="td_style2" colspan="2">
                                <div class="securityareaDiv"></div>
                                <input id="securityarea" name="securityarea"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfinternetemailprocessEntity.securityarea }"/>
                            </td>
                            <td>申請方式</td>
                            <td colspan="2" class="td_style2">
                                <div class="dealapplytypeDiv"></div>
                                <input id="dealapplytype1"
                                       name="dealapplytype1" type="hidden"
                                       class="easyui-validatebox" data-options="width: 100"
                                       value="${wfinternetemailprocessEntity.dealapplytype}"/>
                            </td>
                        </tr>
                    </table>
                    <table class="formList">
                        <tr><td colspan="10" class="td_style1">申請人基本信息</td></tr>
                        <tbody id="info_Body">
                        <c:if test="${wfinternetemailprocessEntity.itemsEntitys!=null&&wfinternetemailprocessEntity.itemsEntitys.size()>0}">
                            <c:forEach items="${wfinternetemailprocessEntity.itemsEntitys}" var="itemsEntity" varStatus="status">
                                <tr align="center" bgcolor="#faebd7"><td colspan="10">第${status.index+1}位申請人信息</td></tr>
                                <tr align="center" id="itemsEntitys${status.index}">
                                    <td>申請人工號</td>
                                    <td class="td_style2">${itemsEntity.applyno}</td>
                                    <td>申請人</td>
                                    <td class="td_style2">${itemsEntity.applyname}</td>
                                    <td>單位代碼</td>
                                    <td class="td_style2">${itemsEntity.applydeptno}</td>
                                    <td>費用代碼</td>
                                    <td class="td_style2">${itemsEntity.applycostno}</td>
                                    <td>所在廠區</td>
                                    <td class="td_style1">
                                        <input id="applyfactoryid${status.index}" name="itemsEntitys[${status.index}].applyfactoryid"
                                               data-options="panelHeight:300,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'applyfactoryid${status.index}\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(${status.index});}"
                                               style="width:100px;" class="easyui-combobox" value="${itemsEntity.applyfactoryid}"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>資位</td>
                                    <td class="td_style2">${itemsEntity.applyleveltype }</td>
                                    <td>管理職</td>
                                    <td class="td_style2">${itemsEntity.applymanager }</td>
                                    <td>聯繫郵箱</td>
                                    <td colspan="3" class="td_style2">${itemsEntity.applyemail}</td>
                                    <td>聯繫分機</td>
                                    <td class="td_style2">${itemsEntity.applyphone}</td>
                                </tr>
                                <tr align="center">
                                    <td>單位&nbsp;</td>
                                    <td colspan="4" class="td_style2">${itemsEntity.applydeptname}</td>
                                    <td>法人代碼</td>
                                    <td class="td_style2">${itemsEntity.applycompanycode}</td>
                                    <td>法人名稱</td>
                                    <td class="td_style2" colspan="2">${itemsEntity.applycompanyname}</td>
                                </tr>
                                <tr align="center">
                                    <td>Internet E-Mail賬號</td>
                                    <td colspan="5" class="td_style2">
                                            ${itemsEntity.intermail }
                                            ${itemsEntity.mailsuffix}
                                    </td>
                                    <td colspan="2"><a href="${ctx}/wfinternetemailprocess/downLoadNamingRules">Email賬號命名規則</a></td>
                                    <td>申請類型</td>
                                    <td class="td_style2">${itemsEntity.intermailtype}</td>
                                </tr>
                                <tr align="center">
                                    <td>需求類型</td>
                                    <td colspan="3" class="td_style2">${itemsEntity.demandtype}</td>
                                    <td>原Email賬號名</td>
                                    <td colspan="5" class="td_style2">
                                            ${itemsEntity.oldintermail}${itemsEntity.oldmailsuffix}
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>開始時間</td>
                                    <td colspan="4" class="td_style2">
                                        <input id="starttime${status.index}" name="itemsEntitys[${status.index}].starttime" class="Wdate" data-options="width:150,required:true"
                                               style="width:150px"  value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${itemsEntity.starttime}"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                                    </td>
                                    <td>結束時間</td>
                                    <td colspan="4" class="td_style2">
                                        <input id="endtime${status.index}" name="itemsEntitys[${status.index}].endtime" class="Wdate" data-options="width:150,required:true"
                                               style="width:150px" value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${itemsEntity.endtime}"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'starttime${status.index}\')}',maxDate:'#F{$dp.$D(\'starttime${status.index}\',{M:6})}'})" />
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>內部郵箱</td>
                                    <td colspan="9" class="td_style2">
                                        <div style="float: left;margin-right: 5px">
                                                ${itemsEntity.internalmail}
                                        </div>
                                        <div style="float: left;">
                                                ${itemsEntity.innerchecknotesname}
                                        </div>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>需求說明</td>
                                    <td colspan="9" class="td_style2">
                                        <textarea id="describtion${status.index}" name="itemsEntitys[${status.index}].describtion"
                                                  class="easyui-validatebox"
                                                  oninput="return LessThan(this);"
                                                  onchange="return LessThan(this);"
                                                  onpropertychange="return LessThan(this);"
                                                  maxlength="300" style="width:99%;height:80px;" rows="5" cols="6"
                                                  data-options="required:true,validType:'length[0,300]'">${itemsEntity.describtion}</textarea>
                                    </td>
                                </tr>
                            </c:forEach>
                        </c:if>
                        </tbody>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style2">
                                <c:if test="${wfinternetemailprocessEntity.attachids!=null}">
                                    <div id="dowloadUrl">
                                        <c:forEach items="${file}" varStatus="i" var="item">
                                            <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                                <div class="float_L"><a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a></div>
                                            </div>
                                        </c:forEach>
                                    </div>
                                </c:if>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>批註</td>
                            <td colspan="9" class="td_style2">
                                <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                          style="width:1000px;height:60px;"
                                          rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','Internet E-Mail賬號申請');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfinternetemailprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfinternetemailprocessEntity.workstatus!=null&&wfinternetemailprocessEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<div id="dlg"></div>
<input type="hidden" id="disOrEnabled" value="disabled"/>
<script src='${ctx}/static/js/information/wfinternetemailprocess.min.js?random=2025052802'></script>
</body>
</html>