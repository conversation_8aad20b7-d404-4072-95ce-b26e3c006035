<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>Internet E-Mail賬號申請</title>
    <script type="text/javascript">var ctx = "${pageContext.request.contextPath}";</script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
        .internalmailDiv{
            float: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfinternetemailprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfinternetemailprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfinternetemailprocessEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfinternetemailprocessEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfinternetemailprocessEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfinternetemailprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${wfinternetemailprocessEntity.makerfactoryid }"/>
    <input id="applynofactoryid" name="applynofactoryid" type="hidden" value="${wfinternetemailprocessEntity.applynofactoryid }"/>
    <div class="commonW">
        <div class="headTitle">Internet E-Mail賬號申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfinternetemailprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfinternetemailprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfinternetemailprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfinternetemailprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfinternetemailprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfinternetemailprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfinternetemailprocessEntity.makerno}/${wfinternetemailprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="4%" class="td_style1">
                                <input id="dealno" name="dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="<c:if test="${wfinternetemailprocessEntity.dealno!=null&&wfinternetemailprocessEntity.dealno!=''}">${wfinternetemailprocessEntity.dealno}</c:if>
                                       <c:if test="${wfinternetemailprocessEntity.dealno==null||wfinternetemailprocessEntity.dealno==''}">${user.loginName}</c:if>" onchange="queryUserInfo('deal');"/>
                            </td>
                            <td width="4%">承辦人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfinternetemailprocessEntity.dealname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfinternetemailprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="5%">廠區&nbsp;</td>
                            <td width="5%" class="td_style1">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfinternetemailprocessEntity.dealfactoryid }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'dealfactoryid\',\'请選擇承辦人廠區\']',onSelect:function(){onchangeFactory('dealfactoryid');}"/>
                            </td>
                            <td width="3%">聯繫分機&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="dealtel" name="dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfinternetemailprocessEntity.dealtel }" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="dealdeptname" name="dealdeptname" class="easyui-validatebox" data-options="width: 410,required:true"
                                       value="${wfinternetemailprocessEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="dealemail" class="easyui-validatebox"
                                       value="${wfinternetemailprocessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>使用區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style1" colspan="3">
                                <input id="applyarea" name="applyarea" class="easyui-combobox"
                                       data-options="width: 150,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wfinternetemailprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="applybuilding" class="easyui-combobox"
                                       data-options="width: 150,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wfinternetemailprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                            <td>安保區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style2" colspan="2">
                                <div class="securityareaDiv"></div>
                                <input id="securityarea" name="securityarea"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfinternetemailprocessEntity.securityarea }"/>
                            </td>
                            <td>申請方式&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style2">
                                <div class="dealapplytypeDiv"></div>
                                <input id="dealapplytype1"
                                       name="dealapplytype1" type="hidden"
                                       class="easyui-validatebox" data-options="width: 100"
                                       value="${wfinternetemailprocessEntity.dealapplytype}"/>
                            </td>
                        </tr>
                    </table>
                    <table class="formList">
                        <tr><td colspan="10" class="td_style1">申請人基本信息</td></tr>
                        <tbody id="info_Body">
                        <c:if test="${wfinternetemailprocessEntity.itemsEntitys!=null&&wfinternetemailprocessEntity.itemsEntitys.size()>0}">
                            <c:forEach items="${wfinternetemailprocessEntity.itemsEntitys}" var="itemsEntity" varStatus="status">
                                <tr align="center" bgcolor="#faebd7"><td colspan="10">第${status.index+1}位申請人信息</td></tr>
                                <tr align="center" id="itemsEntitys${status.index}">
                                    <td>申請人工號&nbsp;<font color="red">*</font></td>
                                    <td class="td_style1">
                                        <input id="applyno${status.index}" name="itemsEntitys[${status.index}].applyno" class="easyui-validatebox"
                                               data-options="width: 80,required:true" value="${itemsEntity.applyno}"
                                               onblur="getUserNameByEmpno('apply',${status.index});"/>
                                    </td>
                                    <td>申請人</td>
                                    <td class="td_style1">
                                        <input id="applyname${status.index}" name="itemsEntitys[${status.index}].applyname"
                                               class="easyui-validatebox inputCss" value="${itemsEntity.applyname}"
                                               data-options="width:80" readonly/>
                                    </td>
                                    <td>單位代碼</td>
                                    <td class="td_style1">
                                        <input id="applydeptno${status.index}" name="itemsEntitys[${status.index}].applydeptno"
                                               class="easyui-validatebox inputCss" data-options="width: 90"
                                               value="${itemsEntity.applydeptno}" readonly/>
                                    </td>
                                    <td>費用代碼&nbsp;<font color="red">*</font></td>
                                    <td class="td_style1">
                                        <input id="applycostno${status.index}" name="itemsEntitys[${status.index}].applycostno"
                                               class="easyui-validatebox" data-options="width: 90" value="${itemsEntity.applycostno}"/>
                                    </td>
                                    <td>所在廠區&nbsp;<font color="red">*</font></td>
                                    <td class="td_style1">
                                        <input id="applyfactoryid${status.index}" name="itemsEntitys[${status.index}].applyfactoryid"
                                               data-options="panelHeight:300,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'applyfactoryid${status.index}\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(${status.index});}"
                                               style="width:100px;" class="easyui-combobox" value="${itemsEntity.applyfactoryid}"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>資位</td>
                                    <td class="td_style1">
                                        <input id="applyleveltype${status.index}" name="itemsEntitys[${status.index}].applyleveltype" class="easyui-validatebox inputCss" data-options="width: 80"
                                               value="${itemsEntity.applyleveltype }" readonly/>
                                    </td>
                                    <td>管理職</td>
                                    <td class="td_style1">
                                        <input id="applymanager${status.index}" name="itemsEntitys[${status.index}].applymanager" class="easyui-validatebox inputCss" data-options="width: 80"
                                               value="${itemsEntity.applymanager }" readonly/>
                                    </td>
                                    <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                                    <td colspan="3" class="td_style1">
                                        <input id="applyemail${status.index}" name="itemsEntitys[${status.index}].applyemail" class="easyui-validatebox"
                                               value="${itemsEntity.applyemail }" style="width:300px;"
                                               data-options="required:true,validType:'email[\'applyemail${status.index}\',\'郵箱的格式不正確\']'"/>
                                    </td>
                                    <td>聯繫分機&nbsp;<font color="red">*</font></td>
                                    <td class="td_style1">
                                        <input id="applyphone${status.index}" name="itemsEntitys[${status.index}].applyphone" class="easyui-validatebox"
                                               style="width:90px;"
                                               value="${itemsEntity.applyphone}" data-options="required:true,prompt:'579+66666'"
                                               onblur="valdApplyTel(this)"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>單位&nbsp;<font color="red">*</font></td>
                                    <td colspan="4" class="td_style1">
                                        <input id="applydeptname${status.index}" name="itemsEntitys[${status.index}].applydeptname" class="easyui-validatebox" data-options="width: 410,required:true"
                                               value="${itemsEntity.applydeptname}"/>
                                    </td>
                                    <td>法人代碼</td>
                                    <td class="td_style1">
                                        <input id="applycompanycode${status.index}" name="itemsEntitys[${status.index}].applycompanycode"
                                               class="easyui-validatebox"
                                               data-options="width:80,required:true" value="${itemsEntity.applycompanycode}"/>
                                    </td>
                                    <td>法人名稱</td>
                                    <td class="td_style1" colspan="2">
                                        <input id="applycompanyname${status.index}" name="itemsEntitys[${status.index}].applycompanyname"
                                               class="easyui-validatebox"
                                               data-options="width:300,required:true" value="${itemsEntity.applycompanyname}"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>Internet E-Mail賬號&nbsp;<font color="red">*</font></td>
                                    <td colspan="5" class="td_style2">
                                        <input id="intermail${status.index}" name="itemsEntitys[${status.index}].intermail" class="easyui-validatebox" data-options="width: 200,required:true"
                                               value="${itemsEntity.intermail }" onblur="checkEmail(this)"/>
                                        <input id="mailsuffix${status.index}" name="itemsEntitys[${status.index}].mailsuffix" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,validType:'comboxValidate[\'mailsuffix${status.index}\',\'请選擇Internet E-Mail賬號後綴\']',onBeforeLoad:function(){loadMailsuffix(${status.index});}" style="width:150px;"
                                               class="easyui-combobox"  value="${itemsEntity.mailsuffix}"/>
                                    </td>
                                    <td colspan="2"><a href="${ctx}/wfinternetemailprocess/downLoadNamingRules">Email賬號命名規則</a></td>
                                    <td>申請類型&nbsp;<font color="red">*</font></td>
                                    <td class="td_style2">
                                        <input id="intermailtype${status.index}" name="itemsEntitys[${status.index}].intermailtype" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,validType:'comboxValidate[\'intermailtype${status.index}\',\'请選擇申請類型\']',onBeforeLoad:function(){loadIntermailtype(${status.index});}" style="width:120px;"
                                               class="easyui-combobox"  value="${itemsEntity.intermailtype}"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>需求類型&nbsp;<font color="red">*</font></td>
                                    <td colspan="3" class="td_style2">
                                        <div class="demandtypeDiv${status.index}" id="demandtypeId${status.index}"></div>
                                        <input id="demandtype${status.index}${status.index}" name="demandtype${status.index}${status.index}" type="hidden" class="easyui-validatebox"
                                               value="${itemsEntity.demandtype}" data-options="width: 100"/>
                                    </td>
                                    <td>原Email賬號名</td>
                                    <td colspan="5" class="td_style2">
                                        <input id="oldintermail${status.index}" name="itemsEntitys[${status.index}].oldintermail" class="easyui-validatebox" data-options="width: 200"
                                               value="${itemsEntity.oldintermail }" onblur="checkEmail(this)"/>
                                        <input id="oldmailsuffix${status.index}" name="itemsEntitys[${status.index}].oldmailsuffix" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadOldmailsuffix(${status.index});}" style="width:150px;"
                                               class="easyui-combobox"  value="${itemsEntity.oldmailsuffix}"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>開始時間&nbsp;<font color="red">*</font></td>
                                    <td colspan="4" class="td_style2">
                                        <input id="starttime${status.index}" name="itemsEntitys[${status.index}].starttime" class="easyui-validatebox Wdate" data-options="width:150,required:true"
                                               style="width:150px"  value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${itemsEntity.starttime}"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                                    </td>
                                    <td>結束時間&nbsp;<font color="red">*</font></td>
                                    <td colspan="4" class="td_style2">
                                        <input id="endtime${status.index}" name="itemsEntitys[${status.index}].endtime" class="easyui-validatebox Wdate" data-options="width:150,required:true"
                                               style="width:150px" value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${itemsEntity.endtime}"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'starttime${status.index}\')}',maxDate:'#F{$dp.$D(\'starttime${status.index}\',{M:6})}'})" />
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>內部郵箱&nbsp;<font color="red">*</font></td>
                                    <td colspan="9" class="td_style2">
                                        <div style="float: left;margin-right: 5px">
                                            <input id="internalmail${status.index}" name="itemsEntitys[${status.index}].internalmail" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,validType:'comboxValidate[\'internalmail${status.index}\',\'请選擇內部郵箱類型\']',onBeforeLoad:function(){loadInternalmail(${status.index});}" style="width:150px;"
                                                   class="easyui-combobox"  value="${itemsEntity.internalmail}"/>
                                        </div>
                                        <div style="float: left;">
                                            <input id="innerchecknotesname${status.index}" name="itemsEntitys[${status.index}].innerchecknotesname" class="easyui-validatebox" data-options="width: 400,required:true"
                                                   value="${itemsEntity.innerchecknotesname }"/>
                                            <font color="red">*</font>
                                        </div>

                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>需求說明&nbsp;<font color="red">*</font></td>
                                    <td colspan="9" class="td_style2">
                                                            <textarea id="describtion${status.index}" name="itemsEntitys[${status.index}].describtion"
                                                                      class="easyui-validatebox"
                                                                      oninput="return LessThan(this);"
                                                                      onchange="return LessThan(this);"
                                                                      onpropertychange="return LessThan(this);"
                                                                      maxlength="300"
                                                                      style="width:99%;height:80px;" data-options="required:true"
                                                                      rows="5" cols="6"
                                                                      data-options="required:true,validType:'length[0,300]'">${itemsEntity.describtion }</textarea><span id="txtNum"></span>
                                    </td>
                                </tr>
                            </c:forEach>
                        </c:if>
                        <c:if test="${wfinternetemailprocessEntity.itemsEntitys.size()==0 || wfinternetemailprocessEntity.itemsEntitys==null}">
                            <tr align="center" bgcolor="#faebd7"><td colspan="10">第1位申請人信息</td></tr>
                            <tr align="center" id="itemsEntitys0">
                                <td>申請人工號&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input id="applyno0" name="itemsEntitys[0].applyno" class="easyui-validatebox"
                                           data-options="width: 80,required:true" value=""
                                           onblur="getUserNameByEmpno('apply',0);"/>
                                </td>
                                <td>申請人</td>
                                <td class="td_style1">
                                    <input id="applyname0" name="itemsEntitys[0].applyname"
                                           class="easyui-validatebox inputCss" value=""
                                           data-options="width:80" readonly/>
                                </td>
                                <td>單位代碼</td>
                                <td  class="td_style1">
                                    <input id="applydeptno0" name="itemsEntitys[0].applydeptno"
                                           class="easyui-validatebox inputCss" data-options="width: 90"
                                           value="" readonly/>
                                </td>
                                <td>費用代碼&nbsp;<font color="red">*</font></td>
                                <td  class="td_style1">
                                    <input id="applycostno0" name="itemsEntitys[0].applycostno"
                                           class="easyui-validatebox" data-options="width: 90" value=""/>
                                </td>
                                <td>所在廠區&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input id="applyfactoryid0" name="itemsEntitys[0].applyfactoryid"
                                           data-options="panelHeight:300,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'applyfactoryid0\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(0);}"
                                           style="width:100px;" class="easyui-combobox" value=""/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>資位</td>
                                <td class="td_style1">
                                    <input id="applyleveltype0" name="itemsEntitys[0].applyleveltype" class="easyui-validatebox inputCss" data-options="width: 80"
                                           value="" readonly/>
                                </td>
                                <td>管理職</td>
                                <td class="td_style1">
                                    <input id="applymanager0" name="itemsEntitys[0].applymanager" class="easyui-validatebox inputCss" data-options="width: 80"
                                           value="" readonly/>
                                </td>
                                <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                                <td colspan="3" class="td_style1">
                                    <input id="applyemail0" name="itemsEntitys[0].applyemail" class="easyui-validatebox"
                                           value="" style="width:300px;"
                                           data-options="required:true,validType:'email[\'applyemail0\',\'郵箱的格式不正確\']'"/>
                                </td>
                                <td>聯繫分機&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input id="applyphone0" name="itemsEntitys[0].applyphone" class="easyui-validatebox"
                                           style="width:90px;"
                                           value="" data-options="required:true,prompt:'579+66666'"
                                           onblur="valdApplyTel(this)"/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>單位&nbsp;<font color="red">*</font></td>
                                <td colspan="4" class="td_style1">
                                    <input id="applydeptname0" name="itemsEntitys[0].applydeptname" class="easyui-validatebox" data-options="width: 410,required:true"
                                           value=""/>
                                </td>
                                <td>法人代碼</td>
                                <td class="td_style1">
                                    <input id="applycompanycode0" name="itemsEntitys[0].applycompanycode"
                                           class="easyui-validatebox"
                                           data-options="width:80,required:true"  value=""/>
                                </td>
                                <td>法人名稱</td>
                                <td class="td_style1" colspan="2">
                                    <input id="applycompanyname0" name="itemsEntitys[0].applycompanyname"
                                           class="easyui-validatebox"
                                           data-options="width:300,required:true" value=""/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>Internet E-Mail賬號&nbsp;<font color="red">*</font></td>
                                <td colspan="4" class="td_style2">
                                    <input id="intermail0" name="itemsEntitys[0].intermail" class="easyui-validatebox" data-options="width: 200,required:true"
                                           value="" onblur="checkEmail(this)"/>
                                    <input id="mailsuffix0" name="itemsEntitys[0].mailsuffix" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,validType:'comboxValidate[\'mailsuffix0\',\'请選擇Internet E-Mail賬號後綴\']',onBeforeLoad:function(){loadMailsuffix(0);}" style="width:150px;"
                                           class="easyui-combobox"  value=""/>
                                </td>
                                <td colspan="2"><a href="${ctx}/wfinternetemailprocess/downLoadNamingRules">Email賬號命名規則</a></td>
                                <td>申請類型&nbsp;<font color="red">*</font></td>
                                <td class="td_style2" colspan="2">
                                    <input id="intermailtype0" name="itemsEntitys[0].intermailtype" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,validType:'comboxValidate[\'intermailtype0\',\'请選擇申請類型\']',onBeforeLoad:function(){loadIntermailtype(0);}" style="width:120px;"
                                           class="easyui-combobox"  value=""/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>需求類型&nbsp;<font color="red">*</font></td>
                                <td colspan="4" class="td_style2">
                                    <div class="demandtypeDiv0"></div>
                                    <input id="demandtype00" name="demandtype00" type="hidden" class="easyui-validatebox"
                                           data-options="width: 100"/>
                                </td>
                                <td colspan="2">原Email賬號名</td>
                                <td colspan="3" class="td_style2">
                                    <input id="oldintermail0" name="itemsEntitys[0].oldintermail" class="easyui-validatebox" data-options="width: 200"
                                           value="" onblur="checkEmail(this)"/>
                                    <input id="oldmailsuffix0" name="itemsEntitys[0].oldmailsuffix" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadOldmailsuffix(0);}" style="width:150px;"
                                           class="easyui-combobox"  value=""/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>開始時間&nbsp;<font color="red">*</font></td>
                                <td colspan="4" class="td_style2">
                                    <input id="starttime0" name="itemsEntitys[0].starttime" class="easyui-validatebox Wdate" data-options="width:150,required:true"
                                           style="width:150px"  value="<fmt:formatDate  pattern="yyyy-MM-dd" value=""/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                                </td>
                                <td>結束時間&nbsp;<font color="red">*</font></td>
                                <td colspan="4" class="td_style2">
                                    <input id="endtime0" name="itemsEntitys[0].endtime" class="easyui-validatebox Wdate" data-options="width:150,required:true"
                                           style="width:150px" value="<fmt:formatDate  pattern="yyyy-MM-dd" value=""/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'starttime0\')}',maxDate:'#F{$dp.$D(\'starttime0\',{M:6})}'})" />
                                </td>
                            </tr>
                            <tr align="center">
                                <td>內部郵箱&nbsp;<font color="red">*</font></td>
                                <td colspan="9" class="td_style2">
                                    <div style="float: left;margin-right: 5px">
                                        <input id="internalmail0" name="itemsEntitys[0].internalmail" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,validType:'comboxValidate[\'internalmail0\',\'请選擇內部郵箱類型\']',onBeforeLoad:function(){loadInternalmail(0);}" style="width:150px;"
                                               class="easyui-combobox"  value=""/>
                                    </div>
                                    <div style="float: left;">
                                        <input id="innerchecknotesname0" name="itemsEntitys[0].innerchecknotesname" class="easyui-validatebox" data-options="width: 400,required:true"
                                               value=""/>
                                    </div>

                                </td>
                            </tr>
                            <tr align="center">
                                <td>需求說明&nbsp;<font color="red">*</font></td>
                                <td colspan="9" class="td_style2">
                                                        <textarea id="describtion0" name="itemsEntitys[0].describtion"
                                                                  class="easyui-validatebox"
                                                                  oninput="return LessThan(this);"
                                                                  onchange="return LessThan(this);"
                                                                  onpropertychange="return LessThan(this);"
                                                                  maxlength="300"
                                                                  style="width:99%;height:80px;" data-options="required:true"
                                                                  rows="5" cols="6"
                                                                  data-options="required:true,validType:'length[0,300]'"></textarea><span id="txtNum"></span>
                                </td>
                            </tr>
                        </c:if>
                        </tbody>
                        <tr align="center">
                            <td width="10%"><a href="${ctx}/wfinternetemailprocess/downLoad/batchImportTpl" id="btnBatchImportTpl">批量申請模板下載</a></td>
                            <td colspan="9" class="td_style1">&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="batchImport" class="easyui-linkbutton" data-options="iconCls:'icon-hamburg-cv'" style="width: 100px;" onclick="openBatchImportWin();">批量導入</a>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style2">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="attachids" value="${wfinternetemailprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註&nbsp;</td>
                            <td colspan="9" class="td_style2">
                                1.特別說明Email賬號命名規則請仔細閱讀<a href="${ctx}/wfinternetemailprocess/downLoadNamingRules">Email賬號(即“用戶名”)的命名規則</a></br>
                                2.此單據須簽核并上傳<a href="${ctx}/wfinternetemailprocess/internetWwwFile">承諾書</a>，同時要對申請權限之電腦進行資安管控，并簽核<a href="${ctx}/wfspecialnetprocess/downLoad/InformationSecurityReview">外網資安點檢表</a>；</br>
                                3.申請Internet E-Mail帳號,FII用戶請選擇域名 @fii-foxconn.com ,非FII用戶請選擇域名
                                @foxconn.com</br>
                                4.發送外部郵件時，郵件大小不能超過2M；接收外部郵件時郵件大小不能超過10M
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)" onclick="showWfImag('${processId}','Internet E-Mail賬號申請');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="5" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="20%" style="float: left;margin-left: 5px;" id="yl10Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno10_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(190,'ylno10','ylname10',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno10" name="ylno10"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno10']}"
                                                               readonly
                                                               value="${wfinternetemailprocessEntity.ylno10 }"/><c:if
                                                            test="${requiredMap['ylno10'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname10" name="ylname10"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno10']}"
                                                                value="${wfinternetemailprocessEntity.ylname10 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl5Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno5_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'yl5Table','ylno5','ylname5',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno5" name="ylno5"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno5']}"
                                                               readonly
                                                               value="${wfinternetemailprocessEntity.ylno5 }"/><c:if
                                                            test="${requiredMap['ylno5'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname5" name="ylname5"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno5']}"
                                                                value="${wfinternetemailprocessEntity.ylname5 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#dealdeptno').val(),'cchargeno','cchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfinternetemailprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfinternetemailprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="czchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['czchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('czchargeTable',$('#dealdeptno').val(),'czchargeno','czchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="czchargeno" name="czchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['czchargeno']}"
                                                               readonly
                                                               value="${wfinternetemailprocessEntity.czchargeno }"/><c:if
                                                            test="${requiredMap['czchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="czchargename" name="czchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['czchargeno']}"
                                                                value="${wfinternetemailprocessEntity.czchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole33('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfinternetemailprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfinternetemailprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="20%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wfinternetemailprocessEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfinternetemailprocessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno2_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(62,'yl2Table','ylno2','ylname2',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="ylno2"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}"
                                                               readonly
                                                               value="${wfinternetemailprocessEntity.ylno2 }"/><c:if
                                                            test="${requiredMap['ylno2'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname2" name="ylname2"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}"
                                                                value="${wfinternetemailprocessEntity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl6Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno6_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(25,'yl6Table','ylno6','ylname6',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno6" name="ylno6"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno6']}"
                                                               readonly
                                                               value="${wfinternetemailprocessEntity.ylno6 }"/><c:if
                                                            test="${requiredMap['ylno6'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname6" name="ylname6"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno6']}"
                                                                value="${wfinternetemailprocessEntity.ylname6 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zabchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zabchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(154,'zabchargeTable','zabchargeno','zabchargename',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zabchargeno" name="zabchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zabchargeno']}"
                                                               readonly
                                                               value="${wfinternetemailprocessEntity.zabchargeno }"/><c:if
                                                            test="${requiredMap['zabchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zabchargename" name="zabchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zabchargeno']}"
                                                                value="${wfinternetemailprocessEntity.zabchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl3Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno3_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(155,'yl3Table','ylno3','ylname3',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno3" name="ylno3"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno3']}"
                                                               readonly
                                                               value="${wfinternetemailprocessEntity.ylno3 }"/><c:if
                                                            test="${requiredMap['ylno3'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname3" name="ylname3"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno3']}"
                                                                value="${wfinternetemailprocessEntity.ylname3 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="20%" style="float: left;margin-left: 5px;"
                                                   id="zacwchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zacwchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(193,'zacwchargeTable','zacwchargeno','zacwchargename',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zacwchargeno" name="zacwchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zacwchargeno']}"
                                                               readonly
                                                               value="${wfinternetemailprocessEntity.zacwchargeno }"/>
                                                        <c:if test="${requiredMap['zacwchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        /<input id="zacwchargename" name="zacwchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zacwchargeno']}"
                                                                value="${wfinternetemailprocessEntity.zacwchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl4Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno4_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(188,'yl4Table','ylno4','ylname4',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno4" name="ylno4"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno4']}"
                                                               readonly
                                                               value="${wfinternetemailprocessEntity.ylno4 }"/>
                                                        <c:if test="${requiredMap['ylno4'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        /<input id="ylname4" name="ylname4"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno4']}"
                                                                value="${wfinternetemailprocessEntity.ylname4 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl7Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno7_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(187,'yl7Table','ylno7','ylname7',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno7" name="ylno7"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno7']}"
                                                               readonly
                                                               value="${wfinternetemailprocessEntity.ylno7 }"/>
                                                        <c:if test="${requiredMap['ylno7'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        /<input id="ylname7" name="ylname7"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno7']}"
                                                                value="${wfinternetemailprocessEntity.ylname7 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="sychargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['sychargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('sychargeTable',$('#dealdeptno').val(),'sychargeno','sychargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="sychargeno" name="sychargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['sychargeno']}"
                                                               readonly
                                                               value="${wfinternetemailprocessEntity.sychargeno }"/>
                                                        <c:if test="${requiredMap['sychargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        /<input id="sychargename" name="sychargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['sychargeno']}"
                                                                value="${wfinternetemailprocessEntity.sychargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfinternetemailprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <input type="checkbox" id="agree" name="agree"/><a href="${ctx}/requisitionlist/downLoad/commitmentTpl" plain="true" id="btnCommitmentTpl">本人已閱讀并同意服務條款</a>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;"
                                   onclick="canelTask('${wfinternetemailprocessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="areaId" name="areaId" value="" />
    <input type="hidden" id="buildingId" name="buildingId" value="" />
    <input type="hidden" id="onlyKchargeSignle" value="1" />
    <div id="win"></div>
    <div id="dlg"></div>
</form>
<div id="optionWin" class="easyui-window" title="Internet E-Mail賬號申請單" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%">
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="file" type="file" style="width: 300px"
                           accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>
                    <input id="isExcel2003" name="isExcel2003" type="hidden" value="">

                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr>
                <td align="left" style="width: 60%; white-space: nowrap;">
                    <span id="tishi">正在導入中，請稍後...</span>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <span id="labelListAddResult"></span><a href="${ctx}/wfinternetemailprocess/errorExcel"
                                                            id="downloadError" plain="true">查看錯誤信息</a>
                </td>
            </tr>
        </table>
    </form>
</div>
<script src='${ctx}/static/js/information/wfinternetemailprocess.min.js?random=2025052802'></script>
</body>
</html>
