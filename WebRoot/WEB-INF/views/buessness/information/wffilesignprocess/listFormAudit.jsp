<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>文檔簽核申請表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
        .commonW{
            width: 100%;
            min-width: 1200px;
        }
        .textarea-style{
            border-width: 1px;
            border-style: solid;
            line-height: 16px;
            padding-top: 1px;
            padding-left: 3px;
            padding-bottom: 1px;
            padding-right: 3px;
            background-attachment: scroll;
            background-size: auto;
            background-origin: padding-box;
            background-clip: border-box;
            border: 1px solid #AED0EA;
            vertical-align: middle;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wffilesignprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wffilesignprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wffilesignprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">文檔簽核申請表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wffilesignprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wffilesignprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wffilesignprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wffilesignprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wffilesignprocessEntity.makerno}/${wffilesignprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">提報人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">提報人工號</td>
                            <td width="4%" class="td_style2">${wffilesignprocessEntity.applyno}</td>
                            <td width="5%">提報人</td>
                            <td width="5%" class="td_style2">${wffilesignprocessEntity.applyname}</td>
                            <td width="5%">單位代碼</td>
                            <td width="5%" class="td_style2">${wffilesignprocessEntity.applydeptno}</td>
                            <td width="5%">費用代碼</td>
                            <td width="5%" class="td_style2">${wffilesignprocessEntity.applycostno}</td>
                            <td width="5%">所在廠區</td>
                            <td width="5%" class="td_style2">${wffilesignprocessEntity.applyfactoryname}</td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱</td>
                            <td colspan="3" class="td_style2">${wffilesignprocessEntity.applydeptname}</td>
                            <td>聯繫方式</td>
                            <td class="td_style2">${wffilesignprocessEntity.applytel}</td>
                            <td>緊急程度</td>
                            <td colspan="3">
                                <label style="padding-right: 10px;"><input type="radio" name="urgencyLevelRadio"
                                                                           style="transform: translate(0, -3px);"
                                                                           value="0"
                                                                           <c:if test="${wffilesignprocessEntity.urgencyLevel=='0'}">checked</c:if>/>特急</label>
                                <label style="padding-right: 10px;"><input type="radio" name="urgencyLevelRadio"
                                                                           style="transform: translate(0, -3px);"
                                                                           value="1"
                                                                           <c:if test="${wffilesignprocessEntity.urgencyLevel=='1'}">checked</c:if>/>緊急</label>
                                <label style="padding-right: 10px;"><input type="radio" name="urgencyLevelRadio"
                                                                           style="transform: translate(0, -3px);"
                                                                           value="2"
                                                                           <c:if test="${wffilesignprocessEntity.urgencyLevel=='2'}">checked</c:if>/>一般</label>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>呈簽表單名稱</td>
                            <td colspan="3" class="td_style2">${wffilesignprocessEntity.applytablename}</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wffilesignprocessEntity.applyemail}</td>
                            <td>表單類型</td>
                            <td class="td_style2">
                                <input id="formtype" name="formtype" class="easyui-combobox" disabled
                                       value="${wffilesignprocessEntity.formtype}" panelHeight="auto"
                                       data-options="width: 120,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/APPLY_FORM_TYPE'"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>呈簽說明</td>
                            <td colspan="9" class="td_style2">
                                <textarea id="applyexplain" name="applyexplain"
                                          maxlength="500" class="easyui-validatebox" style="width:99%;height:60px;"
                                          rows="5" cols="3">${wffilesignprocessEntity.applyexplain}</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" class="td_style1">
                                <input type="hidden" id="attachids2" name="attachids2"
                                       value="${wffilesignprocessEntity.attachids2 }"/>
                                <fox:hasPermssion name="${wffilesignprocessEntity.serialno }">
                                    <div class="pdf" style="padding-top: 5px;margin-left: 5px;">
                                        <iframe id="pdf_page" name="pdf_page" style="width:99%;height:700px"
                                                src="${ctx}/static/plugins/PDFJSInNet/PDFJSInNet/web/viewer.html?file=${ctx}/fileSignUpload/fileSignDownloadPdfAddImg111/${fn:replace(wffilesignprocessEntity.attachids2,',','')}.pdf?name=${wffilesignprocessEntity.applytablename}<fmt:formatDate value="${wffilesignprocessEntity.createtime}" pattern="yyyyMMdd" />.pdf">
                                        </iframe>
                                    </div>
                                </fox:hasPermssion>
                                <fox:hasNotPermssion name="${wffilesignprocessEntity.serialno }">
                                    <font color="red" style="margin-left: 10px">您無權限查看附件，請聯繫管理員添加ip權限</font>
                                </fox:hasNotPermssion>
                                <%--                                <div id="dowloadUrl2">--%>
                                <%--                                    <c:forEach items="${file2}" varStatus="i" var="item">--%>
                                <%--                                        <div id="${item.id}"--%>
                                <%--                                             style="line-height:30px;margin-left:5px;" class="float_L">--%>
                                <%--                                            <div class="float_L">--%>
                                <%--                                                <fox:hasPermssion name="${wffilesignprocessEntity.serialno }">--%>
                                <%--                                                    <a href="${ctx}/fileSignUpload/fileSignDownloadPdfAddImg/${item.id}">${item.name}</a>--%>
                                <%--                                                </fox:hasPermssion>--%>
                                <%--                                                <fox:hasNotPermssion name="${wffilesignprocessEntity.serialno }">--%>
                                <%--                                                    <a href="${ctx}/fileSignUpload/fileSignDownloadPdfAddImg/${item.id}" title="您無權限查看，請聯繫管理員添加ip權限" onclick="javascript:return false;">${item.name}</a>--%>
                                <%--                                                </fox:hasNotPermssion>--%>
                                <%--                                            </div>--%>
                                <%--                                        </div>--%>
                                <%--                                    </c:forEach>--%>
                                <%--                                </div>--%>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <input type="hidden" id="attachids1" name="attachids1"
                                       value="${wffilesignprocessEntity.attachids1 }"/>
                                <div id="dowloadUrl1">
                                    <c:forEach items="${file1}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <fox:hasPermssion name="${wffilesignprocessEntity.serialno }">
                                                    <a href="${ctx}/fileSignUpload/fileSignDownloadEncryptFtps/${item.id}">${item.name}</a>
                                                </fox:hasPermssion>
                                                <fox:hasNotPermssion name="${wffilesignprocessEntity.serialno }">
                                                    <a href="${ctx}/fileSignUpload/fileSignDownloadEncryptFtps/${item.id}"
                                                       title="您無權限查看，請聯繫管理員添加ip權限"
                                                       onclick="javascript:return false;">${item.name}</a>
                                                </fox:hasNotPermssion>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="9" class="td_style2">
                                <font color="red">1.簽核文檔類型需為PDF檔，文檔小於8M，單據簽核完成後可郵件直接下載，大於8M只能在網站下載；</font><br/>
                                2.簽核路徑設定時，一個部門設置到一個會簽部門節點中；<br/>
                                3.“批註”欄位設置超過${maxLength}個字，需自定義位置或不顯示在簽核件上。
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                       <%-- <tr >
                            <td align="center" width="10%" >批註<br/>是否顯示到簽核件</td>
                            <td align="left" width="90%" style="padding-left:30px;">
                                <label> <input type="radio" selected name="reamrkShow" value="Y" style="margin: 0;zoom: 1.5;transform: translate(0, -1px);"/><span style="padding-left: 5px;">是</span></label>
                                <label style="padding-left: 10px;"> <input type="radio" name="reamrkShow" value="N" style="margin: 0;zoom: 1.5;transform: translate(0, -1px);"/><span style="padding-left: 5px;">否</span></label>
                            </td>
                        </tr>--%>
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%">
                                <div align="left" style="padding: 3px 0 5px 32px;height: 26px;box-sizing: border-box;">批註是否顯示到簽核件:
                                    <label> <input type="radio" checked name="remarkShow" value="Y" style="margin: 0;zoom: 1.5;transform: translate(0, -1px);"/><span style="padding-left: 5px;">固定位置（字數≤<span style="color:red;">${maxLength}</span>）</span></label>
                                    <label style="padding-left: 10px;"> <input type="radio" name="remarkShow" value="Z" style="margin: 0;zoom: 1.5;transform: translate(0, -1px);"/><span style="padding-left: 5px;">自定義位置（字數不限，鼠標選中批註框拖動位置|縮放尺寸）</span></label>
                                    <div style="display: none;height:25px;line-height: 25px;margin-top: -3px;" id="remarkAddAndDel">
                                        <a class="easyui-linkbutton" iconCls="icon-add" plain="true" id="addReamekBtn" href="javascript:void(0);" onclick="addOneRemark()">添加批註</a>
                                        <a class="easyui-linkbutton" iconCls="icon-remove" plain="true" id="delReamekBtn" href="javascript:void(0);" onclick="delAllRemark()">删除全部批注</a>
                                        <%-- <a href="javaScript:void(0);" style="display:none;margin-bottom:15px;" id="addReamekBtn" onclick="addOneRemark()">添加批註</a>
                                        <a href="javaScript:void(0);" style="display:none;" id="delReamekBtn" onclick="delAllRemark()">删除ALL批注</a>--%>
                                    </div>
                                    <span style="color: gainsboro;">|</span>
                                    <label style="padding-left: 10px;"> <input type="radio" name="remarkShow" value="N" style="margin: 0;zoom: 1.5;transform: translate(0, -1px);"/><span style="padding-left: 5px;">批註內容不顯示在簽核件上</span></label>
                                </div>
                                <textarea id="attachidsremark" name="attachidsremark"  class="textarea-style"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                              <%--  <div style="display:inline-block;">

                                </div>--%>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wffilesignprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','文檔簽核申請表');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wffilesignprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script>
    var positionX = "${signPositions.positionX}"?"${signPositions.positionX}":0;
    var positionY = "${signPositions.positionY}"?"${signPositions.positionY}":0;
    var signDivWidth = "${signPositions.width}"?"${signPositions.width}":100;
    var signDivHeight = "${signPositions.height}"?"${signPositions.height}":1;
    var pageNum = "${signPositions.pageNumber}"?"${signPositions.pageNumber}":1;
    var imgShowType = "${signPositions.imgShowType}"?"${signPositions.imgShowType}":"1";   //1-三行 2-二行 3-一行
    var remarkList = new Array();
    var workMaxLength = ${maxLength};
</script>
<script src='${ctx}/static/js/information/wffilesignprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>