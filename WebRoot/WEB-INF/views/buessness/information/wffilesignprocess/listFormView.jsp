<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@ taglib prefix="fox" uri="/foxconn-tags" %>
<html>
<head>
    <title>文檔簽核申請表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
<%--    <script type="text/javascript" src='${ctx}/static/plugins/PDFJSInNet/PDFJSInNet/web/viewer.js'></script>--%>
<%--    <script type="text/javascript" src='${ctx}/static/plugins/PDFJSInNet/PDFJSInNet/build/pdf.js'></script>--%>
    <style type="text/css">
        .commonW{
            width: 100%;
            min-width: 1200px;
        }
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wffilesignprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wffilesignprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wffilesignprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">文檔簽核申請表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wffilesignprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wffilesignprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wffilesignprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wffilesignprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wffilesignprocessEntity.makerno}/${wffilesignprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">提報人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">提報人工號</td>
                            <td width="4%" class="td_style2">${wffilesignprocessEntity.applyno}</td>
                            <td width="5%">提報人</td>
                            <td width="5%" class="td_style2">${wffilesignprocessEntity.applyname}</td>
                            <td width="5%">單位代碼</td>
                            <td width="5%" class="td_style2">${wffilesignprocessEntity.applydeptno}</td>
                            <td width="5%">費用代碼</td>
                            <td width="5%" class="td_style2">${wffilesignprocessEntity.applycostno}</td>
                            <td width="5%">所在廠區</td>
                            <td width="5%" class="td_style2">${wffilesignprocessEntity.applyfactoryname}</td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱</td>
                            <td colspan="3" class="td_style2">${wffilesignprocessEntity.applydeptname}</td>
                            <td>聯繫方式</td>
                            <td class="td_style2">${wffilesignprocessEntity.applytel}</td>
                            <td>緊急程度</td>
                            <td colspan="3">
                                <label style="padding-right: 10px;"><input type="radio" name="urgencyLevelRadio"
                                                                           style="transform: translate(0, -3px);"
                                                                           value="0"
                                                                           <c:if test="${wffilesignprocessEntity.urgencyLevel=='0'}">checked</c:if>/>特急</label>
                                <label style="padding-right: 10px;"><input type="radio" name="urgencyLevelRadio"
                                                                           style="transform: translate(0, -3px);"
                                                                           value="1"
                                                                           <c:if test="${wffilesignprocessEntity.urgencyLevel=='1'}">checked</c:if>/>緊急</label>
                                <label style="padding-right: 10px;"><input type="radio" name="urgencyLevelRadio"
                                                                           style="transform: translate(0, -3px);"
                                                                           value="2"
                                                                           <c:if test="${wffilesignprocessEntity.urgencyLevel=='2'}">checked</c:if>/>一般</label>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>呈簽表單名稱</td>
                            <td colspan="3" class="td_style2">${wffilesignprocessEntity.applytablename}</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wffilesignprocessEntity.applyemail}</td>
                            <td>表單類型</td>
                            <td class="td_style2">
                                <input id="formtype" name="formtype" class="easyui-combobox" disabled
                                       value="${wffilesignprocessEntity.formtype}" panelHeight="auto"
                                       data-options="width: 120,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/APPLY_FORM_TYPE'"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>呈簽說明</td>
                            <td colspan="9" class="td_style2">
                                <textarea id="applyexplain" name="applyexplain"
                                          maxlength="500" class="easyui-validatebox" style="width:99%;height:60px;"
                                          rows="5" cols="3">${wffilesignprocessEntity.applyexplain}</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" class="td_style1">
                                <input type="hidden" id="attachids2" name="attachids1" value="${wffilesignprocessEntity.attachids2 }"/>
                                <fox:hasPermssion name="${wffilesignprocessEntity.serialno }">
                                    <div class="pdf" style="padding-top: 5px;margin-left: 5px;">
                                        <iframe id="pdf_page" name="pdf_page" style="width:99%;height:700px"
                                                src="${ctx}/static/plugins/PDFJSInNet/PDFJSInNet/web/viewer.html?file=${ctx}/fileSignUpload/fileSignDownloadPdfAddImg111/${fn:replace(wffilesignprocessEntity.attachids2,',','')}.pdf?name=${wffilesignprocessEntity.applytablename}<fmt:formatDate value="${wffilesignprocessEntity.createtime}" pattern="yyyyMMdd" />.pdf&random=<%=Math.random()%>">
                                        </iframe>
                                    </div>
                                </fox:hasPermssion>
                                <fox:hasNotPermssion name="${wffilesignprocessEntity.serialno }">
                                    <font color="red" style="margin-left: 10px">您無權限查看附件，請聯繫管理員添加ip權限</font>
                                </fox:hasNotPermssion>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <input type="hidden" id="attachids1" name="attachids1" value="${wffilesignprocessEntity.attachids1 }"/>
                                <div id="dowloadUrl1">
                                    <c:forEach items="${file1}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <fox:hasPermssion name="${wffilesignprocessEntity.serialno }">
                                                    <a href="${ctx}/fileSignUpload/fileSignDownloadEncryptFtps/${item.id}">${item.name}</a>
                                                </fox:hasPermssion>
                                                <fox:hasNotPermssion name="${wffilesignprocessEntity.serialno }">
                                                    <a href="${ctx}/fileSignUpload/fileSignDownloadEncryptFtps/${item.id}" title="您無權限查看，請聯繫管理員添加ip權限" onclick="javascript:return false;">${item.name}</a>
                                                </fox:hasNotPermssion>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="9" class="td_style2">
                                <font color="red">1.簽核文檔類型需為PDF檔，文檔小於8M，單據簽核完成後可郵件直接下載，大於8M只能在網站下載；</font><br/>
                                2.簽核路徑設定時，一個部門設置到一個會簽部門節點中；<br/>
                                3.“批註”欄位設置超過10字僅可在App/PC端簽核記錄內查看批註內容，呈簽檔不顯示其內容。
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','文檔簽核申請表');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wffilesignprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wffilesignprocessEntity.workstatus!=null&&wffilesignprocessEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/information/wffilesignprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>