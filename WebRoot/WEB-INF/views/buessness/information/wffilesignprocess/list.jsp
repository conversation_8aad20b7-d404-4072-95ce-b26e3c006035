<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>文檔簽核申請表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input type="text" name="filter_EQS_applyno" class="easyui-validatebox"
               data-options="width:150,prompt: '提報人工號'"/>
        <input type="text" name="filter_EQS_applydeptno" class="easyui-validatebox"
               data-options="width:150,prompt: '提報人單位代碼'"/>
        <input type="text" name="filter_EQS_serialno" class="easyui-validatebox"
               data-options="width:150,prompt: '任務編碼'"/>
        <input id="cxFactory" name="filter_EQS_applyfactoryid" class="easyui-combobox"
               panelHeight="auto" data-options="width:150"/>
        <input type="text" name="filter_GED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd"
               data-options="width:150,prompt: '填單开始日期'"/>
        - <input type="text" name="filter_LED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd"
                 data-options="width:150,prompt: '填單结束日期'"/>
        <input type="text" name="filter_GED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"
               data-options="width:150,prompt: '簽核完成开始日期'"/>
        - <input type="text" name="filter_LED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"
                 data-options="width:150,prompt: '簽核完成结束日期'"/>
        <input id="qysjzt" style="width:100px" class="easyui-validatebox" name="filter_EQS_workstatus"/>
        <input id="qysjtype" style="width:120px" class="easyui-validatebox" name="filter_EQS_formtype"/>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
           onclick="listSearchReset()">重置</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
           onclick="exportExcel()">导出Excel</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
           onclick="revokeForm()">撤單</a>
        <!--導出用，請勿刪除-->
        <input id="page" name="page" type="hidden" value="1"/>
        <input id="rows" name="rows" type="hidden" value="30"/>
    </form>
    <div id="passwordDialog"></div>
</div>
<table id="dg"></table>
<div id="dlg"></div>

<div id="optionWin" class="easyui-window" title="撤單原因" closed="true" modal="true"
     collapsible="false" minimizable="false" maximizable="true" draggable="true" resizable="false"
     style="width:450px;height:200px;padding:5px;left:160px;top:150px;background: #fafafa;display:none;overflow:hidden;"
     align="left">
    <table style="text-align: center;width:100%;height:70%;">
        <tr>
            <td style="width:20%;">撤單原因：</td>
            <td><textarea id="remark" style="width:99%;height:100%;border: 1px solid #138CDD;"></textarea></td>
        </tr>
    </table>
    <div style="text-align: center;padding-top:15px;">
        <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
           data-options="iconCls:'icon-add'"
           style="width: 100px;" onclick="revokeFormSave();">確定</a>&nbsp;&nbsp;&nbsp;&nbsp;
        <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
           data-options="iconCls:'icon-ok'"
           style="width: 100px;" onclick="closeWin();">取消</a>
    </div>
</div>

<script type="text/javascript">

    //創建下拉查詢條件
    $.ajax({
        url: ctx + "/system/dict/getDictByType/audit_status",
        dataType: "json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#qysjzt").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
    //創建下拉查詢條件
    $.ajax({
        url: ctx + "/system/dict/getDictByType/APPLY_FORM_TYPE",
        dataType: "json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#qysjtype").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 100,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇表單類型'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
    var d;
    $(function () {
        dg = $('#dg').datagrid({
            method: "get",
            url: ctx + '/wffilesignprocess/list',
            fit: true,
            fitColumns: true,
            //fitColumns: false,  //如需要底部出現滾動條，修改爲false
            border: false,
            idField: 'id',
            striped: true,
            cache: false,  //关闭AJAX相应的缓存
            pagination: true,
            rownumbers: true,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: true,
            columns: [[
                {field: 'id', title: '主鍵', hidden: true},
                {field: 'serialno', title: '任務編碼', sortable: true, width: 210, formatter: operation},
                {field: 'applyno', title: '提報人工號', sortable: true, width: 100},
                {field: 'applyname', title: '提報人姓名', sortable: true, width: 100},
                {field: 'applydeptno', title: '提報人單位代碼', sortable: true, width: 100},
                {field: 'applytablename', title: '呈簽表單名稱', sortable: true, width: 100},
                {field: 'makerno', title: '填單人工號', sortable: true, width: 100},
                {field: 'createtime', title: '填單日期', sortable: true, width: 100},
                {field: 'workstatus', title: '任務狀態', sortable: true, width: 100},
                {field: 'nodeName', title: '當前審核節點', sortable: true, width: 150, formatter: formatProgress},
                {field: 'auditUser', title: '當前簽核人', sortable: true, width: 120},
                {field: 'complettime', title: '簽核完成日期', sortable: true, width: 100},
                {field: 'filepassword', title: '是否加密', width: 50, hidden: true},
            ]],
            onLoadSuccess: function () {
                $(".easyui-tooltip").tooltip({
                    onShow: function () {
                        $(this).tooltip('tip').css({
                            borderColor: '#000'
                        });
                    }
                });
                //$(this).datagrid("fixRownumber");
            },
            rowStyler: rowStylerFun,
            enableHeaderClickMenu: true,
            enableHeaderContextMenu: true,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });
        //創建表單查詢任務廠區下拉查詢條件
        $.get(ctx + '/tqhfactoryidconfig/allFactorys/', function (result) {
            var requestFactoryResult = JSON.parse(JSON.stringify(result));
            requestFactoryResult.unshift({
                factoryid: '',
                factoryname: '請選擇承辦人所在廠區'
            });
            $("#cxFactory").combobox({
                data: requestFactoryResult,
                valueField: "factoryid",
                textField: "factoryname",
                editable: false,
                panelHeight: 350
            });
        }, "json");
    });

    //任務編號查看頁面
    function operation(value, row, index) {
        if (row.filepassword != null && row.filepassword != '') {
            return '<a href="#" onclick="pwdDialog(\'' + row.serialno + '\',\'' + row.filepassword + '\',\'' + row.makerno + '\')">' + value + '</a>';
        } else {
            return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('詳情',ctx+'/wffilesignprocess/view/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
        }
    };

    function pwdDialog(serialno, filepassword, empno) {
        /*$.ajaxSettings.async = false;
        $.ajax({
            type: "get",
            url: ctx + "/wffilesignprocess/sendFilePassword",
            beforeSend: ajaxLoading,
            data: {serialno: serialno, password: filepassword},
            success: function (data) {
                ajaxLoadEnd();
            }
        });
        $.ajaxSettings.async = true;*/
        d = $("#passwordDialog").dialog({
            title: "操作提醒", // 对话框标题
            width: 300, // 宽度
            height: 200, // 高度
            modal: true, // 是否模态
            content: '<div style="padding:20px;font-size: 14px">此文件加密，密碼將發送至聚會、便易簽，請輸入密碼<br><br><input id="passwordInput" type="password" style="width:100%;"></div>', // 对话框内容
            buttons: [
                {
                    text: "確定",
                    handler: function () {
                        var passwordInput = $("#passwordInput").val(); // 获取输入的密码
                        if (passwordInput === "") {
                            $.messager.alert("操作提示", "請輸入密碼！", "warning");
                        } else {
                            // 发送密码到后端
                            $.ajax({
                                type: "get",
                                url: ctx + "/wffilesignprocess/validFilePassword",
                                beforeSend: ajaxLoading,
                                data: {passwordInput: passwordInput, password: filepassword},
                                success: function (data) {
                                    ajaxLoadEnd();
                                    if (data == "success") {
                                        window.parent.mainpage.mainTabs.addModule('詳情', ctx + "/wffilesignprocess/view/" + serialno, "icon-hamburg-basket");
                                        d.panel('close');
                                    } else {
                                        $.messager.alert("溫馨提示", "密碼不正確", "warning");
                                    }
                                }
                            });
                        }
                    }
                },
                {
                    text: "獲取密碼",
                    handler: function () {
                        $.ajax({
                            type: "get",
                            url: ctx + "/wffilesignprocess/sendFilePassword2",
                            beforeSend: ajaxLoading,
                            data: {serialno: serialno, password: filepassword, empno: empno},
                            success: function (data) {
                                ajaxLoadEnd();
                                successTip(data, dg);
                                $.messager.alert("提示", "密码已发送,请检查您的聚會、便易签。", "info");
                            }
                        });

                    }
                }
            ],
            onClose: function () {
                d.panel('close');
            }
        });

    }

    //创建查询对象并查询
    function cx() {
        $("#optionWin").window('close');
        var obj = $("#searchFrom").serializeObject();
        dg.datagrid('load', obj);
    }

    //表單進度查詢頁面查詢條件重置
    function listSearchReset() {
        $("#searchFrom").form("reset");
    }

    //导出excel
    function exportExcel() {
        var options = $('#dg').datagrid('getPager').data("pagination").options;
        var page = options.pageNumber;//当前页数  
        var total = options.total;
        var rows = options.pageSize;//每页的记录数（行数）  
        var form = document.getElementById("searchFrom");
        $('#page').val(page);
        $('#rows').val(total);
        var form = document.getElementById("searchFrom");
        searchFrom.action = ctx + '/wffilesignprocess/exportExcel';
        form.submit();
    }

    //撤單
    var revokeFormNo = "";
    function revokeForm(){
        var selected = $('#dg').datagrid('getSelected');
        if(selected){
            revokeFormNo = selected.serialno;
            if(selected.workstatus=="簽核中"){
                var optionWin = $("#optionWin");
                if (optionWin.is(':visible')){
                    ;
                }else{
                    optionWin.slideDown();//显示
                }
                $("#remark").val("");
                optionWin.window('open');
            }else{
                $.messager.alert('系統提示','只可撤銷狀態為簽核中的表單','warning');
            }
        }else{
            $.messager.alert('系統提示','請先選擇一筆資料再進行撤單','warning');
        }
    }

    //關閉撤單頁面
    function closeWin(){
         revokeFormNo = "";
         $("#optionWin").window('close');
    }

    //撤單
    function revokeFormSave(){
        var remark = $("#remark").val();
        if(remark==null||remark.trim()==""){
            $.messager.alert('系統提示','請填寫撤單原因','warning');
            return;
        }
        $.ajax({
            type:'post',
            dataType:'text',
            url: ctx + '/wffilesignprocess/revokeForm',
            data:{"formNo":revokeFormNo,"remark":remark},
            success: function(data){
                if(data == "formDoing"){
                    $.messager.alert('系統提示','表單正在審核中','warning');
                    return;
                }
                if(data == "statusChange"){
                    $.messager.alert('系統提示','只可撤銷本人提交的申請單','warning');
                    return;
                }
                if(data == "success"){
                    $("#remark").val("");
                    $.messager.alert('系統提示','操作成功','warning',cx());
                }
            }
        });
    }

</script>
</body>
</html>