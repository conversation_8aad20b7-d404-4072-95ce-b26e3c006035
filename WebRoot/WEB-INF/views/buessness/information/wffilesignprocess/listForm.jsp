<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>文檔簽核申請表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src='${ctx}/static/js/information/wffilesignprocess.js?random=<%= Math.random()%>'></script>
    <script type="text/javascript" src='${ctx}/static/plugins/jquery/jquery.media.js'></script>

    <style type="text/css">
        html,body{
            width:100%;
            height: 100%;
        }
        .td_style2 {
            text-align: left;
        }
        .approval-template{
            width:100%;
            height:100%;
            display: none;
        }
        .left-head{
            height:50px;
            line-height:50px;
            width:200px;
            position: fixed;
            background: white;
            text-align: center;
            box-shadow: 5px 2px 12px 0 rgba(0,0,0,.1);
            z-index:3;
        }
        .left-pannel{
            width:200px;
            height:100%;
            position: fixed;
            left:0;
            top:0;
            box-shadow: 5px 2px 12px 0 rgba(0,0,0,.1);
        }
        .right-pannel{
            height:100%;
            margin-left:200px;
            position: relative;
        }
        .left-content{
            padding: 50px 0;
            height: 100%;
            overflow: hidden;
            box-sizing: border-box;
        }
        .content-roll{
            width:210px;
            overflow-y: scroll;
            height: 100%;
            background: white;
        }
        .right-head{
            height:50px;
            width:100%;
            position: fixed;
            line-height: 50px;
            background: white;
            box-shadow: 5px 2px 12px 0 rgba(0,0,0,.1);
            text-align: center;
            box-sizing: border-box;
            padding-right: 200px;
            z-index:3;
        }
        .page-input{
            width: 15px;
            text-align: center;
        }

        .content-box{
            position: relative;
            padding: 50px 0;
            width:100%;
        }
        .right-content{
            width:800px;
            margin: 30px auto 30px auto;;
            position: relative;
            display: block;
        }
        .template-img{
            position: relative;
            width:100%;
            height:100%;
            user-select: none;
        }
        .btn-page{
            background-color: #297dd1;
            color: #fff ;
            border: 1px solid #297dd1 ;
            width: 80px ;
            border-radius: 3px ;
            padding: 5px;
            margin-left: 10px;
            cursor: pointer;
        }
        .btn-page:hover{
            background-color: #68A9E9;
            border: 1px solid #68A9E9 ;
        }
        .btn-page:active{
            background-color: #fff;
            color: #68A9E9 ;
        }
        .footer-btn{
            height:50px;
            line-height:50px;
            bottom: 0;
            width:100%;
            position: fixed;
            background: white;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
            text-align: center;
        }
        .sign-item{
            width :160px;
            border: 1px solid #138CDD;
            margin-left: 15px;
            margin-top: 20px;
        }
        .sign-item-head{
            border-bottom: 1px solid #138CDD;
            text-align: center;
            height:30px;
            line-height:30px;
        }
        .item-img{
            width: 100px;
            height: 100px;
            margin: 10px 30px;
            border: 1px solid #E4E6E4;
            box-sizing: border-box;
            cursor: pointer;
            background: white;
        }
        .item-img:after{
            content:"";
            clear:both;
            display: block;
        }
        .name-img{
            margin: 0;
            height: 50%;
            line-height: 1.6;
            font-size: 30px;
            text-align: center;
            font-family: cursive;
        }
        .signed-time,.signed-remark{
            margin: 0;
            font-size: 14px;
            height: 25%;
            line-height: 1;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .content-font{
            color: #000000;
            font-size: 10px;
            font-weight: normal;
        }
        .auditor-img{
            width:100%;
            height:100%;
        }

        .pre-view{
            position: fixed;
            top:0;
            left:0;
            width:100%;
            height:100%;
            z-index:99;
            display:none;
            background:white;
            text-align: center;
        }
        .pre-pdf{
            border: 1px solid;
            height: calc(100% - 45px);
            width:1000px;
            margin: 5px 0;
        }
        .sub-bar{
            width:100%;
            height:35px;
            padding-top:3px;
            text-align: center;
        }
        .loading-bar{
            height:100%;
            width:100%;
            background: black;
            opacity: 0.3;
            position: absolute;
            top: 0;
        }
        .iframe.panel-iframe{
            height:100%;
        }

        .datagrid-mask,.datagrid-mask-msg{
            z-index:999;
        }

        .active{
            border-color:#ccc !important;
        }

        .right-btn-item{
            width:100px;
            height:100px;
            display:none;
            font-size: 14px;
            text-align: center;
            background: white;
            position: absolute;
            cursor: pointer;
            user-select: none;
            border: 1px solid #505050;
        }
        .right-btn-item>div{
            height:25px;
            box-sizing: border-box;
            border-top: 1px solid #f6f1f1;
        }
        .right-btn-item>div:hover{
            background: lavender;
        }
        .item-img-type1>div{
            float:left;
            width:50%;
        }
        .item-img-type1>.name-img{
            height:100%;
        }
        .item-img-type1>.signed-time,.item-img-type1>.signed-remark{
            height:50%;
        }
        .item-img-type2>.name-img,.item-img-type2>.signed-time{
            float:left;
            width:50%;
        }
        .item-img-type2>.name-img{
            height:66.7%;
        }
        .item-img-type2>.signed-time{
            margin-top: 12.5%;
            height:33.3%;
        }
        .item-img-type2>.signed-remark{
            float: left;
            width: 100%;
            height:33.3%;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wffilesignprocess/preView#toolbar=0" method="post" target="prePdf">
    <input id="ids" name="ids" type="hidden" value="${wffilesignprocessEntity.id }"/>
    <input id="empNo111" name="empNo111" type="hidden" value="<shiro:principal property="loginName"/>"/>
    <input id="name111" name="name111" type="hidden" value="<shiro:principal property="name"/>"/>
    <input id="serialno" name="serialno" type="hidden" value="${wffilesignprocessEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wffilesignprocessEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wffilesignprocessEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wffilesignprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${wffilesignprocessEntity.makerfactoryid }"/>
    <input id="source" name="source" type="hidden" value="${wffilesignprocessEntity.source }"/>
    <div class="commonW">
        <div class="headTitle">文檔簽核申請表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wffilesignprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wffilesignprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wffilesignprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wffilesignprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wffilesignprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wffilesignprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wffilesignprocessEntity.makerno}/${wffilesignprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">提報人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">提報人工號&nbsp;<font color="red">*</font></td>
                            <td width="4%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wffilesignprocessEntity.applyno}" onblur="queryUserInfo('apply');"/>
                            </td>
                            <td width="5%">提報人</td>
                            <td width="5%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wffilesignprocessEntity.applyname }"/>
                            </td>
                            <td width="5%">單位代碼</td>
                            <td width="5%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wffilesignprocessEntity.applydeptno }"/>
                            </td>
                            <td width="5%">費用代碼</td>
                            <td width="5%" class="td_style1">
                                <input id="applycostno" name="applycostno" class="easyui-validatebox inputCss"
                                       data-options="width: 80"
                                       value="${wffilesignprocessEntity.applycostno }"/>
                            </td>
                            <td width="5%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wffilesignprocessEntity.applyfactoryid }"
                                       data-options="width: 120,validType:'comboxValidate[\'applyfactoryid\',\'请選擇所在廠區\']',onSelect:function(){onchangeFactory('apply');}"/>
                                <input id="applyfactoryname" name="applyfactoryname" type="hidden" value="${wffilesignprocessEntity.applyfactoryname }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox" data-options="width: 400,required:true"
                                       value="${wffilesignprocessEntity.applydeptname }"/>
                            </td>
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applytel" name="applytel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wffilesignprocessEntity.applytel}" data-options="required:true"/>
                            </td>
                            <td>緊急程度</td>
                            <td colspan="3">
                                <label><input type="radio" name="urgencyLevelRadio" value="0"/>特急</label>
                                <label><input type="radio" name="urgencyLevelRadio" value="1"/>緊急</label>
                                <label><input type="radio" name="urgencyLevelRadio" value="2" checked/>一般</label>
                                <input type="hidden" name="urgencyLevel" value="2"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>呈簽表單名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applytablename" name="applytablename" class="easyui-validatebox"
                                       data-options="width: 400,required:true"
                                       value="${wffilesignprocessEntity.applytablename }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       value="${wffilesignprocessEntity.applyemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td>表單類型<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="formtype" name="formtype" class="easyui-combobox"
                                       value="${wffilesignprocessEntity.formtype}" panelHeight="auto"
                                       data-options="width: 120,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/APPLY_FORM_TYPE',onLoadSuccess: function () { var data = $('#formtype').combobox('getData');if (data.length > 0) {$('#formtype').combobox('select', data[0].value);}}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>呈簽說明</td>
                            <td colspan="9" class="td_style1">
                                <textarea id="applyexplain" name="applyexplain"
                                          class="easyui-validatebox" style="width:99%;height:60px;"
                                          rows="5"
                                          cols="3">${wffilesignprocessEntity.applyexplain}</textarea><span
                                    id="txtNum"></span>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>签核文檔&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachids2Upload" name="attachids2Upload" type="file"
                                           onchange="uploadSignedPdf('attachids2','dowloadUrl2');" class="ui-input-file"
                                           accept="application/pdf"/>
								</span>
                                <input type="hidden" id="attachids2" name="attachids2" value="${wffilesignprocessEntity.attachids2 }"/>
                                <div id="dowloadUrl2">
                                    <c:forEach items="${file2}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <c:if test="${wffilesignprocessEntity.serialno!=null }">
                                                    <fox:hasPermssion name="${wffilesignprocessEntity.serialno }">
                                                        <a href="${ctx}/fileSignUpload/fileSignDownloadEncryptFtps/${item.id}">${item.name}</a>
                                                    </fox:hasPermssion>
                                                    <fox:hasNotPermssion name="${wffilesignprocessEntity.serialno }">
                                                        <a href="${ctx}/fileSignUpload/fileSignDownloadEncryptFtps/${item.id}" title="您無權限查看，請聯繫管理員添加ip權限" onclick="javascript:return false;">${item.name}</a>
                                                    </fox:hasNotPermssion>
                                                </c:if>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAttFile('attachids2','${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                            <td>加密密碼&nbsp</td>
                            <td colspan="3" class="td_style1">
                                <input id="filepassword" name="filepassword"
                                       class="easyui-validatebox"
                                       data-options="width:400,prompt:'文檔將被加密，簽核人需輸入密碼才能審核單據'"
                                       value=""/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachids1Upload" name="attachids1Upload" type="file"
                                           onchange="uploadEncryptFile11('attachids1','dowloadUrl1');"
                                           class="ui-input-file" accept="application/pdf"/>
								</span>
                                <input type="hidden" id="attachids1" name="attachids1" value="${wffilesignprocessEntity.attachids1}"/>
                                <div id="dowloadUrl1">
                                    <c:forEach items="${file1}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <fox:hasPermssion name="${wffilesignprocessEntity.serialno }">
                                                    <a href="${ctx}/fileSignUpload/fileSignDownloadEncryptFtps/${item.id}">${item.name}</a>
                                                </fox:hasPermssion>
                                                <fox:hasNotPermssion name="${wffilesignprocessEntity.serialno }">
                                                    <a href="${ctx}/fileSignUpload/fileSignDownloadEncryptFtps/${item.id}" title="您無權限查看，請聯繫管理員添加ip權限" onclick="javascript:return false;">${item.name}</a>
                                                </fox:hasNotPermssion>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAttFile('attachids1','${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="9" class="td_style2">
                                <font color="red">1.簽核文檔類型需為PDF檔且小於20M，文檔小於8M，單據簽核完成後可郵件直接下載，大於8M只能在網站下載；</font><br/>
                                2.簽核路徑設定時，一個部門設置到一個會簽部門節點中；<br/>
                                3.“批註”欄位設置超過10字僅可在App/PC端簽核記錄內查看批註內容，呈簽檔不顯示其內容。
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList" >
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_wendangqianheshenqingdan','文檔簽核申請表','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%" id="signList">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hq1chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽部門1
                                                                    <a href="#" onclick="addHq2('hq1charge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hq1chargeno" name="hq1chargeno" onblur="gethqUserNameByEmpno(this,'hq1charge');"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hq1chargeno']}"
                                                               value="${wffilesignprocessEntity.hq1chargeno }"/><c:if
                                                            test="${requiredMap['hq1chargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hq1chargename" name="hq1chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hq1chargeno']}"
                                                                value="${wffilesignprocessEntity.hq1chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="hq2chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽部門2
                                                                    <a href="#" onclick="addHq2('hq2charge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hq2chargeno" name="hq2chargeno" onblur="gethqUserNameByEmpno(this,'hq2charge');"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hq2chargeno']}"
                                                               value="${wffilesignprocessEntity.hq2chargeno }"/><c:if
                                                            test="${requiredMap['hq2chargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hq2chargename" name="hq2chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hq2chargeno']}"
                                                                value="${wffilesignprocessEntity.hq2chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="hq3chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽部門3
                                                                    <a href="#" onclick="addHq2('hq3charge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hq3chargeno" name="hq3chargeno" onblur="gethqUserNameByEmpno(this,'hq3charge');"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hq3chargeno']}"
                                                               value="${wffilesignprocessEntity.hq3chargeno }"/><c:if
                                                            test="${requiredMap['hq3chargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hq3chargename" name="hq3chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hq3chargeno']}"
                                                                value="${wffilesignprocessEntity.hq3chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="hq4chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽部門4
                                                                    <a href="#" onclick="addHq2('hq4charge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hq4chargeno" name="hq4chargeno" onblur="gethqUserNameByEmpno(this,'hq4charge');"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hq4chargeno']}"
                                                               value="${wffilesignprocessEntity.hq4chargeno }"/><c:if
                                                            test="${requiredMap['hq4chargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hq4chargename" name="hq4chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hq4chargeno']}"
                                                                value="${wffilesignprocessEntity.hq4chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="hq5chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽部門5
                                                                    <a href="#" onclick="addHq2('hq5charge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hq5chargeno" name="hq5chargeno" onblur="gethqUserNameByEmpno(this,'hq5charge');"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hq5chargeno']}"
                                                               value="${wffilesignprocessEntity.hq5chargeno }"/><c:if
                                                            test="${requiredMap['hq5chargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hq5chargename" name="hq5chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hq5chargeno']}"
                                                                value="${wffilesignprocessEntity.hq5chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hq6chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽部門6
                                                                    <a href="#" onclick="addHq2('hq6charge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hq6chargeno" name="hq6chargeno" onblur="gethqUserNameByEmpno(this,'hq6charge');"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hq6chargeno']}"
                                                               value="${wffilesignprocessEntity.hq6chargeno }"/><c:if
                                                            test="${requiredMap['hq6chargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hq6chargename" name="hq6chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hq6chargeno']}"
                                                                value="${wffilesignprocessEntity.hq6chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="hq7chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽部門7
                                                                    <a href="#" onclick="addHq2('hq7charge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hq7chargeno" name="hq7chargeno" onblur="gethqUserNameByEmpno(this,'hq7charge');"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hq7chargeno']}"
                                                               value="${wffilesignprocessEntity.hq7chargeno }"/><c:if
                                                            test="${requiredMap['hq7chargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hq7chargename" name="hq7chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hq7chargeno']}"
                                                                value="${wffilesignprocessEntity.hq7chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="hq8chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽部門8
                                                                    <a href="#" onclick="addHq2('hq8charge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hq8chargeno" name="hq8chargeno" onblur="gethqUserNameByEmpno(this,'hq8charge');"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hq8chargeno']}"
                                                               value="${wffilesignprocessEntity.hq8chargeno }"/><c:if
                                                            test="${requiredMap['hq8chargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hq8chargename" name="hq8chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hq8chargeno']}"
                                                                value="${wffilesignprocessEntity.hq8chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="hq9chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽部門9
                                                                    <a href="#" onclick="addHq2('hq9charge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hq9chargeno" name="hq9chargeno" onblur="gethqUserNameByEmpno(this,'hq9charge');"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hq9chargeno']}"
                                                               value="${wffilesignprocessEntity.hq9chargeno }"/><c:if
                                                            test="${requiredMap['hq9chargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hq9chargename" name="hq9chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hq9chargeno']}"
                                                                value="${wffilesignprocessEntity.hq9chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="hq10chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽部門10
                                                                    <a href="#" onclick="addHq2('hq10charge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hq10chargeno" name="hq10chargeno" onblur="gethqUserNameByEmpno(this,'hq10charge');"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hq10chargeno']}"
                                                               value="${wffilesignprocessEntity.hq10chargeno }"/><c:if
                                                            test="${requiredMap['hq10chargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hq10chargename" name="hq10chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hq10chargeno']}"
                                                                value="${wffilesignprocessEntity.hq10chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
</form>


<!-- 審核模板創建begin --->
<div class="approval-template">

    <div class="right-pannel">
        <div class="right-head">
            <input type="button" value="第一頁" onclick="firstPage()" class="btn-page"/>
            <input type="button" value="上一頁" onclick="prewPage()" class="btn-page"/>
            共<span id="totalePage"></span>頁
            當前第<input id="nowPage"  type="text" class="page-input"  />頁
            <input type="button" value="下一頁" onclick="nextPage()" class="btn-page"/>
            <input type="button" value="最後一頁" onclick="lastPage()" class="btn-page"/>
        </div>
        <div class="content-box">
            <div class="right-content">
                <img id="approvalTemplate" class="template-img" src=""/>
                <div class="right-btn-item">
                    <div style="border-top:0;">刪除</div>
                   <%-- <div>簽名、時間、備註豎直顯示</div>
                    <div>簽名與時間、備註水平顯示</div>
                    <div>簽核時間居右備註居下顯示</div>--%>
                    <div>三行</div>
                    <div>兩行</div>
                    <div>一行</div>
                </div>
            </div>
        </div>
    </div>  <!--right pannel-->
    <div class="left-pannel">
        <div class="left-head"></div>
        <div class="left-content">
            <div class="content-roll">

            </div>
        </div>
    </div>  <!-- left pannel-->
    <div class="footer-btn">
        <a href="javascript:;" id="kkk" class="easyui-linkbutton"
           data-options="iconCls:'icon-add'"
           style="width: 100px;" onclick="saveAll();">確定</a>&nbsp;&nbsp;&nbsp;&nbsp;
        <a href="javascript:;"  class="easyui-linkbutton"
           data-options="iconCls:'icon-hamburg-left'"
           style="width: 100px;" onclick="returnBack();">返回</a>
    </div>
</div>

<div class="pre-view">
    <iframe class="pre-pdf" name="prePdf"></iframe>
    <div class="sub-bar">
        <input type="button" value="呈簽" onclick="sub()" class="btn-page">
        <input type="button" value="返回" onclick="closePreView()" class="btn-page">
    </div>
</div>
<!-- 審核模板創建end --->

<script type="text/javascript">
    if ("${wffilesignprocessEntity.hq1chargeno}" != "") {
        var nostr = "${wffilesignprocessEntity.hq1chargeno}";
        var namestr = "${wffilesignprocessEntity.hq1chargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hq1chargeTable tr:eq(" + (i + 2) + ")").find("#hq1chargeno").val(notr[i]);
            $("#hq1chargeTable tr:eq(" + (i + 2) + ")").find("#hq1chargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hq1chargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hq1chargeno' name='hq1chargeno' style='width: 80px' value='' onblur=\"gethqUserNameByEmpno(this,'hq1charge');\"/>/<input id='hq1chargename' name='hq1chargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wffilesignprocessEntity.hq2chargeno}" != "") {
        var nostr = "${wffilesignprocessEntity.hq2chargeno}";
        var namestr = "${wffilesignprocessEntity.hq2chargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hq2chargeTable tr:eq(" + (i + 2) + ")").find("#hq2chargeno").val(notr[i]);
            $("#hq2chargeTable tr:eq(" + (i + 2) + ")").find("#hq2chargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hq2chargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hq2chargeno' name='hq2chargeno' style='width: 80px' value='' onblur=\"gethqUserNameByEmpno(this,'hq2charge');\"/>/<input id='hq2chargename' name='hq2chargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wffilesignprocessEntity.hq3chargeno}" != "") {
        var nostr = "${wffilesignprocessEntity.hq3chargeno}";
        var namestr = "${wffilesignprocessEntity.hq3chargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hq3chargeTable tr:eq(" + (i + 2) + ")").find("#hq3chargeno").val(notr[i]);
            $("#hq3chargeTable tr:eq(" + (i + 2) + ")").find("#hq3chargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hq3chargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hq3chargeno' name='hq3chargeno' style='width: 80px' value='' onblur=\"gethqUserNameByEmpno(this,'hq3charge');\"/>/<input id='hq3chargename' name='hq3chargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wffilesignprocessEntity.hq4chargeno}" != "") {
        var nostr = "${wffilesignprocessEntity.hq4chargeno}";
        var namestr = "${wffilesignprocessEntity.hq4chargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hq4chargeTable tr:eq(" + (i + 2) + ")").find("#hq4chargeno").val(notr[i]);
            $("#hq4chargeTable tr:eq(" + (i + 2) + ")").find("#hq4chargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hq4chargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hq4chargeno' name='hq4chargeno' style='width: 80px' value='' onblur=\"gethqUserNameByEmpno(this,'hq4charge');\"/>/<input id='hq4chargename' name='hq4chargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wffilesignprocessEntity.hq5chargeno}" != "") {
        var nostr = "${wffilesignprocessEntity.hq5chargeno}";
        var namestr = "${wffilesignprocessEntity.hq5chargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hq5chargeTable tr:eq(" + (i + 2) + ")").find("#hq5chargeno").val(notr[i]);
            $("#hq5chargeTable tr:eq(" + (i + 2) + ")").find("#hq5chargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hq5chargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hq5chargeno' name='hq5chargeno' style='width: 80px' value='' onblur=\"gethqUserNameByEmpno(this,'hq5charge');\"/>/<input id='hq5chargename' name='hq5chargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wffilesignprocessEntity.hq6chargeno}" != "") {
        var nostr = "${wffilesignprocessEntity.hq6chargeno}";
        var namestr = "${wffilesignprocessEntity.hq6chargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hq6chargeTable tr:eq(" + (i + 2) + ")").find("#hq6chargeno").val(notr[i]);
            $("#hq6chargeTable tr:eq(" + (i + 2) + ")").find("#hq6chargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hq6chargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hq6chargeno' name='hq6chargeno' style='width: 80px' value='' onblur=\"gethqUserNameByEmpno(this,'hq6charge');\"/>/<input id='hq6chargename' name='hq6chargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wffilesignprocessEntity.hq7chargeno}" != "") {
        var nostr = "${wffilesignprocessEntity.hq7chargeno}";
        var namestr = "${wffilesignprocessEntity.hq7chargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hq7chargeTable tr:eq(" + (i + 2) + ")").find("#hq7chargeno").val(notr[i]);
            $("#hq7chargeTable tr:eq(" + (i + 2) + ")").find("#hq7chargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hq7chargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hq7chargeno' name='hq7chargeno' style='width: 80px' value='' onblur=\"gethqUserNameByEmpno(this,'hq7charge');\"/>/<input id='hq7chargename' name='hq7chargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wffilesignprocessEntity.hq8chargeno}" != "") {
        var nostr = "${wffilesignprocessEntity.hq8chargeno}";
        var namestr = "${wffilesignprocessEntity.hq8chargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hq8chargeTable tr:eq(" + (i + 2) + ")").find("#hq8chargeno").val(notr[i]);
            $("#hq8chargeTable tr:eq(" + (i + 2) + ")").find("#hq8chargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hq8chargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hq8chargeno' name='hq8chargeno' style='width: 80px' value='' onblur=\"gethqUserNameByEmpno(this,'hq8charge');\"/>/<input id='hq8chargename' name='hq8chargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wffilesignprocessEntity.hq9chargeno}" != "") {
        var nostr = "${wffilesignprocessEntity.hq9chargeno}";
        var namestr = "${wffilesignprocessEntity.hq9chargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hq9chargeTable tr:eq(" + (i + 2) + ")").find("#hq9chargeno").val(notr[i]);
            $("#hq9chargeTable tr:eq(" + (i + 2) + ")").find("#hq9chargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hq9chargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hq9chargeno' name='hq9chargeno' style='width: 80px' value='' onblur=\"gethqUserNameByEmpno(this,'hq9charge');\"/>/<input id='hq9chargename' name='hq9chargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wffilesignprocessEntity.hq10chargeno}" != "") {
        var nostr = "${wffilesignprocessEntity.hq10chargeno}";
        var namestr = "${wffilesignprocessEntity.hq10chargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hq10chargeTable tr:eq(" + (i + 2) + ")").find("#hq10chargeno").val(notr[i]);
            $("#hq10chargeTable tr:eq(" + (i + 2) + ")").find("#hq10chargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hq10chargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hq10chargeno' name='hq10chargeno' style='width: 80px' value='' onblur=\"gethqUserNameByEmpno(this,'hq10charge');\"/>/<input id='hq10chargename' name='hq10chargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
</script>
</body>
</html>
