<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>iPEBG事業群聯絡單</title>
	<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
    </script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<%@ include file="/WEB-INF/views/include/kindeditor.jsp" %>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
	type="text/javascript"></script>
</head>
<body>
	<form id="mainform" action="${ctx}/wfcaaliaison/${action}" method="post">
		<input id="ids" name="ids" type="hidden"
			value="${wfIpebgLiaisonEntity.id }" /> <input id="serialno"
			name="serialno" type="hidden" value="${wfIpebgLiaisonEntity.serialno }" />
        <input id="makerno" name="makerno" type="hidden" value="${wfIpebgLiaisonEntity.makerno }"/>
        <input id="makername" name="makername" type="hidden" value="${wfIpebgLiaisonEntity.makername }"/>
        <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfIpebgLiaisonEntity.makerdeptno }"/>
		<div class="commonW">
			<div class="headTitle">iPEBG事業群聯絡單</div>
			<div class="position_L">
				任務編碼：<span style="color:#999;"> <c:choose>
						<c:when test="${wfIpebgLiaisonEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
						<c:otherwise>
                           ${wfIpebgLiaisonEntity.serialno}
                       </c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_L1 margin_L">
				填單時間：<span style="color:#999;"> <c:choose>
						<c:when test="${wfIpebgLiaisonEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
						<c:otherwise>
                            <fmt:formatDate value='${wfIpebgLiaisonEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>
						</c:otherwise>
					</c:choose>
				</span>
			</div>
            <c:if test="${empty wfIpebgLiaisonEntity.makerno}">
                <div class="position_R margin_R">填單人：<span style="color:#999;">${user.loginName}/${user.name}</span></div>
            </c:if>
            <c:if test="${not empty wfIpebgLiaisonEntity.makerno}">
                <div class="position_R margin_R">填單人：<span style="color:#999;">${wfIpebgLiaisonEntity.makerno}/${wfIpebgLiaisonEntity.makername}</span></div>
            </c:if>
			<div class="clear"></div>
			<table class="formList">
				<tr>
					<td>
						<table class="formList">
						 <tr align="center" style="display:none">
								<td colspan="2">承辦人工號&nbsp;<font color="red">*</font></td>
								<td><input id="dealno" name="dealno"
									class="easyui-validatebox inputCss"
									data-options="width: 150"
									value="${user.loginName}" /></td>
								<td>承辦人&nbsp;<font color="red">*</font></td>
								<td><input id="dealname" name="dealname" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfIpebgLiaisonEntity.dealname }" /></td>
								<td  colspan="2">單位代碼&nbsp;<font color="red">*</font></td>
								<td><input id="dealdeptno" name="dealdeptno" readonly
									class="easyui-validatebox inputCss" data-options="width: 150"
									value="${wfIpebgLiaisonEntity.dealdeptno }" /></td>
								<td>廠區&nbsp;<font color="red">*</font></td>
								<td><input id="dealfactoryid" name="dealfactoryid"
									class="easyui-combobox"
									data-options="width: 150"
									value="${wfIpebgLiaisonEntity.dealfactoryid }" />
								</td>
							</tr>
						</table>
						<table class="formList">
						 <tr align="center">
						    <td style="width:80px">表單類型&nbsp;<font color="red">*</font></td>
						    <td colspan="9">
						        <%--<div class="liaisonTypeDiv"></div>
                                <input id="liaisonType" name="liaisonType" class="easyui-validatebox" data-options="width: 150" type="hidden" value="${wfIpebgLiaisonEntity.liaisonType }" />
                                <input id="liaisonTypeQitamemo1" name="liaisonTypeQitamemo1" disabled="disabled" type="hidden" class="easyui-validatebox" data-options="width: 30" value="${wfIpebgLiaisonEntity.liaisonTypeQitamemo}" />--%>
                                <input type="radio" name="formtypes" class="formTypes" id="formtype1" onclick="isCheck1(this)" value="${wfIpebgLiaisonEntity.formtype}"
                                        <c:if test="${wfIpebgLiaisonEntity.formtype=='1'}">
                                            checked
                                        </c:if>
                                /><span>資訊類</span>&nbsp;&nbsp;&nbsp;&nbsp;
                                <input id="liaisonType" name="liaisonType" class="easyui-combobox"
                                       value="${wfIpebgLiaisonEntity.liaisonType }" panelHeight="auto" editable="false" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadDevicetype();}" />
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <input type="radio" name="formtypes" class="formTypes" id="formtype2" onclick="isCheck2(this)" value="${wfIpebgLiaisonEntity.formtype}"
                                        <c:if test="${wfIpebgLiaisonEntity.formtype=='2'}">
                                            checked
                                        </c:if>
                                /><span>其他</span>
                                <input id='liaisonTypeQitamemo' name='liaisonTypeQitamemo' class='easyui-validatebox' data-options='width: 200'/>
                                <input id="formtype" name="formtype" type="hidden" value="${wfIpebgLiaisonEntity.formtype}"/>

                            </td>
						 </tr>
						 <tr align="center">
						 <td>呈   核&nbsp;<font color="red">*</font></td>
						 <td colspan="4" align="left"><input id="recipientUnit" name="recipientUnit" maxlength="20"
						 class="easyui-validatebox" data-options="width:450 ,required:true"
						 value="${wfIpebgLiaisonEntity.recipientUnit }" /></td>
						 <td>發文單位&nbsp;<font color="red">*</font></td>
						 <td colspan="4" align="left"><input id="dispatchUnit" name="dispatchUnit" maxlength="20"
						 class="easyui-validatebox" data-options="width: 450,required:true"
						 value="${wfIpebgLiaisonEntity.dispatchUnit }" /></td>
						 </tr>
						 <tr align="center">
						 <td>會審單位</td>
						 <td colspan="4" align="left"><input id="organizerUnit" name="organizerUnit" maxlength="20"
						 class="easyui-validatebox" data-options="width:450"
						 value="${wfIpebgLiaisonEntity.organizerUnit }" /></td>
						 <td>發文字號&nbsp;<font color="red">*</font></td>
						 <td colspan="4" align="left"><input id="issueNumber" name="issueNumber" maxlength="40"
						 class="easyui-validatebox" data-options="width: 450,required:true"
						 value="${wfIpebgLiaisonEntity.issueNumber }" /></td>
						 </tr>
						 <tr align="center">						
                         <td>受文單位</td>
						 <td colspan="4" align="left"><input id="copySubmitted" name="copySubmitted" maxlength="100"
						 class="easyui-validatebox" data-options="width: 450 "
						 value="${wfIpebgLiaisonEntity.copySubmitted }" /></td>
						 <td>發文日期&nbsp;<font color="red">*</font></td>
						 <td colspan="4" align="left"><input id="dealtime" name="dealtime" class="Wdate"
									data-options="width:300,required:true" style="width:180px"
									value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${wfIpebgLiaisonEntity.dealtime}"/>"
									onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
						 </td>
						 </tr>
						 <tr align="center">						
                         <td>主旨&nbsp;<font color="red">*</font></td>
                         <td colspan="9" class="td_style1"><input id="purpose" name="purpose" maxlength="50"
						 class="easyui-validatebox" data-options="width: 1050,required:true"
						 value="${wfIpebgLiaisonEntity.purpose }" /></td>
                         </tr>
                         <%-- <tr align="center">						
                         <td>詳細說明<font color="red">*</font></td>
                         <td colspan="9">
                         <input id="detailMemo" name="detailMemo" 
						 class="easyui-validatebox" data-options="width: 450"
						 value="${wfIpebgLiaisonEntity.detailMemo }" />
						 		<textarea style="width:1050px;height:650px;visibility:hidden;" id="detailMemo" name="detailMemo" class="dd">value="${wfIpebgLiaisonEntity.detailMemo }"</textarea>				 
						 </td>
                         </tr> --%>
                         <tr align="center">						
                         <td>詳細說明</td>
                         <td colspan="9" class="td_style1">
                         <textarea style="width:1120px;height:650px;visibility:hidden;border:0px" id="detailMemo" name="detailMemo" class="dd">${wfIpebgLiaisonEntity.detailMemo}</textarea>		 
						 </td>
                         </tr>
						<!-- </table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList"> -->
							<tr align="center">
								<td>附件</td>
								<td colspan="9" class="td_style1">
                                    <span class="sl-custom-file">
                                        <input type="button" value="点击上传文件" class="btn-file" />
                                        <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file" />
								    </span>
                                    <input type="hidden" id="attachids" name="attachids" value="${wfIpebgLiaisonEntity.attachids }" />
									<div id="dowloadUrl">
										<c:forEach items="${file}" varStatus="i" var="item">
											<div id="${item.id}"
												style="line-height:30px;margin-left:5px;" class="float_L">
												<div class="float_L">
													<a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
												</div>
												<div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
											</div>
										</c:forEach>
									</div></td>
							</tr>
							<tr>
								<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
									<a href="javascript:void(0)"
									onclick="showWfImag('${workFlowId}','iPEBG事業群聯絡單','');">點擊查看簽核流程圖</a>
								</th>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
                <td colspan="10" style="text-align:left;">
                    <table class="flowList"
                           style="margin-left:5px;margin-top:5px;width:99%">
                        <tr>
                            <td style="border:none">
                                <table width="16%" style="float: left;margin-left: 5px;">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="kchargeno" name="kchargeno" class="easyui-validatebox" data-options="width:80,required:${requiredMap['kchargeno']}"
                                                   readonly value="${wfIpebgLiaisonEntity.kchargeno }"/>
                                            <c:if test="${requiredMap['kchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="kchargename" name="kchargename"
                                                    readonly class="easyui-validatebox"
                                                    data-options="width:80,required:${requiredMap['kchargeno']}"
                                                    value="${wfIpebgLiaisonEntity.kchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="16%" style="float: left;margin-left: 5px;">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="bchargeno" name="bchargeno" class="easyui-validatebox" data-options="width: 80,required:${requiredMap['bchargeno']}"
                                                   readonly value="${wfIpebgLiaisonEntity.bchargeno }"/>
                                            <c:if test="${requiredMap['bchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="bchargename" name="bchargename" class="easyui-validatebox" data-options="width: 80,required:${requiredMap['bchargeno']}"
                                                    readonly value="${wfIpebgLiaisonEntity.bchargename }"/></td>
                                    </tr>
                                </table>
                                <table width="16%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole18('cchargeTable',$('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="cchargeno" name="cchargeno" class="easyui-validatebox" data-options="width: 80,required:${requiredMap['cchargeno']}" readonly
                                                   value="${wfIpebgLiaisonEntity.cchargeno }"/>
                                            <c:if test="${requiredMap['cchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="cchargename" name="cchargename" class="easyui-validatebox" data-options="width: 80,required:${requiredMap['cchargeno']}" readonly
                                                    value="${wfIpebgLiaisonEntity.cchargename }"/></td>
                                    </tr>
                                </table>
                                <table width="16%" style="float: left;margin-left: 5px;"
                                       id="zchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="zchargeno" name="zchargeno" class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zchargeno']}"
                                                   readonly value="${wfIpebgLiaisonEntity.zchargeno }"/>
                                            <c:if test="${requiredMap['zchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="zchargename" name="zchargename" class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zchargeno']}"
                                                    readonly value="${wfIpebgLiaisonEntity.zchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="16%" style="float: left;margin-left: 5px;"
                                       id="hchargenoTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: center;">${requiredMap['hchargeno_name']}<a
                                                            href="javascript:void(0);"  onclick="addHq();">添加一位</a></td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="hchargeno" onblur="getUserNameByEmpno(this);" 
                                                   name="hchargeno" class="easyui-validatebox" data-options="width: 70,required:${requiredMap['hchargeno']}"
                                                   value="${wfIpebgLiaisonEntity.hchargeno }"/>
                                            <c:if test="${requiredMap['hchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="hchargename" name="hchargename" class="easyui-validatebox"
                                                    data-options="width: 55,required:${requiredMap['hchargeno']}"  readonly  value="${wfIpebgLiaisonEntity.hchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="16%" style="float: left;margin-left: 5px;"
                                       id="zcchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="zcchargeno" name="zcchargeno" class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zcchargeno']}"
                                                   readonly value="${wfIpebgLiaisonEntity.zcchargeno }"/>
                                            <c:if test="${requiredMap['zcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="zcchargename" name="zcchargename" class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zcchargeno']}"
                                                    readonly value="${wfIpebgLiaisonEntity.zcchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td style="border:none">
                                <table width="16%" style="float: left;margin-left: 5px;"
                                       id="pcchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole3('pcchargeTable',$('#dealdeptno').val(),'pcchargeno','pcchargename',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="pcchargeno" name="pcchargeno" class="easyui-validatebox" data-options="width: 80,required:${requiredMap['pcchargeno']}" readonly
                                                   value="${wfIpebgLiaisonEntity.pcchargeno }"/>
                                            <c:if test="${requiredMap['pcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            / <input id="pcchargename" name="pcchargename" class="easyui-validatebox" data-options="width: 80,required:${requiredMap['pcchargeno']}"
                                                    readonly value="${wfIpebgLiaisonEntity.pcchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="16%" style="float: left;margin-left: 5px;" id="sgqhchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['sgqhchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon" class="easyui-validatebox"
                                                             data-options="width: 80"
                                                             onclick="selectRole2(653,'sgqhchargeTable','sgqhchargeno','sgqhchargename',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="sgqhchargeno" name="sgqhchargeno" class="easyui-validatebox" data-options="width:80,required:${requiredMap['sgqhchargeno']}"
                                                   readonly value="${wfIpebgLiaisonEntity.sgqhchargeno }"/>
                                            <c:if test="${requiredMap['sgqhchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="sgqhchargename" name="sgqhchargename"
                                                    readonly class="easyui-validatebox"
                                                    data-options="width:80,required:${requiredMap['sgqhchargeno']}"
                                                    value="${wfIpebgLiaisonEntity.sgqhchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="16%" style="float: left;margin-left: 5px;" id="rzchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%" id="hrcheckchargeTable">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['hrcheckchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon" class="easyui-validatebox"
                                                             data-options="width: 80"
                                                             onclick="selectRole4(156,'hrcheckchargeno','hrcheckchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="hrcheckchargeno" name="hrcheckchargeno"
                                                   class="easyui-validatebox" data-options="width:80,required:${requiredMap['hrcheckchargeno']}"
                                                   readonly value="${wfIpebgLiaisonEntity.hrcheckchargeno }"/>
                                            <c:if test="${requiredMap['hrcheckchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            /<input id="hrcheckchargename" name="hrcheckchargename"
                                                    readonly class="easyui-validatebox"
                                                    data-options="width:80,required:${requiredMap['hrcheckchargeno']}"
                                                    value="${wfIpebgLiaisonEntity.hrcheckchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="16%" style="float: left;margin-left: 5px;"
                                       id="pqchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['pqchargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(28,'pqchargeTable','pqchargeno','pqchargename',$('#dealfactoryid').combobox('getValue'),null)"></div>   
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input id="pqchargeno"  name="pqchargeno" class="easyui-validatebox" data-options="width: 80,required:${requiredMap['pqchargeno']}"
                                                   readonly  value="${wfIpebgLiaisonEntity.pqchargeno }"/>
                                            <c:if test="${requiredMap['pqchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            / <input id="pqchargename" name="pqchargename" class="easyui-validatebox" data-options="width: 80,required:${requiredMap['pqchargeno']}"
                                                     readonly value="${wfIpebgLiaisonEntity.pqchargename }"/>
                                        </td>
                                    </tr>
                                </table>
                                <table width="16%" style="float: left;margin-left: 5px;"
                                       id="caacchargeTable">
                                    <tr>
                                        <td>
                                            <table width="100%">
                                                <tr>
                                                    <td style="border: none;text-align: right;">${requiredMap['caachargeno_name']}</td>
                                                    <td style="border: none;">
                                                        <div class="float_L qhUserIcon"
                                                             onclick="selectRole2(29,'caacchargeTable','caachargeno','caachargename',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input id="caachargeno"  name="caachargeno" class="easyui-validatebox" data-options="width: 80,required:${requiredMap['caachargeno']}"
                                                   readonly value="${wfIpebgLiaisonEntity.caachargeno }"/>
                                            <c:if test="${requiredMap['caachargeno'].equals('true')}"><font color="red">*</font></c:if>
                                            / <input id="caachargename" name="caachargename"
                                                     class="easyui-validatebox" data-options="width: 80,required:${requiredMap['caachargeno']}" readonly
                                                     value="${wfIpebgLiaisonEntity.caachargename }"/></td>
                                    </tr>
                                </table>
                              </td>
                        </tr>
                    </table>
                </td>
            </tr>
				<tr>
					<td colspan="10" style="text-align:left;">
						<table class="flowList"
							style="margin-left:5px;margin-top:5px;width:99%">
						</table>
					</td>
				</tr>

				<tr>
					<td colspan="10" style="text-align:left;">
						<table class="flowList"
							style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
							<tr>
								<td>簽核時間</td>
								<td>簽核節點</td>
								<td>簽核主管</td>
								<td>簽核意見</td>
								<td>批註</td>
								<td>簽核電腦IP</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td colspan="10"
						style="border:none;text-align:center;margin-top:10px"><a
						href="javascript:;" id="btnSave" class="easyui-linkbutton"
						data-options="iconCls:'icon-add'" style="width: 100px;"
						onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp; <a
						href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
						data-options="iconCls:'icon-ok'" style="width: 100px;"
						onclick="saveInfo(2);">提交</a></td>
				</tr>
			</table>
		</div>
		<input type="hidden" id="deptNo" name="deptNo" value="" /> <input
			type="hidden" id="chargeNo" name="chargeNo" value="" /> <input
			type="hidden" id="chargeName" name="chargeName" value="" /> <input
			type="hidden" id="factoryId" name="factoryId" value="" /> <input
			type="hidden" id="dutyId" name="dutyId" value="" />
        <input type="hidden" id="onlyKchargeSignle" value="1" />
		<div id="win"></div>
	</form>
	<script
		src='${ctx}/static/js/information/wfipebgliaison.js?random=<%= Math.random()%>'></script>
</body>
</html>
<%!
private String htmlspecialchars(String str) {
	str = str.replaceAll("&", "&amp;");
	str = str.replaceAll("<", "&lt;");
	str = str.replaceAll(">", "&gt;");
	str = str.replaceAll("\"", "&quot;");
	return str;
}
%>