<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>iPEBG事業群聯絡單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <style type="text/css" media='print'>
        .noprint {
            display: none;
        }

        .printAll {
            width: 80%;
        }
    </style>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <%@ include file="/WEB-INF/views/include/kindeditor.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfcaaliaison/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfIpebgLiaisonEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfIpebgLiaisonEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">iPEBG事業群聯絡單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfIpebgLiaisonEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfIpebgLiaisonEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfIpebgLiaisonEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <fmt:formatDate value='${wfIpebgLiaisonEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：<span
                style="color:#999;">${wfIpebgLiaisonEntity.makerno}/${wfIpebgLiaisonEntity.makername}</span></div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td style="width:80px">表單類型</td>
                            <td colspan="9">
                                <c:if test="${wfIpebgLiaisonEntity.formtype=='1'}">資訊類:
                                    <input id="liaisonType" name="liaisonType" class="easyui-combobox"
                                           value="${wfIpebgLiaisonEntity.liaisonType }" panelHeight="auto"
                                           editable="false" disabled
                                           data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadDevicetype();}"/>
                                </c:if>
                                <c:if test="${wfIpebgLiaisonEntity.formtype=='2'}">其他:
                                    ${wfIpebgLiaisonEntity.liaisonTypeQitamemo}
                                </c:if>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>呈 核</td>
                            <td colspan="4" class="td_style"
                                style="text-align: left">${wfIpebgLiaisonEntity.recipientUnit }
                            </td>
                            <td style="width:80px">發文單位</td>
                            <td colspan="4" class="td_style"
                                style="text-align: left">${wfIpebgLiaisonEntity.dispatchUnit }
                            </td>
                        </tr>
                        <tr align="center">
                            <td>會審單位</td>
                            <td colspan="4" class="td_style"
                                style="text-align: left">${wfIpebgLiaisonEntity.organizerUnit }
                            </td>
                            <td>發文字號</td>
                            <td colspan="4" class="td_style"
                                style="text-align: left">${wfIpebgLiaisonEntity.issueNumber }</td>
                        </tr>
                        <tr align="center">
                            <td>受文單位</td>
                            <td colspan="4" class="td_style"
                                style="text-align: left">${wfIpebgLiaisonEntity.copySubmitted }
                            </td>
                            <td>發文日期</td>
                            <td colspan="4" class="td_style" style="text-align: left"><fmt:formatDate
                                    pattern="yyyy-MM-dd"
                                    value="${wfIpebgLiaisonEntity.dealtime}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>主旨</td>
                            <td colspan="9" class="td_style"
                                style="text-align: left">${wfIpebgLiaisonEntity.purpose }</td>
                        </tr>
                        <tr align="center">
                            <td>詳細說明</td>
                            <td colspan="9">
                                <%-- <input id="detailMemo" name="detailMemo"
                                class="easyui-validatebox inputCss" data-options="width: 450"
                                value="${wfIpebgLiaisonEntity.detailMemo }" /> --%>
                                <textarea style="width:100%;height:650px;visibility:hidden;border:0px" id="detailMemo"
                                          name="detailMemo" class="dd">${wfIpebgLiaisonEntity.detailMemo}</textarea>
                            </td>
                        </tr>
                        <!-- 						</table>
                                        </td>
                                   </tr>
                                    <tr>
                                        <td>
                                            <table class="formList"> -->
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${wfIpebgLiaisonEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','iPEBG事業群聯絡單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfIpebgLiaisonEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="noprint">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfIpebgLiaisonEntity.workstatus!=null&&wfIpebgLiaisonEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <div id="dlg"></div>
</form>
<input type="hidden" id="b" name="b" value="2"/>
<script src='${ctx}/static/js/information/wfipebgliaison.js?random=<%= Math.random()%>'></script>
</body>
</html>