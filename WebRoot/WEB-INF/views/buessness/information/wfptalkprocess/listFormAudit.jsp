<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>P-Talk帳號需求申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src='${ctx}/static/js/information/wfptalkprocess.js?random=<%= Math.random()%>'></script>
<style type="text/css">
    .td_style2 {
        text-align: left;
    }
    .overdiv{
        overflow-y: auto;
        overflow-x: auto;
    }
</style>
</head>
<body>
<form id="mainform" action="${ctx}/wfptalkprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfptalkprocessEntity.id }"/>
    <input id="serialno" name="wfptalkprocess.serialno" type="hidden" value="${wfptalkprocessEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">P-Talk/賦能柜帳號需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfptalkprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfptalkprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfptalkprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfptalkprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfptalkprocessEntity.makerno}/${wfptalkprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
           <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">申請人工號</td>
                            <td width="10%" class="td_style1">
                                <input id="applyno" name="wfptalkprocess.applyno" class="easyui-validatebox inputCss"
                                       data-options="width: 80" readonly value="${wfptalkprocessEntity.applyno}"/>
                            </td>
                            <td width="10%">申請人</td>
                            <td width="10%" class="td_style1">
                                <input id="applyname" name="wfptalkprocess.applyname" class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfptalkprocessEntity.applyname }"/>
                            </td>
                            <td width="10%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="applydeptno" name="wfptalkprocess.applydeptno" class="easyui-validatebox inputCss"
                                       data-options="width: 90" readonly value="${wfptalkprocessEntity.applydeptno }"/>
                            </td>
                            <td width="10%">所在廠區</td>
                            <td width="10%" class="td_style1">
                                <input id="applyfactoryid" name="wfptalkprocess.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfptalkprocessEntity.applyfactoryid }" disabled
                                       data-options="width: 120"/>
                            </td>
                            <td width="10%">聯繫電話</td>
                            <td width="10%" class="td_style1">
                                <input id="applytel" name="wfptalkprocess.applytel" class="easyui-validatebox inputCss"
                                       style="width:90px;" readonly value="${wfptalkprocessEntity.applytel }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="wfptalkprocess.applydeptname" class="easyui-validatebox inputCss"
                                       data-options="width: 410" readonly value="${wfptalkprocessEntity.applydeptname }"/>
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="wfptalkprocess.applyemail" class="easyui-validatebox inputCss"
                                       value="${wfptalkprocessEntity.applyemail }" readonly style="width:300px;"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">權限申請內容</td>
                        </tr>
                        <tr align="center">
                            <td>申請類別</td>
                            <td colspan="2" class="td_style1">
                                <input id="applytype" name="wfptalkprocess.applytype" class="easyui-combobox"
                                       value="${wfptalkprocessEntity.applytype }" panelHeight="auto" editable="false"  disabled/>
                            </td>
                            <td>系統名稱</td>
                            <td colspan="2" class="td_style1">
                                <input id="systemname" name="wfptalkprocess.systemname" class="easyui-combobox"
                                       value="${wfptalkprocessEntity.systemname }" panelHeight="auto" editable="false" disabled/>
                            </td>
                            <td>權限廠區</td>
                            <td colspan="3" class="td_style1">
                                <input id="limitfactoryid" name="wfptalkprocess.limitfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfptalkprocessEntity.limitfactoryid }"
                                       data-options="width: 120" disabled/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>角色列表</td>
                            <td colspan="9" class="td_style2">
                                <div class="applyroleDiv"></div>
                                <input id="applyrole" name="wfptalkprocess.applyrole"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfptalkprocessEntity.applyrole}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>申請原因</td>
                            <td colspan="9" class="td_style1">
                                <textarea id="applyreason" name="wfptalkprocess.applyreason"
                                          class="easyui-validatebox" maxlength="300"
                                          style="width:99%;height:80px;" data-options="prompt:'請需求單位詳細說明'"
                                          rows="5" cols="6">${wfptalkprocessEntity.applyreason }</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <c:if test="${ptalkitems!=null && ptalkitems.size()>0&& ptalkitems.size()>10}">
                                    <div style="overflow-x: auto;overflow-y: auto;height: 400px" width="100%">
                                </c:if>
                                <c:if test="${ptalkitems!=null && ptalkitems.size()>0&& ptalkitems.size()<10}">
                                    <div style="overflow-x: auto;overflow-y: auto" width="100%">
                                </c:if>
                                <c:if test="${ptalkitems==null}">
                                    <div style="overflow-x: auto;overflow-y: auto" width="100%">
                                </c:if>
                                    <table id="ptalkitemTable" width="100%">
                                        <tr align="center">
                                            <td width="10%">&nbsp;序號&nbsp;</td>
                                            <td width="20%">工號</td>
                                            <td width="20%">姓名</td>
                                            <td width="50%">部門</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${ptalkitems!=null && ptalkitems.size()>0}">
                                            <c:forEach items="${ptalkitems}" var="ptalkitems" varStatus="status">
                                                <tr align="center" id="ptalkitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>${ptalkitems.empno}</td>
                                                    <td>${ptalkitems.empname}</td>
                                                    <td>${ptalkitems.empdeptname}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
           </tr>
            <tr>
                <td>
                     <table class="formList">
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%" class="td_style2">
						        <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;" rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wfptalkprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','P-Talk帳號需求申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfptalkprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
<div id="dlg"></div>
<input type="hidden" id="disOrEnabled"  value="disabled"/>
</body>
</html>