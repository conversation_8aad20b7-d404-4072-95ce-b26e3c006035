<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>P-Talk帳號需求申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src='${ctx}/static/js/information/wfptalkprocess.js?random=<%= Math.random()%>'></script>
<style type="text/css">
    .td_style2 {
        text-align: left;
    }
    .overdiv{
        overflow-y: auto;
        overflow-x: auto;
    }
</style>
</head>
<body>
<form id="mainform" action="${ctx}/wfptalkprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfptalkprocessEntity.id }"/>
    <input id="serialno" name="wfptalkprocess.serialno" type="hidden" value="${wfptalkprocessEntity.serialno }"/>
    <input id="makerno" name="wfptalkprocess.makerno" type="hidden" value="${wfptalkprocessEntity.makerno }"/>
    <input id="makername" name="wfptalkprocess.makername" type="hidden" value="${wfptalkprocessEntity.makername }"/>
    <input id="makerdeptno" name="wfptalkprocess.makerdeptno" type="hidden" value="${wfptalkprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfptalkprocess.makerfactoryid" type="hidden" value="${wfptalkprocessEntity.makerfactoryid }"/>
    <div class="commonW">
    <div class="headTitle">P-Talk/賦能柜帳號需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfptalkprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfptalkprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfptalkprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfptalkprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfptalkprocessEntity.makerno}">
			  <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfptalkprocessEntity.makerno}">
                <div class="position_R margin_R">填單人：${wfptalkprocessEntity.makerno}/${wfptalkprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
		<table class="formList">
		   <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="applyno" name="wfptalkprocess.applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfptalkprocessEntity.applyno}" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="10%">申請人</td>
                            <td width="10%" class="td_style1">
                                <input id="applyname" name="wfptalkprocess.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfptalkprocessEntity.applyname }"/>
                            </td>
                            <td width="10%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="applydeptno" name="wfptalkprocess.applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfptalkprocessEntity.applydeptno }"/>
                            </td>
                            <td width="10%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="applyfactoryid" name="wfptalkprocess.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfptalkprocessEntity.applyfactoryid }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applyfactoryid\',\'请选择所在廠區\']'"/>
                            </td>
                            <td width="10%">聯繫電話&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="applytel" name="wfptalkprocess.applytel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfptalkprocessEntity.applytel }" data-options="required:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                               <input id="applydeptname" name="wfptalkprocess.applydeptname" class="easyui-validatebox" data-options="width: 410,required:true"
                                       value="${wfptalkprocessEntity.applydeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="wfptalkprocess.applyemail" class="easyui-validatebox"
                                       value="${wfptalkprocessEntity.applyemail }" style="width:300px;"
                                       data-options="required:true" onblur="valdEmail('')"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">權限申請內容</td>
                        </tr>
                        <tr align="center">
                            <td>申請類別&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="applytype" name="wfptalkprocess.applytype" class="easyui-combobox"
                                       value="${wfptalkprocessEntity.applytype }" panelHeight="auto" editable="false" data-options="required:true,validType:'comboxValidate[\'applytype\',\'请选择申請類別\']'"/>
                            </td>
                            <td>系統名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="systemname" name="wfptalkprocess.systemname" class="easyui-combobox"
                                       value="${wfptalkprocessEntity.systemname }" panelHeight="auto" editable="false" data-options="required:true,validType:'comboxValidate[\'systemname\',\'请选择系統名稱\']',onChange:function(){resetInfoRole();}"/>
                            </td>
                            <td>權限廠區&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="limitfactoryid" name="wfptalkprocess.limitfactoryid" class="easyui-combobox"
                                   panelHeight="auto" value="${wfptalkprocessEntity.limitfactoryid }"
                                   data-options="width: 120,required:true,validType:'comboxValidate[\'limitfactoryid\',\'请选择權限廠區\']'"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>角色列表&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style2">
                                <div class="applyroleDiv"></div>
                                <input id="applyrole" name="wfptalkprocess.applyrole"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfptalkprocessEntity.applyrole}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>申請原因&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1">
                                <textarea id="applyreason" name="wfptalkprocess.applyreason"
                                          class="easyui-validatebox"
                                          oninput="return LessThan(this);"
                                          onchange="return LessThan(this);"
                                          onpropertychange="return LessThan(this);"
                                          maxlength="300"
                                          style="width:99%;height:80px;" data-options="required:true"
                                          rows="5" cols="6"
                                          data-options="required:true,validType:'length[0,300]'">${wfptalkprocessEntity.applyreason }</textarea><span id="txtNum"></span>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <c:if test="${ptalkitems!=null && ptalkitems.size()>0&& ptalkitems.size()>10}">
                                    <div style="overflow-x: auto;overflow-y: auto;height: 400px" width="100%">
                                </c:if>
                                <c:if test="${ptalkitems!=null && ptalkitems.size()>0&& ptalkitems.size()<10}">
                                    <div style="overflow-x: auto;overflow-y: auto" width="100%">
                                </c:if>
                                <c:if test="${ptalkitems==null}">
                                    <div class="overdiv" width="100%" id="overflowdiv">
                                </c:if>
                                    <input id="ptalkitemTableIndex" type="hidden"
                                           value="<c:if test="${ptalkitems!=null && ptalkitems.size()>0}">${ptalkitems.size()+1}</c:if>
                                        <c:if test="${ptalkitems.size()==0 || ptalkitems==null}">2</c:if>">
                                    </input>
                                    <table id="ptalkitemTable" width="100%">
                                        <tr align="center">
                                            <td width="10%">&nbsp;序號&nbsp;</td>
                                            <td width="20%">工號&nbsp;<font color="red">*</font></td>
                                            <td width="20%">姓名</td>
                                            <td width="30%">部門</td>
                                            <td width="20%">&nbsp;操作&nbsp;</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${ptalkitems!=null && ptalkitems.size()>0}">
                                            <c:forEach items="${ptalkitems}" var="ptalkitems" varStatus="status">
                                                <tr align="center" id="ptalkitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td><input id="ptalk_empno${status.index+1}" name="wfptalkitem[${status.index}].empno"
                                                               class="easyui-validatebox" data-options="required:true" style="width: 80px;" onblur="queryUserInfo1(this,'${status.index+1}');" value="${ptalkitems.empno}"/></td>
                                                    <td><input id="ptalk_empname${status.index+1}" name="wfptalkitem[${status.index}].empname"
                                                               class="easyui-validatebox inputCss" readonly style="width:60px;text-align: center;" value="${ptalkitems.empname}"/></td>
                                                    <td><input id="ptalk_empdeptname${status.index+1}" name="wfptalkitem[${status.index}].empdeptname"
                                                               class="easyui-validatebox" style="width:350px;text-align: center;" data-options="required:true" value="${ptalkitems.empdeptname}"/></td>
                                                    <td><input type="image" src="${ctx}/static/images/deleteRow.png" onclick="ptalkdeltr(${status.index+1});return false;"/></td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${ptalkitems.size()==0 || ptalkitems==null}">
                                            <tr align="center" id="ptalkitems1">
                                                <td>1</td>
                                                <td><input id="ptalk_empno1" name="wfptalkitem[0].empno"
                                                           class="easyui-validatebox" data-options="required:true" onblur="queryUserInfo1(this,'1');" style="width: 80px;" value=""/></td>
                                                <td><input id="ptalk_empname1" name="wfptalkitem[0].empname"
                                                           class="easyui-validatebox inputCss" readonly style="width:60px;text-align: center;" value=""/></td>
                                                <td><input id="ptalk_empdeptname1" name="wfptalkitem[0].empdeptname"
                                                           class="easyui-validatebox" style="width:350px;text-align: center;"  data-options="required:true" value=""/></td>
                                                <td><input type="image" src="${ctx}/static/images/deleteRow.png" onclick="ptalkdeltr(1);return false;"/></td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr">
                                            <td colspan="12" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="ptalkItemAdd" style="width:100px;float:left;" value="添加一筆"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>
                                <a href="${ctx}/ossAdmin/download/5b00c5b6e3cf42e4b89e6a5132a85688" id="btnBatchImportTpl">模板下載.xls</a>
                            </td>
                            <td colspan="9" class="td_style1">
                                &nbsp;&nbsp;&nbsp;&nbsp;<a href="#" id="batchImport" class="easyui-linkbutton"
                                                           data-options="iconCls:'icon-hamburg-cv'" style="width: 100px;"
                                                           onclick="openBatchImportWin();">批量導入</a>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="9" class="td_style1">
                                1、該單用於P-Talk/賦能柜帳號新增使用；<br>
                                <font color="red">2、用戶單位需簽核至廠部級主管。</font>
                            </td>
                        </tr>
                    </table>
                </td>
           </tr>
		   <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <input type="hidden" id="attachids" name="wfptalkprocess.attachids" value="${wfptalkprocessEntity.attachids }"/>
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_ptalkzhanghaoshenqingdan_v1','P-Talk帳號需求申請單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="ywqchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%" >
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ywqchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(21,'ywqchargeTable','ywqchargeno','ywqchargename',$('#applyfactoryid').combobox('getValue'),'wfptalkprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ywqchargeno" name="wfptalkprocess.ywqchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ywqchargeno']}" readonly
                                                               value="${wfptalkprocessEntity.ywqchargeno}"/><c:if test="${requiredMap['ywqchargeno']}"><font color="red">*</font></c:if>
                                                        /<input id="ywqchargename" name="wfptalkprocess.ywqchargename"
                                                                readonly class="easyui-validatebox" data-options="width:80,required:${requiredMap['ywqchargeno']}"
                                                                value="${wfptalkprocessEntity.ywqchargename}"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zakTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zakchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(153,'zakTable','zakchargeno','zakchargename',$('#applyfactoryid').combobox('getValue'),'wfptalkprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zakchargeno" name="wfptalkprocess.zakchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zakchargeno']}" readonly
                                                               value="${wfptalkprocessEntity.zakchargeno }"/><c:if
                                                            test="${requiredMap['zakchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zakchargename" name="wfptalkprocess.zakchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zakchargeno']}"
                                                                value="${wfptalkprocessEntity.zakchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="wfptalkprocess.kchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}" readonly
                                                               value="${wfptalkprocessEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="wfptalkprocess.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfptalkprocessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="wfptalkprocess.bchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}" readonly
                                                               value="${wfptalkprocessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="wfptalkprocess.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfptalkprocessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'cchargeno','cchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wfptalkprocess.cchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}" readonly
                                                               value="${wfptalkprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wfptalkprocess.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfptalkprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applyfactoryid').combobox('getValue'),'wfptalkprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wfptalkprocess.zchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}" readonly
                                                               value="${wfptalkprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="wfptalkprocess.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfptalkprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#applydeptno').val(),'zcchargeno','zcchargename',$('#applyfactoryid').combobox('getValue'),'wfptalkprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="wfptalkprocess.zcchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}" readonly
                                                               value="${wfptalkprocessEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="wfptalkprocess.zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfptalkprocessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#applydeptno').val(),'pcchargeno','pcchargename',$('#applyfactoryid').combobox('getValue'),'wfptalkprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="wfptalkprocess.pcchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}" readonly
                                                               value="${wfptalkprocessEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="wfptalkprocess.pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wfptalkprocessEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="ywkTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ywkchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'ywkTable','ywkchargeno','ywkchargename',$('#applyfactoryid').combobox('getValue'),'wfptalkprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ywkchargeno" name="wfptalkprocess.ywkchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ywkchargeno']}" readonly
                                                               value="${wfptalkprocessEntity.ywkchargeno }"/><c:if
                                                            test="${requiredMap['ywkchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ywkchargename" name="wfptalkprocess.ywkchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ywkchargeno']}"
                                                                value="${wfptalkprocessEntity.ywkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="ywbTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ywbchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(25,'ywbTable','ywbchargeno','ywbchargename',$('#applyfactoryid').combobox('getValue'),'wfptalkprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ywbchargeno" name="wfptalkprocess.ywbchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ywbchargeno']}" readonly
                                                               value="${wfptalkprocessEntity.ywbchargeno }"/><c:if
                                                            test="${requiredMap['ywbchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ywbchargename" name="wfptalkprocess.ywbchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ywbchargeno']}"
                                                                value="${wfptalkprocessEntity.ywbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zabTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zabchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(154,'zabTable','zabchargeno','zabchargename',$('#applyfactoryid').combobox('getValue'),'wfptalkprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zabchargeno" name="wfptalkprocess.zabchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zabchargeno']}" readonly
                                                               value="${wfptalkprocessEntity.zabchargeno }"/><c:if
                                                            test="${requiredMap['zabchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zabchargename" name="wfptalkprocess.zabchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zabchargeno']}"
                                                                value="${wfptalkprocessEntity.zabchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"  id="zzyyzxTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zzyyzxno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(223,'zzyyzxTable','zzyyzxno','zzyyzxname',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zzyyzxno" name="wfptalkprocess.zzyyzxno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['zzyyzxno']}"
                                                               readonly
                                                               value="${wfptalkprocessEntity.zzyyzxno }"/><c:if test="${requiredMap['zzyyzxno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="zzyyzxname" name="wfptalkprocess.zzyyzxname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zzyyzxno']}"
                                                                value="${wfptalkprocessEntity.zzyyzxname }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="ywcTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ywcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(155,'ywcTable','ywcchargeno','ywcchargename',$('#applyfactoryid').combobox('getValue'),'wfptalkprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ywcchargeno" name="wfptalkprocess.ywcchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ywcchargeno']}" readonly
                                                               value="${wfptalkprocessEntity.ywcchargeno }"/><c:if
                                                            test="${requiredMap['ywcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ywcchargename" name="wfptalkprocess.ywcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ywcchargeno']}"
                                                                value="${wfptalkprocessEntity.ywcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="ywzchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">${requiredMap['ywzchargeno_name']}
                                                                </td>
                                                                <td style="border: none;"></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ywzchargeno" name="wfptalkprocess.ywzchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ywzchargeno']}" readonly
                                                               value="${wfptalkprocessEntity.ywzchargeno }"/>
                                                        <c:if test="${requiredMap['ywzchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ywzchargename" name="wfptalkprocess.ywzchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ywzchargeno']}"
                                                                value="${wfptalkprocessEntity.ywzchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton" data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	<input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
<div id="optionWin" class="easyui-window" title="P-Talk帳號需求申請單信息導入" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%">
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="file" type="file" style="width: 300px"
                           accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>
                    <input id="isExcel2003" name="isExcel2003" type="hidden" value="">

                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <span ID="labelListAddResult"></span><a href="${ctx}/wfptalkprocess/downLoad/errorExcel" id="downloadError"
                                                            plain="true">查看錯誤信息</a>
                </td>
            </tr>
        </table>
    </form>
</div>
</body>
</html>
