<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>列印權限申請</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/wfprintauthinfoprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfprintauthinfoprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfprintauthinfoprocessEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">列印權限申請</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfprintauthinfoprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfprintauthinfoprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfprintauthinfoprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfprintauthinfoprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfprintauthinfoprocessEntity.makerno}/${wfprintauthinfoprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
           <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">承辦人工號</td>
                            <td width="10%" class="td_style1">
                                <input id="dealno" name="wfprintauthinfoprocess.dealno" class="easyui-validatebox inputCss"
                                       data-options="width: 80,required:true" readonly
                                       value="${wfprintauthinfoprocessEntity.dealno}"/>
                            </td>
                            <td width="5%">承辦人</td>
                            <td width="12%">${wfprintauthinfoprocessEntity.dealname }</td>
                            <td width="6%">單位代碼</td>
                            <td width="12%" >${wfprintauthinfoprocessEntity.dealdeptno }</td>
                            <td width="6%">費用代碼</td>
                            <td width="12%" >${wfprintauthinfoprocessEntity.applycostno }</td>
                            <td width="8%">所在廠區</td>
                            <td width="21%" class="td_style1">
                                <input id="applychoosefactoryid" name="wfprintauthinfoprocess.applychoosefactoryid"
                                       class="easyui-combobox" disabled
                                       panelHeight="auto" value="${wfprintauthinfoprocessEntity.applychoosefactoryid }"
                                       data-options="width: 120,required:true,onSelect:function(){onchangeFactory('applychoosefactoryid');onchangePlantNo('applychoosefactoryid')}"/>
                                <input id="applyfactoryid" name="wfprintauthinfoprocess.applyfactoryid" type="hidden"
                                       value="${wfprintauthinfoprocessEntity.applyfactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td>${wfprintauthinfoprocessEntity.applyleveltype }</td>
                            <td>管理職</td>
                            <td>${wfprintauthinfoprocessEntity.applymanager }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3">${wfprintauthinfoprocessEntity.dealemail }</td>
                            <td>使用區域&nbsp;</td>
                            <td class="td_style1">
                                <input id="applyarea" name="wfprintauthinfoprocess.applyarea" class="easyui-combobox" disabled
                                       data-options="width: 70,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wfprintauthinfoprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="wfprintauthinfoprocess.applybuilding"  class="easyui-combobox" disabled
                                       data-options="width: 70,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wfprintauthinfoprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機</td>
                            <td>${wfprintauthinfoprocessEntity.dealtel}</td>
                            <td>單位</td>
                            <td colspan="7" class="td_style1">
                                <input id="dealdeptname" name="wfprintauthinfoprocess.dealdeptname" readonly
                                       class="easyui-validatebox inputCss" data-options="width: 410,required:true"
                                       value="${wfprintauthinfoprocessEntity.dealdeptname }"/>
                            </td>
                        </tr>
                    </table>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">列印機位置</td>
                        </tr>
                        <tr align="center">
                            <td>列印機位置</td>
                            <td><input id="printerposition" name="wfprintauthinfoprocess.printerposition"
                                       class="easyui-combobox" disabled data-options="width:130"
                                       value="${wfprintauthinfoprocessEntity.printerposition }"/></td>
                            <td>列印機名稱</td>
                            <td><input id="printername" name="wfprintauthinfoprocess.printername"
                                       class="easyui-combobox" disabled data-options="width:130"
                                       value="${wfprintauthinfoprocessEntity.printername }"/></td>
                            <td>列印機保養人</td>
                            <td>${wfprintauthinfoprocessEntity.printermanager }</td>
                            <td>列印機服務器地址</td>
                            <td>${wfprintauthinfoprocessEntity.printserveraddress }</td>
                        </tr>
                        <tr align="center">
                            <td>所屬部門代碼</td>
                            <td>${wfprintauthinfoprocessEntity.printdeptno }</td>
                            <td>所屬部門名稱</td>
                            <td>${wfprintauthinfoprocessEntity.printdeptname }</td>
                            <td>列印機IP</td>
                            <td>${wfprintauthinfoprocessEntity.printip }</td>
                            <td>列印管控</td>
                            <td>${wfprintauthinfoprocessEntity.printcontrol }</td>
                        </tr>
                    </table>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人明細</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;">
                                    <table id="wfprintauthItemTable" width="100%" style="border-collapse: collapse">
                                        <tr align="center">
                                            <td>項次</td>
                                            <td>申請人工號</td>
                                            <td>申請人姓名</td>
                                            <td>單位代碼</td>
                                            <td>電腦IP</td>
                                            <td>是否列印審核</td>
                                            <td>審核人工號</td>
                                            <td>審核人姓名</td>
                                            <td>安保區域</td>
                                            <td>申請動作</td>
                                            <td>需求說明</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wfprintauthitems!=null&&wfprintauthitems.size()>0}">
                                            <c:forEach items="${wfprintauthitems}" var="wfprintauthitems"
                                                       varStatus="status">
                                                <tr align="center" id="wfprintauthitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>${wfprintauthitems.applyno}</td>
                                                    <td>${wfprintauthitems.applyname}</td>
                                                    <td>${wfprintauthitems.pcno}</td>
                                                    <td>${wfprintauthitems.pcip}</td>
                                                    <td>
                                                        <%--<input id="isprintcheck${status.index+1}"
                                                               name="wfprintauthitems[${status.index+1}].isprintcheck"
                                                               class="easyui-combobox" disabled
                                                               data-options="width:90,required:true,validType:'comboxValidate[\'isprintcheck${status.index+1}\',\'请選擇是否列印審核\']',onSelect:function(){onchangeIsprintcheck(${status.index+1});},
                                                                              panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadIsprintcheck(${status.index+1});}"
                                                               style="width:90px;"
                                                               value="${wfprintauthitems.isprintcheck}"/>--%>
                                                            ${wfprintauthitems.isprintcheck}
                                                    </td>
                                                    <td>${wfprintauthitems.checkno}</td>
                                                    <td>${wfprintauthitems.checkname}</td>
                                                    <td><input id="securityarea${status.index+1}"
                                                               name="wfprintauthitems[${status.index+1}].securityarea"
                                                               class="easyui-combobox" disabled
                                                               data-options="width:80,required:true,validType:'comboxValidate[\'securityarea${status.index+1}\',\'请選擇安保區域\']', onSelect:function(){onchangeSecurityareaOrApplyType(${status.index+1});},
                                                                              panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadSecurityArea(${status.index+1});}"
                                                               style="width:80px;"
                                                               value="${wfprintauthitems.securityarea}"/></td>
                                                    <td><input id="applytype${status.index+1}"
                                                               name="wfprintauthitems[${status.index+1}].applytype"
                                                               class="easyui-combobox" disabled
                                                               data-options="width:75,required:true,validType:'comboxValidate[\'applytype${status.index+1}\',\'请選擇申請動作\']', onSelect:function(){onchangeSecurityareaOrApplyType(${status.index+1});},
                                                                              panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadApplyType(${status.index+1});}"
                                                               style="width:75px;"
                                                               value="${wfprintauthitems.applytype}"/></td>
                                                    <td>${wfprintauthitems.requirecontent}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
           </tr>
            <tr>
                <td>
                     <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="wfprintauthinfoprocess.attachids" value="${wfprintauthinfoprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%">
						<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wfprintauthinfoprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','列印權限申請');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfprintauthinfoprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
 <input id="disOrEnabled" type="hidden" value="disabled"/>
  <div id="dlg"></div>
<script src='${ctx}/static/js/information/wfprintauthinfoprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>