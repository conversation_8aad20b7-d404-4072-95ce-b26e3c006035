<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>列印權限申請</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfprintauthinfoprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfprintauthinfoprocessEntity.id }"/>
    <input id="serialno" name="wfprintauthinfoprocess.serialno" type="hidden"
           value="${wfprintauthinfoprocessEntity.serialno }"/>
    <input id="makerno" name="wfprintauthinfoprocess.makerno" type="hidden"
           value="${wfprintauthinfoprocessEntity.makerno }"/>
    <input id="makername" name="wfprintauthinfoprocess.makername" type="hidden"
           value="${wfprintauthinfoprocessEntity.makername }"/>
    <input id="makerdeptno" name="wfprintauthinfoprocess.makerdeptno" type="hidden"
           value="${wfprintauthinfoprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfprintauthinfoprocess.makerfactoryid" type="hidden"
           value="${wfprintauthinfoprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">列印權限申請</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfprintauthinfoprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfprintauthinfoprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfprintauthinfoprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfprintauthinfoprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfprintauthinfoprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfprintauthinfoprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfprintauthinfoprocessEntity.makerno}/${wfprintauthinfoprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="dealno" name="wfprintauthinfoprocess.dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfprintauthinfoprocessEntity.dealno}"
                                       onchange="queryUserInfo();"/>
                            </td>
                            <td width="5%">承辦人</td>
                            <td width="12%" class="td_style1">
                                <input id="dealname" name="wfprintauthinfoprocess.dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wfprintauthinfoprocessEntity.dealname }"/>
                            </td>
                            <td width="6%">單位代碼</td>
                            <td width="12%" class="td_style1">
                                <input id="dealdeptno" name="wfprintauthinfoprocess.dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfprintauthinfoprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="6%">費用代碼</td>
                            <td width="12%" class="td_style1">
                                <input id="applycostno" name="wfprintauthinfoprocess.applycostno"
                                       class="easyui-validatebox inputCss" style="width:90px;"
                                       readonly value="${wfprintauthinfoprocessEntity.applycostno }"/>
                            </td>
                            <td width="8%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="21%" class="td_style1">
                                <input id="applychoosefactoryid" name="wfprintauthinfoprocess.applychoosefactoryid"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${wfprintauthinfoprocessEntity.applychoosefactoryid }"
                                       data-options="width: 120,required:true,onSelect:function(){onchangeFactory('applychoosefactoryid');onchangePlantNo('applychoosefactoryid')}"/>
                                <input id="applyfactoryid" name="wfprintauthinfoprocess.applyfactoryid" type="hidden"
                                       value="${wfprintauthinfoprocessEntity.applyfactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="wfprintauthinfoprocess.applyleveltype"
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfprintauthinfoprocessEntity.applyleveltype }" readonly/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="wfprintauthinfoprocess.applymanager"
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfprintauthinfoprocessEntity.applymanager }" readonly/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="wfprintauthinfoprocess.dealemail" class="easyui-validatebox"
                                       value="${wfprintauthinfoprocessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td>使用區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyarea" name="wfprintauthinfoprocess.applyarea" class="easyui-combobox"
                                       data-options="width: 70,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wfprintauthinfoprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="wfprintauthinfoprocess.applybuilding"
                                       class="easyui-combobox"
                                       data-options="width: 70,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wfprintauthinfoprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealtel" name="wfprintauthinfoprocess.dealtel" class="easyui-validatebox"
                                       style="width:90px;position:relative;"
                                       value="${wfprintauthinfoprocessEntity.dealtel}"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="7" class="td_style1">
                                <input id="dealdeptname" name="wfprintauthinfoprocess.dealdeptname"
                                       class="easyui-validatebox" data-options="width: 410,required:true"
                                       value="${wfprintauthinfoprocessEntity.dealdeptname }"/>
                            </td>
                        </tr>
                    </table>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">列印機位置</td>
                        </tr>
                        <tr align="center">
                            <td>列印機位置&nbsp;<font color="red">*</font></td>
                            <td><input id="printerposition" name="wfprintauthinfoprocess.printerposition"
                                       class="easyui-combobox"
                                       data-options="width:130,validType:'comboxValidate[\'printerposition\',\'请選擇列印機位置\']',onSelect:function(){onchangeBuildNam('printerposition')}"
                                       value="${wfprintauthinfoprocessEntity.printerposition }"/></td>
                            <td>列印機名稱&nbsp;<font color="red">*</font></td>
                            <td><input id="printername" name="wfprintauthinfoprocess.printername"
                                       class="easyui-combobox"
                                       data-options="width:130,validType:'comboxValidate[\'printername\',\'请選擇列印機名稱\']',onSelect:function(){onchangePrintNam('printername')}"
                                       value="${wfprintauthinfoprocessEntity.printername }"/></td>
                            <td>列印機保養人</td>
                            <td><input id="printermanager" name="wfprintauthinfoprocess.printermanager"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:100" readonly
                                       value="${wfprintauthinfoprocessEntity.printermanager }"/></td>
                            <td>列印機服務器地址</td>
                            <td><input id="printserveraddress" name="wfprintauthinfoprocess.printserveraddress"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:120" readonly
                                       value="${wfprintauthinfoprocessEntity.printserveraddress }"/></td>
                        </tr>
                        <tr align="center">
                            <td>所屬部門代碼</td>
                            <td><input id="printdeptno" name="wfprintauthinfoprocess.printdeptno"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:100" readonly
                                       value="${wfprintauthinfoprocessEntity.printdeptno }"/></td>
                            <td>所屬部門名稱</td>
                            <td><input id="printdeptname" name="wfprintauthinfoprocess.printdeptname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:120" readonly
                                       value="${wfprintauthinfoprocessEntity.printdeptname }"/></td>
                            <td>列印機IP</td>
                            <td><input id="printip" name="wfprintauthinfoprocess.printip"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:100" readonly
                                       value="${wfprintauthinfoprocessEntity.printip }"/></td>
                            <td>列印管控</td>
                            <td><input id="printcontrol" name="wfprintauthinfoprocess.printcontrol"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:100" readonly
                                       value="${wfprintauthinfoprocessEntity.printcontrol }"/></td>
                        </tr>
                    </table>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人明細</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;">
                                    <input id="wfprintauthItemTableIndex" type="hidden"
                                           value="<c:if test="${wfprintauthitems!=null && wfprintauthitems.size()>0}">${wfprintauthitems.size() +1}</c:if>
                                        <c:if test="${wfprintauthitems.size()==0 || wfprintauthitems==null}">2</c:if>"/>
                                    </input>
                                    <table id="wfprintauthItemTable" width="100%" style="border-collapse: collapse">
                                        <tr align="center">
                                            <td>項次</td>
                                            <td>申請人工號&nbsp;<font color="red">*</font></td>
                                            <td>申請人姓名</td>
                                            <td>單位代碼</td>
                                            <td>電腦IP&nbsp;<font color="red">*</font></td>
                                            <td>是否列印審核&nbsp;<font color="red">*</font></td>
                                            <td>審核人工號</td>
                                            <td>審核人姓名</td>
                                            <td>安保區域&nbsp;<font color="red">*</font></td>
                                            <td>申請動作&nbsp;<font color="red">*</font></td>
                                            <td>需求說明&nbsp;<font color="red">*</font></td>
                                            <td>操作</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wfprintauthitems!=null&&wfprintauthitems.size()>0}">
                                            <c:forEach items="${wfprintauthitems}" var="wfprintauthitems"
                                                       varStatus="status">
                                                <tr align="center" id="wfprintauthitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <input id="applyno${status.index+1}"
                                                               onblur="getUserNameByEmpno2(this,'apply',${status.index+1});"
                                                               name="wfprintauthitems[${status.index+1}].applyno"
                                                               class="easyui-validatebox" style="width:60px;"
                                                               data-options="required:true"
                                                               value="${wfprintauthitems.applyno}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyname${status.index+1}"
                                                               name="wfprintauthitems[${status.index+1}].applyname"
                                                               class="easyui-validatebox inputCss" style="width:60px;"
                                                               readonly value="${wfprintauthitems.applyname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="pcno${status.index+1}"
                                                               name="wfprintauthitems[${status.index+1}].pcno"
                                                               style="width:90px;" class="easyui-validatebox inputCss"
                                                               readonly value="${wfprintauthitems.pcno}"/>
                                                    </td>
                                                    <td>
                                                        <input id="pcip${status.index+1}"
                                                               name="wfprintauthitems[${status.index+1}].pcip"
                                                               style="width:90px;" class="easyui-validatebox"
                                                               data-options="required:true,validType:'ip[\'pcip${status.index+1}\']'"
                                                               value="${wfprintauthitems.pcip}"/>
                                                    </td>
                                                    <td>
                                                        <%--<input id="isprintcheck${status.index+1}"
                                                               name="wfprintauthitems[${status.index+1}].isprintcheck"
                                                               class="easyui-combobox"
                                                               data-options="width:90,panelHeight:'auto',valueField:'value',textField:'label',editable:false,onBeforeLoad:function(){loadIsprintcheck(${status.index+1});}" style="width:90px;"
                                                               value="${wfprintauthitems.isprintcheck}"/>--%>
                                                            <%--onSelect:function(){onchangeIsprintcheck(${status.index+1});}   validType:'comboxValidate[\'isprintcheck${status.index+1}\',\'请選擇是否列印審核\']'--%>
                                                        <input id="isprintcheck${status.index+1}"
                                                               name="wfprintauthitems[${status.index+1}].isprintcheck"
                                                               class="easyui-validatebox inputCss" style="width:60px;"
                                                               readonly value="${wfprintauthitems.isprintcheck}"/>
                                                    </td>
                                                    <td>
                                                        <input id="checkno${status.index+1}"
                                                               onblur="getUserNameByEmpno2(this,'check',${status.index+1});"
                                                               name="wfprintauthitems[${status.index+1}].checkno"
                                                               class="easyui-validatebox" style="width:60px;"
                                                               data-options="required:true"
                                                               value="${wfprintauthitems.checkno}"/>
                                                    </td>
                                                    <td>
                                                        <input id="checkname${status.index+1}"
                                                               name="wfprintauthitems[${status.index+1}].checkname"
                                                               class="easyui-validatebox inputCss" style="width:60px;"
                                                               readonly value="${wfprintauthitems.checkname}"/>
                                                    </td>
                                                    <td><input id="securityarea${status.index+1}"
                                                               name="wfprintauthitems[${status.index+1}].securityarea"
                                                               class="easyui-combobox"
                                                               data-options="width:80,required:true,validType:'comboxValidate[\'securityarea${status.index+1}\',\'请選擇安保區域\']', onSelect:function(){onchangeSecurityareaOrApplyType(${status.index+1});},
                                                                              panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadSecurityArea(${status.index+1});}"
                                                               style="width:80px;"
                                                               value="${wfprintauthitems.securityarea}"/></td>
                                                    <td><input id="applytype${status.index+1}"
                                                               name="wfprintauthitems[${status.index+1}].applytype"
                                                               class="easyui-combobox"
                                                               data-options="width:75,required:true,validType:'comboxValidate[\'applytype${status.index+1}\',\'请選擇申請動作\']', onSelect:function(){onchangeSecurityareaOrApplyType(${status.index+1});},
                                                                              panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadApplyType(${status.index+1});}"
                                                               style="width:75px;"
                                                               value="${wfprintauthitems.applytype}"/></td>
                                                    <td>
                                                        <input id="requirecontent${status.index+1}"
                                                               name="wfprintauthitems[${status.index+1}].requirecontent"
                                                               class="easyui-validatebox" style="width:80px;"
                                                               data-options="required:true"
                                                               value="${wfprintauthitems.requirecontent}"/>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="deltr(${status.index+1});return false;"/>
                                                        <input id="shunxu${status.index+1}" type="hidden"
                                                               name="wfprintauthitems[${status.index+1}].shunxu"
                                                               value="${status.index+1}"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${wfprintauthitems.size()==0 || wfprintauthitems==null}">
                                            <tr align="center" id="wfprintauthitems${status.index+1}">
                                                <td>1</td>
                                                <td>
                                                    <input id="applyno1"
                                                           onblur="getUserNameByEmpno2(this,'apply',1);"
                                                           name="wfprintauthitems[1].applyno"
                                                           class="easyui-validatebox" style="width:60px;"
                                                           data-options="required:true"
                                                           value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyname1"
                                                           name="wfprintauthitems[1].applyname"
                                                           class="easyui-validatebox inputCss" style="width:60px;"
                                                           readonly value=""/>
                                                </td>
                                                <td>
                                                    <input id="pcno1"
                                                           name="wfprintauthitems[1].pcno"
                                                           style="width:90px;" class="easyui-validatebox inputCss"
                                                           readonly value=""/>
                                                </td>
                                                <td>
                                                    <input id="pcip1"
                                                           name="wfprintauthitems[1].pcip"
                                                           style="width:90px;" class="easyui-validatebox"
                                                           data-options="required:true,validType:'ip[\'pcip1\']'"
                                                           value=""/>
                                                </td>
                                                <td>
                                                    <%--<input id="isprintcheck1"
                                                           name="wfprintauthitems[1].isprintcheck"
                                                           class="easyui-combobox"
                                                           data-options="width:90,panelHeight:'auto',valueField:'value',textField:'label',editable:false,onBeforeLoad:function(){loadIsprintcheck(1);}"
                                                           style="width:90px;" value=""/>--%>
                                                    <input id="isprintcheck1" name="wfprintauthitems[1].isprintcheck"
                                                           class="easyui-validatebox inputCss" style="width:60px;" readonly value=""/>
                                                </td>
                                                <td>
                                                    <input id="checkno1"
                                                           onblur="getUserNameByEmpno2(this,'check',1);"
                                                           name="wfprintauthitems[1].checkno"
                                                           class="easyui-validatebox" style="width:60px;"
                                                           data-options="required:true"
                                                           value=""/>
                                                </td>
                                                <td>
                                                    <input id="checkname1"
                                                           name="wfprintauthitems[1].checkname"
                                                           class="easyui-validatebox inputCss" style="width:60px;"
                                                           readonly value=""/>
                                                </td>
                                                <td><input id="securityarea1"
                                                           name="wfprintauthitems[1].securityarea"
                                                           class="easyui-combobox"
                                                           data-options="width:80,required:true,validType:'comboxValidate[\'securityarea1\',\'请選擇安保區域\']', onSelect:function(){onchangeSecurityareaOrApplyType(1);},
                                                                              panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadSecurityArea(1);}"
                                                           style="width:80px;"
                                                           value=""/></td>
                                                <td><input id="applytype1"
                                                           name="wfprintauthitems[1].applytype"
                                                           class="easyui-combobox"
                                                           data-options="width:75,required:true,validType:'comboxValidate[\'applytype1\',\'请選擇申請動作\']',  onSelect:function(){onchangeSecurityareaOrApplyType(1);},
                                                                              panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadApplyType(1);}"
                                                           style="width:75px;"
                                                           value=""/></td>
                                                <td>
                                                    <input id="requirecontent1"
                                                           name="wfprintauthitems[1].requirecontent"
                                                           class="easyui-validatebox" style="width:80px;"
                                                           data-options="required:true"
                                                           value=""/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="deltr(1);return false;"/>
                                                    <input id="shunxu1" type="hidden"
                                                           name="wfprintauthitems[1].shunxu"
                                                           value="1"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr0">
                                            <td colspan="20" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="itemAdd" style="width:100px;float:left;"
                                                       value="添加一筆申請人"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td>導入格式：<a href="${ctx}/wfprintauthinfoprocess/downLoad/printBatchImport"><font
                                    color="blue">参考</font></a></td>
                            <td><a href="#" id="batchImport" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-hamburg-cv'" style="width: 100px;"
                                   onclick="openBatchImportWin();">批量導入</a></td>
                        </tr>
                        <tr align="center">
                            <td width="10%">附件<span id="fujianNoIs"></span></td>
                            <td width="90%" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button"
                                                                   value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <c:choose>
                                    <c:when test="${file.size()>0}">
                                        <input type="hidden" id="attachids" name="wfprintauthinfoprocess.attachids"
                                               value="${wfprintauthinfoprocessEntity.attachids }"/>
                                    </c:when>
                                    <c:otherwise>
                                        <input type="hidden" id="attachids" name="wfprintauthinfoprocess.attachids" value=""/>
                                    </c:otherwise>
                                </c:choose>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td align="left"><font color="red">1、列印权限申請需廠部級主管核准</font><br/>
                                2、NPI區域用戶請填寫以下附件并上傳<a href="${ctx}/wfprintauthinfoprocess/downLoad/printCommitment">NPI列印帳號使用承諾書</a>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','列印權限申請');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="16%" style="float: left;margin-left: 5px;" id="yl3Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno3_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(23,'yl3Table','ylno3','ylname3',$('#applychoosefactoryid').combobox('getValue'),'wfprintauthinfoprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno3" name="wfprintauthinfoprocess.ylno3"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno3']}"
                                                               readonly
                                                               value="${wfprintauthinfoprocessEntity.ylno3 }"/><c:if
                                                            test="${requiredMap['ylno3'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname3" name="wfprintauthinfoprocess.ylname3"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno3']}"
                                                                value="${wfprintauthinfoprocessEntity.ylname3 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="16%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#applyfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="wfprintauthinfoprocess.bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfprintauthinfoprocessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename"
                                                                name="wfprintauthinfoprocess.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfprintauthinfoprocessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="16%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#dealdeptno').val(),'cchargeno','cchargename',$('#applyfactoryid').val(),'wfprintauthinfoprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wfprintauthinfoprocess.cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfprintauthinfoprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename"
                                                                name="wfprintauthinfoprocess.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfprintauthinfoprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zxywkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zxywkchargeno_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'zxywkchargeTable','zxywkchargeno','zxywkchargename',$('#applychoosefactoryid').combobox('getValue'),'wfprintauthinfoprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxywkchargeno"
                                                               name="wfprintauthinfoprocess.zxywkchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxywkchargeno']}"
                                                               readonly
                                                               value="${wfprintauthinfoprocessEntity.zxywkchargeno }"/><c:if
                                                            test="${requiredMap['zxywkchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxywkchargename"
                                                                name="wfprintauthinfoprocess.zxywkchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxywkchargeno']}"
                                                                value="${wfprintauthinfoprocessEntity.zxywkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="16%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno2_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(176,'yl2Table','ylno2','ylname2',$('#applychoosefactoryid').combobox('getValue'),'wfprintauthinfoprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="wfprintauthinfoprocess.ylno2"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}"
                                                               readonly
                                                               value="${wfprintauthinfoprocessEntity.ylno2 }"/><c:if
                                                            test="${requiredMap['ylno2'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname2" name="wfprintauthinfoprocess.ylname2"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}"
                                                                value="${wfprintauthinfoprocessEntity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfprintauthinfoprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;"
                                   onclick="canelTask('${wfprintauthinfoprocessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="areaId" name="areaId" value=""/>
    <input type="hidden" id="buildingId" name="buildingId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="onlyKchargeSignle" value="1" />
    <input id="isExcel" type="hidden" value=""/>
    <div id="win"></div>
    <div id="dlg"></div>
</form>
</div>
<div id="optionWin" class="easyui-window" title="列印權限信息導入" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%">
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="file" type="file" style="width: 300px"
                           accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>
                    <input id="isExcel2003" name="isExcel2003" type="hidden" value="">

                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr>
                <td align="left" style="width: 60%; white-space: nowrap;">
                    <div id="tishi">正在導入中，請稍後...</div>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <span id="labelListAddResult"></span><a href="${ctx}/wfprintauthinfoprocess/downLoad/errorExcel"
                                                            id="downloadError"
                                                            plain="true">查看錯誤信息</a>
                </td>
            </tr>
        </table>
    </form>
</div>
<script src='${ctx}/static/js/information/wfprintauthinfoprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>