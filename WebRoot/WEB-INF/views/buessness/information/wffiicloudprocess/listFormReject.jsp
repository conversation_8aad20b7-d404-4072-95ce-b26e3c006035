<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>Fii cloud端口申請</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wffiicloudprocess/${action}" method="post">
    <!--
          workflowid 流程編碼
serialno 工單流水號
processid 工單實例ID
factoryid 廠區代碼
makerno 填單人
makername 填單人名稱
makerfactoryid 填單人廠區編碼
createtime 創建時間
makerip 填單人IP
attachids 附件ID
dealno 承辦人工號
dealname 承辦人名稱
dealdeptno 承辦人部門名稱
dealdeptname 承辦人部門
dealfactoryid 承辦人廠區代碼
dealemail 承辦人郵箱
dealtel 承辦人電話
applyno 申請人工號
applyname 申請人姓名
applydeptno 申請人部門代碼
applydeptname 申請人部門名稱
applycostno 申請人費用代碼
applyleveltype 申請人資位 dict_leveltype
applymanager 申請人管理職 dict_ismanager
applyfactoryid 申請人廠區代碼
applychoosefactoryid 申請人選擇廠區代碼
applyemail 申請人郵箱
applyphone 申請人聯繫電話
describtion 需求說明
workstatus 表單狀態
applyarea 申請人申請區域
applybuilding 申請人申請樓棟
securityarea 申請區域安保類型
ylno2 經管主管
ylname2 經管主管
ylno4 Fii Cloud主管
ylname4 Fii Cloud主管
ylno6 資訊處級主管
ylname6 資訊處級主管
ylno7 IT最高主管
ylname7 IT最高主管
ylno8 資訊雲平台課級主管
ylname8 資訊雲平台課級主管
complettime 完成時間
ylno14 資安部級主管
ylname14 資安部級主管
id 主鍵（舊翻新時新增）
createBy 創建人（舊翻新時新增）
createDate 創建時間（舊翻新時新增）
updateBy 更新者（舊翻新時新增）
updateDate 更新時間（舊翻新時新增）
delFlag 刪除標識（舊翻新時新增）
makerdeptno 填單人單位代碼（舊翻新時新增）
viewbagresult 拋轉富鴻網調用接口返回結果
     -->
    <input id="ids" name="ids" type="hidden" value="${wffiicloudprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wffiicloudprocessEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wffiicloudprocessEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wffiicloudprocessEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wffiicloudprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${wffiicloudprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">Fii cloud端口申請</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wffiicloudprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wffiicloudprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wffiicloudprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wffiicloudprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wffiicloudprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wffiicloudprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wffiicloudprocessEntity.makerno}/${wffiicloudprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="dealno" name="dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wffiicloudprocessEntity.dealno}" onblur="queryUserInfo('deal');"/>
                            </td>
                            <td width="3%">承辦人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wffiicloudprocessEntity.dealname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wffiicloudprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="5%">廠區&nbsp;</td>
                            <td width="5%" class="td_style1">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wffiicloudprocessEntity.dealfactoryid }" disabled
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'dealfactoryid\',\'请選擇承辦人廠區\']'"/>
                            </td>
                            <td width="4%">聯繫分機&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="dealtel" name="dealtel" class="easyui-validatebox"
                                       style="width:90px;position:relative;"
                                       value="${wffiicloudprocessEntity.dealtel }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="dealdeptname" name="dealdeptname" class="easyui-validatebox"
                                       data-options="width: 410,required:true"
                                       value="${wffiicloudprocessEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="dealemail" class="easyui-validatebox"
                                       value="${wffiicloudprocessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td>申請人工號&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wffiicloudprocessEntity.applyno}" onblur="queryUserInfo('apply');"/>
                            </td>
                            <td>申請人</td>
                            <td class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wffiicloudprocessEntity.applyname}"/>
                            </td>
                            <td>單位代碼</td>
                            <td class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wffiicloudprocessEntity.applydeptno }"/>
                            </td>
                            <td>費用代碼&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applycostno" name="applycostno"
                                       class="easyui-validatebox" data-options="width: 90,required:true"
                                       value="${wffiicloudprocessEntity.applycostno }"/>
                            </td>
                            <td>所在廠區&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applychoosefactoryid" name="applychoosefactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wffiicloudprocessEntity.applychoosefactoryid }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applychoosefactoryid\',\'请選擇申請人廠區\']',onSelect:function(){onchangeFactory('applychoosefactoryid');}"/>
                                <input id="applyfactoryid" name="applyfactoryid" type="hidden"
                                       value="${wffiicloudprocessEntity.applyfactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="applyleveltype" class="easyui-validatebox inputCss"
                                       data-options="width: 80"
                                       value="${wffiicloudprocessEntity.applyleveltype }" readonly/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="applymanager" class="easyui-validatebox inputCss"
                                       data-options="width: 80"
                                       value="${wffiicloudprocessEntity.applymanager }" readonly/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       value="${wffiicloudprocessEntity.applyemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td>使用區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyarea" name="applyarea" class="easyui-combobox"
                                       data-options="width: 70,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wffiicloudprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="applybuilding" class="easyui-combobox"
                                       data-options="width: 70,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wffiicloudprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyphone" name="applyphone" class="easyui-validatebox"
                                       style="width:90px;position:relative;"
                                       value="${wffiicloudprocessEntity.applyphone }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox"
                                       data-options="width: 410,required:true"
                                       value="${wffiicloudprocessEntity.applydeptname }"/>
                            </td>
                            <td>安保區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style2">
                                <div class="securityareaDiv"></div>
                                <input id="securityarea" name="securityarea"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wffiicloudprocessEntity.securityarea }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">Fii Cloud端口申請單</td>
                        </tr>
                        <tr align="center">
                            <td>需求說明&nbsp;<font color="red">*</font></td>
                            <td align="left" colspan="9"><textarea
                                    id="describtion"
                                    name="describtion" data-options="required:true"
                                    oninput="return LessThanAuto(this,'txtNum');"
                                    onchange="return LessThanAuto(this,'txtNum');"
                                    onpropertychange="return LessThanAuto(this,'txtNum');"
                                    maxlength="60"
                                    class="easyui-validatebox" style="width:900px;height:40px;"
                                    rows="5" cols="6">${wffiicloudprocessEntity.describtion}</textarea><span
                                    id="txtNum"></span></td>
                        </tr>
                        <tr align="center">
                            <td>備註/要求</td>
                            <td align="left" colspan="9">請按附件格式填寫并上傳<a
                                    href="${ctx}/wffiicloudprocess/downLoad/signpathargument">端口開通申請表</a></td>
                        </tr>
                        <tr align="center">
                            <td>附件&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button"
                                                                   value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span> <input type="hidden" id="attachids"
                                               name="attachids" value="${wffiicloudprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','Fii cloud端口申請');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="16%" style="float: left;margin-left: 5px;" id="yl8Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">資訊雲平臺課級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(157,'ylno8','ylname8',$('#applychoosefactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno8" name="ylno8"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno8']}"
                                                               readonly
                                                               value="${wffiicloudprocessEntity.ylno8 }"/><c:if
                                                            test="${requiredMap['ylno8'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname8" name="ylname8"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno8']}"
                                                                value="${wffiicloudprocessEntity.ylname8 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="16%" style="float: left;margin-left: 5px;" id="yl14Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">資安部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(154,'yl14Table','ylno14','ylname14',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno14" name="ylno14"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno14']}"
                                                               readonly
                                                               value="${wffiicloudprocessEntity.ylno14 }"/><c:if
                                                            test="${requiredMap['ylno14'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname14" name="ylname14"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno14']}"
                                                                value="${wffiicloudprocessEntity.ylname14 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="16%" style="float: left;margin-left: 5px;" id="yl4Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">FiiCloud主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(158,'yl4Table','ylno4','ylname4',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno4" name="ylno4"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno4']}"
                                                               readonly
                                                               value="${wffiicloudprocessEntity.ylno4 }"/><c:if
                                                            test="${requiredMap['ylno4'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname4" name="ylname4"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno4']}"
                                                                value="${wffiicloudprocessEntity.ylname4 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="16%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">經管主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(62,'yl2Table','ylno2','ylname2',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="ylno2"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}"
                                                               readonly
                                                               value="${wffiicloudprocessEntity.ylno2 }"/><c:if
                                                            test="${requiredMap['ylno2'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname2" name="ylname2"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}"
                                                                value="${wffiicloudprocessEntity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="16%" style="float: left;margin-left: 5px;" id="yl6Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">資訊處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(156,'yl6Table','ylno6','ylname6',$('#applychoosefactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno6" name="ylno6"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno6']}"
                                                               readonly
                                                               value="${wffiicloudprocessEntity.ylno6 }"/><c:if
                                                            test="${requiredMap['ylno6'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname6" name="ylname6"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno6']}"
                                                                value="${wffiicloudprocessEntity.ylname6 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="16%" style="float: left;margin-left: 5px;" id="yl7Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">IT最高主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(187,'ylno7','ylname7',$('#applychoosefactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno7" name="ylno7"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno7']}"
                                                               readonly
                                                               value="${wffiicloudprocessEntity.ylno7 }"/><c:if
                                                            test="${requiredMap['ylno7'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname7" name="ylname7"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno7']}"
                                                                value="${wffiicloudprocessEntity.ylname7 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wffiicloudprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="canelTask('${wffiicloudprocessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="areaId" name="areaId" value=""/>
    <input type="hidden" id="buildingId" name="buildingId" value=""/>
    <input type="hidden" id="loginname" name="loginname" value="${user.loginName}"/>
    <div id="win"></div>
    <div id="dlg"></div>
</form>
</div>
<script src='${ctx}/static/js/information/wffiicloudprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>