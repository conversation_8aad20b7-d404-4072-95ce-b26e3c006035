<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>Fii cloud端口申請</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wffiicloudprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wffiicloudprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wffiicloudprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">Fii cloud端口申請</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wffiicloudprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wffiicloudprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wffiicloudprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wffiicloudprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wffiicloudprocessEntity.makerno}/${wffiicloudprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">承辦人工號</td>
                            <td width="5%" class="td_style1">
                                <input id="dealno" name="dealno" class="easyui-validatebox inputCss"
                                       data-options="width: 80,required:true" readonly
                                       value="${wffiicloudprocessEntity.dealno}"/>
                            </td>
                            <td width="3%">承辦人</td>
                            <td width="6%" align="left">${wffiicloudprocessEntity.dealname }</td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" align="left">${wffiicloudprocessEntity.dealdeptno }</td>
                            <td width="5%">廠區</td>
                            <td width="5%" class="td_style1">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wffiicloudprocessEntity.dealfactoryid }" disabled
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'dealfactoryid\',\'请選擇承辦人廠區\']'"/>
                            </td>
                            <td width="4%">聯繫分機</td>
                            <td width="7%" align="left">${wffiicloudprocessEntity.dealtel }</td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="5" align="left">${wffiicloudprocessEntity.dealdeptname }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" align="left">${wffiicloudprocessEntity.dealemail }</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td>申請人工號</td>
                            <td align="left">${wffiicloudprocessEntity.applyno}</td>
                            <td>申請人</td>
                            <td align="left">${wffiicloudprocessEntity.applyname}</td>
                            <td>單位代碼</td>
                            <td align="left">${wffiicloudprocessEntity.applydeptno }</td>
                            <td>費用代碼</td>
                            <td align="left">${wffiicloudprocessEntity.applycostno }</td>
                            <td>所在廠區</td>
                            <td class="td_style1">
                                <input id="applychoosefactoryid" name="applychoosefactoryid" disabled class="easyui-combobox"
                                       panelHeight="auto" value="${wffiicloudprocessEntity.applychoosefactoryid }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applychoosefactoryid\',\'请選擇申請人廠區\']',onSelect:function(){onchangeFactory('applychoosefactoryid');}"/>
                                <input id="applyfactoryid" name="applyfactoryid" type="hidden"
                                       value="${wffiicloudprocessEntity.applyfactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td align="left">${wffiicloudprocessEntity.applyleveltype }</td>
                            <td>管理職</td>
                            <td align="left">${wffiicloudprocessEntity.applymanager }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" align="left">${wffiicloudprocessEntity.applyemail }</td>
                            <td>使用區域</td>
                            <td class="td_style1">
                                <input id="applyarea" name="applyarea" class="easyui-combobox" disabled data-options="width: 70,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wffiicloudprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="applybuilding" disabled class="easyui-combobox" data-options="width: 70,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wffiicloudprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機</td>
                            <td align="left">${wffiicloudprocessEntity.applyphone }</td>
                            <td>單位</td>
                            <td colspan="5" align="left">${wffiicloudprocessEntity.applydeptname }</td>
                            <td>安保區域</td>
                            <td class="td_style2">
                                <div class="securityareaDiv"></div>
                                <input id="disOrEnabled" value="disabled" type="hidden"/>
                                <input id="securityarea" name="securityarea"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wffiicloudprocessEntity.securityarea }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">Fii Cloud端口申請單</td>
                        </tr>
                        <tr align="center">
                            <td>需求說明</td>
                            <td align="left" colspan="9"><textarea
                                    id="describtion"
                                    name="describtion" data-options="required:true"
                                    readonly
                                    maxlength="60"
                                    class="easyui-validatebox" style="width:900px;height:40px;"
                                    rows="5" cols="6">${wffiicloudprocessEntity.describtion}</textarea><span
                                    id="txtNum"></span></td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td  colspan="9" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${wffiicloudprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%">
						<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wffiicloudprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','Fii cloud端口申請');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wffiicloudprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<div id="dlg"></div>
<script src='${ctx}/static/js/information/wffiicloudprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>