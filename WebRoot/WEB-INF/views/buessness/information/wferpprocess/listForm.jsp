<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>Tiptop系統需求/變更申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
        .td_style3{
            border: none 0px;
        }
        .applytypeDiv {
            float: left;
            width: 80%;
        }
        .applysystemtypeDiv{
            float: left;
            width: 15%;
        }
        .starRed{
            display: inline;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wferpprocess/${action}" method="post">
    <input id="makerno" name="makerno" type="hidden" value="${wferpprocess.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wferpprocess.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wferpprocess.makerdeptno }"/>

    <input id="ids" name="ids" type="hidden" value="${wferpprocess.id }"/>
    <input id="serialno" name="wferpprocess.serialno" type="hidden" value="${wferpprocess.serialno }"/>
    <div class="commonW">
        <div class="headTitle">Tiptop系統需求/變更申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wferpprocess.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wferpprocess.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wferpprocess.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wferpprocess.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wferpprocess.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wferpprocess.makerno}">
            <div class="position_R margin_R">填單人：${wferpprocess.makerno}/${wferpprocess.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="wferpprocess.applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wferpprocess.applyno}" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="wferpprocess.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wferpprocess.applyname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="wferpprocess.applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wferpprocess.applydeptno }"/>
                            </td>
                            <td width="4%">費用代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applycostno" name="wferpprocess.applycostno" class="easyui-validatebox" data-options="width: 80"
                                       value="${wferpprocess.applycostno }"/>
                            </td>
                            <td width="4%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="wferpprocess.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wferpprocess.applyfactoryid }"
                                       data-options="width: 120,required:true,onSelect:function(){onchangeFactory();}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="wferpprocess.applydeptname" class="easyui-validatebox" data-options="width: 410,required:true"
                                       value="${wferpprocess.applydeptname }"/>
                            </td>
                            <td>申請日期&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applytime" name="wferpprocess.applytime" class="easyui-validatebox"  data-options="width:100,required:true" readonly
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wferpprocess.applytime}"/>"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>申請人職能&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyfunction" name="wferpprocess.applyfunction" class="easyui-combobox"
                                       value="${wferpprocess.applyfunction }"  editable="false"/>
                            </td>
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applytel" name="wferpprocess.applytel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wferpprocess.applytel }" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="wferpprocess.applyemail" class="easyui-validatebox"
                                       value="${wferpprocess.applyemail }" style="width:300px;"
                                       data-options="required:true" onblur="valdEmail('')"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td>需求類別&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style2">
                                <div class="applysystemtypeDiv">
                                    <input id="applysystemtype"
                                           name="wferpprocess.applysystemtype"
                                           class="easyui-combobox"
                                           value="${wferpprocess.applysystemtype}"
                                           data-options="width: 140,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/APPLY_SYSTEM_TYPE',onLoadSuccess: function () { var data = $('#applysystemtype').combobox('getData');if (data.length > 0) {$('#applysystemtype').combobox('select', data[0].value);}},onSelect:function(){onchangeSystemtype();}"/>
                                </div>
                                <div class="applytypeDiv"></div>
                                <input id="applytype" name="wferpprocess.applytype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${wferpprocess.applytype }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>需求描述&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style1">
						    <textarea id="applydescribtion" name="wferpprocess.applydescribtion"
                                      class="easyui-validatebox"
                                      oninput="return LessThan(this);"
                                      onchange="return LessThan(this);"
                                      onpropertychange="return LessThan(this);"
                                      maxlength="300"
                                      style="width:99%;height:80px;" data-options="required:true,prompt:'請需求單位詳細說明，如欄位不夠，請附件說明'"
                                      rows="5" cols="6"
                                      data-options="required:true,validType:'length[0,300]'">${wferpprocess.applydescribtion }</textarea><span id="txtNum"></span></td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="wferpprocess.attachids" value="${wferpprocess.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="9" class="td_style1">會簽部門需簽核至部級主管。</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">

                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_ERPxitongxuqiubiangengshenqingdan_v3','經管類系統需求/變更申請單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="wferpprocess.kchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['kchargeno']}" readonly
                                                               value="${wferpprocess.kchargeno }"/><c:if test="${requiredMap['kchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="kchargename" name="wferpprocess.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wferpprocess.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="wferpprocess.bchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wferpprocess.bchargeno }"/><c:if test="${requiredMap['bchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="bchargename" name="wferpprocess.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wferpprocess.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"  id="zxcTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">中心級/處級主管
                                                                    <a href="#" onclick="addHq('zxc');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxcno" onblur="getUserNameByEmpno(this,'zxc');" name="wferpprocess.zxcno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zxcno']}" value="${wferpprocess.zxcno }"/>
                                                        <c:if test="${requiredMap['zxcno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="zxcname" name="wferpprocess.zxcname" readonly
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['zxcno']}"  value="${wferpprocess.zxcname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"  id="jgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">經管審核</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRoleForERP(63,'jgchargeTable','jgchargeno','jgchargename',$('#applyfactoryid').combobox('getValue'),'wferpprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <%--<input id="jgchargeno" name="wferpprocess.jgchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['jgchargeno']}"
                                                               readonly
                                                               value="${wferpprocess.jgchargeno }"/><c:if test="${requiredMap['jgchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="jgchargename" name="wferpprocess.jgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jgchargeno']}"
                                                                value="${wferpprocess.jgchargename }"/>--%>
                                                        <input id="jgchargeno" name="wferpprocess.jgchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:false"
                                                               readonly value="${wferpprocess.jgchargeno }"/>
                                                        <div class="starRed"><font color="red">*</font></div>
                                                        /<input id="jgchargename" name="wferpprocess.jgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:false"
                                                                value="${wferpprocess.jgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"  id="jgzchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">經管主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRoleForERP(62,'jgzchargeTable','jgzchargeno','jgzchargename',$('#applyfactoryid').combobox('getValue'),'wferpprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <%--<input id="jgzchargeno" name="wferpprocess.jgzchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['jgzchargeno']}"
                                                               readonly
                                                               value="${wferpprocess.jgzchargeno }"/><c:if test="${requiredMap['jgzchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="jgzchargename" name="wferpprocess.jgzchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jgzchargeno']}"
                                                                value="${wferpprocess.jgzchargename }"/>--%>

                                                            <input id="jgzchargeno" name="wferpprocess.jgzchargeno"
                                                                    class="easyui-validatebox" data-options="width:80,required:false"
                                                                    readonly value="${wferpprocess.jgzchargeno }"/>
                                                            <div class="starRed"><font color="red">*</font></div>
                                                            /<input id="jgzchargename" name="wferpprocess.jgzchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:false"
                                                                value="${wferpprocess.jgzchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"  id="hqochargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽部門1
                                                                    <a href="#" onclick="addHq('hqocharge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hqochargeno" onblur="getUserNameByEmpno(this,'hqocharge');" name="wferpprocess.hqochargeno"
                                                               class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hqochargeno']}" value="${wferpprocess.hqochargeno }"/>
                                                        <c:if test="${requiredMap['hqochargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hqochargename" name="wferpprocess.hqochargename" readonly
                                                                class="easyui-validatebox" data-options="width: 80,required:${requiredMap['hqochargeno']}"  value="${wferpprocess.hqochargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"  id="hqtchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽部門2
                                                                    <a href="#" onclick="addHq('hqtcharge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hqtchargeno" onblur="getUserNameByEmpno(this,'hqtcharge');" name="wferpprocess.hqtchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['hqtchargeno']}"
                                                               value="${wferpprocess.hqtchargeno }"/><c:if test="${requiredMap['hqtchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hqtchargename" name="wferpprocess.hqtchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hqtchargeno']}"
                                                                value="${wferpprocess.hqtchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"  id="hqschargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽部門3
                                                                    <a href="#" onclick="addHq('hqscharge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hqschargeno" onblur="getUserNameByEmpno(this,'hqscharge');" name="wferpprocess.hqschargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['hqschargeno']}"
                                                               value="${wferpprocess.hqschargeno }"/><c:if test="${requiredMap['hqschargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hqschargename" name="wferpprocess.hqschargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hqschargeno']}"
                                                                value="${wferpprocess.hqschargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"  id="hqfchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">會簽部門4
                                                                    <a href="#" onclick="addHq('hqfcharge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hqfchargeno" onblur="getUserNameByEmpno(this,'hqfcharge');" name="wferpprocess.hqfchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['hqfchargeno']}"
                                                               value="${wferpprocess.hqfchargeno }"/><c:if test="${requiredMap['hqfchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hqfchargename" name="wferpprocess.hqfchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hqfchargeno']}"
                                                                value="${wferpprocess.hqfchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"  id="kfgjdchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">系統開發工程師接單</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole4(89,'kfgjdchargeno','kfgjdchargename',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kfgjdchargeno" name="wferpprocess.kfgjdchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['kfgjdchargeno']}"
                                                               readonly
                                                               value="${wferpprocess.kfgjdchargeno }"/><c:if test="${requiredMap['kfgjdchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="kfgjdchargename" name="wferpprocess.kfgjdchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kfgjdchargeno']}"
                                                                value="${wferpprocess.kfgjdchargename }"/>
                                                       <%-- <input id="kfgqchargeno" name="wferpprocess.kfgqchargeno" type="hidden" value="${wferpprocess.kfgqchargeno }"/>
                                                        <input id="kfgqchargename" name="wferpprocess.kfgqchargename" type="hidden" value="${wferpprocess.kfgqchargename }"/>--%>
                                                    </td>
                                                </tr>
                                            </table>

                                            <%--<table width="18%" style="float: left;margin-left: 5px;"  id="kfgqchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">系統開發工程師確認</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(64,'kfgqchargeTable','kfgqchargeno','kfgqchargename',$('#applyfactoryid').combobox('getValue'),'wferpprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kfgqchargeno" name="wferpprocess.kfgqchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['kfgqchargeno']}"
                                                               readonly
                                                               value="${wferpprocess.kfgqchargeno }"/><c:if test="${requiredMap['kfgqchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="kfgqchargename" name="wferpprocess.kfgqchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kfgqchargeno']}"
                                                                value="${wferpprocess.kfgqchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>--%>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"  id="kfkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">系統開發課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(65,'kfkchargeTable','kfkchargeno','kfkchargename',$('#applyfactoryid').combobox('getValue'),'wferpprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kfkchargeno" name="wferpprocess.kfkchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['kfkchargeno']}"
                                                               readonly
                                                               value="${wferpprocess.kfkchargeno }"/><c:if test="${requiredMap['kfkchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="kfkchargename" name="wferpprocess.kfkchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kfkchargeno']}"
                                                                value="${wferpprocess.kfkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"  id="kfbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">系統開發部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(66,'kfbchargeTable','kfbchargeno','kfbchargename',$('#applyfactoryid').combobox('getValue'),'wferpprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kfbchargeno" name="wferpprocess.kfbchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['kfbchargeno']}"
                                                               readonly
                                                               value="${wferpprocess.kfbchargeno }"/><c:if test="${requiredMap['kfbchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="kfbchargename" name="wferpprocess.kfbchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kfbchargeno']}"
                                                                value="${wferpprocess.kfbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"  id="xtkfzxjzgTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">系統開發中心級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(261,'xtkfzxjzgTable','xtkfzxjzgno','xtkfzxjzgname',$('#applyfactoryid').combobox('getValue'),'wferpprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="xtkfzxjzgno" name="wferpprocess.xtkfzxjzgno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['xtkfzxjzgno']}"
                                                               readonly
                                                               value="${wferpprocess.xtkfzxjzgno }"/><c:if test="${requiredMap['xtkfzxjzgno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="xtkfzxjzgname" name="wferpprocess.xtkfzxjzgname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['xtkfzxjzgno']}"
                                                                value="${wferpprocess.xtkfzxjzgname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"  id="kfgzchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">系統開發工程師作業</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole5('67','kfgzchargeno','kfgzchargename','kfgjchargeno','kfgjchargename','','',$('#applyfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kfgzchargeno" name="wferpprocess.kfgzchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['kfgzchargeno']}"
                                                               readonly
                                                               value="${wferpprocess.kfgzchargeno }"/><c:if test="${requiredMap['kfgzchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="kfgzchargename" name="wferpprocess.kfgzchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kfgzchargeno']}"
                                                                value="${wferpprocess.kfgzchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"  id="kfgjchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">系統開發工程師結案</td>
                                                                <td style="border: none;">
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kfgjchargeno" name="wferpprocess.kfgjchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['kfgjchargeno']}"
                                                               readonly
                                                               value="${wferpprocess.kfgjchargeno }"/><c:if test="${requiredMap['kfgjchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="kfgjchargename" name="wferpprocess.kfgjchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kfgjchargeno']}"
                                                                value="${wferpprocess.kfgjchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">用戶確認</td>
                                                                <td style="border: none;">
                                                                    <div></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="yhqchargeno" name="wferpprocess.yhqchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['yhqchargeno']}"
                                                               readonly
                                                               value="${wferpprocess.yhqchargeno }"/><c:if test="${requiredMap['yhqchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="yhqchargename" name="wferpprocess.yhqchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['yhqchargeno']}"
                                                                value="${wferpprocess.yhqchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
</div>
<script src='${ctx}/static/js/information/wferpprocess.js?random=<%= Math.random()%>'></script>
<script type="text/javascript">
    if ("${wferpprocess.hqochargeno}" != "") {
        var nostr = "${wferpprocess.hqochargeno}";
        var namestr = "${wferpprocess.hqochargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hqochargeTable tr:eq(" + (i + 2) + ")").find("#hqochargeno").val(notr[i]);
            $("#hqochargeTable tr:eq(" + (i + 2) + ")").find("#hqochargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hqochargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hqochargeno' name='wferpprocess.hqochargeno' style='width: 80px' value='' onblur='getUserNameByEmpno(this,'hqocharge');'/>/<input id='hqochargename' name='wferpprocess.hqochargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wferpprocess.hqtchargeno}" != "") {
        var nostr = "${wferpprocess.hqtchargeno}";
        var namestr = "${wferpprocess.hqtchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hqtchargeTable tr:eq(" + (i + 2) + ")").find("#hqtchargeno").val(notr[i]);
            $("#hqtchargeTable tr:eq(" + (i + 2) + ")").find("#hqtchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hqtchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hqtchargeno' name='wferpprocess.hqtchargeno' style='width: 80px' value='' onblur='getUserNameByEmpno(this,'hqtcharge');'/>/<input id='hqtchargename' name='wferpprocess.hqtchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wferpprocess.hqschargeno}" != "") {
        var nostr = "${wferpprocess.hqschargeno}";
        var namestr = "${wferpprocess.hqschargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hqschargeTable tr:eq(" + (i + 2) + ")").find("#hqschargeno").val(notr[i]);
            $("#hqschargeTable tr:eq(" + (i + 2) + ")").find("#hqschargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hqschargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hqschargeno' name='wferpprocess.hqschargeno' style='width: 80px' value='' onblur='getUserNameByEmpno(this,'hqscharge');'/>/<input id='hqschargename' name='wferpprocess.hqschargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wferpprocess.hqfchargeno}" != "") {
        var nostr = "${wferpprocess.hqfchargeno}";
        var namestr = "${wferpprocess.hqfchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hqfchargeTable tr:eq(" + (i + 2) + ")").find("#hqfchargeno").val(notr[i]);
            $("#hqfchargeTable tr:eq(" + (i + 2) + ")").find("#hqfchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hqfchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hqfchargeno' name='wferpprocess.hqfchargeno' style='width: 80px' value='' onblur='getUserNameByEmpno(this,'hqfcharge');'/>/<input id='hqfchargename' name='wferpprocess.hqfchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
</script>
</body>
<script src='${ctx}/static/js/information/wferpprocess.js?random=<%= Math.random()%>'></script>
</html>
