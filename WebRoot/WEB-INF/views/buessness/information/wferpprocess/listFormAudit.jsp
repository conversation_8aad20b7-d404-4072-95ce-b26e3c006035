<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>Tiptop系統需求/變更申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
<script src='${ctx}/static/js/information/wferpprocess.js?random=<%= Math.random()%>'></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
        .td_style3{
            border: none 0px;
        }
        .applytypeDiv {
            float: left;
            width: 80%;
        }
        .applysystemtypeDiv{
            float: left;
            width: 15%;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wferpprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wferpprocess.id }"/>
    <input id="serialno" name="wferpprocess.serialno" type="hidden" value="${wferpprocess.serialno }"/>
    <div class="commonW">
        <div class="headTitle">Tiptop系統需求/變更申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wferpprocess.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wferpprocess.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wferpprocess.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wferpprocess.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wferpprocess.makerno}/${wferpprocess.makername}</div>
        <div class="clear"></div>
        <table class="formList">
           <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">申請人工號</td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="wferpprocess.applyno" class="easyui-validatebox inputCss"
                                       data-options="width: 80" value="${wferpprocess.applyno}" readonly/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="wferpprocess.applyname" class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wferpprocess.applyname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="wferpprocess.applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wferpprocess.applydeptno }"/>
                            </td>
                            <td width="4%">費用代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applycostno" name="wferpprocess.applycostno" class="easyui-validatebox inputCss"
                                       data-options="width: 80" value="${wferpprocess.applycostno }" readonly/>
                            </td>
                            <td width="4%">所在廠區</td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="wferpprocess.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wferpprocess.applyfactoryid }" disabled
                                       data-options="width: 120"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="wferpprocess.applydeptname" class="easyui-validatebox inputCss"
                                       data-options="width: 410" value="${wferpprocess.applydeptname }" readonly/>
                            </td>
                            <td>申請日期</td>
                            <td colspan="3" class="td_style1">
                                <input id="applytime" name="wferpprocess.applytime" class="easyui-validatebox inputCss"  data-options="width:100" readonly
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wferpprocess.applytime}"/>"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>申請人職能</td>
                            <td colspan="3" class="td_style1">
                                <input id="applyfunction" name="wferpprocess.applyfunction" class="easyui-combobox"
                                       value="${wferpprocess.applyfunction }" panelHeight="auto" editable="false" disabled/>
                            </td>
                            <td>聯繫分機</td>
                            <td class="td_style1">
                                <input id="applytel" name="wferpprocess.applytel" class="easyui-validatebox inputCss"
                                       style="width:90px;" value="${wferpprocess.applytel }" readonly/>
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="wferpprocess.applyemail" class="easyui-validatebox"
                                       value="${wferpprocess.applyemail }" style="width:300px;" readonly/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td>需求類別</td>
                            <td colspan="9" class="td_style2">
                                <c:if test="${wferpprocess.applysystemtype!=''&& wferpprocess.applysystemtype!=null}">
                                    <div class="applysystemtypeDiv">
                                        <input id="applysystemtype"
                                               name="wferpprocess.applysystemtype"
                                               class="easyui-combobox" disabled
                                               value="${wferpprocess.applysystemtype}"
                                               data-options="width: 140,validType:'comboxValidate[\'applysystemtype\',\'请选择系統類別\']',valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/APPLY_SYSTEM_TYPE',loadFilter: function (data){data.unshift({ value:'',label:'請選擇'});return data; },onSelect:function(){onchangeSystemtype();}"/>
                                    </div>
                                </c:if>

                                <div class="applytypeDiv"></div>
                                <input id="applytype" name="wferpprocess.applytype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${wferpprocess.applytype }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>需求描述</td>
                            <td colspan="9" class="td_style1">
						    <textarea id="applydescribtion" name="wferpprocess.applydescribtion" readonly
                                      class="easyui-validatebox" style="width:99%;height:80px;" rows="5" cols="6">${wferpprocess.applydescribtion }</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <c:if test="${wferpprocess.attachids!=null}">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${wferpprocess.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                                </c:if>
                                <c:if test="${wferpprocess.attachids==null}">無</c:if>
                            </td>
                        </tr>
                        <c:choose>
                            <c:when test="${not empty nodeName&&'系統開發工程師接單' eq nodeName}">
                                <tr align="left">
                                    <td colspan="10" class="td_style1">系統開發工程師評估</td>
                                </tr>
                                <tr align="center">
                                    <td colspan="1">評估內容&nbsp;<font color="red">*</font></td>
                                    <td colspan="9" class="td_style1">
						                <textarea id="assesscontent" name="wferpprocess.assesscontent"
                                                  class="easyui-validatebox"
                                                  oninput="return LessThan(this);"
                                                  onchange="return LessThan(this);"
                                                  onpropertychange="return LessThan(this);"
                                                  maxlength="500"
                                                  style="width:99%;height:80px;" data-options="required:true"
                                                  rows="5" cols="6"
                                                  data-options="required:true,validType:'length[0,500]'">${wferpprocess.assesscontent }</textarea><span id="txtNum"></span></td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <c:if test="${nodeOrder ge 9}">
                                    <tr align="left">
                                        <td colspan="10" class="td_style1">系統開發工程師評估</td>
                                    </tr>
                                    <tr align="center">
                                        <td colspan="1">評估內容&nbsp;<font color="red">*</font></td>
                                        <td colspan="9" class="td_style1">
                                        <textarea name="wferpprocess.assesscontent" readonly
                                                  class="easyui-validatebox" style="width:99%;height:80px;" rows="5" cols="6">${wferpprocess.assesscontent }</textarea>
                                        </td>
                                    </tr>
                                </c:if>
                            </c:otherwise>
                        </c:choose>

                        <c:choose>
                            <c:when test="${not empty nodeName&&'用戶確認' eq nodeName}">
                                <tr align="left">
                                    <td colspan="10" class="td_style1">用戶確認</td>
                                </tr>
                                <tr align="center">
                                    <td colspan="1">用戶確認</td>
                                    <td colspan="9" class="td_style2">
                                        <div class="userconfirmDiv"></div>
                                        <input id="userconfirm" name="wferpprocess.userconfirm"
                                               type="hidden" class="easyui-validatebox" data-options="width: 150"
                                               value="${wferpprocess.userconfirm}"/>
                                    </td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <c:if test="${wferpprocess.userconfirm!=null&&wferpprocess.userconfirm!=''}">
                                    <tr align="center">
                                        <td colspan="1">用戶確認</td>
                                        <td colspan="9" class="td_style2">
                                            <div class="userconfirmDiv"></div>
                                            <input id="userconfirmAudit" name="wferpprocess.userconfirm"
                                                   type="hidden" class="easyui-validatebox" data-options="width: 150"
                                                   value="${wferpprocess.userconfirm }"/>
                                        </td>
                                    </tr>
                                </c:if>
                            </c:otherwise>
                        </c:choose>
                        <tr align="center">
                            <td colspan="1">批註</td>
                            <td colspan="9" class="td_style2">
                                    <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                              style="width:99%;height:60px;"
                                              rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                    </table>
                </td>
           </tr>

           <tr>
                <td>
                     <table class="formList">
                         <c:choose>
                             <c:when test="${not empty nodeName&&'系統開發工程師接單' eq nodeName}">
                                 <tr align="center">
                                     <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                         <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"  perCall="gcsqupdate"
                                                     serialNo="${wferpprocess.serialno}"></fox:action>
                                     </td>
                                 </tr>
                             </c:when>
                            <%-- <c:when test="${not empty nodeName&&'系統開發工程師結案' eq nodeName}">
                                 <tr align="center">
                                     <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                         <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"  perCall="gcsjupdate"
                                                     serialNo="${wferpprocess.serialno}"></fox:action>
                                     </td>
                                 </tr>
                             </c:when>--%>
                             <c:when test="${not empty nodeName&&'用戶確認' eq nodeName}">
                                 <tr align="center">
                                     <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                         <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"  perCall="yhupdate"
                                                     serialNo="${wferpprocess.serialno}"></fox:action>
                                     </td>
                                 </tr>
                             </c:when>
                             <c:otherwise>
                                 <tr align="center">
                                     <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                         <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                                     serialNo="${wferpprocess.serialno}"></fox:action>
                                     </td>
                                 </tr>
                             </c:otherwise>
                         </c:choose>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','經管類系統需求/變更申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wferpprocess.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
</form>
<div id="dlg"></div>
<input type="hidden" id="nodeName"  value="${nodeName}"/>
<input type="hidden" id="disOrEnabled"  value="disabled"/>
</body>
</html>