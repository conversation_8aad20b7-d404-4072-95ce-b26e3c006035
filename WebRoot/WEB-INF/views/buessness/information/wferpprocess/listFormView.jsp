<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>Tiptop系統需求/變更申請單</title>
<script type="text/javascript">var ctx = "${pageContext.request.contextPath}";</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
<style type="text/css">
    .td_style2 {
        text-align: left;
    }
    .td_style3{
        border: none 0px;
    }
    .applytypeDiv {
        float: left;
        width: 80%;
    }
    .applysystemtypeDiv{
        float: left;
        width: 15%;
    }
</style>
</head>
<body>
<form id="mainform" action="${ctx}/wferpprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wferpprocess.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wferpprocess.serialno }"/>
    <div class="commonW">
    <div class="headTitle">Tiptop系統需求/變更申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wferpprocess.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wferpprocess.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wferpprocess.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wferpprocess.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wferpprocess.makerno}/${wferpprocess.makername}</div>
        <div class="clear"></div>
        <table  class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號</td>
                            <td width="6%" class="td_style2">${wferpprocess.applyno}</td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style2">${wferpprocess.applyname }</td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style2">${wferpprocess.applydeptno }</td>
                            <td width="3%">費用代碼</td>
                            <td width="6%" class="td_style2">${wferpprocess.applycostno }</td>
                            <td width="4%">所在廠區</td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="wferpprocess.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wferpprocess.applyfactoryid }" disabled
                                       data-options="width: 120"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="5" class="td_style2">${wferpprocess.applydeptname }
                            </td>
                            <td>申請日期</td>
                            <td colspan="3" class="td_style1">
                                <input id="applytime" name="wferpprocess.applytime" class="easyui-validatebox inputCss"  data-options="width:100" readonly
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wferpprocess.applytime}"/>"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>申請人職能</td>
                            <td colspan="3" class="td_style1">
                                <input id="applyfunction" name="wferpprocess.applyfunction" class="easyui-combobox"
                                       value="${wferpprocess.applyfunction }" panelHeight="auto" editable="false" disabled/>
                            </td>
                            <td>聯繫分機</td>
                            <td class="td_style2">${wferpprocess.applytel }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wferpprocess.applyemail }</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td>需求類別</td>
                            <td colspan="9" class="td_style2">
                                <c:if test="${wferpprocess.applysystemtype!=''&& wferpprocess.applysystemtype!=null}">
                                    <div class="applysystemtypeDiv">
                                        <input id="applysystemtype"
                                               name="wferpprocess.applysystemtype"
                                               class="easyui-combobox" disabled
                                               value="${wferpprocess.applysystemtype}"
                                               data-options="width: 140,validType:'comboxValidate[\'applysystemtype\',\'请选择系統類別\']',valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/APPLY_SYSTEM_TYPE',loadFilter: function (data){data.unshift({ value:'',label:'請選擇'});return data; },onSelect:function(){onchangeSystemtype();}"/>
                                    </div>
                                </c:if>
                                <div class="applytypeDiv"></div>
                                <input id="applytype" name="wferpprocess.applytype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${wferpprocess.applytype }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>需求描述</td>
                            <td colspan="9" class="td_style1">
						    <textarea id="applydescribtion" name="wferpprocess.applydescribtion" readonly
                                      class="easyui-validatebox" style="width:99%;height:80px;" rows="5" cols="6">${wferpprocess.applydescribtion }</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${wferpprocess.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <c:if test="${wferpprocess.assesscontent!=null}">
                            <tr align="left">
                                <td colspan="10" class="td_style1">系統開發工程師評估</td>
                            </tr>
                            <tr align="center">
                                <td colspan="1">評估內容</td>
                                <td colspan="9" class="td_style1">
                                    <textarea name="wferpprocess.assesscontent" readonly
                                              class="easyui-validatebox" style="width:99%;height:80px;" rows="5" cols="6">${wferpprocess.assesscontent }</textarea>
                                </td>
                            </tr>
                            <%--<c:if test="${count gt 0}">
                            <tr align="center">
                                <td colspan="10" width="100%">
                                    <div style="overflow-x: auto;width: 100%;">
                                        <table width="100%">
                                            <tr align="center">
                                                <td>工號<font color="red">*</font></td>
                                                <td>姓名<font color="red">*</font></td>
                                                <td>技術職<font color="red">*</font></td>
                                                <td>預估工時<font color="red">*</font></td>
                                                <td>處理項目<font color="red">*</font></td>
                                                <td>完工時間</td>
                                            </tr>
                                            <c:forEach items="${erpItemEntity}" var="erpItem" varStatus="status">
                                                <tr align="center" id="erpItem${status.index+1}">
                                                    <td width="10%">${erpItem.empno}</td>
                                                    <td width="10%">${erpItem.empname}</td>
                                                    <td width="15%">
                                                        <input id="erp_technicalpost${status.index+1}" name="wferpitems[${status.index}].technicalpost" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadTechnicalpost(${status.index+1});}" style="width:100px;"
                                                               class="easyui-combobox" editable="false" value="${erpItem.technicalpost}" disabled/>&nbsp;&nbsp;
                                                    </td>
                                                    <td width="15%">${erpItem.estimatehours}人*天</td>
                                                    <td width="30%">${erpItem.treatmentproject}</td>
                                                    <td width="10%">
                                                        <c:if test="${erpItem.makespan!=null}">
                                                            <input class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                                                   data-options="width: 100"
                                                                   value="<fmt:formatDate value="${erpItem.makespan}"/>" disabled/>
                                                        </c:if>

                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            </c:if>--%>
                        </c:if>
                        <c:if test="${wferpprocess.userconfirm!=null}">
                            <tr align="left">
                                <td colspan="10" class="td_style1">用戶確認</td>
                            </tr>
                            <tr align="center">
                                <td colspan="1">用戶確認</td>
                                <td colspan="9" class="td_style2">
                                    <div class="userconfirmDiv"></div>
                                    <input id="userconfirm" name="wferpprocess.userconfirm"
                                           type="hidden" class="easyui-validatebox" data-options="width: 150"
                                           value="${wferpprocess.userconfirm}"/>
                                    <input id="userconfirmAudit" name="wferpprocess.userconfirm"
                                           type="hidden" class="easyui-validatebox" data-options="width: 150"
                                           value="${wferpprocess.userconfirm }"/>
                                </td>
                            </tr>
                        </c:if>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','經管類系統需求/變更申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wferpprocess.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                   style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>

                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
<div id="dlg"></div>
</body>
<script src='${ctx}/static/js/information/wferpprocess.js?random=<%= Math.random()%>'></script>
</html>