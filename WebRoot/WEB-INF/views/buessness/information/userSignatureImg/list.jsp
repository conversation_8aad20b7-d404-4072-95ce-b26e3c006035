<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>用戶簽名檔維護</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input type="text" name="applyno" class="easyui-validatebox"
               data-options="width:150,prompt: '工號'"/>
        <input type="text" name="applyname" class="easyui-validatebox"
               data-options="width:150,prompt: '姓名'"/>
        <input type="text" name="deptno" class="easyui-validatebox"
               data-options="width:150,prompt: '部門代碼'"/>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
           onclick="listSearchReset()">重置</a>
        <!--導出用，請勿刪除-->
        <input id="page" name="page" type="hidden" value="1"/>
        <input id="rows" name="rows" type="hidden" value="30"/>
    </form>

</div>
<table id="dg"></table>
<div id="dlg"></div>

<script type="text/javascript">
    var dg;
    $(function () {
        dg = $('#dg').datagrid({
            method: "get",
            url: ctx + '/userSignatureImg/list',
            fit: true,
            // fitColumns: true,
            fitColumns: true,  //如需要底部出現滾動條，修改爲false
            border: false,
            idField: 'id',
            striped: true,
            cache: false,  //关闭AJAX相应的缓存
            pagination: true,
            rownumbers: true,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: true,
            columns: [[
                {field: 'id', title: '主鍵', hidden: true},
                {field: 'imgNo', title: '工號', sortable: true, width: 100},
                {field: 'imgName', title: '姓名', sortable: true, width: 100},
                {field: 'deptNo', title: '單位代碼', sortable: true, width: 100},
                {field: 'updateName', title: '變更人', sortable: true, width: 100},
                { field: 'updateDate', title: '變更時間',sortable:true,width:150},
                { field: 'ip', title: '變更IP',sortable:true,width:150},
                { field: 'accountNumber', title: '操作',sortable:true,width:150,
                    formatter: function(value,row,index){
                        return '<div style="font-size:12px;width:100px;text-align:center;padding:5px 10px;background:#2FA4E7;color:#ffffff;border-radius:5px;" ' +
                            ' onclick="window.parent.mainpage.mainTabs.addModule(\'签名维护页面\',ctx+\'/userSignatureImg/createTurn/' + row.id + '\',\'icon-hamburg-basket\')">設置簽名檔</div>';
                    }
                }
            ]],
            onLoadSuccess: function () {
                $(".easyui-tooltip").tooltip({
                    onShow: function () {
                        $(this).tooltip('tip').css({
                            borderColor: '#000'
                        });
                    }
                });
                //$(this).datagrid("fixRownumber");
            },
            rowStyler: rowStylerFun,
            enableHeaderClickMenu: true,
            enableHeaderContextMenu: true,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });


    });


    //创建查询对象并查询
    function cx() {
        var obj = $("#searchFrom").serializeObject();
        if(obj.applyno==""&&obj.applyname==""&&obj.deptno==""){
            $.messager.alert("查询条件不可为空");
            return;
        }
      //  dg.datagrid({'load': obj,  'url': ctx + '/userSignatureImg/list'});

        dg.datagrid('load', obj);
    };

</script>
</body>
</html>