<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>签名维护页面</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfadaccountprocess/${action}" method="post">
     <div style="width:90%;height:94%;margin: 0 auto;padding:3% 5%;">
        <div class="headTitle">簽名維護</div>


        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">主管信息</td>
                            <td width="10%" >
                               工號
                            </td>
                            <td width="10%" >
                                ${userSignatureImgDetail.empno}
                            </td>
                            <td width="8%">姓名</td>
                            <td width="10%">
                                ${userSignatureImgDetail.empname}
                            </td>
                        </tr>
                        <tr align="center">
                            <td style="min-height:150px;">簽名檔</td>
                            <td  colspan="4">
                                <div style="width:100%;text-align: left;padding:5px 0 0 5px;">
                                    <span class="sl-custom-file">
                                        <input type="button" value="点击上传文件" class="btn-file"/>
                                        <input name="imgUpLoad" type="file" accept="image/png, image/jpeg" class="ui-input-file"/>
                                    </span>
                                </div>
                                <div style="width:300px;height:150px;margin:auto;padding:40px;overflow: hidden;">
                                    <img id="signImg" src="${ctx}/userSignatureImg/showImgFtps/${userSignatureImgDetail.imgId}" style="width:100%;border: 1px solid #000000;<c:if test="${userSignatureImgDetail.imgId==null}">display:none;</c:if>"/>
                                </div>

                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="4" style="text-align: left;">
                                圖片要求：底色為純白色，長寬比例为2:1，图片格式為png或jpg
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

<input type="hidden" value="${userSignatureImgDetail.id}" name="id"/>
<input type="hidden" value="${userSignatureImgDetail.imgId}" name="imgId"/>
            <input type="hidden" value="${userSignatureImgDetail.empno}" name="fileName"/>
        </table>
         <div style="width:100%; text-align: center;padding-top:20px;">
            <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
               data-options="iconCls:'icon-add'"
               style="width: 100px;" onclick="returnBack();">返回</a>&nbsp;&nbsp;&nbsp;&nbsp;
            <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
               data-options="iconCls:'icon-ok'"
               style="width: 100px;" onclick="saveInfo();">提交</a>
         </div>
    </div>

</form>
</div>
<script src='${ctx}/static/js/information/usersignatureimg.js?random=<%= Math.random()%>'></script>
</body>
</html>