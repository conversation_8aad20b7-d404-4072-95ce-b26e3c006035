<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>${comments}</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }

        #projectnumber {
            background: #EBEBE4 !important;
        }

        .notice-type {
            padding-right: 10px;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfsystemprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfsystemprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfsystemprocessEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">系統需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfsystemprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfsystemprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfsystemprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfsystemprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfsystemprocessEntity.makerno}/${wfsystemprocessEntity.makername}</div>
        <div class="clear"></div>
        <table  class="formList">
            <tr>
                <td colspan="10" class="td_style1">申請人基本信息</td>
            </tr>
            <tr align="center">
                <td width="6%">申請人工號</td>
                <td width="6%" class="td_style2">${wfsystemprocessEntity.applyno}</td>
                <td width="5%">申請人姓名</td>
                <td width="5%" class="td_style2">${wfsystemprocessEntity.applyname}</td>
                <td width="6%">職責</td>
                <td width="5%" class="td_style2">${wfsystemprocessEntity.applypost}</td>
                <td width="5%">部門代碼</td>
                <td width="6%" class="td_style2">${wfsystemprocessEntity.applydeptno}</td>
                <td width="5%">廠區</td>
                <td width="5%" class="td_style2">${wfsystemprocessEntity.applyfactoryname}</td>
            </tr>
            <tr align="center">
                <td width="6%">部門名稱</td>
                <td colspan="3" class="td_style2">${wfsystemprocessEntity.applydeptname}</td>
                <td width="4%">聯絡電話</td>
                <td width="7%" class="td_style2">${wfsystemprocessEntity.applytel}</td>
                <td>聯繫郵箱</td>
                <td colspan="3" class="td_style2">${wfsystemprocessEntity.applyemail}</td>
            </tr>
            <tr align="center">
                <td width="5%">提出人工號</td>
                <td width="5%" class="td_style2">${wfsystemprocessEntity.dealno}</td>
                <td width="5%">提出人姓名</td>
                <td width="5%" class="td_style2">${wfsystemprocessEntity.dealname }</td>
                <td width="5%">管理職</td>
                <td width="5%" class="td_style2">${wfsystemprocessEntity.dealpost }</td>
                <td width="5%">申請日期</td>
                <td class="td_style2">
                    <fmt:formatDate pattern="yyyy-MM-dd" value="${wfsystemprocessEntity.applydate}"/>
                </td>
                <td>緊急程度</td>
                <td class="td_style2">
                    <c:if test="${wfsystemprocessEntity.urgencyLevel=='0'}">
                        特急
                    </c:if>
                    <c:if test="${wfsystemprocessEntity.urgencyLevel=='1'}">
                        緊急
                    </c:if>
                    <c:if test="${wfsystemprocessEntity.urgencyLevel=='2'}">
                        一般
                    </c:if>
                </td>
            </tr>
            <tr>
                <td colspan="10" class="td_style1">申請內容</td>
            </tr>
            <tr align="center">
                <td>系統類別</td>
                <td colspan="3" class="td_style2"
                    style="text-align: left;">${wfsystemprocessEntity.systemCategoryname}</td>
                <td>系統名稱</td>
                <td colspan="5" class="td_style2"
                    style="text-align: left;">${wfsystemprocessEntity.systemBelowname}</td>
            </tr>
            <tr align="center">
                <td>專案類型</td>
                <td colspan="9" class="td_style2" style="text-align: left;">
                    ${wfsystemprocessEntity.projectTypename }&nbsp;&nbsp;${wfsystemprocessEntity.projectTypeother }
                </td>
            </tr>
            <tr align="center">
                <td>需求描述<br/>(可附件)</td>
                <td colspan="9" class="td_style2" style="text-align: left;">
                    <textarea id="applydescription" class="easyui-validatebox"
                              oninput="return LessThan(this);"
                              onchange="return LessThan(this);"
                              onpropertychange="return LessThan(this);"
                              maxlength="500" disabled
                              data-options="multiline:true,required:true,validType:'length[1,500]'"
                              style="width:99%;height:120px;resize:none;" rows="5" cols="4">${wfsystemprocessEntity.applydescription}</textarea>
                </td>
            </tr>
            <tr align="center">
                <td>效益分析<br/>(需附件)</td>
                <td colspan="3" class="td_style2" style="text-align: left;">
                    用戶效益：${wfsystemprocessEntity.benefitAnalysis }&nbsp;元
                </td>
                <td colspan="6" class="td_style2" style="text-align: left;">
                    效益說明：${wfsystemprocessEntity.benefitStatement }
                </td>
            </tr>
            <c:if test="${count gt 0}">
            <tr>
                <td colspan="10" class="td_style1">資訊評估信息</td>
            </tr>
            <tr align="center">
                <td>需求性質</td>
                <td colspan="9" class="td_style2" style="text-align: left;">${wfsystemprocessEntity.demandNaturename }</td>
            </tr>
                <tr align="center">
                    <td>推廣度</td>
                    <td colspan="9" class="td_style2" style="text-align: left;">
                            ${wfsystemprocessEntity.promotionDegreename}&nbsp;${wfsystemprocessEntity.promotionDegreeother}
                    </td>
                </tr>
                <tr align="center">
                    <td>軟件</td>
                    <td colspan="3" class="td_style2" style="text-align: left;">
                        預計作業工期：
                        <c:choose>
                            <c:when test="${wfsystemprocessEntity.activityDuration!=''&& wfsystemprocessEntity.activityDuration!=null}">
                                ${wfsystemprocessEntity.activityDuration }
                            </c:when>
                            <c:otherwise>/</c:otherwise>
                        </c:choose> 天
                    </td>
                    <td>預計專案效益價值</td>
                    <td colspan="2" class="td_style2" style="text-align: left;">
                        <c:choose>
                            <c:when test="${wfsystemprocessEntity.projectBenefit!=''&& wfsystemprocessEntity.projectBenefit!=null}">
                                ${wfsystemprocessEntity.projectBenefit }
                            </c:when>
                            <c:otherwise>/</c:otherwise>
                        </c:choose>&nbsp;RMB
                    </td>
                    <td>預計運維效益價值</td>
                    <td colspan="2" class="td_style2" style="text-align: left;">
                        <c:choose>
                            <c:when test="${wfsystemprocessEntity.operationBenefit!=''&& wfsystemprocessEntity.operationBenefit!=null}">
                                ${wfsystemprocessEntity.operationBenefit }
                            </c:when>
                            <c:otherwise>/</c:otherwise>
                        </c:choose>&nbsp;MB/年
                    </td>
                </tr>
                <tr align="center">
                    <td>硬件資源</td>
                    <td colspan="3" class="td_style2" style="text-align: left;">
                        <c:if test="${wfsystemprocessEntity.hardwareResources.equals('Y')}">有</c:if>
                        <c:if test="${wfsystemprocessEntity.hardwareResources.equals('N')}">無</c:if>
                    </td>
                    <td>方案&費用</td>
                    <td colspan="5" class="td_style2" style="text-align: left;">
                            ${wfsystemprocessEntity.planCost }
                    </td>
                </tr>
                <tr align="center">
                    <td>對接系統</td>
                    <td colspan="3" class="td_style2" style="text-align: left;">
                        <c:choose>
                            <c:when test="${wfsystemprocessEntity.dockingSystemname!=''&& wfsystemprocessEntity.dockingSystemname!=null}">
                                ${wfsystemprocessEntity.dockingSystemname }
                            </c:when>
                            <c:otherwise>/</c:otherwise>
                        </c:choose>
                    </td>
                    <td>立項編號</td>
                    <td colspan="5" class="td_style2" style="text-align: left;">
                        <c:choose>
                            <c:when test="${wfsystemprocessEntity.projectnumber!=''&& wfsystemprocessEntity.projectnumber!=null}">
                                ${wfsystemprocessEntity.projectnumber }
                            </c:when>
                            <c:otherwise>/</c:otherwise>
                        </c:choose>
                    </td>
                </tr>
                <tr align="center">
                    <td>評估說明&nbsp;<br/>(可附件)</td>
                    <td colspan="9" class="td_style2" style="text-align: left;">
                    <textarea class="easyui-validatebox"
                              oninput="return LessThan(this);"
                              onchange="return LessThan(this);"
                              onpropertychange="return LessThan(this);"
                              maxlength="500" disabled
                              data-options="multiline:true,required:true,validType:'length[1,500]'"
                              style="width:99%;height:80px;resize:none;" rows="5" cols="4">${wfsystemprocessEntity.assessDescription}</textarea>
                </td>
            </tr>
            </c:if>
            <tr>
                <td colspan="10" class="td_style1">附件信息</td>
            </tr>
            <tr align="center">
                <td>附件</td>
                <td colspan="9" class="td_style2" style="text-align: left;">
                    <c:if test="${count gt 0}">
                    <div>
                        <c:forEach items="${file2}" varStatus="i" var="item">
                            <div id="${item.id}"
                                 style="line-height:30px;margin-left:5px;" class="float_L">
                                <div class="float_L">
                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                </div>
                            </div>
                        </c:forEach>
                    </div>
                    </c:if>
                    <div>
                        <c:forEach items="${file}" varStatus="i" var="item">
                            <div id="${item.id}"
                                 style="line-height:30px;margin-left:5px;" class="float_L">
                                <div class="float_L">
                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                </div>
                            </div>
                        </c:forEach>
                    </div>
                </td>
            </tr>
            <tr>
                <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                    <a href="javascript:void(0)"
                       onclick="showWfImag('${processId}','${comments}');">點擊查看簽核流程圖</a>
                </th>
            </tr>
            <tr>
                <td colspan="10" style="text-align:left;">
                    ${chargeNodeInfo}
                </td>
            </tr>

            <tr>
                <td colspan="10" style="text-align:left;">
                    <iframe id="qianheLogFrame" name="qianheLogFrame"
                            src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfsystemprocessEntity.serialno}"
                            width="100%"></iframe>
                </td>
            </tr>
            <tr class="no-print">
                <td colspan="10" style="text-align:center;padding-left:10px;">
                    <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                       data-options="iconCls:'icon-cancel'"
                       style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                   <c:if test="${wfsystemprocessEntity.workstatus!=null&&wfsystemprocessEntity.workstatus==3}">
                      <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                          style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                   </c:if>
                </td>
            </tr>


        </table>
	</div>
	</form>
  </div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/information/wfsystemprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>