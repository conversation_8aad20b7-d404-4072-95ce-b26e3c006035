<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>${comments}</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }

        #projectnumber {
            background: #EBEBE4 !important;
        }

        .notice-type {
            padding-right: 10px;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfsystemprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfsystemprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfsystemprocessEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">系統需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfsystemprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfsystemprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfsystemprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfsystemprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfsystemprocessEntity.makerno}/${wfsystemprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td colspan="10" class="td_style1">申請人基本信息</td>
            </tr>
            <tr align="center">
                <td width="6%">申請人工號</td>
                <td width="6%" class="td_style2">${wfsystemprocessEntity.applyno}</td>
                <td width="5%">申請人姓名</td>
                <td width="5%" class="td_style2">${wfsystemprocessEntity.applyname}</td>
                <td width="6%">職責</td>
                <td width="5%" class="td_style2">${wfsystemprocessEntity.applypost}</td>
                <td width="5%">部門代碼</td>
                <td width="6%" class="td_style2">${wfsystemprocessEntity.applydeptno}</td>
                <td width="5%">廠區</td>
                <td width="5%" class="td_style2">${wfsystemprocessEntity.applyfactoryname}</td>
            </tr>
            <tr align="center">
                <td width="6%">部門名稱</td>
                <td colspan="3" class="td_style2">${wfsystemprocessEntity.applydeptname}</td>
                <td width="4%">聯絡電話</td>
                <td width="7%" class="td_style2">${wfsystemprocessEntity.applytel}</td>
                <td>聯繫郵箱</td>
                <td colspan="3" class="td_style2">${wfsystemprocessEntity.applyemail}</td>
            </tr>
            <tr align="center">
                <td width="5%">提出人工號</td>
                <td width="5%" class="td_style2">${wfsystemprocessEntity.dealno}</td>
                <td width="5%">提出人姓名</td>
                <td width="5%" class="td_style2">${wfsystemprocessEntity.dealname }</td>
                <td width="5%">管理職</td>
                <td width="5%" class="td_style2">${wfsystemprocessEntity.dealpost }</td>
                <td width="5%">申請日期</td>
                <td class="td_style2">
                    <fmt:formatDate pattern="yyyy-MM-dd" value="${wfsystemprocessEntity.applydate}"/>
                </td>
                <td>緊急程度</td>
                <td class="td_style2">
                    <c:if test="${wfsystemprocessEntity.urgencyLevel=='0'}">
                        特急
                    </c:if>
                    <c:if test="${wfsystemprocessEntity.urgencyLevel=='1'}">
                        緊急
                    </c:if>
                    <c:if test="${wfsystemprocessEntity.urgencyLevel=='2'}">
                        一般
                    </c:if>
                </td>
            </tr>
            <tr>
                <td colspan="10" class="td_style1">申請內容</td>
            </tr>
            <tr align="center">
                <td>系統類別</td>
                <td colspan="3" class="td_style2"
                    style="text-align: left;">${wfsystemprocessEntity.systemCategoryname}</td>
                <td>系統名稱</td>
                <td colspan="5" class="td_style2"
                    style="text-align: left;">${wfsystemprocessEntity.systemBelowname}</td>
            </tr>
            <tr align="center">
                <td>專案類型</td>
                <td colspan="9" class="td_style2" style="text-align: left;">
                    ${wfsystemprocessEntity.projectTypename }&nbsp;&nbsp;${wfsystemprocessEntity.projectTypeother }
                </td>
            </tr>
            <tr align="center">
                <td>需求描述<br/>(可附件)</td>
                <td colspan="9" class="td_style2" style="text-align: left;">
                    <textarea id="applydescription" class="easyui-validatebox"
                              oninput="return LessThan(this);"
                              onchange="return LessThan(this);"
                              onpropertychange="return LessThan(this);"
                              maxlength="500" disabled
                              data-options="multiline:true,required:true,validType:'length[1,500]'"
                              style="width:99%;height:120px;resize:none;" rows="5" cols="4">${wfsystemprocessEntity.applydescription}</textarea>
                </td>
            </tr>
            <tr align="center">
                <td>效益分析<br/>(需附件)</td>
                <td colspan="3" class="td_style2" style="text-align: left;">
                    用戶效益：${wfsystemprocessEntity.benefitAnalysis }&nbsp;元
                </td>
                <td colspan="6" class="td_style2" style="text-align: left;">
                    效益說明：${wfsystemprocessEntity.benefitStatement }
                </td>
            </tr>
    <c:choose>
        <c:when test="${not empty nodeName && '規劃工程師審核評估' eq nodeName}">
            <tr>
                <td colspan="10" class="td_style1">資訊評估信息</td>
            </tr>
            <tr align="center">
                <td>需求性質&nbsp;<font color="red">*</font></td>
                <td colspan="9" class="td_style2" style="text-align: left;">
                    <div class="demandNatureDiv"></div>
                    <input id="demandNature" name="demandNature"
                           type="hidden" class="easyui-validatebox" data-options="width: 100"
                           value="${wfsystemprocessEntity.demandNature}"/>
                    <input id="demandNaturename" name="demandNaturename" type="hidden"
                           value="${wfsystemprocessEntity.demandNaturename }"/>
                </td>
            </tr>
            <tr align="center">
                <td>推廣度&nbsp;<font color="red">*</font></td>
                <td colspan="9" class="td_style2" style="text-align: left;">
                    <div class="promotionDegreeDiv"></div>
                    <input id="promotionDegree" name="promotionDegree"
                           type="hidden" class="easyui-validatebox" data-options="width: 100"
                           value="${wfsystemprocessEntity.promotionDegree}"/>
                    <input id="promotionDegreename" name="promotionDegreename" type="hidden" value="${wfsystemprocessEntity.promotionDegreename}"/>
                </td>
            </tr>
            <tr align="center">
                <td>軟件</td>
                <td colspan="3" class="td_style2" style="text-align: left;">
                    預計作業工期：<input id="activityDuration" name="activityDuration" onblur="valdIsNumber('activityDuration')"
                                    class="easyui-validatebox" data-options="width: 100"
                                    value="${wfsystemprocessEntity.activityDuration }"/>&nbsp;天
                </td>
                <td>預計專案效益價值</td>
                <td colspan="2" class="td_style2" style="text-align: left;">
                    <input id="projectBenefit" name="projectBenefit" onblur="valdIsNumber('projectBenefit')"
                                    class="easyui-validatebox" data-options="width: 100"
                                    value="${wfsystemprocessEntity.projectBenefit }"/> RMB
                </td>
                <td>預計運維效益價值</td>
                <td colspan="2" class="td_style2" style="text-align: left;">
                    <input id="operationBenefit" name="operationBenefit" onblur="valdIsNumber('operationBenefit')"
                                            class="easyui-validatebox" data-options="width: 100"
                                            value="${wfsystemprocessEntity.operationBenefit }"/> MB/年
                </td>
            </tr>
            <tr align="center">
                <td>硬件資源&nbsp;<font color="red">*</font></td>
                <td colspan="3" class="td_style2" style="text-align: left;">
                    <div class="hardwareResourcesDiv"></div>
                    <input id="hardwareResources" name="hardwareResources"
                           type="hidden" class="easyui-validatebox" data-options="width: 100"
                           value="${wfsystemprocessEntity.hardwareResources}"/>
                </td>
                <td>方案&費用&nbsp;<font color="red">*</font></td>
                <td colspan="5" class="td_style2" style="text-align: left;">
                    <input id="planCost" name="planCost"
                           class="easyui-validatebox" data-options="width: 400,required:true"
                           value="${wfsystemprocessEntity.planCost }"/>
                </td>
            </tr>
            <tr align="center">
                    <%--<td>立案確認</td>
                    <td colspan="3" class="td_style2" style="text-align: left;">
                            &lt;%&ndash;<input id="registerConfirm" name="registerConfirm"
                                   class="easyui-validatebox" data-options="width: 200"
                                   value="${wfsystemprocessEntity.registerConfirm }"/>&ndash;%&gt;
                        <label class="notice-type" style="padding-right: 10px;"><input type="radio" name="registerConfirm"
                                                                                       style="transform: translate(0, -3px);"
                                                                                       value="是"
                                                                                       <c:if test="${wfsystemprocessEntity.registerConfirm==null}">checked</c:if>
                                                                                           <c:if test="${wfsystemprocessEntity.registerConfirm=='是'}">checked</c:if>/>是</label>
                        <label class="notice-type" style="padding-right: 10px;"><input type="radio" name="registerConfirm"
                                                                                       style="transform: translate(0, -3px);"
                                                                                       value="否"
                                                                                       <c:if test="${wfsystemprocessEntity.registerConfirm=='否'}">checked</c:if>/>否</label>
                    </td>--%>
                <td>對接系統&nbsp;<font color="red">*</font></td>
                <td colspan="3" class="td_style2" style="text-align: left;">
                    <input id="dockingSystem" name="dockingSystem" class="easyui-combobox"
                           data-options="width:300,prompt:'請選擇對接系統,可多選',required:true,validType:'comboxValidate[\'dockingSystem\',\'请選擇對接系統\']',onSelect:function(){onChangeDockingSystem();}"
                           value="${wfsystemprocessEntity.dockingSystem}"/>
                    <input id="dockingSystemname" name="dockingSystemname" type="hidden"
                           value="${wfsystemprocessEntity.dockingSystemname}"/>
                </td>
                    <%--<td>需求編號</td>
                    <td colspan="5" class="td_style2" style="text-align: left;">
                        <input id="projectCode" name="projectCode" readonly
                               class="easyui-validatebox" data-options="width: 400"
                               value="${wfsystemprocessEntity.projectCode }"/>
                    </td>--%>
                <td>立項編號</td>
                <td colspan="5" class="td_style2" style="text-align: left;">
                    <input id="projectnumber" name="projectnumber" readonly
                           class="easyui-validatebox" data-options="width: 400"
                           value="${wfsystemprocessEntity.projectnumber }"/>
                </td>
            </tr>
            <tr align="center">
                <td>評估說明&nbsp;<font color="red">*</font><br/>(可附件)</td>
                <td colspan="9" class="td_style2" style="text-align: left;">
                    <textarea id="assessDescription"
                              name="assessDescription" class="easyui-validatebox"
                              oninput="return LessThan(this);"
                              onchange="return LessThan(this);"
                              onpropertychange="return LessThan(this);"
                              maxlength="500"
                              data-options="multiline:true,required:true,validType:'length[1,500]'"
                              style="width:99%;height:80px;resize:none;" rows="5" cols="4">${wfsystemprocessEntity.assessDescription}</textarea>
                    <span id="txtNum"></span>
                </td>
            </tr>
            <tr>
                <td colspan="10" class="td_style1">附件信息</td>
            </tr>
            <tr align="center">
                <td>附件</td>
                <td colspan="9" class="td_style2" style="text-align: left;">
                        <%--<span class="sl-custom-file">
                            <input type="button" value="点击上传文件" class="btn-file"/><font style="color:red;">（目前APP端僅支持PDF文檔格式查閱）</font>
                            <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
                        </span>--%>
                    <span class="sl-custom-file">
                        <div style="display: inline-block; position: relative;">
                            <input type="button" value="点击上传文件" class="btn-file" style="z-index: 1;"/>
                            <input id="attachidsUpload" name="attachidsUpload" type="file"
                                   onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"
                                   style="position: absolute; left: 0; top: 0; width: 100%; height: 100%; opacity: 0; z-index: 2; cursor: pointer;"/>
                        </div>
                    </span>
                    <font style="color:red; margin-left: 5px;">（目前APP端仅支持PDF文档格式查阅）</font>
                    <input type="hidden" id="attachids" name="attachids2" value="${wfsystemprocessEntity.attachids2 }"/>
                    <div id="dowloadUrl">
                        <c:forEach items="${file2}" varStatus="i" var="item">
                            <div id="${item.id}"
                                 style="line-height:30px;margin-left:5px;" class="float_L">
                                <div class="float_L">
                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                </div>
                                <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                            </div>
                        </c:forEach>
                    </div>
                    <%--<input type="hidden"  name="attachids" value="${wfsystemprocessEntity.attachids }"/>--%>
                    <div id="dowloadUrl2">
                        <c:forEach items="${file}" varStatus="i" var="item">
                            <div id="${item.id}"
                                 style="line-height:30px;margin-left:5px;" class="float_L">
                                <div class="float_L">
                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                </div>
                            </div>
                        </c:forEach>
                    </div>
                </td>
            </tr>
        </c:when>
        <c:when test="${nodeOrder ge 9}">
            <tr>
                <td colspan="10" class="td_style1">資訊評估信息</td>
            </tr>
            <tr align="center">
                <td>需求性質</td>
                <td colspan="9" class="td_style2"
                    style="text-align: left;">${wfsystemprocessEntity.demandNaturename }</td>
            </tr>
            <tr align="center">
                <td>推廣度</td>
                <td colspan="9" class="td_style2" style="text-align: left;">
                        ${wfsystemprocessEntity.promotionDegreename}&nbsp;${wfsystemprocessEntity.promotionDegreeother}
                </td>
            </tr>
            <tr align="center">
                <td>軟件</td>
                <td colspan="3" class="td_style2" style="text-align: left;">
                    預計作業工期：
                    <c:choose>
                        <c:when test="${wfsystemprocessEntity.activityDuration!=''&& wfsystemprocessEntity.activityDuration!=null}">
                            ${wfsystemprocessEntity.activityDuration }
                        </c:when>
                        <c:otherwise>/</c:otherwise>
                    </c:choose> 天
                </td>
                <td>預計專案效益價值</td>
                <td colspan="2" class="td_style2" style="text-align: left;">
                    <c:choose>
                        <c:when test="${wfsystemprocessEntity.projectBenefit!=''&& wfsystemprocessEntity.projectBenefit!=null}">
                            ${wfsystemprocessEntity.projectBenefit }
                        </c:when>
                        <c:otherwise>/</c:otherwise>
                    </c:choose>&nbsp;RMB
                </td>
                <td>預計運維效益價值</td>
                <td colspan="2" class="td_style2" style="text-align: left;">
                    <c:choose>
                        <c:when test="${wfsystemprocessEntity.operationBenefit!=''&& wfsystemprocessEntity.operationBenefit!=null}">
                            ${wfsystemprocessEntity.operationBenefit }
                        </c:when>
                        <c:otherwise>/</c:otherwise>
                    </c:choose>&nbsp;MB/年
                </td>
            </tr>
            <tr align="center">
                <td>硬件資源</td>
                <td colspan="3" class="td_style2" style="text-align: left;">
                    <c:if test="${wfsystemprocessEntity.hardwareResources.equals('Y')}">有</c:if>
                    <c:if test="${wfsystemprocessEntity.hardwareResources.equals('N')}">無</c:if>
                </td>
                <td>方案&費用</td>
                <td colspan="5" class="td_style2" style="text-align: left;">
                        ${wfsystemprocessEntity.planCost }
                </td>
            </tr>
            <tr align="center">
                <td>對接系統</td>
                <td colspan="3" class="td_style2" style="text-align: left;">
                    <c:choose>
                        <c:when test="${wfsystemprocessEntity.dockingSystemname!=''&& wfsystemprocessEntity.dockingSystemname!=null}">
                            ${wfsystemprocessEntity.dockingSystemname }
                        </c:when>
                        <c:otherwise>/</c:otherwise>
                    </c:choose>
                </td>
                <td>立項編號</td>
                <td colspan="5" class="td_style2" style="text-align: left;">
                    <c:choose>
                        <c:when test="${wfsystemprocessEntity.projectnumber!=''&& wfsystemprocessEntity.projectnumber!=null}">
                            ${wfsystemprocessEntity.projectnumber}
                        </c:when>
                        <c:otherwise>/</c:otherwise>
                    </c:choose>
                </td>
            </tr>
            <tr align="center">
                <td>評估說明&nbsp;<br/>(可附件)</td>
                <td colspan="9" class="td_style2" style="text-align: left;">
                    <textarea class="easyui-validatebox"
                              oninput="return LessThan(this);"
                              onchange="return LessThan(this);"
                              onpropertychange="return LessThan(this);"
                              maxlength="500" disabled
                              data-options="multiline:true,required:true,validType:'length[1,500]'"
                              style="width:99%;height:80px;resize:none;" rows="5" cols="4">${wfsystemprocessEntity.assessDescription}</textarea>
                </td>
            </tr>
            <tr>
                <td colspan="10" class="td_style1">附件信息</td>
            </tr>
            <tr align="center">
                <td>附件</td>
                <td colspan="9" class="td_style2" style="text-align: left;">
                    <div>
                        <c:forEach items="${file2}" varStatus="i" var="item">
                            <div id="${item.id}"
                                 style="line-height:30px;margin-left:5px;" class="float_L">
                                <div class="float_L">
                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                </div>
                            </div>
                        </c:forEach>
                    </div>
                    <div>
                        <c:forEach items="${file}" varStatus="i" var="item">
                            <div id="${item.id}"
                                 style="line-height:30px;margin-left:5px;" class="float_L">
                                <div class="float_L">
                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                </div>
                            </div>
                        </c:forEach>
                    </div>
                </td>
            </tr>
        </c:when>
        <c:otherwise>
            <tr>
                <td colspan="10" class="td_style1">附件信息</td>
            </tr>
            <tr align="center">
                <td>附件</td>
                <td colspan="9" class="td_style2" style="text-align: left;">
                    <div>
                        <c:forEach items="${file}" varStatus="i" var="item">
                            <div id="${item.id}"
                                 style="line-height:30px;margin-left:5px;" class="float_L">
                                <div class="float_L">
                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                </div>
                            </div>
                        </c:forEach>
                    </div>
                </td>
            </tr>
        </c:otherwise>
    </c:choose>
            <tr align="center">
                <td width="10%">批註</td>
                <td colspan="9" class="td_style2" style="text-align: left;">
						<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                </td>
            </tr>
            <tr align="center">
                <td colspan="10" style="text-align:center;margin-top:10px">
                    <c:choose>
                        <c:when test="${not empty nodeName && '規劃工程師審核評估' eq nodeName}">
                            <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" perCall="audiPrValid"
                                        serialNo="${wfsystemprocessEntity.serialno}"></fox:action>
                        </c:when>
                        <c:otherwise>
                            <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" serialNo="${wfsystemprocessEntity.serialno}"></fox:action>
                        </c:otherwise>
                    </c:choose>
                </td>
            </tr>
            <tr>
                <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                    <a href="javascript:void(0)"
                       onclick="showWfImag('${processId}','${comments}');">點擊查看簽核流程圖</a>
                </th>
            </tr>
            <tr>
                <td colspan="10" style="text-align:left;">
                    ${chargeNodeInfo}
                </td>
            </tr>

            <tr>
                <td colspan="10" style="text-align:left;">
                    <iframe id="qianheLogFrame" name="qianheLogFrame"
                            src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfsystemprocessEntity.serialno}"
                            width="100%"></iframe>
                </td>
            </tr>
        </table>
	</div>
	</form>
<input type="hidden" id="currentNodeOrder" value="${nodeOrder}" />
<input id="disOrEnabled" type="hidden" value="disabled"/>
<input type="hidden" id="promotionDegreeotherAudit"   value="${wfsystemprocessEntity.promotionDegreeother}" />
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
  <div id="dlg"></div>
<script src='${ctx}/static/js/information/wfsystemprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>