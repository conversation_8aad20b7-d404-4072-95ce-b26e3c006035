<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>${comments}</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
<style type="text/css">
    .td_style2 {
        text-align: left;
    }
</style>
</head>
<body>
<form id="mainform" action="${ctx}/wfsystemprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfsystemprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfsystemprocessEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfsystemprocessEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfsystemprocessEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfsystemprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${wfsystemprocessEntity.makerfactoryid }"/>
    <input id="projectCode" name="projectCode" type="hidden" value="${wfsystemprocessEntity.projectCode }"/>
    <input id="projectnumber" name="projectnumber" type="hidden" value="${wfsystemprocessEntity.projectnumber}"/>
    <div class="commonW">
        <div class="headTitle">系統需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfsystemprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfsystemprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfsystemprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfsystemprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfsystemprocessEntity.makerno}">
			  <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfsystemprocessEntity.makerno}">
                <div class="position_R margin_R">填單人：${wfsystemprocessEntity.makerno}/${wfsystemprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td colspan="10" class="td_style1">申請人基本信息</td>
            </tr>
            <tr align="center">
                <td width="6%">申請人工號&nbsp;<font color="red">*</font></td>
                <td width="6%" class="td_style1">
                    <input id="applyno" name="applyno" class="easyui-validatebox"
                           data-options="width: 80,required:true"
                           value="${wfsystemprocessEntity.applyno}" onblur="queryUserInfo('apply');"/>
                </td>
                <td width="5%">申請人姓名</td>
                <td width="5%" class="td_style1">
                    <input id="applyname" name="applyname"
                           class="easyui-validatebox inputCss"
                           data-options="width:80" readonly value="${wfsystemprocessEntity.applyname }"/>
                </td>
                <td width="5%">職責</td>
                <td width="5%" class="td_style1">
                    <input id="applypost" name="applypost" class="easyui-validatebox"
                           style="width:90px;position:relative;"
                           value="${wfsystemprocessEntity.applypost }"/>
                </td>
                <td width="5%">部門代碼</td>
                <td width="5%" class="td_style1">
                    <input id="applydeptno" name="applydeptno"
                           class="easyui-validatebox inputCss" data-options="width: 90"
                           readonly value="${wfsystemprocessEntity.applydeptno }"/>
                </td>
                <td width="5%">廠區</td>
                <td width="5%" class="td_style1">
                    <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                           panelHeight="auto" value="${wfsystemprocessEntity.applyfactoryid }"
                           data-options="width: 120,required:true,validType:'comboxValidate[\'applyfactoryid\',\'请選擇申請人廠區\']'"/>
                    <input id="applyfactoryname" name="applyfactoryname" type="hidden" value="${wfsystemprocessEntity.applyfactoryname }"/>
                    <input id="applynofactoryid" name="applynofactoryid" type="hidden" value="${wfsystemprocessEntity.applynofactoryid }"/>
                </td>
            </tr>
            <tr align="center">
                <td width="6%">部門名稱&nbsp;<font color="red">*</font></td>
                <td colspan="3" class="td_style1">
                    <input id="applydeptname" name="applydeptname" class="easyui-validatebox" data-options="width: 380,required:true"
                           value="${wfsystemprocessEntity.applydeptname }"/>
                </td>
                <td width="4%">聯絡電話&nbsp;<font color="red">*</font></td>
                <td width="7%" class="td_style1">
                    <input id="applytel" name="applytel" class="easyui-validatebox"
                           style="width:90px;"
                           value="${wfsystemprocessEntity.applytel }" data-options="required:true"/>
                </td>
                <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                <td colspan="3" class="td_style1">
                    <input id="applyemail" name="applyemail" class="easyui-validatebox"
                           value="${wfsystemprocessEntity.applyemail }" style="width:300px;"
                           data-options="required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                </td>
            </tr>
            <tr align="center">
                <td width="5%">提出人工號&nbsp;<font color="red">*</font></td>
                <td width="5%" class="td_style1">
                    <input id="dealno" name="dealno" class="easyui-validatebox"
                           data-options="width: 80,required:true"
                           value="${wfsystemprocessEntity.dealno}" onblur="queryUserInfo('deal');"/>
                </td>
                <td width="5%">提出人姓名</td>
                <td width="5%" class="td_style1">
                    <input id="dealname" name="dealname"
                           class="easyui-validatebox inputCss"
                           data-options="width:80" readonly value="${wfsystemprocessEntity.dealname }"/>
                </td>
                <td width="5%">管理職</td>
                <td width="5%" class="td_style1">
                    <input id="dealpost" name="dealpost" class="easyui-validatebox"
                           style="width:90px;"
                           value="${wfsystemprocessEntity.dealpost }"/>
                </td>
                <td width="5%">申請日期</td>
                <td class="td_style1">
                    <input id="applydate" name="applydate" class="easyui-validatebox"
                           data-options="width:100,required:true" readonly
                           value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfsystemprocessEntity.applydate}"/>"/>
                </td>
                <td>緊急程度</td>
                <td>
                    <label><input type="radio" name="urgencyLevelRadio"
                                  <c:if test="${wfsystemprocessEntity.urgencyLevel=='0'}">checked</c:if> value="0"/>特急</label>
                    <label><input type="radio" name="urgencyLevelRadio"
                                  <c:if test="${wfsystemprocessEntity.urgencyLevel=='1'}">checked</c:if> value="1"/>緊急</label>
                    <label><input type="radio" name="urgencyLevelRadio"
                                  <c:if test="${wfsystemprocessEntity.urgencyLevel=='2'}">checked</c:if> value="2"/>一般</label>
                    <input type="hidden" name="urgencyLevel" value="${wfsystemprocessEntity.urgencyLevel}"/>
                </td>
            </tr>
            <tr>
                <td colspan="10" class="td_style1">申請內容</td>
            </tr>
            <tr align="center">
                <td>系統類別&nbsp;<font color="red">*</font></td>
                <td colspan="3" class="td_style2" style="text-align: left;">
                    <input id="systemCategory" name="systemCategory" class="easyui-combobox"
                           panelHeight="auto" value="${wfsystemprocessEntity.systemCategory }"
                           data-options="width: 200,required:true,validType:'comboxValidate[\'systemCategory\',\'请選擇系統類別\']'"/>
                    <input id="systemCategoryname" name="systemCategoryname" type="hidden"
                           value="${wfsystemprocessEntity.systemCategoryname}"/>
                </td>
                <td>系統名稱&nbsp;<font color="red">*</font></td>
                <td colspan="5" class="td_style2" style="text-align: left;">
                    <input id="systemBelow" name="systemBelow" class="easyui-combobox"
                           panelHeight="auto" value="${wfsystemprocessEntity.systemBelow}"
                           data-options="width: 250,required:true,validType:'comboBoxEditvalid[\'systemBelow\',\'请選擇系統類別\']',onSelect:function(){checkSystemBelow();},"/>
                    <input id="systemBelowname" name="systemBelowname" type="hidden"
                           value="${wfsystemprocessEntity.systemBelowname}"/>
                </td>
            </tr>
            <tr align="center">
                <td>專案類型&nbsp;<font color="red">*</font></td>
                <td colspan="9" class="td_style2" style="text-align: left;">
                    <div class="projectTypeDiv"></div>
                    <input id="projectType" name="projectType"
                           type="hidden" class="easyui-validatebox" data-options="width: 100"
                           value="${wfsystemprocessEntity.projectType}"/>
                    <input id="projectTypename" name="projectTypename" type="hidden"
                           value="${wfsystemprocessEntity.projectTypename }"/>
                </td>
            </tr>
            <tr align="center">
                <td>需求描述&nbsp;<font color="red">*</font><br/>(可附件)</td>
                <td colspan="9" class="td_style2" style="text-align: left;">
                    <textarea id="applydescription"
                              name="applydescription" class="easyui-validatebox"
                              oninput="return LessThan(this);"
                              onchange="return LessThan(this);"
                              onpropertychange="return LessThan(this);"
                              maxlength="500"
                              data-options="multiline:true,required:true,validType:'length[1,500]'"
                              style="width:99%;height:120px;resize:none;" rows="5" cols="4">${wfsystemprocessEntity.applydescription}</textarea>
                    <span id="txtNum"></span>
                </td>
            </tr>
            <tr align="center">
                <td>效益分析&nbsp;<font color="red">*</font><br/>(需附件)</td>
                <td colspan="3" class="td_style2" style="text-align: left;">
                    用戶效益：<input id="benefitAnalysis" name="benefitAnalysis" onblur="valdIsNumber('benefitAnalysis')"
                                    class="easyui-validatebox" data-options="width: 200,required:true"
                                    value="${wfsystemprocessEntity.benefitAnalysis }"/>&nbsp;元
                </td>
                <td colspan="6" class="td_style2" style="text-align: left;">
                    效益說明：<input id="benefitStatement" name="benefitStatement"
                                    class="easyui-validatebox" data-options="width: 400,required:true"
                                    value="${wfsystemprocessEntity.benefitStatement }"/>
                </td>
            </tr>
            <tr>
                <td colspan="10" class="td_style1">附件信息</td>
            </tr>
            <tr align="center">
                <td>附件&nbsp;</td>
                <td colspan="9" class="td_style2" style="text-align: left;">
                    <span class="sl-custom-file">
                        <%--<input type="button" value="点击上传文件" class="btn-file"/>
                        <font style="color:red;">（目前APP端僅支持PDF文檔格式查閱）</font>
                        <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>--%>
                        <div style="display: inline-block; position: relative;">
                            <input type="button" value="点击上传文件" class="btn-file" style="z-index: 1;"/>
                            <input id="attachidsUpload" name="attachidsUpload" type="file"
                                   onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"
                                   style="position: absolute; left: 0; top: 0; width: 100%; height: 100%; opacity: 0; z-index: 2; cursor: pointer;"/>
                        </div>
                    </span>
                    <font style="color:red; margin-left: 5px;">（目前APP端仅支持PDF文档格式查阅）</font>
                    <c:choose>
                        <c:when test="${file.size()>0}">
                            <input type="hidden" id="attachids" name="attachids"
                                   value="${wfsystemprocessEntity.attachids }"/>
                        </c:when>
                        <c:otherwise>
                            <input type="hidden" id="attachids" name="attachids" value=""/>
                        </c:otherwise>
                    </c:choose>
                    <div id="dowloadUrl">
                        <c:forEach items="${file}" varStatus="i" var="item">
                            <div id="${item.id}"
                                 style="line-height:30px;margin-left:5px;" class="float_L">
                                <div class="float_L">
                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                </div>
                                <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                            </div>
                        </c:forEach>
                    </div>
                </td>
            </tr>
            <tr align="center" style="height: 100%" >
                <td>備註</td>
                <td colspan="9" class="td_style2">
                    <input id="applyMemo" type="hidden" value="${applyMemo}"/>
                    <textarea id="applyTextareaMemo" class="easyui-validatebox"
                              disabled readonly
                              style="width:99%;resize:none;background-color: #F2F5F7;border: 0px;outline: none;">${applyMemo}</textarea>
                    &nbsp;4.<a href="${ctx}/ossAdmin/download/0871f0273bc84f8fac321bda21a95cad">效益評估表模板</a>&nbsp;&nbsp;<a href="${ctx}/ossAdmin/download/0d4f1607603c4cf094f8393a46d28a87">數據流量評估表模板</a>
                </td>
            </tr>
            <tr>
                <td colspan="10">
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_xitongxuqiushenqing_v1','${comments}','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zxgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zxgchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole9(64,'zxgchargeTable','zxgchargeno','zxgchargename',$('#applyfactoryid').combobox('getValue'),$('#systemBelow').combobox('getValue'),'wfsystemprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxgchargeno" name="zxgchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxgchargeno']}"
                                                               readonly value="${wfsystemprocessEntity.zxgchargeno }"/><c:if
                                                            test="${requiredMap['zxgchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxgchargename" name="zxgchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxgchargeno']}"
                                                                value="${wfsystemprocessEntity.zxgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wfsystemprocessEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="kchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfsystemprocessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                                <%--<td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#applynofactoryid').val())"></div>
                                                                </td>--%>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('bchargeTable',$('#applydeptno').val(),'bchargeno','bchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfsystemprocessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="bchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfsystemprocessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#applydeptno').val(),'cchargeno','cchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfsystemprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfsystemprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hqchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['hqchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <a href="javascript:addHq2('hqcharge');">添加一位</a>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hqchargeno" name="hqchargeno"
                                                               onblur="gethqUserNameByEmpno(this,'hqcharge');"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hqchargeno']}"
                                                               onblur="getUserNameByEmpno(this,'hqcharge');"
                                                               value="${wfsystemprocessEntity.hqchargeno }"/><c:if
                                                            test="${requiredMap['hqchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hqchargename" name="hqchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hqchargeno']}"
                                                                value="${wfsystemprocessEntity.hqchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfsystemprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="zchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfsystemprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="lxckjdchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['lxckjdchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(668,'lxckjdchargeTable','lxckjdchargeno','lxckjdchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="lxckjdchargeno" name="lxckjdchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['lxckjdchargeno']}"
                                                               readonly
                                                               value="${wfsystemprocessEntity.lxckjdchargeno }"/><c:if
                                                            test="${requiredMap['lxckjdchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="lxckjdchargename" name="lxckjdchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['lxckjdchargeno']}"
                                                                value="${wfsystemprocessEntity.lxckjdchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="'ghpgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">${requiredMap['ghpgchargeno_name']}
                                                                </td>
                                                                <td style="border: none;"></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ghpgchargeno" name="ghpgchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ghpgchargeno']}"
                                                               readonly
                                                               value="${wfsystemprocessEntity.ghpgchargeno }"/>
                                                        <c:if test="${requiredMap['ghpgchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        /<input id="ghpgchargename" name="ghpgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ghpgchargeno']}"
                                                                value="${wfsystemprocessEntity.ghpgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="lxckshchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['lxckshchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(669,'lxckshchargeTable','lxckshchargeno','lxckshchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="lxckshchargeno" name="lxckshchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['lxckshchargeno']}"
                                                               readonly
                                                               value="${wfsystemprocessEntity.lxckshchargeno }"/><c:if
                                                            test="${requiredMap['lxckshchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="lxckshchargename" name="lxckshchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['lxckshchargeno']}"
                                                                value="${wfsystemprocessEntity.lxckshchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zxkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zxkchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole9(65,'zxkchargeTable','zxkchargeno','zxkchargename',$('#applyfactoryid').combobox('getValue'),$('#systemBelow').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxkchargeno" name="zxkchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxkchargeno']}"
                                                               readonly value="${wfsystemprocessEntity.zxkchargeno }"/><c:if
                                                            test="${requiredMap['zxkchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxkchargename" name="zxkchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxkchargeno']}"
                                                                value="${wfsystemprocessEntity.zxkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zxbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zxbchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole9(66,'zxbchargeTable','zxbchargeno','zxbchargename',$('#applyfactoryid').combobox('getValue'),$('#systemBelow').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxbchargeno" name="zxbchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxbchargeno']}"
                                                               readonly value="${wfsystemprocessEntity.zxbchargeno }"/><c:if
                                                            test="${requiredMap['zxbchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxbchargename" name="zxbchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxbchargeno']}"
                                                                value="${wfsystemprocessEntity.zxbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zxzchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zxzchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(261,'zxzchargeTable','zxzchargeno','zxzchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxzchargeno" name="zxzchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxzchargeno']}"
                                                               readonly value="${wfsystemprocessEntity.zxzchargeno }"/><c:if
                                                            test="${requiredMap['zxzchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxzchargename" name="zxzchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxzchargeno']}"
                                                                value="${wfsystemprocessEntity.zxzchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zxcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zxcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(262,'zxcchargeTable','zxcchargeno','zxcchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxcchargeno" name="zxcchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxcchargeno']}"
                                                               readonly
                                                               value="${wfsystemprocessEntity.zxcchargeno }"/><c:if
                                                            test="${requiredMap['zxcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxcchargename" name="zxcchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxcchargeno']}"
                                                                value="${wfsystemprocessEntity.zxcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="kfjdchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['kfjdchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <a href="javascript:addHq2('kfjdcharge');">添加一位</a>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kfjdchargeno" name="kfjdchargeno"
                                                               onblur="gethqUserNameByEmpno(this,'kfjdcharge');"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kfjdchargeno']}"
                                                               onblur="getUserNameByEmpno(this,'kfjdcharge');"
                                                               value="${wfsystemprocessEntity.kfjdchargeno }"/><c:if
                                                            test="${requiredMap['kfjdchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kfjdchargename" name="kfjdchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kfjdchargeno']}"
                                                                value="${wfsystemprocessEntity.kfjdchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="kfshchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['kfshchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <a href="javascript:addHq2('kfshcharge');">添加一位</a>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kfshchargeno" name="kfshchargeno"
                                                               onblur="gethqUserNameByEmpno(this,'kfshcharge');"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kfshchargeno']}"
                                                               onblur="getUserNameByEmpno(this,'kfshcharge');"
                                                               value="${wfsystemprocessEntity.kfshchargeno }"/><c:if
                                                            test="${requiredMap['kfshchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kfshchargename" name="kfshchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kfshchargeno']}"
                                                                value="${wfsystemprocessEntity.kfshchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="kfhzchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['kfhzchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <a href="javascript:addHq2('kfhzcharge');">添加一位</a>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kfhzchargeno" name="kfhzchargeno"
                                                               onblur="gethqUserNameByEmpno(this,'kfhzcharge');"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kfhzchargeno']}"
                                                               onblur="getUserNameByEmpno(this,'kfhzcharge');"
                                                               value="${wfsystemprocessEntity.kfhzchargeno }"/><c:if
                                                            test="${requiredMap['kfhzchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kfhzchargename" name="kfhzchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kfhzchargeno']}"
                                                                value="${wfsystemprocessEntity.kfhzchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfsystemprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="canelTask('${wfsystemprocessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	<input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <input type="hidden" id="projectTypeotherAudit"   value="${wfsystemprocessEntity.projectTypeother}" />
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
    <div id="dlg"></div>
	</form>
  </div>
<script src='${ctx}/static/js/information/wfsystemprocess.js?random=<%= Math.random()%>'></script>
<script type="text/javascript">
    applyMemoHeight();
    if ("${wfsystemprocessEntity.hqchargeno}" != "") {
        var nostr = "${wfsystemprocessEntity.hqchargeno}";
        var namestr = "${wfsystemprocessEntity.hqchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hqchargeTable tr:eq(" + (i + 2) + ")").find("#hqchargeno").val(notr[i]);
            $("#hqchargeTable tr:eq(" + (i + 2) + ")").find("#hqchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hqchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hqchargeno' name='hqchargeno' style='width: 80px' value='' onblur=\"gethqUserNameByEmpno(this,'hqcharge');\"/>/<input id='hqchargename' name='hqchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wfsystemprocessEntity.kfjdchargeno}" != "") {
        var nostr = "${wfsystemprocessEntity.kfjdchargeno}";
        var namestr = "${wfsystemprocessEntity.kfjdchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#kfjdchargeTable tr:eq(" + (i + 2) + ")").find("#kfjdchargeno").val(notr[i]);
            $("#kfjdchargeTable tr:eq(" + (i + 2) + ")").find("#kfjdchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#kfjdchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='kfjdchargeno' name='kfjdchargeno' style='width: 80px' value='' onblur=\"gethqUserNameByEmpno(this,'kfjdcharge');\"/>/<input id='kfjdchargename' name='kfjdchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wfsystemprocessEntity.kfshchargeno}" != "") {
        var nostr = "${wfsystemprocessEntity.kfshchargeno}";
        var namestr = "${wfsystemprocessEntity.kfshchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#kfshchargeTable tr:eq(" + (i + 2) + ")").find("#kfshchargeno").val(notr[i]);
            $("#kfshchargeTable tr:eq(" + (i + 2) + ")").find("#kfshchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#kfshchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='kfshchargeno' name='kfshchargeno' style='width: 80px' value='' onblur=\"gethqUserNameByEmpno(this,'kfshcharge');\"/>/<input id='kfshchargename' name='kfshchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wfsystemprocessEntity.kfhzchargeno}" != "") {
        var nostr = "${wfsystemprocessEntity.kfhzchargeno}";
        var namestr = "${wfsystemprocessEntity.kfhzchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#kfhzchargeTable tr:eq(" + (i + 2) + ")").find("#kfhzchargeno").val(notr[i]);
            $("#kfhzchargeTable tr:eq(" + (i + 2) + ")").find("#kfhzchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#kfhzchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='kfhzchargeno' name='kfhzchargeno' style='width: 80px' value='' onblur=\"gethqUserNameByEmpno(this,'kfhzcharge');\"/>/<input id='kfhzchargename' name='kfhzchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
</script>
</body>
</html>