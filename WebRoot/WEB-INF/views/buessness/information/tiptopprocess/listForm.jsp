<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>${comments}</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/tiptopprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tiptopProcessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${tiptopProcessEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${tiptopProcessEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${tiptopProcessEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${tiptopProcessEntity.makerdeptno }"/>
    <input id="applynofactoryid" name="applynofactoryid" type="hidden"
           value="${tiptopProcessEntity.applynofactoryid }"/>
    <div class="commonW">
        <div class="headTitle">Tiptop賬號申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${tiptopProcessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${tiptopProcessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${tiptopProcessEntity.createDate==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${tiptopProcessEntity.createDate}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty tiptopProcessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty tiptopProcessEntity.makerno}">
            <div class="position_R margin_R">填單人：${tiptopProcessEntity.makerno}/${tiptopProcessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="7%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="<c:if test="${tiptopProcessEntity.applyno!=null&&tiptopProcessEntity.applyno!=''}">${tiptopProcessEntity.applyno}</c:if><c:if test="${tiptopProcessEntity.applyno==null||tiptopProcessEntity.applyno==''}">${user.loginName}</c:if>"
                                       onblur="queryUserInfo('apply');"/>
                            </td>
                            <td width="5%">申請人</td>
                            <td width="5%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${tiptopProcessEntity.applyname }"/>
                            </td>
                            <td width="5%">單位代碼</td>
                            <td width="5%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${tiptopProcessEntity.applydeptno }"/>
                            </td>
                            <td width="5%">聯繫分機&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="applyphone" name="applyphone" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${tiptopProcessEntity.applyphone }"
                                       data-options="required:true,prompt:'579+66666',validType:'tel[\'applytel\',\'分機格式不正確\']'"/>
                            </td>
                            <td width="5%">廠區</td>
                            <td width="5%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${tiptopProcessEntity.applyfactoryid }"
                                       data-options="width: 120"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox"
                                       data-options="width: 550,required:true"
                                       value="${tiptopProcessEntity.applydeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       value="${tiptopProcessEntity.applyemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td>申請內容&nbsp;<font color="red">*</font></td>
                            <td class="td_style2" colspan="9">
                                <div class="applyContentDiv" align="left"></div>
                                <input id="applyContentValue" name="applyContentValue"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${tiptopProcessEntity.applyContent }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>Tiptop主機地址&nbsp;<font color="red">*</font></td>
                            <td class="td_style1" colspan="3">
                                <input id="computerAddress" name="computerAddress" class="easyui-combobox"
                                       data-options="onSelect:function(){resetAllFactroyCode();},width: 250,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/tiptop_computer_address',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; },validType:'comboxValidate[\'computerAddress\',\'请選擇Tiptop主機地址\']'"
                                       value="${tiptopProcessEntity.computerAddress }"/>
                            </td>
                            <td>初始密碼</td>
                            <td class="td_style1" colspan="5">
                                <input id="initPwd" name="initPwd"
                                       class="easyui-validatebox" data-options="width: 250,disabled:true"
                                       value="${tiptopProcessEntity.initPwd }"/>
                            </td>
                        </tr>
                    </table>
                    <table>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="softItemTable" width="100%">
                                        <tr align="center">
                                            <td width="50px;">項次</td>
                                            <td width="100px;">帳號&nbsp;<font color="red">*</font></td>
                                            <td width="100px;">姓名&nbsp;<font color="red">*</font></td>
                                            <td width="100px;">部門代碼&nbsp;<font color="red">*</font></td>
                                            <td width="200px;" id="qxlb">權限類別</td>
                                            <td width="200px;">工廠編碼&nbsp;<font color="red">*</font></td>
                                            <td width="50px;">&nbsp;操作&nbsp;</td>
                                        </tr>
                                        <tbody id="info_Body">
                                        <c:if test="${tiptopProcessEntity.itemsEntity!=null&&tiptopProcessEntity.itemsEntity.size()>0}">
                                            <c:forEach items="${tiptopProcessEntity.itemsEntity}" var="itemsEntity"
                                                       varStatus="status">
                                                <tr align="center" id="itemsEntity${status.index}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <input id="accountNumber${status.index}"
                                                               onblur="getUserNameByEmpno(this,${status.index});"
                                                               name="itemsEntity[${status.index}].accountNumber"
                                                               class="easyui-validatebox" style="width:80px;"
                                                               data-options="required:true"
                                                               value="${itemsEntity.accountNumber}"/>
                                                    </td>
                                                    <td>
                                                        <input id="accountName${status.index}"
                                                               name="itemsEntity[${status.index}].accountName"
                                                               class="easyui-validatebox" style="width:80px;"
                                                               data-options="required:true"
                                                               value="${itemsEntity.accountName}"/>
                                                    </td>
                                                    <td>
                                                        <input id="accountDept${status.index}"
                                                               name="itemsEntity[${status.index}].accountDept"
                                                            <%--                                                               data-options="panelHeight:300,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'userfactoryid${status.index}\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(${status.index});},onSelect:function(){pcOnchangeFactory('userfactoryid${status.index}',${status.index});}"--%>
                                                               style="width:100px;" class="easyui-validatebox"
                                                               data-options="required:true"
                                                               value="${itemsEntity.accountDept}"/>
                                                    </td>
                                                    <td>
                                                        <input id="accountType${status.index}"
                                                               name="itemsEntity[${status.index}].accountType"
                                                               class="easyui-combobox" style="width:150px"
                                                               value="${itemsEntity.accountType}" disabled
                                                               data-options="valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/tiptop_account_type',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; },validType:'comboxValidate[\'accountType${status.index}\',\'请選擇權限類別\']'"/>
                                                    </td>
                                                    <td>
                                                        <input id="factroyCode${status.index}"
                                                               name="itemsEntity[${status.index}].factroyCode"
                                                               class="easyui-combobox" style="width:150px;" onblur="checkUserInfo('${status.index}')"
                                                               data-options="onSelect:function(){checkUserInfo('${status.index}');},valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/tiptop_factroy_code',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; },validType:'comboBoxEditvalid[\'factroyCode${status.index}\',\'请選擇工廠編碼\']'"
                                                               value="${itemsEntity.factroyCode}"/>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="pcdeltr(${status.index});return false;"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${tiptopProcessEntity.itemsEntity.size()==0 || tiptopProcessEntity.itemsEntity==null}">
                                            <tr align="center" id="itemsEntity0">
                                                <td>1</td>
                                                <td>
                                                    <input id="accountNumber0"
                                                           onblur="getUserNameByEmpno(this,0);"
                                                           name="itemsEntity[0].accountNumber"
                                                           class="easyui-validatebox" style="width:80px;"
                                                           data-options="required:true"
                                                           value="${itemsEntity.accountNumber}"/>
                                                </td>
                                                <td>
                                                    <input id="accountName0"
                                                           name="itemsEntity[0].accountName"
                                                           class="easyui-validatebox" style="width:80px;"
                                                           data-options="required:true"
                                                           value="${itemsEntity.accountName}"/>
                                                </td>
                                                <td>
                                                    <input id="accountDept0"
                                                           name="itemsEntity[0].accountDept"
                                                        <%--                                                               data-options="panelHeight:300,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'userfactoryid${status.index}\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(${status.index});},onSelect:function(){pcOnchangeFactory('userfactoryid${status.index}',${status.index});}"--%>
                                                           style="width:100px;" class="easyui-validatebox"
                                                           data-options="required:true"
                                                           value="${itemsEntity.accountDept}"/>
                                                </td>
                                                <td>
                                                    <input id="accountType0"
                                                           name="itemsEntity[0].accountType"
                                                           class="easyui-combobox" style="width:150px"
                                                           value="${itemsEntity.accountType}" disabled
                                                           data-options="valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/tiptop_account_type',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; },validType:'comboxValidate[\'accountType0\',\'请選擇權限類別\']'"/>
                                                </td>
                                                <td>
                                                    <input id="factroyCode0"
                                                           name="itemsEntity[0].factroyCode"
                                                           class="easyui-combobox" style="width:150px;"
                                                           data-options="onSelect:function(){checkUserInfo('0');},valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/tiptop_factroy_code',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; },validType:'comboBoxEditvalid[\'factroyCode0\',\'请選擇工廠編碼\']'"
                                                           value="${itemsEntity.factroyCode}"/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="pcdeltr(0);return false;"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr0">
                                            <td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="pcItemAdd" style="width:100px;float:left;"
                                                       value="添加一筆"/>
                                            </td>
                                        </tr>
                                        <tr align="center">
                                            <td><a href="${ctx}/tiptopprocess/downLoad"
                                                               id="btnBatchImportTpl">模板下載</a></td>
                                            <td colspan="9" align="left"><span
                                                    class="sl-custom-file"> <input type="button"
                                                                                   onclick="openBatchImportWin();"
                                                                                   value="批量導入" class="btn-file"/>
                            </span>
                                            </td>
                                        </tr>
                                        <tr align="center">
                                            <td>詳細說明&nbsp;<font color="red">*</font></td>
                                            <td colspan="9" class="td_style2" align="left">
                             <textarea id="describtion" name="describtion"
                                       oninput="return LessThanAuto(this,'txtNum');"
                                       onchange="return LessThanAuto(this,'txtNum');"
                                       onpropertychange="return LessThanAuto(this,'txtNum');"
                                       maxlength="100" class="easyui-validatebox" style="width:80%;height:80px;"
                                       data-options="required:true"
                                       rows="5" cols="6">${tiptopProcessEntity.describtion}</textarea><span
                                                    id="txtNum"></span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_tiptopzhanghaoshenqingdan','${comments}','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${tiptopProcessEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${tiptopProcessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${tiptopProcessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${tiptopProcessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="jgshchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">經管初核</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(205,'jgshchargeTable','jgshchargeno','jgshchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jgshchargeno" name="jgshchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['jgshchargeno']}"
                                                               readonly
                                                               value="${tiptopProcessEntity.jgshchargeno }"/><c:if
                                                            test="${requiredMap['jgshchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="jgshchargename" name="jgshchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jgshchargeno']}"
                                                                value="${tiptopProcessEntity.jgshchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="jgfhchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">經管復核</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(206,'jgfhchargeTable','jgfhchargeno','jgfhchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jgfhchargeno" name="jgfhchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['jgfhchargeno']}"
                                                               readonly
                                                               value="${tiptopProcessEntity.jgfhchargeno }"/><c:if
                                                            test="${requiredMap['jgfhchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="jgfhchargename" name="jgfhchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jgfhchargeno']}"
                                                                value="${tiptopProcessEntity.jgfhchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="jghzchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">經管核准</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(150,'jghzchargeTable','jghzchargeno','jghzchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jghzchargeno" name="jghzchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['jghzchargeno']}"
                                                               readonly
                                                               value="${tiptopProcessEntity.jghzchargeno }"/><c:if
                                                            test="${requiredMap['jghzchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="jghzchargename" name="jghzchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jghzchargeno']}"
                                                                value="${tiptopProcessEntity.jghzchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="xtkfgcsqrTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">系統開發工程師確認
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(64,'xtkfgcsqrTable','xtkfgcsqrno','xtkfgcsqrname',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="xtkfgcsqrno" name="xtkfgcsqrno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['xtkfgcsqrno']}"
                                                               readonly
                                                               value="${tiptopProcessEntity.xtkfgcsqrno }"/><c:if
                                                            test="${requiredMap['xtkfgcsqrno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="xtkfgcsqrname" name="xtkfgcsqrname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['xtkfgcsqrno']}"
                                                                value="${tiptopProcessEntity.xtkfgcsqrname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="xtkfkjzgTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">系統開發課級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(65,'xtkfkjzgTable','xtkfkjzgno','xtkfkjzgname',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="xtkfkjzgno" name="xtkfkjzgno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['xtkfkjzgno']}"
                                                               readonly
                                                               value="${tiptopProcessEntity.xtkfkjzgno }"/><c:if
                                                            test="${requiredMap['xtkfkjzgno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="xtkfkjzgname" name="xtkfkjzgname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['xtkfkjzgno']}"
                                                                value="${tiptopProcessEntity.xtkfkjzgname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="xtkfbjzgTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">系統開發部級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(66,'xtkfbjzgTable','xtkfbjzgno','xtkfbjzgname',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="xtkfbjzgno" name="xtkfbjzgno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['xtkfbjzgno']}"
                                                               readonly
                                                               value="${tiptopProcessEntity.xtkfbjzgno }"/><c:if
                                                            test="${requiredMap['xtkfbjzgno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="xtkfbjzgname" name="xtkfbjzgname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['xtkfbjzgno']}"
                                                                value="${tiptopProcessEntity.xtkfbjzgname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="xtkfjaTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">用戶確認
                                                                </td>
                                                                <td style="border: none;">

                                                                    </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="xtkfjano" name="xtkfjano"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['xtkfjano']}"
                                                               readonly
                                                               value="${tiptopProcessEntity.xtkfjano }"/><c:if
                                                            test="${requiredMap['xtkfjano'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="xtkfjaname" name="xtkfjaname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['xtkfjano']}"
                                                                value="${tiptopProcessEntity.xtkfjaname }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
</form>
<div id="optionWin" class="easyui-window" title="Tiptop申請單信息導入" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%">
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="file" type="file" style="width: 300px"
                           accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <span id="labelListAddResult"></span><a href="${ctx}/tiptopprocess/errorExcel" id="downloadError"
                                                     plain="true">查看錯誤信息</a>
                </td>
            </tr>
        </table>
    </form>
</div>
</div>
<script src='${ctx}/static/js/information/tiptopprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>