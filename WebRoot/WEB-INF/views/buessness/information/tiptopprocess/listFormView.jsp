<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>${comments}</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/tiptopprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${tiptopProcessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${tiptopProcessEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">Tiptop賬號申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${tiptopProcessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${tiptopProcessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${tiptopProcessEntity.createDate==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${tiptopProcessEntity.createDate}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${tiptopProcessEntity.makerno}/${tiptopProcessEntity.makername}</div>
        <div class="clear"></div>
        <table  class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="7%">申請人工號</td>
                            <td width="5%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true,disabled:true"
                                       value="<c:if test="${tiptopProcessEntity.applyno!=null&&tiptopProcessEntity.applyno!=''}">${tiptopProcessEntity.applyno}</c:if><c:if test="${tiptopProcessEntity.applyno==null||tiptopProcessEntity.applyno==''}">${user.loginName}</c:if>"
                                       onblur="queryUserInfo('apply');"/>
                            </td>
                            <td width="5%">申請人</td>
                            <td width="5%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${tiptopProcessEntity.applyname }"/>
                            </td>
                            <td width="5%">單位代碼</td>
                            <td width="5%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${tiptopProcessEntity.applydeptno }"/>
                            </td>
                            <td width="5%">聯繫分機</td>
                            <td width="5%" class="td_style1">
                                <input id="applyphone" name="applyphone" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${tiptopProcessEntity.applyphone }"
                                       data-options="required:true,prompt:'579+66666',disabled:true"/>
                            </td>
                            <td width="5%">廠區</td>
                            <td width="5%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${tiptopProcessEntity.applyfactoryid }"
                                       data-options="width: 120,disabled:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox"
                                       data-options="width: 550,required:true,disabled:true"
                                       value="${tiptopProcessEntity.applydeptname }"/>
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       value="${tiptopProcessEntity.applyemail }" style="width:300px;"
                                       data-options="required:true,disabled:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td>申請內容</td>
                            <td class="td_style2" colspan="9">
                                <div class="applyContentDiv" align="left"></div>
                                <input id="applyContentValue" name="applyContentValue"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${tiptopProcessEntity.applyContent }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>Tiptop主機地址</td>
                            <td class="td_style1" colspan="3">
                                <input id="computerAddress" name="computerAddress" class="easyui-combobox"
                                       data-options="width: 250,disabled:true,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/tiptop_computer_address',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; },validType:'comboxValidate[\'computerAddress\',\'请選擇Tiptop主機地址\']'"
                                       value="${tiptopProcessEntity.computerAddress }"/>
                            </td>
                            <td>初始密碼</td>
                            <td class="td_style1" colspan="5">
                                <input id="initPwd" name="initPwd"
                                       class="easyui-validatebox" data-options="width: 250,disabled:true"
                                       value="${tiptopProcessEntity.initPwd }"/>
                            </td>
                        </tr>
                    </table>
                    <table>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="softItemTable" width="100%">
                                        <tr align="center">
                                            <td width="50px;">項次</td>
                                            <td width="100px;">帳號</td>
                                            <td width="100px;">姓名</td>
                                            <td width="100px;">部門代碼</td>
                                            <td width="200px;" id="qxlb">權限類別</td>
                                            <td width="150px;" id="qxlb">權限類別名稱</td>
                                            <td width="200px;">工廠編碼</td>
                                            <%--<td width="50px;">&nbsp;操作&nbsp;</td>--%>
                                        </tr>
                                        <tbody id="info_Body">
                                        <c:if test="${tiptopProcessEntity.itemsEntity!=null&&tiptopProcessEntity.itemsEntity.size()>0}">
                                            <c:forEach items="${tiptopProcessEntity.itemsEntity}" var="itemsEntity"
                                                       varStatus="status">
                                                <tr align="center" id="itemsEntity${status.index}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <input id="accountNumber${status.index}"
                                                               onblur="getUserNameByEmpno(this,status.index);"
                                                               name="itemsEntity[${status.index}].accountNumber"
                                                               class="easyui-validatebox" style="width:80px;"
                                                               data-options="required:true,disabled:true"
                                                               value="${itemsEntity.accountNumber}"/>
                                                    </td>
                                                    <td>
                                                        <input id="accountName${status.index}"
                                                               name="itemsEntity[${status.index}].accountName"
                                                               class="easyui-validatebox" style="width:80px;"
                                                               data-options="required:true,disabled:true"
                                                               value="${itemsEntity.accountName}"/>
                                                    </td>
                                                    <td>
                                                        <input id="accountDept${status.index}"
                                                               name="itemsEntity[${status.index}].accountDept"
                                                            <%--                                                               data-options="panelHeight:300,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'userfactoryid${status.index}\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(${status.index});},onSelect:function(){pcOnchangeFactory('userfactoryid${status.index}',${status.index});}"--%>
                                                               style="width:100px;" class="easyui-validatebox"
                                                               data-options="required:true,disabled:true"
                                                               value="${itemsEntity.accountDept}"/>
                                                    </td>
                                                    <td>
                                                        <input id="accountType${status.index}"
                                                               name="itemsEntity[${status.index}].accountType"
                                                               class="easyui-combobox" style="width:150px"
                                                               value="${itemsEntity.accountType}" disabled
                                                               data-options="valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/tiptop_account_type',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; },validType:'comboxValidate[\'accountType${status.index}\',\'请選擇權限類別\']'"/>
                                                    </td>
                                                    <td>
                                                        <input id="accountTypeName${status.index}"
                                                               name="itemsEntity[${status.index}].accountTypeName"
                                                               class="easyui-validatebox" style="width:150px;"
                                                               data-options="disabled:true"
                                                               value="${itemsEntity.accountTypeName}"/>
                                                    </td>
                                                    <td>
                                                        <input id="factroyCode${status.index}"
                                                               name="itemsEntity[${status.index}].factroyCode"
                                                               class="easyui-combobox" style="width:150px;"
                                                               data-options="valueField:'value',disabled:true,textField:'label',url:'${ctx}/system/dict/getDictByType/tiptop_factroy_code',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; },validType:'comboxValidate[\'factroyCode${status.index}\',\'请選擇工廠編碼\']'"
                                                               value="${itemsEntity.factroyCode}"/>
                                                    </td>
                                                        <%--<td>
                                                            <input type="image" src="${ctx}/static/images/deleteRow.png" disabled
                                                                   onclick="pcdeltr(${status.index});return false;"/>
                                                        </td>--%>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr0">
                                            <td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="pcItemAdd" style="width:100px;float:left;"
                                                       value="添加一筆" disabled/>
                                            </td>
                                        </tr>
                                        <tr align="center">
                                            <td>詳細說明</td>
                                            <td colspan="9" class="td_style2" align="left">
                             <textarea id="describtion" name="describtion"
                                       oninput="return LessThanAuto(this,'txtNum');"
                                       onchange="return LessThanAuto(this,'txtNum');"
                                       onpropertychange="return LessThanAuto(this,'txtNum');"
                                       maxlength="100" class="easyui-validatebox" style="width:80%;height:80px;"
                                       data-options="required:true,disabled:true"
                                       rows="5" cols="6">${tiptopProcessEntity.describtion}</textarea><span
                                                    id="txtNum"></span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
           </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','${comments}');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${tiptopProcessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                               <c:if test="${tiptopProcessEntity.workstatus!=null&&tiptopProcessEntity.workstatus==3}">
                                  <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                      style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                               </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/information/tiptopprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>