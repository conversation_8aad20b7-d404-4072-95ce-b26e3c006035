<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>功能程式發佈申請單流程</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
		<form id="searchFrom" action="">
			系統名稱
            <input type="text" name="filter_EQS_systemId"  class="easyui-validatebox" id="systemId0" />
			<input type="text" name="filter_EQS_applyno" class="easyui-validatebox" data-options="width:150,prompt: '申請人工號'" />
			<input type="text" name="filter_EQS_serialno" class="easyui-validatebox" data-options="width:150,prompt: '任務編碼'" />
			<input id="cxFactory" name="filter_EQS_applyFactoryId" class="easyui-combobox"
				   panelHeight="auto" data-options="width:150"/>
			<input id="qysjzt" style="width:100px" class="easyui-validatebox" name="filter_EQS_workstatus" />
			<input type="text" name="filter_GED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width:150,prompt: '填單开始日期'" /> -
			<input type="text" name="filter_LED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width:150,prompt: '填單结束日期'" />
			<input type="text" name="filter_GED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width:150,prompt: '簽核完成开始日期'" /> -
			<input type="text" name="filter_LED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width:150,prompt: '簽核完成结束日期'" />
		    <span class="toolbar-item dialog-tool-separator"></span>
			<input type="text" name="filter_GED_kfqrCompleteDate" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width:150,prompt: '正式環境完成开始日期'" /> -
			<input type="text" name="filter_LED_kfqrCompleteDate" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width:150,prompt: '正式環境完成结束日期'" />
			<input id="xtkfbjzg" name="filter_EQS_xtkfbjzgno" class="easyui-combobox"
				   panelHeight="auto" data-options="width:160"/>
		    <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
			<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh" onclick="listSearchReset()">重置</a>
			<a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel" onclick="exportExcel()">导出Excel</a>
			<!--導出用，請勿刪除-->
			<input id="page" name="page" type="hidden" value="1" />
			<input id="rows" name="rows" type="hidden" value="30" />
		</form>

	</div>
<table id="dg"></table>
<div id="dlg"></div>

<script src="${ctx}/static/js/information/wfonlineprocess.min.js?random=20221007"></script>
<script type="text/javascript">
    //創建下拉查詢條件
    $.ajax({
        url: ctx+"/system/dict/getDictByType/audit_status",
        dataType:"json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#qysjzt").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
	//創建表單查詢任務廠區下拉查詢條件
	$.get(ctx + '/tqhfactoryidconfig/allFactorys/', function(result) {
		var requestFactoryResult = JSON.parse(JSON.stringify(result));
		requestFactoryResult.unshift({
			factoryid : '',
			factoryname : '請選擇申請人所在廠區'
		});
		$("#cxFactory").combobox({
			data : requestFactoryResult,
			valueField : "factoryid",
			textField : "factoryname",
			editable : false,
			panelHeight : 350
		});
	},"json");
    //創建表單查詢任務廠區下拉查詢條件
    $.get(ctx + '/wfonlineprocess/getXtkfbUsers/', function(result) {
        var requestFactoryResult = JSON.parse(JSON.stringify(result));
        requestFactoryResult.unshift({
            empno : '',
            username : '請選擇系統開發部級主管'
        });
        $("#xtkfbjzg").combobox({
            data : requestFactoryResult,
            valueField : "empno",
            textField : "username",
            editable : false,
            panelHeight : 300
        });
    },"json");
	 $.ajax({
		type : 'GET',
		async : false,
		url :  ctx + '/system/sysinfo/json1',
		dataType : 'json',
		success : function(treedata) {
			treedata.unshift({"id":'',"text":"請選擇","sort":1});
			$('#systemId0').combotree({
				width:300,
			    data: treedata
			});
		},
		error : function(data) {
			$.messager.alert('Info', '操作異常', 'info');
		}
	})


</script>
</body>
</html>
