<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>功能程式發佈申請單流程</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfonlineprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfonlineprocessEntity.id }"/>
    <input id="serialno" name="wfonlineprocess.serialno" type="hidden" value="${wfonlineprocessEntity.serialno }"/>
    <input id="makerno" name="wfonlineprocess.makerno" type="hidden" value="${wfonlineprocessEntity.makerno }"/>
    <input id="makername" name="wfonlineprocess.makername" type="hidden" value="${wfonlineprocessEntity.makername }"/>
    <input id="makerdeptno" name="wfonlineprocess.makerdeptno" type="hidden" value="${wfonlineprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfonlineprocess.makerfactoryid" type="hidden" value="${wfonlineprocessEntity.makerfactoryid }"/>
    <div class="commonW">
    <div class="headTitle">功能/程式發佈申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfonlineprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfonlineprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfonlineprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfonlineprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfonlineprocess.makerno}">
			  <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfonlineprocess.makerno}">
                <div class="position_R margin_R">填單人：${wfonlineprocessEntity.makerno}/${wfonlineprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
		<table class="formList">
		   <tr>
                <td>
                    <table class="formList">
                    	 <tr>
                            <td colspan="8" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="wfonlineprocess.applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfonlineprocessEntity.applyno}" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="wfonlineprocess.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfonlineprocessEntity.applyname }"/>
                            </td>
                            <td width="4%">聯繫電話&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applytel" name="wfonlineprocess.applytel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfonlineprocessEntity.applytel }" data-options="required:true"
                                       onblur="valdApplyTel(this)"/>
                            </td>

                            <td width="4%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="applyFactoryId" name="wfonlineprocess.applyFactoryId" class="easyui-combobox"
                                       panelHeight="auto" value="${wfonlineprocessEntity.applyFactoryId }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applyFactoryId\',\'请选择所在廠區\']'"/>
								<input id="applyfactoryname" name="wfonlineprocess.applyfactoryname" type="hidden" value="${wfonlineprocessEntity.applyfactoryname }"/>
                            </td>
                        </tr>
                        <tr align="center">
                        	<td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydepartno" name="wfonlineprocess.applydepartno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfonlineprocessEntity.applydepartno}"/>
                            </td>
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applydepartname" name="wfonlineprocess.applydepartname" class="easyui-validatebox" data-options="width: 410,required:true"
                                       value="${wfonlineprocessEntity.applydepartname}"/>
                            </td>
							<td>緊急程度</td>
							<td>
								<label><input type="radio" name="urgencyLevel" <c:if test="${wfonlineprocessEntity.urgencyLevel=='0'}">checked</c:if> value="0"/>特急</label>
								<label><input type="radio" name="urgencyLevel" <c:if test="${wfonlineprocessEntity.urgencyLevel=='1'}">checked</c:if> value="1"/>緊急</label>
								<label><input type="radio" name="urgencyLevel" <c:if test="${wfonlineprocessEntity.urgencyLevel=='2'}">checked</c:if> value="2" <c:if test="${wfonlineprocessEntity.urgencyLevel==null}">checked</c:if>/>一般</label>
								<input type="hidden" name="wfonlineprocess.urgencyLevel" <c:if test="${wfonlineprocessEntity.urgencyLevel==null}">value="2"</c:if> <c:if test="${wfonlineprocessEntity.urgencyLevel!=null}">value="${wfonlineprocessEntity.urgencyLevel}"</c:if> />
							</td>
                        </tr>
                        <tr align="center">
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="wfonlineprocess.applyemail" class="easyui-validatebox"
                                       value="${wfonlineprocessEntity.applyemail }" style="width:300px;"
                                       data-options="required:true" onblur="valdEmail('')"/>
                            </td>
                            <td>申請日期&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applydate" name="wfonlineprocess.applydate" class="easyui-validatebox"  data-options="width:100,required:true" readonly
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfonlineprocessEntity.applydate}"/>"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>系統名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                               	<input id="systemId" name="wfonlineprocess.systemId" type="text" value="${wfonlineprocessEntity.systemId }" class="easyui-combotree"
									   data-options="required:'required',validType:'combotreeValidate[\'systemId\',\'请选择系統\']',onSelect:findUserDutyBySys" />
								<input id="systemnameonline" name="wfonlineprocess.systemname" type="hidden" value="${wfonlineprocessEntity.systemname }"/>
                            </td>
                            <td>需求類型&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style2">
                                <div class="requireTypeDiv"></div>
                                <input id="requireType" name="wfonlineprocess.requireType"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfonlineprocessEntity.requireType}"/>
								<input id="requiretypename" name="wfonlineprocess.requiretypename" type="hidden"
									   value="${wfonlineprocessEntity.requiretypename }"/>
                            </td>
                        </tr>
                        <tr align="center">
                        	<td rowspan="2">測試結果說明&nbsp;<font color="red">*</font></td>
                        	<td colspan="7" class="td_style2" style="text-align: left;">
                        		<span style="display: inline-block;width:300px;">
                        			<div class="ptTestDiv" style="display: inline-block;"></div>
                               		<input id="ptTest" name="wfonlineprocess.ptTest"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfonlineprocessEntity.ptTest}"/>
									<input id="pttestname" name="wfonlineprocess.pttestname" type="hidden" value="${wfonlineprocessEntity.pttestname }"/>
                        		</span>
                        		<span style="display: inline-block;width:220px;">
                               		 測試人員：
                               		<input id="ptUser" name="wfonlineprocess.ptUser" class="easyui-validatebox" data-options="width: 120,required:true"
                                       value="${wfonlineprocessEntity.ptUser}"/>
                                </span>
                                <span style="display: inline-block;width:360px;">
	                                 測試日期：
	                                <input id="ptDateStart" name="wfonlineprocess.ptDateStart" class="easyui-validatebox Wdate"
										data-options="width:120,required:true" style="width:120px"
										value="<fmt:formatDate  pattern="yyyy-MM-dd"
										value="${wfonlineprocessEntity.ptDateStart}"/>"
										onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'%y-%M-%d'})" />~
								    <input id="ptDateEnd" name="wfonlineprocess.ptDateEnd" class="easyui-validatebox Wdate"
									data-options="width:120,required:true" style="width:120px"
									value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${wfonlineprocessEntity.ptDateEnd}"/>"
									onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'%y-%M-%d'})" />
							     </span>
                        	</td>
                        </tr>
                        <tr>
                        	<td colspan="7" class="td_style2">
                        		<span style="display: inline-block;width:300px;">
                        			<div class="uatTestDiv" style="display: inline-block;"></div>
                                	<input id="uatTest" name="wfonlineprocess.uatTest"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfonlineprocessEntity.uatTest}"/>
									<input id="uattestname" name="wfonlineprocess.uattestname" type="hidden" value="${wfonlineprocessEntity.uattestname}"/>
                        		</span>
                        		<span style="display: inline-block;width:220px;">
                               	 	測試人員：
                               		<input id="uatUser" name="wfonlineprocess.uatUser" class="easyui-validatebox" data-options="width:120,required:true"
                                       value="${wfonlineprocessEntity.uatUser}"/>
                                </span>
                                <span style="display: inline-block;width:360px;">
	                                測試日期：
	                                <input id="uatDateStart" name="wfonlineprocess.uatDateStart" class="easyui-validatebox Wdate"
										data-options="width:120,required:true" style="width:120px"
										value="<fmt:formatDate  pattern="yyyy-MM-dd"
										value="${wfonlineprocessEntity.uatDateStart}"/>"
										onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'%y-%M-%d'})" />~
								    <input id="uatDateEnd" name="wfonlineprocess.uatDateEnd" class="easyui-validatebox Wdate"
										data-options="width:120,required:true" style="width:120px"
										value="<fmt:formatDate  pattern="yyyy-MM-dd"
										value="${wfonlineprocessEntity.uatDateEnd}"/>"
										onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'%y-%M-%d'})" />
								</span>
                        	</td>
                        </tr>
                         <tr align="center">
                        	<td rowspan="2">資源點檢</td>
                        	<td colspan="7" class="td_style2" style="text-align: left;">
                        		<div class="resourcesDiv"></div>
                                <input id="resources" name="wfonlineprocess.resources"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfonlineprocessEntity.resources}"/>
								<input id="resourcesname" name="wfonlineprocess.resourcesname" type="hidden" value="${wfonlineprocessEntity.resourcesname }"/>
                        	</td>
                        </tr>
                    </table>
                    <table class="formList">
                    	 <tr>
                            <td colspan="3" class="td_style1">需求廠區及期望完成日期</td>
                        </tr>
                        <tr align="center">
                        	<td width="20%">廠區&nbsp;<font color="red">*</font></td>
                        	<td width="50%">樓層&nbsp;<font color="red">*</font></td>
                        	<td width="30%">正式環境完成日期&nbsp;<font color="red">*</font></td>
                        </tr>
                        <tr align="center">
                        	<td>
                        		<input id="requireFactoryId" name="wfonlineprocess.requireFactoryId" class="easyui-combobox"
                                       panelHeight="auto" value="${wfonlineprocessEntity.requireFactoryId}"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'requireFactoryId\',\'请选择所在廠區\']'"/>
								<input id="requirefactoryname" name="wfonlineprocess.requirefactoryname" type="hidden" value="${wfonlineprocessEntity.requirefactoryname }"/>
                        	</td>
                        	<td>
                       		 	<input type="radio" id="requireFloorAll" name="requireFloorRadio" <c:if test="${wfonlineprocessEntity.requireFloor == 'ALL'}">checked="checked"</c:if> onclick="$('#requireFloor').val('ALL').hide();"/>ALL
                       			<input type="radio" id="requireFloorOther" name="requireFloorRadio"  <c:if test="${not empty wfonlineprocessEntity.requireFloor && wfonlineprocessEntity.requireFloor != 'ALL'}">checked="checked"</c:if> onclick="$('#requireFloor').val('').show();"/>其它
                       			<input id="requireFloor" name="wfonlineprocess.requireFloor" style="<c:if test="${empty wfonlineprocessEntity.requireFloor || wfonlineprocessEntity.requireFloor == 'ALL'}">display: none;</c:if>" class="easyui-validatebox" data-options="width:150,required:true"
                                       value="${wfonlineprocessEntity.requireFloor}"/>
                        	</td>
                        	<td>
                       		   	<input id="requireCompleteDate" name="wfonlineprocess.requireCompleteDate" class="easyui-validatebox Wdate"
										data-options="width:120,required:true" style="width:120px"
										value="<fmt:formatDate  pattern="yyyy-MM-dd"
										value="${wfonlineprocessEntity.requireCompleteDate}"/>"
										onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                        	</td>
                        </tr>
                     </table>
                    <table class="formList">
                        <tr align="center">
                        	<td width="12%">
                        		需求描述<font color="red">*</font>
                        	</td>
                        	<td colspan="2" class="td_style1">
                        		<textarea id="requireDetail"
										name="wfonlineprocess.requireDetail" class="easyui-validatebox"
										oninput="return LessThan(this);"
                                      	onchange="return LessThan(this);"
                                      	onpropertychange="return LessThan(this);"
                                      	maxlength="500"
										data-options="multiline:true,required:true,validType:'length[1,500]'"
										style="width:99%;height:120px;resize:none;" rows="5" cols="4">${wfonlineprocessEntity.requireDetail}</textarea>
								<span id="txtNum"></span>
                        	</td>
                        </tr>
                   	</table>
                   	<table class="formList">
                    	 <tr>
                            <td colspan="9" class="td_style1">
                            	發佈功能/程式及順序<br/>
                            	<span style="font-size:12px;color:#111;font-weight:normal;">備注：DBA 作業嚴格依照以下先後順序執行，請謹慎填寫步驟，功能負責人請確認關聯系統每一步驟程序正確</span>
                            </td>
                        </tr>
                        <tr align="center">
                        	<td width="4%;">順序</td>
                        	<td width="10%;">功能模組名稱&nbsp;<font color="red">*</font></td>
							<td width="10%;">廠區&nbsp;<font color="red">*</font></td>
                        	<td width="16%;">發佈時間&nbsp;<font color="red">*</font></td>
                        	<td width="25%;">發佈URL&nbsp;<font color="red">*</font></td>
                        	<td width="10%;">開發DRI&nbsp;<font color="red">*</font></td>
                        	<td width="10%;">開發課級主管&nbsp;<font color="red">*</font></td>
                        	<td width="10%;">開發部級主管&nbsp;<font color="red">*</font></td>
                        	<td width="5%;">操作</td>
                        </tr>
                        <c:choose>
                        	<c:when test="${not empty wfonlineitems}">
                        		<c:forEach items="${wfonlineitems}" var="item" varStatus="is">
	                        		<tr align="center" id="wfonlineitems${is.index}" class="wfonlineitemsTr">
		                        		<td>${is.index + 1}</td>
			                        	<td>
			                        		<input id="items_modulename${is.index}" name="wfonlineitems[${is.index}].modulename" class="easyui-validatebox" data-options="width: 80,required:true" value="${item.modulename}"/>
			                        	</td>
										<td>
											<input id="items_onlinefactory${is.index}" name="wfonlineitems[${is.index}].onlinefactory"
												   data-options="width: 100,valueField:'value',textField:'label',editable:false,url:'${ctx}/system/dict/getDictByType/dict_onlineFactory',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; },validType:'comboxValidate[\'items_onlinefactory${is.index}\',\'请選擇廠區\']'"
												   class="easyui-combobox"  value="${item.onlinefactory}"/>
										</td>
			                        	<td>
			                        		<%--<input id="items_onlineDate${is.index}" name="wfonlineitems[${is.index}].onlineDate" class="easyui-validatebox" data-options="width: 80,required:true" value="${item.onlineDate}"/>--%>
											<input id="items_online2Date${is.index}" name="wfonlineitems[${is.index}].online2Date" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"  data-options="width: 150,required:true" value="<fmt:formatDate  pattern="yyyy-MM-dd HH:mm:ss" value="${item.online2Date}"/>"/>
			                        	</td>
			                        	<td>
			                        		<input id="items_onlineUrl${is.index}" name="wfonlineitems[${is.index}].onlineUrl" class="easyui-validatebox" data-options="width:200,required:true" value="${item.onlineUrl}"/>
			                        	</td>
			                        	<td>
			                        		<input id="items_xtkfDri${is.index}" name="wfonlineitems[${is.index}].xtkfDri" class="easyui-combobox" data-options="width:80,required:true,validType:'comboxValidate[\'items_xtkfDri${is.index}\',\'请选择\']',onSelect:function(record){buildXtNodeArr();}" value="${item.xtkfDri}"/>
			                        		<input id="items_xtkfDriName${is.index}" name="wfonlineitems[${is.index}].xtkfDriName" type="hidden" value="${item.xtkfDriName}"/>
										</td>
			                        	<td>
			                        		<input id="items_xtkfkj${is.index}" name="wfonlineitems[${is.index}].xtkfkj" class="easyui-combobox" data-options="width:80,required:true,validType:'comboxValidate[\'items_xtkfkj${is.index}\',\'请选择\']',onSelect:function(record){buildXtNodeArr();}" value="${item.xtkfkj}"/>
											<input id="items_xtkfkjname${is.index}" name="wfonlineitems[${is.index}].xtkfkjname" type="hidden" value="${item.xtkfkjname}"/>
										</td>
			                        	<td>
			                        		<input id="items_xtkfbj${is.index}" name="wfonlineitems[${is.index}].xtkfbj" class="easyui-combobox" data-options="width:80,required:true,validType:'comboxValidate[\'items_xtkfbj${is.index}\',\'请选择\']',onSelect:function(record){buildXtNodeArr();}" value="${item.xtkfbj}"/>
											<input id="items_xtkfbjname${is.index}" name="wfonlineitems[${is.index}].xtkfbjname" type="hidden" value="${item.xtkfbjname}"/>
										</td>
			                        	<td>
			                        		<input type="image" src="${ctx}/static/images/deleteRow.png" onclick="moduleItemDeltr(${is.index});return false;"/>
			                        	</td>
		                        	</tr>
                        		</c:forEach>
                        	</c:when>
                        	<c:otherwise>
                        	  	<tr align="center" id="wfonlineitems0" class="wfonlineitemsTr">
	                        		<td>1</td>
		                        	<td>
		                        		<input id="items_modulename0" name="wfonlineitems[0].modulename" class="easyui-validatebox" data-options="width: 80,required:true" value=""/>
		                        	</td>
									<td>
										<input id="items_onlinefactory0" name="wfonlineitems[0].onlinefactory"
											   data-options="width: 100,valueField:'value',textField:'label',editable:false,url:'${ctx}/system/dict/getDictByType/dict_onlineFactory',loadFilter: function (data) { data.unshift({ value: '', label: '請選擇' }); return data; },validType:'comboxValidate[\'items_onlinefactory0\',\'请選擇廠區\']'"
											   class="easyui-combobox"  value=""/>
									</td>
		                        	<td>
		                        		<%--<input id="items_onlineDate0" name="wfonlineitems[0].onlineDate" class="easyui-validatebox" data-options="width: 80,required:true" value=""/>--%>
										<%--<input id="items_online2Date0" name="wfonlineitems[0].online2Date" class="easyui-validatebox Wdate" data-options="width: 150,required:true" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'%y-%M-%d'})"/>--%>
										<input id="items_online2Date0" name="wfonlineitems[0].online2Date" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"  data-options="width: 150,required:true" value=""/>
		                        	</td>
		                        	<td>
		                        		<input id="items_onlineUrl0" name="wfonlineitems[0].onlineUrl" class="easyui-validatebox" data-options="width:200,required:true" value=""/>
		                        	</td>
		                        	<td>
		                        		<input id="items_xtkfDri0" name="wfonlineitems[0].xtkfDri" class="easyui-combobox" data-options="width:80,required:true,validType:'comboxValidate[\'items_xtkfDri0\',\'请选择\']',onSelect:function(record){buildXtNodeArr();}" value=""/>
										<input id="items_xtkfDriName0" name="wfonlineitems[0].xtkfDriName" type="hidden" value=""/>
									</td>
		                        	<td>
		                        		<input id="items_xtkfkj0" name="wfonlineitems[0].xtkfkj" class="easyui-combobox" data-options="width:80,required:true,validType:'comboxValidate[\'items_xtkfkj0\',\'请选择\']',onSelect:function(record){buildXtNodeArr();}" value=""/>
										<input id="items_xtkfkjname0" name="wfonlineitems[0].xtkfkjname" type="hidden" value=""/>
									</td>
		                        	<td>
		                        		<input id="items_xtkfbj0" name="wfonlineitems[0].xtkfbj" class="easyui-combobox" data-options="width:80,required:true,validType:'comboxValidate[\'items_xtkfbj0\',\'请选择\']',onSelect:function(record){buildXtNodeArr();}" value=""/>
										<input id="items_xtkfbjname0" name="wfonlineitems[0].xtkfbjname" type="hidden" value=""/>
									</td>
		                        	<td>
		                        		<input type="image" src="${ctx}/static/images/deleteRow.png" onclick="moduleItemDeltr(0);return false;"/>
		                        	</td>
	                        	</tr>
                        	</c:otherwise>
                        </c:choose>
                        <tr align="left" class="nottr">
                            <td colspan="9" width="100%" style="text-align:left;padding-left:10px;">
                                <input type="button" id="moduleItemAdd" style="width:100px;float:left;" value="添加一筆" />
                            </td>
                        </tr>
                    </table>
                   	<table class="formList">
                    	 <tr>
                            <td colspan="7" class="td_style1">
                            	關聯系統功能確認<br/>
                            	<span style="font-size:12px;color:#111;font-weight:normal;">備注：此項為未發佈程式但受此發佈影響，需要驗證功能是否正常的系統</span>
                            </td>
                        </tr>
                        <tr align="center">
                        	<td width="5%;">順序</td>
                        	<td width="25%;">關聯系統名稱</td>
                        	<td width="26%;">發佈URL</td>
                        	<td width="13%;">開發DRI</td>
                        	<td width="13%;">開發課級主管</td>
                        	<td width="13%;">開發部級主管</td>
                        	<td width="5%;">操作</td>
                        </tr>
                        <c:choose>
                        	<c:when test="${not empty wfonlinerelates}">
                        		<c:forEach items="${wfonlinerelates}" var="item" varStatus="rs">
                        			<tr align="center" id="wfonlinerelates${rs.index}" class="wfonlineRealteTr">
			                        	<td>${rs.index + 1}</td>
			                        	<td>
			                        		<input id="relates_glztId${rs.index}" name="wfonlinerelates[${rs.index}].glztId" class="easyui-validatebox" data-options="width: 120,onChange:findRelateUserDuty" value="${item.glztId}"/>
											<input id="relates_glztname${rs.index}" name="wfonlinerelates[${rs.index}].glztname" type="hidden" value="${item.glztname}"/>
			                        	</td>
			                        	<td>
			                        		<input id="relates_onlineUrl${rs.index}" name="wfonlinerelates[${rs.index}].onlineUrl" class="easyui-validatebox" data-options="width:200" value="${item.onlineUrl}"/>
			                        	</td>
			                        	<td>
			                        		<input id="relates_xtkfDri${rs.index}" name="wfonlinerelates[${rs.index}].xtkfDri" class="easyui-combobox" data-options="width:80,onSelect:function(record){buildGlNodeArr();}" value="${item.xtkfDri}"/>
											<input id="relates_xtkfDriName${rs.index}" name="wfonlinerelates[${rs.index}].xtkfDriName" type="hidden" value="${item.xtkfDriName}"/>
			                        	</td>
			                        	<td>
			                        		<input id="relates_xtkfkj${rs.index}" name="wfonlinerelates[${rs.index}].xtkfkj" class="easyui-combobox" data-options="width:80,onSelect:function(record){buildGlNodeArr();}" value="${item.xtkfkj}"/>
											<input id="relates_xtkfkjname${rs.index}" name="wfonlinerelates[${rs.index}].xtkfkjname" type="hidden" value="${item.xtkfkjname}"/>
										</td>
			                        	<td>
			                        		<input id="relates_xtkfbj${rs.index}" name="wfonlinerelates[${rs.index}].xtkfbj" class="easyui-combobox" data-options="width:80,onSelect:function(record){buildGlNodeArr();}" value="${item.xtkfbj}"/>
											<input id="relates_xtkfbjname${rs.index}" name="wfonlinerelates[${rs.index}].xtkfbjname" type="hidden" value="${item.xtkfbjname}"/>
										</td>
			                        	<td>
			                        		<input type="image" src="${ctx}/static/images/deleteRow.png" onclick="relateItemDeltr(${rs.index});return false;"/>
			                        	</td>
			                        </tr>
                        		</c:forEach>
                        	</c:when>
                        	<c:otherwise>
                        		<tr align="center" id="wfonlinerelates0" class="wfonlineRealteTr">
		                        	<td>1</td>
		                        	<td>
		                        		<input id="relates_glztId0" name="wfonlinerelates[0].glztId" class="easyui-validatebox" data-options="width: 120,onChange:findRelateUserDuty" value="${item.glztId}"/>
										<input id="relates_glztname0" name="wfonlinerelates[0].glztname" type="hidden" value="${item.glztname}"/>
									</td>
		                        	<td>
		                        		<input id="relates_onlineUrl0" name="wfonlinerelates[0].onlineUrl" class="easyui-validatebox" data-options="width:200" value="${item.onlineUrl}"/>
		                        	</td>
		                        	<td>
		                        		<input id="relates_xtkfDri0" name="wfonlinerelates[0].xtkfDri" class="easyui-combobox" data-options="width:80,onSelect:function(record){buildGlNodeArr();}" value="${item.xtkfDri}"/>
										<input id="relates_xtkfDriName0" name="wfonlinerelates[0].xtkfDriName" type="hidden" value=""/>
									</td>
		                        	<td>
		                        		<input id="relates_xtkfkj0" name="wfonlinerelates[0].xtkfkj" class="easyui-combobox" data-options="width:80,onSelect:function(record){buildGlNodeArr();}" value="${item.xtkfkj}"/>
										<input id="relates_xtkfkjname0" name="wfonlinerelates[0].xtkfkjname" type="hidden" value=""/>
									</td>
		                        	<td>
		                        		<input id="relates_xtkfbj0" name="wfonlinerelates[0].xtkfbj" class="easyui-combobox" data-options="width:80,onSelect:function(record){buildGlNodeArr();}" value="${item.xtkfbj}"/>
										<input id="relates_xtkfbjname0" name="wfonlinerelates[0].xtkfbjname" type="hidden" value=""/>
									</td>
		                        	<td>
		                        		<input type="image" src="${ctx}/static/images/deleteRow.png" onclick="relateItemDeltr(0);return false;"/>
		                        	</td>
		                        </tr>
                        	</c:otherwise>
                        </c:choose>
                        <tr align="left" class="nottr">
                            <td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
                                <input type="button" id="relateItemAdd" style="width:100px;float:left;" value="添加一筆" />
                            </td>
                        </tr>
                    </table>
                </td>
           </tr>
		   <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                            	<span class="sl-custom-file">
                            		<input type="button" value="点击上传文件" class="btn-file"/>
									<input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
								<input type="hidden" id="attachids" name="wfonlineprocess.attachids" value="${wfonlineprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                        	<td>備註</td>
                        	<td align="left">表單簽核流程：<br/>表單申請——>會簽用戶——>BPM簽核——>系統運維確認——>系統開發確認——>DBA作業。</td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
								<a href="javascript:void(0)"
								   onclick="showWfImag('dzqh_gongnengchengshifabushenqingdan_v3','功能程式發佈申請單流程','');">點擊查看簽核流程圖</a>
							</th>
						</tr>
						<tr>
							<td colspan="10" style="text-align:left;">
								<table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
									<tr>
										<td style="border:none">
											<table width="18%" style="float: left;margin-left: 5px;" id="zxyyfzrTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['zxyyfzrno_name']}
																</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																	onclick="selectRole9(197,'zxyyfzrTable','zxyyfzrno','zxyyfzrname',$('#dealfactoryid').val(),$('#systemId').combotree('getValue'),'wfonlineprocess')"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="zxyyfzrno" name="wfonlineprocess.zxyyfzrno"
															   class="easyui-validatebox"
															   data-options="width:80,required:${requiredMap['zxyyfzrno']}"
															   readonly
															   value="${wfonlineprocess.zxyyfzrno }"/><c:if
															test="${requiredMap['zxyyfzrno'].equals('true')}"><font
															color="red">*</font></c:if>
														/<input id="zxyyfzrname" name="wfonlineprocess.zxyyfzrname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['zxyyfzrno']}"
																value="${wfonlineprocess.zxyyfzrname }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;" id="hqyhTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['hqyhno_name']}</td>
																<td style="border: none;">
																	<a href="javascript:addHq('hqyh');">添加一位</a>
																</td>
															</tr>
                                            			</table>
                                        			</td>
                                    			</tr>
                                    			<tr>
													<td><input id="hqyhno" name="wfonlineprocess.hqyhno"
															   class="easyui-validatebox" data-options="width:80,required:${requiredMap['hqyhno']}"
															   onblur="getUserNameByEmpno(this,'hqyh');"
															   value="${wfonlineprocessEntity.hqyhno }"/><c:if test="${requiredMap['hqyhno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="hqyhname" name="wfonlineprocess.hqyhname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['hqyhno']}"
																value="${wfonlineprocessEntity.hqyhname }"/>
													</td>
												</tr>
											</table>

											<table width="18%" style="float: left;margin-left: 5px;"  id="bpmqhTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['bpmqhno_name']}</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(161,'bpmqhTable','bpmqhno','bpmqhname',$('#dealfactoryid').val(),'wfonlineprocess')"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="bpmqhno" name="wfonlineprocess.bpmqhno"
															   class="easyui-validatebox" data-options="width:80,required:${requiredMap['bpmqhno']}"
															   readonly
															   value="${wfonlineprocessEntity.bpmqhno }"/><c:if test="${requiredMap['bpmqhno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="bpmqhname" name="wfonlineprocess.bpmqhname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['bpmqhno']}"
																value="${wfonlineprocessEntity.bpmqhname }"/>
													</td>
												</tr>
											</table>

											 <table width="18%" style="float: left;margin-left: 5px;"  id="zabjzgTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['zabjzgno_name']}</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(296,'zabjzgTable','zabjzgno','zabjzgname',$('#dealfactoryid').val(),'wfonlineprocess')"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="zabjzgno" name="wfonlineprocess.zabjzgno"
															   class="easyui-validatebox" data-options="width:80,required:${requiredMap['zabjzgno']}"
															   readonly
															   value="${wfonlineprocessEntity.zabjzgno }"/><c:if test="${requiredMap['zabjzgno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="zabjzgname" name="wfonlineprocess.zabjzgname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['zabjzgno']}"
																value="${wfonlineprocessEntity.zabjzgname }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;" id="zxywkjzgTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['zxywkjzgno_name']}
																</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="setYWRole(22,'zxywkjzgTable','zxywkjzgno','zxywkjzgname',$('#dealfactoryid').val())"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="zxywkjzgno" name="wfonlineprocess.zxywkjzgno"
															   class="easyui-validatebox"
															   data-options="width:80,required:${requiredMap['zxywkjzgno']}"
															   readonly
															   value="${wfonlineprocessEntity.zxywkjzgno }"/><c:if
															test="${requiredMap['zxywkjzgno'].equals('true')}"><font
															color="red">*</font></c:if>
														/<input id="zxywkjzgname" name="wfonlineprocess.zxywkjzgname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['zxywkjzgno']}"
																value="${wfonlineprocessEntity.zxywkjzgname }"/>
													</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td style="border:none">

											<table width="18%" style="float: left;margin-left: 5px;" id="zxywbjzgTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['zxywbjzgno_name']}
																</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="setYWRole(25,'zxywbjzgTable','zxywbjzgno','zxywbjzgname',$('#dealfactoryid').val())"></div>
																</td>
                                                			</tr>
                                            			</table>
                                        			</td>
                                    			</tr>
                                    			<tr>
													<td><input id="zxywbjzgno" name="wfonlineprocess.zxywbjzgno"
															   class="easyui-validatebox" data-options="width:80,required:${requiredMap['zxywbjzgno']}"
															   readonly
															   value="${wfonlineprocessEntity.zxywbjzgno }"/><c:if test="${requiredMap['zxywbjzgno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="zxywbjzgname" name="wfonlineprocess.zxywbjzgname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['zxywbjzgno']}"
																value="${wfonlineprocessEntity.zxywbjzgname }"/>
													</td>
                                    			</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;" id="zxywcjzgTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['zxywcjzgno_name']}
																</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="setYWRole(260,'zxywcjzgTable','zxywcjzgno','zxywcjzgname',$('#dealfactoryid').val())"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="zxywcjzgno" name="wfonlineprocess.zxywcjzgno"
															   class="easyui-validatebox" data-options="width:80,required:${requiredMap['zxywcjzgno']}"
															   readonly
															   value="${wfonlineprocessEntity.zxywcjzgno }"/><c:if test="${requiredMap['zxywcjzgno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="zxywcjzgname" name="wfonlineprocess.zxywcjzgname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['zxywcjzgno']}"
																value="${wfonlineprocessEntity.zxywcjzgname }"/>
													</td>
												</tr>
											</table>

											<table width="18%" style="float: left;margin-left: 5px;"  id="xtkfgcsqrTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: center;">${requiredMap['xtkfgcsqrno_name']}</td>
																<!-- <td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(64,'xtkfgcsqrTable','xtkfgcsqrno','xtkfgcsqrname',$('#dealfactoryid').val(),null)"></div>
																</td> -->
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="xtkfgcsqrno" name="wfonlineprocess.xtkfgcsqrno"
															   class="easyui-validatebox" data-options="width:80,required:${requiredMap['xtkfgcsqrno']}"
															   readonly
															   value="${wfonlineprocessEntity.xtkfgcsqrno }"/><c:if test="${requiredMap['xtkfgcsqrno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="xtkfgcsqrname" name="wfonlineprocess.xtkfgcsqrname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['xtkfgcsqrno']}"
																value="${wfonlineprocessEntity.xtkfgcsqrname }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;"  id="xtkfkjzgTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: center;">${requiredMap['xtkfkjzgno_name']}</td>
															   <!-- <td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(65,'xtkfkjzgTable','xtkfkjzgno','xtkfkjzgname',$('#dealfactoryid').val(),null)"></div>
																</td> -->
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="xtkfkjzgno" name="wfonlineprocess.xtkfkjzgno"
															   class="easyui-validatebox" data-options="width:80,required:${requiredMap['xtkfkjzgno']}"
															   readonly
															   value="${wfonlineprocessEntity.xtkfkjzgno }"/><c:if test="${requiredMap['xtkfkjzgno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="xtkfkjzgname" name="wfonlineprocess.xtkfkjzgname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['xtkfkjzgno']}"
																value="${wfonlineprocessEntity.xtkfkjzgname }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;"  id="xtkfbjzgTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: center;">${requiredMap['xtkfbjzgno_name']}</td>
																<!-- <td style="border: none;">
                                                                     <div class="float_L qhUserIcon"
                                                                          onclick="selectRole2(66,'xtkfbjzgTable','xtkfbjzgno','xtkfbjzgname',$('#dealfactoryid').val(),null)"></div>
                                                                 </td> -->
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="xtkfbjzgno" name="wfonlineprocess.xtkfbjzgno"
															   class="easyui-validatebox" data-options="width:80,required:${requiredMap['xtkfbjzgno']}"
															   readonly
															   value="${wfonlineprocessEntity.xtkfbjzgno }"/><c:if test="${requiredMap['xtkfbjzgno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="xtkfbjzgname" name="wfonlineprocess.xtkfbjzgname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['xtkfbjzgno']}"
																value="${wfonlineprocessEntity.xtkfbjzgname }"/>
													</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td style="border:none">
											<table width="18%" style="float: left;margin-left: 5px;" id="glxtkfgcsTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: center;">${requiredMap['glxtkfgcsno_name']}
																</td>
																<!--  <td style="border: none;">
                                                                      <div class="float_L qhUserIcon"
                                                                           onclick="selectRole2(170,'glxtkfgcsTable','glxtkfgcsno','glxtkfgcsname',$('#dealfactoryid').val(),null)"></div>
                                                                  </td> -->
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="glxtkfgcsno" name="wfonlineprocess.glxtkfgcsno"
															   class="easyui-validatebox"
															   data-options="width:80,required:${requiredMap['glxtkfgcsno']}"
															   readonly
															   value="${wfonlineprocessEntity.glxtkfgcsno }"/><c:if
															test="${requiredMap['glxtkfgcsno'].equals('true')}"><font
															color="red">*</font></c:if>
														/<input id="glxtkfgcsname" name="wfonlineprocess.glxtkfgcsname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['glxtkfgcsno']}"
																value="${wfonlineprocessEntity.glxtkfgcsname }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;" id="glxtkfkjzgTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: center;">
																	${requiredMap['glxtkfkjzgno_name']}
																</td>
																<!-- <td style="border: none;">
                                                                     <div class="float_L qhUserIcon"
                                                                          onclick="selectRole2(171,'glxtkfkjzgTable','glxtkfkjzgno','glxtkfkjzgname',$('#dealfactoryid').val(),null)"></div>
                                                                 </td> -->
															</tr>
                                            			</table>
                                        			</td>
                                    			</tr>
                                    			<tr>
													<td><input id="glxtkfkjzgno" name="wfonlineprocess.glxtkfkjzgno"
															   class="easyui-validatebox" data-options="width:80,required:${requiredMap['glxtkfkjzgno']}"
															   readonly
															   value="${wfonlineprocessEntity.glxtkfkjzgno }"/><c:if test="${requiredMap['glxtkfkjzgno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="glxtkfkjzgname" name="wfonlineprocess.glxtkfkjzgname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['glxtkfkjzgno']}"
																value="${wfonlineprocessEntity.glxtkfkjzgname }"/>
													</td>
                                    			</tr>
                                			</table>
											<table width="18%" style="float: left;margin-left: 5px;"  id="glxtkfbjzgTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: center;">${requiredMap['glxtkfbjzgno_name']}</td>
															   <!-- <td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(172,'glxtkfbjzgTable','glxtkfbjzgno','glxtkfbjzgname',$('#dealfactoryid').val(),null)"></div>
																</td> -->
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="glxtkfbjzgno" name="wfonlineprocess.glxtkfbjzgno"
															   class="easyui-validatebox" data-options="width:80,required:${requiredMap['glxtkfbjzgno']}"
															   readonly
															   value="${wfonlineprocessEntity.glxtkfbjzgno }"/><c:if test="${requiredMap['glxtkfbjzgno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="glxtkfbjzgname" name="wfonlineprocess.glxtkfbjzgname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['glxtkfbjzgno']}"
																value="${wfonlineprocessEntity.glxtkfbjzgname }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;"  id="xtkfzxjzgTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['xtkfzxjzgno_name']}</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(261,'xtkfzxjzgTable','xtkfzxjzgno','xtkfzxjzgname',$('#dealfactoryid').val(),'wfonlineprocess')"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="xtkfzxjzgno" name="wfonlineprocess.xtkfzxjzgno"
															   class="easyui-validatebox" data-options="width:80,required:${requiredMap['xtkfzxjzgno']}"
															   readonly
															   value="${wfonlineprocessEntity.xtkfzxjzgno }"/><c:if test="${requiredMap['xtkfzxjzgno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="xtkfzxjzgname" name="wfonlineprocess.xtkfzxjzgname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['xtkfzxjzgno']}"
																value="${wfonlineprocessEntity.xtkfzxjzgname }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;"  id="xtkfcjzgTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['xtkfcjzgno_name']}</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(1200,'xtkfcjzgTable','xtkfcjzgno','xtkfcjzgname',$('#dealfactoryid').val(),'wfonlineprocess')"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="xtkfcjzgno" name="wfonlineprocess.xtkfcjzgno"
															   class="easyui-validatebox" data-options="width:80,required:${requiredMap['xtkfcjzgno']}"
															   readonly
															   value="${wfonlineprocessEntity.xtkfcjzgno }"/><c:if test="${requiredMap['xtkfcjzgno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="xtkfcjzgname" name="wfonlineprocess.xtkfcjzgname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['xtkfcjzgno']}"
																value="${wfonlineprocessEntity.xtkfcjzgname }"/>
													</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td style="border:none">
											<table width="18%" style="float: left;margin-left: 5px;" id="dbazyryTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;"><!-- 資訊運維責任人作業 -->${requiredMap['dbazyryno_name']}</td>
																<td style="border: none;">
																	<!-- <div class="float_L qhUserIcon" onclick="selectRole($('#dealdeptno').val(),'dbazyryno','dbazyryname',$('#dealfactoryid').val())"></div> -->
																	<!--<div class="float_L qhUserIcon"
                                                                    onclick="selectRole5('173','dbazyryno','dbazyryname','dbazyno','dbazyname','','',$('#dealfactoryid').val())">-->
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole7(173,'dbazyryTable','dbazyryno','dbazyryname',$('#dealfactoryid').val(),'wfonlineprocess','dbazyTable','dbazyno','dbazyname')">
																	</div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="dbazyryno" name="wfonlineprocess.dbazyryno"
															   class="easyui-validatebox" data-options="width:80,required:${requiredMap['dbazyryno']}"
															   readonly
															   value="${wfonlineprocessEntity.dbazyryno }"/><c:if test="${requiredMap['dbazyryno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="dbazyryname" name="wfonlineprocess.dbazyryname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['dbazyryno']}"
																value="${wfonlineprocessEntity.dbazyryname }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;"  id="dbakjzgTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['dbakjzgno_name']}</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(174,'dbakjzgTable','dbakjzgno','dbakjzgname',$('#dealfactoryid').val(),'wfonlineprocess')"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="dbakjzgno" name="wfonlineprocess.dbakjzgno"
															   class="easyui-validatebox" data-options="width:80,required:${requiredMap['dbakjzgno']}"
															   readonly
															   value="${wfonlineprocessEntity.dbakjzgno }"/><c:if test="${requiredMap['dbakjzgno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="dbakjzgname" name="wfonlineprocess.dbakjzgname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['dbakjzgno']}"
																value="${wfonlineprocessEntity.dbakjzgname }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;" id="dbabjzgTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['dbabjzgno_name']}</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(175,'dbabjzgTable','dbabjzgno','dbabjzgname',$('#dealfactoryid').val(),'wfonlineprocess')"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="dbabjzgno" name="wfonlineprocess.dbabjzgno"
															   class="easyui-validatebox"
															   data-options="width:80,required:${requiredMap['dbabjzgno']}"
															   readonly
															   value="${wfonlineprocessEntity.dbabjzgno }"/><c:if
															test="${requiredMap['dbabjzgno'].equals('true')}"><font
															color="red">*</font></c:if>
														/<input id="dbabjzgname" name="wfonlineprocess.dbabjzgname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['dbabjzgno']}"
																value="${wfonlineprocessEntity.dbabjzgname }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;" id="dbazyTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: center;">${requiredMap['dbazyno_name']}</td>
																<td style="border: none;">
																	<!--<div class="float_L qhUserIcon"  onclick="selectRole2(176,'dbazyTable','dbazyno','dbazyname',$('#dealfactoryid').val(),null)"></div>-->
																</td>
															</tr>
                                            			</table>
                                        			</td>
                                    			</tr>
                                    			<tr>
													<td><input id="dbazyno" name="wfonlineprocess.dbazyno"
															   class="easyui-validatebox" data-options="width:80,required:${requiredMap['dbazyno']}"
															   readonly
															   value="${wfonlineprocessEntity.dbazyno }"/><c:if test="${requiredMap['dbazyno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="dbazyname" name="wfonlineprocess.dbazyname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['dbazyno']}"
																value="${wfonlineprocessEntity.dbazyname }"/>
													</td>
                                    			</tr>
                                			</table>
											<table width="18%" style="float: left;margin-left: 5px;display:none"  id="zaglzxgcsTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['zaglzxgcsno_name']}</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(163,'zaglzxgcsTable','zaglzxgcsno','zaglzxgcsname',$('#dealfactoryid').val(),'wfonlineprocess')"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="zaglzxgcsno" name="wfonlineprocess.zaglzxgcsno"
															   class="easyui-validatebox" data-options="width:80,required:${requiredMap['zaglzxgcsno']}"
															   readonly
															   value="${wfonlineprocessEntity.zaglzxgcsno }"/><c:if test="${requiredMap['zaglzxgcsno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="zaglzxgcsname" name="wfonlineprocess.zaglzxgcsname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['zaglzxgcsno']}"
																value="${wfonlineprocessEntity.zaglzxgcsname }"/>
													</td>
												</tr>
											</table>
											<table width="18%" style="float: left;margin-left: 5px;display:none"  id="zaglzxbjzgTable">
												<tr>
													<td>
														<table width="100%">
															<tr>
																<td style="border: none;text-align: right;">${requiredMap['zaglzxbjzgno_name']}</td>
																<td style="border: none;">
																	<div class="float_L qhUserIcon"
																		 onclick="selectRole2(164,'zaglzxbjzgTable','zaglzxbjzgno','zaglzxbjzgname',$('#dealfactoryid').val(),'wfonlineprocess')"></div>
																</td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td><input id="zaglzxbjzgno" name="wfonlineprocess.zaglzxbjzgno"
															   class="easyui-validatebox" data-options="width:80,required:${requiredMap['zaglzxbjzgno']}"
															   readonly
															   value="${wfonlineprocessEntity.zaglzxbjzgno }"/><c:if test="${requiredMap['zaglzxbjzgno'].equals('true')}"><font color="red">*</font></c:if>
														/<input id="zaglzxbjzgname" name="wfonlineprocess.zaglzxbjzgname"
																readonly class="easyui-validatebox"
																data-options="width:80,required:${requiredMap['zaglzxbjzgno']}"
																value="${wfonlineprocessEntity.zaglzxbjzgname }"/>
													</td>
												</tr>
											</table>
                             			</td>
                        			</tr>
                    			</table>
                			</td>
            			</tr>

						<tr>
							<td colspan="10" style="text-align:left;">
								<table class="flowList" style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
									<tr>
										<td>簽核時間</td>
										<td>簽核節點</td>
										<td>簽核主管</td>
										<td>簽核意見</td>
										<td>批註</td>
										<td>簽核電腦IP</td>
									</tr>
								</table>
							</td>
						</tr>
						<tr>
							<td colspan="10" style="border:none;text-align:center;margin-top:10px">
								<a href="javascript:;" id="btnSave" class="easyui-linkbutton" data-options="iconCls:'icon-add'"
								   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
								<a href="javascript:;" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
								   style="width: 100px;" onclick="saveInfo(2);">提交</a>
							</td>
						</tr>
        			</table>
        		</td>
        	</tr>
        </table>
	</div>
	<input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="dealfactoryid" value="${wfonlineprocessEntity.makerfactoryid }"/>
    <input type="hidden" id="dealdeptno" value="${wfonlineprocessEntity.makerdeptno}"/>
     <div id="win"></div>
	</form>
  </div>
<script src='${ctx}/static/js/information/wfonlineprocess.min.js?random=20221007'></script>
</body>
</html>
