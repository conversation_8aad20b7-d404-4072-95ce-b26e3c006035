<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>功能程式發佈申請單流程</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfonlineprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfonlineprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfonlineprocessEntity.serialno }"/>
    <input type="hidden" id="dealfactoryid" value="${wfonlineprocessEntity.makerfactoryid }"/>
    <input type="hidden" id="dealdeptno" value=""/>
    <div class="commonW">
    <div class="headTitle">功能/程式發佈申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfonlineprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfonlineprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfonlineprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfonlineprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfonlineprocessEntity.makerno}/${wfonlineprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
           <tr>
                <td>
                    <table class="formList">
                    	 <tr>
                            <td colspan="8" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號</td>
                            <td width="6%" class="td_style2">${wfonlineprocessEntity.applyno}</td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style2">${wfonlineprocessEntity.applyname}</td>
                            <td width="4%">聯繫電話</td>
                            <td width="6%" class="td_style2">${wfonlineprocessEntity.applytel}</td>
                            <td width="4%">所在廠區</td>
                            <td width="7%" class="td_style2">
								<c:choose>
									<c:when test="${wfonlineprocessEntity.applyfactoryname!=''&& wfonlineprocessEntity.applyfactoryname!=null}">
										${wfonlineprocessEntity.applyfactoryname}
									</c:when>
									<c:otherwise>
										<input id="applyFactoryId"  class="easyui-combobox"
											   panelHeight="auto" value="${wfonlineprocessEntity.applyFactoryId}" disabled
											   data-options="width: 120"/>
									</c:otherwise>
								</c:choose>
                            </td>
                        </tr>
                        <tr align="center">
                        	<td width="4%">單位代碼</td>
                            <td width="6%" class="td_style2">${wfonlineprocessEntity.applydepartno}</td>
                            <td>單位</td>
                            <td colspan="5" class="td_style2">${wfonlineprocessEntity.applydepartname}</td>
                        </tr>
                        <tr align="center">
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfonlineprocessEntity.applyemail}</td>
                            <td>申請日期</td>
                            <td colspan="3" class="td_style2">
                                <fmt:formatDate value='${wfonlineprocessEntity.applydate}' pattern='yyyy-MM-dd'/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>系統名稱</td>
                            <td colspan="3" class="td_style2">
								<c:choose>
									<c:when test="${wfonlineprocessEntity.systemname!=''&& wfonlineprocessEntity.systemname!=null}">
										${wfonlineprocessEntity.systemname}
									</c:when>
									<c:otherwise>
										<input id="systemId" name="wfonlineprocess.systemId" value="${wfonlineprocessEntity.systemId }" class="easyui-combotree"
											   data-options="width: 120" disabled />
									</c:otherwise>
								</c:choose>
							</td>
                            <td>需求類型</td>
                            <td colspan="3" class="td_style2">
								<c:choose>
									<c:when test="${wfonlineprocessEntity.requiretypename!=''&& wfonlineprocessEntity.requiretypename!=null}">
										${wfonlineprocessEntity.requiretypename}
									</c:when>
									<c:otherwise>
										<div class="requireTypeDiv"></div>
										<input id="requireType" name="wfonlineprocess.requireType"
											   type="hidden" class="easyui-validatebox" data-options="width: 100"
											   value="${wfonlineprocessEntity.requireType}"/>
									</c:otherwise>
								</c:choose>
                            </td>
                        </tr>
                        <tr align="center">
                        	<td rowspan="2">測試結果說明</td>
                        	<td colspan="7" class="td_style2" style="text-align: left;">
                        		<span style="display: inline-block;width:300px;">
	                        		<%--<div class="ptTestDiv" style="display: inline-block;"></div>
                               		<input id="ptTest" name="wfonlineprocess.ptTest"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfonlineprocessEntity.ptTest}"/>
                                     <font color="red">*</font>--%>
									<c:choose>
										<c:when test="${wfonlineprocessEntity.pttestname!=''&& wfonlineprocessEntity.pttestname!=null}">
											${wfonlineprocessEntity.pttestname}
										</c:when>
										<c:otherwise>
											<div class="ptTestDiv" style="display: inline-block;"></div>
											<input id="ptTest" name="wfonlineprocess.ptTest"
												   type="hidden" class="easyui-validatebox" data-options="width: 100"
												   value="${wfonlineprocessEntity.ptTest}"/>
										</c:otherwise>
									</c:choose>
                        		</span>
                        		<span style="display: inline-block;width:220px;">
                               		 測試人員：${wfonlineprocessEntity.ptUser}
                                </span>
                                <span style="display: inline-block;width:360px;">
	                                測試日期：
	                                <fmt:formatDate value='${wfonlineprocessEntity.ptDateStart}' pattern='yyyy-MM-dd'/>~
	                                <fmt:formatDate value='${wfonlineprocessEntity.ptDateEnd}' pattern='yyyy-MM-dd'/>
							     </span>
                        	</td>
                        </tr>
                        <tr>
                        	<td colspan="7" class="td_style2">
                        		<span style="display: inline-block;width:300px;">
	                                <%--<div class="uatTestDiv" style="display: inline-block;"></div>
                                	<input id="uatTest" name="wfonlineprocess.uatTest"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfonlineprocessEntity.uatTest}"/>
	                                <font color="red">*</font>--%>
									<c:choose>
										<c:when test="${wfonlineprocessEntity.uattestname!=''&& wfonlineprocessEntity.uattestname!=null}">
											${wfonlineprocessEntity.uattestname}
										</c:when>
										<c:otherwise>
											<div class="uatTestDiv" style="display: inline-block;"></div>
											<input id="uatTest" name="wfonlineprocess.uatTest"
												   type="hidden" class="easyui-validatebox" data-options="width: 100"
												   value="${wfonlineprocessEntity.uatTest}"/>
										</c:otherwise>
									</c:choose>
                        		</span>
                        		<span style="display: inline-block;width:220px;">
                               	 	測試人員：${wfonlineprocessEntity.uatUser}
                                </span>
                                <span style="display: inline-block;width:360px;">
									測試日期：
                                    <fmt:formatDate value='${wfonlineprocessEntity.uatDateStart}' pattern='yyyy-MM-dd'/>~
	                                <fmt:formatDate value='${wfonlineprocessEntity.uatDateEnd}' pattern='yyyy-MM-dd'/>
								</span>
                        	</td>
                        </tr>
                         <tr align="center">
                        	<td rowspan="2">資源點檢：</td>
                        	<td colspan="7" class="td_style2" style="text-align: left;">
                        		<%--<div class="resourcesDiv"></div>
                                <input id="resources" name="wfonlineprocess.resources"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfonlineprocessEntity.resources}"/>--%>
								<c:choose>
									<c:when test="${wfonlineprocessEntity.resourcesname!=''&& wfonlineprocessEntity.resourcesname!=null}">
										${wfonlineprocessEntity.resourcesname}
									</c:when>
									<c:otherwise>
										<c:if test="${wfonlineprocessEntity.resources!='' && wfonlineprocessEntity.resourcesname!=null}">
											<div class="resourcesDiv"></div>
											<input id="resources" name="wfonlineprocess.resources"
												   type="hidden" class="easyui-validatebox" data-options="width: 100"
												   value="${wfonlineprocessEntity.resources}"/>
										</c:if>
									</c:otherwise>
								</c:choose>
                            </td>
                        </tr>
                    </table>
                    <table class="formList">
                    	 <tr>
                            <td colspan="3" class="td_style1">需求廠區及期望完成日期</td>
                        </tr>
                        <tr align="center">
                        	<td width="20%">廠區</td>
                        	<td width="50%">樓層</td>
                        	<td width="30%">正式環境完成日期</td>
                        </tr>
                        <tr align="center">
                        	<td>
								<c:choose>
									<c:when test="${wfonlineprocessEntity.requirefactoryname!=''&& wfonlineprocessEntity.requirefactoryname!=null}">
										${wfonlineprocessEntity.requirefactoryname}
									</c:when>
									<c:otherwise>
										<input id="requireFactoryId"  class="easyui-combobox"
											   panelHeight="auto" value="${wfonlineprocessEntity.requireFactoryId}" disabled
											   data-options="width: 120"/>
									</c:otherwise>
								</c:choose>
                        	</td>
                        	<td>
                        		<c:if test="${wfonlineprocessEntity.requireFloor != 'ALL'}">其它</c:if>
                       		 	${wfonlineprocessEntity.requireFloor}
                        	</td>
                        	<td>
                       		    <fmt:formatDate value='${wfonlineprocessEntity.requireCompleteDate}' pattern='yyyy-MM-dd'/>
                        	</td>
                        </tr>
                    </table>
                    <table class="formList">
                        <tr align="center">
                        	<td width="12%">
                        		需求描述
                        	</td>
                        	<td colspan="2" align="left">
								<textarea id="requireDetail"
										  name="wfonlineprocess.requireDetail" class="easyui-validatebox"
										  disabled readonly
										  style="width:99%;height:120px;resize:none;background-color: #F2F5F7;outline: none;" rows="5" cols="4">${wfonlineprocessEntity.requireDetail}</textarea>
                        	</td>
                        </tr>
                   	</table>
                   	<table class="formList">
                    	 <tr>
                            <td colspan="8" class="td_style1">
                            	發佈功能/程式及順序<br/>
                            	<span style="font-size:12px;color:#111;font-weight:normal;">備注：DBA 作業嚴格依照以下先後順序執行，請謹慎填寫步驟，功能負責人請確認關聯系統每一步驟程序正確</span>
                            </td>
                        </tr>
                        <tr align="center">
                        	<td width="5%;">順序</td>
                        	<td width="10%;">功能模組名稱</td>
							<td width="10%;">廠區</td>
                        	<td width="15%;">發佈時間</td>
                        	<td width="26%;">發佈URL</td>
                        	<td width="13%;">開發DRI</td>
                        	<td width="13%;">開發課級主管</td>
                        	<td width="13%;">開發部級主管</td>
                        </tr>
                       	<c:if test="${not empty wfonlineitems}">
                       		<c:forEach items="${wfonlineitems}" var="item" varStatus="is">
                        		<tr align="center" id="wfonlineitems${is.index}" class="wfonlineitemsTr">
	                        		<td>${is.index + 1}</td>
		                        	<td>
		                        		${item.modulename}
		                        	</td>
									<td>
										${item.onlinefactory}
									</td>
		                        	<td>
		                        		${item.onlineDate}
		                        	</td>
		                        	<td>
		                        		${item.onlineUrl}
		                        	</td>
	                        		<td>
										<c:choose>
											<c:when test="${item.xtkfDriName!=''&& item.xtkfDriName!=null}">
												${item.xtkfDriName}
											</c:when>
											<c:otherwise>
												<input id="items_xtkfDri${is.index}" name="wfonlineitems[${is.index}].xtkfDri" class="easyui-combobox" disabled data-options="width:80" value="${item.xtkfDri}"/>
											</c:otherwise>
										</c:choose>
									</td>
		                        	<td>
										<c:choose>
											<c:when test="${item.xtkfkjname!=''&& item.xtkfkjname!=null}">
												${item.xtkfkjname}
											</c:when>
											<c:otherwise>
												<input id="items_xtkfkj${is.index}" name="wfonlineitems[${is.index}].xtkfkj" class="easyui-combobox" disabled data-options="width:80" value="${item.xtkfkj}"/>
											</c:otherwise>
										</c:choose>
									</td>
		                        	<td>
										<c:choose>
											<c:when test="${item.xtkfbjname!=''&& item.xtkfbjname!=null}">
												${item.xtkfbjname}
											</c:when>
											<c:otherwise>
												<input id="items_xtkfbj${is.index}" name="wfonlineitems[${is.index}].xtkfbj" class="easyui-combobox" disabled data-options="width:80" value="${item.xtkfbj}"/>
											</c:otherwise>
										</c:choose>
									</td>
	                        	</tr>
                       		</c:forEach>
                       	</c:if>
                    </table>
                   	<table class="formList">
                    	 <tr>
                            <td colspan="7" class="td_style1">
                            	關聯系統功能確認<br/>
                            	<span style="font-size:12px;color:#111;font-weight:normal;">備注：此項為未發佈程式但受此發佈影響，需要驗證功能是否正常的系統</span>
                            </td>
                        </tr>
                        <tr align="center">
                        	<td width="5%;">順序</td>
                        	<td width="25%;">關聯系統名稱</td>
                        	<td width="26%;">發佈URL</td>
                        	<td width="13%;">開發DRI</td>
                        	<td width="13%;">開發課級主管</td>
                        	<td width="13%;">開發部級主管</td>
                        </tr>
                       	<c:if test="${not empty wfonlinerelates}">
                       		<c:forEach items="${wfonlinerelates}" var="item" varStatus="rs">
                       			<tr align="center" id="wfonlinerelates${rs.index}" class="wfonlineRealteTr">
		                        	<td>${rs.index + 1}</td>
		                        	<td>
										<c:choose>
											<c:when test="${item.glztname!=''&& item.glztname!=null}">
												${item.glztname}
											</c:when>
											<c:otherwise>
												<input id="relates_glztId${rs.index}" name="wfonlinerelates[${rs.index}].glztId" class="easyui-combotree" disabled="disabled" data-options="width: 120" value="${item.glztId}"/>
											</c:otherwise>
										</c:choose>
									</td>
		                        	<td>
		                        		${item.onlineUrl}
		                        	</td>
		                        	<td>
										<c:choose>
											<c:when test="${item.xtkfDriName!=''&& item.xtkfDriName!=null}">
												${item.xtkfDriName}
											</c:when>
											<c:otherwise>
												<input id="relates_xtkfDri${rs.index}" name="wfonlinerelates[${rs.index}].xtkfDri" class="easyui-combobox" disabled="disabled" data-options="width:80" value="${item.xtkfDri}"/>
											</c:otherwise>
										</c:choose>
									</td>
		                        	<td>
										<c:choose>
											<c:when test="${item.xtkfkjname!=''&& item.xtkfkjname!=null}">
												${item.xtkfkjname}
											</c:when>
											<c:otherwise>
												<input id="relates_xtkfkj${rs.index}" name="wfonlinerelates[${rs.index}].xtkfkj" class="easyui-combobox" disabled="disabled" data-options="width:80" value="${item.xtkfkj}"/>
											</c:otherwise>
										</c:choose>
									</td>
		                        	<td>
										<c:choose>
											<c:when test="${item.xtkfbjname!=''&& item.xtkfbjname!=null}">
												${item.xtkfbjname}
											</c:when>
											<c:otherwise>
												<input id="relates_xtkfbj${rs.index}" name="wfonlinerelates[${rs.index}].xtkfbj" class="easyui-combobox"  disabled="disabled" data-options="width:80" value="${item.xtkfbj}"/>
											</c:otherwise>
										</c:choose>
									</td>
		                        </tr>
                       		</c:forEach>
                       	</c:if>
                    </table>
                </td>
           </tr>
            <tr>
                <td>
                  <!-- BPM簽核幾點補充說明 -->
               	  <c:choose>
	           		<c:when test="${not empty nodeName && 'BPM簽核' eq nodeName}">
	           		<table class="formList">
	                     <tr align="center">
	                      	<td width="12%">
	                      		補充說明<br/>（BPM填寫）<font color="red">*</font>
	                      	</td>
	                      	<td colspan="2" class="td_style1">
                        		<textarea id="bpmpz"
										name="wfonlineprocess.bpmpz" class="easyui-validatebox"
										oninput="return LessThan(this);"
                                      	onchange="return LessThan(this);"
                                      	onpropertychange="return LessThan(this);"
                                      	maxlength="300"
										data-options="multiline:true,required:true,validType:'length[1,300]'"
										style="width:99%;height:90px;resize:none;" rows="4" cols="4"></textarea>
								<span id="txtNum"></span>
                        	</td>
	                      </tr>
                     </table>
	           		</c:when>
	           		<c:otherwise>
	           			<c:if test="${not empty wfonlineprocessEntity.bpmpz}">
	           			<table class="formList">
		                     <tr align="center">
		                      	<td width="12%">
		                      		補充說明<br/>（BPM填寫）
		                      	</td>
		                      	<td colspan="2" align="left">
		                      		${wfonlineprocessEntity.bpmpz}
		                      	</td>
		                      </tr>
		                 </table>
	           			</c:if>
	           		</c:otherwise>
		           </c:choose>

		           <!-- 系統開發工程師確認 -->
            	   <c:choose>
	           		<c:when test="${not empty nodeName && '系統開發工程師確認' eq nodeName}">
	           		<table class="formList">
                    	 <tr>
                            <td colspan="3" class="td_style1">開發人員確認</td>
                        </tr>
                        <tr align="center">
                        	<td width="20%">廠區<font color="red">*</font></td>
                        	<td width="50%">樓層<font color="red">*</font></td>
                        	<td width="30%">正式環境完成日期<font color="red">*</font></td>
                        </tr>
                        <tr align="center">
                        	<td>
                       		 	<input id="kfqrFactory" name="wfonlineprocess.kfqrFactory"  class="easyui-combobox"
                                    panelHeight="auto" value="${wfonlineprocessEntity.kfqrFactory}"
                                    data-options="width:120,required:true,validType:'comboxValidate[\'kfqrFactory\',\'請選擇廠區\']'"/>
								<input id="kfqrfactoryname" name="wfonlineprocess.kfqrfactoryname" type="hidden" value="${wfonlineprocessEntity.kfqrfactoryname }"/>
                        	</td>
                        	<td>
                        		<input type="radio" id="kfqrFloorAll" name="kfqrFloorRadio" <c:if test="${wfonlineprocessEntity.kfqrFloor == 'ALL'}">checked="checked"</c:if> onclick="$('#kfqrFloor').val('ALL').hide();"/>ALL
                       			<input type="radio" id="kfqrFloorOther" name="kfqrFloorRadio"  <c:if test="${not empty wfonlineprocessEntity.kfqrFloor && wfonlineprocessEntity.kfqrFloor != 'ALL'}">checked="checked"</c:if> onclick="$('#kfqrFloor').val('').show();"/>其它
                       			<input id="kfqrFloor" name="wfonlineprocess.kfqrFloor" style="<c:if test="${empty wfonlineprocessEntity.kfqrFloor || wfonlineprocessEntity.kfqrFloor == 'ALL'}">display: none;</c:if>" class="easyui-validatebox" data-options="width:150,required:true"
                                       value="${wfonlineprocessEntity.kfqrFloor}"/>
                        	</td>
                        	<td>
                       			<input id="kfqrCompleteDate" name="wfonlineprocess.kfqrCompleteDate" class="easyui-validatebox Wdate"
										data-options="width:120,required:true" style="width:120px"
										value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfonlineprocessEntity.kfqrCompleteDate}"/>"
										onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                        	</td>
                        </tr>
                    </table>
	           		</c:when>
	           		<c:otherwise>
	           			<c:if test="${not empty wfonlineprocessEntity.kfqrFactory}">
	           			<table class="formList">
	                    	 <tr>
	                            <td colspan="3" class="td_style1">開發人員確認</td>
	                        </tr>
	                        <tr align="center">
	                        	<td width="20%">廠區</td>
	                        	<td width="50%">樓層</td>
	                        	<td width="30%">正式環境完成日期</td>
	                        </tr>
	                        <tr align="center">
	                        	<td>
									<c:choose>
										<c:when test="${wfonlineprocessEntity.kfqrfactoryname!=''&& wfonlineprocessEntity.kfqrfactoryname!=null}">
											${wfonlineprocessEntity.kfqrfactoryname}
										</c:when>
										<c:otherwise>
											<input id="kfqrFactory"  class="easyui-combobox"
												   panelHeight="auto" value="${wfonlineprocessEntity.kfqrFactory}" disabled
												   data-options="width: 120"/>
										</c:otherwise>
									</c:choose>
	                        	</td>
	                        	<td>
	                        		<c:if test="${wfonlineprocessEntity.kfqrFloor != 'ALL'}">其它</c:if>
	                       		 	${wfonlineprocessEntity.kfqrFloor}
	                        	</td>
	                        	<td>
	                       		    <fmt:formatDate value='${wfonlineprocessEntity.kfqrCompleteDate}' pattern='yyyy-MM-dd'/>
	                        	</td>
	                        </tr>
	                    </table>
	           			</c:if>
	           		</c:otherwise>
		           </c:choose>

                     <table class="formList">
                     	   <!-- 資訊運維責任人作業 -->
		            	   <c:choose>
			           		<c:when test="${not empty nodeName && 'DBA作業人員' eq nodeName}">
			           			<tr align="center">
                            		<td width="10%">服務器資源評估說明<br/>（DBA填寫）<font color="red">*</font></td>

                            		<td class="td_style1">
		                        		<textarea id="dbapz"
												name="wfonlineprocess.dbapz" class="easyui-validatebox"
												oninput="return LessThanAuto(this,'txtNumDba');"
		                                      	onchange="return LessThanAuto(this,'txtNumDba');"
		                                      	onpropertychange="return LessThanAuto(this,'txtNumDba');"
		                                      	maxlength="200"
												data-options="multiline:true,required:true,validType:'length[1,200]'"
												style="width:99%;height:60px;resize:none;" rows="4" cols="4"></textarea>
										<span id="txtNumDba"></span>
		                        	</td>

                            	</tr>
			           		</c:when>
			           		<c:otherwise>
			           			<c:if test="${not empty wfonlineprocessEntity.dbapz}">
			           				<tr align="center">
	                            		<td width="10%">服務器資源評估說明<br/>（DBA填寫）</td>
	                            		<td align="left">
	                            			${wfonlineprocessEntity.dbapz}
	                            		</td>
	                            	</tr>
			           			</c:if>
			           		</c:otherwise>
				           </c:choose>

                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <input type="hidden" id="attachids" name="attachids" value="${wfonlineprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                        	<td>備註</td>
                        	<td align="left">表單簽核流程：<br/>表單申請——>會簽用戶——>BPM簽核——>系統運維確認——>系統開發確認——>DBA作業。</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%">
						<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
								<c:choose>
									<c:when test="${not empty nodeName && ('BPM簽核' eq nodeName||'系統開發工程師確認' eq nodeName ||'DBA作業人員' eq nodeName) }">
										<fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" perCall="audiPrValid" serialNo="${wfonlineprocessEntity.serialno}"></fox:action>
									</c:when>
									<c:otherwise>
										<fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" serialNo="${wfonlineprocessEntity.serialno}"></fox:action>
									</c:otherwise>
								</c:choose>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','功能程式發佈申請單流程');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfonlineprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
  <div id="dlg"></div>
 <input type="hidden" id="currentNodeOrder" value="${nodeOrder}" />
<script src='${ctx}/static/js/information/wfonlineprocess.min.js?random=20221007'></script>
</body>
</html>
