<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>SSL VPN服務申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfsslvpnprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfsslvpnprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfsslvpnprocessEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfsslvpnprocessEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfsslvpnprocessEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfsslvpnprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${wfsslvpnprocessEntity.makerfactoryid }"/>
    <input id="applynofactoryid" name="applynofactoryid" type="hidden"
           value="${wfsslvpnprocessEntity.applynofactoryid }"/>
    <div class="commonW">
        <div class="headTitle">SSL VPN服務申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfsslvpnprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfsslvpnprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfsslvpnprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfsslvpnprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfsslvpnprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfsslvpnprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfsslvpnprocessEntity.makerno}/${wfsslvpnprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="dealno" name="dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="<c:if test="${wfsslvpnprocessEntity.dealno!=null&&wfsslvpnprocessEntity.dealno!=''}">${wfsslvpnprocessEntity.dealno}</c:if>
                                       <c:if test="${wfsslvpnprocessEntity.dealno==null||wfsslvpnprocessEntity.dealno==''}">${user.loginName}</c:if>"
                                       onchange="queryUserInfo('deal');"/>
                            </td>
                            <td width="8%">承辦人</td>
                            <td width="10%" class="td_style1">
                                <input id="dealname" name="dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfsslvpnprocessEntity.dealname }"/>
                            </td>
                            <td width="10%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="dealdeptno" name="dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfsslvpnprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="10%">廠區&nbsp;</td>
                            <td width="10%" class="td_style1">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfsslvpnprocessEntity.dealfactoryid }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'dealfactoryid\',\'请選擇承辦人廠區\']',onSelect:function(){onchangeFactory('dealfactoryid');}"/>
                            </td>
                            <td width="8%">聯繫分機&nbsp;<font color="red">*</font></td>
                            <td width="16%" class="td_style1">
                                <input id="dealtel" name="dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfsslvpnprocessEntity.dealtel }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="dealdeptname" name="dealdeptname" class="easyui-validatebox"
                                       data-options="width: 410,required:true"
                                       value="${wfsslvpnprocessEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="dealemail" class="easyui-validatebox"
                                       value="${wfsslvpnprocessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>使用區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style1" colspan="3">
                                <input id="applyarea" name="applyarea" class="easyui-combobox"
                                       data-options="width: 150,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wfsslvpnprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="applybuilding" class="easyui-combobox"
                                       data-options="width: 150,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wfsslvpnprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                            <td>安保區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style2" colspan="2">
                                <div class="securityareaDiv"></div>
                                <input id="securityarea1" name="securityarea1"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfsslvpnprocessEntity.securityarea }"/>
                            </td>
                            <td>申請方式&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style2">
                                <div class="dealapplytypeDiv"></div>
                                <input id="dealapplytype1"
                                       name="dealapplytype1" type="hidden"
                                       class="easyui-validatebox" data-options="width: 100"
                                       value="${wfsslvpnprocessEntity.dealapplytype}"/>
                            </td>
                        </tr>
                    </table>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tbody id="info_Body">
                        <c:if test="${wfsslvpnprocessEntity.itemsEntitys!=null&&wfsslvpnprocessEntity.itemsEntitys.size()>0}">
                            <c:forEach items="${wfsslvpnprocessEntity.itemsEntitys}" var="itemsEntity"
                                       varStatus="status">
                                <tr align="center" bgcolor="#faebd7"><td colspan="8">第${status.index+1}位申請人信息</td></tr>
                                <tr align="center" id="itemsEntitys${status.index}">
                                    <td>申請人工號&nbsp;<font color="red">*</font></td>
                                    <td class="td_style1">
                                        <input id="applyno${status.index}" name="itemsEntitys[${status.index}].applyno" class="easyui-validatebox"
                                               data-options="width: 80,required:true" value="${itemsEntity.applyno}"
                                               onchange="getUserNameByEmpno('apply',${status.index});"/>
                                    </td>
                                    <td>申請人</td>
                                    <td class="td_style1">
                                        <input id="applyname${status.index}" name="itemsEntitys[${status.index}].applyname"
                                               class="easyui-validatebox inputCss" value="${itemsEntity.applyname}"
                                               data-options="width:80" readonly/>
                                    </td>
                                    <td>單位代碼</td>
                                    <td class="td_style1">
                                        <input id="applydeptno${status.index}" name="itemsEntitys[${status.index}].applydeptno"
                                               class="easyui-validatebox inputCss" data-options="width: 90"
                                               value="${itemsEntity.applydeptno}" readonly/>
                                    </td>
                                    <td>費用代碼&nbsp;<font color="red">*</font></td>
                                    <td class="td_style1">
                                        <input id="applycostno${status.index}" name="itemsEntitys[${status.index}].applycostno"
                                               class="easyui-validatebox" data-options="width: 90" value="${itemsEntity.applycostno}"
                                        />
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>資位</td>
                                    <td class="td_style1">
                                        <input id="applyleveltype${status.index}" name="itemsEntitys[${status.index}].applyleveltype" class="easyui-validatebox inputCss"
                                               data-options="width: 80" value="${itemsEntity.applyleveltype}"
                                               readonly/>
                                    </td>
                                    <td>管理職</td>
                                    <td class="td_style1">
                                        <input id="applymanager${status.index}" name="itemsEntitys[${status.index}].applymanager" class="easyui-validatebox inputCss"
                                               data-options="width: 80" value="${itemsEntity.applymanager}"
                                               readonly/>
                                    </td>
                                    <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                                    <td colspan="1" class="td_style1">
                                        <input id="applyemail${status.index}" name="itemsEntitys[${status.index}].applyemail" class="easyui-validatebox"
                                               style="width:300px;" value="${itemsEntity.applyemail}"
                                               data-options="required:true,validType:'email[\'applyemail${status.index}\',\'郵箱的格式不正確\']'"/>
                                    </td>
                                    <td>所在廠區&nbsp;<font color="red">*</font></td>
                                    <td class="td_style1">
                                        <input id="applyfactoryid${status.index}" name="itemsEntitys[${status.index}].applyfactoryid" class="easyui-combobox" disabled
                                               panelHeight="auto" value="${itemsEntity.applyfactoryid}"
                                               data-options="width: 120,required:true,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'applyfactoryid${status.index}\',\'请選擇申請人廠區\']',onBeforeLoad:function(){loadapplyfactory(${status.index});}"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>聯繫分機&nbsp;<font color="red">*</font></td>
                                    <td class="td_style1">
                                        <input id="applyphone${status.index}" name="itemsEntitys[${status.index}].applyphone" class="easyui-validatebox"
                                               style="width:90px;" value="${itemsEntity.applyphone}"
                                               data-options="required:true,prompt:'579+66666'"
                                               onblur="valdApplyTel(this)"/>
                                    </td>
                                    <td>單位&nbsp;<font color="red">*</font></td>
                                    <td colspan="5" class="td_style1">
                                        <input id="applydeptname${status.index}" name="itemsEntitys[${status.index}].applydeptname" class="easyui-validatebox"
                                               data-options="width: 410,required:true" value="${itemsEntity.applydeptname}"
                                        />
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>法人代碼</td>
                                    <td class="td_style1">
                                        <input id="applycompanycode${status.index}" name="itemsEntitys[${status.index}].applycompanycode"
                                               class="easyui-validatebox"
                                               data-options="width:80,required:true" value="${itemsEntity.applycompanycode}"/>
                                    </td>
                                    <td>法人名稱</td>
                                    <td class="td_style1" colspan="7">
                                        <input id="applycompanyname${status.index}" name="itemsEntitys[${status.index}].applycompanyname"
                                               class="easyui-validatebox"
                                               data-options="width:300,required:true" value="${itemsEntity.applycompanyname}"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td width="8%">用戶名&nbsp;<font color="red">*</font></td>
                                    <td width="10%" class="td_style1">
                                        <input id="vpnname${status.index}" name="itemsEntitys[${status.index}].vpnname" class="easyui-validatebox"
                                               data-options="width: 100,required:true" value="${itemsEntity.vpnname}"
                                        />
                                    </td>
                                    <td width="10%">申請人類型&nbsp;<font color="red">*</font></td>
                                    <td width="12%" class="td_style2">
                                        <input id="vpnapplytype${status.index}" name="itemsEntitys[${status.index}].vpnapplytype" class="easyui-combobox"
                                               panelHeight="auto" value="${itemsEntity.vpnapplytype}"
                                               data-options="width: 120,required:true,valueField:'value', textField:'label',validType:'comboxValidate[\'vpnapplytype${status.index}\',\'请選擇申請人類別\']',onBeforeLoad:function(){getvpnapplytype(${status.index});}"/>

                                    </td>
                                    <td width="10%">申請類型&nbsp;<font color="red">*</font></td>
                                    <td width="30%" class="td_style2">
                                        <div class="vpntypeDiv${status.index}" id="vpntypeId${status.index}"></div>
                                        <input id="vpntype${status.index}${status.index}" name="vpntype${status.index}${status.index}" type="hidden" class="easyui-validatebox"
                                               value="${itemsEntity.vpntype}" data-options="width: 100"/>
                                    </td>
                                    <td width="10%">操作系統&nbsp;<font color="red">*</font></td>
                                    <td width="10%" class="td_style2">
                                        <input id="vpnrequiretype${status.index}" name="itemsEntitys[${status.index}].vpnrequiretype" class="easyui-validatebox"
                                               value="${itemsEntity.vpnrequiretype}" data-options="width: 100,required:true"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>手機號碼&nbsp;<font color="red">*</font></td>
                                    <td class="td_style1">
                                        <input id="vpnhostip${status.index}" name="itemsEntitys[${status.index}].vpnhostip" class="easyui-validatebox"
                                               value="${itemsEntity.vpnhostip}" data-options="width: 100,required:true,prompt:'13166666666',validType:'phone[\'vpnhostip${status.index}\',\'手機號的格式不正確\']'"
                                        />
                                    </td>
                                    <td>使用期限&nbsp;<font color="red">*</font></td>
                                    <td class="td_style2">
                                        <input id="vpnexpire${status.index}" name="itemsEntitys[${status.index}].vpnexpire" class="easyui-combobox"
                                               panelHeight="auto" value="${itemsEntity.vpnexpire}"
                                               data-options="width: 120,required:true,valueField:'value', textField:'label',validType:'comboxValidate[\'vpnexpire${status.index}\',\'请選擇使用期限\']',onBeforeLoad:function(){getvpnexpire(${status.index});}"/>

                                    </td>
                                    <td>開始時間&nbsp;<font color="red">*</font></td>
                                    <td class="td_style1">
                                        <input id="vpnstarttime${status.index}" name="itemsEntitys[${status.index}].vpnstarttime" class="Wdate"
                                               data-options="width:100,required:true"
                                               style="width:150px" value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${itemsEntity.vpnstarttime}"/>"
                                               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"/>

                                    </td>
                                    <td>結束日期&nbsp;<font color="red">*</font></td>
                                    <td class="td_style1">
                                        <input id="vpnendtime${status.index}" name="itemsEntitys[${status.index}].vpnendtime" class="Wdate"
                                               data-options="width:100,required:true"
                                               style="width:150px" value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${itemsEntity.vpnendtime}"/>"
                                               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'vpnstarttime${status.index}\')}'})"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>需求說明&nbsp;<font color="red">*</font></td>
                                    </td>
                                    <td colspan="7" class="td_style1">
                                <textarea id="describtion${status.index}" name="itemsEntitys[${status.index}].describtion"
                                          oninput="return LessThanAuto(this,'txtNum${status.index}');"
                                          onchange="return LessThanAuto(this,'txtNum${status.index}');"
                                          onpropertychange="return LessThanAuto(this,'txtNum${status.index}');"
                                          maxlength="60" class="easyui-validatebox" style="width:85%;height:80px;"
                                          data-options="required:true"
                                          rows="5" cols="6">${itemsEntity.describtion}</textarea><span
                                            id="txtNum${status.index}"></span></td>
                                </tr>
                            </c:forEach>
                        </c:if>
                        <c:if test="${wfsslvpnprocessEntity.itemsEntitys.size()==0 || wfsslvpnprocessEntity.itemsEntitys==null}">
                            <tr align="center" bgcolor="#faebd7"><td colspan="8">第1位申請人信息</td></tr>
                            <tr align="center" id="itemsEntitys0">
                                <td>申請人工號&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input id="applyno0" name="itemsEntitys[0].applyno" class="easyui-validatebox"
                                           data-options="width: 80,required:true"
                                           onchange="getUserNameByEmpno('apply',0);"/>
                                </td>
                                <td>申請人</td>
                                <td class="td_style1">
                                    <input id="applyname0" name="itemsEntitys[0].applyname"
                                           class="easyui-validatebox inputCss"
                                           data-options="width:80" readonly/>
                                </td>
                                <td>單位代碼</td>
                                <td class="td_style1">
                                    <input id="applydeptno0" name="itemsEntitys[0].applydeptno"
                                           class="easyui-validatebox inputCss" data-options="width: 90"
                                           readonly/>
                                </td>
                                <td>費用代碼&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input id="applycostno0" name="itemsEntitys[0].applycostno"
                                           class="easyui-validatebox" data-options="width: 90"
                                    />
                                </td>
                            </tr>
                            <tr align="center">
                                <td>資位</td>
                                <td class="td_style1">
                                    <input id="applyleveltype0" name="itemsEntitys[0].applyleveltype" class="easyui-validatebox inputCss"
                                           data-options="width: 80"
                                           readonly/>
                                </td>
                                <td>管理職</td>
                                <td class="td_style1">
                                    <input id="applymanager0" name="itemsEntitys[0].applymanager" class="easyui-validatebox inputCss"
                                           data-options="width: 80"
                                           readonly/>
                                </td>
                                <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                                <td colspan="1" class="td_style1">
                                    <input id="applyemail0" name="itemsEntitys[0].applyemail" class="easyui-validatebox"
                                           style="width:300px;"
                                           data-options="required:true,validType:'email[\'applyemail0\',\'郵箱的格式不正確\']'"/>
                                </td>
                                <td>所在廠區&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input id="applyfactoryid0" name="itemsEntitys[0].applyfactoryid" class="easyui-combobox"
                                           panelHeight="auto" disabled
                                           data-options="width: 120,required:true,valueField:'factoryid', textField:'factoryname',validType:'comboxValidate[\'applyfactoryid0\',\'请選擇申請人廠區\']',onBeforeLoad:function(){loadapplyfactory(0);}"/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>聯繫分機&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input id="applyphone0" name="itemsEntitys[0].applyphone" class="easyui-validatebox"
                                           style="width:90px;"
                                           data-options="required:true,prompt:'579+66666'"
                                           onblur="valdApplyTel(this)"/>
                                </td>
                                <td>單位&nbsp;<font color="red">*</font></td>
                                <td colspan="5" class="td_style1">
                                    <input id="applydeptname0" name="itemsEntitys[0].applydeptname" class="easyui-validatebox"
                                           data-options="width: 410,required:true"
                                    />
                                </td>
                            </tr>
                            <tr align="center">
                                <td>法人代碼</td>
                                <td class="td_style1">
                                    <input id="applycompanycode0" name="itemsEntitys[0].applycompanycode"
                                           class="easyui-validatebox" data-options="width:80,required:true"/>
                                </td>
                                <td>法人名稱</td>
                                <td class="td_style1" colspan="7">
                                    <input id="applycompanyname0" name="itemsEntitys[0].applycompanyname"
                                           class="easyui-validatebox" data-options="width:300,required:true"/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td width="8%">用戶名&nbsp;<font color="red">*</font></td>
                                <td width="10%" class="td_style1">
                                    <input id="vpnname0" name="itemsEntitys[0].vpnname" class="easyui-validatebox"
                                           data-options="width: 100,required:true"
                                    />
                                </td>
                                <td width="10%">申請人類型&nbsp;<font color="red">*</font></td>
                                <td width="12%" class="td_style2">
                                    <input id="vpnapplytype0" name="itemsEntitys[0].vpnapplytype" class="easyui-combobox"
                                           panelHeight="auto"
                                           data-options="width: 120,required:true,valueField:'value', textField:'label',validType:'comboxValidate[\'vpnapplytype0\',\'请選擇申請人類別\']',onBeforeLoad:function(){getvpnapplytype(0);}"/>

                                </td>
                                <td width="10%">申請類型&nbsp;<font color="red">*</font></td>
                                <td width="30%" class="td_style2">
                                    <div class="vpntypeDiv"></div>
                                    <input id="vpntype00" name="vpntype00" type="hidden" class="easyui-validatebox"
                                           data-options="width: 100"/>
                                </td>
                                <td width="10%">操作系統&nbsp;<font color="red">*</font></td>
                                <td width="10%" class="td_style2">
                                    <input id="vpnrequiretype0" name="itemsEntitys[0].vpnrequiretype" class="easyui-validatebox"
                                           data-options="width: 100,required:true"/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>手機號碼&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input id="vpnhostip0" name="itemsEntitys[0].vpnhostip" class="easyui-validatebox"
                                           data-options="width: 100,required:true,prompt:'13166666666',validType:'phone[\'vpnhostip0\',\'手機號的格式不正確\']'"
                                    />
                                </td>
                                <td>使用期限&nbsp;<font color="red">*</font></td>
                                <td class="td_style2">
                                    <input id="vpnexpire0" name="itemsEntitys[0].vpnexpire" class="easyui-combobox"
                                           panelHeight="auto"
                                           data-options="width: 120,required:true,valueField:'value', textField:'label',validType:'comboxValidate[\'vpnexpire0\',\'请選擇使用期限\']',onBeforeLoad:function(){getvpnexpire(0);}"/>

                                </td>
                                <td>開始時間&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input id="vpnstarttime0" name="itemsEntitys[0].vpnstarttime" class="Wdate"
                                           data-options="width:100,required:true"
                                           style="width:150px"
                                           onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"/>

                                </td>
                                <td>結束日期&nbsp;<font color="red">*</font></td>
                                <td class="td_style1">
                                    <input id="vpnendtime0" name="itemsEntitys[0].vpnendtime" class="Wdate"
                                           data-options="width:100,required:true"
                                           style="width:150px"
                                           onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'vpnstarttime0\')}'})"/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>需求說明&nbsp;<font color="red">*</font></td>
                                </td>
                                <td colspan="7" class="td_style1">
                                <textarea id="describtion0" name="itemsEntitys[0].describtion"
                                          oninput="return LessThanAuto(this,'txtNum0');"
                                          onchange="return LessThanAuto(this,'txtNum0');"
                                          onpropertychange="return LessThanAuto(this,'txtNum0');"
                                          maxlength="60" class="easyui-validatebox" style="width:85%;height:80px;"
                                          data-options="required:true"
                                          rows="5" cols="6"></textarea><span
                                        id="txtNum0"></span></td>
                            </tr>
                        </c:if>
                        </tbody>
                        <tr align="center">
                            <td width="10%"><a href="${ctx}/wfsslvpnprocess/downLoad/batchImportTpl"
                                               id="btnBatchImportTpl">批量申請模板下載</a></td>
                            <td colspan="7" class="td_style1">&nbsp;&nbsp;&nbsp;&nbsp;<a
                                    href="#" id="batchImport" class="easyui-linkbutton"
                                    data-options="iconCls:'icon-hamburg-cv'" style="width: 100px;"
                                    onclick="openBatchImportWin();">批量導入</a>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件&nbsp;<font color="red">*</font></td>
                            <td colspan="7" class="td_style2">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file"
                                           onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="attachids"
                                       value="${wfsslvpnprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="7" class="td_style2">
                                <font color="red">1.“申請人類別”選擇系統管理員時，需說明服務器是何類型及用途；</font><br>
                                2.因SSL VPN用戶的不正常使用會使公司在智慧財產或其它機密信息方面蒙受損失，惠請各階主管務必嚴格審核。服務器管理人員應制定管理辦法，防止機密資訊由所開通服務外泄；<br>
                                3.如離職或調動導致費用代碼變更，請及時通知2進行賬號刪除，持令牌者需將令牌返還；<br>
                                4.SSL VPN賬號一次申請使用期限最長為6個月，如需繼續使用請在賬號到期前提交SSL
                                VPN申請單續用，以免影響您的使用；<br>
                                5.賬號及密碼通知方式為手機短信通知，請務必提供有效的手機號碼；<br>
                                6.登錄SSL VPN賬號無法觀看香信視頻；<br>
                                7.SSL VPN服務按自然月計費，資費為CNY200元/月，不足一個月的部分按一個月計費；<br>
                                8.申請權限之電腦進行資安管控，并簽核<a
                                    href="${ctx}/wfspecialnetprocess/downLoad/InformationSecurityReview">外網資安點檢表</a>；
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','SSL VPN服務申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="5" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl5Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno5_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'yl5Table','ylno5','ylname5',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno5" name="ylno5" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno5']}"
                                                               readonly value="${wfsslvpnprocessEntity.ylno5 }"/><c:if
                                                            test="${requiredMap['ylno5'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname5" name="ylname5" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno5']}"
                                                                value="${wfsslvpnprocessEntity.ylname5 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#dealdeptno').val(),'cchargeno','cchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfsslvpnprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfsslvpnprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="czchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['czchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('czchargeTable',$('#dealdeptno').val(),'czchargeno','czchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="czchargeno" name="czchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['czchargeno']}"
                                                               readonly
                                                               value="${wfsslvpnprocessEntity.czchargeno }"/><c:if
                                                            test="${requiredMap['czchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="czchargename" name="czchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['czchargeno']}"
                                                                value="${wfsslvpnprocessEntity.czchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole33('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfsslvpnprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="zchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfsslvpnprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="20%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wfsslvpnprocessEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="zcchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfsslvpnprocessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno2_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(62,'yl2Table','ylno2','ylname2',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="ylno2" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}"
                                                               readonly value="${wfsslvpnprocessEntity.ylno2 }"/><c:if
                                                            test="${requiredMap['ylno2'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname2" name="ylname2" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}"
                                                                value="${wfsslvpnprocessEntity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl6Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno6_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(25,'yl6Table','ylno6','ylname6',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno6" name="ylno6" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno6']}"
                                                               readonly value="${wfsslvpnprocessEntity.ylno6 }"/><c:if
                                                            test="${requiredMap['ylno6'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname6" name="ylname6" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno6']}"
                                                                value="${wfsslvpnprocessEntity.ylname6 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl14Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno14_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(154,'yl14Table','ylno14','ylname14',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno14" name="ylno14" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno14']}"
                                                               readonly value="${wfsslvpnprocessEntity.ylno14 }"/><c:if
                                                            test="${requiredMap['ylno14'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname14" name="ylname14" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno14']}"
                                                                value="${wfsslvpnprocessEntity.ylname14 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl3Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno3_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(155,'yl3Table','ylno3','ylname3',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno3" name="ylno3" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno3']}"
                                                               readonly value="${wfsslvpnprocessEntity.ylno3 }"/><c:if
                                                            test="${requiredMap['ylno3'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname3" name="ylname3" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno3']}"
                                                                value="${wfsslvpnprocessEntity.ylname3 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="20%" style="float: left;margin-left: 5px;"
                                                   id="zacwchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zacwchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(193,'zacwchargeTable','zacwchargeno','zacwchargename',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zacwchargeno" name="zacwchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zacwchargeno']}"
                                                               readonly
                                                               value="${wfsslvpnprocessEntity.zacwchargeno }"/>
                                                        <c:if test="${requiredMap['zacwchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        /<input id="zacwchargename" name="zacwchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zacwchargeno']}"
                                                                value="${wfsslvpnprocessEntity.zacwchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <%--<table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#dealdeptno').val(),'pcchargeno','pcchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${wfsslvpnprocessEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="pcchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wfsslvpnprocessEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>--%>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl4Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno4_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(188,'yl4Table','ylno4','ylname4',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno4" name="ylno4" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno4']}"
                                                               readonly value="${wfsslvpnprocessEntity.ylno4 }"/>
                                                        <c:if test="${requiredMap['ylno4'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        <%--<span id="ylno4RedDiv"><font color="red">*</font></span>--%>
                                                        /<input id="ylname4" name="ylname4" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno4']}"
                                                                value="${wfsslvpnprocessEntity.ylname4 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl7Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno7_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(187,'yl7Table','ylno7','ylname7',$('#dealfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno7" name="ylno7" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno7']}"
                                                               readonly value="${wfsslvpnprocessEntity.ylno7 }"/>
                                                        <%--<span id="ylno7RedDiv"><font color="red">*</font></span>--%>
                                                        <c:if test="${requiredMap['ylno7'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        /<input id="ylname7" name="ylname7" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno7']}"
                                                                value="${wfsslvpnprocessEntity.ylname7 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="sychargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['sychargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('sychargeTable',$('#dealdeptno').val(),'sychargeno','sychargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="sychargeno" name="sychargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['sychargeno']}"
                                                               readonly
                                                               value="${wfsslvpnprocessEntity.sychargeno }"/>
                                                        <c:if test="${requiredMap['sychargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        <%--<span id="sychargeRedDiv"><font color="red">*</font></span>--%>
                                                        /<input id="sychargename" name="sychargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['sychargeno']}"
                                                                value="${wfsslvpnprocessEntity.sychargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfsslvpnprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10"><font color="red">溫馨提示：如果您在填單過程中有任何疑問，請聯繫本廠區資訊人員：</font><a href="${ctx}/wfvlanprocess/downLoadcontact">各廠區資訊聯繫方式</a></td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <input type="checkbox" id="agree" name="agree"/><a href="${ctx}/requisitionlist/downLoad/commitmentTpl" plain="true" id="btnCommitmentTpl">本人已閱讀并同意服務條款</a>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;"
                                   onclick="canelTask('${wfsslvpnprocessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="areaId" name="areaId" value=""/>
    <input type="hidden" id="buildingId" name="buildingId" value=""/>
    <input type="hidden" id="onlyKchargeSignle" value="1"/>
    <div id="win"></div>
    <div id="dlg"></div>
</form>
</div>
<script src='${ctx}/static/js/information/wfsslvpnprocess.min.js?random=2025052802'></script>
</body>
</html>
