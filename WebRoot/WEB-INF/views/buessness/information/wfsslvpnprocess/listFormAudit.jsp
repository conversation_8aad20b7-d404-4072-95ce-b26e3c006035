<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>SSL VPN服務申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfsslvpnprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfsslvpnprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfsslvpnprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">SSL VPN服務申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfsslvpnprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfsslvpnprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfsslvpnprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfsslvpnprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfsslvpnprocessEntity.makerno}/${wfsslvpnprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">承辦人工號</td>
                            <td width="10%" class="td_style1">
                                <input id="dealno" name="dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true,disabled:true"
                                       value="<c:if test="${wfsslvpnprocessEntity.dealno!=null&&wfsslvpnprocessEntity.dealno!=''}">${wfsslvpnprocessEntity.dealno}</c:if>
                                       <c:if test="${wfsslvpnprocessEntity.dealno==null||wfsslvpnprocessEntity.dealno==''}">${user.loginName}</c:if>"
                                       onchange="queryUserInfo('deal');"/>
                            </td>
                            <td width="8%">承辦人</td>
                            <td width="10%" class="td_style1">
                                <input id="dealname" name="dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfsslvpnprocessEntity.dealname }"/>
                            </td>
                            <td width="10%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="dealdeptno" name="dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfsslvpnprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="10%">廠區</td>
                            <td width="10%" class="td_style1">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfsslvpnprocessEntity.dealfactoryid }"
                                       data-options="width: 120,disabled:true,required:true,validType:'comboxValidate[\'dealfactoryid\',\'请選擇承辦人廠區\']',onSelect:function(){onchangeFactory('dealfactoryid');}"/>
                            </td>
                            <td width="8%">聯繫分機</td>
                            <td width="16%" class="td_style1">
                                <input id="dealtel" name="dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfsslvpnprocessEntity.dealtel }"
                                       data-options="required:true,disabled:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="5" class="td_style1">
                                <input id="dealdeptname" name="dealdeptname" class="easyui-validatebox"
                                       data-options="width: 410,required:true,disabled:true"
                                       value="${wfsslvpnprocessEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="dealemail" class="easyui-validatebox"
                                       value="${wfsslvpnprocessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,disabled:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>使用區域</td>
                            <td class="td_style1" colspan="3">
                                <input id="applyarea" name="applyarea" class="easyui-combobox"
                                       data-options="width: 150,disabled:true,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wfsslvpnprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="applybuilding" class="easyui-combobox"
                                       data-options="width: 150,disabled:true,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wfsslvpnprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                            <td>安保區域</td>
                            <td class="td_style2" colspan="2">
                                <div class="securityareaDiv"></div>
                                <input id="securityarea1" name="securityarea1"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfsslvpnprocessEntity.securityarea }"/>
                            </td>
                            <td>申請方式</td>
                            <td colspan="2" class="td_style2">
                                <div class="dealapplytypeDiv"></div>
                                <input id="dealapplytype1"
                                       name="dealapplytype1" type="hidden"
                                       class="easyui-validatebox" data-options="width: 100"
                                       value="${wfsslvpnprocessEntity.dealapplytype}"/>
                            </td>
                        </tr>
                    </table>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tbody id="info_Body">
                        <c:if test="${wfsslvpnprocessEntity.itemsEntitys!=null&&wfsslvpnprocessEntity.itemsEntitys.size()>0}">
                            <c:forEach items="${wfsslvpnprocessEntity.itemsEntitys}" var="itemsEntity"
                                       varStatus="status">
                                <tr align="center" bgcolor="#faebd7"><td colspan="8">第${status.index+1}位申請人信息</td></tr>
                                <tr align="center" id="itemsEntitys${status.index}">
                                    <td>申請人工號</td>
                                    <td class="td_style1">
                                        <input id="applyno${status.index}" name="itemsEntitys[${status.index}].applyno" class="easyui-validatebox"
                                               data-options="width: 80,disabled:true,required:true" value="${itemsEntity.applyno}"
                                               onchange="getUserNameByEmpno('apply',${status.index});"/>
                                    </td>
                                    <td>申請人</td>
                                    <td class="td_style1">
                                        <input id="applyname${status.index}" name="itemsEntitys[${status.index}].applyname"
                                               class="easyui-validatebox inputCss" value="${itemsEntity.applyname}"
                                               data-options="width:80" readonly/>
                                    </td>
                                    <td>單位代碼</td>
                                    <td class="td_style1">
                                        <input id="applydeptno${status.index}" name="itemsEntitys[${status.index}].applydeptno"
                                               class="easyui-validatebox inputCss" data-options="width: 90"
                                               value="${itemsEntity.applydeptno}" readonly/>
                                    </td>
                                    <td>費用代碼</td>
                                    <td class="td_style1">
                                        <input id="applycostno${status.index}" name="itemsEntitys[${status.index}].applycostno"
                                               class="easyui-validatebox" data-options="width: 90,disabled:true" value="${itemsEntity.applycostno}"
                                        />
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>資位</td>
                                    <td class="td_style1">
                                        <input id="applyleveltype${status.index}" name="itemsEntitys[${status.index}].applyleveltype" class="easyui-validatebox inputCss"
                                               data-options="width: 80" value="${itemsEntity.applyleveltype}"
                                               readonly/>
                                    </td>
                                    <td>管理職</td>
                                    <td class="td_style1">
                                        <input id="applymanager${status.index}" name="itemsEntitys[${status.index}].applymanager" class="easyui-validatebox inputCss"
                                               data-options="width: 80" value="${itemsEntity.applymanager}"
                                               readonly/>
                                    </td>
                                    <td>聯繫郵箱</td>
                                    <td colspan="1" class="td_style1">
                                        <input id="applyemail${status.index}" name="itemsEntitys[${status.index}].applyemail" class="easyui-validatebox"
                                               style="width:300px;" value="${itemsEntity.applyemail}"
                                               data-options="required:true,disabled:true,validType:'email[\'applyemail${status.index}\',\'郵箱的格式不正確\']'"/>
                                    </td>
                                    <td>所在廠區</td>
                                    <td class="td_style1">
                                        <input id="applyfactoryid${status.index}" name="itemsEntitys[${status.index}].applyfactoryid" class="easyui-combobox" disabled
                                               panelHeight="auto" value="${itemsEntity.applyfactoryid}"
                                               data-options="width: 120,required:true,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'applyfactoryid${status.index}\',\'请選擇申請人廠區\']',onBeforeLoad:function(){loadapplyfactory(${status.index});}"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>聯繫分機</td>
                                    <td class="td_style1">
                                        <input id="applyphone${status.index}" name="itemsEntitys[${status.index}].applyphone" class="easyui-validatebox"
                                               style="width:90px;" value="${itemsEntity.applyphone}"
                                               data-options="required:true,prompt:'579+66666',disabled:true"
                                               onblur="valdApplyTel(this)"/>
                                    </td>
                                    <td>單位</td>
                                    <td colspan="5" class="td_style1">
                                        <input id="applydeptname${status.index}" name="itemsEntitys[${status.index}].applydeptname" class="easyui-validatebox"
                                               data-options="width: 410,required:true,disabled:true" value="${itemsEntity.applydeptname}"
                                        />
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>法人代碼</td>
                                    <td class="td_style1">
                                        <input id="applycompanycode${status.index}" name="itemsEntitys[${status.index}].applycompanycode"
                                               class="easyui-validatebox inputCss" data-options="width:80" readonly value="${itemsEntity.applycompanycode}"/>
                                    </td>
                                    <td>法人名稱</td>
                                    <td class="td_style1" colspan="7">
                                        <input id="applycompanyname${status.index}" name="itemsEntitys[${status.index}].applycompanyname"
                                               class="easyui-validatebox inputCss" data-options="width:300" readonly value="${itemsEntity.applycompanyname}"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td width="8%">用戶名</td>
                                    <td width="10%" class="td_style1">
                                        <input id="vpnname${status.index}" name="itemsEntitys[${status.index}].vpnname" class="easyui-validatebox"
                                               data-options="width: 100,required:true,disabled:true" value="${itemsEntity.vpnname}"
                                        />
                                    </td>
                                    <td width="10%">申請人類型</td>
                                    <td width="12%" class="td_style2">
                                        <input id="vpnapplytype${status.index}" name="itemsEntitys[${status.index}].vpnapplytype" class="easyui-combobox"
                                               panelHeight="auto" value="${itemsEntity.vpnapplytype}"
                                               data-options="width: 120,disabled:true,required:true,valueField:'value', textField:'label',validType:'comboxValidate[\'vpnapplytype${status.index}\',\'请選擇申請人類別\']',onBeforeLoad:function(){getvpnapplytype(${status.index});}"/>

                                    </td>
                                    <td width="10%">申請類型</td>
                                    <td width="30%" class="td_style2">
                                        <div class="vpntypeDiv${status.index}" id="vpntypeId${status.index}"></div>
                                        <input id="vpntype${status.index}${status.index}" name="vpntype${status.index}${status.index}" type="hidden" class="easyui-validatebox"
                                               value="${itemsEntity.vpntype}" data-options="width: 100"/>
                                    </td>
                                    <td width="10%">操作系統</td>
                                    <td width="10%" class="td_style2">
                                        <input id="vpnrequiretype${status.index}" name="itemsEntitys[${status.index}].vpnrequiretype" class="easyui-validatebox"
                                               value="${itemsEntity.vpnrequiretype}" data-options="width: 100,disabled:true,required:true"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>手機號碼</td>
                                    <td class="td_style1">
                                        <input id="vpnhostip${status.index}" name="itemsEntitys[${status.index}].vpnhostip" class="easyui-validatebox"
                                               value="${itemsEntity.vpnhostip}" data-options="width: 100,disabled:true,required:true,prompt:'13166666666',validType:'phone[\'vpnhostip${status.index}\',\'手機號的格式不正確\']'"
                                        />
                                    </td>
                                    <td>使用期限</td>
                                    <td class="td_style2">
                                        <input id="vpnexpire${status.index}" name="itemsEntitys[${status.index}].vpnexpire" class="easyui-combobox"
                                               panelHeight="auto" value="${itemsEntity.vpnexpire}"
                                               data-options="width: 120,disabled:true,required:true,valueField:'value', textField:'label',validType:'comboxValidate[\'vpnexpire${status.index}\',\'请選擇使用期限\']',onBeforeLoad:function(){getvpnexpire(${status.index});}"/>

                                    </td>
                                    <td>開始時間</td>
                                    <td class="td_style1">
                                        <input id="vpnstarttime${status.index}" name="itemsEntitys[${status.index}].vpnstarttime" class="Wdate"
                                               data-options="width:100,required:true,disabled:true" disabled
                                               style="width:150px" value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${itemsEntity.vpnstarttime}"/>"
                                               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"/>

                                    </td>
                                    <td>結束日期</td>
                                    <td class="td_style1">
                                        <input id="vpnendtime${status.index}" name="itemsEntitys[${status.index}].vpnendtime" class="Wdate"
                                               data-options="width:100,required:true,disabled:true" disabled
                                               style="width:150px" value="<fmt:formatDate  pattern="yyyy-MM-dd"
									value="${itemsEntity.vpnendtime}"/>"
                                               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'vpnstarttime${status.index}\')}'})"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>需求說明</td>
                                    </td>
                                    <td colspan="7" class="td_style1">
                                <textarea id="describtion${status.index}" name="itemsEntitys[${status.index}].describtion"
                                          oninput="return LessThanAuto(this,'txtNum${status.index}');"
                                          onchange="return LessThanAuto(this,'txtNum${status.index}');"
                                          onpropertychange="return LessThanAuto(this,'txtNum${status.index}');"
                                          maxlength="60" class="easyui-validatebox" style="width:85%;height:80px;"
                                          data-options="required:true,disabled:true"
                                          rows="5" cols="6">${itemsEntity.describtion}</textarea><span
                                            id="txtNum${status.index}"></span></td>
                                </tr>
                            </c:forEach>
                        </c:if>
                        </tbody>
                        <tr align="center">
                            <td width="10%"><a href="${ctx}/wfsslvpnprocess/downLoad/batchImportTpl"
                                               id="btnBatchImportTpl">批量申請模板下載</a></td>
                            <td colspan="7" class="td_style1">&nbsp;&nbsp;&nbsp;&nbsp;<a
                                    href="#" id="batchImport" class="easyui-linkbutton"
                                    data-options="iconCls:'icon-hamburg-cv'" style="width: 100px;"
                                    onclick="openBatchImportWin();">批量導入</a>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="7" class="td_style2">
                                <input type="hidden" id="attachids" name="attachids" value="${wfsslvpnprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>批註</td>
                            <td colspan="7" class="td_style2">
						        <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                          style="width:1000px;height:60px;" rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wfsslvpnprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','SSL VPN服務申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfsslvpnprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<div id="dlg"></div>
<script src='${ctx}/static/js/information/wfsslvpnprocess.min.js?random=2025052802'></script>
</body>
</html>