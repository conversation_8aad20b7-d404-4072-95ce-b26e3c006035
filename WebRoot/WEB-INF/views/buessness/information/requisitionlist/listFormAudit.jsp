<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>領用單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/requisitionlist/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${requisitionListEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${requisitionListEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">領用單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${requisitionListEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${requisitionListEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${requisitionListEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" disabled="true"
                                   value="<fmt:formatDate value='${requisitionListEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${requisitionListEntity.makerno}/${requisitionListEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="4%">申請人工號</td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true,disabled:true"
                                       value="${requisitionListEntity.applyno }" onblur="queryUserInfo(this);"
                                       />
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80,disabled:true" disabled value="${requisitionListEntity.applyname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90,disabled:true"
                                       value="${requisitionListEntity.applydeptno }"/>
                            </td>
                            <td width="3%">費用代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applycostno" name="applycostno" class="easyui-validatebox"
                                       data-options="width: 80,disabled:true"
                                       value="${requisitionListEntity.applycostno }"/>
                            </td>
                            <td width="4%">所在廠區</td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${requisitionListEntity.applyfactoryid }" disabled
                                       data-options="width: 120,required:true,onSelect:function(){onchangeFactory();}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="applyleveltype" class="easyui-validatebox"
                                       data-options="width: 80,disabled:true"
                                       value="${requisitionListEntity.applyleveltype }"/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="applymanager" class="easyui-validatebox"
                                       data-options="width: 80,disabled:true"
                                       value="${requisitionListEntity.applymanager }"/>
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       value="${requisitionListEntity.applyemail }" style="width:90%;"
                                       data-options="required:true,disabled:true" onblur="valdEmail('')"/>
                            </td>
                            <td>使用區域</td>
                            <td class="td_style1">
                                <input id="applyarea" name="applyarea" class="easyui-combobox"
                                       data-options="width: 70,onSelect:function(){onchangeArea();}"
                                       value="${requisitionListEntity.applyarea }" panelHeight="auto" disabled/>&nbsp;/
                                <input id="applybuilding" name="applybuilding" class="easyui-combobox"
                                       data-options="width: 70"
                                       value="${requisitionListEntity.applybuilding }" panelHeight="auto" disabled/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機</td>
                            <td class="td_style1">
                                <input id="applytel" name="applytel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${requisitionListEntity.applytel }"
                                       data-options="required:true,prompt:'579+66666',disabled:true"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>單位</td>
                            <td colspan="4" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox"
                                       data-options="disabled:true" style="width:90%;"
                                       value="${requisitionListEntity.applydeptname }"/>
                            </td>
                            <td>安保區域</td>
                            <td colspan="2" class="td_style2">
                                <div class="securityareaDiv"></div>
                                <input id="securityarea" name="securityarea"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${requisitionListEntity.securityarea }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>法人代碼</td>
                            <td align="left">
                                ${requisitionListEntity.applycompanycode}
                            </td>
                            <td>法人名稱</td>
                            <td align="left" colspan="7">
                                ${requisitionListEntity.applycompanyname}
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10">
                                <table class="formList" id="detailTable">
                                    <tr align="center">
                                        <td colspan="2" style="width: 25%">需求類型</td>
                                        <td colspan="1" style="width: 10%">數量</td>
                                        <td colspan="7">領料原因</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;"
                                             class="float_L">
                                            <div class="float_L"><a
                                                    href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="9" class="td_style1">
                                1、此單據用於申請向中央網通領取話機、水晶頭</br>
                                <font color="red">2、用戶單位需核准至副理及以上主管</font></br>
                                3、SIP话机 X3S：外置电源 X3S-P：POE供电
                            </td>
                        </tr>
                        <tr align="center">
                            <td>批註</td>
                            <td colspan="9" class="td_style1">
                                <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                          style="width:80%;height:60px;" rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${requisitionListEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','領用單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${requisitionListEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input type="hidden" id="disOrEnabled" value="disabled"/>
<script src='${ctx}/static/js/information/requisitionlist.min.js?random=20231118'></script>
</body>
</html>