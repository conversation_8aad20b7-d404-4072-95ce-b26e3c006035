<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>NPI區域郵箱管控需求申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input type="text" name="filter_EQS_dealno" class="easyui-validatebox"
               data-options="width:150,prompt: '承辦人工號'"/>
        <input type="text" name="filter_EQS_applyno" class="easyui-validatebox"
               data-options="width:150,prompt: '郵箱負責人工號'"/>
        <input type="text" name="filter_EQS_serialno" class="easyui-validatebox"
               data-options="width:150,prompt: '任務編碼'"/>
        <input id="qysjcq" style="width:150px" class="easyui-validatebox" name="filter_EQS_dealfactoryid"/>
        <input id="qysjzt" style="width:100px" class="easyui-validatebox" name="filter_EQS_workstatus"/>
        <input type="text" name="filter_GED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd"
               data-options="width:150,prompt: '填單开始日期'"/>
        - <input type="text" name="filter_LED_createDate" class="easyui-my97" datefmt="yyyy-MM-dd"
                 data-options="width:150,prompt: '填單结束日期'"/>
        <input type="text" name="filter_GED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"
               data-options="width:150,prompt: '簽核完成开始日期'"/>
        - <input type="text" name="filter_LED_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"
                 data-options="width:150,prompt: '簽核完成结束日期'"/>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
           onclick="listSearchReset()">重置</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
           onclick="exportExcel()">导出Excel</a>
        <!--導出用，請勿刪除-->
        <input id="page" name="page" type="hidden" value="1"/>
        <input id="rows" name="rows" type="hidden" value="30"/>
    </form>

</div>
<table id="dg"></table>
<div id="dlg"></div>

<script type="text/javascript">
    //創建下拉查詢條件
    $.ajax({
        url: ctx + "/system/dict/getDictByType/audit_status",
        dataType: "json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#qysjzt").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });

    //獲取廠區
    $.get(ctx+'/tqhfactoryidconfig/allFactorys/',
        function (result) {
            $("#qysjcq").combobox({
                data: result,
                valueField: "factoryid",
                textField: "factoryname",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({factoryid: '', factoryname: '請選擇承辦人所在廠區'});
                    return data;
                }
            });
        },'json');
    $(function () {
        dg = $('#dg').datagrid({
            method: "get",
            url: ctx + '/wfnpisupercontrolprocess/list',
            fit: true,
            fitColumns: false,
            //fitColumns: false,  //如需要底部出現滾動條，修改爲false
            border: false,
            idField: 'id',
            striped: true,
            cache: false,  //关闭AJAX相应的缓存
            pagination: true,
            rownumbers: true,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: true,
            columns: [[
                {field: 'id', title: '主鍵', hidden: true},
                {field: 'serialno', title: '任務編碼', sortable: true, width: 150, formatter: operation},
                {field: 'dealno', title: '承辦人工號', sortable: true, width: 100},
                {field: 'dealname', title: '承辦人姓名',sortable:true,width:100},
                {field: 'dealfactoryid', title: '廠區',sortable:true,width:100},
                {field: 'dealdeptno', title: '單位代碼', sortable: true, width: 100},
                {field: 'dealdeptname', title: '單位名稱', sortable: true, width: 100, formatter: cellTextTip},
                {field: 'dealcostno', title: '費用代碼',sortable:true,width:100},
                {field: 'applyno', title: '郵箱負責人工號', sortable: true, width: 100},
                {field: 'applyname', title: '負責人', sortable: true, width: 100},
                {field: 'createtime', title: '填單日期', sortable: true, width: 100},
                {field: 'workstatus', title: '任務狀態', sortable: true, width: 100},
                {field: 'nodeName', title: '當前審核節點', sortable: true, width: 150, formatter: formatProgress},
                {field: 'auditUser', title: '當前簽核人', sortable: true, width: 120},
                {field: 'complettime', title: '簽核完成日期', sortable: true, width: 100}
            ]],
            onLoadSuccess: function () {
                $(".easyui-tooltip").tooltip({
                    onShow: function () {
                        $(this).tooltip('tip').css({
                            borderColor: '#000'
                        });
                    }
                });
                //$(this).datagrid("fixRownumber");
            },
            rowStyler: rowStylerFun,
            enableHeaderClickMenu: true,
            enableHeaderContextMenu: true,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });
    });

    //任務編號查看頁面
    function operation(value, row, index) {
        return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('詳情',ctx+'/wfnpisupercontrolprocess/view/"
            + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
    };

    //创建查询对象并查询
    function cx() {
        var obj = $("#searchFrom").serializeObject();
        dg.datagrid('load', obj);
    }

    //表單進度查詢頁面查詢條件重置
    function listSearchReset() {
        $("#searchFrom").form("reset");
    }

    //导出excel
    function exportExcel() {
        var options = $('#dg').datagrid('getPager').data("pagination").options;
        var page = options.pageNumber;//当前页数  
        var total = options.total;
        var rows = options.pageSize;//每页的记录数（行数）  
        var form = document.getElementById("searchFrom");
        $('#page').val(page);
        $('#rows').val(total);
        var form = document.getElementById("searchFrom");
        searchFrom.action = ctx + '/wfnpisupercontrolprocess/exportExcel';
        form.submit();
    }
</script>
</body>
</html>