<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>NPI區域郵箱管控需求申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
        #info_Body table:nth-child(even){
            background: #e4f1fb;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfnpisupercontrolprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfnpisupercontrolprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfnpisupercontrolprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">NPI區域郵箱管控需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfnpisupercontrolprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfnpisupercontrolprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfnpisupercontrolprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfnpisupercontrolprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfnpisupercontrolprocessEntity.makerno}/${wfnpisupercontrolprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="4%">申請人工號</td>
                            <td width="6%" class="td_style2">${wfnpisupercontrolprocessEntity.dealno }</td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style2">${wfnpisupercontrolprocessEntity.dealname }</td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style2">${wfnpisupercontrolprocessEntity.dealdeptno }</td>
                            <td width="3%">費用代碼</td>
                            <td width="6%" class="td_style2">${wfnpisupercontrolprocessEntity.dealcostno }</td>
                            <td width="4%">所在廠區</td>
                            <td width="7%" class="td_style2">
                                <input id="dealfactoryid" name="wfnpisupercontrol.dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfnpisupercontrolprocessEntity.dealfactoryid }" disabled
                                       data-options="width: 120,required:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style2">${wfnpisupercontrolprocessEntity.dealleveltype }</td>
                            <td>管理職</td>
                            <td class="td_style2">${wfnpisupercontrolprocessEntity.dealmanager }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfnpisupercontrolprocessEntity.dealemail }</td>
                            <td>使用區域</td>
                            <td class="td_style2">
                                <input id="dealarea" name="wfnpisupercontrol.dealarea" class="easyui-combobox" data-options="width: 70" disabled
                                       value="${wfnpisupercontrolprocessEntity.dealarea }" panelHeight="auto"/>&nbsp;/
                                <input id="dealbuilding" name="wfnpisupercontrol.dealbuilding" class="easyui-combobox" data-options="width: 70" disabled
                                       value="${wfnpisupercontrolprocessEntity.dealbuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機</td>
                            <td class="td_style2">${wfnpisupercontrolprocessEntity.dealtel }</td>
                            <td>單位</td>
                            <td colspan="7" class="td_style2">${wfnpisupercontrolprocessEntity.dealdeptname }</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr>
                            <td colspan="10" width="100%">
                                <div id="info_Body">
                                    <c:if test="${Wfnpisupercontrolitems!=null||Wfnpisupercontrolitems.size()>0}">
                                        <c:forEach items="${Wfnpisupercontrolitems}" var="itemsEntity" varStatus="status">
                                            <table class="formList" id="info_Body_Pid${status.index}">
                                                <tbody id="info_Body_item${status.index}">
                                                <tr align="center">
                                                    <td width="8%">郵箱負責人工號</td>
                                                    <td width="10%" class="td_style2">${itemsEntity.applyno }</td>
                                                    <td width="8%">負責人</td>
                                                    <td width="10%" class="td_style2">${itemsEntity.applyname }</td>
                                                    <td width="8%">所屬安保區域</td>
                                                    <td colspan="2" class="td_style2">${itemsEntity.securityarea}</td>
                                                    <td width="8%" colspan="2">申請類別</td>
                                                    <td class="td_style2">${itemsEntity.applytype}
                                                    </td>
                                                </tr>
                                                <tr align="center">
                                                    <td width="8%">郵箱地址</td>
                                                    <td colspan="3" class="td_style2">${itemsEntity.applyemail}</td>
                                                    <td width="8%">綁定IP</td>
                                                    <td class="td_style2">${itemsEntity.applyip}</td>
                                                    <td width="8%">是否NPI</td>
                                                    <td class="td_style2">${itemsEntity.applynpi}</td>
                                                    <td width="12%">是否可發送非管控附件</td>
                                                    <td class="td_style2">${itemsEntity.sendemail}</td>
                                                </tr>
                                                <tr align="center">
                                                    <td width="8%">安保區審核主管工號</td>
                                                    <td width="10%" class="td_style2">${itemsEntity.auditno}</td>
                                                    <td width="8%">審核主管</td>
                                                    <td width="10%" class="td_style2">${itemsEntity.auditname}</td>
                                                    <td width="8%">管理職</td>
                                                    <td width="10%" class="td_style2">${itemsEntity.auditmanager}</td>
                                                    <td width="8%">審核主管郵箱</td>
                                                    <td colspan="3" class="td_style2">${itemsEntity.auditemail}</td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </c:forEach>
                                    </c:if>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${wfnpisupercontrolprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">需求說明</td>
                            <td width="90%" class="td_style2">
                                <textarea id="applyreason" name="wfnpispercontrol.applyreason"
                                          class="easyui-validatebox"
                                          maxlength="300" readonly
                                          style="width:99%;height:80px;" data-options="required:true"
                                          rows="5" cols="6">${wfnpisupercontrolprocessEntity.applyreason }</textarea>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','NPI區域郵箱管控需求申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfnpisupercontrolprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfnpisupercontrolprocessEntity.workstatus!=null&&wfnpisupercontrolprocessEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/information/wfnpisupercontrolprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>