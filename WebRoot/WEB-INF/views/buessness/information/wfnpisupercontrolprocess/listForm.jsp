<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>NPI區域郵箱管控需求申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfnpisupercontrolprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfnpisupercontrolprocessEntity.id }"/>
    <input id="serialno" name="wfnpisupercontrol.serialno" type="hidden" value="${wfnpisupercontrolprocessEntity.serialno }"/>
    <input id="makerno" name="wfnpisupercontrol.makerno" type="hidden" value="${wfnpisupercontrolprocessEntity.makerno }"/>
    <input id="makername" name="wfnpisupercontrol.makername" type="hidden" value="${wfnpisupercontrolprocessEntity.makername }"/>
    <input id="makerdeptno" name="wfnpisupercontrol.makerdeptno" type="hidden" value="${wfnpisupercontrolprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfnpisupercontrol.makerfactoryid" type="hidden"
           value="${wfnpisupercontrolprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">NPI區域郵箱管控需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfnpisupercontrolprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfnpisupercontrolprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfnpisupercontrolprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfnpisupercontrolprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfnpisupercontrolprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfnpisupercontrolprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfnpisupercontrolprocessEntity.makerno}/${wfnpisupercontrolprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="4%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealno" name="wfnpisupercontrol.dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfnpisupercontrolprocessEntity.dealno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="wfnpisupercontrol.dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfnpisupercontrolprocessEntity.dealname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="wfnpisupercontrol.dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfnpisupercontrolprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="3%">費用代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealcostno" name="wfnpisupercontrol.dealcostno" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfnpisupercontrolprocessEntity.dealcostno }"/>
                            </td>
                            <td width="4%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="dealfactoryid" name="wfnpisupercontrol.dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfnpisupercontrolprocessEntity.dealfactoryid }"
                                       data-options="width: 120,validType:'comboxValidate[\'dealfactoryid\',\'请選擇所在廠區\']',onSelect:function(){onchangeFactory();}"/>
                                <input id="dealnofactoryid" name="wfnpisupercontrol.dealnofactoryid" type="hidden" value="${wfnpisupercontrolprocessEntity.dealnofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="dealleveltype" name="wfnpisupercontrol.dealleveltype" class="easyui-validatebox" data-options="width: 80"
                                       value="${wfnpisupercontrolprocessEntity.dealleveltype }"/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="dealmanager" name="wfnpisupercontrol.dealmanager" class="easyui-validatebox" data-options="width: 80"
                                       value="${wfnpisupercontrolprocessEntity.dealmanager }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="wfnpisupercontrol.dealemail" class="easyui-validatebox"
                                       value="${wfnpisupercontrolprocessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td>使用區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealarea" name="wfnpisupercontrol.dealarea" class="easyui-combobox" data-options="width: 70,validType:'comboxValidate[\'dealarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wfnpisupercontrolprocessEntity.dealarea }" panelHeight="auto"/>&nbsp;/
                                <input id="dealbuilding" name="wfnpisupercontrol.dealbuilding" class="easyui-combobox" data-options="width: 70,validType:'comboxValidate[\'dealbuilding\',\'请選擇樓棟\']'"
                                       value="${wfnpisupercontrolprocessEntity.dealbuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealtel" name="wfnpisupercontrol.dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfnpisupercontrolprocessEntity.dealtel }" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="7" class="td_style1">
                                <input id="dealdeptname" name="wfnpisupercontrol.dealdeptname" class="easyui-validatebox" data-options="width: 400,required:true"
                                       value="${wfnpisupercontrolprocessEntity.dealdeptname }"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr>
                            <td colspan="10" width="100%">
                                <div id="info_Body">
                                    <c:if test="${Wfnpisupercontrolitems!=null||Wfnpisupercontrolitems.size()>0}">
                                        <c:forEach items="${Wfnpisupercontrolitems}" var="itemsEntity" varStatus="status">
                                            <table class="formList" id="info_Body_Pid${status.index}">
                                                <tbody id="info_Body_item${status.index}">
                                                    <tr align="center">
                                                        <td width="8%">郵箱負責人工號&nbsp;<font color="red">*</font></td>
                                                        <td width="10%" class="td_style1">
                                                            <input id="applyno${status.index}"
                                                                   name="npisupercontrolitems[${status.index}].applyno"
                                                                   class="easyui-validatebox"
                                                                   data-options="width: 80,required:true"
                                                                   value="${itemsEntity.applyno }"
                                                                   onblur="queryApplyUserInfo(${status.index},'apply');"/>
                                                        </td>
                                                        <td width="8%">負責人</td>
                                                        <td width="10%" class="td_style1">
                                                            <input id="applyname${status.index}" name="npisupercontrolitems[${status.index}].applyname"
                                                                   class="easyui-validatebox inputCss" data-options="width: 80" value="${itemsEntity.applyname }"/>
                                                        </td>
                                                        <td width="8%">所屬安保區域&nbsp;<font color="red">*</font></td>
                                                        <td colspan="2" class="td_style1">
                                                            <input id="securityarea${status.index}" name="npisupercontrolitems[${status.index}].securityarea" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadSecurityarea(${status.index});}" style="width:120px;"
                                                                   class="easyui-combobox"  value="${itemsEntity.securityarea}"/>
                                                        </td>
                                                        <td width="8%" colspan="2">申請類別&nbsp;<font color="red">*</font></td>
                                                        <td class="td_style1">
                                                            <input id="applytype${status.index}" name="npisupercontrolitems[${status.index}].applytype" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadApplytype(${status.index});}" style="width:100px;"
                                                                   class="easyui-combobox"  value="${itemsEntity.applytype}"/>
                                                        </td>
                                                    </tr>
                                                    <tr align="center">
                                                        <td width="8%">郵箱地址&nbsp;<font color="red">*</font></td>
                                                        <td colspan="3" class="td_style1">
                                                            <input id="applyemail${status.index}" name="npisupercontrolitems[${status.index}].applyemail" class="easyui-validatebox"
                                                                   data-options="width: 300,required:true,validType:'email[\'applyemail${status.index}\',\'郵箱的格式不正確\']'" value="${itemsEntity.applyemail}"/>
                                                        </td>
                                                        <td width="8%">綁定IP&nbsp;<font color="red">*</font></td>
                                                        <td class="td_style1">
                                                            <input id="applyip${status.index}" name="npisupercontrolitems[${status.index}].applyip" class="easyui-validatebox" onblur="isNpiCheck(this,${status.index});"
                                                                   data-options="width: 100,required:true,validType:'ip[\'applyip${status.index}\']'" value="${itemsEntity.applyip}"/>
                                                        </td>
                                                        <td width="8%">是否NPI</td>
                                                        <td class="td_style1">
                                                            <input id="applynpi${status.index}" name="npisupercontrolitems[${status.index}].applynpi" class="easyui-validatebox inputCss" readonly
                                                                   data-options="width: 80" value="${itemsEntity.applynpi}"/>
                                                        </td>
                                                        <td width="12%">是否可發送非管控附件&nbsp;<font color="red">*</font></td>
                                                        <td class="td_style1">
                                                            <input id="sendemail${status.index}" name="npisupercontrolitems[${status.index}].sendemail" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadSendEmail(${status.index});}" style="width:100px;"
                                                                   class="easyui-combobox" value="${itemsEntity.sendemail}"/>
                                                        </td>
                                                    </tr>
                                                    <tr align="center">
                                                        <td width="8%">安保區審核主管工號&nbsp;<font color="red">*</font></td>
                                                        <td width="10%" class="td_style1">
                                                            <input id="auditno${status.index}" name="npisupercontrolitems[${status.index}].auditno" class="easyui-validatebox"
                                                                   data-options="width: 80,required:true" value="${itemsEntity.auditno}" onblur="queryApplyUserInfo(${status.index},'audit');"/>
                                                        </td>
                                                        <td width="8%">審核主管</td>
                                                        <td width="10%" class="td_style1">
                                                            <input id="auditname${status.index}" name="npisupercontrolitems[${status.index}].auditname"
                                                                   class="easyui-validatebox inputCss" data-options="width: 80" value="${itemsEntity.auditname}"/>
                                                        </td>
                                                        <td width="8%">管理職</td>
                                                        <td width="10%" class="td_style1">
                                                            <input id="auditmanager${status.index}" name="npisupercontrolitems[${status.index}].auditmanager"
                                                                   class="easyui-validatebox inputCss" data-options="width: 80" value="${itemsEntity.auditmanager}"/>
                                                        </td>
                                                        <td width="8%">審核主管郵箱&nbsp;<font color="red">*</font></td>
                                                        <td colspan="3" class="td_style1">
                                                            <input id="auditemail${status.index}" name="npisupercontrolitems[${status.index}].auditemail"
                                                                   class="easyui-validatebox" data-options="width: 300,required:true,validType:'email[\'auditemail${status.index}\',\'郵箱的格式不正確\']'" value="${itemsEntity.auditemail}"/>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                                <tr align="center">
                                                    <td colspan="10">
                                                        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove"
                                                           plain="true" data-options="disabled:false"
                                                           onclick="delItems('${status.index}')">删除此筆</a>
                                                    </td>
                                                </tr>
                                            </table>
                                        </c:forEach>
                                    </c:if>
                                    <c:if test="${Wfnpisupercontrolitems==null||Wfnpisupercontrolitems.size()==0}">
                                        <table class="formList" id="info_Body_Pid0">
                                            <tbody id="info_Body_item0">
                                                <tr align="center">
                                                    <td width="8%">郵箱負責人工號&nbsp;<font color="red">*</font></td>
                                                    <td width="10%" class="td_style1">
                                                        <input id="applyno0" name="npisupercontrolitems[0].applyno" class="easyui-validatebox"
                                                               data-options="width: 80,required:true" value="" onblur="queryApplyUserInfo(0,'apply');"/>
                                                    </td>
                                                    <td width="8%">負責人</td>
                                                    <td width="10%" class="td_style1">
                                                        <input id="applyname0" name="npisupercontrolitems[0].applyname"
                                                               class="easyui-validatebox inputCss" data-options="width: 80" value=""/>
                                                    </td>

                                                    <td width="8%">所屬安保區域&nbsp;<font color="red">*</font></td>
                                                    <td colspan="2" class="td_style1">
                                                        <input id="securityarea0" name="npisupercontrolitems[0].securityarea" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadSecurityarea(0);}" style="width:120px;"
                                                               class="easyui-combobox"  value=""/>
                                                    </td>
                                                    <td width="8%" colspan="2">申請類別&nbsp;<font color="red">*</font></td>
                                                    <td class="td_style1">
                                                        <input id="applytype0" name="npisupercontrolitems[0].applytype" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadApplytype(0);}" style="width:100px;"
                                                               class="easyui-combobox"  value=""/>
                                                    </td>
                                                </tr>
                                                <tr align="center">
                                                    <td width="8%">郵箱地址&nbsp;<font color="red">*</font></td>
                                                    <td colspan="3" class="td_style1">
                                                        <input id="applyemail0" name="npisupercontrolitems[0].applyemail" class="easyui-validatebox"
                                                               data-options="width: 300,required:true,validType:'email[\'applyemail0\',\'郵箱的格式不正確\']'" value=""/>
                                                    </td>
                                                    <td width="8%">綁定IP&nbsp;<font color="red">*</font></td>
                                                    <td class="td_style1">
                                                        <input id="applyip0" name="npisupercontrolitems[0].applyip" class="easyui-validatebox" onblur="isNpiCheck(this,0);"
                                                               data-options="width: 100,required:true,validType:'ip[\'applyip0\']'" value=""/>
                                                    </td>
                                                    <td width="8%">是否NPI</td>
                                                    <td class="td_style1">
                                                        <input id="applynpi0" name="npisupercontrolitems[0].applynpi" class="easyui-validatebox inputCss" readonly
                                                               data-options="width: 80" value=""/>
                                                    </td>
                                                    <td width="12%">是否可發送非管控附件&nbsp;<font color="red">*</font></td>
                                                    <td class="td_style1">
                                                        <input id="sendemail0" name="npisupercontrolitems[0].sendemail" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadSendEmail(0);}" style="width:100px;"
                                                               class="easyui-combobox"  value=""/>
                                                    </td>
                                                </tr>
                                                <tr align="center">
                                                    <td width="8%">安保區審核主管工號&nbsp;<font color="red">*</font></td>
                                                    <td width="10%" class="td_style1">
                                                        <input id="auditno0" name="npisupercontrolitems[0].auditno" class="easyui-validatebox"
                                                               data-options="width: 80,required:true" value="" onblur="queryApplyUserInfo(0,'audit');"/>
                                                    </td>
                                                    <td width="8%">審核主管</td>
                                                    <td width="10%" class="td_style1">
                                                        <input id="auditname0" name="npisupercontrolitems[0].auditname"
                                                               class="easyui-validatebox inputCss" data-options="width: 80" value=""/>
                                                    </td>
                                                    <td width="8%">管理職</td>
                                                    <td width="10%" class="td_style1">
                                                        <input id="auditmanager0" name="npisupercontrolitems[0].auditmanager"
                                                               class="easyui-validatebox inputCss" data-options="width: 80" value=""/>
                                                    </td>
                                                    <td width="8%">審核主管郵箱&nbsp;<font color="red">*</font></td>
                                                    <td colspan="3" class="td_style1">
                                                        <input id="auditemail0" name="npisupercontrolitems[0].auditemail"
                                                               class="easyui-validatebox" data-options="width: 300,required:true,validType:'email[\'auditemail0\',\'郵箱的格式不正確\']'" value=""/>
                                                    </td>
                                                </tr>
                                            </tbody>
                                            <tr align="center">
                                                <td colspan="10">
                                                    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove"
                                                       plain="true" data-options="disabled:false"
                                                       onclick="delItems('0')">删除此筆</a>
                                                </td>
                                            </tr>
                                        </table>
                                    </c:if>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="8">
                                <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true"
                                   onclick="addItems();">添加一筆</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button"  value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="wfnpisupercontrol.attachids" value="${wfnpisupercontrolprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">需求說明&nbsp;<font color="red">*</font></td>
                            <td width="90%" class="td_style1">
                                <textarea id="applyreason" name="wfnpisupercontrol.applyreason"
                                          class="easyui-validatebox"
                                          oninput="return LessThan(this);"
                                          onchange="return LessThan(this);"
                                          onpropertychange="return LessThan(this);"
                                          maxlength="300"
                                          style="width:99%;height:80px;" data-options="required:true,validType:'length[0,300]'"
                                          rows="5" cols="6">${wfnpisupercontrolprocessEntity.applyreason }</textarea><span id="txtNum"></span>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">備註</td>
                            <td width="90%" class="td_style2">
                                1.NPI群組說明：三工單位所屬群組為iPEBG，智動化、檢測、iTRG、廠部等非三工單位所屬群組為NONiPEBG；<br>
                                2.加入NPI區域郵箱管控審核主管需為中心級/處級主管；<br>
                                3.“是否可發送非管控附件”一欄指的是NPI區域管控郵箱發往非管控區域郵箱是否可發送附件；<br>
                                4.NPI區域郵箱管控需求申請表須核准至處級主管；<br>
                                5.新增管控郵箱時, 需上傳用戶簽合的<a href="${ctx}/wfnpisupercontrolprocess/downLoad/npisuperpromiseFile">遵守資訊安全承諾書</a>；<br>
                                6.添加加入NPI區域郵箱管控最大為10筆，超過10筆請上傳<a href="${ctx}/wfnpisupercontrolprocess/downLoad/npisupercontrolFile">NPI區域郵箱管控需求申請表</a>。
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_npiquyuyouxiangshenqing','NPI區域郵箱管控需求申請單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zxywkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zxywkchargeno_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'zxywkchargeTable','zxywkchargeno','zxywkchargename',$('#dealfactoryid').combobox('getValue'),'wfnpisupercontrol')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxywkchargeno" name="wfnpisupercontrol.zxywkchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxywkchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.zxywkchargeno }"/><c:if
                                                            test="${requiredMap['zxywkchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxywkchargename"
                                                                name="wfnpisupercontrol.zxywkchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxywkchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.zxywkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#dealdeptno').val(),'cchargeno','cchargename',$('#dealnofactoryid').val(),'wfnpisupercontrol')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wfnpisupercontrol.cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wfnpisupercontrol.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealnofactoryid').val(),'wfnpisupercontrol')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wfnpisupercontrol.zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="wfnpisupercontrol.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealnofactoryid').val(),'wfnpisupercontrol')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="wfnpisupercontrol.zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="wfnpisupercontrol.zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zxywbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zxywbchargeno_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(25,'zxywbchargeTable','zxywbchargeno','zxywbchargename',$('#dealfactoryid').combobox('getValue'),'wfnpisupercontrol')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxywbchargeno" name="wfnpisupercontrol.zxywbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxywbchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.zxywbchargeno }"/><c:if
                                                            test="${requiredMap['zxywbchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxywbchargename"
                                                                name="wfnpisupercontrol.zxywbchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxywbchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.zxywbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zabchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zabchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(154,'zabchargeTable','zabchargeno','zabchargename',$('#dealfactoryid').combobox('getValue'),'wfnpisupercontrol')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zabchargeno" name="wfnpisupercontrol.zabchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zabchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.zabchargeno }"/><c:if
                                                            test="${requiredMap['zabchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zabchargename"
                                                                name="wfnpisupercontrol.zabchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zabchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.zabchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#dealdeptno').val(),'pcchargeno','pcchargename',$('#dealnofactoryid').val(),'wfnpisupercontrol')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="wfnpisupercontrol.pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="wfnpisupercontrol.pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="yxglchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['yxglchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(191,'yxglchargeTable','yxglchargeno','yxglchargename',$('#dealfactoryid').combobox('getValue'),'wfnpisupercontrol')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="yxglchargeno" name="wfnpisupercontrol.yxglchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['yxglchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.yxglchargeno }"/><c:if
                                                            test="${requiredMap['yxglchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="yxglchargename"
                                                                name="wfnpisupercontrol.yxglchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['yxglchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.yxglchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>

                                    <%--<tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="ywqchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ywqchargeno_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole5(21,'ywqchargeno','ywqchargename','','','','',$('#dealfactoryid').combobox('getValue'),$('#dealarea').combobox('getValue'),$('#dealbuilding').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ywqchargeno" name="wfnpisupercontrol.ywqchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ywqchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.ywqchargeno }"/><c:if
                                                            test="${requiredMap['ywqchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ywqchargename" name="wfnpisupercontrol.ywqchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ywqchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.ywqchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zakchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zakchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(153,'zakchargeTable','zakchargeno','zakchargename',$('#dealfactoryid').combobox('getValue'),'wfnpisupercontrol')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zakchargeno" name="wfnpisupercontrol.zakchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zakchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.zakchargeno }"/><c:if
                                                            test="${requiredMap['zakchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zakchargename" name="wfnpisupercontrol.zakchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zakchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.zakchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealnofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="wfnpisupercontrol.kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="wfnpisupercontrol.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealnofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="wfnpisupercontrol.bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="wfnpisupercontrol.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#dealdeptno').val(),'cchargeno','cchargename',$('#dealnofactoryid').val(),'wfnpisupercontrol')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wfnpisupercontrol.cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wfnpisupercontrol.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealnofactoryid').val(),'wfnpisupercontrol')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wfnpisupercontrol.zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="wfnpisupercontrol.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealnofactoryid').val(),'wfnpisupercontrol')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="wfnpisupercontrol.zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="wfnpisupercontrol.zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#dealdeptno').val(),'pcchargeno','pcchargename',$('#dealnofactoryid').val(),'wfnpisupercontrol')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="wfnpisupercontrol.pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="wfnpisupercontrol.pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zxywkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zxywkchargeno_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'zxywkchargeTable','zxywkchargeno','zxywkchargename',$('#dealfactoryid').combobox('getValue'),'wfnpisupercontrol')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxywkchargeno" name="wfnpisupercontrol.zxywkchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxywkchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.zxywkchargeno }"/><c:if
                                                            test="${requiredMap['zxywkchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxywkchargename" name="wfnpisupercontrol.zxywkchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxywkchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.zxywkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zxywbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zxywbchargeno_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(25,'zxywbchargeTable','zxywbchargeno','zxywbchargename',$('#dealfactoryid').combobox('getValue'),'wfnpisupercontrol')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxywbchargeno" name="wfnpisupercontrol.zxywbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxywbchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.zxywbchargeno }"/><c:if
                                                            test="${requiredMap['zxywbchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxywbchargename" name="wfnpisupercontrol.zxywbchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxywbchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.zxywbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zabchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zabchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(154,'zabchargeTable','zabchargeno','zabchargename',$('#dealfactoryid').combobox('getValue'),'wfnpisupercontrol')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zabchargeno" name="wfnpisupercontrol.zabchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zabchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.zabchargeno }"/><c:if
                                                            test="${requiredMap['zabchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zabchargename" name="wfnpisupercontrol.zabchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zabchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.zabchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="yxglchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['yxglchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(191,'yxglchargeTable','yxglchargeno','yxglchargename',$('#dealfactoryid').combobox('getValue'),'wfnpisupercontrol')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="yxglchargeno" name="wfnpisupercontrol.yxglchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['yxglchargeno']}"
                                                               readonly
                                                               value="${wfnpisupercontrolprocessEntity.yxglchargeno }"/><c:if
                                                            test="${requiredMap['yxglchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="yxglchargename" name="wfnpisupercontrol.yxglchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['yxglchargeno']}"
                                                                value="${wfnpisupercontrolprocessEntity.yxglchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>--%>

                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <input type="hidden" id="onlyKchargeSignle" value="1" />
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
</form>
</div>
<script src='${ctx}/static/js/information/wfnpisupercontrolprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>
