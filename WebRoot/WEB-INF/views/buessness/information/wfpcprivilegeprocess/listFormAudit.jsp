<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>個人電腦特殊權限申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<style type="text/css">
    .td_style2 {
        text-align: left;
    }
</style>
</head>
<body>
<form id="mainform" action="${ctx}/wfpcprivilegeprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfpcprivilegeprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfpcprivilegeprocessEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">個人電腦特殊權限申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfpcprivilegeprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfpcprivilegeprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfpcprivilegeprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfpcprivilegeprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfpcprivilegeprocessEntity.makerno}/${wfpcprivilegeprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
           <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">承辦人工號</td>
                            <td width="10%" class="td_style2">${wfpcprivilegeprocessEntity.dealno}</td>
                            <td width="8%">承辦人</td>
                            <td width="10%" class="td_style2">${wfpcprivilegeprocessEntity.dealname}</td>
                            <td width="10%">單位代碼</td>
                            <td width="10%" class="td_style2">${wfpcprivilegeprocessEntity.dealdeptno}</td>
                            <td width="7%">費用代碼</td>
                            <td width="10%" class="td_style2">${wfpcprivilegeprocessEntity.applycostno}</td>
                            <td width="10%">所在廠區</td>
                            <td width="15%" class="td_style1">
                                <input id="applyfactoryid" name="wfpcprivilegeprocess.applyfactoryid"
                                       class="easyui-combobox" disabled
                                       panelHeight="auto" value="${wfpcprivilegeprocessEntity.applyfactoryid }"
                                       data-options="width: 120"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style2">${wfpcprivilegeprocessEntity.applyleveltype}</td>
                            <td>管理職</td>
                            <td class="td_style2">${wfpcprivilegeprocessEntity.applymanager }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfpcprivilegeprocessEntity.dealemail }</td>
                            <td>使用區域</td>
                            <td class="td_style1">
                                <input id="applyarea" name="wfpcprivilegeprocess.applyarea" class="easyui-combobox" disabled
                                       data-options="width: 70,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wfpcprivilegeprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="wfpcprivilegeprocess.applybuilding" disabled
                                       class="easyui-combobox" data-options="width: 70,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wfpcprivilegeprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機</td>
                            <td class="td_style2">${wfpcprivilegeprocessEntity.dealtel}</td>
                            <td>單位</td>
                            <td colspan="5" class="td_style2">${wfpcprivilegeprocessEntity.dealdeptname }</td>
                            <td>安保區域</td>
                            <td class="td_style2">
                                <div class="securityareaDiv"></div>
                                <input id="securityarea" name="wfpcprivilegeprocess.securityarea"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfpcprivilegeprocessEntity.securityarea }"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="5" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">申請操作</td>
                            <td width="10%">申請類型</td>
                            <td width="15%">申請項目</td>
                            <td width="45%">其他信息</td>
                            <td width="20%">需求說明</td>
                        </tr>
                        <c:if test="${wfpcprivilegeprocessEntity.usbapplytype=='1'}">
                        <tr align="center">
                            <td>
                               <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='0'}">新增</c:if>
                               <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='1'}">變更</c:if>
                               <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='2'}">刪除</c:if>
                               <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='3'}">續期</c:if>
                            </td>
                            <td>USB</td>
                            <td>
                                <c:if test="${wfpcprivilegeprocessEntity.applyproject=='1'}">連接設備</c:if>
                                <c:if test="${wfpcprivilegeprocessEntity.applyproject=='2'}">只讀權限</c:if>
                                <c:if test="${wfpcprivilegeprocessEntity.applyproject=='3'}">存儲權限</c:if>
                            </td>
                            <td>
                                設備類別：
                                <c:if test="${wfpcprivilegeprocessEntity.applyproject=='1'}">${wfpcprivilegeprocessEntity.connectdevicetype}；</c:if>
                                <c:if test="${wfpcprivilegeprocessEntity.applyproject=='2'}">${wfpcprivilegeprocessEntity.readdevicetype}；</c:if>
                                <c:if test="${wfpcprivilegeprocessEntity.applyproject=='3'}">${wfpcprivilegeprocessEntity.savedevicetype}；</c:if>
                                DL管控：
                                <c:if test="${wfpcprivilegeprocessEntity.applyproject=='1'}">
                                    <c:if test="${wfpcprivilegeprocessEntity.connectdevicedl=='0'}">管控；</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.connectdevicedl=='1'}">不管控（協理及以上主管）；</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.connectdevicedl=='2'}">不管控（DL與軟件衝突）；</c:if>
                                </c:if>
                                <c:if test="${wfpcprivilegeprocessEntity.applyproject=='2'}">
                                    <c:if test="${wfpcprivilegeprocessEntity.readdevicedl=='0'}">管控；</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.readdevicedl=='1'}">不管控（協理及以上主管）；</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.readdevicedl=='2'}">不管控（DL與軟件衝突）；</c:if>
                                </c:if>
                                <c:if test="${wfpcprivilegeprocessEntity.applyproject=='3'}">
                                    <c:if test="${wfpcprivilegeprocessEntity.savedevicedl=='0'}">管控；</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.savedevicedl=='1'}">不管控（協理及以上主管）；</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.savedevicedl=='2'}">不管控（DL與軟件衝突）；</c:if>
                                </c:if>
                                設備編號：
                                <c:if test="${wfpcprivilegeprocessEntity.applyproject=='1'}">${wfpcprivilegeprocessEntity.connectdeviceno}；</c:if>
                                <c:if test="${wfpcprivilegeprocessEntity.applyproject=='2'}">${wfpcprivilegeprocessEntity.readdeviceno}；有效期：${validTimeBegin}~${validTimeEnd}</c:if>
                                <c:if test="${wfpcprivilegeprocessEntity.applyproject=='3'}">${wfpcprivilegeprocessEntity.savedeviceno}；有效期：${validTimeBegin}~${validTimeEnd}</c:if>
                            </td>
                            <td>${wfpcprivilegeprocessEntity.usbdesc}</td>
                        </tr>
                        </c:if>
                        <c:if test="${wfpcprivilegeprocessEntity.cdrom=='1'}">
                            <tr align="center">
                                <td>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='0'}">新增</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='1'}">變更</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='2'}">刪除</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='3'}">續期</c:if>
                                </td>
                                <td>外設</td>
                                <td>CD-ROM</td>
                                <td>無</td>
                                <td>${wfpcprivilegeprocessEntity.cdromdesc}</td>
                            </tr>
                        </c:if>
                        <c:if test="${wfpcprivilegeprocessEntity.floppydrive=='1'}">
                            <tr align="center">
                                <td>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='0'}">新增</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='1'}">變更</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='2'}">刪除</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='3'}">續期</c:if>
                                </td>
                                <td>外設</td>
                                <td>軟驅</td>
                                <td>無</td>
                                <td>${wfpcprivilegeprocessEntity.floppydrivedesc}</td>
                            </tr>
                        </c:if>
                        <c:if test="${wfpcprivilegeprocessEntity.soundcard=='1'}">
                            <tr align="center">
                                <td>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='0'}">新增</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='1'}">變更</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='2'}">刪除</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='3'}">續期</c:if>
                                </td>
                                <td>外設</td>
                                <td>聲卡</td>
                                <td>無</td>
                                <td>${wfpcprivilegeprocessEntity.soundcarddesc}</td>
                            </tr>
                        </c:if>

                        <c:if test="${wfpcprivilegeprocessEntity.accountprivilege!='' && wfpcprivilegeprocessEntity.accountprivilege!=null}">
                            <tr align="center">
                                <td>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='0'}">新增</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='1'}">變更</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='2'}">刪除</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='3'}">續期</c:if>
                                </td>
                                <td>電腦策略</td>
                                <td>
                                    <c:if test="${wfpcprivilegeprocessEntity.accountprivilege=='0'}">本機賬號（用戶）</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.accountprivilege=='1'}">本機賬號（管理員）</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.accountprivilege=='3'}">AD域帳號（管理員）</c:if>
                                </td>
                                <td>
                                    <c:if test="${wfpcprivilegeprocessEntity.specialsoft!=''&& wfpcprivilegeprocessEntity.specialsoft!=null}">
                                      軟體名稱：<c:choose><c:when test="${wfpcprivilegeprocessEntity.softname!=''&& wfpcprivilegeprocessEntity.softname!=null}">${wfpcprivilegeprocessEntity.softname}；</c:when><c:otherwise>無</c:otherwise>    </c:choose>
                                        軟體版本：<c:choose><c:when test="${wfpcprivilegeprocessEntity.softversion!=''&& wfpcprivilegeprocessEntity.softversion!=null}">${wfpcprivilegeprocessEntity.softversion}；</c:when><c:otherwise>無</c:otherwise>    </c:choose>
                                        供應商名稱：<c:choose><c:when test="${wfpcprivilegeprocessEntity.softsupply!=''&& wfpcprivilegeprocessEntity.softsupply!=null}">${wfpcprivilegeprocessEntity.softsupply}；</c:when><c:otherwise>無</c:otherwise>    </c:choose>
                                        是否有版權：
                                        <c:choose><c:when test="${wfpcprivilegeprocessEntity.softcopyright!=''&& wfpcprivilegeprocessEntity.softcopyright!=null}">
                                            <c:if test="${wfpcprivilegeprocessEntity.softcopyright=='0'}">是</c:if>
                                            <c:if test="${wfpcprivilegeprocessEntity.softcopyright=='1'}">否</c:if>
                                        </c:when><c:otherwise>無</c:otherwise>    </c:choose><br />

                                    </c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.specialdevice!='' && wfpcprivilegeprocessEntity.specialdevice!=null}">
                                        設備名稱：<c:choose><c:when test="${wfpcprivilegeprocessEntity.devicename!='' && wfpcprivilegeprocessEntity.devicename!=null}">${wfpcprivilegeprocessEntity.devicename}；</c:when><c:otherwise>無</c:otherwise></c:choose>
                                        型號：<c:choose><c:when test="${wfpcprivilegeprocessEntity.deviceversion!='' && wfpcprivilegeprocessEntity.deviceversion!=null}">${wfpcprivilegeprocessEntity.deviceversion}；</c:when><c:otherwise>無</c:otherwise></c:choose>
                                        供應商名稱：<c:choose><c:when test="${wfpcprivilegeprocessEntity.devicesupply!='' && wfpcprivilegeprocessEntity.devicesupply!=null}">${wfpcprivilegeprocessEntity.devicesupply}；</c:when><c:otherwise>無</c:otherwise></c:choose>
                                        製造序列號：<c:choose><c:when test="${wfpcprivilegeprocessEntity.deviceserialnum!='' && wfpcprivilegeprocessEntity.deviceserialnum!=null}">${wfpcprivilegeprocessEntity.deviceserialnum}</c:when><c:otherwise>無</c:otherwise></c:choose><br />
                                    </c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.accountotherdesc!=''&& wfpcprivilegeprocessEntity.accountotherdesc!=null}">
                                        其他：${wfpcprivilegeprocessEntity.accountotherdesc}
                                    </c:if>
                                </td>
                                <td>${wfpcprivilegeprocessEntity.accountspecialdesc}</td>
                            </tr>
                        </c:if>
                        <c:if test="${wfpcprivilegeprocessEntity.shareprivilege!=''&& wfpcprivilegeprocessEntity.shareprivilege!=null}">
                            <tr align="center">
                                <td>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='0'}">新增</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='1'}">變更</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='2'}">刪除</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='3'}">續期</c:if>
                                </td>
                                <td>電腦策略</td>
                                <td>共享</td>
                                <td>
                                    <c:if test="${wfpcprivilegeprocessEntity.sharespecialsoft!='' && wfpcprivilegeprocessEntity.sharespecialsoft!=null}">
                                        軟體名稱：<c:choose><c:when test="${wfpcprivilegeprocessEntity.sharesoftname!=''&& wfpcprivilegeprocessEntity.sharesoftname!=null}">${wfpcprivilegeprocessEntity.sharesoftname}；</c:when><c:otherwise>無</c:otherwise></c:choose>
                                        軟體版本：<c:choose><c:when test="${wfpcprivilegeprocessEntity.sharesoftversion!=''&& wfpcprivilegeprocessEntity.sharesoftversion!=null}">${wfpcprivilegeprocessEntity.sharesoftversion}；</c:when><c:otherwise>無</c:otherwise></c:choose>
                                        供應商名稱：<c:choose><c:when test="${wfpcprivilegeprocessEntity.sharesoftsupply!=''&& wfpcprivilegeprocessEntity.sharesoftsupply!=null}">${wfpcprivilegeprocessEntity.sharesoftsupply}；</c:when><c:otherwise>無</c:otherwise></c:choose>
                                        是否有版權：
                                        <c:choose>
                                        <c:when test="${wfpcprivilegeprocessEntity.sharesoftcopyright!=''&& wfpcprivilegeprocessEntity.sharesoftcopyright!=null}">
                                            <c:if test="${wfpcprivilegeprocessEntity.sharesoftcopyright=='0'}">是</c:if>
                                            <c:if test="${wfpcprivilegeprocessEntity.sharesoftcopyright=='1'}">否</c:if>
                                        </c:when><c:otherwise>無</c:otherwise></c:choose><br />
                                    </c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.sharespecialdevice!='' && wfpcprivilegeprocessEntity.sharespecialdevice!=null}">
                                        設備名稱：<c:choose><c:when test="${wfpcprivilegeprocessEntity.sharedevicename!='' && wfpcprivilegeprocessEntity.sharedevicename!=null}">${wfpcprivilegeprocessEntity.sharedevicename}；</c:when><c:otherwise>無</c:otherwise></c:choose>
                                        型號：<c:choose><c:when test="${wfpcprivilegeprocessEntity.sharedeviceversion!='' && wfpcprivilegeprocessEntity.sharedeviceversion!=null}">${wfpcprivilegeprocessEntity.sharedeviceversion}；</c:when><c:otherwise>無</c:otherwise></c:choose>
                                        供應商名稱：<c:choose><c:when test="${wfpcprivilegeprocessEntity.sharedevicesupply!='' && wfpcprivilegeprocessEntity.sharedevicesupply!=null}">${wfpcprivilegeprocessEntity.sharedevicesupply}；</c:when><c:otherwise>無</c:otherwise></c:choose>
                                        製造序列號：<c:choose><c:when test="${wfpcprivilegeprocessEntity.sharedeviceserialnum!='' && wfpcprivilegeprocessEntity.sharedeviceserialnum!=null}">${wfpcprivilegeprocessEntity.sharedeviceserialnum}</c:when><c:otherwise>無</c:otherwise></c:choose><br />
                                    </c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.shareotherdesc!='' && wfpcprivilegeprocessEntity.shareotherdesc!=null}">
                                        其他：${wfpcprivilegeprocessEntity.shareotherdesc}
                                    </c:if>
                                </td>
                                <td>${wfpcprivilegeprocessEntity.sharespecialdesc}</td>
                            </tr>
                        </c:if>
                        <c:if test="${wfpcprivilegeprocessEntity.remotedescription!=''&& wfpcprivilegeprocessEntity.remotedescription!=null}">
                            <tr align="center">
                                <td>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='0'}">新增</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='1'}">變更</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='2'}">刪除</c:if>
                                    <c:if test="${wfpcprivilegeprocessEntity.applyoperate=='3'}">續期</c:if>
                                </td>
                                <td>遠程連線</td>
                                <td>電腦遠程連線權限</td>
                                <td>
                                    申請開通時間：<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfpcprivilegeprocessEntity.pcpstarttime}"/>至<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfpcprivilegeprocessEntity.pcpendtime}"/>
                                </td>
                                <td>
                                    ${wfpcprivilegeprocessEntity.remotedescription}
                                </td>
                            </tr>
                        </c:if>
                        <tr>
                            <td colspan="10" class="td_style1">申請人明細</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <input id="pcprivilegeItemTableIndex" type="hidden"
                                           value="<c:if test="${pcprivilegeitems!=null && pcprivilegeitems.size()>0}">${pcprivilegeitems.size() +1}</c:if>
                                        <c:if test="${pcprivilegeitems.size()==0 || pcprivilegeitems==null}">2</c:if>"/>
                                    </input>
                                    <table id="pcprivilegeItemTable" width="100%">
                                        <tr align="center">
                                            <td width="10%">申請人工號</td>
                                            <td width="10%">申請人</td>
                                            <td width="10%">所在廠區</td>
                                            <td width="15%">使用區域</td>
                                            <td width="10%">IP地址</td>
                                            <td width="10%">是否NPI</td>
                                            <td width="15%">電腦名稱</td>
                                            <td width="15%">遠端/被遠端</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${pcprivilegeitems!=null&&pcprivilegeitems.size()>0}">
                                            <c:forEach items="${pcprivilegeitems}" var="pcprivilegeitems" varStatus="status">
                                                <tr align="center" id="pcprivilegeitems${status.index+1}">
                                                    <td>${pcprivilegeitems.applyno}</td>
                                                    <td>${pcprivilegeitems.applyname}</td>
                                                    <td>
                                                        <input id="pc_applyfactoryid${status.index+1}" name="wfpcprivilegeitems[${status.index+1}].applyfactoryid" disabled
                                                               data-options="panelHeight:300,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'pc_applyfactoryid${status.index+1}\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(${status.index+1});},onSelect:function(){pcOnchangeFactory('pc_applyfactoryid${status.index+1}',${status.index+1});}"
                                                               style="width:100px;" class="easyui-combobox" value="${pcprivilegeitems.applyfactoryid}"/>
                                                    </td>
                                                    <td>
                                                        <input id="pc_applyarea${status.index+1}" name="wfpcprivilegeitems[${status.index+1}].applyarea" disabled
                                                               data-options="validType:'comboxValidate[\'pc_applyarea${status.index+1}\',\'请選擇區域\']',onSelect:function(){pcOnchangeArea('pc_applyarea${status.index+1}',${status.index+1});}"
                                                               class="easyui-combobox" style="width:80px;" value="${pcprivilegeitems.applyarea}" panelHeight="auto"/>/
                                                        <input id="pc_applybuilding${status.index+1}" name="wfpcprivilegeitems[${status.index+1}].applybuilding" disabled
                                                               data-options="validType:'comboxValidate[\'pc_applybuilding${status.index+1}\',\'请選擇樓棟\']'"
                                                               class="easyui-combobox" style="width:80px;" value="${pcprivilegeitems.applybuilding}" panelHeight="auto"/>
                                                    </td>
                                                    <td>${pcprivilegeitems.applyip}</td>
                                                    <td>${pcprivilegeitems.securityarea}</td>
                                                    <td>${pcprivilegeitems.pcname}</td>
                                                    <td>
                                                        <input id="pc_remoteend${status.index+1}" name="wfpcprivilegeitems[${status.index+1}].remoteend" disabled
                                                               data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadRemoteend(${status.index+1});}" style="width:80px;"
                                                               class="easyui-combobox"  value="${pcprivilegeitems.remoteend}"/>
                                                    </td>

                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                     <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${wfpcprivilegeprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%" class="td_style1">
						    <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox" style="width:1000px;height:60px;" rows="4" cols="4"></textarea>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wfpcprivilegeprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','個人電腦特殊權限申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfpcprivilegeprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<input type="hidden" id="auditAndView" value="1" />
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
  <div id="dlg"></div>
<script src='${ctx}/static/js/information/wfpcprivilegeprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>
