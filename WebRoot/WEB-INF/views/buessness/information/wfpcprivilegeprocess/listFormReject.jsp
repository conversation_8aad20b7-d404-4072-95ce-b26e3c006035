<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>個人電腦特殊權限申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }

        .qhUserIcon1 {
            /*background: url('../../../../../static/images/qhUser.png') no-repeat center;*/
            background: url('/newEsign/static/images/qhUser.png') no-repeat center;
            width: 30px;
            height: 30px;
            border: none;
            cursor: pointer;
        }

        .internalmailDiv {
            float: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfpcprivilegeprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfpcprivilegeprocessEntity.id }"/>
    <input id="serialno" name="wfpcprivilegeprocess.serialno" type="hidden"
           value="${wfpcprivilegeprocessEntity.serialno }"/>
    <input id="makerno" name="wfpcprivilegeprocess.makerno" type="hidden"
           value="${wfpcprivilegeprocessEntity.makerno }"/>
    <input id="makername" name="wfpcprivilegeprocess.makername" type="hidden"
           value="${wfpcprivilegeprocessEntity.makername }"/>
    <input id="makerdeptno" name="wfpcprivilegeprocess.makerdeptno" type="hidden"
           value="${wfpcprivilegeprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfpcprivilegeprocess.makerfactoryid" type="hidden"
           value="${wfpcprivilegeprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">個人電腦特殊權限申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfpcprivilegeprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfpcprivilegeprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfpcprivilegeprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfpcprivilegeprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfpcprivilegeprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfpcprivilegeprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfpcprivilegeprocessEntity.makerno}/${wfpcprivilegeprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="10%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="dealno" name="wfpcprivilegeprocess.dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="<c:if test="${wfpcprivilegeprocessEntity.dealno!=null&&wfpcprivilegeprocessEntity.dealno!=''}">${wfpcprivilegeprocessEntity.dealno}</c:if>
                                       <c:if test="${wfpcprivilegeprocessEntity.dealno==null||wfpcprivilegeprocessEntity.dealno==''}">${user.loginName}</c:if>"
                                       onchange="queryUserInfo(this);"/>
                            </td>
                            <td width="8%">承辦人</td>
                            <td width="10%" class="td_style1">
                                <input id="dealname" name="wfpcprivilegeprocess.dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wfpcprivilegeprocessEntity.dealname }"/>
                            </td>
                            <td width="10%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="dealdeptno" name="wfpcprivilegeprocess.dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfpcprivilegeprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="7%">費用代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="applycostno" name="wfpcprivilegeprocess.applycostno"
                                       class="easyui-validatebox inputCss" style="width:90px;"
                                       value="${wfpcprivilegeprocessEntity.applycostno }"/>
                            </td>
                            <td width="10%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="15%" class="td_style1">
                                <input id="applyfactoryid" name="wfpcprivilegeprocess.applyfactoryid"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${wfpcprivilegeprocessEntity.applyfactoryid }"
                                       data-options="width: 120,required:true,onSelect:function(){onchangeFactory('applyfactoryid');}"/>
                                <input id="applynofactoryid" name="wfpcprivilegeprocess.applynofactoryid" type="hidden"
                                       value="${wfpcprivilegeprocessEntity.applynofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="wfpcprivilegeprocess.applyleveltype"
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfpcprivilegeprocessEntity.applyleveltype }" readonly/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="wfpcprivilegeprocess.applymanager"
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfpcprivilegeprocessEntity.applymanager }" readonly/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="wfpcprivilegeprocess.dealemail" class="easyui-validatebox"
                                       value="${wfpcprivilegeprocessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td>使用區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyarea" name="wfpcprivilegeprocess.applyarea" class="easyui-combobox"
                                       data-options="width: 70,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wfpcprivilegeprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="wfpcprivilegeprocess.applybuilding"
                                       class="easyui-combobox"
                                       data-options="width: 70,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wfpcprivilegeprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealtel" name="wfpcprivilegeprocess.dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfpcprivilegeprocessEntity.dealtel}"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="dealdeptname" name="wfpcprivilegeprocess.dealdeptname"
                                       class="easyui-validatebox" data-options="width: 410,required:true"
                                       value="${wfpcprivilegeprocessEntity.dealdeptname }"/>
                            </td>
                            <td>安保區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style2">
                                <div class="securityareaDiv"></div>
                                <input id="securityarea" name="wfpcprivilegeprocess.securityarea"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfpcprivilegeprocessEntity.securityarea }"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="6" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">申請操作&nbsp;<font color="red">*</font></td>
                            <td class="td_style2" colspan="4">
                                <div class="applyoperateDiv"></div>
                                <input id="applyoperate" name="wfpcprivilegeprocess.applyoperate"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfpcprivilegeprocessEntity.applyoperate }"/>
                            </td>
                        </tr>
                        <tbody id="checkBody">
                        <tr align="center">
                            <td width="10%">申請類型</td>
                            <td width="10%">申請項目</td>
                            <td width="20%">設備類別</td>
                            <td width="15%">設備編號</td>
                            <td width="32%">DL管控</td>
                            <td width="13%">有效期</td>
                        </tr>
                        <tr align="center">
                            <td rowspan="4">
                                <input type="radio" class="lbox" id="USBId" onclick="isCheck(this)"
                                       name="wfpcprivilegeprocess.usbapplytype"  value="${wfpcprivilegeprocessEntity.usbapplytype}"
                                        <c:if test="${wfpcprivilegeprocessEntity.usbapplytype=='1'}">
                                            checked
                                        </c:if>
                                /><span>USB</span>
                            </td>
                            <td>
                                <input type="radio" name="wfpcprivilegeprocess.applyproject" class="applyProject" id="connectDevice"
                                       onclick="isCheck1(this)" value="${wfpcprivilegeprocessEntity.applyproject}"
                                        <c:if test="${wfpcprivilegeprocessEntity.applyproject=='1'}">
                                            checked
                                        </c:if>
                                /><span>連接設備</span>
                            </td>
                            <td>
                                <div style="margin-top:3px;float:left;display:inline-block">
                                    <input id="connectdevicetype" name="wfpcprivilegeprocess.connectdevicetype" type="text" class="easyui-validatebox"
                                           style="width:150px;" value="${wfpcprivilegeprocessEntity.connectdevicetype}"/>
                                    <font color="red">*</font>
                                </div>
                                <div class="qhUserIcon1" id="selectDeviceShow" style="float:left;" onclick="">

                                </div>
                                <div class="qhUserIcon1" id="selectDeviceHide" style="display:none"
                                     onclick="selectDeviceType('connectDevice');">
                                </div>
                            </td>
                            <td>
                                <input id="connectdeviceno" name="wfpcprivilegeprocess.connectdeviceno" type="text" class="easyui-validatebox"
                                       style="width:150px;" value="${wfpcprivilegeprocessEntity.connectdeviceno}"/>
                                <font color="red">*</font>
                            </td>
                            <td>
                                <div class="connectdevicedlDiv"></div>
                                <input id="connectdevicedl" name="wfpcprivilegeprocess.connectdevicedl"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfpcprivilegeprocessEntity.connectdevicedl}"/>
                            </td>
                            <td>...</td>
                        </tr>
                        <tr align="center">
                            <td>
                                <input type="radio" name="wfpcprivilegeprocess.applyproject" class="applyProject" id="readPrivilege"
                                       onclick="isCheck2(this)" value="${wfpcprivilegeprocessEntity.applyproject}"
                                        <c:if test="${wfpcprivilegeprocessEntity.applyproject=='2'}">
                                            checked
                                        </c:if>
                                /><span>只讀權限</span>
                            </td>
                            <td>
                                <div style="margin-top:3px;float:left;display:inline-block">
                                    <input id="readdevicetype" name="wfpcprivilegeprocess.readdevicetype" type="text" class="easyui-validatebox"
                                           style="width:150px;" value="${wfpcprivilegeprocessEntity.readdevicetype}"/>
                                    <font color="red">*</font>
                                </div>
                                <div class="qhUserIcon1" id="readDeviceShow" style="float:left;" onclick=""></div>
                                <div class="qhUserIcon1" style="float:left;display:none" id="readDeviceHide"
                                     onclick="selectDeviceType('readDevice');"></div>
                            </td>
                            <td>
                                <input id="readdeviceno" name="wfpcprivilegeprocess.readdeviceno" type="text" class="easyui-validatebox"
                                       style="width:150px;" value="${wfpcprivilegeprocessEntity.readdeviceno}"/>
                                <font color="red">*</font>
                            </td>
                            <td>
                                <div class="readdevicedlDiv"></div>
                                <input id="readdevicedl" name="wfpcprivilegeprocess.readdevicedl"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfpcprivilegeprocessEntity.readdevicedl}"/>
                            </td>
                            <td>六個月</td>
                        </tr>
                        <tr align="center">
                            <td>
                                <input type="radio" name="wfpcprivilegeprocess.applyproject" class="applyProject" id="savePrivilege"
                                       onclick="isCheck3(this)" value="${wfpcprivilegeprocessEntity.applyproject}"
                                        <c:if test="${wfpcprivilegeprocessEntity.applyproject=='3'}">
                                            checked
                                        </c:if>
                                /><span>存儲權限</span>
                            </td>
                            <td>
                                <div style="margin-top:3px;float:left;display:inline-block">
                                    <input id="savedevicetype" name="wfpcprivilegeprocess.savedevicetype" type="text" class="easyui-validatebox"
                                           style="width:150px;" value="${wfpcprivilegeprocessEntity.savedevicetype}"/>
                                    <font color="red">*</font>
                                </div>
                                <div class="qhUserIcon1" id="saveDeviceShow" style="float:left;" onclick=""></div>
                                <div class="qhUserIcon1" id="saveDeviceHide" style="float:left;display:none"
                                     onclick="selectDeviceType('saveDevice');"></div>
                            </td>
                            <td>
                                <input id="savedeviceno" name="wfpcprivilegeprocess.savedeviceno" type="text" class="easyui-validatebox"
                                       style="width:150px;" value="${wfpcprivilegeprocessEntity.savedeviceno}"/>
                                <font color="red">*</font>
                            </td>
                            <td>
                                <div class="savedevicedlDiv"></div>
                                <input id="savedevicedl" name="wfpcprivilegeprocess.savedevicedl"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfpcprivilegeprocessEntity.savedevicedl}"/>
                            </td>
                            <td>六個月</td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">需求說明：</td>
                            <td colspan="3" class="td_style2">
                                <input id="usbdesc" name="wfpcprivilegeprocess.usbdesc" type="text" class="easyui-validatebox"
                                       style="width:500px;" value="${wfpcprivilegeprocessEntity.usbdesc}"/>
                                <font color="red">*</font>
                            </td>
                        </tr>
                        </tbody>
                        <%--<tbody id="shellBody">
                        <tr align="center">
                            <td rowspan="4">
                                <input type="radio" class="lbox" id="shellApplyTypeId" onclick="isCheck4(this)"
                                       name="wfpcprivilegeprocess.shellapplytype"  value="${wfpcprivilegeprocessEntity.shellapplytype}"
                                        <c:if test="${wfpcprivilegeprocessEntity.shellapplytype=='1'}">
                                            checked
                                        </c:if>
                                /><span>外設</span>
                            </td>
                            <td>申請項目</td>
                            <td colspan="4">需求說明</td>
                        </tr>
                        <tr align="center">
                            <td>
                                <input type="checkbox" class="lbox" name="wfpcprivilegeprocess.cdrom" value="${wfpcprivilegeprocessEntity.cdrom}" id="cdRomId"
                                       onclick="cdRomClick(this);"
                                       <c:if test="${wfpcprivilegeprocessEntity.cdrom=='1'}">checked</c:if>/><span>CD-ROM</span>
                            </td>
                            <td colspan="4" class="td_style2">
                                <input id="cdromdesc" name="wfpcprivilegeprocess.cdromdesc" class="easyui-validatebox" type="text"
                                       style="width:800px;" value="${wfpcprivilegeprocessEntity.cdromdesc}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>
                                <input type="checkbox" class="lbox" name="wfpcprivilegeprocess.floppydrive" value="${wfpcprivilegeprocessEntity.floppydrive}" id="floppyDriveId"
                                       onclick="cdRomClick(this);"
                                       <c:if test="${wfpcprivilegeprocessEntity.floppydrive=='1'}">checked</c:if>/><span>軟驅</span>
                            </td>
                            <td colspan="4" class="td_style2">
                                <input id="floppydrivedesc" name="wfpcprivilegeprocess.floppydrivedesc" class="easyui-validatebox" type="text"
                                       style="width:800px;" value="${wfpcprivilegeprocessEntity.floppydrivedesc}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>
                                <input type="checkbox" class="lbox" name="wfpcprivilegeprocess.soundcard" value="${wfpcprivilegeprocessEntity.soundcard}" id="soundCardId"
                                       onclick="cdRomClick(this);"
                                       <c:if test="${wfpcprivilegeprocessEntity.soundcard=='1'}">checked</c:if>/><span>聲卡</span>
                            </td>
                            <td colspan="4" class="td_style2">
                                <input id="soundcarddesc" name="wfpcprivilegeprocess.soundcarddesc" class="easyui-validatebox" type="text"
                                       style="width:800px;" value="${wfpcprivilegeprocessEntity.soundcarddesc}"/>
                            </td>
                        </tr>
                        </tbody>--%>
                        <tbody id="specialBody">
                        <tr align="center">
                            <td rowspan="15">
                                <input type="radio" class="lbox" id="specialPrivilegeId" onclick="isCheck5(this)"
                                       name="wfpcprivilegeprocess.specialprivilege"  value="${wfpcprivilegeprocessEntity.specialprivilege}"
                                        <c:if test="${wfpcprivilegeprocessEntity.specialprivilege=='1'}">
                                            checked
                                        </c:if>
                                /><span>電腦策略</span>
                            </td>
                            <td>申請權限</td>
                            <td colspan="4">其他信息</td>
                        </tr>
                        <tr align="center">
                            <td rowspan="7" class="td_style2">
                                <div class="accountprivilegeDiv"></div>
                                <input id="accountprivilege" name="wfpcprivilegeprocess.accountprivilege"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfpcprivilegeprocessEntity.accountprivilege}"/>

                            </td>
                            <td colspan="2" class="td_style2">
                                <input type="checkbox" class="lbox" name="wfpcprivilegeprocess.specialsoft"
                                       value="${wfpcprivilegeprocessEntity.specialsoft}" id="specialSoftId"
                                       onclick="specialSoftCheck(this);"
                                       <c:if test="${wfpcprivilegeprocessEntity.specialsoft=='1'}">checked</c:if>/><span>特殊軟件使用</span>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                                <input type="checkbox" class="lbox" name="wfpcprivilegeprocess.specialdevice" value="${wfpcprivilegeprocessEntity.specialdevice}"
                                       id="specialDeviceId" onclick="specialDeviceCheck(this);"
                                       <c:if test="${wfpcprivilegeprocessEntity.specialdevice=='1'}">checked</c:if>/><span>特殊設備使用</span>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                                <input type="checkbox" class="lbox" name="wfpcprivilegeprocess.accountother" onclick="isCheck6(this);"
                                       value="${wfpcprivilegeprocessEntity.accountother}" id="accountOtherId"
                                       <c:if test="${wfpcprivilegeprocessEntity.accountother=='1'}">checked</c:if>/><span>其他</span>
                            </td>
                            <td colspan="2" class="td_style2">
                                <input id="accountotherdesc" name="wfpcprivilegeprocess.accountotherdesc" class="easyui-validatebox"
                                       style="width:500px;" value="${wfpcprivilegeprocessEntity.accountotherdesc}"/>
                                <font color="red">*</font>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>軟件名稱</td>
                            <td>版本</td>
                            <td>供應商名稱</td>
                            <td>是否有版權</td>
                        </tr>
                        <tr align="center">
                            <td class="td_style2">
                                <input id="softname" name="wfpcprivilegeprocess.softname" class="easyui-validatebox" type="text"
                                       style="width:200px;" value="${wfpcprivilegeprocessEntity.softname}"/>
                                <font color="red">*</font>
                            </td>
                            <td class="td_style2">
                                <input id="softversion" name="wfpcprivilegeprocess.softversion" class="easyui-validatebox" type="text"
                                       style="width:150px;" value="${wfpcprivilegeprocessEntity.softversion}"/>
                                <font color="red">*</font>
                            </td>
                            <td>
                                <input id="softsupply" name="wfpcprivilegeprocess.softsupply" class="easyui-validatebox" type="text"
                                       style="width:200px;" value="${wfpcprivilegeprocessEntity.softsupply}"/>
                            </td>
                            <td>
                                <div class="softcopyrightDiv"></div>
                                <input id="softcopyright" name="wfpcprivilegeprocess.softcopyright"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfpcprivilegeprocessEntity.softcopyright}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>設備名稱</td>
                            <td>型號</td>
                            <td>供應商名稱</td>
                            <td>製造序列號</td>
                        </tr>
                        <tr align="center">
                            <td class="td_style2">
                                <input id="devicename" name="wfpcprivilegeprocess.devicename" class="easyui-validatebox" type="text"
                                       style="width:200px;" value="${wfpcprivilegeprocessEntity.devicename}"/>
                                <font color="red">*</font>
                            </td>
                            <td class="td_style2">
                                <input id="deviceversion" name="wfpcprivilegeprocess.deviceversion" class="easyui-validatebox" type="text"
                                       style="width:150px;" value="${wfpcprivilegeprocessEntity.deviceversion}"/>
                                <font color="red">*</font>
                            </td>
                            <td>
                                <input id="devicesupply" name="wfpcprivilegeprocess.devicesupply" class="easyui-validatebox" type="text"
                                       style="width:200px;" value="${wfpcprivilegeprocessEntity.devicesupply}"/>
                            </td>
                            <td>
                                <input id="deviceserialnum" name="wfpcprivilegeprocess.deviceserialnum" class="easyui-validatebox" type="text"
                                       style="width:130px;" value="${wfpcprivilegeprocessEntity.deviceserialnum}"/>
                                <font color="red">*</font>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="4">特殊需求用途詳細說明</td>
                        </tr>
                        <tr align="center">
                            <td colspan="4" class="td_style2">
                                <input id="accountspecialdesc" name="wfpcprivilegeprocess.accountspecialdesc" class="easyui-validatebox" type="text"
                                       style="width:800px;" value="${wfpcprivilegeprocessEntity.accountspecialdesc}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td rowspan="7">
                                <input type="checkbox" class="lbox" id="shareId" onclick="shareCheck(this);"
                                       name="wfpcprivilegeprocess.shareprivilege" value="${wfpcprivilegeprocessEntity.shareprivilege}"
                                       <c:if test="${wfpcprivilegeprocessEntity.shareprivilege=='1'}">checked</c:if>/><span>共享</span>
                            </td>
                            <td colspan="2" class="td_style2">
                                <input type="checkbox" class="lbox" name="wfpcprivilegeprocess.sharespecialsoft"
                                       value="${wfpcprivilegeprocessEntity.sharespecialsoft}" id="shareSpecialSoftId"
                                       onclick="specialSoftCheck(this);"
                                       <c:if test="${wfpcprivilegeprocessEntity.sharespecialsoft=='1'}">checked</c:if>/><span>特殊軟件使用</span>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                                <input type="checkbox" class="lbox" name="wfpcprivilegeprocess.sharespecialdevice" value="${wfpcprivilegeprocessEntity.sharespecialdevice}"
                                       id="shareSpecialDeviceId" onclick="specialDeviceCheck(this);"
                                       <c:if test="${wfpcprivilegeprocessEntity.sharespecialdevice=='1'}">checked</c:if>/><span>特殊設備使用</span>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                                <input type="checkbox" class="lbox" name="wfpcprivilegeprocess.shareother" onclick="isCheck6(this);"
                                       value="${wfpcprivilegeprocessEntity.shareother}" id="shareOtherId"
                                       <c:if test="${wfpcprivilegeprocessEntity.shareother=='1'}">checked</c:if>/><span>其他</span>
                            </td>
                            <td colspan="2" class="td_style2">
                                <input id="shareotherdesc" name="wfpcprivilegeprocess.shareotherdesc" class="easyui-validatebox"
                                       style="width:500px;" value="${wfpcprivilegeprocessEntity.shareotherdesc}"/>
                                <font color="red">*</font>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>軟件名稱</td>
                            <td>版本</td>
                            <td>供應商名稱</td>
                            <td>是否有版權</td>
                        </tr>
                        <tr align="center">
                            <td class="td_style2">
                                <input id="sharesoftname" name="wfpcprivilegeprocess.sharesoftname" class="easyui-validatebox" type="text"
                                       style="width:200px;" value="${wfpcprivilegeprocessEntity.sharesoftname}"/>
                                <font color="red">*</font>
                            </td>
                            <td class="td_style2">
                                <input id="sharesoftversion" name="wfpcprivilegeprocess.sharesoftversion" class="easyui-validatebox" type="text"
                                       style="width:150px;" value="${wfpcprivilegeprocessEntity.sharesoftversion}"/>
                                <font color="red">*</font>
                            </td>
                            <td>
                                <input id="sharesoftsupply" name="wfpcprivilegeprocess.sharesoftsupply" class="easyui-validatebox" type="text"
                                       style="width:200px;" value="${wfpcprivilegeprocessEntity.sharesoftsupply}"/>
                            </td>
                            <td>
                                <div class="sharesoftcopyrightDiv"></div>
                                <input id="sharesoftcopyright" name="wfpcprivilegeprocess.sharesoftcopyright"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfpcprivilegeprocessEntity.sharesoftcopyright}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>設備名稱</td>
                            <td>型號</td>
                            <td>供應商名稱</td>
                            <td>製造序列號</td>
                        </tr>
                        <tr align="center">
                            <td class="td_style2">
                                <input id="sharedevicename" name="wfpcprivilegeprocess.sharedevicename" class="easyui-validatebox" type="text"
                                       style="width:200px;" value="${wfpcprivilegeprocessEntity.sharedevicename}"/>
                                <font color="red">*</font>
                            </td>
                            <td class="td_style2">
                                <input id="sharedeviceversion" name="wfpcprivilegeprocess.sharedeviceversion" class="easyui-validatebox" type="text"
                                       style="width:150px;" value="${wfpcprivilegeprocessEntity.sharedeviceversion}"/>
                                <font color="red">*</font>
                            </td>
                            <td>
                                <input id="sharedevicesupply" name="wfpcprivilegeprocess.sharedevicesupply" class="easyui-validatebox" type="text"
                                       style="width:200px;" value="${wfpcprivilegeprocessEntity.sharedevicesupply}"/>
                            </td>
                            <td>
                                <input id="sharedeviceserialnum" name="wfpcprivilegeprocess.sharedeviceserialnum" class="easyui-validatebox" type="text"
                                       style="width:130px;" value="${wfpcprivilegeprocessEntity.sharedeviceserialnum}"/>
                                <font color="red">*</font>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="4">特殊需求用途詳細說明</td>
                        </tr>
                        <tr align="center">
                            <td colspan="4" class="td_style2">
                                <input id="sharespecialdesc" name="wfpcprivilegeprocess.sharespecialdesc" class="easyui-validatebox" type="text"
                                       style="width:800px;" value="${wfpcprivilegeprocessEntity.sharespecialdesc}"/>
                            </td>
                        </tr>
                        </tbody>
                        <tbody id="remoteendBody">
                        <tr align="center">
                            <td rowspan="15">
                                <input type="radio" class="lbox" id="remoteendPrivilegeId" onclick="isCheck7(this)"
                                       name="wfpcprivilegeprocess.remoteendprivilege"  value="${wfpcprivilegeprocessEntity.remoteendprivilege}"
                                        <c:if test="${wfpcprivilegeprocessEntity.remoteendprivilege=='1'}">
                                            checked
                                        </c:if>
                                /><span>遠程連線</span>
                            </td>
                            <td colspan="5" class="td_style2">申請開通時間：
                                <input id="pcpstarttime" name="wfpcprivilegeprocess.pcpstarttime" class="Wdate" data-options="width:150"
                                       style="width:150px"  value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfpcprivilegeprocessEntity.pcpstarttime}"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />至
                                <input id="pcpendtime" name="wfpcprivilegeprocess.pcpendtime" class="Wdate" data-options="width:150"
                                       style="width:150px" value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfpcprivilegeprocessEntity.pcpendtime}"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'pcpstarttime\')}',maxDate:'#F{$dp.$D(\'pcpstarttime\',{M:6})}'})" />
                                有效期最長6個月
                            </td>
                        </tr>
                        <tr align="center">
                            <td rowspan="2" colspan="5" class="td_style2">
                                需求說明：
                                <input id="remotedescription" name="wfpcprivilegeprocess.remotedescription" class="easyui-validatebox" type="text"
                                       style="width:800px;" value="${wfpcprivilegeprocessEntity.remotedescription}"/>
                            </td>
                        </tr>

                        </tbody>
                        <tr>
                            <td colspan="10" class="td_style1">申請人明細</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <input id="pcprivilegeItemTableIndex" type="hidden"
                                           value="<c:if test="${pcprivilegeitems!=null && pcprivilegeitems.size()>0}">${pcprivilegeitems.size() +1}</c:if>
                                        <c:if test="${pcprivilegeitems.size()==0 || pcprivilegeitems==null}">2</c:if>"/>
                                    </input>
                                    <table id="pcprivilegeItemTable" width="100%">
                                        <tr align="center">
                                            <td width="10%">申請人工號&nbsp;<font color="red">*</font></td>
                                            <td width="10%">申請人</td>
                                            <td width="10%">所在廠區&nbsp;<font color="red">*</font></td>
                                            <td width="15%">使用區域&nbsp;<font color="red">*</font></td>
                                            <td width="10%">IP地址&nbsp;<font color="red">*</font></td>
                                            <td width="10%">是否NPI&nbsp;<font color="red">*</font></td>
                                            <td width="15%">電腦名稱&nbsp;<font color="red">*</font></td>
                                            <td width="10%">遠端/被遠端</td>
                                            <td width="5%">&nbsp;操作&nbsp;</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${pcprivilegeitems!=null&&pcprivilegeitems.size()>0}">
                                            <c:forEach items="${pcprivilegeitems}" var="pcprivilegeitems" varStatus="status">
                                                <tr align="center" id="pcprivilegeitems${status.index+1}">
                                                    <td>
                                                        <input id="pc_applyno${status.index+1}" onblur="getUserNameByEmpno2(this,status.index+1);"
                                                               name="wfpcprivilegeitems[${status.index+1}].applyno" class="easyui-validatebox" style="width:80px;"
                                                               data-options="required:true" value="${pcprivilegeitems.applyno}"/>
                                                    </td>
                                                    <td>
                                                        <input id="pc_applyname${status.index+1}" name="wfpcprivilegeitems[${status.index+1}].applyname"
                                                               class="easyui-validatebox inputCss" style="width:80px;" readonly value="${pcprivilegeitems.applyname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="pc_applyfactoryid${status.index+1}" name="wfpcprivilegeitems[${status.index+1}].applyfactoryid"
                                                               data-options="panelHeight:300,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'pc_applyfactoryid${status.index+1}\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(${status.index+1});},onSelect:function(){pcOnchangeFactory('pc_applyfactoryid${status.index+1}',${status.index+1});}"
                                                               style="width:100px;" class="easyui-combobox" value="${pcprivilegeitems.applyfactoryid}"/>
                                                    </td>
                                                    <td>
                                                        <input id="pc_applyarea${status.index+1}" name="wfpcprivilegeitems[${status.index+1}].applyarea"
                                                               data-options="validType:'comboxValidate[\'pc_applyarea${status.index+1}\',\'请選擇區域\']',onSelect:function(){pcOnchangeArea('pc_applyarea${status.index+1}',${status.index+1});}"
                                                               class="easyui-combobox" style="width:80px;" value="${pcprivilegeitems.applyarea}" panelHeight="auto"/>/
                                                        <input id="pc_applybuilding${status.index+1}" name="wfpcprivilegeitems[${status.index+1}].applybuilding"
                                                               data-options="validType:'comboxValidate[\'pc_applybuilding${status.index+1}\',\'请選擇樓棟\']'"
                                                               class="easyui-combobox" style="width:80px;" value="${pcprivilegeitems.applybuilding}" panelHeight="auto"/>
                                                    </td>
                                                    <td>
                                                        <input id="pc_applyip${status.index+1}" name="wfpcprivilegeitems[${status.index+1}].applyip" onblur="isNpiCheck(this,${status.index+1});"
                                                               class="easyui-validatebox" data-options="required:true" style="width:100px" value="${pcprivilegeitems.applyip}"/>
                                                    </td>
                                                    <td>
                                                       <%-- <div class="pc_securityareaDiv${status.index+1}"></div>
                                                        <input id="pc_securityarea${status.index+1}" name="wfpcprivilegeitems[${status.index+1}].securityarea"
                                                               readonly type="hidden" class="easyui-validatebox"
                                                               style="width:50px;" value="${pcprivilegeitems.securityarea}"/>--%>
                                                           <input id="pc_securityarea${status.index+1}" name="wfpcprivilegeitems[${status.index+1}].securityarea" class="easyui-validatebox inputCss" readonly
                                                                  data-options="width: 60" value="${pcprivilegeitems.securityarea}"/>
                                                    </td>
                                                    <td>
                                                        <input id="pc_pcname${status.index+1}" name="wfpcprivilegeitems[${status.index+1}].pcname"
                                                               class="easyui-validatebox" style="width:100px;" data-options="required:true" value="${pcprivilegeitems.pcname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="pc_remoteend${status.index+1}" name="wfpcprivilegeitems[${status.index+1}].remoteend" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,validType:'comboxValidate[\'pc_remoteend${status.index+1}\',\'请選擇遠端類型\']',onBeforeLoad:function(){loadRemoteend(${status.index+1});}" style="width:80px;"
                                                               class="easyui-combobox" editable="false" value="${pcprivilegeitems.remoteend}"/>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="pcdeltr(${status.index+1});return false;"/>
                                                        <input id="pc_shunxu${status.index+1}" type="hidden" name="wfpcprivilegeitems[${status.index+1}].shunxu" value="${status.index+1}"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${pcprivilegeitems.size()==0 || pcprivilegeitems==null}">
                                            <tr align="center" id="pcprivilegeitems1">
                                                <td>
                                                    <input id="pc_applyno1" onblur="getUserNameByEmpno2(this,1);" data-options="required:true" name="wfpcprivilegeitems[1].applyno" class="easyui-validatebox" style="width:80px;" value=""/>
                                                </td>
                                                <td>
                                                    <input id="pc_applyname1" name="wfpcprivilegeitems[1].applyname" class="easyui-validatebox inputCss" style="width:80px;" readonly value=""/>
                                                </td>
                                                <td>
                                                    <input id="pc_applyfactoryid1" name="wfpcprivilegeitems[1].applyfactoryid"
                                                           data-options="panelHeight:300,valueField:'factoryid',textField:'factoryname',editable:false,validType:'comboxValidate[\'pc_applyfactoryid1\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(1);},onSelect:function(){pcOnchangeFactory('pc_applyfactoryid1',1);}"
                                                           style="width:100px;" class="easyui-combobox" value=""/>
                                                </td>
                                                <td>
                                                    <input id="pc_applyarea1" name="wfpcprivilegeitems[1].applyarea" class="easyui-combobox" style="width:80px;"
                                                           data-options="validType:'comboxValidate[\'pc_applyarea1\',\'请選擇區域\']',onSelect:function(){pcOnchangeArea('pc_applyarea1',1);}"
                                                           value="" panelHeight="auto"/>/
                                                    <input id="pc_applybuilding1" name="wfpcprivilegeitems[1].applybuilding" class="easyui-combobox" style="width:80px;"
                                                           data-options="validType:'comboxValidate[\'pc_applybuilding1\',\'请選擇樓棟\']'"
                                                           value="" panelHeight="auto"/>
                                                </td>
                                                <td>
                                                    <input id="pc_applyip1" name="wfpcprivilegeitems[1].applyip" class="easyui-validatebox" onblur="isNpiCheck(this,1);" data-options="required:true"
                                                           style="width:100px;" value=""/>
                                                </td>
                                                <td>
                                                   <%-- <div class="pc_securityareaDiv1"></div>
                                                    <input id="pc_securityarea1" name="wfpcprivilegeitems[1].securityarea" readonly type="hidden"
                                                           class="easyui-validatebox" style="width:50px;" value=""/>--%>
                                                    <input id="pc_securityarea1" name="wfpcprivilegeitems[1].securityarea" class="easyui-validatebox inputCss" readonly
                                                              data-options="width: 60" value=""/>
                                                </td>
                                                <td>
                                                    <input id="pc_pcname1" name="wfpcprivilegeitems[1].pcname" data-options="required:true" class="easyui-validatebox" style="width:100px;" value=""/>
                                                </td>
                                                <td>
                                                    <input id="pc_remoteend1" name="wfpcprivilegeitems[1].remoteend" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,validType:'comboxValidate[\'pc_remoteend1\',\'请選擇遠端類型\']',onBeforeLoad:function(){loadRemoteend(1);}" style="width:80px;"
                                                           class="easyui-combobox" editable="false" value=""/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="pcdeltr(1);return false;"/>
                                                    <input id="pc_shunxu1" type="hidden" name="wfpcprivilegeitems[1].shunxu" value="1"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr0">
                                            <td colspan="8" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="pcItemAdd" style="width:100px;float:left;" value="添加一筆"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file"
                                           onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="wfpcprivilegeprocess.attachids"
                                       value="${wfpcprivilegeprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">備註</td>
                            <td width="90%" class="td_style1">
                                <%--<input id="applyMemo" type="hidden" value="${applyMemo}"/>
                                <textarea id="applyTextareaMemo" class="easyui-validatebox"
                                          disabled readonly
                                          style="width:99%;resize:none;background-color: #F2F5F7;border: 0px;outline: none;">${applyMemo}</textarea>--%>
                                1.申請遠程連線權限需附出差單，根據出差時間決定遠程權限有效期，到期自動清除權限；最長使用期限六個月，到期後請填單續期；<br/>
                                2.申請遠程連線權限時需上傳兩地運維責任人簽核的<a href="${ctx}/ossAdmin/download/497ce4eeba8c4937af8b8d3558ccba15">Checklist</a>；<br/>
                                    <font color="red">3.個人電腦特殊權限申請單需核准至機能處/製造處級主管；</font><br/>
                                    4.DBA確認請同時選擇遠程與被遠程兩廠區服務器管理員（可多選）。
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','個人電腦特殊權限申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno2_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'yl2Table','ylno2','ylname2',$('#applyfactoryid').combobox('getValue'),'wfpcprivilegeprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="wfpcprivilegeprocess.ylno2"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}"
                                                               readonly
                                                               value="${wfpcprivilegeprocessEntity.ylno2 }"/><c:if
                                                            test="${requiredMap['ylno2'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname2" name="wfpcprivilegeprocess.ylname2"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}"
                                                                value="${wfpcprivilegeprocessEntity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#dealdeptno').val(),'cchargeno','cchargename',$('#applynofactoryid').val(),'wfpcprivilegeprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wfpcprivilegeprocess.cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfpcprivilegeprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wfpcprivilegeprocess.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfpcprivilegeprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole33('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#applynofactoryid').val(),'wfpcprivilegeprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wfpcprivilegeprocess.zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfpcprivilegeprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="wfpcprivilegeprocess.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfpcprivilegeprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#applynofactoryid').val(),'wfpcprivilegeprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="wfpcprivilegeprocess.zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wfpcprivilegeprocessEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename"
                                                                name="wfpcprivilegeprocess.zcchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfpcprivilegeprocessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl15Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno15_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(25,'yl15Table','ylno15','ylname15',$('#applyfactoryid').combobox('getValue'),'wfpcprivilegeprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno15" name="wfpcprivilegeprocess.ylno15"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno15']}"
                                                               readonly
                                                               value="${wfpcprivilegeprocessEntity.ylno15 }"/><c:if
                                                            test="${requiredMap['ylno15'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname15" name="wfpcprivilegeprocess.ylname15" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno15']}"
                                                                value="${wfpcprivilegeprocessEntity.ylname15 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl14Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno14_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(154,'yl14Table','ylno14','ylname14',$('#applyfactoryid').combobox('getValue'),'wfpcprivilegeprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno14" name="wfpcprivilegeprocess.ylno14"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno14']}"
                                                               readonly
                                                               value="${wfpcprivilegeprocessEntity.ylno14 }"/><c:if
                                                            test="${requiredMap['ylno14'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname14" name="wfpcprivilegeprocess.ylname14"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno14']}"
                                                                value="${wfpcprivilegeprocessEntity.ylname14 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl10Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno10_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(155,'yl10Table','ylno10','ylname10',$('#applyfactoryid').combobox('getValue'),'wfpcprivilegeprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno10" name="wfpcprivilegeprocess.ylno10"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno10']}"
                                                               readonly
                                                               value="${wfpcprivilegeprocessEntity.ylno10 }"/><c:if
                                                            test="${requiredMap['ylno10'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname10" name="wfpcprivilegeprocess.ylname10"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno10']}"
                                                                value="${wfpcprivilegeprocessEntity.ylname10 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zacwchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zacwchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(193,'zacwchargeTable','zacwchargeno','zacwchargename',$('#applyfactoryid').combobox('getValue'),'wfpcprivilegeprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zacwchargeno"
                                                               name="wfpcprivilegeprocess.zacwchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zacwchargeno']}"
                                                               readonly
                                                               value="${wfpcprivilegeprocessEntity.zacwchargeno }"/>
                                                        <c:if test="${requiredMap['zacwchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        /<input id="zacwchargename"
                                                                name="wfpcprivilegeprocess.zacwchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zacwchargeno']}"
                                                                value="${wfpcprivilegeprocessEntity.zacwchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#dealdeptno').val(),'pcchargeno','pcchargename',$('#applynofactoryid').val(),'wfpcprivilegeprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="wfpcprivilegeprocess.pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${wfpcprivilegeprocessEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename"
                                                                name="wfpcprivilegeprocess.pcchargename" readonly
                                                                class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wfpcprivilegeprocessEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="dbazyTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['dbazyno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(176,'dbazyTable','dbazyno','dbazyname',$('#applyfactoryid').combobox('getValue'),'wfpcprivilegeprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="dbazyno" name="wfpcprivilegeprocess.dbazyno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['dbazyno']}"
                                                               readonly
                                                               value="${wfpcprivilegeprocessEntity.dbazyno }"/><c:if test="${requiredMap['dbazyno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="dbazyname" name="wfpcprivilegeprocess.dbazyname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['dbazyno']}"
                                                                value="${wfpcprivilegeprocessEntity.dbazyname }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfpcprivilegeprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;"
                                   onclick="canelTask('${wfpcprivilegeprocessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="areaId" name="areaId" value="" />
    <input type="hidden" id="buildingId" name="buildingId" value="" />
    <input id="disOrEnabled" type="hidden" value=""/>
    <input type="hidden" id="onlyKchargeSignle" value="1" />
    <input type="hidden" id="auditAndView" value="0" />
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
    <div id="dlg"></div>
</form>
</div>
<script src='${ctx}/static/js/information/wfpcprivilegeprocess.js?random=<%= Math.random()%>'></script>
<%--<script type="text/javascript">
    applyMemoHeight();
</script>--%>
</body>
</html>