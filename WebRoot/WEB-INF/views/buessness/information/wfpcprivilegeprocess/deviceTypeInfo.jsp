<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title></title>
	<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
	<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>

</head>
<body>
	<table class="formTable" style="width: 98%;">
		<tr>
			<td>
				<div class="devicetypesDiv"></div>
			</td>
		</tr>
	</table>
	<script type="text/javascript">
        var dg;
        var d;
        $(function(){
            var deviceType =$('#deviceType').val();
            var dict_name='';
            if(deviceType=='connectDevice'){
                dict_name='dict_connectDevice';
            }else if(deviceType=='readDevice'){
                dict_name='dict_readDevice';
            }else{
                dict_name='dict_readDevice';
            }

            //獲取設備類別
            $.ajax({
                type:"GET",
                dataType:'json',
                url:ctx+"/system/dict/getDictByType/"+dict_name,
                success: function (data) {
                    var html = '';
					$.each(data, function (i, v) {
						html += "<div style='width:130px;float:left;'><input type='radio' value='" + v.value + "' name='devicetypesName'/>" + v.label + "</div>";
					})
                    $(".devicetypesDiv").html(html);
                }
            });
        });
	</script>
</body>
</html>