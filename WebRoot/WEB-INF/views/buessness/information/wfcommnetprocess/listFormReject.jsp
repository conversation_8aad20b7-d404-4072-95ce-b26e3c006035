<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>通訊網絡服務申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfcommnetprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfcommnetProcessEntity.id }"/>
    <input id="serialno" name="wfcommnetProcess.serialno" type="hidden" value="${wfcommnetProcessEntity.serialno }"/>
    <!-- <input id="makerno" name="makerno" type="hidden" value="${wfcommnetProcessEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfcommnetProcessEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfcommnetProcessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden" value="${wfcommnetProcessEntity.makerfactoryid }"/> -->
    <div class="commonW">
        <div class="headTitle">通訊網絡服務申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfcommnetProcessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfcommnetProcessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfcommnetProcessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfcommnetProcessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfcommnetProcessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfcommnetProcessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfcommnetProcessEntity.makerno}/${wfcommnetProcessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <c:choose>
                            <c:when test="${not empty wfcommnetProcessEntity.makerno}">
                                <input type="hidden" id="isNew" value="0"/>
                                <tr align="center">
                                    <td width="6%">承辦人工號<font color="red">*</font></td>
                                    <td width="6%" class="td_style1">
                                        <input id="makerno" name="wfcommnetProcess.makerno" class="easyui-validatebox"
                                               data-options="width: 80,required:true"
                                               value="${wfcommnetProcessEntity.makerno}" onblur="loadDealUserInfo();"/>
                                    </td>
                                    <td width="4%">承辦人</td>
                                    <td width="6%" class="td_style1">
                                        <input id="makername" name="wfcommnetProcess.makername"
                                               class="easyui-validatebox inputCss"
                                               data-options="width:80" readonly
                                               value="${wfcommnetProcessEntity.makername}"/>
                                    </td>
                                    <td width="4%">單位代碼</td>
                                    <td width="6%" class="td_style1">
                                        <input id="makerdeptno" name="wfcommnetProcess.makerdeptno"
                                               class="easyui-validatebox inputCss"
                                               data-options="width:80" readonly
                                               value="${wfcommnetProcessEntity.makerdeptno}"/>
                                    </td>
                                    <td width="4%">所在廠區</td>
                                    <td width="7%" class="td_style1">
                                        <input id="makerfactoryid" name="wfcommnetProcess.makerfactoryid"
                                               class="easyui-combobox" disabled
                                               panelHeight="auto" value="${wfcommnetProcessEntity.applyFactoryId }"
                                               data-options="width: 120,required:true,validType:'comboxValidate[\'applyFactoryId\',\'请选择所在廠區\']'"/>
                                    </td>
                                    <td width="5%">聯繫分機&nbsp;<font color="red">*</font></td>
                                    <td width="5%" class="td_style1">
                                        <input id="makertel" name="wfcommnetProcess.makertel" class="easyui-validatebox"
                                               style="width:90px;"
                                               value="${wfcommnetProcessEntity.makertel}" data-options="required:true"
                                               onblur="valdApplyTel(this)"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td width="6%">單位<font color="red">*</font></td>
                                    <td colspan="5" class="td_style1">
                                        <input id="makerdeptname" name="wfcommnetProcess.makerdeptname"
                                               class="easyui-validatebox"
                                               data-options="width: 450,required:true"
                                               value="${wfcommnetProcessEntity.makerdeptname}"/>
                                    </td>
                                    <td width="4%">聯繫郵箱<font color="red">*</font></td>
                                    <td colspan="3" class="td_style1">
                                        <input id="makeremail" name="wfcommnetProcess.makeremail"
                                               class="easyui-validatebox"
                                               data-options="width: 300,required:true"
                                               value="${wfcommnetProcessEntity.makeremail}"/>
                                    </td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <input type="hidden" id="isNew" value="1"/>
                                <tr align="center">
                                    <td width="6%">承辦人工號&nbsp;<font color="red">*</font></td>
                                    <td width="6%" class="td_style1">
                                        <input id="makerno" name="wfcommnetProcess.makerno" class="easyui-validatebox"
                                               data-options="width: 80,required:true"
                                               value="${user.loginName}" onblur="loadDealUserInfo();"/>
                                    </td>
                                    <td width="4%">承辦人</td>
                                    <td width="6%" class="td_style1">
                                        <input id="makername" name="wfcommnetProcess.makername"
                                               class="easyui-validatebox inputCss"
                                               data-options="width:80" readonly value="${user.name}"/>
                                    </td>
                                    <td width="4%">單位代碼</td>
                                    <td width="6%" class="td_style1">
                                        <input id="makerdeptno" name="wfcommnetProcess.makerdeptno"
                                               class="easyui-validatebox inputCss"
                                               data-options="width:80" readonly value=""/>
                                    </td>
                                    <td width="4%">所在廠區</td>
                                    <td width="7%" class="td_style1">
                                        <input id="makerfactoryid" name="wfcommnetProcess.makerfactoryid"
                                               class="easyui-combobox"
                                               panelHeight="auto" value="" disabled
                                               data-options="width: 120,required:true,validType:'comboxValidate[\'applyFactoryId\',\'请选择所在廠區\']'"/>
                                    </td>
                                    <td width="4%">聯繫分機&nbsp;<font color="red">*</font></td>
                                    <td width="10%" class="td_style1">
                                        <input id="makertel" name="wfcommnetProcess.makertel" class="easyui-validatebox"
                                               style="width:90px;"
                                               value="${user.phone}" data-options="required:true"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td width="6%">單位&nbsp;<font color="red">*</font></td>
                                    <td colspan="5" class="td_style1">
                                        <input id="makerdeptname" name="wfcommnetProcess.makerdeptname"
                                               class="easyui-validatebox"
                                               data-options="width: 450,required:true"
                                               value=""/>
                                    </td>
                                    <td width="4%">聯繫郵箱&nbsp;<font color="red">*</font></td>
                                    <td colspan="3" class="td_style1">
                                        <input id="makeremail" name="wfcommnetProcess.makeremail"
                                               class="easyui-validatebox"
                                               data-options="width: 300,required:true"
                                               value="${user.email}"/>
                                    </td>
                                </tr>
                            </c:otherwise>
                        </c:choose>
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="wfcommnetProcess.applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfcommnetProcessEntity.applyno}" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="5%">申請人</td>
                            <td width="5%" class="td_style1">
                                <input id="applyname" name="wfcommnetProcess.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfcommnetProcessEntity.applyname}"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydepartno" name="wfcommnetProcess.applydepartno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfcommnetProcessEntity.applydepartno}"/>
                            </td>
                            <td width="4%">費用代碼&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applycostno" name="wfcommnetProcess.applycostno"
                                       class="easyui-validatebox" data-options="width: 90,required:true"
                                       value="${wfcommnetProcessEntity.applycostno}"/>
                            </td>
                            <td width="4%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="applyFactoryId" name="wfcommnetProcess.applyFactoryId"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${wfcommnetProcessEntity.applyFactoryId }"
                                       data-options="width: 120,required:true,onSelect:function(){onchangeFactory();},validType:'comboxValidate[\'applyFactoryId\',\'请选择所在廠區\']'"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="6%">資位</td>
                            <td width="6%" class="td_style1">
                                <input id="applyLevel" name="wfcommnetProcess.applyLevel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfcommnetProcessEntity.applyLevel}" data-options="required:true"
                                />
                            </td>
                            <td width="4%">管理職</td>
                            <td width="6%" class="td_style1">
                                <input id="applyPost" name="wfcommnetProcess.applyPost" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfcommnetProcessEntity.applyPost}" data-options="required:true"
                                />
                            </td>
                            <td width="4%">聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="wfcommnetProcess.applyemail" class="easyui-validatebox"
                                       style="width:350px;"
                                       value="${wfcommnetProcessEntity.applyemail}" data-options="required:true"
                                />
                            </td>
                            <td width="4%">使用區域&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="applyArea" name="wfcommnetProcess.applyArea" class="easyui-combobox"
                                       data-options="width: 90,onSelect:function(){onchangeArea();},validType:'comboxValidate[\'applyArea\',\'请选择區域\']'"
                                       value="${wfcommnetProcessEntity.applyArea}" panelHeight="auto"/>&nbsp;/
                                <input id="applyFloor" name="wfcommnetProcess.applyFloor"
                                       class="easyui-combobox"
                                       data-options="width: 60,validType:'comboxValidate[\'applyFloor\',\'请选择樓層\']'"
                                       value="${wfcommnetProcessEntity.applyFloor}" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="6%">聯繫分機&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applytel" name="wfcommnetProcess.applytel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfcommnetProcessEntity.applytel}" data-options="required:true"
                                />
                            </td>
                            <td width="4%">單位</td>
                            <td colspan="5" class="td_style1">
                                <input id="applydepartname" name="wfcommnetProcess.applydepartname"
                                       class="easyui-validatebox"
                                       style="width:500px;"
                                       value="${wfcommnetProcessEntity.applydepartname}" data-options="required:true"/>
                            </td>
                            <td width="4%">安保區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style2">
                                <div class="isProtectDiv"></div>
                                <input id="isProtect" name="wfcommnetProcess.isProtect"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfcommnetProcessEntity.isProtect}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>法人代碼&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applycompanycode" name="wfcommnetProcess.applycompanycode"
                                       class="easyui-validatebox"
                                       data-options="width:80,required:true" value="${wfcommnetProcessEntity.applycompanycode}"/>
                            </td>
                            <td>法人名稱&nbsp;<font color="red">*</font></td>
                            <td class="td_style1" colspan="7">
                                <input id="applycompanyname" name="wfcommnetProcess.applycompanyname"
                                       class="easyui-validatebox"
                                       data-options="width:300,required:true" value="${wfcommnetProcessEntity.applycompanyname}"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">網點需求</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">新增網點</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">需求地點</td>
                            <td colspan="3">
                                <input id="newDotLocation" name="wfcommnetProcess.newDotLocation"
                                       class="easyui-validatebox"
                                       style="width:300px;" title="限定50漢字"
                                       value="${wfcommnetProcessEntity.newDotLocation }"
                                       data-options="validType:'maxLength[50]'"
                                />
                            </td>
                            <td width="6%">數量</td>
                            <td width="6%">
                                <input id="newDotNum" name="wfcommnetProcess.newDotNum" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfcommnetProcessEntity.newDotNum }"
                                />
                            </td>
                            <td width="6%">需求說明</td>
                            <td colspan="3">
                                <input id="newDotDetail" name="wfcommnetProcess.newDotDetail" class="easyui-validatebox"
                                       style="width:300px;" title="限定300漢字"
                                       value="${wfcommnetProcessEntity.newDotDetail }"
                                       data-options="validType:'maxLength[300]'"
                                />
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">網點遷移</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">需求地點</td>
                            <td colspan="3">
                                <input id="transDotLocation" name="wfcommnetProcess.transDotLocation"
                                       class="easyui-validatebox"
                                       style="width:300px;" title="限定50漢字"
                                       value="${wfcommnetProcessEntity.transDotLocation }"
                                       data-options="validType:'maxLength[50]'"
                                />
                            </td>
                            <td width="6%">數量</td>
                            <td width="6%">
                                <input id="transDotNum" name="wfcommnetProcess.transDotNum" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfcommnetProcessEntity.transDotNum }"
                                />
                            </td>
                            <td width="6%">需求說明</td>
                            <td colspan="3">
                                <input id="transDotDetail" name="wfcommnetProcess.transDotDetail"
                                       class="easyui-validatebox"
                                       style="width:300px;" title="限定300漢字"
                                       value="${wfcommnetProcessEntity.transDotDetail }"
                                       data-options="validType:'maxLength[300]'"
                                />
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">地面線路布放</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">需求地點</td>
                            <td colspan="3">
                                <input id="gdLineLocation" name="wfcommnetProcess.gdLineLocation"
                                       class="easyui-validatebox"
                                       style="width:300px;" title="限定50漢字"
                                       value="${wfcommnetProcessEntity.gdLineLocation}"
                                       data-options="validType:'maxLength[50]'"
                                />
                            </td>
                            <td width="6%">數量</td>
                            <td width="6%">
                                <input id="gdLineNum" name="wfcommnetProcess.gdLineNum" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfcommnetProcessEntity.gdLineNum}"
                                />
                            </td>
                            <td width="6%">需求說明</td>
                            <td colspan="3">
                                <input id="gdLineDetail" name="wfcommnetProcess.gdLineDetail" class="easyui-validatebox"
                                       style="width:300px;" title="限定300漢字"
                                       value="${wfcommnetProcessEntity.gdLineDetail}"
                                       data-options="validType:'maxLength[300]'"
                                />
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">普通分機</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">新增分機號碼</td>
                        </tr>
                        <tr>
                            <td colspan="10">
                                <table class="flowList" style="width:100%;">
                                    <tr>
                                        <td>使用人工號</td>
                                        <td>使用人姓名</td>
                                        <td>需求地點</td>
                                        <td>需求說明</td>
                                        <td>操作</td>
                                    </tr>
                                    <c:choose>
                                        <c:when test="${not empty newTelephones}">
                                            <c:forEach items="${newTelephones}" var="item" varStatus="rs">
                                                <tr id="newTelephones${rs.index}" class="newTelephonesTr">
                                                    <td>
                                                        <input id="newTelephones_empNo${rs.index}"
                                                               name="newTelephones[${rs.index}].empNo"
                                                               class="easyui-validatebox"
                                                               style="width:90px;"
                                                               onblur="getEmpName('newTelephones',${rs.index});"
                                                               value="${item.empNo}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input id="newTelephones_empName${rs.index}"
                                                               name="newTelephones[${rs.index}].empName"
                                                               class="easyui-validatebox inputCss"
                                                               style="width:90px;" readonly
                                                               value="${item.empName}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input id="newTelephones_requireLocation${rs.index}"
                                                               name="newTelephones[${rs.index}].requireLocation"
                                                               class="easyui-validatebox"
                                                               style="width:150px;" title="限定50漢字"
                                                               value="${item.requireLocation}"
                                                               data-options="validType:'maxLength[50]'"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input id="newTelephones_requireDetail${rs.index}"
                                                               name="newTelephones[${rs.index}].requireDetail"
                                                               class="easyui-validatebox"
                                                               style="width:400px;" title="限定300漢字"
                                                               value="${item.requireDetail}"
                                                               data-options="validType:'maxLength[300]'"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="itemLineDelTr('newTelephones',${rs.index});return false;"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <tr id="newTelephones0" class="newTelephonesTr">
                                                <td>
                                                    <input id="newTelephones_empNo0" name="newTelephones[0].empNo"
                                                           class="easyui-validatebox"
                                                           onblur="getEmpName('newTelephones',0);"
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="newTelephones_empName0" name="newTelephones[0].empName"
                                                           class="easyui-validatebox inputCss" readonly
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="newTelephones_requireLocation0"
                                                           name="newTelephones[0].requireLocation"
                                                           class="easyui-validatebox" title="限定50漢字"
                                                           data-options="validType:'maxLength[50]'"
                                                           style="width:150px;"/>
                                                </td>
                                                <td>
                                                    <input id="newTelephones_requireDetail0"
                                                           name="newTelephones[0].requireDetail"
                                                           class="easyui-validatebox" title="限定300漢字"
                                                           data-options="validType:'maxLength[300]'"
                                                           style="width:400px;"/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="itemLineDelTr('newTelephones',0);return false;"/>
                                                </td>
                                            </tr>
                                        </c:otherwise>
                                    </c:choose>
                                </table>
                            </td>
                        </tr>
                        <tr align="left" class="nottr">
                            <td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
                                <input type="button" id="newTelephoneItemAdd" style="width:100px;float:left;"
                                       value="添加一筆"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">分機號碼遷移</td>
                        </tr>
                        <tr>
                            <td colspan="10">
                                <table class="flowList" style="width:100%;" id="applyTable2">
                                    <tr>
                                        <td>需遷移號碼</td>
                                        <td>使用人工號</td>
                                        <td>使用人姓名</td>
                                        <td>原使用地點</td>
                                        <td>現需求地點</td>
                                        <td>需求說明</td>
                                        <td>操作</td>
                                    </tr>
                                    <c:choose>
                                        <c:when test="${not empty transTelephones}">
                                            <c:forEach items="${transTelephones}" var="item" varStatus="rs">
                                                <tr id="transTelephones${rs.index}" class="transTelephonesTr">
                                                    <td>
                                                        <input id="transTelephones_transNo${rs.index}"
                                                               name="transTelephones[${rs.index}].transNo"
                                                               class="easyui-validatebox"
                                                               style="width:90px;" value="${item.transNo }"/>
                                                    </td>
                                                    <td>
                                                        <input id="transTelephones_empNo${rs.index}"
                                                               name="transTelephones[${rs.index}].empNo"
                                                               class="easyui-validatebox"
                                                               onblur="getEmpName('transTelephones',${rs.index});"
                                                               style="width:90px;" value="${item.empNo }"/>
                                                    </td>
                                                    <td>
                                                        <input id="transTelephones_empName${rs.index}"
                                                               name="transTelephones[${rs.index}].empName"
                                                               class="easyui-validatebox inputCss"
                                                               style="width:90px;" readonly value="${item.empName}"/>
                                                    </td>
                                                    <td>
                                                        <input id="transTelephones_oldLocation${rs.index}"
                                                               name="transTelephones[${rs.index}].oldLocation"
                                                               class="easyui-validatebox"
                                                               style="width:150px;" value="${item.oldLocation }"
                                                               title="限定50漢字" data-options="validType:'maxLength[50]'"/>
                                                    </td>
                                                    <td>
                                                        <input id="transTelephones_requireLocation${rs.index}"
                                                               name="transTelephones[${rs.index}].requireLocation"
                                                               class="easyui-validatebox"
                                                               style="width:150px;" value="${item.requireLocation }"
                                                               title="限定50漢字" data-options="validType:'maxLength[50]'"/>
                                                    </td>
                                                    <td>
                                                        <input id="transTelephones_requireDetail${rs.index}"
                                                               name="transTelephones[${rs.index}].requireDetail"
                                                               class="easyui-validatebox"
                                                               style="width:300px;" value="${item.requireDetail }"
                                                               title="限定300漢字"
                                                               data-options="validType:'maxLength[300]'"/>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="itemLineDelTr('transTelephones',${rs.index});return false;"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <tr id="transTelephones0" class="transTelephonesTr">
                                                <td>
                                                    <input id="transTelephones_transNo0"
                                                           name="transTelephones[0].transNo" class="easyui-validatebox"
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="transTelephones_empNo0" name="transTelephones[0].empNo"
                                                           class="easyui-validatebox"
                                                           onblur="getEmpName('transTelephones',0);"
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="transTelephones_empName0"
                                                           name="transTelephones[0].empName"
                                                           class="easyui-validatebox inputCss" readonly
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="transTelephones_oldLocation0"
                                                           name="transTelephones[0].oldLocation"
                                                           class="easyui-validatebox"
                                                           style="width:150px;" title="限定50漢字"
                                                           data-options="validType:'maxLength[50]'"/>
                                                </td>
                                                <td>
                                                    <input id="transTelephones_requireLocation0"
                                                           name="transTelephones[0].requireLocation"
                                                           class="easyui-validatebox"
                                                           style="width:150px;" title="限定50漢字"
                                                           data-options="validType:'maxLength[50]'"/>
                                                </td>
                                                <td>
                                                    <input id="transTelephones_requireDetail0"
                                                           name="transTelephones[0].requireDetail"
                                                           class="easyui-validatebox"
                                                           style="width:300px;" title="限定300漢字"
                                                           data-options="validType:'maxLength[300]'"/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="itemLineDelTr('transTelephones',0);return false;"/>
                                                </td>
                                            </tr>
                                        </c:otherwise>
                                    </c:choose>
                                </table>
                            </td>
                        </tr>
                        <tr align="left" class="nottr">
                            <td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
                                <input type="button" id="transTelephoneItemAdd" style="width:100px;float:left;"
                                       value="添加一筆"/>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" class="td_style1">分機號碼并機</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">分機號碼</td>
                            <td colspan="3">
                                <input id="mergeNo" name="wfcommnetProcess.mergeNo" class="easyui-validatebox"
                                       style="width:300px;"
                                       value="${wfcommnetProcessEntity.mergeNo}"
                                />
                            </td>
                            <td width="6%">號碼數量</td>
                            <td width="6%">
                                <input id="mergeNum" name="wfcommnetProcess.mergeNum" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfcommnetProcessEntity.mergeNum }"
                                />
                            </td>
                            <td width="6%">需求說明</td>
                            <td colspan="3">
                                <input id="mergeDetail" name="wfcommnetProcess.mergeDetail" class="easyui-validatebox"
                                       style="width:300px;" title="限定300漢字" data-options="validType:'maxLength[300]'"
                                       value="${wfcommnetProcessEntity.mergeDetail }"
                                />
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" class="td_style1">刪除分機</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">分機號碼</td>
                            <td colspan="3">
                                <input id="deleteNo" name="wfcommnetProcess.deleteNo" class="easyui-validatebox"
                                       style="width:300px;"
                                       value="${wfcommnetProcessEntity.deleteNo }"
                                />
                            </td>
                            <td width="6%">號碼數量</td>
                            <td width="6%">
                                <input id="deleteNum" name="wfcommnetProcess.deleteNum" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfcommnetProcessEntity.deleteNum}"
                                />
                            </td>
                            <td width="6%">需求說明</td>
                            <td colspan="3">
                                <input id="deleteDetail" name="wfcommnetProcess.deleteDetail" class="easyui-validatebox"
                                       style="width:300px;" title="限定300漢字" data-options="validType:'maxLength[300]'"
                                       value="${wfcommnetProcessEntity.deleteDetail}"
                                />
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" class="td_style1">網絡電話</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">新增SIP號碼</td>
                        </tr>
                        <tr>
                            <td colspan="10">
                                <table class="flowList" style="width:100%;">
                                    <tr>
                                        <td>使用人工號</td>
                                        <td>使用人姓名</td>
                                        <td>需求地點</td>
                                        <td>需求說明</td>
                                        <td>操作</td>
                                    </tr>
                                    <c:choose>
                                        <c:when test="${not empty newSipnos}">
                                            <c:forEach items="${newSipnos}" var="item" varStatus="rs">
                                                <tr id="newSipnos${rs.index}" class="newSipnosTr">
                                                    <td>
                                                        <input id="newSipnos_empNo${rs.index}"
                                                               name="newSipnos[${rs.index}].empNo"
                                                               class="easyui-validatebox"
                                                               style="width:90px;"
                                                               onblur="getEmpName('newSipnos',${rs.index});"
                                                               value="${item.empNo}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input id="newSipnos_empName${rs.index}"
                                                               name="newSipnos[${rs.index}].empName"
                                                               class="easyui-validatebox inputCss"
                                                               style="width:90px;" readonly
                                                               value="${item.empName}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input id="newSipnos_requireLocation${rs.index}"
                                                               name="newSipnos[${rs.index}].requireLocation"
                                                               class="easyui-validatebox"
                                                               style="width:150px;" title="限定50漢字"
                                                               data-options="validType:'maxLength[50]'"
                                                               value="${item.requireLocation}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input id="newSipnos_requireDetail${rs.index}"
                                                               name="newSipnos[${rs.index}].requireDetail"
                                                               class="easyui-validatebox"
                                                               style="width:400px;" title="限定300漢字"
                                                               data-options="validType:'maxLength[300]'"
                                                               value="${item.requireDetail}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="itemLineDelTr('newSipnos',${rs.index});return false;"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <tr id="newSipnos0" class="newSipnosTr">
                                                <td>
                                                    <input id="newSipnos_empNo0" name="newSipnos[0].empNo"
                                                           class="easyui-validatebox"
                                                           onblur="getEmpName('newSipnos',0);"
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="newSipnos_empName0" name="newSipnos[0].empName"
                                                           class="easyui-validatebox inputCss" readonly
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="newSipnos_requireLocation0"
                                                           name="newSipnos[0].requireLocation"
                                                           class="easyui-validatebox"
                                                           style="width:150px;" title="限定50漢字"
                                                           data-options="validType:'maxLength[50]'"/>
                                                </td>
                                                <td>
                                                    <input id="newSipnos_requireDetail0"
                                                           name="newSipnos[0].requireDetail" class="easyui-validatebox"
                                                           style="width:400px;" title="限定300漢字"
                                                           data-options="validType:'maxLength[300]'"/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="itemLineDelTr('newSipnos',0);return false;"/>
                                                </td>
                                            </tr>
                                        </c:otherwise>
                                    </c:choose>
                                </table>
                            </td>
                        </tr>
                        <tr align="left" class="nottr">
                            <td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
                                <input type="button" id="newSipnoItemAdd" style="width:100px;float:left;" value="添加一筆"/>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" class="td_style1">刪除SIP號碼</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">號碼</td>
                            <td colspan="3">
                                <input id="deleteSipNo" name="wfcommnetProcess.deleteSipNo" class="easyui-validatebox"
                                       style="width:300px;"
                                       value="${wfcommnetProcessEntity.deleteSipNo }"
                                />
                            </td>
                            <td width="6%">號碼數量</td>
                            <td width="6%">
                                <input id="deleteSipNum" name="wfcommnetProcess.deleteSipNum" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfcommnetProcessEntity.deleteSipNum}"
                                />
                            </td>
                            <td width="6%">需求說明</td>
                            <td colspan="3">
                                <input id="deleteSipDetail" name="wfcommnetProcess.deleteSipDetail"
                                       class="easyui-validatebox"
                                       style="width:300px;" title="限定300漢字" data-options="validType:'maxLength[300]'"
                                       value="${wfcommnetProcessEntity.deleteSipDetail}"
                                />
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" class="td_style1">新增思科IP Phone號碼</td>
                        </tr>

                        <tr>
                            <td colspan="10">
                                <table class="flowList" style="width:100%;">
                                    <tr>
                                        <td>使用人工號</td>
                                        <td>使用人姓名</td>
                                        <td>型號</td>
                                        <td>序列號</td>
                                        <td>MAC地址</td>
                                        <td>需求地點</td>
                                        <td>需求說明</td>
                                        <td>操作</td>
                                    </tr>
                                    <c:choose>
                                        <c:when test="${not empty newCiscos}">
                                            <c:forEach items="${newCiscos}" var="item" varStatus="rs">
                                                <tr id="newCiscos${rs.index}" class="newCiscosTr">
                                                    <td>
                                                        <input id="newCiscos_empNo${rs.index}"
                                                               name="newCiscos[${rs.index}].empNo"
                                                               class="easyui-validatebox"
                                                               onblur="getEmpName('newCiscos',${rs.index});"
                                                               style="width:90px;" value="${item.empNo}"/>
                                                    </td>
                                                    <td>
                                                        <input id="newCiscos_empName${rs.index}"
                                                               name="newCiscos[${rs.index}].empName"
                                                               class="easyui-validatebox inputCss" readonly
                                                               style="width:90px;" value="${item.empName}"/>
                                                    </td>
                                                    <td>
                                                        <input id="newCiscos_model${rs.index}"
                                                               name="newCiscos[${rs.index}].model"
                                                               class="easyui-validatebox"
                                                               style="width:90px;" value="${item.model}"/>
                                                    </td>
                                                    <td>
                                                        <input id="newCiscos_imei${rs.index}"
                                                               name="newCiscos[${rs.index}].imei"
                                                               class="easyui-validatebox"
                                                               style="width:90px;" value="${item.imei}"/>
                                                    </td>
                                                    <td>
                                                        <input id="newCiscos_macAddress${rs.index}"
                                                               name="newCiscos[${rs.index}].macAddress"
                                                               class="easyui-validatebox"
                                                               style="width:90px;" value="${item.macAddress}"/>
                                                    </td>
                                                    <td>
                                                        <input id="newCiscos_requireLocation${rs.index}"
                                                               name="newCiscos[${rs.index}].requireLocation"
                                                               class="easyui-validatebox"
                                                               style="width:180px;" value="${item.requireLocation}"
                                                               title="限定50漢字" data-options="validType:'maxLength[50]'"/>
                                                    </td>
                                                    <td>
                                                        <input id="newCiscos_requireDetail${rs.index}"
                                                               name="newCiscos[${rs.index}].requireDetail"
                                                               class="easyui-validatebox"
                                                               style="width:300px;" value="${item.requireDetail}"
                                                               title="限定300漢字"
                                                               data-options="validType:'maxLength[300]'"/>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="itemLineDelTr('newCiscos', ${rs.index});return false;"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <tr id="newCiscos0" class="newCiscosTr">
                                                <td>
                                                    <input id="newCiscos_empNo0" name="newCiscos[0].empNo"
                                                           class="easyui-validatebox"
                                                           onblur="getEmpName('newCiscos',0);"
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="newCiscos_empName0" name="newCiscos[0].empName"
                                                           class="easyui-validatebox inputCss" readonly
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="newCiscos_model0" name="newCiscos[0].model"
                                                           class="easyui-validatebox"
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="newCiscos_imei0" name="newCiscos[0].imei"
                                                           class="easyui-validatebox"
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="newCiscos_macAddress0" name="newCiscos[0].macAddress"
                                                           class="easyui-validatebox"
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="newCiscos_requireLocation0"
                                                           name="newCiscos[0].requireLocation"
                                                           class="easyui-validatebox"
                                                           style="width:180px;" title="限定50漢字"
                                                           data-options="validType:'maxLength[50]'"/>
                                                </td>
                                                <td>
                                                    <input id="newCiscos_requireDetail0"
                                                           name="newCiscos[0].requireDetail" class="easyui-validatebox"
                                                           style="width:300px;" title="限定300漢字"
                                                           data-options="validType:'maxLength[300]'"/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="itemLineDelTr('newCiscos', 0);return false;"/>
                                                </td>
                                            </tr>
                                        </c:otherwise>
                                    </c:choose>
                                </table>
                            </td>
                        </tr>
                        <tr align="left" class="nottr">
                            <td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
                                <input type="button" id="newCiscoItemAdd" style="width:100px;float:left;" value="添加一筆"/>
                            </td>
                        </tr>


                        <tr>
                            <td colspan="10" class="td_style1">刪除思科IP Phone號碼</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">號碼</td>
                            <td colspan="3">
                                <input id="deleteCiscoNo" name="wfcommnetProcess.deleteCiscoNo"
                                       class="easyui-validatebox"
                                       style="width:300px;"
                                       value="${wfcommnetProcessEntity.deleteCiscoNo }"
                                />
                            </td>
                            <td width="6%">號碼數量</td>
                            <td width="6%">
                                <input id="deleteCiscoNum" name="wfcommnetProcess.deleteCiscoNum"
                                       class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfcommnetProcessEntity.deleteCiscoNum}"
                                />
                            </td>
                            <td width="6%">需求說明</td>
                            <td colspan="3">
                                <input id="deleteCiscoDetail" name="wfcommnetProcess.deleteCiscoDetail"
                                       class="easyui-validatebox"
                                       style="width:300px;" title="限定300漢字" data-options="validType:'maxLength[300]'"
                                       value="${wfcommnetProcessEntity.deleteCiscoDetail}"
                                />
                            </td>
                        </tr>


                        <tr>
                            <td colspan="10" class="td_style1">思科號碼更換話機</td>
                        </tr>

                        <tr>
                            <td colspan="10">
                                <table class="flowList" style="width:100%;">
                                    <tr>
                                        <td>IP Phone號碼</td>
                                        <td>使用人工號</td>
                                        <td>使用人姓名</td>
                                        <td>新型號</td>
                                        <td>新序列號</td>
                                        <td>新MAC地址</td>
                                        <td>需求說明</td>
                                        <td>操作</td>
                                    </tr>
                                    <c:choose>
                                        <c:when test="${not empty transCiscos}">
                                            <c:forEach items="${transCiscos}" var="item" varStatus="rs">
                                                <tr id="transCiscos${rs.index}" class="transCiscosTr">
                                                    <td>
                                                        <input id="transCiscos_ipPhoneNo${rs.index}"
                                                               name="transCiscos[${rs.index}].ipPhoneNo"
                                                               class="easyui-validatebox"
                                                               style="width:90px;"
                                                               value="${item.ipPhoneNo}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input id="transCiscos_empNo${rs.index}"
                                                               name="transCiscos[${rs.index}].empNo"
                                                               class="easyui-validatebox"
                                                               style="width:90px;"
                                                               onblur="getEmpName('transCiscos',${rs.index});"
                                                               value="${item.empNo}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input id="transCiscos_empName${rs.index}"
                                                               name="transCiscos[${rs.index}].empName"
                                                               class="easyui-validatebox inputCss"
                                                               style="width:90px;" readonly
                                                               value="${item.empName}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input id="transCiscos_model${rs.index}"
                                                               name="transCiscos[${rs.index}].model"
                                                               class="easyui-validatebox"
                                                               style="width:90px;"
                                                               value="${item.model}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input id="transCiscos_imei${rs.index}"
                                                               name="transCiscos[${rs.index}].imei"
                                                               class="easyui-validatebox"
                                                               style="width:90px;"
                                                               value="${item.imei}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input id="transCiscos_macAddress${rs.index}"
                                                               name="transCiscos[${rs.index}].macAddress"
                                                               class="easyui-validatebox"
                                                               style="width:90px;"
                                                               value="${item.macAddress}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input id="transCiscos_requireDetail${rs.index}"
                                                               name="transCiscos[${rs.index}].requireDetail"
                                                               class="easyui-validatebox"
                                                               style="width:300px;" title="限定300漢字"
                                                               data-options="validType:'maxLength[300]'"
                                                               value="${item.requireDetail}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="itemLineDelTr('transCiscos',${rs.index});return false;"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <tr id="transCiscos0" class="transCiscosTr">
                                                <td>
                                                    <input id="transCiscos_ipPhoneNo0" name="transCiscos[0].ipPhoneNo"
                                                           class="easyui-validatebox"
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="transCiscos_empNo0" name="transCiscos[0].empNo"
                                                           class="easyui-validatebox"
                                                           onblur="getEmpName('transCiscos',0);"
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="transCiscos_empName0" name="transCiscos[0].empName"
                                                           class="easyui-validatebox inputCss" readonly
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="transCiscos_model0" name="transCiscos[0].model"
                                                           class="easyui-validatebox"
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="transCiscos_imei0" name="transCiscos[0].imei"
                                                           class="easyui-validatebox"
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="transCiscos_macAddress0" name="transCiscos[0].macAddress"
                                                           class="easyui-validatebox"
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="transCiscos_requireDetail0"
                                                           name="transCiscos[0].requireDetail"
                                                           class="easyui-validatebox"
                                                           style="width:300px;" title="限定300漢字"
                                                           data-options="validType:'maxLength[300]'"/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="itemLineDelTr('transCiscos',0);return false;"/>
                                                </td>
                                            </tr>
                                        </c:otherwise>
                                    </c:choose>
                                </table>
                            </td>
                        </tr>
                        <tr align="left" class="nottr">
                            <td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
                                <input type="button" id="transCiscoItemAdd" style="width:100px;float:left;"
                                       value="添加一筆"/>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" class="td_style1">思科號碼并機</td>
                        </tr>
                        <tr>
                            <td colspan="10">
                                <table class="flowList" style="width:100%;">
                                    <tr>
                                        <td>IP Phone號碼</td>
                                        <td>使用人工號</td>
                                        <td>使用人姓名</td>
                                        <td>新型號</td>
                                        <td>新序列號</td>
                                        <td>新MAC地址</td>
                                        <td>需求說明</td>
                                        <td>操作</td>
                                    </tr>
                                    <c:choose>
                                        <c:when test="${not empty mergeCiscos}">
                                            <c:forEach items="${mergeCiscos}" var="item" varStatus="rs">
                                                <tr id="mergeCiscos${rs.index}" class="mergeCiscosTr">
                                                    <td>
                                                        <input id="mergeCiscos_ipPhoneNo${rs.index}"
                                                               name="mergeCiscos[${rs.index}].ipPhoneNo"
                                                               class="easyui-validatebox"
                                                               style="width:90px;"
                                                               value="${item.ipPhoneNo}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input id="mergeCiscos_empNo${rs.index}"
                                                               name="mergeCiscos[${rs.index}].empNo"
                                                               class="easyui-validatebox"
                                                               style="width:90px;"
                                                               onblur="getEmpName('mergeCiscos',${rs.index});"
                                                               value="${item.empNo}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input id="mergeCiscos_empName${rs.index}"
                                                               name="mergeCiscos[${rs.index}].empName"
                                                               class="easyui-validatebox inputCss"
                                                               style="width:90px;" readonly
                                                               value="${item.empName}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input id="mergeCiscos_model${rs.index}"
                                                               name="mergeCiscos[${rs.index}].model"
                                                               class="easyui-validatebox"
                                                               style="width:90px;"
                                                               value="${item.model}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input id="mergeCiscos_imei${rs.index}"
                                                               name="mergeCiscos[${rs.index}].imei"
                                                               class="easyui-validatebox"
                                                               style="width:90px;"
                                                               value="${item.imei}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input id="mergeCiscos_macAddress${rs.index}"
                                                               name="mergeCiscos[${rs.index}].macAddress"
                                                               class="easyui-validatebox"
                                                               style="width:90px;"
                                                               value="${item.macAddress}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input id="mergeCiscos_requireDetail${rs.index}"
                                                               name="mergeCiscos[${rs.index}].requireDetail"
                                                               class="easyui-validatebox"
                                                               style="width:300px;" title="限定300漢字"
                                                               data-options="validType:'maxLength[300]'"
                                                               value="${item.requireDetail}"
                                                        />
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="itemLineDelTr('mergeCiscos',${rs.index});return false;"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <tr id="mergeCiscos0" class="mergeCiscosTr">
                                                <td>
                                                    <input id="mergeCiscos_ipPhoneNo0" name="mergeCiscos[0].ipPhoneNo"
                                                           class="easyui-validatebox"
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="mergeCiscos_empNo0" name="mergeCiscos[0].empNo"
                                                           class="easyui-validatebox"
                                                           onblur="getEmpName('mergeCiscos',0);"
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="mergeCiscos_empName0" name="mergeCiscos[0].empName"
                                                           class="easyui-validatebox inputCss" readonly
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="mergeCiscos_model0" name="mergeCiscos[0].model"
                                                           class="easyui-validatebox"
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="mergeCiscos_imei0" name="mergeCiscos[0].imei"
                                                           class="easyui-validatebox"
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="mergeCiscos_macAddress0" name="mergeCiscos[0].macAddress"
                                                           class="easyui-validatebox"
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="mergeCiscos_requireDetail0"
                                                           name="mergeCiscos[0].requireDetail"
                                                           class="easyui-validatebox"
                                                           style="width:300px;" title="限定300漢字"
                                                           data-options="validType:'maxLength[300]'"/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="itemLineDelTr('mergeCiscos',0);return false;"/>
                                                </td>
                                            </tr>
                                        </c:otherwise>
                                    </c:choose>
                                </table>
                            </td>
                        </tr>
                        <tr align="left" class="nottr">
                            <td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
                                <input type="button" id="mergeCiscoItemAdd" style="width:100px;float:left;"
                                       value="添加一筆"/>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" class="td_style1">直線電話</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">新增直線電話</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">需求地點</td>
                            <td colspan="3">
                                <input id="newLineLocation" name="wfcommnetProcess.newLineLocation"
                                       class="easyui-validatebox"
                                       style="width:300px;" title="限定50漢字" data-options="validType:'maxLength[50]'"
                                       value="${wfcommnetProcessEntity.newLineLocation}"
                                />
                            </td>
                            <td width="6%">需求數量</td>
                            <td width="6%">
                                <input id="newLineNum" name="wfcommnetProcess.newLineNum" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfcommnetProcessEntity.newLineNum}"
                                />
                            </td>
                            <td width="6%">需求說明</td>
                            <td colspan="3">
                                <input id="newLineDetail" name="wfcommnetProcess.newLineDetail"
                                       class="easyui-validatebox"
                                       style="width:300px;" title="限定300漢字" data-options="validType:'maxLength[300]'"
                                       value="${wfcommnetProcessEntity.newLineDetail}"
                                />
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">刪除直線電話</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">直線號碼</td>
                            <td colspan="3">
                                <input id="deleteLineNo" name="wfcommnetProcess.deleteLineNo" class="easyui-validatebox"
                                       style="width:300px;"
                                       value="${wfcommnetProcessEntity.deleteLineNo}"
                                />
                            </td>
                            <td width="6%">號碼數量</td>
                            <td width="6%">
                                <input id="deleteLineNum" name="wfcommnetProcess.deleteLineNum"
                                       class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfcommnetProcessEntity.deleteLineNum}"
                                />
                            </td>
                            <td width="6%">需求說明</td>
                            <td colspan="3">
                                <input id="deleteLineDetail" name="wfcommnetProcess.deleteLineDetail"
                                       class="easyui-validatebox"
                                       style="width:300px;" title="限定300漢字" data-options="validType:'maxLength[300]'"
                                       value="${wfcommnetProcessEntity.deleteLineDetail}"
                                />
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" class="td_style1">遷移直線電話</td>
                        </tr>
                        <tr>
                            <td colspan="10">
                                <table class="flowList" style="width:100%;">
                                    <tr>
                                        <td>需遷移號碼</td>
                                        <td>原使用地點</td>
                                        <td>現使用地點</td>
                                        <td>需求說明</td>
                                        <td>操作</td>
                                    </tr>
                                    <c:choose>
                                        <c:when test="${not empty transLines}">
                                            <c:forEach items="${transLines}" var="item" varStatus="rs">
                                                <tr id="transLines${rs.index}" class="transLinesTr">
                                                    <td>
                                                        <input id="transLines_transNo${rs.index}"
                                                               name="transLines[${rs.index}].transNo"
                                                               class="easyui-validatebox"
                                                               style="width:90px;" value="${item.transNo}"/>
                                                    </td>
                                                    <td>
                                                        <input id="transLines_oldLocation${rs.index}"
                                                               name="transLines[${rs.index}].oldLocation"
                                                               class="easyui-validatebox"
                                                               style="width:90px;" value="${item.oldLocation}"
                                                               title="限定50漢字" data-options="validType:'maxLength[50]'"/>
                                                    </td>
                                                    <td>
                                                        <input id="transLines_newLocation${rs.index}"
                                                               name="transLines[${rs.index}].newLocation"
                                                               class="easyui-validatebox"
                                                               style="width:90px;" value="${item.newLocation}"
                                                               title="限定50漢字" data-options="validType:'maxLength[50]'"/>
                                                    </td>
                                                    <td>
                                                        <input id="transLines_requireDetail${rs.index}"
                                                               name="transLines[${rs.index}].requireDetail"
                                                               class="easyui-validatebox"
                                                               style="width:500px;" value="${item.requireDetail}"
                                                               title="限定300漢字"
                                                               data-options="validType:'maxLength[300]'"/>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="itemLineDelTr('mergeCiscos',0);return false;"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <tr id="transLines0" class="transLinesTr">
                                                <td>
                                                    <input id="transLines_transNo0" name="transLines[0].transNo"
                                                           class="easyui-validatebox"
                                                           style="width:90px;"/>
                                                </td>
                                                <td>
                                                    <input id="transLines_oldLocation0" name="transLines[0].oldLocation"
                                                           class="easyui-validatebox"
                                                           style="width:90px;" title="限定50漢字"
                                                           data-options="validType:'maxLength[50]'"/>
                                                </td>
                                                <td>
                                                    <input id="transLines_newLocation0" name="transLines[0].newLocation"
                                                           class="easyui-validatebox"
                                                           style="width:90px;" title="限定50漢字"
                                                           data-options="validType:'maxLength[50]'"/>
                                                </td>
                                                <td>
                                                    <input id="transLines_requireDetail0"
                                                           name="transLines[0].requireDetail" class="easyui-validatebox"
                                                           style="width:500px;" title="限定300漢字"
                                                           data-options="validType:'maxLength[300]'"/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="itemLineDelTr('transLines',0);return false;"/>
                                                </td>
                                            </tr>
                                        </c:otherwise>
                                    </c:choose>
                                </table>
                            </td>
                        </tr>
                        <tr align="left" class="nottr">
                            <td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
                                <input type="button" id="transLineItemAdd" style="width:100px;float:left;"
                                       value="添加一筆"/>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" class="td_style1">話機功能設定</td>
                        </tr>
                        <tr>
                            <td colspan="10">
                                <div class="functionSetDiv"></div>
                                <input id="functionSet" name="wfcommnetProcess.functionSet"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfcommnetProcessEntity.functionSet}"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10">
                                <table class="flowList" style="width:100%;">
                                    <tr>
                                        <td>號碼</td>
                                        <td>話機類型(仅來電顯示需填寫)</td>
                                        <td>號碼數量</td>
                                        <td>需求說明</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input id="displayNo" name="wfcommnetProcess.displayNo"
                                                   class="easyui-validatebox"
                                                   style="width:200px;"
                                                   value="${wfcommnetProcessEntity.displayNo}"
                                            />
                                        </td>
                                        <td>
                                            <input id="displayType" name="wfcommnetProcess.displayType"
                                                   class="easyui-validatebox"
                                                   style="width:200px;"
                                                   value="${wfcommnetProcessEntity.displayType}"
                                            />
                                        </td>
                                        <td>
                                            <input id="displayNum" name="wfcommnetProcess.displayNum"
                                                   class="easyui-validatebox"
                                                   style="width:100px;"
                                                   value="${wfcommnetProcessEntity.displayNum}"
                                            />
                                        </td>
                                        <td>
                                            <input id="displayDetail" name="wfcommnetProcess.displayDetail"
                                                   class="easyui-validatebox"
                                                   style="width:400px;" title="限定300漢字"
                                                   data-options="validType:'maxLength[300]'"
                                                   value="${wfcommnetProcessEntity.displayDetail}"
                                            />
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">其他需求</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">對講機調頻</td>
                        </tr>
                        <tr align="center">
                            <td>需求說明</td>
                            <td colspan="9">
                                <input id="interComFmDetail" name="wfcommnetProcess.interComFmDetail"
                                       class="easyui-validatebox"
                                       style="width:800px;" title="限定300漢字" data-options="validType:'maxLength[300]'"
                                       value="${wfcommnetProcessEntity.interComFmDetail}"
                                />
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">其他需求</td>
                        </tr>
                        <tr align="center">
                            <td>需求說明</td>
                            <td colspan="9">
                                <input id="otherRequireDetail" name="wfcommnetProcess.otherRequireDetail"
                                       class="easyui-validatebox"
                                       style="width:800px;" title="限定300漢字" data-options="validType:'maxLength[300]'"
                                       value="${wfcommnetProcessEntity.otherRequireDetail}"
                                />
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件&nbsp;<font color="red">*</font></td>
                            <td width="90%" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button"
                                                                   value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <c:choose>
                                    <c:when test="${file.size()>0}">
                                        <input type="hidden" id="attachids" name="wfcommnetProcess.attachids"
                                               value="${wfcommnetProcessEntity.attachids }"/>
                                    </c:when>
                                    <c:otherwise>
                                        <input type="hidden" id="attachids" name="wfcommnetProcess.attachids" value=""/>
                                    </c:otherwise>
                                </c:choose>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">備註</td>
                            <td width="90%" align="left">
                                1.請注意在上傳分機附件時禁止更改附件的名稱！若要上傳CAD圖檔，請以圖片形式上傳；<br/>
                                2.新增分機、分機遷移、並機、新增IP Phone、新增直線電話、遷移直線電話請下載填寫 <a
                                    href="${ctx}/static/resources/download/commNet.xls">分機使用人信息登記表</a>；<br/>
                                3.如非特保區辦公網絡需新增VLAN權限，“需求項目”請選擇“其他需求”，同時填寫以下附件并上傳 <a
                                    href="${ctx}/static/resources/download/NetworkReq.xls">網絡訪問需求調查表</a>；<br/>
                                <font color="red">4.用戶單位需要製造處級主管核准；</font><br/>
                                5.如需申請TCL話機/SIP話機請另行填寫領料單；<br/>
                                <font color="red">6.單據中所有需填寫數量的欄位，僅填寫數字即可(請勿擅自增加單位，增加單位將導致單據無法拋轉作業)；</font><br/>
                                <font color="red">7.申請分機遷移時，申請人分機號碼與遷移分機號碼不能相同，否則無法拋轉至中央網通作業。</font>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','通訊網絡服務申請單流程');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zxywkjzgTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zxywkjzgno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'zxywkjzgTable','zxywkjzgno','zxywkjzgname',$('#applyFactoryId').combobox('getValue'),'wfcommnetProcess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxywkjzgno" name="wfcommnetProcess.zxywkjzgno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxywkjzgno']}"
                                                               readonly
                                                               value="${wfcommnetProcessEntity.zxywkjzgno }"/><c:if
                                                            test="${requiredMap['zxywkjzgno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxywkjzgname" name="wfcommnetProcess.zxywkjzgname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxywkjzgno']}"
                                                                value="${wfcommnetProcessEntity.zxywkjzgname }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').val(),'wfcommnetProcess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wfcommnetProcess.cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfcommnetProcessEntity.cchargeno}"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wfcommnetProcess.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfcommnetProcessEntity.cchargename}"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealfactoryid').val(),'wfcommnetProcess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wfcommnetProcess.zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfcommnetProcessEntity.zchargeno}"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="wfcommnetProcess.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfcommnetProcessEntity.zchargename}"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealfactoryid').val(),'wfcommnetProcess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="wfcommnetProcess.zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wfcommnetProcessEntity.zcchargeno}"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="wfcommnetProcess.zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfcommnetProcessEntity.zcchargename}"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="jgzgTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['jgzgno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(62,'jgzgTable','jgzgno','jgzgname',$('#applyFactoryId').combobox('getValue'),'wfcommnetProcess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jgzgno" name="wfcommnetProcess.jgzgno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['jgzgno']}"
                                                               readonly
                                                               value="${wfcommnetProcessEntity.jgzgno }"/><c:if
                                                            test="${requiredMap['jgzgno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="jgzgname" name="wfcommnetProcess.jgzgname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jgzgno']}"
                                                                value="${wfcommnetProcessEntity.jgzgname }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zxywbjzgTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zxywbjzgno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(25,'zxywbjzgTable','zxywbjzgno','zxywbjzgname',$('#applyFactoryId').combobox('getValue'),'wfcommnetProcess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxywbjzgno" name="wfcommnetProcess.zxywbjzgno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxywbjzgno']}"
                                                               readonly
                                                               value="${wfcommnetProcessEntity.zxywbjzgno }"/><c:if
                                                            test="${requiredMap['zxywbjzgno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxywbjzgname" name="wfcommnetProcess.zxywbjzgname"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxywbjzgno']}"
                                                                value="${wfcommnetProcessEntity.zxywbjzgname }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#dealdeptno').val(),'pcchargeno','pcchargename',$('#dealfactoryid').val(),'wfcommnetProcess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="wfcommnetProcess.pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${wfcommnetProcessEntity.pcchargeno}"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="wfcommnetProcess.pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wfcommnetProcessEntity.pcchargename}"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfcommnetProcessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10">
                                <font color="red">溫馨提示：如果您在填單過程中有任何疑問，請聯繫本廠區資訊人員：</font>
                                <a href="${ctx}/static/resources/download/factoryAreaContact.xls">各廠區資訊聯繫方式</a>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10">
                                <input type="checkbox" id="acceptedServiceItem" value="1"/>
                                <a target="_blank" href="${ctx}/static/resources/download/FHWTermService.pdf">本人已閱讀并同意服務條款</a>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="canelTask('${wfcommnetProcessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="areaId" name="areaId" value=""/>
    <input type="hidden" id="buildingId" name="buildingId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="dealfactoryid" value="${wfcommnetProcessEntity.makerfactoryid }"/>
    <input type="hidden" id="dealdeptno" value="${wfcommnetProcessEntity.makerdeptno}"/>
    <input type="hidden" id="onlyKchargeSignle" value="1" />
    <input type="hidden" id="sendMail" name="wfcommnetProcess.sendMail" value="${wfcommnetProcessEntity.sendMail }"/>
    <input type="hidden" id="postData" name="wfcommnetProcess.postData" value="${wfcommnetProcessEntity.postData }"/>
    <input type="hidden" id="useHub" name="wfcommnetProcess.useHub" value="${wfcommnetProcessEntity.useHub }"/>
    <input type="hidden" id="hubSpec" name="wfcommnetProcess.hubSpec" value="${wfcommnetProcessEntity.hubSpec }"/>
    <input type="hidden" id="hubNum" name="wfcommnetProcess.hubNum" value="${wfcommnetProcessEntity.hubNum }"/>
    <input type="hidden" id="hubAmmount" name="wfcommnetProcess.hubAmmount"
           value="${wfcommnetProcessEntity.hubAmmount }"/>
    <input type="hidden" id="useMaterial" name="wfcommnetProcess.useMaterial"
           value="${wfcommnetProcessEntity.useMaterial }"/>

    <div id="win"></div>
    <div id="dlg"></div>
</form>
</div>
<script type="text/javascript">
    function loadPageData() {

    }
</script>
<script src='${ctx}/static/js/information/wfcommnetprocess.min.js?random=20240924'></script>
</body>
</html>
