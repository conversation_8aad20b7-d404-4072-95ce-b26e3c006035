<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>通訊網絡服務申請單</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/wfcommnetprocess/${action}" method="post">

    <input id="ids" name="ids" type="hidden" value="${wfcommnetProcessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfcommnetProcessEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">通訊網絡服務申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfcommnetProcessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfcommnetProcessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfcommnetProcessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfcommnetProcessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfcommnetProcessEntity.makerno}/${wfcommnetProcessEntity.makername}</div>
        <div class="clear"></div>
        <table  class="formList">
            <tr>
                <td>
                    <table class="formList">
                     	<tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                       	 <tr align="center">
                            <td width="6%">承辦人工號</td>
                            <td width="6%">${wfcommnetProcessEntity.makerno}</td>
                            <td width="4%">承辦人</td>
                            <td width="6%">${wfcommnetProcessEntity.makername}</td>
                            <td width="4%">單位代碼</td>
                            <td width="6%">${wfcommnetProcessEntity.makerdeptno}</td>
                            <td width="4%">所在廠區</td>
                            <td width="7%" class="td_style1">
                                <input id="makerfactoryid" name="wfcommnetProcess.makerfactoryid" class="easyui-combobox" disabled 
                                       panelHeight="auto" value="${wfcommnetProcessEntity.applyFactoryId }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'applyFactoryId\',\'请选择所在廠區\']'"/>
                            </td>
                            <td width="4%">聯繫分機</td>
                            <td width="10%">${wfcommnetProcessEntity.makertel}</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">單位</td>
                            <td colspan="5">${wfcommnetProcessEntity.makerdeptname}</td>
                            <td width="4%">聯繫郵箱</td>
                            <td colspan="3">${wfcommnetProcessEntity.makeremail}</td>
                        </tr>
                    	<tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">申請人工號</td>
                            <td width="6%">${wfcommnetProcessEntity.applyno}</td>
                            <td width="4%">申請人</td>
                            <td width="6%">${wfcommnetProcessEntity.applyname}</td>
                            <td width="4%">單位代碼</td>
                            <td width="6%">${wfcommnetProcessEntity.applydepartno}</td>
                            <td width="4%">費用代碼</td>
                            <td width="6%">${wfcommnetProcessEntity.applycostno}</td>
                            <td width="4%">所在廠區&nbsp;</td>
                            <td width="10%" class="td_style1">
                                <input id="applyFactoryId" name="wfcommnetProcess.applyFactoryId" class="easyui-combobox" disabled
                                       panelHeight="auto" value="${wfcommnetProcessEntity.applyFactoryId }"
                                       data-options="width: 120,required:true,onSelect:function(){onchangeFactory();},validType:'comboxValidate[\'applyFactoryId\',\'请选择所在廠區\']'"/>
                            </td>
                          </tr>
                          <tr align="center">
                          	<td width="6%">資位</td>
                            <td width="6%">${wfcommnetProcessEntity.applyLevel}</td>
                          	<td width="4%">管理職</td>
                            <td width="6%">${wfcommnetProcessEntity.applyPost}</td>
                            <td width="4%">聯繫郵箱</td>
                            <td colspan="3">${wfcommnetProcessEntity.applyemail}</td>
                          	<td width="4%">使用區域</td>
                            <td width="10%" class="td_style1">
                                <input id="applyArea" name="wfcommnetProcess.applyArea" class="easyui-combobox" disabled
                                       data-options="width: 90,onSelect:function(){onchangeArea();}"
                                       value="${wfcommnetProcessEntity.applyArea}" panelHeight="auto"/>&nbsp;/
                                <input id="applyFloor" name="wfcommnetProcess.applyFloor" disabled
                                       class="easyui-combobox" data-options="width: 60"
                                       value="${wfcommnetProcessEntity.applyFloor}" panelHeight="auto"/>
                            </td>
                          </tr>
                          <tr align="center">
                          	<td width="6%">聯繫分機</td>
                            <td width="6%">${wfcommnetProcessEntity.applytel}</td>
                          	<td width="4%">單位</td>
                            <td colspan="5">${wfcommnetProcessEntity.applydepartname}</td>
                            <td width="4%">安保區域</td>
                            <td class="td_style2">
                                <div class="isProtectDiv"></div>
                                <input id="isProtect" name="wfcommnetProcess.isProtect" disabled
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfcommnetProcessEntity.isProtect}"/>
                            </td>
                          </tr>
                        <tr align="center">
                            <td>法人代碼</td>
                            <td align="center">${wfcommnetProcessEntity.applycompanycode}</td>
                            <td>法人名稱</td>
                            <td align="left" colspan="7">${wfcommnetProcessEntity.applycompanyname}</td>
                        </tr>
                          <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                         </tr>
                         <c:if test="${not empty wfcommnetProcessEntity.newDotLocation 
                         || not empty wfcommnetProcessEntity.transDotLocation 
                         || not empty wfcommnetProcessEntity.gdLineLocation}">
	                         <c:if test="${not empty wfcommnetProcessEntity.newDotLocation}">
	                         <tr>
	                            <td colspan="10" class="td_style1">網點需求</td>
	                         </tr>
	                         <tr>
	                            <td colspan="10" class="td_style1">新增網點</td>
	                         </tr>
	                         <tr align="center">
	                         	<td width="6%">需求地點</td><td colspan="3">${wfcommnetProcessEntity.newDotLocation }</td>
	                            <td width="6%">數量</td><td width="6%">${wfcommnetProcessEntity.newDotNum }</td>
	                            <td width="6%">需求說明</td><td colspan="3">${wfcommnetProcessEntity.newDotDetail}</td>
	                         </tr>
	                         </c:if>
	                         <c:if test="${not empty wfcommnetProcessEntity.transDotLocation}">
	                         <tr>
	                            <td colspan="10" class="td_style1">網點遷移</td>
	                         </tr>
	                         <tr align="center">
	                         	<td width="6%">需求地點</td><td colspan="3">${wfcommnetProcessEntity.transDotLocation }</td>
                           		<td width="6%">數量</td><td width="6%">${wfcommnetProcessEntity.transDotNum }</td>
	                            <td width="6%">需求說明</td><td colspan="3">${wfcommnetProcessEntity.transDotDetail }</td>
	                         </tr>
	                         </c:if>
	                         <c:if test="${not empty wfcommnetProcessEntity.gdLineLocation}">
	                         <tr>
	                            <td colspan="10" class="td_style1">地面線路布放</td>
	                         </tr>
	                         <tr align="center">
	                         	<td width="6%">需求地點</td><td colspan="3">${wfcommnetProcessEntity.gdLineLocation}</td>
	                            <td width="6%">數量</td><td width="6%">${wfcommnetProcessEntity.gdLineNum}</td>
	                            <td width="6%">需求說明</td><td colspan="3">${wfcommnetProcessEntity.gdLineDetail}</td>
	                         </tr>
	                         </c:if>
                         </c:if>
                         
                         <c:if test="${not empty newTelephones 
                         || not empty transTelephones 
                         || not empty wfcommnetProcessEntity.mergeNo 
                         || not empty wfcommnetProcessEntity.deleteNo}">
                         <tr>
                            <td colspan="10" class="td_style1">普通分機</td>
                         </tr>
                         <c:if test="${not empty newTelephones}">
                         <tr>
                            <td colspan="10" class="td_style1">新增分機號碼</td>
                         </tr>
                         <tr>
                         	<td colspan="10">
                         		<table class="flowList" style="width:100%;">
                         			<tr>
                         				<td>使用人工號</td>
                         				<td>使用人姓名</td>
                         				<td>需求地點</td>
                         				<td>需求說明</td>
                         			</tr>
                        			<c:forEach items="${newTelephones}" var="item" varStatus="rs">
                         			<tr id="newTelephones${rs.index}" class="newTelephonesTr">
                         				<td>${item.empNo}</td>
                         				<td>${item.empName}</td>
                         				<td>${item.requireLocation}</td>
                         				<td>${item.requireDetail}</td>
                         			</tr>
                         			</c:forEach>
                         		</table>
                         	</td>
                         </tr>
                         </c:if>
                         
                         <c:if test="${not empty transTelephones}">
                         <tr>
                            <td colspan="10" class="td_style1">分機號碼遷移</td>
                         </tr>
                         <tr>
                         	<td colspan="10">
                         		<table class="flowList" style="width:100%;">
                         			<tr>
                         				<td>需遷移號碼</td>
                         				<td>使用人工號</td>
                         				<td>使用人姓名</td>
                         				<td>原使用地點</td>
                         				<td>現需求地點</td>
                         				<td>需求說明</td>
                         			</tr>
                        			<c:forEach items="${transTelephones}" var="item" varStatus="rs">
                         			<tr id="transTelephones${rs.index}" class="transTelephonesTr">
                         				<td>${item.transNo}</td>
                         				<td>${item.empNo}</td>
                         				<td>${item.empName}</td>
                         				<td>${item.oldLocation}</td>
                         				<td>${item.requireLocation}</td>
                         				<td>${item.requireDetail}</td>
                         			</tr>
                         			</c:forEach>
                         		</table>
                         	</td>
                         </tr>
                        </c:if>
                        
                        <c:if test="${not empty wfcommnetProcessEntity.mergeNo}">
                        <tr>
                            <td colspan="10" class="td_style1">分機號碼并機</td>
                         </tr>
                         <tr align="center">
                         	<td width="6%">分機號碼</td>
                            <td colspan="3">${wfcommnetProcessEntity.mergeNo}</td>
                            <td width="6%">號碼數量</td>
                            <td width="6%">${wfcommnetProcessEntity.mergeNum}</td>
                            <td width="6%">需求說明</td>
                            <td colspan="3">${wfcommnetProcessEntity.mergeDetail}</td>
                         </tr>
                        </c:if>
                        
                        <c:if test="${not empty wfcommnetProcessEntity.deleteNo}">
                        <tr>
                            <td colspan="10" class="td_style1">刪除分機</td>
                         </tr>
                         <tr align="center">
                         	<td width="6%">分機號碼</td>
                            <td colspan="3">${wfcommnetProcessEntity.deleteNo}</td>
                            <td width="6%">號碼數量</td>
                            <td width="6%">${wfcommnetProcessEntity.deleteNum}</td>
                            <td width="6%">需求說明</td>
                            <td colspan="3">${wfcommnetProcessEntity.deleteDetail}</td>
                         </tr>
                         </c:if>
                         </c:if>
                        
                         <c:if test="${not empty newSipnos 
                         || not empty wfcommnetProcessEntity.deleteSipNo 
                         || not empty newCiscos 
                         || not empty wfcommnetProcessEntity.deleteCiscoNo
                         || not empty transCiscos
                         || not empty mergeCiscos}">
                         <tr>
                            <td colspan="10" class="td_style1">網絡電話</td>
                         </tr>
                         <c:if test="${not empty newSipnos}">
                         <tr>
                            <td colspan="10" class="td_style1">新增SIP號碼</td>
                         </tr>
                         <tr>
                         	<td colspan="10">
                         		<table class="flowList" style="width:100%;">
                         			<tr>
                         				<td>使用人工號</td>
                         				<td>使用人姓名</td>
                         				<td>需求地點</td>
                         				<td>需求說明</td>
                         			</tr>
                        			<c:forEach items="${newSipnos}" var="item" varStatus="rs">
                         			<tr id="newSipnos${rs.index}" class="newSipnosTr">
                         				<td>${item.empNo}</td>
                         				<td>${item.empName}</td>
                         				<td>${item.requireLocation}</td>
                         				<td>${item.requireDetail}</td>
                         			</tr>
                         			</c:forEach>
                         		</table>
                         	</td>
                         </tr>
                         </c:if>
                         
                         <c:if test="${not empty wfcommnetProcessEntity.deleteSipNo}">
                         <tr>
                            <td colspan="10" class="td_style1">刪除SIP號碼</td>
                         </tr>
                         <tr align="center">
                         	<td width="6%">號碼</td><td colspan="3">${wfcommnetProcessEntity.deleteSipNo}</td>
                            <td width="6%">號碼數量</td><td width="6%">${wfcommnetProcessEntity.deleteSipNum}</td>
                            <td width="6%">需求說明</td><td colspan="3">${wfcommnetProcessEntity.deleteSipDetail}</td>
                         </tr>
                         </c:if>
                         
                         <c:if test="${not empty newCiscos}">
                         <tr>
                            <td colspan="10" class="td_style1">新增思科IP Phone號碼</td>
                         </tr>
                         
                         <tr>
                         	<td colspan="10">
                         		<table class="flowList" style="width:100%;">
                         			<tr>
                         				<td>使用人工號</td>
                         				<td>使用人姓名</td>
                         				<td>型號</td>
                         				<td>序列號</td>
                         				<td>MAC地址</td>
                         				<td>需求地點</td>
                         				<td>需求說明</td>
                         			</tr>
                        			<c:forEach items="${newCiscos}" var="item" varStatus="rs">
                         			<tr id="newCiscos${rs.index}" class="newCiscosTr">
                         				<td>${item.empNo}</td>
                         				<td>${item.empName}</td>
                         				<td>${item.model}</td>
                         				<td>${item.imei}</td>
                         				<td>${item.macAddress}</td>
                         				<td>${item.requireLocation}</td>
                         				<td>${item.requireDetail}</td>
                         			</tr>
                         			</c:forEach>
                         		</table>
                         	</td>
                         </tr>
                         </c:if>
                        
                        <c:if test="${not empty wfcommnetProcessEntity.deleteCiscoNo}">
                         <tr>
                            <td colspan="10" class="td_style1">刪除思科IP Phone號碼</td>
                         </tr>
                         <tr align="center">
                         	<td width="6%">號碼</td><td colspan="3">${wfcommnetProcessEntity.deleteCiscoNo}</td>
                            <td width="6%">號碼數量</td><td width="6%">${wfcommnetProcessEntity.deleteCiscoNum}</td>
                            <td width="6%">需求說明</td><td colspan="3">${wfcommnetProcessEntity.deleteCiscoDetail}</td>
                         </tr>
                         </c:if>
                         
                         
                         <c:if test="${not empty transCiscos}">
                         <tr>
                            <td colspan="10" class="td_style1">思科號碼更換話機</td>
                         </tr>
                         
                         <tr>
                         	<td colspan="10">
                         		<table class="flowList" style="width:100%;">
                         			<tr>
                         				<td>IP Phone號碼</td>
                         				<td>使用人工號</td>
                         				<td>使用人姓名</td>
                         				<td>新型號</td>
                         				<td>新序列號</td>
                         				<td>新MAC地址</td>
                         				<td>需求說明</td>
                         			</tr>
                        			<c:if test="${not empty transCiscos}">
                        			<c:forEach items="${transCiscos}" var="item" varStatus="rs">
                         			<tr id="transCiscos${rs.index}" class="transCiscosTr">
                         				<td>${item.ipPhoneNo}</td>
                         				<td>${item.empNo}</td>
                         				<td>${item.empName}</td>
                         				<td>${item.model}</td>
                         				<td>${item.imei}</td>
                         				<td>${item.macAddress}</td>
                         				<td>${item.requireDetail}</td>
                         			</tr>
                         			</c:forEach>
                         			</c:if>
                         		</table>
                         	</td>
                         </tr>
                         </c:if>
                         
                         <c:if test="${not empty mergeCiscos}">
                         <tr>
                            <td colspan="10" class="td_style1">思科號碼并機</td>
                         </tr>
                         <tr>
                         	<td colspan="10">
                         		<table class="flowList" style="width:100%;">
                         			<tr>
                         				<td>IP Phone號碼</td>
                         				<td>使用人工號</td>
                         				<td>使用人姓名</td>
                         				<td>新型號</td>
                         				<td>新序列號</td>
                         				<td>新MAC地址</td>
                         				<td>需求說明</td>
                         			</tr>
                        			<c:if test="${not empty mergeCiscos}">
                        			<c:forEach items="${mergeCiscos}" var="item" varStatus="rs">
                         			<tr id="mergeCiscos${rs.index}" class="mergeCiscosTr">
                         				<td>${item.ipPhoneNo}</td>
                         				<td>${item.empNo}</td>
                         				<td>${item.empName}</td>
                         				<td>${item.model}</td>
                         				<td>${item.imei}</td>
                         				<td>${item.macAddress}</td>
                         				<td>${item.requireDetail}</td>
                         			</tr>
                         			</c:forEach>
                         			</c:if>
                         		</table>
                         	</td>
                         </tr>
                         </c:if>
                        </c:if>
                        
                        <c:if test="${not empty wfcommnetProcessEntity.newLineLocation
                        || not empty wfcommnetProcessEntity.deleteLineNo
                        || not empty transLines
                        || not empty wfcommnetProcessEntity.displayNo}">
                        <tr>
                            <td colspan="10" class="td_style1">直線電話</td>
                        </tr>
                        
                        <c:if test="${not empty wfcommnetProcessEntity.newLineLocation}">
                        <tr>
                            <td colspan="10" class="td_style1">新增直線電話</td>
                        </tr>
                        <tr align="center">
                         	<td width="6%">需求地點</td><td colspan="3">${wfcommnetProcessEntity.newLineLocation}</td>
                            <td width="6%">需求數量</td><td width="6%">${wfcommnetProcessEntity.newLineNum}</td>
                            <td width="6%">需求說明</td><td colspan="3">${wfcommnetProcessEntity.newLineDetail}</td>
                        </tr>
                        </c:if>
                        
                        <c:if test="${not empty wfcommnetProcessEntity.deleteLineNo}">
                        <tr>
                            <td colspan="10" class="td_style1">刪除直線電話</td>
                        </tr>
                        <tr align="center">
                         	<td width="6%">直線號碼</td><td colspan="3">${wfcommnetProcessEntity.deleteLineNo}</td>
                            <td width="6%">號碼數量</td><td width="6%">${wfcommnetProcessEntity.deleteLineNum}</td>
                            <td width="6%">需求說明</td><td colspan="3">${wfcommnetProcessEntity.deleteLineDetail}</td>
                        </tr>
                        </c:if>
                        
                        <c:if test="${not empty transLines}">
                        <tr>
                            <td colspan="10" class="td_style1">遷移直線電話</td>
                        </tr>
                         <tr>
                         	<td colspan="10">
                         		<table class="flowList" style="width:100%;">
                         			<tr>
                         				<td>需遷移號碼</td>
                         				<td>原使用地點</td>
                         				<td>現使用地點</td>
                         				<td>需求說明</td>
                         			</tr>
                        			<c:if test="${not empty transLines}">
                        			<c:forEach items="${transLines}" var="item" varStatus="rs">
                         			<tr id="transLines${rs.index}" class="transLinesTr">
                         				<td>${item.transNo}</td>
                         				<td>${item.oldLocation}</td>
                         				<td>${item.newLocation}</td>
                         				<td>${item.requireDetail}</td>
                         			</tr>
                         			</c:forEach>
                         			</c:if>
                         		</table>
                         	</td>
                         </tr>
                         </c:if>
                        
                        <c:if test="${not empty wfcommnetProcessEntity.displayNo}">
                        <tr>
                            <td colspan="10" class="td_style1">話機功能設定</td>
                        </tr>
                        <tr>
                            <td colspan="10">
                            	<div class="functionSetDiv"></div>
                                <input id="functionSet" name="wfcommnetProcess.functionSet"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfcommnetProcessEntity.functionSet}"/>
                            </td>
                        </tr>
                        <tr>
                        	<td colspan="10">
                        		<table class="flowList" style="width:100%;">
                        			<tr>
                        				<td>號碼</td>
                        				<td>話機類型(仅來電顯示需填寫)</td>
                        				<td>號碼數量</td>
                        				<td>需求說明</td>
                        			</tr>
                        			<tr>
                        				<td>${wfcommnetProcessEntity.displayNo}</td>
                         				<td>${wfcommnetProcessEntity.displayType}</td>
                         				<td>${wfcommnetProcessEntity.displayNum}</td>
                         				<td>${wfcommnetProcessEntity.displayDetail}</td>
                        			</tr>
                        		</table>
                        	</td>
                        </tr>
                        </c:if>
                        </c:if>
                        
                        <c:if test="${not empty wfcommnetProcessEntity.interComFmDetail 
                        || not empty wfcommnetProcessEntity.otherRequireDetail}">
                        <tr>
                            <td colspan="10" class="td_style1">其他需求</td>
                        </tr>
                        <c:if test="${not empty wfcommnetProcessEntity.interComFmDetail}">
                        <tr>
                            <td colspan="10" class="td_style1">對講機調頻</td>
                        </tr>
                        <tr align="center">
                        	<td>需求說明</td>
                        	<td colspan="9">${wfcommnetProcessEntity.interComFmDetail}</td>
                        </tr>
                        </c:if>
                        <c:if test="${not empty wfcommnetProcessEntity.otherRequireDetail}">
                        <tr>
                            <td colspan="10" class="td_style1">其他需求</td>
                        </tr>
                        <tr align="center">
                        	<td>需求說明</td>
                        	<td colspan="9">${wfcommnetProcessEntity.otherRequireDetail}</td>
                        </tr>
                        </c:if>
                        </c:if>
                    </table>
                </td>
           </tr>
            <tr>
                <td>
                    <table class="formList">
                    	<tr align="center">
                            <td colspan="2" class="td_style1">需求項目</td>
                        </tr>
	           			 <tr align="center">
                            <td><nobr>是否向中央網通發送郵件</nobr></td>
                            <td>
                            	<input type="radio" name="wfcommnetProcess.sendMail" value="1" <c:if test="${wfcommnetProcessEntity.sendMail == '1'}">checked="checked"</c:if> onclick="javascript:return false;"/>發送郵件
                            	<input type="radio" name="wfcommnetProcess.sendMail" value="0" <c:if test="${wfcommnetProcessEntity.sendMail == '0'}">checked="checked"</c:if> onclick="javascript:return false;"/>不發送郵件
                            </td>
                        </tr>  
                        <tr align="center">
                            <td>是否拋轉中央網通網站</td>
                            <td>
                            	<input type="radio" name="wfcommnetProcess.postData" value="1" <c:if test="${wfcommnetProcessEntity.postData == '1'}">checked="checked"</c:if> onclick="javascript:return false;"/>拋轉中央網通網站
                            	<input type="radio" name="wfcommnetProcess.postData" value="0" <c:if test="${wfcommnetProcessEntity.postData == '0'}">checked="checked"</c:if> onclick="javascript:return false;"/>不拋轉中央網通網站
                            </td>
                        </tr>   
                        <tr align="center">
                            <td>是否使用交換機</td>
                            <td>
                            	<input type="radio" name="wfcommnetProcess.useHub" value="1" <c:if test="${wfcommnetProcessEntity.useHub == '1'}">checked="checked"</c:if> onclick="javascript:return false;"/>是
                            	<input type="radio" name="wfcommnetProcess.useHub" value="0" <c:if test="${wfcommnetProcessEntity.useHub == '0'}">checked="checked"</c:if> onclick="javascript:return false;"/>否
                            </td>
                        </tr> 
                        <c:if test="${wfcommnetProcessEntity.useHub == 1}">
                        <tr align="center" id="hubInfo">
                        	<td colspan="2">
                        		<table class="formList">
                        			<tr align="center">
                        				<td>規格型號</td>
                        				<td>數量</td>
                        				<td>單價</td>
                        			</tr>
                        			<tr align="center">
                        				<td>${wfcommnetProcessEntity.hubSpec}</td>
                        				<td>${wfcommnetProcessEntity.hubNum}</td>
                        				<td>${wfcommnetProcessEntity.hubAmmount}</td>
                        			</tr>
                        		</table>
                        	</td>
                        </tr>
                        </c:if>
                        <tr align="center">
                            <td>是否使用材料</td>
                            <td>
                            	<input type="radio" name="wfcommnetProcess.useMaterial" value="1" <c:if test="${wfcommnetProcessEntity.useMaterial == '1'}">checked="checked"</c:if> onclick="javascript:return false;"/>是
                            	<input type="radio" name="wfcommnetProcess.useMaterial" value="0" <c:if test="${wfcommnetProcessEntity.useMaterial == '0'}">checked="checked"</c:if> onclick="javascript:return false;"/>否
                            </td>
                        </tr>
                        <c:if test="${not empty materials && wfcommnetProcessEntity.useMaterial == 1}"> 
                        <tr align="center" id="materialInfo">
                        	<td colspan="2">
                        		<table class="formList">
                        			<tr align="center">
                        				<td>材料名稱</td>
                        				<td>規格型號</td>
                        				<td>單價</td>
                        				<td>單位</td>
                        				<td>數量</td>
                        				<td>是否利舊材料</td>
                        				<td>合計</td>
                        			</tr>
                        			
                        			<c:forEach items="${materials}" var="item" varStatus="rs">
                                        <tr align="center">
                                            <td><select disabled id="mt${rs.index}" class="easyui-combobox" value="${item.materialType}" name="materials[${rs.index}].materialType" style="width:150px;"></select></td>
                                            <td><select disabled id="ms${rs.index}" class="easyui-combobox" value="${item.materialSpec}" name="materials[${rs.index}].materialSpec" style="width:200px;"></select></td>
                                            <td>${item.singleAmount}</td>
                                            <td><select disabled id="mu${rs.index}" class="easyui-combobox" value="${item.materialUnit}" name="materials[${rs.index}].materialUnit" style="width:150px;"></select></td>
                                            <td>${item.nums}</td>
                                            <td><select disabled id="uot${rs.index}" class="easyui-combobox" value="${item.useOldType}" name="materials[${rs.index}].useOldType" style="width:150px;"></select></td>
                                            <td class="itemCount">
                                                <fmt:formatNumber type="number" value="${item.nums * item.singleAmount + 0.0001}" pattern="0.00" maxFractionDigits="2"/>
                                            </td>
                                        </tr>
				                        <c:set var="vitemCount" value="${vitemCount + item.nums * item.singleAmount}"/>
		                       		</c:forEach>
                        		</table>
                        	</td>
                        </tr>
                        </c:if>
           			 	<tr align="center">
                            <td>費用合計</td>
                            <td id="item_count_total">
                            <c:if test="${not empty wfcommnetProcessEntity.hubNum && not empty wfcommnetProcessEntity.hubAmmount}">
                            	<c:set var="vitemCount" value="${vitemCount + wfcommnetProcessEntity.hubNum * wfcommnetProcessEntity.hubAmmount}"/>
                            </c:if>
                            <fmt:formatNumber type="number" value="${vitemCount + 0.0001}" pattern="0.00" maxFractionDigits="2"/>
                            </td>
                        </tr>
                       <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${wfcommnetProcessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','通訊網絡服務申請單流程');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfcommnetProcessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                               <c:if test="${wfcommnetProcessEntity.workstatus!=null&&wfcommnetProcessEntity.workstatus==3}">
                                  <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                      style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                               </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
<div id="dlg"></div>
<script type="text/javascript">
    function loadPageData() {
        <c:forEach items="${materials}" var="item" varStatus="rs">
        $("#mt${rs.index}").combobox("setValue", "${item.materialType}");
        $("#ms${rs.index}").combobox("setValue", "${item.materialSpec}");
        $("#mu${rs.index}").combobox("setValue", "${item.materialUnit}");
        $("#uot${rs.index}").combobox("setValue", "${item.useOldType}");
        </c:forEach>
    }
</script>
<script src='${ctx}/static/js/information/wfcommnetprocess.min.js?random=20240924'></script>
</body>
</html>