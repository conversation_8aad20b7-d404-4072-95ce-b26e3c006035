<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>Super Notes審核主管變更申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <style type="text/css">
        .td_style2 {
            text-align: center;
            font-weight: bold;
            font-size: 15px !important;
            color: #1a7bc9;
        }
    </style>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wfsupernotechange/${action}" method="post">
    <!--
                    ylno6 資訊網絡部級主管
        ylname6 資訊網絡部級主管
        ylno8 資訊網絡責任人作業
        ylname8 資訊網絡責任人作業
        ylno9 郵箱管理員作業
        ylname9 郵箱管理員作業
        workstatus 表單狀態
        complettime 完成時間
        applybuilding 樓層
        makerfactoryid 填單人廠區
        workflowversion 工作流版本
        ylno10 產品群級主管
        ylname10 產品群級主管
        ylno11 群最高主管
        ylname11 群最高主管
        createBy 創建人
        createDate 創建人
        updateBy 更新者
        updateDate 更新時間
        delFlag 刪除標識
        makerdeptno 填單人所在部門
        id 主鍵
        attachids 附件ID
        workflowid 流程id
        serialno 工單流水號
        processid 工單實例ID
        makerno 填單人
        makername 填單人名稱
        createtime 創建時間
        makerip 填單人IP
        applyno 申請人工號
        applyname 申請人姓名
        applydeptno 申請人部門代碼
        applycostno 申請人費用代碼
        applyfactoryid 申請人廠區代碼
        applyleveltype 申請人資位 dict_leveltype
        applymanager 申請人管理職 dict_ismanager
        applyemail 申請人聯繫郵箱
        applyarea 使用區域
        applyphone 聯繫分機
        applydeptname 單位
        describtion 需求說明
        ylno1 資訊網絡責任人確認
        ylname1 資訊網絡責任人確認
        kchargeno 課級主管
        kchargename 課級主管
        bchargeno 部級主管
        bchargename 部級主管
        cchargeno 廠級主管
        cchargename 廠級主管
        zchargeno 製造處級主管
        zchargename 製造處級主管
        zcchargeno 製造總處級主管
        zcchargename 製造總處級主管
        pcchargeno 產品處級主管
        pcchargename 產品處級主管
        ylno5 資訊網絡課級主管
        ylname5 資訊網絡課級主管
               -->
    <input id="ids" name="ids" type="hidden" value="${wfSupernoteChangeEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfSupernoteChangeEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">Super Notes審核主管變更申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfSupernoteChangeEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfSupernoteChangeEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfSupernoteChangeEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfSupernoteChangeEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfSupernoteChangeEntity.makerno}/${wfSupernoteChangeEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td>承辦人工號</td>
                            <td class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true,disabled:true"
                                       value="${wfSupernoteChangeEntity.applyno}" onblur="queryUserInfo('apply');"/>
                            </td>
                            <td>承辦人</td>
                            <td class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfSupernoteChangeEntity.applyname}"/>
                            </td>
                            <td>單位代碼</td>
                            <td class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfSupernoteChangeEntity.applydeptno }"/>
                            </td>
                            <td>費用代碼</td>
                            <td class="td_style1">
                                <input id="applycostno" name="applycostno"
                                       class="easyui-validatebox" data-options="width: 90,required:true,disabled:true"
                                       value="${wfSupernoteChangeEntity.applycostno }"/>
                            </td>
                            <td>所在廠區</td>
                            <td class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfSupernoteChangeEntity.applyfactoryid }"
                                       data-options="width: 120,disabled:true,required:true,validType:'comboxValidate[\'applyfactoryid\',\'请選擇廠區\']'"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="applyleveltype" class="easyui-validatebox inputCss"
                                       data-options="width: 80"
                                       value="${wfSupernoteChangeEntity.applyleveltype }" readonly/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="applymanager" class="easyui-validatebox inputCss"
                                       data-options="width: 80"
                                       value="${wfSupernoteChangeEntity.applymanager }" readonly/>
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       value="${wfSupernoteChangeEntity.applyemail }" style="width:300px;"
                                       data-options="required:true,disabled:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td>使用區域</td>
                            <td class="td_style1">
                                <input id="applyarea" name="applyarea" class="easyui-combobox"
                                       data-options="width: 70,disabled:true,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wfSupernoteChangeEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="applybuilding" class="easyui-combobox"
                                       data-options="width: 70,disabled:true,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wfSupernoteChangeEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機</td>
                            <td class="td_style1">
                                <input id="applyphone" name="applyphone" class="easyui-validatebox"
                                       style="width:90px;position:relative;"
                                       value="${wfSupernoteChangeEntity.applyphone }"
                                       data-options="required:true,disabled:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>單位</td>
                            <td colspan="7" class="td_style1">
                                <input id="applydeptname" name="applydeptname" class="easyui-validatebox"
                                       data-options="width: 410,disabled:true,required:true"
                                       value="${wfSupernoteChangeEntity.applydeptname }"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
            <tr>
                <td colspan="8" class="td_style1">申請詳細信息</td>
            </tr>
            <td>
                <div id="info_Body">
                    <c:if test="${wfSupernoteChangeEntity.itemsEntity!=null||wfSupernoteChangeEntity.itemsEntity.size()>0}">
                    <c:forEach items="${wfSupernoteChangeEntity.itemsEntity}"
                               var="itemsEntity"
                               varStatus="status">
                    <table class="formList" id="info_Body_Pid${status.index}">
                        <tbody id="info_Body_item${status.index}">
                        <tr align="center">
                            <td width="5%">申請人工號</td>
                            <td width="5%" class="td_style1">
                                <input id="applyno${status.index}"
                                       name="itemsEntity[${status.index}].applyno"
                                       class="easyui-validatebox"
                                       data-options="width: 80,disabled:true,required:true"
                                       value="${itemsEntity.applyno }"
                                       onblur="queryApplyUserInfo(${status.index});"/>
                            </td>
                            <td width="5%">申請人</td>
                            <td width="5%" class="td_style1">
                                <input id="applyname${status.index}"
                                       name="itemsEntity[${status.index}].applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${itemsEntity.applyname }"/>
                            </td>
                            <td width="5%">管理職</td>
                            <td width="5%" class="td_style1">
                                <input id="applymanager${status.index}"
                                       name="itemsEntity[${status.index}].applymanager"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${itemsEntity.applymanager }"/>
                            </td>
                            <td width="3%">安保區域</td>
                            <td width="7%">
                                <div class="securityarea${status.index}Div"></div>
                                <input id="securityarea${status.index}Value"
                                       name="securityarea${status.index}Value"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${itemsEntity.securityarea }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">
                                郵箱地址
                            </td>
                            <td colspan="6" class="td_style1">
                                <input id="applymail${status.index}"
                                       name="itemsEntity[${status.index}].applymail"
                                       class="easyui-validatebox"
                                       value="${itemsEntity.applymail }" style="width:300px;"
                                       data-options="required:true,disabled:true,validType:'email[\'applymail${status.index}\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>
                                主管工號
                            </td>
                            <td>
                                審核人
                            </td>
                            <td colspan="2">
                                寄件審核主管類型
                            </td>
                            <td>
                                管理職
                            </td>
                            <td colspan="2">
                                寄件審核主管郵箱
                            </td>
                            <td>
                                操作
                            </td>
                        </tr>
                        <c:forEach items="${itemsEntity.itemsEntityList}"
                                   var="itemsEntityList"
                                   varStatus="status1">
                            <tr align="center" id="changeKeyItem${status.index}${status1.index}">
                                <td>
                                    <input id="keyno${status.index}${status1.index}"
                                           name="itemsEntity[${status.index}].itemsEntityList[${status1.index}].keyno"
                                           class="easyui-validatebox"
                                           data-options="width: 80,required:true,disabled:true"
                                           value="${itemsEntityList.keyno }"
                                           onblur="queryKeyUserInfo('${status.index}${status1.index}');"/>
                                </td>
                                <td class="td_style2">
                                    <input id="keyname${status.index}${status1.index}"
                                           name="itemsEntity[${status.index}].itemsEntityList[${status1.index}].keyname"
                                           class="easyui-validatebox" readonly
                                           data-options="width: 80"
                                           value="${itemsEntityList.keyname }"/>
                                </td>
                                <td colspan="2">
                                    <input id="keytype${status.index}${status1.index}"
                                           name="itemsEntity[${status.index}].itemsEntityList[${status1.index}].keytype"
                                           class="easyui-combobox" disabled
                                           value="${itemsEntityList.keytype}"
                                           data-options="panelHeight: 200,width: 200,valueField:'value',textField:'label',url:'${ctx}/system/dict/getDictByType/dict_superCheckType'"/>
                                </td>
                                <td class="td_style2">
                                    <input id="keymanager${status.index}${status1.index}"
                                           name="itemsEntity[${status.index}].itemsEntityList[${status1.index}].keymanager"
                                           class="easyui-validatebox" readonly
                                           data-options="width: 80"
                                           value="${itemsEntityList.keymanager }"/>
                                </td>
                                <td colspan="2">
                                    <input id="keymail${status.index}${status1.index}"
                                           name="itemsEntity[${status.index}].itemsEntityList[${status1.index}].keymail"
                                           class="easyui-validatebox"
                                           data-options="width: 200,required:true,disabled:true"
                                           value="${itemsEntityList.keymail }"/>
                                </td>
                                <td>
                                    <a href="javascript:void(0)"
                                       class="easyui-linkbutton"
                                       data-options="plain:true,disabled:true,iconCls:'icon-remove'"></a>
                                </td>
                            </tr>
                        </c:forEach>
                        </tbody>
                        <tr align="center">
                            <td colspan="8">
                                <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add"
                                   plain="true" data-options="disabled:true"
                                   onclick="addItem('${status.index}');">添加一行</a>
                                <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove"
                                   plain="true" data-options="disabled:true"
                                   onclick="delItems('${status.index}')">删除此筆</a>
                            </td>
                        </tr>
                        </c:forEach>
                        </c:if>
                    </table>
                </div>
            </td>
            <tr align="center">
                <td colspan="8">
                    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true"
                       data-options="disabled:true" onclick="addItems();">添加一筆</a>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${wfSupernoteChangeEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>需求說明</td>
                            <td align="left" colspan="9"><textarea
                                    id="describtion"
                                    name="describtion" data-options="required:true,disabled:true"
                                    maxlength="100"
                                    class="easyui-validatebox" style="width:900px;"
                                    rows="5" cols="6">${wfSupernoteChangeEntity.describtion}</textarea><span
                                    id="txtNum"></span></td>
                        </tr>
                        <tr align="center">
                            <td>備註/要求</td>
                            <td align="left" colspan="9" class="td_style1">
                                1.審核主管限定：關鍵字必須為製造處或以上主管，跨事業群必須為副理或以上主管，跨製造處/附件審核必須為課級或以上主管；</br>
                                2.審核優先級：Internet審核 --＞跨事業群審核--＞附件 --＞關鍵字審核--＞跨製造處--＞安保區域；</br>
                                3.跨事業群審核：集團內其他事業群的郵箱地址；跨製造處審核：發送到事業群內產品處外的郵箱地址；附件審核：發送內容帶有附件的郵件；關鍵字審核：發送內容帶有關鍵字的郵件；</br>
                                4.以上所有審核均可另設置抄送，由用戶單位決定是否添加抄送主管；</br>
                                5.<font color="red">Supernotes寄件審核主管變更需處級主管核准；</font></br>
                                6.郵箱變更審核主管超過10筆請上傳<a href="${ctx}/wfsupernotechange/downLoad/commitmentTpl">iPEBG事業群郵箱寄件審核主管批量變更提報表</a>。
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','Super Notes審核主管變更申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfSupernoteChangeEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfSupernoteChangeEntity.workstatus!=null&&wfSupernoteChangeEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<div id="dlg"></div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<script src='${ctx}/static/js/information/wfsupernotechange.js?random=<%= Math.random()%>'></script>
</body>
</html>