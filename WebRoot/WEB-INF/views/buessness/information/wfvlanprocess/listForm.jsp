<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>無線區域網絡申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
            type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfvlanprocess/${action}"
      method="post">
    <input id="ids" name="ids" type="hidden"
           value="${wfvlanprocessEntity.id }"/> <input id="serialno"
                                                       name="wfvlanprocess.serialno" type="hidden"
                                                       value="${wfvlanprocessEntity.serialno }"/> <input id="makerno"
                                                                                                         name="wfvlanprocess.makerno"
                                                                                                         type="hidden"
                                                                                                         value="${wfvlanprocessEntity.makerno }"/>
    <input id="makername"
           name="wfvlanprocess.makername" type="hidden"
           value="${wfvlanprocessEntity.makername }"/> <input id="makerdeptno"
                                                              name="wfvlanprocess.makerdeptno" type="hidden"
                                                              value="${wfvlanprocessEntity.makerdeptno }"/> <input
        id="makerfactoryid" name="wfvlanprocess.makerfactoryid" type="hidden"
        value="${wfvlanprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">無線區域網絡使用申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;"> <c:choose>
            <c:when test="${wfvlanprocessEntity.serialno==null}">
                提交成功后自動編碼
            </c:when>
            <c:otherwise>
                ${wfvlanprocessEntity.serialno}
            </c:otherwise>
        </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;"> <c:choose>
            <c:when test="${wfvlanprocessEntity.createtime==null}">
                YYYY/MM/DD
            </c:when>
            <c:otherwise>
							<input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfvlanprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
            </c:otherwise>
        </c:choose>
				</span>
        </div>
        <c:if test="${empty wfvlanprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfvlanprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${wfvlanprocessEntity.makerno}/${wfvlanprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="7%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td class="td_style1"><input id="dealno"
                                                         name="wfvlanprocess.dealno" class="easyui-validatebox"
                                                         onblur="queryUserInfo(this)"
                                                         data-options="width:80,required:true"
                                                         value="${wfvlanprocessEntity.dealno}"/></td>
                            <td width="7%">承辦人</td>
                            <td class="td_style1"><input id="dealname"
                                                         name="wfvlanprocess.dealname" readonly
                                                         class="easyui-validatebox inputCss" data-options="width: 70"
                                                         value="${wfvlanprocessEntity.dealname}"/></td>
                            <td width="7%">單位代碼</td>
                            <td class="td_style1"><input id="dealdeptno"
                                                         name="wfvlanprocess.dealdeptno" readonly
                                                         class="easyui-validatebox inputCss" data-options="width:100"
                                                         value="${wfvlanprocessEntity.dealdeptno}"/></td>
                            <td width="7%">廠區&nbsp;<font color="red">*</font></td>
                            <td class="td_style1"><input id="dealchoosefactoryid"
                                                         name="wfvlanprocess.dealchoosefactoryid"
                                                         class="easyui-combobox"
                                                         data-options="width:90,required:true,validType:'comboxValidate[\'dealchoosefactoryid\',\'请選擇填單人廠區\']',onSelect:function(){onchangeFactory('dealchoosefactoryid');}"
                                                         value="${wfvlanprocessEntity.dealchoosefactoryid}"/><input
                                    class="easyui-validatebox" id="dealfactoryid"
                                    name="wfvlanprocess.dealfactoryid" style="width:30px" readonly
                                    type="hidden" value="${wfvlanprocessEntity.dealfactoryid}"/></td>
                            <td width="7%">聯繫電話&nbsp;<font color="red">*</font></td>
                            <td align="left"><input id="dealtel" style="width:80px;position:relative;"
                                                    name="wfvlanprocess.dealtel" class="easyui-validatebox"
                                                    data-options="width:80,validType:'tel[\'dealtel\',\'聯繫電話不正確\']',required:true,prompt:'579+66666'"
                                                    value="${wfvlanprocessEntity.dealtel}"/></td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td class="td_style1" colspan="3"><input id="dealdeptname"
                                                                     style="width:450px"
                                                                     name="wfvlanprocess.dealdeptname"
                                                                     class="easyui-validatebox"
                                                                     data-options="width:450,required:true"
                                                                     value="${wfvlanprocessEntity.dealdeptname}"/></td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td class="td_style1" colspan="5"><input id="dealemail"
                                                                     name="wfvlanprocess.dealemail"
                                                                     class="easyui-validatebox"
                                                                     data-options="width: 250,required:true,validType:'email[\'wfvlanprocess.dealemail\',\'郵箱的格式不正確\']'"
                                                                     value="${wfvlanprocessEntity.dealemail}"/></td>
                        </tr>
                        <tr align="center">
                            <td>使用區域&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1"><input id="applyarea"
                                                                     name="wfvlanprocess.applyarea"
                                                                     class="easyui-combobox"
                                                                     data-options="width:150,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                                                     value="${wfvlanprocessEntity.applyarea }"
                                                                     panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="wfvlanprocess.applybuilding"
                                       class="easyui-combobox"
                                       data-options="width:150,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wfvlanprocessEntity.applybuilding }"
                                       panelHeight="auto"/></td>
                            <td>安保區域&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style2">
                                <div class="securityareaDiv"></div>
                                <input id="securityarea"
                                       name="wfvlanprocess.securityarea" type="hidden"
                                       class="easyui-validatebox" data-options="width: 100"
                                       value="${wfvlanprocessEntity.securityarea }"/>
                            </td>
                            <td>申請方式&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style2">
                                <div class="applytypeDiv"></div>
                                <input id="applytype"
                                       name="wfvlanprocess.applytype" type="hidden"
                                       class="easyui-validatebox" data-options="width: 100"
                                       value="${wfvlanprocessEntity.applytype}"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="10" class="td_style1">單筆申請信息填寫（批量申請時不需填寫）</td>
                        </tr>
                        <tbody id="info_Body">
                        <c:if test="${wfvlanprocessitemEntity.size()==1}">
                            <c:forEach items="${wfvlanprocessitemEntity}" var="itemsEntity"
                                       varStatus="status">
                                <tr align="center">
                                    <td>申請人工號&nbsp;<font color="red">*</font></td>
                                    <td align="left"><input type="hidden" id="shunxu0"
                                                            name="wfvlanitem[0].shunxu" readonly
                                                            data-options="width:10"
                                                            value="${itemsEntity.shunxu}"/><input id="applyno0"
                                                                                                  name="wfvlanitem[0].applyno"
                                                                                                  class="easyui-validatebox"
                                                                                                  onblur="queryApplyInfo()"
                                                                                                  data-options="width:80,required:true"
                                                                                                  value="${itemsEntity.applyno}"/>
                                    </td>
                                    <td>申請人</td>
                                    <td width='9%' align="left"><input id="applyname0"
                                                                       name="wfvlanitem[0].applyname" readonly
                                                                       class="easyui-validatebox inputCss"
                                                                       data-options="width: 150"
                                                                       value="${itemsEntity.applyname}"/></td>
                                    <td>單位代碼</td>
                                    <td colspan="2" align="left"><input
                                            id="applydeptno0" name="wfvlanitem[0].applydeptno" readonly
                                            class="easyui-validatebox inputCss"
                                            data-options="width: 150" value="${itemsEntity.applydeptno}"/></td>
                                    <td>費用代碼&nbsp;<font color="red">*</font></td>
                                    <td align="left" colspan="2"><input
                                            id="applycostno0" name="wfvlanitem[0].applycostno"
                                            class="easyui-validatebox"
                                            data-options="width: 150" value="${itemsEntity.applycostno}"/></td>
                                    <!-- <td rowspan="8">刪除</td> -->
                                </tr>
                                <tr align="center">
                                    <td>資位</td>
                                    <td align="left"><input id="applyleveltype0"
                                                            name="wfvlanitem[0].applyleveltype" readonly
                                                            class="easyui-validatebox inputCss"
                                                            data-options="width: 150"
                                                            value="${itemsEntity.applyleveltype}"/></td>
                                    <td>管理職</td>
                                    <td align="left"><input id="applymanager0"
                                                            name="wfvlanitem[0].applymanager" readonly
                                                            class="easyui-validatebox inputCss"
                                                            data-options="width: 150"
                                                            value="${itemsEntity.applymanager}"/></td>
                                    <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                                    <td align="left" colspan="2"><input
                                            id="applyemail0" name="wfvlanitem[0].applyemail"
                                            class="easyui-validatebox"
                                            data-options="width:250,validType:'email[\'applyemail0\',\'郵箱的格式不正確\']'"
                                            value="${itemsEntity.applyemail}"/></td>
                                    <td>聯繫分機&nbsp;<font color="red">*</font></td>
                                    <td align="left"><input id="applyphone0" style="position:relative;"
                                                            name="wfvlanitem[0].applyphone" class="easyui-validatebox"
                                                            data-options="width:100,validType:'tel[\'applyphone0\',\'分機格式不正確\']',prompt:'579+66666'"
                                                            value="${itemsEntity.applyphone}"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>單位&nbsp;<font color="red">*</font></td>
                                    <td align="left" colspan="3"><input
                                            id="applydeptname0" name="wfvlanitem[0].applydeptname"
                                            class="easyui-validatebox" style="width:450px"
                                            data-options="width: 450"
                                            value="${itemsEntity.applydeptname}"/></td>
                                    <td>手機號碼&nbsp;<font color="red">*</font></td>
                                    <td align="left" colspan="2"><input id="vlanphone0"
                                                                        onblur="validMobilephone(vlanphone0)"
                                                                        name="wfvlanitem[0].vlanphone"
                                                                        class="easyui-validatebox"
                                                                        data-options="width: 150"
                                                                        value="${itemsEntity.vlanphone}"/>
                                    </td>
                                    <td>所在廠區&nbsp;<font color="red">*</font></td>
                                    <td align="left"><input id="applyfactoryid0"
                                                            name="wfvlanitem[0].applyfactoryid" class="easyui-combobox"
                                                            data-options="width: 150,validType:'comboxValidate[\'applyfactoryid0\',\'请选择所在廠區\']'"
                                                            value="${itemsEntity.applyfactoryid}"/></td>
                                </tr>
                                <tr align="center">
                                    <td>法人代碼</td>
                                    <td class="td_style1">
                                        <input id="applycompanycode0" name="wfvlanitem[0].applycompanycode"
                                               class="easyui-validatebox" data-options="width:80,required:true" value="${itemsEntity.applycompanycode}"/>
                                    </td>
                                    <td>法人名稱</td>
                                    <td class="td_style1" colspan="7">
                                        <input id="applycompanyname0" name="wfvlanitem[0].applycompanyname"
                                               class="easyui-validatebox" data-options="width:300,required:true" value="${itemsEntity.applycompanyname}"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>用戶名&nbsp;<font color="red">*</font></td>
                                    <td align="left"><input id="vlanname0"
                                                            name="wfvlanitem[0].vlanname" class="easyui-validatebox"
                                                            data-options="width: 150" value="${itemsEntity.vlanname}"/>
                                    </td>
                                    <td>使用期限&nbsp;<font color="red">*</font></td>
                                    <td align="left" colspan="6">自<input
                                            id="vlanstarttime0" name="wfvlanitem[0].vlanstarttime"
                                            class="Wdate" data-options="width:150,required:true"
                                            style="width:150px"
                                            value="<fmt:formatDate  pattern="yyyy-MM-dd"
								    value="${itemsEntity.vlanstarttime}"/>"
                                            onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"/>
                                        至<input id="vlanendtime0" name="wfvlanitem[0].vlanendtime"
                                                class="Wdate" data-options="width:150,required:true"
                                                style="width:150px"
                                                value="<fmt:formatDate  pattern="yyyy-MM-dd"
								    value="${itemsEntity.vlanendtime}"/>"
                                                onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'vlanstarttime0\')}',maxDate:'#F{$dp.$D(\'vlanstarttime0\',{M:12})}'})"/>（最長不超過12個月）
                                    </td>

                                </tr>
                                <tr align="center">
                                    <td>服務類型&nbsp;<font color="red">*</font></td>
                                    <td align="left" colspan="8">
                                        <div class="vlanapplytypeDiv0"></div>
                                        <input
                                                id="vlanapplytype0" name="wfvlanitem[0].vlanapplytype"
                                                readonly type="hidden" class="easyui-validatebox"
                                                style="width:50px;"
                                                value="${itemsEntity.vlanapplytype}"/></td>
                                </tr>
                                <tr align="center">
                                    <td>用戶類型&nbsp;<font color="red">*</font></td>
                                    <td align="left">
                                        <input id="vlanusertype0" name="wfvlanitem[0].vlanusertype" class="easyui-combobox"
                                               data-options="panelHeight:'auto',valueField:'value',textField:'label',editable:false,onBeforeLoad:function(){loadvlanusertype(0);},onSelect:function(){onchangeVlanusertype(0);}"
                                               style="width:100px;" value="${itemsEntity.vlanusertype}"/>
                                        <input id="vlanusertypename0" name="wfvlanitem[0].vlanusertypename" type="hidden" value="${itemsEntity.vlanusertypename}"/>
                                    </td>
                                    <td>終端類型&nbsp;<font color="red">*</font></td>
                                    <td align="left">
                                        <input id="vlanterminaltype0" name="wfvlanitem[0].vlanterminaltype" class="easyui-combobox"
                                               data-options="panelHeight:'auto',editable:false,onSelect:function(){changeVlanterminaltype(0);}"
                                               style="width:100px;"  value="${itemsEntity.vlanusertype}"/>
                                        <input id="vlanterminaltypename0" name="wfvlanitem[0].vlanterminaltypename" type="hidden" value="${itemsEntity.vlanterminaltypename}"/>
                                    </td>
                                    <td>SSID&nbsp;<font color="red">*</font></td>
                                    <td align="left" colspan="2">
                                        <input id="ssid0" name="wfvlanitem[0].ssid" class="easyui-combobox"
                                               data-options="panelHeight:'auto',editable:false"
                                               style="width:100px;"  value="${itemsEntity.ssid}"/>
                                        <input id="ssidelse0" name="wfvlanitem[0].ssidelse" class="easyui-validatebox" style="position:relative;"
                                               data-options="width: 150,prompt:'選擇其它時必填'" value="${itemsEntity.ssidelse}"/>
                                    </td>
                                    <td>MAC地址&nbsp;<font color="red">*</font></td>
                                    <td align="left" colspan="3"><input
                                            id="vlanterminalmac0" name="wfvlanitem[0].vlanterminalmac"
                                            class="easyui-validatebox"
                                            data-options="width: 150,validType:'mac[\'vlanterminalmac0\',\'MAC地址格式不正確\']'"
                                            value="${itemsEntity.vlanterminalmac}"/></td>
                                </tr>
                                <tr align="center">
                                    <td>申請服務需求詳細說明&nbsp;<font color="red">*</font></td>
                                    <td align="left" colspan="8"><textarea
                                            id="describtion0" name="wfvlanitem[0].describtion"
                                            oninput="return LessThanAuto(this,'txtNum');"
                                            onchange="return LessThanAuto(this,'txtNum');"
                                            onpropertychange="return LessThanAuto(this,'txtNum');"
                                            data-options="required:true" maxlength="100"
                                            class="easyui-validatebox" style="width:800px;height:40px;"
                                            rows="5" cols="6">${itemsEntity.describtion}</textarea><span
                                            id="txtNum"></span></td>
                                </tr>
                            </c:forEach>
                        </c:if>
                        <c:if test="${wfvlanprocessitemEntity.size()==0||wfvlanprocessitemEntity==null||(wfvlanprocessitemEntity!=null&&wfvlanprocessitemEntity.size()>1)}">
                            <tr align="center">
                                <td>申請人工號&nbsp;<font color="red">*</font></td>
                                <td align="left"><input type="hidden" id="shunxu0"
                                                        name="wfvlanitem[0].shunxu" readonly
                                                        data-options="width:10"
                                                        value="0"/><input id="applyno0"
                                                                          name="wfvlanitem[0].applyno"
                                                                          class="easyui-validatebox"
                                                                          onblur="queryApplyInfo()"
                                                                          data-options="width:80,required:true"
                                                                          value=""/></td>
                                <td>申請人</td>
                                <td width='9%' align="left"><input id="applyname0"
                                                                   name="wfvlanitem[0].applyname" readonly
                                                                   class="easyui-validatebox inputCss"
                                                                   data-options="width: 150"
                                                                   value=""/></td>
                                <td>單位代碼</td>
                                <td colspan="2" align="left"><input
                                        id="applydeptno0" name="wfvlanitem[0].applydeptno" readonly
                                        class="easyui-validatebox inputCss" data-options="width: 150"
                                        value=""/></td>
                                <td>費用代碼&nbsp;<font color="red">*</font></td>
                                <td align="left" colspan="2"><input
                                        id="applycostno0" name="wfvlanitem[0].applycostno"
                                        class="easyui-validatebox" data-options="width: 150"
                                        value=""/></td>
                            </tr>
                            <tr align="center">
                                <td>資位</td>
                                <td align="left"><input id="applyleveltype0"
                                                        name="wfvlanitem[0].applyleveltype" readonly
                                                        class="easyui-validatebox inputCss" data-options="width: 150"
                                                        value=""/></td>
                                <td>管理職</td>
                                <td align="left"><input id="applymanager0"
                                                        name="wfvlanitem[0].applymanager" readonly
                                                        class="easyui-validatebox inputCss" data-options="width: 150"
                                                        value=""/></td>
                                <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                                <td align="left" colspan="2"><input id="applyemail0"
                                                                    name="wfvlanitem[0].applyemail"
                                                                    class="easyui-validatebox"
                                                                    data-options="width:250,validType:'email[\'wfvlanprocess.dealemail\',\'郵箱的格式不正確\']'"
                                                                    value=""/></td>
                                <td>聯繫分機&nbsp;<font color="red">*</font></td>
                                <td align="left"><input id="applyphone0" style="position:relative;"
                                                        name="wfvlanitem[0].applyphone" class="easyui-validatebox"
                                                        data-options="width:100,validType:'tel[\'applyphone0\',\'分機格式不正確\']',prompt:'579+66666'"
                                                        value=""/></td>
                            </tr>
                            <tr align="center">
                                <td>單位&nbsp;<font color="red">*</font></td>
                                <td align="left" colspan="3"><input
                                        id="applydeptname0" name="wfvlanitem[0].applydeptname"
                                        class="easyui-validatebox" style="width:450px"
                                        data-options="width: 450" value=""/></td>
                                <td>手機號碼&nbsp;<font color="red">*</font></td>
                                <td align="left" colspan="2"><input id="vlanphone0"
                                                                    onblur="validMobilephone(vlanphone0)"
                                                                    name="wfvlanitem[0].vlanphone"
                                                                    class="easyui-validatebox"
                                                                    data-options="width: 150" value=""/></td>
                                <td>所在廠區&nbsp;<font color="red">*</font></td>
                                <td align="left"><input id="applyfactoryid0"
                                                        name="wfvlanitem[0].applyfactoryid" class="easyui-combobox"
                                                        data-options="width: 150" value=""/></td>
                            </tr>
                            <tr align="center">
                                <td>法人代碼</td>
                                <td class="td_style1">
                                    <input id="applycompanycode0" name="wfvlanitem[0].applycompanycode"
                                           class="easyui-validatebox" data-options="width:80,required:true" value=""/>
                                </td>
                                <td>法人名稱</td>
                                <td class="td_style1" colspan="7">
                                    <input id="applycompanyname0" name="wfvlanitem[0].applycompanyname"
                                           class="easyui-validatebox" data-options="width:300,required:true"  value=""/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>用戶名&nbsp;<font color="red">*</font></td>
                                <td align="left"><input id="vlanname0"
                                                        name="wfvlanitem[0].vlanname" class="easyui-validatebox"
                                                        data-options="width: 150" value=""/></td>
                                <td>使用期限&nbsp;<font color="red">*</font></td>
                                <td align="left" colspan="6">自<input
                                        id="vlanstarttime0" name="wfvlanitem[0].vlanstarttime"
                                        class="Wdate" data-options="width:150,required:true"
                                        style="width:150px"
                                        onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"/>
                                    至<input id="vlanendtime0" name="wfvlanitem[0].vlanendtime"
                                            class="Wdate" data-options="width:150,required:true"
                                            style="width:150px"
                                            onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'vlanstarttime0\')}',maxDate:'#F{$dp.$D(\'vlanstarttime0\',{M:12})}'})"/>（最長不超過12個月）
                                </td>

                            </tr>
                            <tr align="center">
                                <td>服務類型&nbsp;<font color="red">*</font></td>
                                <td align="left" colspan="8">
                                    <div class="vlanapplytypeDiv0"></div>
                                    <input
                                            id="vlanapplytype0" name="wfvlanitem[0].vlanapplytype"
                                            readonly type="hidden" class="easyui-validatebox"
                                            style="width:50px;" value=""/></td>
                            </tr>
                            <tr align="center">
                                <td>用戶類型&nbsp;<font color="red">*</font></td>
                                <td align="left">
                                    <input id="vlanusertype0" name="wfvlanitem[0].vlanusertype" class="easyui-combobox"
                                               data-options="panelHeight:'auto',valueField:'value',textField:'label',editable:false,onBeforeLoad:function(){loadvlanusertype(0);},onSelect:function(){onchangeVlanusertype(0);}"
                                               style="width:100px;" value=""/>
                                    <input id="vlanusertypename0" name="wfvlanitem[0].vlanusertypename" type="hidden" value=""/>
                                </td>
                                <td>終端類型&nbsp;<font color="red">*</font></td>
                                <td align="left">
                                    <input id="vlanterminaltype0" name="wfvlanitem[0].vlanterminaltype" class="easyui-combobox"
                                           data-options="panelHeight:'auto',editable:false,onSelect:function(){changeVlanterminaltype(0);}"
                                           style="width:100px;"  value=""/>
                                    <input id="vlanterminaltypename0" name="wfvlanitem[0].vlanterminaltypename" type="hidden" value=""/>
                                </td>
                                <td>SSID&nbsp;<font color="red">*</font></td>
                                <td align="left" colspan="2">
                                    <input id="ssid0" name="wfvlanitem[0].ssid" class="easyui-combobox"
                                           data-options="panelHeight:'auto',editable:false,onSelect:function(){changeSsid(0);}"
                                           style="width:100px;"  value=""/>
                                    <input id="ssidelse0" style="position:relative;" name="wfvlanitem[0].ssidelse" class="easyui-validatebox"
                                           data-options="width: 150,prompt:'選擇其它時必填'" value=""/>
                                </td>
                                <td>MAC地址&nbsp;<font color="red">*</font></td>
                                <td align="left">
                                    <input id="vlanterminalmac0" name="wfvlanitem[0].vlanterminalmac" class="easyui-validatebox"
                                           data-options="width: 150,validType:'mac[\'vlanterminalmac0\',\'MAC地址格式不正確\']'" value=""/>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>申請服務需求詳細說明&nbsp;<font color="red">*</font></td>
                                <td align="left" colspan="8"><textarea
                                        id="describtion0" name="wfvlanitem[0].describtion"
                                        oninput="return LessThanAuto(this,'txtNum');"
                                        onchange="return LessThanAuto(this,'txtNum');"
                                        onpropertychange="return LessThanAuto(this,'txtNum');"
                                        data-options="required:true" maxlength="100"
                                        class="easyui-validatebox" style="width:800px;height:40px;"
                                        rows="5" cols="6"></textarea><span id="txtNum"></span></td>
                            </tr>
                        </c:if>
                        </tbody>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList" id="batchSessionTableold">
                        <tr align="center">
                            <td colspan="10" class="td_style1">批量申請信息導入</td>
                        </tr>
                        <tr align="center">
                            <td width="10%"><a href="${ctx}/wfvlanprocess/downLoad/batchImportTpl"
                                               id="btnBatchImportTpl">批量申請模板下載</a></td>
                            <td colspan="8" class="td_style1">&nbsp;&nbsp;&nbsp;&nbsp;<a
                                    href="#" id="batchImport" class="easyui-linkbutton"
                                    data-options="iconCls:'icon-hamburg-cv'" style="width: 100px;"
                                    onclick="openBatchImportWin();">批量導入</a>
                            <td><a herf="#" onclick="applydelalltr();return false;"><font color="red"><span id="alldel">刪除全部</span></font></a>
                            </td>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td id="isbatchtd" colspan="10">
                    <c:if test="${wfvlanprocessitemEntity!=null&&wfvlanprocessitemEntity.size()>1}">
                    <div style="overflow-y: auto;height:550px" ;width="100%">
                        </c:if>
                        <c:if test="${wfvlanprocessitemEntity==null||wfvlanprocessitemEntity.size()==0}">
                        <div class="overdiv" width="100%" id="overflowdiv">
                            </c:if>
                            <table class="formList" id="batchSessionTable">
                                <tbody id="infobatch_Body">
                                <c:if test="${wfvlanprocessitemEntity!=null&&wfvlanprocessitemEntity.size()>1}">
                                    <c:forEach items="${wfvlanprocessitemEntity}" var="itemsEntity"
                                               varStatus="status">
                                        <tr align="center">
                                            <td width='5%' style="border-bottom:2px solid #138CDD;"
                                                rowspan="8">${status.index+1}</td>
                                            <td width='10%'>申請人工號&nbsp;<font color="red">*</font></td>
                                            <td width='10%' align="left"><input type="hidden"
                                                                                id="shunxu${status.index+1}"
                                                                                name="wfvlanitem[${status.index+1}].shunxu"
                                                                                readonly
                                                                                data-options="width:10"
                                                                                value="${itemsEntity.shunxu}"/><input
                                                    id="applyno${status.index+1}"
                                                    name="wfvlanitem[${status.index+1}].applyno" readonly
                                                    class="easyui-validatebox inputCss"
                                                    data-options="width:80"
                                                    value="${itemsEntity.applyno}"/></td>
                                            <td width='10%'>申請人</td>
                                            <td width='10%' align="left"><input
                                                    id="applyname${status.index+1}"
                                                    name="wfvlanitem[${status.index+1}].applyname" readonly
                                                    class="easyui-validatebox inputCss"
                                                    data-options="width: 150" value="${itemsEntity.applyname}"/></td>
                                            <td width='10%'>單位代碼</td>
                                            <td width='25%' align="left" colspan="2"><input
                                                    id="applydeptno${status.index+1}"
                                                    name="wfvlanitem[${status.index+1}].applydeptno" readonly
                                                    class="easyui-validatebox inputCss"
                                                    data-options="width: 150" value="${itemsEntity.applydeptno}"/></td>
                                            <td width='10%'>費用代碼&nbsp;<font color="red">*</font></td>
                                            <td width='10%' align="left"><input
                                                    id="applycostno${status.index+1}" readonly
                                                    name="wfvlanitem[${status.index+1}].applycostno"
                                                    class="easyui-validatebox inputCss"
                                                    data-options="width: 150" value="${itemsEntity.applycostno}"/></td>
                                        </tr>
                                        <tr align="center">
                                            <td>資位</td>
                                            <td align="left"><input
                                                    id="applyleveltype${status.index+1}"
                                                    name="wfvlanitem[${status.index+1}].applyleveltype" readonly
                                                    class="easyui-validatebox inputCss"
                                                    data-options="width: 150"
                                                    value="${itemsEntity.applyleveltype}"/></td>
                                            <td>管理職</td>
                                            <td align="left"><input
                                                    id="applymanager${status.index+1}"
                                                    name="wfvlanitem[${status.index+1}].applymanager" readonly
                                                    class="easyui-validatebox inputCss"
                                                    data-options="width: 150"
                                                    value="${itemsEntity.applymanager}"/></td>
                                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                                            <td align="left" colspan="2"><input
                                                    id="applyemail${status.index+1}" readonly
                                                    name="wfvlanitem[${status.index+1}].applyemail"
                                                    class="easyui-validatebox inputCss" data-options="width:250"
                                                    value="${itemsEntity.applyemail}"/></td>
                                            <td>聯繫分機<font color="red">*</font></td>
                                            <td align="left"><input
                                                    id="applyphone${status.index+1}" readonly
                                                    name="wfvlanitem[${status.index+1}].applyphone"
                                                    class="easyui-validatebox inputCss" data-options="width:100"
                                                    value="${itemsEntity.applyphone}"/></td>
                                        </tr>
                                        <tr align="center">
                                            <td>單位&nbsp;<font color="red">*</font></td>
                                            <td align="left" colspan="3"><input
                                                    id="applydeptname${status.index+1}"
                                                    name="wfvlanitem[${status.index+1}].applydeptname"
                                                    class="easyui-validatebox inputCss" style="width:380px"
                                                    data-options="width:380" readonly
                                                    value="${itemsEntity.applydeptname}"/></td>
                                            <td>手機號碼&nbsp;<font color="red">*</font></td>
                                            <td align="left" colspan="2"><input
                                                    id="vlanphone${status.index+1}" readonly
                                                    name="wfvlanitem[${status.index+1}].vlanphone"
                                                    class="easyui-validatebox inputCss" data-options="width: 150"
                                                    value="${itemsEntity.vlanphone}"/></td>
                                            <td>所在廠區&nbsp;<font color="red">*</font></td>
                                            <td align="left"><input disabled
                                                                    id="applyfactoryid${status.index+1}"
                                                                    name="wfvlanitem[${status.index+1}].applyfactoryid"
                                                                    class="easyui-combobox" data-options="width: 100"
                                                                    value="${itemsEntity.applyfactoryid}"/></td>
                                        </tr>
                                        <tr align="center">
                                            <td>法人代碼</td>
                                            <td class="td_style1">
                                                <input id="applycompanycode${status.index+1}" name="wfvlanitem[${status.index+1}].applycompanycode"
                                                       class="easyui-validatebox inputCss"
                                                       data-options="width:80" readonly value="${itemsEntity.applycompanycode}"/>
                                            </td>
                                            <td>法人名稱</td>
                                            <td class="td_style1" colspan="7">
                                                <input id="applycompanyname${status.index+1}" name="wfvlanitem[${status.index+1}].applycompanyname"
                                                       class="easyui-validatebox inputCss"
                                                       data-options="width:300" readonly value="${itemsEntity.applycompanyname}"/>
                                            </td>
                                        </tr>
                                        <tr align="center">
                                            <td>用戶名&nbsp;<font color="red">*</font></td>
                                            <td align="left"><input
                                                    id="vlanname${status.index+1}" readonly
                                                    name="wfvlanitem[${status.index+1}].vlanname"
                                                    class="easyui-validatebox inputCss" data-options="width: 150"
                                                    value="${itemsEntity.vlanname}"/></td>
                                            <td>使用期限&nbsp;<font color="red">*</font></td>
                                            <td align="left" colspan="6">自<input
                                                    id="vlanstarttime${status.index+1}" readonly
                                                    name="wfvlanitem[${status.index+1}].vlanstarttime"
                                                    class="Wdate" data-options="width:100"
                                                    style="width:100px"
                                                    value="<fmt:formatDate  pattern="yyyy-MM-dd"
								    value="${itemsEntity.vlanstarttime}"/>"/>
                                                至<input id="vlanendtime${status.index+1}" readonly
                                                        name="wfvlanitem[${status.index+1}].vlanendtime" class="Wdate"
                                                        data-options="width:100" style="width:100px"
                                                        value="<fmt:formatDate  pattern="yyyy-MM-dd"
								    value="${itemsEntity.vlanendtime}"/>"
                                                        onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'vlanstarttime${status.index}\')}',maxDate:'#F{$dp.$D(\'vlanstarttime0\',{M:12})}'})"/>（最長不超過12個月）
                                            </td>

                                        </tr>
                                        <tr align="center">
                                            <td>服務類型&nbsp;<font color="red">*</font></td>
                                            <td align="left" colspan="8"><input
                                                    id="vlanapplytype${status.index+1}"
                                                    name="wfvlanitem[${status.index+1}].vlanapplytype" readonly
                                                    class="easyui-validatebox inputCss" style="width:150px;"
                                                    value="${itemsEntity.vlanapplytype}"/></td>
                                            </td>
                                        </tr>
                                        <tr align="center">
                                            <td>用戶類型&nbsp;<font color="red">*</font></td>
                                            <td align="left" colspan="8"><input
                                                    id="vlanusertype${status.index+1}"
                                                    name="wfvlanitem[${status.index+1}].vlanusertype" readonly
                                                    class="easyui-validatebox inputCss" style="width:150px;"
                                                    value="${itemsEntity.vlanusertype}"/></td>
                                        </tr>
                                        <tr align="center">
                                            <td>終端類型&nbsp;<font color="red">*</font></td>
                                            <td class="td_style1"><input disabled
                                                                         id="vlanterminaltype${status.index+1}"
                                                                         name="wfvlanitem[${status.index+1}].vlanterminaltype"
                                                                         class="easyui-combobox"
                                                                         data-options="panelHeight:'auto',
									valueField:'value',
									textField:'label',
									editable:false,onBeforeLoad:function(){
									loadvlanterminaltype(${status.index+1});}"
                                                                         style="width:100px;"
                                                                         value="${itemsEntity.vlanterminaltype}"/>
                                            </td>
                                            <td>SSID&nbsp;<font color="red">*</font></td>
                                            <td align="left" colspan="2"><input disabled
                                                                                id="ssid${status.index+1}"
                                                                                name="wfvlanitem[${status.index+1}].ssid"
                                                                                class="easyui-combobox"
                                                                                data-options="panelHeight:'auto',
									valueField:'value',
									textField:'label',
									editable:false,onBeforeLoad:function(){
									loadssidtype(${status.index+1});}"
                                                                                style="width:80px;"
                                                                                value="${itemsEntity.ssid}"/> <input
                                                    id="ssidelse${status.index+1}"
                                                    name="wfvlanitem[${status.index+1}].ssidelse"
                                                    class="easyui-validatebox inputCss" readonly
                                                    style='width:50px;'
                                                    value="${itemsEntity.ssidelse}"/></td>
                                            <td>MAC地址&nbsp;<font color="red">*</font></td>
                                            <td align="left" colspan="3"><input
                                                    id="vlanterminalmac${status.index+1}" readonly
                                                    name="wfvlanitem[${status.index+1}].vlanterminalmac"
                                                    class="easyui-validatebox inputCss" data-options="width: 150"
                                                    value="${itemsEntity.vlanterminalmac}"/></td>
                                        </tr>
                                        <tr style="border-bottom:2px solid #138CDD;" align="center">
                                            <td>申請服務需求詳細說明&nbsp;<font color="red">*</font></td>
                                            <td align="left" colspan="8"><textarea
                                                    id="describtion${status.index+1}"
                                                    name="wfvlanitem[${status.index+1}].describtion"
                                                    oninput="return LessThanAuto(this,'txtNum');"
                                                    onchange="return LessThanAuto(this,'txtNum');"
                                                    onpropertychange="return LessThanAuto(this,'txtNum');"
                                                    maxlength="100" readonly
                                                    class="easyui-validatebox inputCss" style="width:800px;height:40px;"
                                                    rows="5" cols="6">${itemsEntity.describtion}</textarea><span
                                                    id="txtNum"></span></td>
                                        </tr>
                                    </c:forEach>
                                </c:if>
                                </tbody>
                            </table>
                            <c:if test="${wfvlanprocessitemEntity!=null&&wfvlanprocessitemEntity.size()>1}">
                        </div>
                        </c:if>
                        <c:if test="${wfvlanprocessitemEntity==null||wfvlanprocessitemEntity.size()==0}">
                    </div>
                    </c:if>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td align="center">備註</td>
                            <td colspan="9" align="left">
                                1.MAC地址欄位請錄入無線網卡的MAC地址，且無線網卡必須支持WPA2企業級/802.1X EAP
                                MAC認證與AES-CCMP數據加密方式；<br/>
                                <font color="red">2.無線區域網絡申請單需核准至產品處級/機能總處級/製造總處級權限主管；<br/></font>
                                3. SSID說明：<br/>
                                &nbsp;&nbsp;&nbsp;&nbsp;300301（公司用戶）：提供無線用戶訪問公司內網，訪問Internet必須通過集團代理<br/>
                                &nbsp;&nbsp;&nbsp;&nbsp;300302（公司用戶）：提供副總級及以上主管訪問公司內網、國內和國外网站以及漫遊服務<br/>
                                &nbsp;&nbsp;&nbsp;&nbsp;300303（公司用戶）：提供副總級以下主管訪問公司內網、國內和部分國外网站以及漫遊服務<br/>
                                &nbsp;&nbsp;&nbsp;&nbsp;300101（外來客戶）：提供訪客访问國內和部分國外网站服務<br/>
                                &nbsp;&nbsp;&nbsp;&nbsp;300102（外來客戶）：提供訪客訪國內外网站服務<br/>
                                &nbsp;&nbsp;&nbsp;&nbsp;400402（產線用戶）：只可訪問與生產，測試或研發相關的服務器IP地址及端口<br/>
                                4.用戶類型說明：<br/>
                                &nbsp;&nbsp;&nbsp;&nbsp;wifi phone用戶：不能訪問數據網路<br/>
                                &nbsp;&nbsp;&nbsp;&nbsp;公司用戶：限製訪問公司資源<br/>
                                &nbsp;&nbsp;&nbsp;&nbsp;外來客戶：只能接入Internet<br/>
                                &nbsp;&nbsp;&nbsp;&nbsp;產線用戶：不能接入園區網絡<br/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">附件&nbsp;<font color="red">*</font></td>
                            <td width="90%" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
                                    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="wfvlanprocess.attachids" value="${wfvlanprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td align="center">附件說明</td>
                            <td colspan="9">附件為無線區域網絡申請的材料模板<a href="${ctx}/wfvlanprocess/downLoadbook">承諾書下載</a>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_wuxianwangluoshenqing_v3','無線網絡賬號申請','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="5" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl5Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno5_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'yl5Table','ylno5','ylname5',$('#dealchoosefactoryid').combobox('getValue'),'wfvlanprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno5" name="wfvlanprocess.ylno5"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno5']}"
                                                               readonly value="${wfvlanprocessEntity.ylno5 }"/> <c:if
                                                            test="${requiredMap['ylno5'].equals('true')}">
                                                        <font color="red">*</font>
                                                    </c:if> /<input id="ylname5" name="wfvlanprocess.ylname5"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['ylno5']}"
                                                                    value="${wfvlanprocessEntity.ylname5 }"/></td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').val(),'wfvlanprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno"
                                                               name="wfvlanprocess.cchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly value="${wfvlanprocessEntity.cchargeno }"/>
                                                        <c:if
                                                                test="${requiredMap['cchargeno'].equals('true')}">
                                                            <font color="red">*</font>
                                                        </c:if> /<input id="cchargename"
                                                                        name="wfvlanprocess.cchargename" readonly
                                                                        class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                        value="${wfvlanprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="czchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['czchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('czchargeTable',$('#dealdeptno').val(),'czchargeno','czchargename',$('#dealfactoryid').val(),'wfvlanprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="czchargeno"
                                                               name="wfvlanprocess.czchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['czchargeno']}"
                                                               readonly value="${wfvlanprocessEntity.czchargeno }"/>
                                                        <c:if
                                                                test="${requiredMap['czchargeno'].equals('true')}">
                                                            <font color="red">*</font>
                                                        </c:if> /<input id="czchargename"
                                                                        name="wfvlanprocess.czchargename" readonly
                                                                        class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['czchargeno']}"
                                                                        value="${wfvlanprocessEntity.czchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole33('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealfactoryid').val(),'wfvlanprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno"
                                                               name="wfvlanprocess.zchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly value="${wfvlanprocessEntity.zchargeno }"/>
                                                        <c:if
                                                                test="${requiredMap['zchargeno'].equals('true')}">
                                                            <font color="red">*</font>
                                                        </c:if> /<input id="zchargename"
                                                                        name="wfvlanprocess.zchargename" readonly
                                                                        class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                        value="${wfvlanprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="20%" style="float: left;margin-left: 5px;"
                                                   id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealfactoryid').val(),'wfvlanprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno"
                                                               name="wfvlanprocess.zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly value="${wfvlanprocessEntity.zcchargeno }"/>
                                                        <c:if
                                                                test="${requiredMap['zcchargeno'].equals('true')}">
                                                            <font color="red">*</font>
                                                        </c:if> /<input id="zcchargename"
                                                                        name="wfvlanprocess.zcchargename" readonly
                                                                        class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                        value="${wfvlanprocessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno2_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(62,'yl2Table','ylno2','ylname2',$('#dealchoosefactoryid').combobox('getValue'),'wfvlanprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="wfvlanprocess.ylno2"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}"
                                                               readonly value="${wfvlanprocessEntity.ylno2 }"/> <c:if
                                                            test="${requiredMap['ylno2'].equals('true')}">
                                                        <font color="red">*</font>
                                                    </c:if> /<input id="ylname2" name="wfvlanprocess.ylname2"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['ylno2']}"
                                                                    value="${wfvlanprocessEntity.ylname2 }"/></td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl6Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno6_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(25,'yl6Table','ylno6','ylname6',$('#dealchoosefactoryid').combobox('getValue'),'wfvlanprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno6" name="wfvlanprocess.ylno6"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno6']}"
                                                               readonly value="${wfvlanprocessEntity.ylno6 }"/> <c:if
                                                            test="${requiredMap['ylno6'].equals('true')}">
                                                        <font color="red">*</font>
                                                    </c:if> /<input id="ylname6" name="wfvlanprocess.ylname6"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['ylno6']}"
                                                                    value="${wfvlanprocessEntity.ylname6 }"/></td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl3Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno3_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(155,'yl3Table','ylno3','ylname3',$('#dealchoosefactoryid').combobox('getValue'),'wfvlanprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno3" name="wfvlanprocess.ylno3"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno3']}"
                                                               readonly value="${wfvlanprocessEntity.ylno3 }"/> <c:if
                                                            test="${requiredMap['ylno3'].equals('true')}">
                                                        <font color="red">*</font>
                                                    </c:if> /<input id="ylname3" name="wfvlanprocess.ylname3"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['ylno3']}"
                                                                    value="${wfvlanprocessEntity.ylname3 }"/></td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zacwchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zacwchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(193,'zacwchargeTable','zacwchargeno','zacwchargename',$('#dealchoosefactoryid').combobox('getValue'),'wfvlanprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zacwchargeno" name="wfvlanprocess.zacwchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zacwchargeno']}"
                                                               readonly
                                                               value="${wfvlanprocessEntity.zacwchargeno }"/>
                                                        <c:if test="${requiredMap['zacwchargeno'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        /<input id="zacwchargename" name="wfvlanprocess.zacwchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zacwchargeno']}"
                                                                value="${wfvlanprocessEntity.zacwchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <%--<table width="18%" style="float: left;margin-left: 5px;"
                                                   id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#dealdeptno').val(),'pcchargeno','pcchargename',$('#dealfactoryid').val(),'wfvlanprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno"
                                                               name="wfvlanprocess.pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly value="${wfvlanprocessEntity.pcchargeno }"/>
                                                        <c:if
                                                                test="${requiredMap['pcchargeno'].equals('true')}">
                                                            <font color="red">*</font>
                                                        </c:if> /<input id="pcchargename"
                                                                        name="wfvlanprocess.pcchargename" readonly
                                                                        class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                        value="${wfvlanprocessEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>--%>
                                            <table width="20%" style="float: left;margin-left: 5px;"
                                                   id="yl4Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno4_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(188,'yl4Table','ylno4','ylname4',$('#dealchoosefactoryid').combobox('getValue'),'wfvlanprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno4" name="wfvlanprocess.ylno4"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno4']}"
                                                               readonly value="${wfvlanprocessEntity.ylno4 }"/> <c:if
                                                            test="${requiredMap['ylno4'].equals('true')}">
                                                        <font color="red">*</font>
                                                    </c:if> /<input id="ylname4" name="wfvlanprocess.ylname4"
                                                                    readonly class="easyui-validatebox"
                                                                    data-options="width:80,required:${requiredMap['ylno4']}"
                                                                    value="${wfvlanprocessEntity.ylname4 }"/></td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="yl7Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno7_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(187,'yl7Table','ylno7','ylname7',$('#dealchoosefactoryid').combobox('getValue'),'wfvlanprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno7" name="wfvlanprocess.ylno7"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno7']}"
                                                               readonly value="${wfvlanprocessEntity.ylno7 }"/>
                                                        <c:if test="${requiredMap['ylno7'].equals('true')}"><font
                                                                color="red">*</font></c:if>
                                                        /<input id="ylname7" name="wfvlanprocess.ylname7"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno7']}"
                                                                value="${wfvlanprocessEntity.ylname7 }"/></td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="sychargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['sychargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('sychargeTable',$('#dealdeptno').val(),'sychargeno','sychargename',$('#dealfactoryid').val(),'wfvlanprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="sychargeno"
                                                               name="wfvlanprocess.sychargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['sychargeno']}"
                                                               readonly value="${wfvlanprocessEntity.sychargeno }"/>
                                                        <c:if
                                                                test="${requiredMap['sychargeno'].equals('true')}">
                                                            <font color="red">*</font>
                                                        </c:if> /<input id="sychargename"
                                                                        name="wfvlanprocess.sychargename" readonly
                                                                        class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['sychargeno']}"
                                                                        value="${wfvlanprocessEntity.sychargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10"><font color="red">溫馨提示：如果您在填單過程中有任何疑問，請聯繫本廠區資訊人員：</font><a
                                    href="${ctx}/wfvlanprocess/downLoadcontact">各廠區資訊聯繫方式</a></td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <input type="checkbox" id="agree" name="agree"/><a
                                    href="${ctx}/requisitionlist/downLoad/commitmentTpl" plain="true"
                                    id="btnCommitmentTpl">本人已閱讀并同意服務條款</a>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10"
                                style="border:none;text-align:center;margin-top:10px"><a
                                    href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                    data-options="iconCls:'icon-add'" style="width: 100px;"
                                    onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp; <a
                                    href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                    data-options="iconCls:'icon-ok'" style="width: 100px;"
                                    onclick="saveInfo(2);">提交</a></td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="loginname" name="loginname" value="${user.loginName}"/>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="areaId" name="areaId" value=""/>
    <input type="hidden" id="buildingId" name="buildingId" value=""/>
    <input type="hidden" id="onlyKchargeSignle" value="1"/>
    <div id="win"></div>
</form>
</div>
<script src='${ctx}/static/js/information/wfvlanprocess.min.js?random=2025052801'></script>
<div id="optionWin" class="easyui-window" title="無線區域網絡賬號申請" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%">
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="file" type="file" style="width: 300px"
                           accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>
                    <input id="isExcel2003" name="isExcel2003" type="hidden" value="">

                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr>
                <td align="left" style="width: 60%; white-space: nowrap;">
                    <span id="tishi">正在導入中，請稍後...</span>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <span id="labelListAddResult"></span><a href="${ctx}/wfvlanprocess/downLoad/errorExcel"
                                                            id="downloadError"
                                                            plain="true">查看錯誤信息</a>
                </td>
            </tr>
        </table>
    </form>
</div>
</body>
</html>
