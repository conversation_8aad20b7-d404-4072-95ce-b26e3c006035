<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>無線網絡賬號申請</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/wfvlanprocess/${action}" method="post">
    		<input id="ids" name="ids" type="hidden"
			value="${wfvlanprocessEntity.id }" /> <input id="serialno"
			name="wfvlanprocess.serialno" type="hidden"
			value="${wfvlanprocessEntity.serialno }" /> <input id="makerno"
			name="wfvlanprocess.makerno" type="hidden"
			value="${wfvlanprocessEntity.makerno }" /> <input id="makername"
			name="wfvlanprocess.makername" type="hidden"
			value="${wfvlanprocessEntity.makername }" /> <input id="makerdeptno"
			name="wfvlanprocess.makerdeptno" type="hidden"
			value="${wfvlanprocessEntity.makerdeptno }" /> <input
			id="makerfactoryid" name="wfvlanprocess.makerfactoryid" type="hidden"
			value="${wfvlanprocessEntity.makerfactoryid }"/>
	<div class="commonW">
		<div class="headTitle">無線區域網絡使用申請單</div>
		<div class="position_L">
			任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfvlanprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfvlanprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfvlanprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfvlanprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfvlanprocessEntity.makerno}/${wfvlanprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
           <tr>
                <td>
                    <table class="formList">
                    		<tr align="center">
								<td colspan="10" class="td_style1">承辦人基本信息</td>
							</tr>
							<tr align="center">
								<td width="10%">承辦人工號</td>
								<td align="left"><input id="dealno" readonly
									name="wfvlanprocess.dealno" class="easyui-validatebox inputCss"
									data-options="width:80"
									value="${wfvlanprocessEntity.dealno}" /></td>
								<td>承辦人</td>
								<td align="left"><input id="dealname"
									name="wfvlanprocess.dealname" readonly
									class="easyui-validatebox inputCss" data-options="width: 70"
									value="${wfvlanprocessEntity.dealname}" /></td>
								<td>單位代碼</td>
								<td align="left"><input id="dealdeptno"
									name="wfvlanprocess.dealdeptno" readonly
									class="easyui-validatebox inputCss" data-options="width:100"
									value="${wfvlanprocessEntity.dealdeptno}" /></td>
								<td>廠區</td>
								<td align="left"><input id="dealchoosefactoryid"
									name="wfvlanprocess.dealchoosefactoryid"
									class="easyui-combobox" disabled
									data-options="width:90"
									value="${wfvlanprocessEntity.dealchoosefactoryid}" /><input
									class="easyui-validatebox" id="dealfactoryid"
									name="wfvlanprocess.dealfactoryid" style="width:30px" readonly
									type="hidden" value="${wfvlanprocessEntity.dealfactoryid}" /></td>
								<td>聯繫電話</td>
								<td align="left"><input id="dealtel"
									name="wfvlanprocess.dealtel" class="easyui-validatebox inputCss"
									data-options="width:80" readonly
									value="${wfvlanprocessEntity.dealtel}" /></td>
							</tr>
							<tr align="center">
								<td>單位</td>
								<td align="left" colspan="3"><input id="dealdeptname"
									style="width:450px" name="wfvlanprocess.dealdeptname"
									class="easyui-validatebox inputCss" readonly
									data-options="width:450"
									value="${wfvlanprocessEntity.dealdeptname}" /></td>
								<td>聯繫郵箱</td>
								<td align="left" colspan="5"><input id="dealemail" readonly
									name="wfvlanprocess.dealemail" class="easyui-validatebox inputCss"
									data-options="width: 250,validType:'email[\'wfvlanprocess.dealemail\',\'郵箱的格式不正確\']'"
									value="${wfvlanprocessEntity.dealemail}" /></td>
							</tr>
							<tr align="center">
								<td>使用區域</td>
								<td colspan="5" align="left"><input id="applyarea" disabled
									name="wfvlanprocess.applyarea" class="easyui-combobox"
									data-options="width:150,onSelect:function(){onchangeArea();}"
									value="${wfvlanprocessEntity.applyarea }" panelHeight="auto" />&nbsp;/
									<input id="applybuilding" name="wfvlanprocess.applybuilding"
									class="easyui-combobox" data-options="width:150" disabled
									value="${wfvlanprocessEntity.applybuilding }"
									panelHeight="auto" /></td>
								<td>安保區域</td>
								<td colspan="3" class="td_style2">
									<div class="securityareaDiv"></div><input id="disOrEnabled" value="disabled" type="hidden"/><input id="securityarea"
									name="wfvlanprocess.securityarea" type="hidden"
									class="easyui-validatebox" data-options="width: 100"
									value="${wfvlanprocessEntity.securityarea }" />
								</td>
							</tr>
                    </table>
                </td>
           </tr>
            <tr>
                <td>
					<c:choose>
					<c:when test="${wfvlanprocessitemEntity!=null&&wfvlanprocessitemEntity.size()==1}">
					   <div style="overflow-y: auto;";width="100%">
					</c:when>
					<c:when test="${wfvlanprocessitemEntity!=null&&wfvlanprocessitemEntity.size()>1}">
					   <div style="overflow-y: auto;height:516px";width="100%">
					</c:when>
					<c:when test="${wfvlanprocessitemEntity==null||wfvlanprocessitemEntity.size()==0}">
					<div style="overflow-y: auto;height:34px";width="100%">
					</c:when>
					<c:otherwise>
						<div style="overflow-y: auto;";width="100%">
					</c:otherwise>
					</c:choose>
                     <table class="formList">
                     <tr align="center">
								<td colspan="10" class="td_style1">申請詳細信息</td>
							</tr>
                            <c:if test="${wfvlanprocessitemEntity!=null&&wfvlanprocessitemEntity.size()==1}">
									<c:forEach items="${wfvlanprocessitemEntity}" var="itemsEntity"
										varStatus="status">
										<tr align="center">
											<td width='10%'>申請人工號</td>
											<td width='10%' align="left">${itemsEntity.applyno}</td>
											<td width='10%'>申請人</td>
											<td width='10%' align="left">${itemsEntity.applyname}</td>
											<td width='10%'>單位代碼</td>
											<td width='25%' colspan='2' align="left">${itemsEntity.applydeptno}</td>
											<td width='10%'>費用代碼</td>
											<td width='15%' align="left" colspan="2">${itemsEntity.applycostno}</td>
										</tr>
										<tr align="center">
											<td>資位</td>
											<td align="left">${itemsEntity.applyleveltype}</td>
											<td>管理職</td>
											<td align="left">${itemsEntity.applymanager}</td>
											<td>聯繫郵箱</td>
											<td align="left" colspan="2">${itemsEntity.applyemail}</td>
											<td>聯繫分機</td>
											<td align="left" colspan="2">${itemsEntity.applyphone}</td>
										</tr>
										<tr align="center">
											<td>單位</td>
											<td align="left" colspan="3">${itemsEntity.applydeptname}</td>
											<td>手機號碼</td>
											<td align="left" colspan="2">${itemsEntity.vlanphone}</td>
											<td>所在廠區</td>
											<td align="left" colspan="2"><input id="applyfactoryid0" disabled class="easyui-combobox" data-options="width: 100"  value="${itemsEntity.applyfactoryid}"/></td>
										</tr>
										<tr align="center">
											<td>法人代碼</td>
											<td align="left">${itemsEntity.applycompanycode}
											</td>
											<td>法人名稱</td>
											<td align="left" colspan="7">${itemsEntity.applycompanyname}</td>
										</tr>
										<tr align="center">
											<td>用戶名</td>
											<td align="left">${itemsEntity.vlanname}</td>
											<td>使用期限</td>
											<td align="left" colspan="7">自<fmt:formatDate  pattern="yyyy-MM-dd"
								    value="${itemsEntity.vlanstarttime}"/>
												至<fmt:formatDate  pattern="yyyy-MM-dd"
								    value="${itemsEntity.vlanendtime}"/>
											</td>
										</tr>
										<tr align="center">
											<td>服務類型</td>
											<td align="left" colspan="4">${itemsEntity.vlanapplytype}</td>
											</td>					
											<td>用戶類型</td>
											<td align="left" colspan="4">
												<c:choose>
													<c:when test="${wfvlanprocessEntity.applytype=='0'}">
														${itemsEntity.vlanusertypename}
													</c:when>
													<c:when test="${wfvlanprocessEntity.applytype==1}">
														${itemsEntity.vlanusertype}
													</c:when>
												</c:choose>
											</td>
										</tr>
										<tr align="center">
											<td>終端類型</td>
											<td align="left">
												<c:choose>
													<c:when test="${wfvlanprocessEntity.applytype=='0'}">
														${itemsEntity.vlanterminaltypename}
													</c:when>
													<c:when test="${wfvlanprocessEntity.applytype==1}">
														${itemsEntity.vlanterminaltype}
													</c:when>
												</c:choose>
											</td>
											<td>SSID</td>
											<td align="left" colspan="2">${itemsEntity.ssid}<c:if test="${itemsEntity.ssidelse!=null&&itemsEntity.ssidelse!=''}">/${itemsEntity.ssidelse}</c:if></td>
											<td>MAC地址</td>
											<td align="left" colspan="4">${itemsEntity.vlanterminalmac}</td>
										</tr>
										<tr style="height:auto;" align="center">
											<td>申請服務需求詳細說明</td>
											<td align="left" colspan="9"><textarea
													readonly
													class="easyui-validatebox inputCss" style="width:900px;height:40px;"
													rows="5" cols="6">${itemsEntity.describtion}</textarea>
											</td>
										</tr>
									</c:forEach>
								</c:if>
						 <c:if test="${wfvlanprocessitemEntity!=null&&wfvlanprocessitemEntity.size()>1}">
							 <c:forEach items="${wfvlanprocessitemEntity}" var="itemsEntity" varStatus="status">
								 <tr align="center">
									 <td rowspan="7" width='5%'style="border-bottom:2px solid #138CDD;">${status.index+1}</td>
									 <td width='10%'>申請人工號</td>
									 <td width='10%' align="left">${itemsEntity.applyno}</td>
									 <td width='10%'>申請人</td>
									 <td width='10%' align="left">${itemsEntity.applyname}</td>
									 <td width='10%'>單位代碼</td>
									 <td width='25%' colspan='2' align="left">${itemsEntity.applydeptno}</td>
									 <td width='10%'>費用代碼</td>
									 <td width='10%' align="left">${itemsEntity.applycostno}</td>
								 </tr>
								 <tr align="center">
									 <td>資位</td>
									 <td align="left">${itemsEntity.applyleveltype}</td>
									 <td>管理職</td>
									 <td align="left">${itemsEntity.applymanager}</td>
									 <td>聯繫郵箱</td>
									 <td align="left" colspan="2">${itemsEntity.applyemail}</td>
									 <td>聯繫分機</td>
									 <td align="left">${itemsEntity.applyphone}</td>
								 </tr>
								 <tr align="center">
									 <td>單位</td>
									 <td align="left" colspan="3">${itemsEntity.applydeptname}</td>
									 <td>手機號碼</td>
									 <td align="left" colspan="2">${itemsEntity.vlanphone}</td>
									 <td>所在廠區</td>
									 <td align="left">${itemsEntity.applyfactoryid}</td>
								 </tr>
								 <tr align="center">
									 <td>法人代碼</td>
									 <td align="left">${itemsEntity.applycompanycode}
									 </td>
									 <td>法人名稱</td>
									 <td align="left" colspan="7">${itemsEntity.applycompanyname}</td>
								 </tr>
								 <tr align="center">
									 <td>用戶名</td>
									 <td align="left">${itemsEntity.vlanname}</td>
									 <td>使用期限</td>
									 <td align="left" colspan="6">自<fmt:formatDate  pattern="yyyy-MM-dd"
								    value="${itemsEntity.vlanstarttime}"/>
										 至<fmt:formatDate  pattern="yyyy-MM-dd"
								    value="${itemsEntity.vlanendtime}"/>
									 </td>
								 </tr>
								 <tr align="center">
									 <td>服務類型</td>
									 <td align="left" colspan="3">${itemsEntity.vlanapplytype}</td>
									 <td>用戶類型</td>
									 <td align="left" colspan="4">
										 <c:choose>
											 <c:when test="${wfvlanprocessEntity.applytype=='0'}">
												 ${itemsEntity.vlanusertypename}
											 </c:when>
											 <c:when test="${wfvlanprocessEntity.applytype==1}">
												 ${itemsEntity.vlanusertype}
											 </c:when>
										 </c:choose>
									 </td>
								 </tr>
								 <tr align="center">
									 <td>終端類型</td>
									 <td align="left">
										 <c:choose>
											 <c:when test="${wfvlanprocessEntity.applytype=='0'}">
												 ${itemsEntity.vlanterminaltypename}
											 </c:when>
											 <c:when test="${wfvlanprocessEntity.applytype==1}">
												 ${itemsEntity.vlanterminaltype}
											 </c:when>
										 </c:choose>
									 </td>
									 <td>SSID</td>
									 <td align="left" colspan="2">${itemsEntity.ssid}<c:if test="${itemsEntity.ssidelse!=null&&itemsEntity.ssidelse!=''}">/${itemsEntity.ssidelse}</c:if></td>
									 <td>MAC地址</td>
									 <td align="left" colspan="3">${itemsEntity.vlanterminalmac}</td>
								 </tr>
								 <tr style="border-bottom:2px solid #138CDD;" align="center">
									 <td>申請服務需求詳細說明</td>
									 <td align="left" colspan="8"><textarea
											 readonly
											 class="easyui-validatebox inputCss" style="width:900px;height:40px;"
											 rows="5" cols="6">${itemsEntity.describtion}</textarea></td>
								 </tr>
							 </c:forEach>
						 </c:if>
                     </table>
					<c:choose>
					<c:when test="${wfvlanprocessitemEntity!=null&&wfvlanprocessitemEntity.size()==1}">
					</div>
					</c:when>
					<c:when test="${wfvlanprocessitemEntity!=null&&wfvlanprocessitemEntity.size()>1}">
					</div>
					</c:when>
					<c:when test="${wfvlanprocessitemEntity==null||wfvlanprocessitemEntity.size()==0}">
					</div>
					</c:when>
					<c:otherwise>
					</div>
					</c:otherwise>
					</c:choose>
                </td>
            </tr>
            <tr>
                <td>
                     <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" align="left">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${wfvlanprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%">
						<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wfvlanprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','無線網絡賬號申請');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfvlanprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                     </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<div id="dlg"></div>
<script src='${ctx}/static/js/information/wfvlanprocess.min.js?random=2025052801'></script>
</body>
</html>