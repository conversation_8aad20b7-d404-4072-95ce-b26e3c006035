<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>文件服務器帳號申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wffsaccountprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wffsaccountprocessEntity.id }"/>
    <input id="serialno" name="wfsaccountprocess.serialno" type="hidden" value="${wffsaccountprocessEntity.serialno }"/>
    <input id="makerno" name=wfsaccountprocess."makerno" type="hidden" value="${wffsaccountprocessEntity.makerno }"/>
    <input id="makername" name="wfsaccountprocess.makername" type="hidden" value="${wffsaccountprocessEntity.makername }"/>
    <input id="makerdeptno" name="wfsaccountprocess.makerdeptno" type="hidden" value="${wffsaccountprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfsaccountprocess.makerfactoryid" type="hidden" value="${wffsaccountprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">文件服務器帳號申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wffsaccountprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wffsaccountprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wffsaccountprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wffsaccountprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wffsaccountprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wffsaccountprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wffsaccountprocessEntity.makerno}/${wffsaccountprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="dealno" name="wfsaccountprocess.dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wffsaccountprocessEntity.dealno}"
                                       onchange="queryUserInfo();"/>
                            </td>
                            <td width="5%">承辦人</td>
                            <td width="12%" class="td_style1">
                                <input id="dealname" name="wfsaccountprocess.dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wffsaccountprocessEntity.dealname }"/>
                            </td>
                            <td width="6%">單位代碼</td>
                            <td width="12%" class="td_style1">
                                <input id="dealdeptno" name="wfsaccountprocess.dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wffsaccountprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="6%">費用代碼</td>
                            <td width="12%" class="td_style1">
                                <input id="applycostno" name="wfsaccountprocess.applycostno"
                                       class="easyui-validatebox inputCss" style="width:90px;"
                                       readonly value="${wffsaccountprocessEntity.applycostno }"/>
                            </td>
                            <td width="8%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="21%" class="td_style1">
                                <input id="dealchoosefactoryid" name="wfsaccountprocess.dealchoosefactoryid"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${wffsaccountprocessEntity.dealchoosefactoryid }"
                                       data-options="width: 120,required:true,onSelect:function(){onchangeFactory('dealchoosefactoryid');}"/>
                                <input id="dealfactoryid" name="wfsaccountprocess.dealfactoryid" type="hidden"
                                       value="${wffsaccountprocessEntity.dealfactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="wfsaccountprocess.applyleveltype"
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wffsaccountprocessEntity.applyleveltype }" readonly/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="wfsaccountprocess.applymanager"
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wffsaccountprocessEntity.applymanager }" readonly/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="wfsaccountprocess.dealemail" class="easyui-validatebox"
                                       value="${wffsaccountprocessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td>使用區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyarea" name="wfsaccountprocess.applyarea" class="easyui-combobox"
                                       data-options="width: 70,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wffsaccountprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="wfsaccountprocess.applybuilding"
                                       class="easyui-combobox"
                                       data-options="width: 70,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wffsaccountprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealtel" name="wfsaccountprocess.dealtel" class="easyui-validatebox"
                                       style="width:90px;position:relative;"
                                       value="${wffsaccountprocessEntity.dealtel}"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="7" class="td_style1">
                                <input id="dealdeptname" name="wfsaccountprocess.dealdeptname"
                                       class="easyui-validatebox" data-options="width: 410,required:true"
                                       value="${wffsaccountprocessEntity.dealdeptname }"/>
                            </td>
                        </tr>
                    </table>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人明細</td>
                        </tr>
                        <tr>
                            <td colspan="10" align="left"><div style="float: left">是否跨部門申請：</div>
                                <div style="float: left" class="ismydeptDiv"></div>
                                <input id="ismydept" name="wfsaccountprocess.ismydept" type="hidden"
                                       value="${wffsaccountprocessEntity.ismydept}"/></td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <input id="wfsaccountItemTableIndex" type="hidden"
                                           value="<c:if test="${wffsaccountitems!=null && wffsaccountitems.size()>0}">${wffsaccountitems.size() +1}</c:if>
                                        <c:if test="${wffsaccountitems.size()==0 || wffsaccountitems==null}">2</c:if>"/>
                                    </input>
                                    <table id="wfsaccountItemTable" width="100%" style="border-collapse: collapse">
                                        <tr align="center">
                                            <td><input class="inputCss" style="width:26px;" value="序號"/></td>
                                            <td>申請人工號&nbsp;<font color="red">*</font></td>
                                            <td>申請人</td>
                                            <td>工作職能&nbsp;<font color="red">*</font></td>
                                            <td>區域&nbsp;<font color="red">*</font></td>
                                            <td>單位代碼&nbsp;<font color="red">*</font></td>
                                            <td>單位&nbsp;<font color="red">*</font></td>
                                            <td>電腦名稱&nbsp;<font color="red">*</font></td>
                                            <td>IP地址&nbsp;<font color="red">*</font></td>
                                            <td>FTP服務器IP&nbsp;<font color="red">*</font></td>
                                            <td>需求類型&nbsp;<font color="red">*</font></td>
                                            <td>申請類型&nbsp;<font color="red">*</font></td>
                                            <td>安保區域&nbsp;<font color="red">*</font></td>
                                            <td>第一層目錄</td>
                                            <td>第二層目錄</td>
                                            <td>第三層目錄</td>
                                            <td>列印機IP&nbsp;<font color="red">*</font></td>
                                            <td>需求空間</td>
                                            <td>權限</td>
                                            <td><input class="inputCss" style="width:25px;" value="操作"/></td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wffsaccountitems!=null&&wffsaccountitems.size()>0}">
                                            <c:forEach items="${wffsaccountitems}" var="wffsaccountitems"
                                                       varStatus="status">
                                                <tr align="center" id="wffsaccountitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <input id="applyno${status.index+1}"
                                                               onblur="getUserNameByEmpno2(this,status.index+1);"
                                                               name="wffsaccountitems[${status.index+1}].applyno"
                                                               class="easyui-validatebox" style="width:60px;"
                                                               data-options="required:true"
                                                               value="${wffsaccountitems.applyno}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyname${status.index+1}"
                                                               name="wffsaccountitems[${status.index+1}].applyname"
                                                               class="easyui-validatebox inputCss" style="width:60px;"
                                                               readonly value="${wffsaccountitems.applyname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="jobtype${status.index+1}"
                                                               name="wffsaccountitems[${status.index+1}].jobtype"
                                                               style="width:70px;" class="easyui-validatebox"
                                                               data-options="required:true"
                                                               value="${wffsaccountitems.jobtype}"/>
                                                    </td>
                                                    <td>
                                                        <input id="usearea${status.index+1}"
                                                               name="wffsaccountitems[${status.index+1}].usearea"
                                                               style="width:70px;" class="easyui-validatebox"
                                                               data-options="required:true"
                                                               value="${wffsaccountitems.usearea}"/>
                                                    </td>
                                                    <td>
                                                        <input id="deptno${status.index+1}"
                                                               name="wffsaccountitems[${status.index+1}].deptno"
                                                               class="easyui-validatebox inputCss" style="width:80px;"
                                                               readonly value="${wffsaccountitems.deptno}"/>
                                                    </td>
                                                    <td>
                                                        <input id="deptname${status.index+1}"
                                                               name="wffsaccountitems[${status.index+1}].deptname"
                                                               style="width:380px;" class="easyui-validatebox"
                                                               data-options="required:true"
                                                               value="${wffsaccountitems.deptname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="pcname${status.index+1}"
                                                               name="wffsaccountitems[${status.index+1}].pcname"
                                                               style="width:90px;" class="easyui-validatebox"
                                                               data-options="required:true"
                                                               value="${wffsaccountitems.pcname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="pcip${status.index+1}"
                                                               name="wffsaccountitems[${status.index+1}].pcip"
                                                               style="width:90px;" class="easyui-validatebox"
                                                               data-options="required:true,validType:'ip[\'pcip${status.index+1}\']'"
                                                               value="${wffsaccountitems.pcip}"/>
                                                    </td>
                                                    <td>
                                                        <input id="ftpip${status.index+1}"
                                                               name="wffsaccountitems[${status.index+1}].ftpip"
                                                               style="width:90px;" class="easyui-validatebox"
                                                               data-options="required:true,validType:'ip[\'ftpip${status.index+1}\']'"
                                                               value="${wffsaccountitems.ftpip}"/>
                                                    </td>
                                                    <td><input id="reqtype${status.index+1}"
                                                               name="wffsaccountitems[${status.index+1}].reqtype"
                                                               class="easyui-combobox"
                                                               data-options="width:110,required:true,validType:'comboxValidate[\'reqtype${status.index+1}\',\'请選擇需求類型\']',onSelect:function(){onchangeReqTypeOrApplyType(${status.index+1});},
                                                                              panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadReqtype(${status.index+1});}"
                                                               style="width:110px;" value="${wffsaccountitems.reqtype}"/>
                                                    </td>
                                                    <td><input id="applytype${status.index+1}"
                                                               name="wffsaccountitems[${status.index+1}].applytype"
                                                               class="easyui-combobox"
                                                               data-options="width:80,required:true,validType:'comboxValidate[\'applytype${status.index+1}\',\'请選擇申請類型\']',onSelect:function(){onchangeReqTypeOrApplyType(${status.index+1});},
                                                                              panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadApplyTypeFSaccount(${status.index+1});}"
                                                               style="width:80px;"
                                                               value="${wffsaccountitems.applytype}"/></td>
                                                    <td><input id="safearea${status.index+1}"
                                                               name="wffsaccountitems[${status.index+1}].safearea"
                                                               class="easyui-combobox"
                                                               data-options="width:80,required:true,validType:'comboxValidate[\'safearea${status.index+1}\',\'请選擇安保區域\']',
                                                                              panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadSecurityArea(${status.index+1});}"
                                                               style="width:80px;"
                                                               value="${wffsaccountitems.safearea}"/></td>
                                                    <td>
                                                        <input id="firstdir${status.index+1}"
                                                               name="wffsaccountitems[${status.index+1}].firstdir"
                                                               class="easyui-validatebox inputCss" readonly style="width:80px;"
                                                               readonly value="${wffsaccountitems.firstdir}"/>
                                                    </td>
                                                    <td>
                                                        <input id="seconddir${status.index+1}"
                                                               name="wffsaccountitems[${status.index+1}].seconddir"
                                                               class="easyui-validatebox inputCss" readonly style="width:80px;"
                                                               readonly value="${wffsaccountitems.seconddir}"/>
                                                    </td>
                                                    <td>
                                                        <input id="thirddir${status.index+1}"
                                                               name="wffsaccountitems[${status.index+1}].thirddir"
                                                               class="easyui-validatebox inputCss" readonly style="width:80px;"
                                                               readonly value="${wffsaccountitems.thirddir}"/>
                                                    </td>
                                                    <td>
                                                        <input id="printip${status.index+1}"
                                                               name="wffsaccountitems[${status.index+1}].printip"
                                                               class="easyui-validatebox inputCss" readonly style="width:90px;"
                                                               data-options="validType:'ip[\'printip${status.index+1}\']'"
                                                               readonly value="${wffsaccountitems.printip}"/>
                                                    </td>
                                                    <td><input id="reqspace${status.index+1}" disabled
                                                               name="wffsaccountitems[${status.index+1}].reqspace"
                                                               class="easyui-combobox"
                                                               data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadReqspace(${status.index+1});}"
                                                               style="width:60px;"
                                                               value="${wffsaccountitems.reqspace}"/></td>
                                                    <td><input id="permissions${status.index+1}" disabled
                                                               name="wffsaccountitems[${status.index+1}].permissions"
                                                               class="easyui-combobox"
                                                               data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadPermissions(${status.index+1});}"
                                                               style="width:60px;"
                                                               value="${wffsaccountitems.permissions}"/></td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="deltr(${status.index+1});return false;"/>
                                                        <input id="shunxu${status.index+1}" type="hidden"
                                                               name="wffsaccountitems[${status.index+1}].shunxu"
                                                               value="${status.index+1}"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${wffsaccountitems.size()==0 || wffsaccountitems==null}">
                                            <tr align="center" id="wffsaccountitems1">
                                                <td>1</td>
                                                <td>
                                                    <input id="applyno1"
                                                           onblur="getUserNameByEmpno2(this,1);"
                                                           name="wffsaccountitems[1].applyno" class="easyui-validatebox"
                                                           style="width:60px;"
                                                           data-options="required:true"
                                                           value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyname1" name="wffsaccountitems[1].applyname"
                                                           class="easyui-validatebox inputCss" style="width:60px;"
                                                           readonly value=""/>
                                                </td>
                                                <td>
                                                    <input id="jobtype1" name="wffsaccountitems[1].jobtype"
                                                           style="width:70px;" class="easyui-validatebox"
                                                           data-options="required:true"
                                                           value=""/>
                                                </td>
                                                <td>
                                                    <input id="usearea1" name="wffsaccountitems[1].usearea"
                                                           style="width:70px;" class="easyui-validatebox"
                                                           data-options="required:true"
                                                           value=""/>
                                                </td>
                                                <td>
                                                    <input id="deptno1" name="wffsaccountitems[1].deptno"
                                                           class="easyui-validatebox inputCss" style="width:80px;"
                                                           readonly value=""/>
                                                </td>
                                                <td>
                                                    <input id="deptname1" name="wffsaccountitems[1].deptname"
                                                           style="width:380px;" class="easyui-validatebox"
                                                           data-options="required:true"
                                                           value=""/>
                                                </td>
                                                <td>
                                                    <input id="pcname1" name="wffsaccountitems[1].pcname"
                                                           style="width:90px;" class="easyui-validatebox"
                                                           data-options="required:true"
                                                           value=""/>
                                                </td>
                                                <td>
                                                    <input id="pcip1" name="wffsaccountitems[1].pcip"
                                                           style="width:90px;" class="easyui-validatebox"
                                                           data-options="required:true,validType:'ip[\'pcip1\']'"
                                                           value=""/>
                                                </td>
                                                <td>
                                                    <input id="ftpip1" name="wffsaccountitems[1].ftpip"
                                                           style="width:90px;" class="easyui-validatebox"
                                                           data-options="required:true,validType:'ip[\'ftpip1\']'"
                                                           value=""/>
                                                </td>
                                                <td><input id="reqtype1"
                                                           name="wffsaccountitems[1].reqtype"
                                                           class="easyui-combobox"
                                                           data-options="width:110,required:true,validType:'comboxValidate[\'reqtype1\',\'请選擇需求類型\']',onSelect:function(){onchangeReqTypeOrApplyType(1);},
                                                                              panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadReqtype(1);}"
                                                           style="width:110px;" value=""/>
                                                </td>
                                                <td><input id="applytype1"
                                                           name="wffsaccountitems[1].applytype"
                                                           class="easyui-combobox"
                                                           data-options="width:80,required:true,validType:'comboxValidate[\'applytype1\',\'请選擇申請類型\']',onSelect:function(){onchangeReqTypeOrApplyType(1);},
                                                                              panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadApplyTypeFSaccount(1);}"
                                                           style="width:80px;" value=""/>
                                                </td>
                                                <td><input id="safearea1"
                                                           name="wffsaccountitems[1].safearea"
                                                           class="easyui-combobox"
                                                           data-options="width:80,required:true,validType:'comboxValidate[\'safearea1\',\'请選擇安保區域\']',
                                                                              panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadSecurityArea(1);}"
                                                           style="width:70px;" value=""/>
                                                </td>
                                                <td>
                                                    <input id="firstdir1" name="wffsaccountitems[1].firstdir"
                                                           class="easyui-validatebox inputCss" readonly style="width:80px;"
                                                           readonly value=""/>
                                                </td>
                                                <td>
                                                    <input id="seconddir1" name="wffsaccountitems[1].seconddir"
                                                           class="easyui-validatebox inputCss" readonly style="width:80px;"
                                                           readonly value=""/>
                                                </td>
                                                <td>
                                                    <input id="thirddir1" name="wffsaccountitems[1].thirddir"
                                                           class="easyui-validatebox inputCss" readonly style="width:80px;"
                                                           readonly value=""/>
                                                </td>
                                                <td>
                                                    <input id="printip1" name="wffsaccountitems[1].printip"
                                                           class="easyui-validatebox inputCss" readonly style="width:90px;"
                                                           data-options="validType:'ip[\'printip1\']'"
                                                           readonly value=""/>
                                                </td>
                                                <td><input id="reqspace1"
                                                           name="wffsaccountitems[1].reqspace"
                                                           class="easyui-combobox" disabled
                                                           data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadReqspace(1);}"
                                                           style="width:60px;" value=""/>
                                                </td>
                                                <td><input id="permissions1"
                                                           name="wffsaccountitems[1].permissions"
                                                           class="easyui-combobox" disabled
                                                           data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadPermissions(1);}"
                                                           style="width:60px;" value=""/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="deltr(1);return false;"/>
                                                    <input id="shunxu1" type="hidden" name="wffsaccountitems[1].shunxu"
                                                           value="1"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr0">
                                            <td colspan="20" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="itemAdd" style="width:100px;float:left;"
                                                       value="添加一筆申請人"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td>導入格式：<a href="${ctx}/wffsaccountprocess/downLoad/fsAccountFile"><font color="blue">参考</font></a></td>
                            <td><a href="#" id="batchImport" class="easyui-linkbutton"
                                    data-options="iconCls:'icon-hamburg-cv'" style="width: 100px;"
                                    onclick="openBatchImportWin();">批量導入</a></td>
                        </tr>
                        <tr align="center">
                            <td width="8%">需求說明&nbsp;<font color="red">*</font></td>
                            <td width="92%" align="left" colspan="9"><textarea
                                id="describtion"
                                name="wfsaccountprocess.describtion" data-options="required:true"
                                oninput="return LessThanAuto(this,'txtNum');"
                                onchange="return LessThanAuto(this,'txtNum');"
                                onpropertychange="return LessThanAuto(this,'txtNum');"
                                maxlength="300"
                                class="easyui-validatebox" style="width:900px;height:70px;"
                                rows="5" cols="6">${wffsaccountprocessEntity.describtion}</textarea><span
                                id="txtNum"></span></td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td align="left" colspan="9">說明：<br/>
                                一﹑文件夾層次填寫說明﹕<br/>
                                (1)一般情況下，文件服務器帳號建議按處﹐部﹐課或工作職能進行層次劃分；(2)掃描帳號建議按SCAN、單位代碼、工號進行層次劃分；(3)以上層次劃分是一個范例﹐請用戶按工作實際需要進行填寫。<br/>
                                二﹑空間需求填寫說明﹕<br/>
                                (1)一般情況﹕課級5G空間﹐個人目錄1G空間限額； (2)如因工作需要需較大的存放空間的﹐請先聯絡管理人員確認空間需求后﹐再進行填寫；(3)如因空間要求較大﹐而現有空間不足夠的情況下﹐管理人員將評估空間擴容方案或是新購設備方案﹐方案所產生的費用由使用單位分攤。<br/>
                                三﹑關於權限設置的說明﹕<br/>
                                (1) R代表讀取及下載； (2) W代表可讀寫以及上傳下載﹔ (3)C代表可讀(下載)﹐可寫(上載)﹐可刪<br/>
                                四、簽核主管說明：<br/>
                                <font color="red">文件服務器帳號申請需處級主管核准。</font><br/>
<%--                                五.除掃描帳號外，新增文件服務器帳號須簽核《CAA資訊賬號使用承諾書》。<br/>--%>
<%--                                <a href="${ctx}/wffsaccountprocess/downLoad/CommitLetterDouble">《CAA資訊賬號使用承諾書-多人》</a><a href="${ctx}/wffsaccountprocess/downLoad/CommitLetterSingle">《CAA資訊賬號使用承諾書-單人》</a>--%>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">附件(<a href="${ctx}/wffsaccountprocess/downLoad/FTPAccount"><font color="blue">模板</font></a>)&nbsp;<font color="red">*</font></td>
                            <td width="90%" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button"
                                                                   value="点击上传文件" class="btn-file"/>
                                <input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
                            </span> <input type="hidden" id="attachids"
                                           name="wfsaccountprocess.attachids" value="${wffsaccountprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_wenjianfuwuqizhanghaoshenqing','文件服務器賬號申請單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl3Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno3_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(23,'yl3Table','ylno3','ylname3',$('#dealchoosefactoryid').combobox('getValue'),'wfsaccountprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno3" name="wfsaccountprocess.ylno3"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno3']}"
                                                               readonly
                                                               value="${wffsaccountprocessEntity.ylno3 }"/><c:if
                                                            test="${requiredMap['ylno3'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname3" name="wfsaccountprocess.ylname3"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno3']}"
                                                                value="${wffsaccountprocessEntity.ylname3 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zxywkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zxywkchargeno_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'zxywkchargeTable','zxywkchargeno','zxywkchargename',$('#dealchoosefactoryid').combobox('getValue'),'wfsaccountprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxywkchargeno" name="wfsaccountprocess.zxywkchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxywkchargeno']}"
                                                               readonly
                                                               value="${wffsaccountprocessEntity.zxywkchargeno }"/><c:if
                                                            test="${requiredMap['zxywkchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxywkchargename"
                                                                name="wfsaccountprocess.zxywkchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxywkchargeno']}"
                                                                value="${wffsaccountprocessEntity.zxywkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').val(),'wfsaccountprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wfsaccountprocess.cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wffsaccountprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wfsaccountprocess.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wffsaccountprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealfactoryid').val(),'wfsaccountprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wfsaccountprocess.zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wffsaccountprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="wfsaccountprocess.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wffsaccountprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealfactoryid').val(),'wfsaccountprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="wfsaccountprocess.zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wffsaccountprocessEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="wfsaccountprocess.zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wffsaccountprocessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl4Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno4_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(25,'yl4Table','ylno4','ylname4',$('#dealchoosefactoryid').combobox('getValue'),'wfsaccountprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno4" name="wfsaccountprocess.ylno4"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno4']}"
                                                               readonly
                                                               value="${wffsaccountprocessEntity.ylno4 }"/><c:if
                                                            test="${requiredMap['ylno4'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname4" name="wfsaccountprocess.ylname4"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno4']}"
                                                                value="${wffsaccountprocessEntity.ylname4 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#dealdeptno').val(),'pcchargeno','pcchargename',$('#dealfactoryid').val(),'wfsaccountprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="wfsaccountprocess.pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${wffsaccountprocessEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="wfsaccountprocess.pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wffsaccountprocessEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno2_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(176,'yl2Table','ylno2','ylname2',$('#dealchoosefactoryid').combobox('getValue'),'wfsaccountprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="wfsaccountprocess.ylno2"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}"
                                                               readonly
                                                               value="${wffsaccountprocessEntity.ylno2 }"/><c:if
                                                            test="${requiredMap['ylno2'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname2" name="wfsaccountprocess.ylname2"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}"
                                                                value="${wffsaccountprocessEntity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <%--<tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl1Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno1_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole5(21,'ylno1','ylname1','','','','',$('#dealchoosefactoryid').combobox('getValue'),$('#applyarea').combobox('getValue'),$('#applybuilding').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno1" name="wfsaccountprocess.ylno1"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno1']}"
                                                               readonly
                                                               value="${wffsaccountprocessEntity.ylno1 }"/><c:if
                                                            test="${requiredMap['ylno1'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname1" name="wfsaccountprocess.ylname1"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno1']}"
                                                                value="${wffsaccountprocessEntity.ylname1 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="wfsaccountprocess.kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wffsaccountprocessEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="wfsaccountprocess.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wffsaccountprocessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="wfsaccountprocess.bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wffsaccountprocessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="wfsaccountprocess.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wffsaccountprocessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').val(),'wfsaccountprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wfsaccountprocess.cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wffsaccountprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wfsaccountprocess.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wffsaccountprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealfactoryid').val(),'wfsaccountprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wfsaccountprocess.zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wffsaccountprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="wfsaccountprocess.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wffsaccountprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealfactoryid').val(),'wfsaccountprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="wfsaccountprocess.zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wffsaccountprocessEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="wfsaccountprocess.zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wffsaccountprocessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#dealdeptno').val(),'pcchargeno','pcchargename',$('#dealfactoryid').val(),'wfsaccountprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="wfsaccountprocess.pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${wffsaccountprocessEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="wfsaccountprocess.pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wffsaccountprocessEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hchargenoTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['hchargeno_name']}</td>
                                                                <td style="border: none;"><a
                                                                        href="#" onclick="addRowDto('hchargeno','wfsaccountprocess')">添加一位</a>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hchargeno" name="wfsaccountprocess.hchargeno"
                                                               class="easyui-validatebox" onblur="getUserNameByEmpno(this);"
                                                               data-options="width:80,required:${requiredMap['hchargeno']}"
                                                               value="${wffsaccountprocessEntity.hchargeno }"/><c:if
                                                            test="${requiredMap['hchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hchargename" name="wfsaccountprocess.hchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hchargeno']}"
                                                                value="${wffsaccountprocessEntity.hchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl3Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno3_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(23,'yl3Table','ylno3','ylname3',$('#dealchoosefactoryid').combobox('getValue'),'wfsaccountprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno3" name="wfsaccountprocess.ylno3"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno3']}"
                                                               readonly
                                                               value="${wffsaccountprocessEntity.ylno3 }"/><c:if
                                                            test="${requiredMap['ylno3'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname3" name="wfsaccountprocess.ylname3"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno3']}"
                                                                value="${wffsaccountprocessEntity.ylname3 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl4Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno4_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(25,'yl4Table','ylno4','ylname4',$('#dealchoosefactoryid').combobox('getValue'),'wfsaccountprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno4" name="wfsaccountprocess.ylno4"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno4']}"
                                                               readonly
                                                               value="${wffsaccountprocessEntity.ylno4 }"/><c:if
                                                            test="${requiredMap['ylno4'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname4" name="wfsaccountprocess.ylname4"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno4']}"
                                                                value="${wffsaccountprocessEntity.ylname4 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ylno2_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(176,'yl2Table','ylno2','ylname2',$('#dealchoosefactoryid').combobox('getValue'),'wfsaccountprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="wfsaccountprocess.ylno2"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}"
                                                               readonly
                                                               value="${wffsaccountprocessEntity.ylno2 }"/><c:if
                                                            test="${requiredMap['ylno2'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname2" name="wfsaccountprocess.ylname2"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}"
                                                                value="${wffsaccountprocessEntity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>--%>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="areaId" name="areaId" value=""/>
    <input type="hidden" id="buildingId" name="buildingId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <input type="hidden" id="onlyKchargeSignle" value="1" />
    <input type="hidden" id="loginname" name="loginname" value="${user.loginName}" />
    <div id="win"></div>
</form>
</div>
<div id="optionWin" class="easyui-window" title="文件服務器賬號申請" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%">
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="file" type="file" style="width: 300px"
                           accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>
                    <input id="isExcel2003" name="isExcel2003" type="hidden" value="">

                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr>
                <td align="left" style="width: 60%; white-space: nowrap;">
                    <div id="tishi" >正在導入中，請稍後...</div>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <span id="labelListAddResult"></span><a href="${ctx}/wffsaccountprocess/downLoad/errorExcel" id="downloadError"
                                                            plain="true">查看錯誤信息</a>
                </td>
            </tr>
        </table>
    </form>
</div>
<script src='${ctx}/static/js/information/wffsaccountprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>
