<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>文件服務器帳號申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wffsaccountprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wffsaccountprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wffsaccountprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">文件服務器帳號申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wffsaccountprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wffsaccountprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wffsaccountprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wffsaccountprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wffsaccountprocessEntity.makerno}/${wffsaccountprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">承辦人工號</td>
                            <td width="10%" class="td_style1">
                                <input id="dealno" name="wfsaccountprocess.dealno" class="easyui-validatebox inputCss"
                                       data-options="width: 80" readonly
                                       value="${wffsaccountprocessEntity.dealno}"
                                       onchange="queryUserInfo();"/>
                            </td>
                            <td width="5%">承辦人</td>
                            <td width="12%" class="td_style1">
                                <input id="dealname" name="wfsaccountprocess.dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wffsaccountprocessEntity.dealname }"/>
                            </td>
                            <td width="6%">單位代碼</td>
                            <td width="12%" class="td_style1">
                                <input id="dealdeptno" name="wfsaccountprocess.dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wffsaccountprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="6%">費用代碼</td>
                            <td width="12%" class="td_style1">
                                <input id="applycostno" name="wfsaccountprocess.applycostno" readonly
                                       class="easyui-validatebox inputCss" style="width:90px;"
                                       value="${wffsaccountprocessEntity.applycostno }"/>
                            </td>
                            <td width="8%">所在廠區</td>
                            <td width="21%" class="td_style1">
                                <input id="dealchoosefactoryid" name="wfsaccountprocess.dealchoosefactoryid"
                                       class="easyui-combobox" disabled
                                       panelHeight="auto" value="${wffsaccountprocessEntity.dealchoosefactoryid }"
                                       data-options="width: 120,required:true,onSelect:function(){onchangeFactory('dealchoosefactoryid');}"/>
                                <input id="dealfactoryid" name="wfsaccountprocess.dealfactoryid" type="hidden"
                                       value="${wffsaccountprocessEntity.dealfactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="wfsaccountprocess.applyleveltype"
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wffsaccountprocessEntity.applyleveltype }" readonly/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="wfsaccountprocess.applymanager"
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wffsaccountprocessEntity.applymanager }" readonly/>
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="wfsaccountprocess.dealemail"
                                       class="easyui-validatebox inputCss"
                                       value="${wffsaccountprocessEntity.dealemail }" style="width:300px;" readonly/>
                            </td>
                            <td>使用區域</td>
                            <td class="td_style1">
                                <input id="applyarea" name="wfsaccountprocess.applyarea" class="easyui-combobox"
                                       disabled
                                       data-options="width: 70,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea();}"
                                       value="${wffsaccountprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="wfsaccountprocess.applybuilding"
                                       class="easyui-combobox" disabled
                                       data-options="width: 70,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']'"
                                       value="${wffsaccountprocessEntity.applybuilding }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機</td>
                            <td class="td_style1">
                                <input id="dealtel" name="wfsaccountprocess.dealtel" class="easyui-validatebox inputCss"
                                       style="width:90px;position:relative;" readonly
                                       value="${wffsaccountprocessEntity.dealtel}"
                                       data-options="prompt:'579+66666'"/>
                            </td>
                            <td>單位</td>
                            <td colspan="7" class="td_style1">
                                <input id="dealdeptname" name="wfsaccountprocess.dealdeptname" readonly
                                       class="easyui-validatebox inputCss" data-options="width: 410"
                                       value="${wffsaccountprocessEntity.dealdeptname }"/>
                            </td>
                        </tr>
                    </table>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人明細</td>
                        </tr>
                        <tr>
                            <td colspan="10" align="left">
                                <div style="float: left">是否跨部門申請：</div>
                                <div style="float: left" class="ismydeptDiv"></div>
                                <input id="ismydept" name="wfsaccountprocess.ismydept" type="hidden"
                                       value="${wffsaccountprocessEntity.ismydept}"/></td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <input id="wfsaccountItemTableIndex" type="hidden"
                                           value="<c:if test="${wffsaccountitems!=null && wffsaccountitems.size()>0}">${wffsaccountitems.size() +1}</c:if>
                                        <c:if test="${wffsaccountitems.size()==0 || wffsaccountitems==null}">2</c:if>"/>
                                    </input>
                                    <table id="wfsaccountItemTable" width="100%" style="border-collapse: collapse">
                                        <tr align="center">
                                            <td>序號</td>
                                            <td>申請人工號</td>
                                            <td>申請人</td>
                                            <td>工作職能</td>
                                            <td>區域</td>
                                            <td>單位代碼</td>
                                            <td>單位</td>
                                            <td>電腦名稱</td>
                                            <td>IP地址</td>
                                            <td>FTP服務器IP</td>
                                            <td>需求類型</td>
                                            <td>申請類型</td>
                                            <td>安保區域</td>
                                            <td>第一層目錄</td>
                                            <td>第二層目錄</td>
                                            <td>第三層目錄</td>
                                            <td>列印機IP</td>
                                            <td>需求空間</td>
                                            <td>權限</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wffsaccountitems!=null&&wffsaccountitems.size()>0}">
                                            <c:forEach items="${wffsaccountitems}" var="wffsaccountitems"
                                                       varStatus="status">
                                                <tr align="center" id="wffsaccountitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                            ${wffsaccountitems.applyno}
                                                    </td>
                                                    <td>
                                                            ${wffsaccountitems.applyname}
                                                    </td>
                                                    <td>
                                                            ${wffsaccountitems.jobtype}
                                                    </td>
                                                    <td>
                                                            ${wffsaccountitems.usearea}
                                                    </td>
                                                    <td>
                                                            ${wffsaccountitems.deptno}
                                                    </td>
                                                    <td>
                                                            ${wffsaccountitems.deptname}
                                                    </td>
                                                    <td>
                                                            ${wffsaccountitems.pcname}
                                                    </td>
                                                    <td>
                                                            ${wffsaccountitems.pcip}
                                                    </td>
                                                    <td>
                                                            ${wffsaccountitems.ftpip}
                                                    </td>
                                                    <td><input id="reqtype${status.index+1}"
                                                               name="wffsaccountitems[${status.index+1}].reqtype"
                                                               class="easyui-combobox" disabled
                                                               data-options="width:110,
                                                                              panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadReqtype(${status.index+1});}"
                                                               style="width:110px;"
                                                               value="${wffsaccountitems.reqtype}"/>
                                                    </td>
                                                    <td><input id="applytype${status.index+1}"
                                                               name="wffsaccountitems[${status.index+1}].applytype"
                                                               class="easyui-combobox"
                                                               data-options="width:80,
                                                                              panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadApplyTypeFSaccount(${status.index+1});}"
                                                               disabled
                                                               style="width:70px;"
                                                               value="${wffsaccountitems.applytype}"/></td>
                                                    <td><input id="safearea${status.index+1}" disabled
                                                               name="wffsaccountitems[${status.index+1}].safearea"
                                                               class="easyui-combobox"
                                                               data-options="width:80,
                                                                              panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadSecurityArea(${status.index+1});}"
                                                               style="width:70px;"
                                                               value="${wffsaccountitems.safearea}"/></td>
                                                    <td>
                                                            ${wffsaccountitems.firstdir}
                                                    </td>
                                                    <td>
                                                            ${wffsaccountitems.seconddir}
                                                    </td>
                                                    <td>
                                                            ${wffsaccountitems.thirddir}
                                                    </td>
                                                    <td>
                                                            ${wffsaccountitems.printip}
                                                    </td>
                                                    <td><input id="reqspace${status.index+1}" disabled
                                                               name="wffsaccountitems[${status.index+1}].reqspace"
                                                               class="easyui-combobox"
                                                               data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadReqspace(${status.index+1});}"
                                                               style="width:50px;"
                                                               value="${wffsaccountitems.reqspace}"/></td>
                                                    <td><input id="permissions${status.index+1}" disabled
                                                               name="wffsaccountitems[${status.index+1}].permissions"
                                                               class="easyui-combobox"
                                                               data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadPermissions(${status.index+1});}"
                                                               style="width:40px;"
                                                               value="${wffsaccountitems.permissions}"/></td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${wffsaccountitems.size()==0 || wffsaccountitems==null}">
                                            <tr align="center" id="wffsaccountitems1">
                                                <td>1</td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td><input id="reqtype1"
                                                           name="wffsaccountitems[1].reqtype"
                                                           class="easyui-combobox"
                                                           data-options="width:110,
                                                                              panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false"
                                                           style="width:110px;" value=""/>
                                                </td>
                                                <td><input id="applytype1" disabled
                                                           name="wffsaccountitems[1].applytype"
                                                           class="easyui-combobox"
                                                           data-options="width:80,
                                                                              panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false"
                                                           style="width:80px;" value=""/>
                                                </td>
                                                <td><input id="safearea1" disabled
                                                           name="wffsaccountitems[1].safearea"
                                                           class="easyui-combobox"
                                                           data-options="width:80,
                                                                              panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false"
                                                           style="width:70px;" value=""/>
                                                </td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td><input id="reqspace1"
                                                           name="wffsaccountitems[1].reqspace"
                                                           class="easyui-combobox" disabled
                                                           data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadReqspace(1);}"
                                                           style="width:60px;" value=""/>
                                                </td>
                                                <td><input id="permissions1"
                                                           name="wffsaccountitems[1].permissions"
                                                           class="easyui-combobox" disabled
                                                           data-options="panelHeight:'auto',
																              valueField:'value',
																              textField:'label',
																              editable:false,
																              onBeforeLoad:function(){
																              loadPermissions(1);}"
                                                           style="width:60px;" value=""/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="8%">需求說明</td>
                            <td width="92%" align="left" colspan="9">${wffsaccountprocessEntity.describtion}</td>
                        </tr>
                        <tr align="center">
                             <td width="10%">附件</td>
                             <td width="90%" class="td_style1">
                                 <input type="hidden" id="attachids"
                                        name="wfsaccountprocess.attachids" value="${wffsaccountprocessEntity.attachids }"/>
                                 <div id="dowloadUrl">
                                     <c:forEach items="${file}" varStatus="i" var="item">
                                         <div id="${item.id}"
                                              style="line-height:30px;margin-left:5px;" class="float_L">
                                             <div class="float_L">
                                                 <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                             </div>
                                         </div>
                                     </c:forEach>
                                 </div>
                             </td>
                         </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','文件服務器賬號密碼申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wffsaccountprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wffsaccountprocessEntity.workstatus!=null&&wffsaccountprocessEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<div id="dlg"></div>
<script src='${ctx}/static/js/information/wffsaccountprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>