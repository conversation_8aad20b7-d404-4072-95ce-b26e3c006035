<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>TrustView系統權限申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wftvjurisdictionprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wftvjurisdictionprocessEntity.id }"/>
    <input id="serialno" name="wftvjurisdiction.serialno" type="hidden" value="${wftvjurisdictionprocessEntity.serialno }"/>
    <input id="makerno" name="wftvjurisdiction.makerno" type="hidden" value="${wftvjurisdictionprocessEntity.makerno }"/>
    <input id="makername" name="wftvjurisdiction.makername" type="hidden" value="${wftvjurisdictionprocessEntity.makername }"/>
    <input id="makerdeptno" name="wftvjurisdiction.makerdeptno" type="hidden" value="${wftvjurisdictionprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wftvjurisdiction.makerfactoryid" type="hidden" value="${wftvjurisdictionprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">TrustView系統權限申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wftvjurisdictionprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wftvjurisdictionprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wftvjurisdictionprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wftvjurisdictionprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wftvjurisdictionprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wftvjurisdictionprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wftvjurisdictionprocessEntity.makerno}/${wftvjurisdictionprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="dealno" name="wftvjurisdiction.dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wftvjurisdictionprocessEntity.dealno }"
                                       onblur="queryUserInfo('deal');"/>
                            </td>
                            <td width="8%">承辦人</td>
                            <td width="10%" class="td_style1">
                                <input id="dealname" name="wftvjurisdiction.dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wftvjurisdictionprocessEntity.dealname }"/>
                            </td>
                            <td width="8%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="dealdeptno" name="wftvjurisdiction.dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wftvjurisdictionprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="8%">費用代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="dealcostno" name="wftvjurisdiction.dealcostno"
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wftvjurisdictionprocessEntity.dealcostno }"/>
                            </td>
                            <td width="8%">廠區&nbsp;<font color="red">*</font></td>
                            <td width="15%" class="td_style1">
                                <input id="dealfactoryid" name="wftvjurisdiction.dealfactoryid"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${wftvjurisdictionprocessEntity.dealfactoryid }"
                                       data-options="width: 120,validType:'comboxValidate[\'dealfactoryid\',\'请選擇所在廠區\']'"/>
                                <input id="dealfactoryname" name="wftvjurisdiction.dealfactoryname" type="hidden" value="${wftvjurisdictionprocessEntity.dealfactoryname }"/>
                                <input id="dealnofactoryid" name="wftvjurisdiction.dealnofactoryid" type="hidden" value="${wftvjurisdictionprocessEntity.dealnofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="dealdeptname" name="wftvjurisdiction.dealdeptname"
                                       class="easyui-validatebox" data-options="width: 400,required:true"
                                       value="${wftvjurisdictionprocessEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="wftvjurisdiction.dealemail" class="easyui-validatebox"
                                       value="${wftvjurisdictionprocessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealtel" name="wftvjurisdiction.dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wftvjurisdictionprocessEntity.dealtel }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="validApplyTel('dealtel')"/>
                            </td>
                            <td>申請類型&nbsp;<font color="red">*</font></td>
                            <td colspan="7">
                                <div class="applytypeDiv" style="float: left;"></div>
                                <input id="applytype" name="wftvjurisdiction.applytype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wftvjurisdictionprocessEntity.applytype }"/>
                                <input id="applytypename" name="wftvjurisdiction.applytypename" type="hidden"
                                       value="${wftvjurisdictionprocessEntity.applytypename }"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申请人信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="wftvjurisdictionitemsTable" width="160%">
                                        <tr align="center">
                                            <td width="30px">序號</td>
                                            <td width="110px">工號&nbsp;<font color="red">*</font></td>
                                            <td width="110px">姓名&nbsp;<font color="red">*</font></td>
                                            <td width="120px">廠區&nbsp;<font color="red">*</font></td>
                                            <td width="120px">單位代碼&nbsp;<font color="red">*</font></td>
                                            <td width="200px">單位&nbsp;<font color="red">*</font></td>
                                            <td width="120px">使用區域&nbsp;<font color="red">*</font></td>
                                            <td width="120px">使用樓層&nbsp;<font color="red">*</font></td>
                                            <td width="130px">電腦IP&nbsp;<font color="red">*</font></td>
                                            <td width="130px">分機&nbsp;<font color="red">*</font></td>
                                            <td width="140px">外發文件類型</td>
                                            <td width="120px">審核主管<br>工號&nbsp;<font color="red">*</font></td>
                                            <td width="120px">審核主管<br>姓名&nbsp;<font color="red">*</font></td>
                                            <td width="120px">核准主管<br>工號&nbsp;<font color="red">*</font></td>
                                            <td width="120px">核准主管<br>姓名&nbsp;<font color="red">*</font></td>
                                            <td width="380px">權限申請說明</td>
                                            <td width="50px">操作</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wftvjurisdictionitems!=null&&wftvjurisdictionitems.size()>0}">
                                            <c:forEach items="${wftvjurisdictionitems}" var="tvjurisdictionitems" varStatus="status">
                                                <tr align="center" id="tvjurisdictionitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <input id="applyno${status.index+1}"
                                                               onblur="getUserNameByEmpno2(this,'apply',status.index+1);"
                                                               name="wftvjurisdictionitems[${status.index+1}].applyno"
                                                               class="easyui-validatebox" style="width:70px;"
                                                               data-options="required:true"
                                                               value="${tvjurisdictionitems.applyno}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyname${status.index+1}"
                                                               name="wftvjurisdictionitems[${status.index+1}].applyname"
                                                               class="easyui-validatebox inputCss" style="width:70px;"
                                                               readonly value="${tvjurisdictionitems.applyname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyfactoryid${status.index+1}"
                                                               name="wftvjurisdictionitems[${status.index+1}].applyfactoryid"
                                                               data-options="panelHeight:300,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'applyfactoryid${status.index+1}\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(${status.index+1});},onSelect:function(){onchangeApplyfactory(${status.index+1});}"
                                                               style="width:90px;" class="easyui-combobox"
                                                               value="${tvjurisdictionitems.applyfactoryid}"/>
                                                        <input id="applyfactoryname${status.index+1}" type="hidden"
                                                               name="wftvjurisdictionitems[${status.index+1}].applyfactoryname" value="${tvjurisdictionitems.applyfactoryname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applydeptno${status.index+1}"
                                                               name="wftvjurisdictionitems[${status.index+1}].applydeptno"
                                                               class="easyui-validatebox inputCss" style="width:80px;"
                                                               readonly value="${tvjurisdictionitems.applydeptno}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applydeptname${status.index+1}"
                                                               name="wftvjurisdictionitems[${status.index+1}].applydeptname"
                                                               class="easyui-validatebox inputCss" style="width:150px;"
                                                               readonly value="${tvjurisdictionitems.applydeptname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyarea${status.index+1}"
                                                               name="wftvjurisdictionitems[${status.index+1}].applyarea"
                                                               class="easyui-validatebox" style="width:80px;"
                                                               data-options="required:true"
                                                               value="${tvjurisdictionitems.applyarea}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applybuilding${status.index+1}"
                                                               name="wftvjurisdictionitems[${status.index+1}].applybuilding"
                                                               class="easyui-validatebox" style="width:80px;"
                                                               data-options="required:true"
                                                               value="${tvjurisdictionitems.applybuilding}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyip${status.index+1}" name="wftvjurisdictionitems[${status.index+1}].applyip" class="easyui-validatebox"
                                                               data-options="required:true,validType:'ip[\'applyip${status.index+1}\']'" style="width:90px;" value="${tvjurisdictionitems.applyip}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applytel${status.index+1}"
                                                               name="wftvjurisdictionitems[${status.index+1}].applytel"
                                                               class="easyui-validatebox" style="width:80px;"
                                                               data-options="required:true" onblur="validApplyTel('applytel${status.index+1}')"
                                                               value="${tvjurisdictionitems.applytel}"/>
                                                    </td>
                                                    <td>
                                                        <input id="outfiletype${status.index+1}"
                                                               name="wftvjurisdictionitems[${status.index+1}].outfiletype"
                                                               class="easyui-validatebox" style="width:100px;"
                                                               value="${tvjurisdictionitems.outfiletype}"/>
                                                    </td>
                                                    <td>
                                                        <input id="shempno${status.index+1}"
                                                               onblur="getUserNameByEmpno2(this,shemp,status.index+1);"
                                                               name="wftvjurisdictionitems[${status.index+1}].shempno"
                                                               class="easyui-validatebox" style="width:70px;"
                                                               data-options="required:true"
                                                               value="${tvjurisdictionitems.shempno}"/>
                                                    </td>
                                                    <td>
                                                        <input id="shempname${status.index+1}"
                                                               name="wftvjurisdictionitems[${status.index+1}].shempname"
                                                               class="easyui-validatebox inputCss" style="width:70px;"
                                                               readonly value="${tvjurisdictionitems.shempname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="hzempno${status.index+1}"
                                                               onblur="getUserNameByEmpno2(this,hzemp,status.index+1);"
                                                               name="wftvjurisdictionitems[${status.index+1}].hzempno"
                                                               class="easyui-validatebox" style="width:70px;"
                                                               data-options="required:true"
                                                               value="${tvjurisdictionitems.hzempno}"/>
                                                    </td>
                                                    <td>
                                                        <input id="hzempname${status.index+1}"
                                                               name="wftvjurisdictionitems[${status.index+1}].hzempname"
                                                               class="easyui-validatebox inputCss" style="width:70px;"
                                                               readonly value="${tvjurisdictionitems.hzempname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyremarks${status.index+1}"
                                                               name="wftvjurisdictionitems[${status.index+1}].applyremarks"
                                                               class="easyui-validatebox" style="width:280px;"
                                                               value="${tvjurisdictionitems.applyremarks}"/>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="tvjurisdeltr(${status.index+1});return false;"/>
                                                        <input id="shunxu${status.index+1}" type="hidden" name="wftvjurisdictionitems[${status.index+1}].shunxu" value="${status.index+1}"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${wftvjurisdictionitems.size()==0 || wftvjurisdictionitems==null}">
                                            <tr align="center" id="tvjurisdictionitems1">
                                                <td>1</td>
                                                <td>
                                                    <input id="applyno1"
                                                           onblur="getUserNameByEmpno2(this,'apply',1);"
                                                           name="wftvjurisdictionitems[1].applyno"
                                                           class="easyui-validatebox" style="width:70px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyname1"
                                                           name="wftvjurisdictionitems[1].applyname"
                                                           class="easyui-validatebox inputCss" style="width:70px;" readonly value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyfactoryid1"
                                                           name="wftvjurisdictionitems[1].applyfactoryid"
                                                           data-options="panelHeight:300,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'applyfactoryid1\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(1);},onSelect:function(){onchangeApplyfactory(1);}"
                                                           style="width:90px;" class="easyui-combobox" value=""/>
                                                    <input id="applyfactoryname1" type="hidden"
                                                           name="wftvjurisdictionitems[1].applyfactoryname" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applydeptno1"
                                                           name="wftvjurisdictionitems[1].applydeptno"
                                                           class="easyui-validatebox inputCss" style="width:80px;"
                                                           readonly value=""/>
                                                </td>
                                                <td>
                                                    <input id="applydeptname1" name="wftvjurisdictionitems[1].applydeptname"
                                                           class="easyui-validatebox inputCss" style="width:150px;" readonly value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyarea1"
                                                           name="wftvjurisdictionitems[1].applyarea"
                                                           class="easyui-validatebox" style="width:80px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applybuilding1"
                                                           name="wftvjurisdictionitems[1].applybuilding"
                                                           class="easyui-validatebox" style="width:80px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyip1" name="wftvjurisdictionitems[1].applyip" class="easyui-validatebox"
                                                           data-options="required:true,validType:'ip[\'applyip1\']'" style="width:90px;" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applytel1"
                                                           name="wftvjurisdictionitems[1].applytel"
                                                           class="easyui-validatebox" style="width:80px;" onblur="validApplyTel('applytel1')"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="outfiletype1"
                                                           name="wftvjurisdictionitems[1].outfiletype"
                                                           class="easyui-validatebox" style="width:100px;" value=""/>
                                                </td>
                                                <td>
                                                    <input id="shempno1"
                                                           onblur="getUserNameByEmpno2(this,'shemp',1);"
                                                           name="wftvjurisdictionitems[1].shempno"
                                                           class="easyui-validatebox" style="width:70px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="shempname1"
                                                           name="wftvjurisdictionitems[1].shempname"
                                                           class="easyui-validatebox inputCss" style="width:70px;"
                                                           readonly value=""/>
                                                </td>
                                                <td>
                                                    <input id="hzempno1"
                                                           onblur="getUserNameByEmpno2(this,'hzemp',1);"
                                                           name="wftvjurisdictionitems[1].hzempno"
                                                           class="easyui-validatebox" style="width:70px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="hzempname1"
                                                           name="wftvjurisdictionitems[1].hzempname"
                                                           class="easyui-validatebox inputCss" style="width:70px;"
                                                           readonly value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyremarks1"
                                                           name="wftvjurisdictionitems[1].applyremarks"
                                                           class="easyui-validatebox" style="width:280px;" value=""/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="tvjurisdeltr(1);return false;"/>
                                                    <input id="shunxu1" type="hidden" name="wftvjurisdictionitems[1].shunxu" value="1"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr0">
                                            <td colspan="17" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="tvjurisItemAdd" style="width:150px;float:left;" value="添加一行"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="100px">備註</td>
                            <td width="90%" class="td_style2" style="text-align: left">
                                使用說明：<br>
                                1. 本表單適用于TrustView系統 NPI外發/解密權限申請；<br>
                                2. 須按表如實填寫申請人員信息及用途；<br>
                                3. 客戶端軟體由廠區資訊人員安裝，安裝申請流程請聯繫廠區資訊維護人員；<br>
                                4. 首次使用需開通電腦IP訪問Trust View服務器地址(NPI帳號：*************，非NPI賬號：************    端口為：9443)登陸使用，輸入以上服務器地址即可；<br>
                                5.外發文件申請人帳號為課部級主管核准主管要求到處級；<br>
                                6.申請此表前請用戶確認審核主管是否有TrustView帳號，若無則需與帳號申請單一起先行申請開通其帳號權限且需與申請外發人員帳號權限類型一致（即同時NPI區域）；<br>
                                7.管控料件圖檔或客戶圖檔，不准申請解密，所有需解密圖檔均需符合資安管控要求。
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_trustviewquanxian_shenqingdan','TrustView系統權限申請單','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zacschargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">資安窗口初審</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(263,'zacschargeTable','zacschargeno','zacschargename',$('#dealfactoryid').combobox('getValue'),'wftvjurisdiction')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zacschargeno" name="wftvjurisdiction.zacschargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zacschargeno']}"
                                                               readonly
                                                               value="${wftvjurisdictionprocessEntity.zacschargeno }"/><c:if
                                                            test="${requiredMap['zacschargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zacschargename" name="wftvjurisdiction.zacschargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zacschargeno']}"
                                                                value="${wftvjurisdictionprocessEntity.zacschargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealnofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="wftvjurisdiction.kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wftvjurisdictionprocessEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="wftvjurisdiction.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wftvjurisdictionprocessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealnofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="wftvjurisdiction.bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wftvjurisdictionprocessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="wftvjurisdiction.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wftvjurisdictionprocessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#dealnofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wftvjurisdiction.cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wftvjurisdictionprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wftvjurisdiction.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wftvjurisdictionprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealnofactoryid').val(),'wftvjurisdiction')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wftvjurisdiction.zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wftvjurisdictionprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="wftvjurisdiction.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wftvjurisdictionprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealnofactoryid').val(),'wftvjurisdiction')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="wftvjurisdiction.zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wftvjurisdictionprocessEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="wftvjurisdiction.zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wftvjurisdictionprocessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產品處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#dealdeptno').val(),'pcchargeno','pcchargename',$('#dealnofactoryid').val(),'wftvjurisdiction')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="wftvjurisdiction.pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${wftvjurisdictionprocessEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="wftvjurisdiction.pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wftvjurisdictionprocessEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zakchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">資安課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(153,'zakchargeTable','zakchargeno','zakchargename',$('#dealfactoryid').combobox('getValue'),'wftvjurisdiction')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zakchargeno" name="wftvjurisdiction.zakchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zakchargeno']}"
                                                               readonly
                                                               value="${wftvjurisdictionprocessEntity.zakchargeno }"/><c:if
                                                            test="${requiredMap['zakchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zakchargename" name="wftvjurisdiction.zakchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zakchargeno']}"
                                                                value="${wftvjurisdictionprocessEntity.zakchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zabchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">資安部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(154,'zabchargeTable','zabchargeno','zabchargename',$('#dealfactoryid').combobox('getValue'),'wftvjurisdiction')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zabchargeno" name="wftvjurisdiction.zabchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zabchargeno']}"
                                                               readonly
                                                               value="${wftvjurisdictionprocessEntity.zabchargeno }"/><c:if
                                                            test="${requiredMap['zabchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zabchargename" name="wftvjurisdiction.zabchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zabchargeno']}"
                                                                value="${wftvjurisdictionprocessEntity.zabchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="cackchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產品安全管理處窗口
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(264,'cackchargeTable','cackchargeno','cackchargename',$('#dealfactoryid').combobox('getValue'),'wftvjurisdiction')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cackchargeno" name="wftvjurisdiction.cackchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cackchargeno']}"
                                                               readonly
                                                               value="${wftvjurisdictionprocessEntity.cackchargeno }"/><c:if
                                                            test="${requiredMap['cackchargeno'].equals('true')}">
                                                        <span id="cackredDiv"><font color="red">*</font></span></c:if>
                                                        /<input id="cackchargename" name="wftvjurisdiction.cackchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cackchargeno']}"
                                                                value="${wftvjurisdictionprocessEntity.cackchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="cakchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產品安全管理處課級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(265,'cakchargeTable','cakchargeno','cakchargename',$('#dealfactoryid').combobox('getValue'),'wftvjurisdiction')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cakchargeno" name="wftvjurisdiction.cakchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cakchargeno']}"
                                                               readonly
                                                               value="${wftvjurisdictionprocessEntity.cakchargeno }"/><c:if
                                                            test="${requiredMap['cakchargeno'].equals('true')}">
                                                        <span id="cakredDiv"><font color="red">*</font></span></c:if>
                                                        /<input id="cakchargename" name="wftvjurisdiction.cakchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cakchargeno']}"
                                                                value="${wftvjurisdictionprocessEntity.cakchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="cabchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產品安全管理處部級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(266,'cabchargeTable','cabchargeno','cabchargename',$('#dealfactoryid').combobox('getValue'),'wftvjurisdiction')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cabchargeno" name="wftvjurisdiction.cabchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cabchargeno']}"
                                                               readonly
                                                               value="${wftvjurisdictionprocessEntity.cabchargeno }"/><c:if
                                                            test="${requiredMap['cabchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cabchargename" name="wftvjurisdiction.cabchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cabchargeno']}"
                                                                value="${wftvjurisdictionprocessEntity.cabchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="cacchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產品安全管理處廠級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(267,'cacchargeTable','cacchargeno','cacchargename',$('#dealfactoryid').combobox('getValue'),'wftvjurisdiction')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cacchargeno" name="wftvjurisdiction.cacchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cacchargeno']}"
                                                               readonly
                                                               value="${wftvjurisdictionprocessEntity.cacchargeno }"/><c:if
                                                            test="${requiredMap['cacchargeno'].equals('true')}">
                                                        <span id="cacredDiv"><font color="red">*</font></span></c:if>
                                                        /<input id="cacchargename" name="wftvjurisdiction.cacchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cacchargeno']}"
                                                                value="${wftvjurisdictionprocessEntity.cacchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="tvchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">TV管理員作業</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(268,'tvchargeTable','tvchargeno','tvchargename',$('#dealfactoryid').combobox('getValue'),'wftvjurisdiction')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="tvchargeno" name="wftvjurisdiction.tvchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['tvchargeno']}"
                                                               readonly
                                                               value="${wftvjurisdictionprocessEntity.tvchargeno }"/><c:if
                                                            test="${requiredMap['tvchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="tvchargename" name="wftvjurisdiction.tvchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['tvchargeno']}"
                                                                value="${wftvjurisdictionprocessEntity.tvchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
</form>
</div>
<script src='${ctx}/static/js/safety/wftvjurisdictionprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>