<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>TrustView系統權限申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wftvjurisdictionprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wftvjurisdictionprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wftvjurisdictionprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">TrustView系統權限申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wftvjurisdictionprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wftvjurisdictionprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wftvjurisdictionprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wftvjurisdictionprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wftvjurisdictionprocessEntity.makerno}/${wftvjurisdictionprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">承辦人工號</td>
                            <td width="10%" class="td_style2">${wftvjurisdictionprocessEntity.dealno}</td>
                            <td width="8%">承辦人</td>
                            <td width="10%" class="td_style2">${wftvjurisdictionprocessEntity.dealname}</td>
                            <td width="8%">單位代碼</td>
                            <td width="10%" class="td_style2">${wftvjurisdictionprocessEntity.dealdeptno}</td>
                            <td width="8%">費用代碼</td>
                            <td width="10%" class="td_style2">${wftvjurisdictionprocessEntity.dealcostno}</td>
                            <td width="8%">廠區</td>
                            <td width="15%" class="td_style2">${wftvjurisdictionprocessEntity.dealfactoryname}
                            </td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱</td>
                            <td colspan="5" class="td_style2">${wftvjurisdictionprocessEntity.dealdeptname}</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wftvjurisdictionprocessEntity.dealemail}</td>
                        </tr>
                        <tr align="center">
                            <td>聯繫方式</td>
                            <td class="td_style2">${wftvjurisdictionprocessEntity.dealtel}</td>
                            <td>申請類型</td>
                            <td colspan="7" class="td_style2">${wftvjurisdictionprocessEntity.applytypename}</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申请人信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="wftvaccountnumberitemsTable" width="160%">
                                        <tr align="center">
                                            <td width="30px">序號</td>
                                            <td width="110px">工號</td>
                                            <td width="110px">姓名</td>
                                            <td width="120px">廠區</td>
                                            <td width="120px">單位代碼</td>
                                            <td width="200px">單位</td>
                                            <td width="120px">使用區域</td>
                                            <td width="120px">使用樓層</td>
                                            <td width="130px">電腦IP</td>
                                            <td width="130px">分機</td>
                                            <td width="110px">外發文件類型</td>
                                            <td width="120px">審核主管<br>工號</td>
                                            <td width="120px">審核主管<br>姓名</td>
                                            <td width="120px">核准主管<br>工號</td>
                                            <td width="120px">核准主管<br>姓名</td>
                                            <td width="380px">權限申請說明</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wftvjurisdictionitems!=null&&wftvjurisdictionitems.size()>0}">
                                            <c:forEach items="${wftvjurisdictionitems}" var="tvjurisdictionitems" varStatus="status">
                                                <tr align="center" id="tvjurisdictionitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>${tvjurisdictionitems.applyno}</td>
                                                    <td>${tvjurisdictionitems.applyname}</td>
                                                    <td>${tvjurisdictionitems.applyfactoryname}</td>
                                                    <td>${tvjurisdictionitems.applydeptno}</td>
                                                    <td>${tvjurisdictionitems.applydeptname}</td>
                                                    <td>${tvjurisdictionitems.applyarea}</td>
                                                    <td>${tvjurisdictionitems.applybuilding}</td>
                                                    <td>${tvjurisdictionitems.applyip}</td>
                                                    <td>${tvjurisdictionitems.applytel}</td>
                                                    <td>${tvjurisdictionitems.outfiletype}</td>
                                                    <td>${tvjurisdictionitems.shempno}</td>
                                                    <td>${tvjurisdictionitems.shempname}</td>
                                                    <td>${tvjurisdictionitems.hzempno}</td>
                                                    <td>${tvjurisdictionitems.hzempname}</td>
                                                    <td>${tvjurisdictionitems.applyremarks}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="100px">批註</td>
                            <td width="90%" td_style2>
						        <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wftvjurisdictionprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','TrustView系統權限申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wftvjurisdictionprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/safety/wftvjurisdictionprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>