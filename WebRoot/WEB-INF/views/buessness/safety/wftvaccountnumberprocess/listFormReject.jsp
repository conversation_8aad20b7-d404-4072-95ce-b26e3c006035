<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>TrustView系統賬號申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/wftvaccountnumberprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wftvaccountnumberprocessEntity.id }"/>
    <input id="serialno" name="wftvaccountnumber.serialno" type="hidden" value="${wftvaccountnumberprocessEntity.serialno }"/>
    <input id="makerno" name="wftvaccountnumber.makerno" type="hidden" value="${wftvaccountnumberprocessEntity.makerno }"/>
    <input id="makername" name="wftvaccountnumber.makername" type="hidden" value="${wftvaccountnumberprocessEntity.makername }"/>
    <input id="makerdeptno" name="wftvaccountnumber.makerdeptno" type="hidden" value="${wftvaccountnumberprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wftvaccountnumber.makerfactoryid" type="hidden" value="${wftvaccountnumberprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">TrustView系統賬號申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wftvaccountnumberprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wftvaccountnumberprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wftvaccountnumberprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wftvaccountnumberprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wftvaccountnumberprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wftvaccountnumberprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wftvaccountnumberprocessEntity.makerno}/${wftvaccountnumberprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="dealno" name="wftvaccountnumber.dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wftvaccountnumberprocessEntity.dealno }"
                                       onblur="queryUserInfo('deal');"/>
                            </td>
                            <td width="8%">承辦人</td>
                            <td width="10%" class="td_style1">
                                <input id="dealname" name="wftvaccountnumber.dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wftvaccountnumberprocessEntity.dealname }"/>
                            </td>
                            <td width="8%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="dealdeptno" name="wftvaccountnumber.dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wftvaccountnumberprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="8%">費用代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="dealcostno" name="wftvaccountnumber.dealcostno"
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wftvaccountnumberprocessEntity.dealcostno }"/>
                            </td>
                            <td width="8%">廠區&nbsp;<font color="red">*</font></td>
                            <td width="15%" class="td_style1">
                                <input id="dealfactoryid" name="wftvaccountnumber.dealfactoryid"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${wftvaccountnumberprocessEntity.dealfactoryid }"
                                       data-options="width: 120,validType:'comboxValidate[\'dealfactoryid\',\'请選擇所在廠區\']'"/>
                                <input id="dealfactoryname" name="wftvaccountnumber.dealfactoryname" type="hidden" value="${wftvaccountnumberprocessEntity.dealfactoryname }"/>
                                <input id="dealnofactoryid" name="wftvaccountnumber.dealnofactoryid" type="hidden" value="${wftvaccountnumberprocessEntity.dealnofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="dealdeptname" name="wftvaccountnumber.dealdeptname"
                                       class="easyui-validatebox" data-options="width: 400,required:true"
                                       value="${wftvaccountnumberprocessEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="wftvaccountnumber.dealemail" class="easyui-validatebox"
                                       value="${wftvaccountnumberprocessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealtel" name="wftvaccountnumber.dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wftvaccountnumberprocessEntity.dealtel }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="validApplyTel('dealtel')"/>
                            </td>
                            <td>申請類型&nbsp;<font color="red">*</font></td>
                            <td colspan="3">
                                <div class="applytypeDiv" style="float: left;"></div>
                                <input id="applytype" name="wftvaccountnumber.applytype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wftvaccountnumberprocessEntity.applytype }"/>
                                <input id="applytypename" name="wftvaccountnumber.applytypename" type="hidden"
                                       value="${wftvaccountnumberprocessEntity.applytypename }"/>
                            </td>
                            <td>使用區域&nbsp;<font color="red">*</font></td>
                            <td colspan="3">
                                <div class="applysectypeDiv" style="float: left;"></div>
                                <input id="applysectype" name="wftvaccountnumber.applysectype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wftvaccountnumberprocessEntity.applysectype }"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申请人信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="wftvaccountnumberitemsTable" width="130%">
                                        <tr align="center">
                                            <td width="30px">序號</td>
                                            <td width="110px">工號&nbsp;<font color="red">*</font></td>
                                            <td width="110px">姓名&nbsp;<font color="red">*</font></td>
                                            <td width="120px">廠區&nbsp;<font color="red">*</font></td>
                                            <td width="120px">單位代碼&nbsp;<font color="red">*</font></td>
                                            <td width="200px">單位&nbsp;<font color="red">*</font></td>
                                            <td width="120px">使用區域&nbsp;<font color="red">*</font></td>
                                            <td width="120px">使用樓層&nbsp;<font color="red">*</font></td>
                                            <td width="130px">電腦IP&nbsp;<font color="red">*</font></td>
                                            <td width="110px">是否NPI&nbsp;<font color="red">*</font></td>
                                            <td width="110px">是否安裝<br/>指紋儀&nbsp;<font color="red">*</font></td>
                                            <td width="130px">分機&nbsp;<font color="red">*</font></td>
                                            <td width="90px">外发权限&nbsp;<font color="red">*</font></td>
                                            <td width="380px">備註</td>
                                            <td width="50px">操作</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wftvaccountnumberitems!=null&&wftvaccountnumberitems.size()>0}">
                                            <c:forEach items="${wftvaccountnumberitems}" var="tvaccountnumberitems" varStatus="status">
                                                <tr align="center" id="tvaccountnumberitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <input id="applyno${status.index+1}"
                                                               onblur="getUserNameByEmpno2(this,status.index+1);"
                                                               name="wftvaccountnumberitems[${status.index+1}].applyno"
                                                               class="easyui-validatebox" style="width:70px;"
                                                               data-options="required:true"
                                                               value="${tvaccountnumberitems.applyno}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyname${status.index+1}"
                                                               name="wftvaccountnumberitems[${status.index+1}].applyname"
                                                               class="easyui-validatebox inputCss" style="width:70px;"
                                                               readonly value="${tvaccountnumberitems.applyname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyfactoryid${status.index+1}"
                                                               name="wftvaccountnumberitems[${status.index+1}].applyfactoryid"
                                                               data-options="panelHeight:300,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'applyfactoryid${status.index+1}\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(${status.index+1});},onSelect:function(){onchangeApplyfactory(${status.index+1});}"
                                                               style="width:90px;" class="easyui-combobox"
                                                               value="${tvaccountnumberitems.applyfactoryid}"/>
                                                        <input id="applyfactoryname${status.index+1}" type="hidden"
                                                               name="wftvaccountnumberitems[${status.index+1}].applyfactoryname" value="${tvaccountnumberitems.applyfactoryname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applydeptno${status.index+1}"
                                                               name="wftvaccountnumberitems[${status.index+1}].applydeptno"
                                                               class="easyui-validatebox inputCss" style="width:80px;"
                                                               readonly value="${tvaccountnumberitems.applydeptno}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applydeptname${status.index+1}"
                                                               name="wftvaccountnumberitems[${status.index+1}].applydeptname"
                                                               class="easyui-validatebox inputCss" style="width:150px;"
                                                               readonly value="${tvaccountnumberitems.applydeptname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyarea${status.index+1}"
                                                               name="wftvaccountnumberitems[${status.index+1}].applyarea"
                                                               class="easyui-validatebox" style="width:80px;"
                                                               data-options="required:true"
                                                               value="${tvaccountnumberitems.applyarea}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applybuilding${status.index+1}"
                                                               name="wftvaccountnumberitems[${status.index+1}].applybuilding"
                                                               class="easyui-validatebox" style="width:80px;"
                                                               data-options="required:true"
                                                               value="${tvaccountnumberitems.applybuilding}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyip${status.index+1}" name="wftvaccountnumberitems[${status.index+1}].applyip" class="easyui-validatebox" onblur="isNpiCheck(this,${status.index+1});"
                                                               data-options="required:true,validType:'ip[\'applyip${status.index+1}\']'" style="width:90px;" value="${tvaccountnumberitems.applyip}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applynpi${status.index+1}" name="wftvaccountnumberitems[${status.index+1}].applynpi" class="easyui-validatebox inputCss" readonly
                                                               data-options="width: 60" value="${tvaccountnumberitems.applynpi}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyfingerprint${status.index+1}" name="wftvaccountnumberitems[${status.index+1}].applyfingerprint" class="easyui-validatebox inputCss" readonly
                                                               data-options="width: 60" value="${tvaccountnumberitems.applyfingerprint}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applytel${status.index+1}"
                                                               name="wftvaccountnumberitems[${status.index+1}].applytel"
                                                               class="easyui-validatebox" style="width:80px;"
                                                               data-options="required:true" onblur="validApplyTel('applytel${status.index+1}')"
                                                               value="${tvaccountnumberitems.applytel}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyout${status.index+1}" name="wftvaccountnumberitems[${status.index+1}].applyout" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadApplyOut(${status.index+1});}" style="width:50px;"
                                                               class="easyui-combobox" value="${tvaccountnumberitems.applyout}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyremarks${status.index+1}"
                                                               name="wftvaccountnumberitems[${status.index+1}].applyremarks"
                                                               class="easyui-validatebox" style="width:280px;"
                                                               value="${tvaccountnumberitems.applyremarks}"/>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="tvaccountdeltr(${status.index+1});return false;"/>
                                                        <input id="shunxu${status.index+1}" type="hidden" name="wftvaccountnumberitems[${status.index+1}].shunxu" value="${status.index+1}"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${wftvaccountnumberitems.size()==0 || wftvaccountnumberitems==null}">
                                            <tr align="center" id="tvaccountnumberitems1">
                                                <td>1</td>
                                                <td>
                                                    <input id="applyno1"
                                                           onblur="getUserNameByEmpno2(this,1);"
                                                           name="wftvaccountnumberitems[1].applyno"
                                                           class="easyui-validatebox" style="width:70px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyname1"
                                                           name="wftvaccountnumberitems[1].applyname"
                                                           class="easyui-validatebox inputCss" style="width:70px;" readonly value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyfactoryid1"
                                                           name="wftvaccountnumberitems[1].applyfactoryid"
                                                           data-options="panelHeight:300,valueField:'factoryid', textField:'factoryname',editable:false,validType:'comboxValidate[\'applyfactoryid1\',\'请選擇廠區\']',onBeforeLoad:function(){loadapplyfactory(1);},onSelect:function(){onchangeApplyfactory(1);}"
                                                           style="width:90px;" class="easyui-combobox" value=""/>
                                                    <input id="applyfactoryname1" type="hidden"
                                                           name="wftvaccountnumberitems[1].applyfactoryname" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applydeptno1"
                                                           name="wftvaccountnumberitems[1].applydeptno"
                                                           class="easyui-validatebox inputCss" style="width:80px;"
                                                           readonly value=""/>
                                                </td>
                                                <td>
                                                    <input id="applydeptname1" name="wftvaccountnumberitems[1].applydeptname"
                                                           class="easyui-validatebox inputCss" style="width:150px;" readonly value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyarea1"
                                                           name="wftvaccountnumberitems[1].applyarea"
                                                           class="easyui-validatebox" style="width:80px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applybuilding1"
                                                           name="wftvaccountnumberitems[1].applybuilding"
                                                           class="easyui-validatebox" style="width:80px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyip1" name="wftvaccountnumberitems[1].applyip" class="easyui-validatebox" onblur="isNpiCheck(this,1);"
                                                           data-options="required:true,validType:'ip[\'applyip1\']'" style="width:90px;" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applynpi1" name="wftvaccountnumberitems[1].applynpi" class="easyui-validatebox inputCss" readonly
                                                           data-options="width: 60" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyfingerprint1" name="wftvaccountnumberitems[1].applyfingerprint" class="easyui-validatebox inputCss" readonly
                                                           data-options="width: 60" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applytel1"
                                                           name="wftvaccountnumberitems[1].applytel"
                                                           class="easyui-validatebox" style="width:80px;" onblur="validApplyTel('applytel1')"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyout1" name="wftvaccountnumberitems[1].applyout" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadApplyOut(1);}" style="width:50px;"
                                                           class="easyui-combobox" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyremarks1"
                                                           name="wftvaccountnumberitems[1].applyremarks"
                                                           class="easyui-validatebox" style="width:280px;" value=""/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="tvaccountdeltr(1);return false;"/>
                                                    <input id="shunxu1" type="hidden" name="wftvaccountnumberitems[1].shunxu" value="1"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr0">
                                            <td colspan="15" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="tvaccountItemAdd" style="width:150px;float:left;" value="添加一行"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">需求說明&nbsp;<font color="red">*</font></td>
                            <td width="90%" class="td_style1">
                            <textarea id="applyreason" name="wftvaccountnumber.applyreason"
                                      class="easyui-validatebox"
                                      oninput="return LessThan(this);"
                                      onchange="return LessThan(this);"
                                      onpropertychange="return LessThan(this);"
                                      maxlength="300"
                                      style="width:99%;height:80px;" data-options="required:true,validType:'length[0,300]'"
                                      rows="5" cols="6">${wftvaccountnumberprocessEntity.applyreason }</textarea><span id="txtNum"></span>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">備註</td>
                            <td width="90%" class="td_style2" style="text-align: left">
                                1.本表單適用于TrustView系統帳號申請；<br>
                                2.須按表如實填寫申請人員信息；<br>
                                3.客戶端軟體由廠區資訊人員安裝，安裝申請流程請聯繫廠區資訊維護人員；<br>
                                4.首次使用需開通電腦IP訪問Trust View服務器地址(NPI帳號：*************，非NPI賬號：************  端口為：9443)登陸使用，輸入以上服務器地址即可。
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','TrustView系統賬號申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zacschargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">資安窗口初審</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(263,'zacschargeTable','zacschargeno','zacschargename',$('#dealfactoryid').combobox('getValue'),'wftvaccountnumber')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zacschargeno" name="wftvaccountnumber.zacschargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zacschargeno']}"
                                                               readonly
                                                               value="${wftvaccountnumberprocessEntity.zacschargeno }"/><c:if
                                                            test="${requiredMap['zacschargeno'].equals('true')}">
                                                        <span id="zacsredDiv"><font color="red">*</font></span></c:if>
                                                        /<input id="zacschargename" name="wftvaccountnumber.zacschargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zacschargeno']}"
                                                                value="${wftvaccountnumberprocessEntity.zacschargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealnofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="wftvaccountnumber.kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wftvaccountnumberprocessEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}">
                                                        <span id="kredDiv"><font color="red">*</font></span></c:if>
                                                        /<input id="kchargename" name="wftvaccountnumber.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wftvaccountnumberprocessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealnofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="wftvaccountnumber.bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wftvaccountnumberprocessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}">
                                                        <span id="bredDiv"><font color="red">*</font></span></c:if>
                                                        /<input id="bchargename" name="wftvaccountnumber.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wftvaccountnumberprocessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#dealdeptno').val(),'cchargeno','cchargename',$('#dealnofactoryid').val(),'wftvaccountnumber')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wftvaccountnumber.cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wftvaccountnumberprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wftvaccountnumber.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wftvaccountnumberprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealnofactoryid').val(),'wftvaccountnumber')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wftvaccountnumber.zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wftvaccountnumberprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}">
                                                        <span id="zredDiv"><font color="red">*</font></span></c:if>
                                                        /<input id="zchargename" name="wftvaccountnumber.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wftvaccountnumberprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealnofactoryid').val(),'wftvaccountnumber')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="wftvaccountnumber.zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wftvaccountnumberprocessEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="wftvaccountnumber.zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wftvaccountnumberprocessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產品處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#dealdeptno').val(),'pcchargeno','pcchargename',$('#dealnofactoryid').val(),'wftvaccountnumber')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="wftvaccountnumber.pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${wftvaccountnumberprocessEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="wftvaccountnumber.pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wftvaccountnumberprocessEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zakchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">資安課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(153,'zakchargeTable','zakchargeno','zakchargename',$('#dealfactoryid').combobox('getValue'),'wftvaccountnumber')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zakchargeno" name="wftvaccountnumber.zakchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zakchargeno']}"
                                                               readonly
                                                               value="${wftvaccountnumberprocessEntity.zakchargeno }"/><c:if
                                                            test="${requiredMap['zakchargeno'].equals('true')}">
                                                        <span id="zakredDiv"><font color="red">*</font></span></c:if>
                                                        /<input id="zakchargename" name="wftvaccountnumber.zakchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zakchargeno']}"
                                                                value="${wftvaccountnumberprocessEntity.zakchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zabchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">資安部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(154,'zabchargeTable','zabchargeno','zabchargename',$('#dealfactoryid').combobox('getValue'),'wftvaccountnumber')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zabchargeno" name="wftvaccountnumber.zabchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zabchargeno']}"
                                                               readonly
                                                               value="${wftvaccountnumberprocessEntity.zabchargeno }"/><c:if
                                                            test="${requiredMap['zabchargeno'].equals('true')}">
                                                        <span id="zabredDiv"><font color="red">*</font></span></c:if>
                                                        /<input id="zabchargename" name="wftvaccountnumber.zabchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zabchargeno']}"
                                                                value="${wftvaccountnumberprocessEntity.zabchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cackchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產品安全管理處窗口
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(264,'cackchargeTable','cackchargeno','cackchargename',$('#dealfactoryid').combobox('getValue'),'wftvaccountnumber')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cackchargeno" name="wftvaccountnumber.cackchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cackchargeno']}"
                                                               readonly
                                                               value="${wftvaccountnumberprocessEntity.cackchargeno }"/><c:if
                                                            test="${requiredMap['cackchargeno'].equals('true')}">
                                                        <span id="cackredDiv"><font color="red">*</font></span></c:if>
                                                        /<input id="cackchargename" name="wftvaccountnumber.cackchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cackchargeno']}"
                                                                value="${wftvaccountnumberprocessEntity.cackchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cakchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    產品安全管理處課級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(265,'cakchargeTable','cakchargeno','cakchargename',$('#dealfactoryid').combobox('getValue'),'wftvaccountnumber')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cakchargeno" name="wftvaccountnumber.cakchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cakchargeno']}"
                                                               readonly
                                                               value="${wftvaccountnumberprocessEntity.cakchargeno }"/><c:if
                                                            test="${requiredMap['cakchargeno'].equals('true')}">
                                                        <span id="cakredDiv"><font color="red">*</font></span></c:if>
                                                        /<input id="cakchargename" name="wftvaccountnumber.cakchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cakchargeno']}"
                                                                value="${wftvaccountnumberprocessEntity.cakchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cabchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    產品安全管理處部級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(266,'cabchargeTable','cabchargeno','cabchargename',$('#dealfactoryid').combobox('getValue'),'wftvaccountnumber')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cabchargeno" name="wftvaccountnumber.cabchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cabchargeno']}"
                                                               readonly
                                                               value="${wftvaccountnumberprocessEntity.cabchargeno }"/><c:if
                                                            test="${requiredMap['cabchargeno'].equals('true')}">
                                                        <span id="cabredDiv"><font color="red">*</font></span></c:if>
                                                        /<input id="cabchargename" name="wftvaccountnumber.cabchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cabchargeno']}"
                                                                value="${wftvaccountnumberprocessEntity.cabchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="cacchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    產品安全管理處廠級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(267,'cacchargeTable','cacchargeno','cacchargename',$('#dealfactoryid').combobox('getValue'),'wftvaccountnumber')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cacchargeno" name="wftvaccountnumber.cacchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cacchargeno']}"
                                                               readonly
                                                               value="${wftvaccountnumberprocessEntity.cacchargeno }"/><c:if
                                                            test="${requiredMap['cacchargeno'].equals('true')}">
                                                        <span id="cacredDiv"><font color="red">*</font></span></c:if>
                                                        /<input id="cacchargename" name="wftvaccountnumber.cacchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cacchargeno']}"
                                                                value="${wftvaccountnumberprocessEntity.cacchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="tvchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">TV管理員作業</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(268,'tvchargeTable','tvchargeno','tvchargename',$('#dealfactoryid').combobox('getValue'),'wftvaccountnumber')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="tvchargeno" name="wftvaccountnumber.tvchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['tvchargeno']}"
                                                               readonly
                                                               value="${wftvaccountnumberprocessEntity.tvchargeno }"/><c:if
                                                            test="${requiredMap['tvchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="tvchargename" name="wftvaccountnumber.tvchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['tvchargeno']}"
                                                                value="${wftvaccountnumberprocessEntity.tvchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wftvaccountnumberprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;"
                                   onclick="canelTask('${wftvaccountnumberprocessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <input type="hidden" id="onlyKchargeSignle" value="1" />
    <div id="win"></div>
    <div id="dlg"></div>
</form>
</div>
<script src='${ctx}/static/js/safety/wftvaccountnumberprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>