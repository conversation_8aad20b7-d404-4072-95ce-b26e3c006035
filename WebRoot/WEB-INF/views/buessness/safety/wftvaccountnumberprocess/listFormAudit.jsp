<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>TrustView系統賬號申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wftvaccountnumberprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wftvaccountnumberprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wftvaccountnumberprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">TrustView系統賬號申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wftvaccountnumberprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wftvaccountnumberprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wftvaccountnumberprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wftvaccountnumberprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wftvaccountnumberprocessEntity.makerno}/${wftvaccountnumberprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">承辦人工號</td>
                            <td width="10%" class="td_style2">${wftvaccountnumberprocessEntity.dealno}</td>
                            <td width="8%">承辦人</td>
                            <td width="10%" class="td_style2">${wftvaccountnumberprocessEntity.dealname}</td>
                            <td width="8%">單位代碼</td>
                            <td width="10%" class="td_style2">${wftvaccountnumberprocessEntity.dealdeptno}</td>
                            <td width="8%">費用代碼</td>
                            <td width="10%" class="td_style2">${wftvaccountnumberprocessEntity.dealcostno}</td>
                            <td width="8%">廠區</td>
                            <td width="15%" class="td_style2">${wftvaccountnumberprocessEntity.dealfactoryname}
                            </td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱</td>
                            <td colspan="5" class="td_style2">${wftvaccountnumberprocessEntity.dealdeptname}</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wftvaccountnumberprocessEntity.dealemail}</td>
                        </tr>
                        <tr align="center">
                            <td>聯繫方式</td>
                            <td class="td_style2">${wftvaccountnumberprocessEntity.dealtel}</td>
                            <td>申請類型</td>
                            <td colspan="3" class="td_style2">${wftvaccountnumberprocessEntity.applytypename}</td>
                            <td>使用區域</td>
                            <td colspan="3" class="td_style2">
                                <c:if test="${wftvaccountnumberprocessEntity.applysectype=='0'}">特保區</c:if>
                                <c:if test="${wftvaccountnumberprocessEntity.applysectype=='1'}">非特保區</c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申请人信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="wftvaccountnumberitemsTable" width="120%">
                                        <tr align="center">
                                            <td width="30px">序號</td>
                                            <td width="110px">工號</td>
                                            <td width="110px">姓名</td>
                                            <td width="120px">廠區</td>
                                            <td width="120px">單位代碼</td>
                                            <td width="200px">單位</td>
                                            <td width="120px">使用區域</td>
                                            <td width="120px">使用樓層</td>
                                            <td width="130px">電腦IP</td>
                                            <td width="110px">是否NPI</td>
                                            <c:if test="${wftvaccountnumberprocessEntity.applysectype=='0'}">
                                                <td width="110px">是否安裝<br/>指紋儀</td>
                                            </c:if>
                                            <td width="130px">分機</td>
                                            <td width="90px">外发权限</td>
                                            <td width="380px">備註</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wftvaccountnumberitems!=null&&wftvaccountnumberitems.size()>0}">
                                            <c:forEach items="${wftvaccountnumberitems}" var="tvaccountnumberitems" varStatus="status">
                                                <tr align="center" id="tvaccountnumberitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>${tvaccountnumberitems.applyno}</td>
                                                    <td>${tvaccountnumberitems.applyname}</td>
                                                    <td>${tvaccountnumberitems.applyfactoryname}</td>
                                                    <td>${tvaccountnumberitems.applydeptno}</td>
                                                    <td>${tvaccountnumberitems.applydeptname}</td>
                                                    <td>${tvaccountnumberitems.applyarea}</td>
                                                    <td>${tvaccountnumberitems.applybuilding}</td>
                                                    <td>${tvaccountnumberitems.applyip}</td>
                                                    <td>${tvaccountnumberitems.applynpi}</td>
                                                    <c:if test="${wftvaccountnumberprocessEntity.applysectype=='0'}">
                                                        <td>${tvaccountnumberitems.applyfingerprint}</td>
                                                    </c:if>
                                                    <td>${tvaccountnumberitems.applytel}</td>
                                                    <td>${tvaccountnumberitems.applyout}</td>
                                                    <td>${tvaccountnumberitems.applyremarks}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="100px">需求說明</td>
                            <td width="90%" class="td_style2">
                            <textarea id="applyreason" name="wftvaccountnumber.applyreason"
                                      class="easyui-validatebox"
                                      maxlength="300"
                                      style="width:99%;height:80px;"
                                      rows="5" cols="6">${wftvaccountnumberprocessEntity.applyreason }</textarea></span>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="100px">批註</td>
                            <td width="90%" class="td_style2">
						        <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wftvaccountnumberprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','TrustView系統賬號申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wftvaccountnumberprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/safety/wftvaccountnumberprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>