<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>安防服務需求申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfsecurityservicesprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfsecurityservicesprocessEntity.id }"/>
    <input id="serialno" name="wfsecurityservicesprocess.serialno" type="hidden" value="${wfsecurityservicesprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">安防服務需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfsecurityservicesprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfsecurityservicesprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfsecurityservicesprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfsecurityservicesprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfsecurityservicesprocessEntity.makerno}/${wfsecurityservicesprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="5%">申請人工號</td>
                            <td width="6%" class="td_style2">${wfsecurityservicesprocessEntity.applyno}</td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style2">${wfsecurityservicesprocessEntity.applyname}</td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style2">${wfsecurityservicesprocessEntity.applydeptno}</td>
                            <td width="3%">費用代碼</td>
                            <td width="6%" class="td_style2">${wfsecurityservicesprocessEntity.applycostno}</td>
                            <td width="4%">廠區</td>
                            <td width="7%" class="td_style2">${wfsecurityservicesprocessEntity.applyfactoryname}</td>
                        </tr>
                        <tr align="center">
                            <td>申請單位</td>
                            <td colspan="4" class="td_style2">${wfsecurityservicesprocessEntity.applydeptname }</td>
                            <td>法人</td>
                            <td colspan="2" class="td_style2">${wfsecurityservicesprocessEntity.corporatename }</td>
                            <td>區域</td>
                            <td class="td_style2">${wfsecurityservicesprocessEntity.applyareaname}/${wfsecurityservicesprocessEntity.applybuildingname}</td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機</td>
                            <td class="td_style2">${wfsecurityservicesprocessEntity.applytel}</td>
                            <td>聯繫郵箱</td>
                            <td colspan="7" class="td_style2">${wfsecurityservicesprocessEntity.applyemail}</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請內容</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <table id="wfsecurityapplyitemsTable" width="100%">
                                    <tr align="center">
                                        <td width="5%">序號</td>
                                        <td width="25%">設備類別</td>
                                        <td width="20%">需求類別</td>
                                        <td width="25%">需求地點</td>
                                        <td width="20%">需求數量</td>
                                    </tr>
                                    <tbody id="info_Body0">
                                    <c:if test="${wfsecurityapplyitems!=null&&wfsecurityapplyitems.size()>0}">
                                        <c:forEach items="${wfsecurityapplyitems}" var="securityapplyitems" varStatus="status">
                                            <tr align="center" id="securityapplyitems${status.index+1}">
                                                <td>${status.index+1}</td>
                                                <td>
                                                    <c:if test="${securityapplyitems.mactypename=='其他'}">
                                                        ${securityapplyitems.mactypename}/${securityapplyitems.mactypeother}
                                                    </c:if>
                                                    <c:if test="${securityapplyitems.mactypename!='其他'}">
                                                        ${securityapplyitems.mactypename}
                                                    </c:if>
                                                </td>
                                                <td>${securityapplyitems.applytypename}</td>
                                                <td>${securityapplyitems.applyaddress}</td>
                                                <td>${securityapplyitems.applynumber}</td>
                                            </tr>
                                        </c:forEach>
                                    </c:if>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="100px">需求說明</td>
                            <td width="1000px" class="td_style2">
                                    <textarea id="description" name="wfsecurityservicesprocess.description"
                                              onpropertychange="return LessThanAuto(this,'txtNum');"
                                              maxlength="500" class="easyui-validatebox" style="width:99%;height:60px;"
                                              rows="5" cols="3">${wfsecurityservicesprocessEntity.description}</textarea>
                            </td>
                        </tr>
                        <c:if test="${wfsecurityservicesprocessEntity.isold!=null}">
                            <tr align="center">
                                <td colspan="10" class="td_style1">資訊評估項目</td>
                            </tr>
                            <tr align="center">
                                <td width="100px">是否利舊設備材料</td>
                                <td width="1000px" class="td_style2">
                                    <c:if test="${wfsecurityservicesprocessEntity.isold=='1'}">是</c:if>
                                    <c:if test="${wfsecurityservicesprocessEntity.isold=='0'}">否</c:if>
                                </td>
                            </tr>
                            <c:if test="${wfsecurityservicesprocessEntity.isold=='1'}">
                                <c:if test="${wfsecuritymacitems!=null&&wfsecuritymacitems.size()>0}">
                                    <tr align="center">
                                        <td colspan="10" width="100%">
                                            <table width="100%">
                                                <tr align="center">
                                                    <td width="5%">序號</td>
                                                    <td width="20%">設備材料名稱</td>
                                                    <td width="10%">品牌</td>
                                                    <td width="20%">規格型號</td>
                                                    <td width="10%">單價</td>
                                                    <td width="10%">單位</td>
                                                    <td width="10%">數量</td>
                                                    <td width="10%">合計</td>
                                                </tr>
                                                <c:forEach items="${wfsecuritymacitems}" var="securitymacitems" varStatus="status">
                                                    <tr align="center" id="securitymacitems${status.index+1}">
                                                        <td>${status.index+1}</td>
                                                        <td>${securitymacitems.macname}</td>
                                                        <td>${securitymacitems.macbrand}</td>
                                                        <td>${securitymacitems.macspecific}</td>
                                                        <td>${securitymacitems.macprice}</td>
                                                        <td>${securitymacitems.macunit}</td>
                                                        <td>${securitymacitems.macnumber}</td>
                                                        <td>${securitymacitems.mactotal}</td>
                                                    </tr>
                                                </c:forEach>
                                                <tr style="text-align: center;">
                                                    <td colspan="2" >費用合計:</td>
                                                    <td colspan="7">
                                                            ${wfsecurityservicesprocessEntity.totalcost}（RMB）
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </c:if>
                            </c:if>
                            <tr align="center">
                                <td width="10%">資訊可行性評估</td>
                                <td width="90%" class="td_style2">
                                <textarea name="wfsecurityservicesprocess.ywassess"
                                          maxlength="100" class="easyui-validatebox" style="width:99%;height:60px;"
                                          rows="5" cols="3">${wfsecurityservicesprocessEntity.ywassess}</textarea>
                                </td>
                            </tr>
                        </c:if>
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%"  class="td_style2">
                                <input type="hidden" id="attachids" name="wfsecurityservicesprocess.attachids" value="${wfsecurityservicesprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','安防服務申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfsecurityservicesprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfsecurityservicesprocessEntity.workstatus!=null&&wfsecurityservicesprocessEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<input id="loadOrNot" type="hidden" value="0"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/safety/wfsecurityservicesprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>