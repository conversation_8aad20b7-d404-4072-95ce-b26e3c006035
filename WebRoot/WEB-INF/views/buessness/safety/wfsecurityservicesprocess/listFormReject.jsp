<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>安防服務需求申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfsecurityservicesprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfsecurityservicesprocessEntity.id }"/>
    <input id="serialno" name="wfsecurityservicesprocess.serialno" type="hidden" value="${wfsecurityservicesprocessEntity.serialno }"/>
    <input id="makerno" name="wfsecurityservicesprocess.makerno" type="hidden" value="${wfsecurityservicesprocessEntity.makerno }"/>
    <input id="makername" name="wfsecurityservicesprocess.makername" type="hidden" value="${wfsecurityservicesprocessEntity.makername }"/>
    <input id="makerdeptno" name="wfsecurityservicesprocess.makerdeptno" type="hidden" value="${wfsecurityservicesprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfsecurityservicesprocess.makerfactoryid" type="hidden" value="${wfsecurityservicesprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">安防服務需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfsecurityservicesprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfsecurityservicesprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfsecurityservicesprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfsecurityservicesprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfsecurityservicesprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfsecurityservicesprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfsecurityservicesprocessEntity.makerno}/${wfsecurityservicesprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="5%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="wfsecurityservicesprocess.applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfsecurityservicesprocessEntity.applyno}" onblur="queryUserInfo('apply');"/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="wfsecurityservicesprocess.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfsecurityservicesprocessEntity.applyname}"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="wfsecurityservicesprocess.applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfsecurityservicesprocessEntity.applydeptno}"/>
                            </td>
                            <td width="3%">費用代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applycostno" name="wfsecurityservicesprocess.applycostno" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfsecurityservicesprocessEntity.applycostno}"/>
                            </td>
                            <td width="4%">廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="wfsecurityservicesprocess.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfsecurityservicesprocessEntity.applyfactoryid}"
                                       data-options="width: 120,validType:'comboxValidate[\'applyfactoryid\',\'请選擇所在廠區\']',onSelect:function(){onchangeFactory('apply');}"/>
                                <input id="applynofactoryid" name="wfsecurityservicesprocess.applynofactoryid" type="hidden" value="${wfsecurityservicesprocessEntity.applynofactoryid}"/>
                                <input id="applyfactoryname" name="wfsecurityservicesprocess.applyfactoryname" type="hidden" value="${wfsecurityservicesprocessEntity.applyfactoryname}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>申請單位&nbsp;<font color="red">*</font></td>
                            <td colspan="4" class="td_style1">
                                <input id="applydeptname" name="wfsecurityservicesprocess.applydeptname" class="easyui-validatebox" data-options="width: 380,required:true"
                                       value="${wfsecurityservicesprocessEntity.applydeptname }"/>
                            </td>
                            <td>法人&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1">
                                <input id="corporateid" name="wfsecurityservicesprocess.corporateid"
                                       class="easyui-combobox" data-options="width: 220,required:true,validType:'comboxValidate[\'corporateid\',\'请選擇法人\']',onSelect:function(){onchangeCorporate();}"
                                       panelHeight="auto" editable="false"
                                       value="${wfsecurityservicesprocessEntity.corporateid }"/>
                                <input id="corporatename" name="wfsecurityservicesprocess.corporatename" type="hidden" value="${wfsecurityservicesprocessEntity.corporatename }"/>
                            </td>
                            <td>區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyarea" name="wfsecurityservicesprocess.applyarea" class="easyui-combobox" data-options="width: 70,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea('apply');}"
                                       value="${wfsecurityservicesprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="wfsecurityservicesprocess.applybuilding" class="easyui-combobox" data-options="width: 70,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']',onSelect:function(){onchangeBuilding('apply');}"
                                       value="${wfsecurityservicesprocessEntity.applybuilding }" panelHeight="auto"/>
                                <input id="applyareaname" name="wfsecurityservicesprocess.applyareaname" type="hidden" value="${wfsecurityservicesprocessEntity.applyareaname}"/>
                                <input id="applybuildingname" name="wfsecurityservicesprocess.applybuildingname" type="hidden" value="${wfsecurityservicesprocessEntity.applybuildingname}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applytel" name="wfsecurityservicesprocess.applytel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfsecurityservicesprocessEntity.applytel}" data-options="required:true,prompt:'579+66666'"
                                       onblur="validApplyTel('applytel')"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="7" class="td_style1">
                                <input id="applyemail" name="wfsecurityservicesprocess.applyemail"
                                       class="easyui-validatebox"
                                       value="${wfsecurityservicesprocessEntity.applyemail}" style="width:300px;"
                                       data-options="required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>

                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請內容</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="wfsecurityapplyitemsTable" width="100%">
                                        <tr align="center">
                                            <td width="5%">序號</td>
                                            <td width="25%">設備類別&nbsp;<font color="red">*</font></td>
                                            <td width="20%">需求類別&nbsp;<font color="red">*</font></td>
                                            <td width="25%">需求地點&nbsp;<font color="red">*</font></td>
                                            <td width="20%">需求數量&nbsp;<font color="red">*</font></td>
                                            <td width="5%">操作</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wfsecurityapplyitems!=null&&wfsecurityapplyitems.size()>0}">
                                            <c:forEach items="${wfsecurityapplyitems}" var="securityapplyitems" varStatus="status">
                                                <tr align="center" id="securityapplyitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <input id="mactype${status.index+1}" name="wfsecurityapplyitems[${status.index+1}].mactype" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadMacType(${status.index+1});},onSelect:function(){onchangeMacType(${status.index+1});}" style="width:100px;"
                                                               class="easyui-combobox" editable="false" value="${securityapplyitems.mactype}"/>
                                                        <input id="mactypename${status.index+1}" name="wfsecurityapplyitems[${status.index+1}].mactypename" type="hidden" value="${securityapplyitems.mactypename}"/>
                                                        <input id="mactypeother${status.index+1}" name="wfsecurityapplyitems[${status.index+1}].mactypeother" class="easyui-validatebox" data-options="width: 120" readonly="readonly" value="${securityapplyitems.mactypeother}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applytype${status.index+1}" name="wfsecurityapplyitems[${status.index+1}].applytype" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadApplyType(${status.index+1});},onSelect:function(){onchangeApplyType(${status.index+1});}" style="width:100px;"
                                                               class="easyui-combobox" editable="false" value="${securityapplyitems.applytype}"/>
                                                        <input id="applytypename${status.index+1}" name="wfsecurityapplyitems[${status.index+1}].applytypename" type="hidden" value="${securityapplyitems.applytypename}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyaddress${status.index+1}"
                                                               name="wfsecurityapplyitems[${status.index+1}].applyaddress" class="easyui-validatebox" style="width:200px;"
                                                               data-options="required:true" value="${securityapplyitems.applyaddress}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applynumber${status.index+1}"
                                                               name="wfsecurityapplyitems[${status.index+1}].applynumber" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${securityapplyitems.applynumber}"/>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="securityapplydeltr(${status.index+1});return false;"/>
                                                        <input id="shunxu${status.index+1}" type="hidden" name="wfsecurityapplyitems[${status.index+1}].shunxu" value="${status.index+1}"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${wfsecurityapplyitems.size()==0 || wfsecurityapplyitems==null}">
                                            <tr align="center" id="securityapplyitems1">
                                                <td>1</td>
                                                <td>
                                                    <input id="mactype1" name="wfsecurityapplyitems[1].mactype" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadMacType(1);},onSelect:function(){onchangeMacType(1);}" style="width:100px;"
                                                           class="easyui-combobox" editable="false" value=""/>
                                                    <input id="mactypename1" name="wfsecurityapplyitems[1].mactypename" type="hidden" value=""/>
                                                    <input id="mactypeother1" name="wfsecurityapplyitems[1].mactypeother" class="easyui-validatebox" data-options="width: 120" readonly="readonly" value=""/>

                                                </td>
                                                <td>
                                                    <input id="applytype1" name="wfsecurityapplyitems[1].applytype" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadApplyType(1);},onSelect:function(){onchangeApplyType(1);}" style="width:100px;"
                                                           class="easyui-combobox" editable="false" value=""/>
                                                    <input id="applytypename1" name="wfsecurityapplyitems[1].applytypename" type="hidden" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyaddress1"
                                                           name="wfsecurityapplyitems[1].applyaddress" class="easyui-validatebox" style="width:200px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applynumber1"
                                                           name="wfsecurityapplyitems[1].applynumber" class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="securityapplydeltr(1);return false;"/>
                                                    <input id="shunxu1" type="hidden" name="wfsecurityapplyitems[1].shunxu" value="1"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr0">
                                            <td colspan="9" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="securityapplyItemAdd" style="width:150px;float:left;" value="添加一行"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>

                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">需求說明&nbsp;<font color="red">*</font></td>
                            <td width="90%" class="td_style2">
                                    <textarea id="description" name="wfsecurityservicesprocess.description"
                                              oninput="return LessThanAuto(this,'txtNum');"
                                              onchange="return LessThanAuto(this,'txtNum');"
                                              onpropertychange="return LessThanAuto(this,'txtNum');"
                                              maxlength="100" class="easyui-validatebox" style="width:99%;height:60px;"
                                              data-options="required:true"
                                              rows="5"
                                              cols="3">${wfsecurityservicesprocessEntity.description}</textarea><span
                                    id="txtNum"></span>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">附件&nbsp;<font color="red">*</font></td>
                            <td width="90%" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <c:choose>
                                    <c:when test="${file.size()>0}">
                                        <input type="hidden" id="attachids" name="wfsecurityservicesprocess.attachids"
                                               value="${wfsecurityservicesprocessEntity.attachids }"/>
                                    </c:when>
                                    <c:otherwise>
                                        <input type="hidden" id="attachids" name="wfsecurityservicesprocess.attachids" value=""/>
                                    </c:otherwise>
                                </c:choose>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="9" class="td_style2">
                                1.此表單暫僅適用于監控﹑門禁﹑報警器、考勤類服務需求；<br/>
                                2.此表僅作為監控門磁類低值易耗品及門禁考勤類卡機提交請購需求時的依據，簽核完成后請將此表單列印保存，并在費用申請系統中將此表單作為附件上傳﹐供各級主管審核時評估參考；<br/>
                                3.安防需求須將詳細安裝位置之Layout圖電子檔﹑簽核檔與本表單一并提交至資訊；<br/>
                                4.需求申請單須需求單位處級主管及經管主管核准；<br/>
                                5.產品安全相關需求會簽產品安全，考勤類需求需會簽人資；<br/>
                                6.工程開展期間需求單位須安排人員協助配合﹐以便于工程正常進行；<br/>
                                7.安防設施建置完成后，使用單位應設專人負責日常點檢，發現異常及時聯繫資訊處理。
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','安防服務申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zxywkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zxywkchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'zxywkchargeTable','zxywkchargeno','zxywkchargename',$('#applyfactoryid').combobox('getValue'),'wfsecurityservicesprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxywkchargeno" name="wfsecurityservicesprocess.zxywkchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxywkchargeno']}"
                                                               readonly
                                                               value="${wfsecurityservicesprocessEntity.zxywkchargeno }"/><c:if
                                                            test="${requiredMap['zxywkchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxywkchargename" name="wfsecurityservicesprocess.zxywkchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxywkchargeno']}"
                                                                value="${wfsecurityservicesprocessEntity.zxywkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole18('cchargeTable',$('#applydeptno').val(),'cchargeno','cchargename',$('#applynofactoryid').val(),'wfsecurityservicesprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wfsecurityservicesprocess.cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfsecurityservicesprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename"
                                                                name="wfsecurityservicesprocess.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfsecurityservicesprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hqchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">${requiredMap['hqchargeno_name']}
                                                                    <a href="#"
                                                                       onclick="addHq('wfsecurityservicesprocess','hqcharge');">添加一位</a>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hqchargeno" onblur="gethqUserNameByEmpno(this,'hqcharge');" name="wfsecurityservicesprocess.hqchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hqchargeno']}"
                                                               value="${wfsecurityservicesprocessEntity.hqchargeno }"/><c:if
                                                            test="${requiredMap['hqchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hqchargename" name="wfsecurityservicesprocess.hqchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hqchargeno']}"
                                                                value="${wfsecurityservicesprocessEntity.hqchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="jgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['jgchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(62,'jgchargeTable','jgchargeno','jgchargename',$('#applyfactoryid').combobox('getValue'),'wfsecurityservicesprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="jgchargeno" name="wfsecurityservicesprocess.jgchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['jgchargeno']}"
                                                               readonly
                                                               value="${wfsecurityservicesprocessEntity.jgchargeno }"/><c:if
                                                            test="${requiredMap['jgchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="jgchargename" name="wfsecurityservicesprocess.jgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['jgchargeno']}"
                                                                value="${wfsecurityservicesprocessEntity.jgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applynofactoryid').val(),'wfsecurityservicesprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wfsecurityservicesprocess.zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfsecurityservicesprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="wfsecurityservicesprocess.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfsecurityservicesprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#applydeptno').val(),'zcchargeno','zcchargename',$('#applynofactoryid').val(),'wfsecurityservicesprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="wfsecurityservicesprocess.zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wfsecurityservicesprocessEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="wfsecurityservicesprocess.zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfsecurityservicesprocessEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zxywbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zxywbchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(25,'zxywbchargeTable','zxywbchargeno','zxywbchargename',$('#applyfactoryid').combobox('getValue'),'wfsecurityservicesprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxywbchargeno" name="wfsecurityservicesprocess.zxywbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxywbchargeno']}"
                                                               readonly
                                                               value="${wfsecurityservicesprocessEntity.zxywbchargeno }"/><c:if
                                                            test="${requiredMap['zxywbchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxywbchargename" name="wfsecurityservicesprocess.zxywbchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxywbchargeno']}"
                                                                value="${wfsecurityservicesprocessEntity.zxywbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#applydeptno').val(),'pcchargeno','pcchargename',$('#applynofactoryid').val(),'wfsecurityservicesprocess')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno"
                                                               name="wfsecurityservicesprocess.pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${wfsecurityservicesprocessEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename"
                                                                name="wfsecurityservicesprocess.pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wfsecurityservicesprocessEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="ywzchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['ywzchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole5(26,'ywzchargeno','ywzchargename','','','','',$('#applyfactoryid').combobox('getValue'),$('#applyarea').combobox('getValue'),$('#applybuilding').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ywzchargeno"
                                                               name="wfsecurityservicesprocess.ywzchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ywzchargeno']}"
                                                               readonly
                                                               value="${wfsecurityservicesprocessEntity.ywzchargeno }"/><c:if
                                                            test="${requiredMap['ywzchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ywzchargename" name="wfsecurityservicesprocess.ywzchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ywzchargeno']}"
                                                                value="${wfsecurityservicesprocessEntity.ywzchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfsecurityservicesprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;"
                                   onclick="canelTask('${wfsecurityservicesprocessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="areaId" name="areaId" value="" />
    <input type="hidden" id="buildingId" name="buildingId" value="" />
    <input id="disOrEnabled" type="hidden" value=""/>
    <input id="loadOrNot" type="hidden" value="1"/>
    <input type="hidden" id="onlyKchargeSignle" value="1" />
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
    <div id="dlg"></div>
</form>
</div>
<script src='${ctx}/static/js/safety/wfsecurityservicesprocess.js?random=<%= Math.random()%>'></script>
<script type="text/javascript">
    if ("${wfsecurityservicesprocessEntity.hqchargeno}" != "") {
        var nostr = "${wfsecurityservicesprocessEntity.hqchargeno}";
        var namestr = "${wfsecurityservicesprocessEntity.hqchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hqchargeTable tr:eq(" + (i + 2) + ")").find("#hqchargeno").val(notr[i]);
            $("#hqchargeTable tr:eq(" + (i + 2) + ")").find("#hqchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hqchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hqchargeno' name='wfsecurityservicesprocess.hqchargeno' style='width: 80px' value='' onblur='gethqUserNameByEmpno(this,'hqcharge');'/>/<input id='hqchargename' name='wfsecurityservicesprocess.hqchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
</script>
</body>
</html>
