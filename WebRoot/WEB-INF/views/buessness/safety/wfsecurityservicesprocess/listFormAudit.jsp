<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>安防服務需求申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
    <script src='${ctx}/static/js/safety/wfsecurityservicesprocess.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfsecurityservicesprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfsecurityservicesprocessEntity.id }"/>
    <input id="serialno" name="wfsecurityservicesprocess.serialno" type="hidden" value="${wfsecurityservicesprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">安防服務需求申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfsecurityservicesprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfsecurityservicesprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfsecurityservicesprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfsecurityservicesprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfsecurityservicesprocessEntity.makerno}/${wfsecurityservicesprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="5%">申請人工號</td>
                            <td width="6%" class="td_style2">${wfsecurityservicesprocessEntity.applyno}</td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style2">${wfsecurityservicesprocessEntity.applyname}</td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style2">${wfsecurityservicesprocessEntity.applydeptno}</td>
                            <td width="3%">費用代碼</td>
                            <td width="6%" class="td_style2">${wfsecurityservicesprocessEntity.applycostno}</td>
                            <td width="4%">廠區</td>
                            <td width="7%" class="td_style2">${wfsecurityservicesprocessEntity.applyfactoryname}</td>
                        </tr>
                        <tr align="center">
                            <td>申請單位</td>
                            <td colspan="4" class="td_style2">${wfsecurityservicesprocessEntity.applydeptname }</td>
                            <td>法人</td>
                            <td colspan="2" class="td_style2">${wfsecurityservicesprocessEntity.corporatename }</td>
                            <td>區域</td>
                            <td class="td_style2">${wfsecurityservicesprocessEntity.applyareaname}/${wfsecurityservicesprocessEntity.applybuildingname}</td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機</td>
                            <td class="td_style2">${wfsecurityservicesprocessEntity.applytel}</td>
                            <td>聯繫郵箱</td>
                            <td colspan="7" class="td_style2">${wfsecurityservicesprocessEntity.applyemail}</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請內容</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="wfsecurityapplyitemsTable" width="100%">
                                        <tr align="center">
                                            <td width="5%">序號</td>
                                            <td width="25%">設備類別</td>
                                            <td width="20%">需求類別</td>
                                            <td width="25%">需求地點</td>
                                            <td width="20%">需求數量</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wfsecurityapplyitems!=null&&wfsecurityapplyitems.size()>0}">
                                            <c:forEach items="${wfsecurityapplyitems}" var="securityapplyitems" varStatus="status">
                                                <tr align="center" id="securityapplyitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <c:if test="${securityapplyitems.mactypename=='其他'}">
                                                            ${securityapplyitems.mactypename}/${securityapplyitems.mactypeother}
                                                        </c:if>
                                                        <c:if test="${securityapplyitems.mactypename!='其他'}">
                                                            ${securityapplyitems.mactypename}
                                                        </c:if>
                                                    </td>
                                                    <td>${securityapplyitems.applytypename}</td>
                                                    <td>${securityapplyitems.applyaddress}</td>
                                                    <td>${securityapplyitems.applynumber}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">需求說明</td>
                            <td width="90%" class="td_style2">
                                    <textarea id="description" name="wfsecurityservicesprocess.description"
                                              onpropertychange="return LessThanAuto(this,'txtNum');"
                                              maxlength="500" class="easyui-validatebox" style="width:99%;height:60px;"
                                              rows="5" cols="3">${wfsecurityservicesprocessEntity.description}</textarea>
                            </td>
                        </tr>
                        <c:choose>
                            <c:when test="${not empty nodeName&&'資訊運維課級主管' eq nodeName}">
                                <tr>
                                    <td colspan="10" class="td_style1">資訊評估項目</td>
                                </tr>
                                <tr align="center">
                                    <td width="10%">是否利舊設備材料</td>
                                    <td width="90%" class="td_style2">
                                        <div class="isoldDiv"></div>
                                        <input id="isold" name="wfsecurityservicesprocess.isold" type="hidden"
                                               value="${wfsecurityservicesprocessEntity.isold}"/>
                                    </td>
                                </tr>
                                <tr align="center" id="isoldthingsTr">
                                    <td colspan="10" width="100%">
                                        <div style="overflow-x: auto;width:1200px;">
                                            <table id="wfsecuritymacitemsTable" width="100%">
                                                <tr align="center">
                                                    <td width="5%">序號</td>
                                                    <td width="20%">設備材料名稱<font color="red">*</font></td>
                                                    <td width="10%">品牌<font color="red">*</font></td>
                                                    <td width="20%">規格型號<font color="red">*</font></td>
                                                    <td width="10%">單價<font color="red">*</font></td>
                                                    <td width="10%">單位<font color="red">*</font></td>
                                                    <td width="10%">數量<font color="red">*</font></td>
                                                    <td width="10%">合計<font color="red">*</font></td>
                                                    <td width="5%">操作</td>
                                                </tr>
                                                <tbody id="info_Body1">
                                                <c:if test="${wfsecuritymacitems!=null&&wfsecuritymacitems.size()>0}">
                                                    <c:forEach items="${wfsecuritymacitems}" var="securitymacitems" varStatus="status">
                                                        <tr align="center" id="securitymacitems${status.index+1}">
                                                            <td>${status.index+1}</td>
                                                            <td>
                                                                <input id="macname${status.index+1}" name="wfsecuritymacitems[${status.index+1}].macname"
                                                                       class="easyui-validatebox"  style="width:150px;" value=""/>
                                                            </td>
                                                            <td>
                                                                <input id="macbrand${status.index+1}" name="wfsecuritymacitems[${status.index+1}].macbrand"
                                                                       class="easyui-validatebox"  style="width:100px;" value=""/>
                                                            </td>
                                                            <td>
                                                                <input id="macspecific${status.index+1}" name="wfsecuritymacitems[${status.index+1}].macspecific"
                                                                       class="easyui-validatebox"  style="width:150px;" value=""/>
                                                            </td>
                                                            <td>
                                                                <input id="macprice${status.index+1}" name="wfsecuritymacitems[${status.index+1}].macprice"
                                                                       class="easyui-validatebox" onchange="countSellMoney(this)" style="width:100px;" value=""/>
                                                            </td>
                                                            <td>
                                                                <input id="macunit${status.index+1}" name="wfsecuritymacitems[${status.index+1}].macunit"
                                                                       class="easyui-validatebox"  style="width:100px;" value=""/>
                                                            </td>
                                                            <td>
                                                                <input id="macnumber${status.index+1}" name="wfsecuritymacitems[${status.index+1}].macnumber"
                                                                       class="easyui-validatebox" onchange="countSellMoney(this)"  style="width:100px;" value=""/>
                                                            </td>
                                                            <td>
                                                                <input id="mactotal${status.index+1}" name="wfsecuritymacitems[${status.index+1}].mactotal"
                                                                       class="easyui-validatebox" readonly  style="width:100px;" value=""/>
                                                            </td>
                                                            <td>
                                                                <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="olddeltr(${status.index+1});return false;"/>
                                                                <input id="shunxu${status.index+1}" type="hidden" name="wfsecuritymacitems[${status.index+1}].shunxu" value=""/>
                                                            </td>
                                                        </tr>
                                                    </c:forEach>
                                                </c:if>
                                                <c:if test="${wfsecuritymacitems.size()==0||wfsecuritymacitems==null}">
                                                    <tr align="center" id="securitymacitems1">
                                                        <td>1</td>
                                                        <td>
                                                            <input id="macname1" name="wfsecuritymacitems[1].macname"
                                                                   class="easyui-validatebox"  style="width:150px;" value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="macbrand1" name="wfsecuritymacitems[1].macbrand"
                                                                   class="easyui-validatebox"  style="width:100px;" value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="macspecific1" name="wfsecuritymacitems[1].macspecific"
                                                                   class="easyui-validatebox"  style="width:150px;" value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="macprice1" name="wfsecuritymacitems[1].macprice"
                                                                   class="easyui-validatebox"  onchange="countSellMoney(this)"   style="width:100px;" value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="macunit1" name="wfsecuritymacitems[1].macunit"
                                                                   class="easyui-validatebox"  style="width:100px;" value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="macnumber1" name="wfsecuritymacitems[1].macnumber"
                                                                   class="easyui-validatebox" onchange="countSellMoney(this)"   style="width:100px;" value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="mactotal1" name="wfsecuritymacitems[1].mactotal"
                                                                   class="easyui-validatebox" readonly  style="width:100px;" value=""/>
                                                        </td>
                                                        <td>
                                                            <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="olddeltr(1);return false;"/>
                                                            <input id="shunxu1" type="hidden" name="wfsecuritymacitems[1].shunxu" value="1"/>
                                                        </td>
                                                    </tr>
                                                </c:if>
                                                </tbody>
                                                <tr style="text-align: center">
                                                    <td colspan="2">費用合計</td>
                                                    <td colspan="7">
                                                        <input id="totalcost" name="wfsecurityservicesprocess.totalcost"
                                                               class="easyui-validatebox" data-options="width:150" readonly value="" />（RMB）
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
                                                        <input type="button" id="oldItemAdd" style="width:100px;float:left;" value="添加一筆"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td width="10%">資訊可行性評估<font color="red">*</font></td>
                                    <td width="90%" class="td_style2">
                                    <textarea id="ywassess" name="wfsecurityservicesprocess.ywassess"
                                              oninput="return LessThanAuto(this,'txtNum');"
                                              onchange="return LessThanAuto(this,'txtNum');"
                                              onpropertychange="return LessThanAuto(this,'txtNum');"
                                              maxlength="500" class="easyui-validatebox" style="width:99%;height:60px;"
                                              data-options="required:true"
                                              rows="5"
                                              cols="3">${wfsecurityservicesprocessEntity.ywassess}</textarea><span
                                            id="txtNum"></span>
                                    </td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <tr align="center">
                                    <td colspan="10" class="td_style1">資訊評估項目</td>
                                </tr>
                                <tr align="center">
                                    <td width="10%">是否利舊設備材料</td>
                                    <td width="90%" class="td_style2">
                                        <c:if test="${wfsecurityservicesprocessEntity.isold=='1'}">是</c:if>
                                        <c:if test="${wfsecurityservicesprocessEntity.isold=='0'}">否</c:if>
                                    </td>
                                </tr>
                                <c:if test="${wfsecurityservicesprocessEntity.isold=='1'}">
                                    <c:if test="${wfsecuritymacitems!=null&&wfsecuritymacitems.size()>0}">
                                        <tr align="center">
                                            <td colspan="10" width="100%">
                                                <div style="overflow-x: auto;width: 100%;">
                                                    <table width="100%">
                                                        <tr align="center">
                                                            <td width="5%">序號</td>
                                                            <td width="20%">設備材料名稱</td>
                                                            <td width="10%">品牌</td>
                                                            <td width="20%">規格型號</td>
                                                            <td width="10%">單價</td>
                                                            <td width="10%">單位</td>
                                                            <td width="10%">數量</td>
                                                            <td width="10%">合計</td>
                                                        </tr>
                                                        <c:forEach items="${wfsecuritymacitems}" var="securitymacitems" varStatus="status">
                                                            <tr align="center" id="securitymacitems${status.index+1}">
                                                                <td>${status.index+1}</td>
                                                                <td>${securitymacitems.macname}</td>
                                                                <td>${securitymacitems.macbrand}</td>
                                                                <td>${securitymacitems.macspecific}</td>
                                                                <td>${securitymacitems.macprice}</td>
                                                                <td>${securitymacitems.macunit}</td>
                                                                <td>${securitymacitems.macnumber}</td>
                                                                <td>${securitymacitems.mactotal}</td>
                                                            </tr>
                                                        </c:forEach>
                                                        <tr style="text-align: center">
                                                            <td colspan="2">費用合計:</td>
                                                            <td colspan="7">
                                                                    ${wfsecurityservicesprocessEntity.totalcost}（RMB）
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </td>
                                        </tr>
                                    </c:if>
                                </c:if>
                                <tr align="center">
                                    <td width="10%">資訊可行性評估</td>
                                    <td width="90%" class="td_style2">
                                    <textarea name="wfsecurityservicesprocess.ywassess"
                                              maxlength="500" class="easyui-validatebox" style="width:99%;height:60px;"
                                              rows="5" cols="3">${wfsecurityservicesprocessEntity.ywassess}</textarea>
                                    </td>
                                </tr>
                            </c:otherwise>
                        </c:choose>
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%"  class="td_style2">
                                <input type="hidden" id="attachids" name="wfsecurityservicesprocess.attachids" value="${wfsecurityservicesprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%"  class="td_style2">
						    <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                      style="width:1000px;height:60px;"
                                      rows="4" cols="4"></textarea>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <c:choose>
                            <c:when test="${not empty nodeName&&'資訊運維課級主管' eq nodeName}">
                                <tr align="center">
                                    <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                                    perCall="ywqupdate"
                                                    serialNo="${wfsecurityservicesprocessEntity.serialno}"></fox:action>
                                    </td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <tr align="center">
                                    <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                                    serialNo="${wfsecurityservicesprocessEntity.serialno}"></fox:action>
                                    </td>
                                </tr>
                            </c:otherwise>
                        </c:choose>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','安防服務申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfsecurityservicesprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<input type="hidden" id="nodeName"  value="${nodeName}"/>
<input id="loadOrNot" type="hidden" value="0"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>

</body>
</html>