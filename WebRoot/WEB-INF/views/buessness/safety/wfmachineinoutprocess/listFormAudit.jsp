<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>機台進出管制區申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfmachineinoutprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfmachineinoutprocessEntity.id }"/>
    <input id="serialno" name="wfmachineinout.serialno" type="hidden" value="${wfmachineinoutprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">機台進出管制區申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfmachineinoutprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfmachineinoutprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfmachineinoutprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfmachineinoutprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfmachineinoutprocessEntity.makerno}/${wfmachineinoutprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號</td>
                            <td width="6%" class="td_style2">${wfmachineinoutprocessEntity.applyno}</td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style2">${wfmachineinoutprocessEntity.applyname}</td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style2">${wfmachineinoutprocessEntity.applydeptno}</td>
                            <td width="3%">費用代碼</td>
                            <td width="6%" class="td_style2">${wfmachineinoutprocessEntity.applycostno}</td>
                            <td width="4%">所在廠區</td>
                            <td width="7%" class="td_style2">${wfmachineinoutprocessEntity.applyfactoryname}</td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style2">${wfmachineinoutprocessEntity.applyleveltype}</td>
                            <td>管理職</td>
                            <td class="td_style2">${wfmachineinoutprocessEntity.applymanager}</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfmachineinoutprocessEntity.applyemail}</td>
                            <td>使用區域</td>
                            <td class="td_style2">
                                ${wfmachineinoutprocessEntity.applyareaname }
                                ${wfmachineinoutprocessEntity.applybuildingname }
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機</td>
                            <td class="td_style2">${wfmachineinoutprocessEntity.applytel }</td>
                            <td>單位</td>
                            <td colspan="5" class="td_style2">${wfmachineinoutprocessEntity.applydeptname }</td>
                            <td>安保區域</td>
                            <td colspan="5" class="td_style2">
                                <c:if test="${wfmachineinoutprocessEntity.applysectype=='0'}">特保區</c:if>
                                <c:if test="${wfmachineinoutprocessEntity.applysectype=='1'}">非特保區</c:if>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">設備接收人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">設備接收人<br/>工號</td>
                            <td width="6%" class="td_style2">${wfmachineinoutprocessEntity.receiverno}</td>
                            <td width="4%">設備接收人</td>
                            <td width="6%" class="td_style2">${wfmachineinoutprocessEntity.receivername}</td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style2">${wfmachineinoutprocessEntity.receiverdeptno}</td>
                            <td width="3%">費用代碼</td>
                            <td width="6%" class="td_style2">${wfmachineinoutprocessEntity.receivercostno}</td>
                            <td width="4%">所在廠區</td>
                            <td width="7%" class="td_style2">${wfmachineinoutprocessEntity.receiverfactoryname}</td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style2">${wfmachineinoutprocessEntity.receiverleveltype}</td>
                            <td>管理職</td>
                            <td class="td_style2">${wfmachineinoutprocessEntity.receivermanager}</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfmachineinoutprocessEntity.receiveremail}</td>
                            <td>使用區域</td>
                            <td class="td_style2">
                                ${wfmachineinoutprocessEntity.receiverareaname }
                                ${wfmachineinoutprocessEntity.receiverbuildingname }
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機</td>
                            <td class="td_style2">${wfmachineinoutprocessEntity.receivertel}</td>
                            <td>單位</td>
                            <td colspan="5" class="td_style2">${wfmachineinoutprocessEntity.receiverdeptname}</td>
                            <td>安保區域</td>
                            <td colspan="5" class="td_style2">
                                <c:if test="${wfmachineinoutprocessEntity.receiversectype=='0'}">特保區</c:if>
                                <c:if test="${wfmachineinoutprocessEntity.receiversectype=='1'}">非特保區</c:if>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">設備明細</td>
                        </tr>
                        <c:choose>
                            <c:when test="${wfmachineinoutitems!=null&&wfmachineinoutitems.size()>0 &&(nodeOrder le 5)}">
                                <tr align="center">
                                    <td colspan="10" width="100%">
                                        <div style="overflow-x: auto;width: 1200px;">
                                            <table width="100%">
                                                <tr align="center">
                                                    <td width="30px">序號</td>
                                                    <td width="80px">品牌型號</td>
                                                    <td width="80px">品名</td>
                                                    <td width="80px">序列號/設備編號</td>
                                                    <td width="100px">設備來源</td>
                                                    <td width="50px">需求數量</td>
                                                    <td width="120px">需求說明</td>
                                                    <td width="120px">資安檢查項</td>
                                                </tr>
                                                <c:if test="${wfmachineinoutitems!=null&&wfmachineinoutitems.size()>0}">
                                                    <c:forEach items="${wfmachineinoutitems}" var="machineinoutitems" varStatus="status">
                                                        <tr align="center" id="machineinoutitems${status.index+1}">
                                                            <td>${status.index+1}</td>
                                                            <td>${machineinoutitems.brand}</td>
                                                            <td>${machineinoutitems.productname}</td>
                                                            <td>${machineinoutitems.serialnumber}</td>
                                                            <td>${machineinoutitems.sourcename}</td>
                                                            <td>${machineinoutitems.demandnumber}</td>
                                                            <td>${machineinoutitems.demanddesc}</td>
                                                            <td>${machineinoutitems.zacheckname}
                                                                <c:if test="${machineinoutitems.zacheckother!=''||machineinoutitems.zacheckother!=null}">${machineinoutitems.zacheckother}</c:if>
                                                            </td>
                                                        </tr>
                                                    </c:forEach>
                                                </c:if>
                                            </table>
                                        </div>
                                    </td>
                                </tr>
                            </c:when>
                            <c:when test="${not empty nodeName&&'IT生技現場管控確認' eq nodeName &&(nodeOrder eq 6)}">
                                <tr align="center">
                                    <td colspan="10" width="100%">
                                        <div style="overflow-x: auto;width: 1200px;">
                                            <table id="wfmachineinoutTable" width="100%">
                                                <tr align="center">
                                                    <td width="30px">序號</td>
                                                    <td width="80px">品牌型號</td>
                                                    <td width="80px">品名</td>
                                                    <td width="80px">序列號/設備編號</td>
                                                    <td width="100px">設備來源</td>
                                                    <td width="50px">需求數量</td>
                                                    <td width="120px">需求說明</td>
                                                    <td width="120px">資安檢查項</td>
                                                    <td width="120px">入廠待檢易碎貼編號<font color="red">*</font></td>
                                                </tr>
                                                <tbody id="info_Body0">
                                                <c:if test="${wfmachineinoutitems!=null&&wfmachineinoutitems.size()>0}">
                                                    <c:forEach items="${wfmachineinoutitems}" var="machineinoutitems" varStatus="status">
                                                        <tr align="center" id="machineinoutitems${status.index+1}">
                                                            <td>${status.index+1}</td>
                                                            <td>
                                                                <input id="brand${status.index+1}" name="wfmachineinoutitems[${status.index+1}].brand" type="hidden" value="${machineinoutitems.brand}"/>
                                                                ${machineinoutitems.brand}
                                                            </td>
                                                            <td>
                                                                <input id="productname${status.index+1}" name="wfmachineinoutitems[${status.index+1}].productname" type="hidden" value="${machineinoutitems.productname}"/>
                                                                ${machineinoutitems.productname}
                                                            </td>
                                                            <td>
                                                                <input id="serialnumber${status.index+1}" name="wfmachineinoutitems[${status.index+1}].serialnumber" type="hidden" value="${machineinoutitems.serialnumber}"/>
                                                                ${machineinoutitems.serialnumber}
                                                            </td>
                                                            <td>
                                                                <input id="sourceid${status.index+1}" name="wfmachineinoutitems[${status.index+1}].sourceid" type="hidden" value="${machineinoutitems.sourceid}"/>
                                                                <input id="sourcename${status.index+1}" name="wfmachineinoutitems[${status.index+1}].sourcename" type="hidden" value="${machineinoutitems.sourcename}"/>
                                                                ${machineinoutitems.sourcename}
                                                            </td>
                                                            <td>
                                                                <input id="demandnumber${status.index+1}" name="wfmachineinoutitems[${status.index+1}].demandnumber" type="hidden" value="${machineinoutitems.demandnumber}"/>
                                                                ${machineinoutitems.demandnumber}
                                                            </td>
                                                            <td>
                                                                <input id="demanddesc${status.index+1}" name="wfmachineinoutitems[${status.index+1}].demanddesc" type="hidden" value="${machineinoutitems.demanddesc}"/>
                                                                ${machineinoutitems.demanddesc}
                                                            </td>
                                                            <td>
                                                                <input id="zacheckid${status.index+1}" name="wfmachineinoutitems[${status.index+1}].zacheckid" type="hidden" value="${machineinoutitems.zacheckid }"/>
                                                                <input id="zacheckname${status.index+1}" name="wfmachineinoutitems[${status.index+1}].zacheckname" type="hidden" value="${machineinoutitems.zacheckname }"/>
                                                                <input id="zacheckother${status.index+1}" name="wfmachineinoutitems[${status.index+1}].zacheckother" type="hidden" value="${machineinoutitems.zacheckother}"/>
                                                                ${machineinoutitems.zacheckname}
                                                                <c:if test="${machineinoutitems.zacheckother!=''||machineinoutitems.zacheckother!=null}">${machineinoutitems.zacheckother}</c:if>
                                                            </td>
                                                            <td>
                                                                <input id="fragilenumber${status.index+1}"
                                                                       name="wfmachineinoutitems[${status.index+1}].fragilenumber" class="easyui-validatebox" style="width:100px;"
                                                                       data-options="required:true" value="${machineinoutitems.fragilenumber}"/>
                                                                <input id="shunxu${status.index+1}" type="hidden" name="wfmachineinoutitems[${status.index+1}].shunxu" value="${status.index+1}"/>
                                                            </td>
                                                        </tr>
                                                    </c:forEach>
                                                </c:if>
                                                </tbody>
                                            </table>
                                        </div>
                                    </td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <c:if test="${wfmachineinoutitems!=null&&wfmachineinoutitems.size()>0 &&(nodeOrder ge 7)}">
                                    <tr align="center">
                                        <td colspan="10" width="100%">
                                            <div style="overflow-x: auto;width: 1200px;">
                                                <table  width="100%">
                                                    <tr align="center">
                                                        <td width="30px">序號</td>
                                                        <td width="80px">品牌型號</td>
                                                        <td width="80px">品名</td>
                                                        <td width="80px">序列號/設備編號</td>
                                                        <td width="100px">設備來源</td>
                                                        <td width="50px">需求數量</td>
                                                        <td width="120px">需求說明</td>
                                                        <td width="120px">資安檢查項</td>
                                                        <td width="100px">入廠待檢易碎貼編號</td>
                                                    </tr>
                                                    <c:if test="${wfmachineinoutitems!=null&&wfmachineinoutitems.size()>0}">
                                                        <c:forEach items="${wfmachineinoutitems}" var="machineinoutitems" varStatus="status">
                                                            <tr align="center" id="machineinoutitems${status.index+1}">
                                                                <td>${status.index+1}</td>
                                                                <td>${machineinoutitems.brand}</td>
                                                                <td>${machineinoutitems.productname}</td>
                                                                <td>${machineinoutitems.serialnumber}</td>
                                                                <td>${machineinoutitems.sourcename}</td>
                                                                <td>${machineinoutitems.demandnumber}</td>
                                                                <td>${machineinoutitems.demanddesc}</td>
                                                                <td>${machineinoutitems.zacheckname}
                                                                    <c:if test="${machineinoutitems.zacheckother!=''||machineinoutitems.zacheckother!=null}">${machineinoutitems.zacheckother}</c:if>
                                                                </td>
                                                                <td>
                                                                    ${machineinoutitems.fragilenumber}
                                                                </td>
                                                            </tr>
                                                        </c:forEach>
                                                    </c:if>
                                                </table>
                                            </div>
                                        </td>
                                    </tr>
                                </c:if>
                            </c:otherwise>
                        </c:choose>

                        <tr align="center">
                            <td rowspan="2">設備流向</td>
                            <td>攜出位置</td>
                            <td colspan="6" class="td_style2">
                                ${wfmachineinoutprocessEntity.takeoutplant}廠區
                                ${wfmachineinoutprocessEntity.takeoutfactory}廠
                                ${wfmachineinoutprocessEntity.takeoutbuilding}棟
                                ${wfmachineinoutprocessEntity.takeoutlayer}層
                            </td>
                            <td colspan="2" class="td_style2">
                                <c:if test="${wfmachineinoutprocessEntity.takeoutsectype=='0'}">特保區</c:if>
                                <c:if test="${wfmachineinoutprocessEntity.takeoutsectype=='1'}">非特保區</c:if>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>攜入位置</td>
                            <td colspan="6" class="td_style2">
                                ${wfmachineinoutprocessEntity.takeinplant}廠區
                                ${wfmachineinoutprocessEntity.takeinfactory}廠
                                ${wfmachineinoutprocessEntity.takeinbuilding}棟
                                ${wfmachineinoutprocessEntity.takeinlayer}層
                            </td>
                            <td colspan="2" class="td_style2">
                                <c:if test="${wfmachineinoutprocessEntity.takeinsectype=='0'}">特保區</c:if>
                                <c:if test="${wfmachineinoutprocessEntity.takeinsectype=='1'}">非特保區</c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${wfmachineinoutprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%">
						<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <c:choose>
                                    <c:when test="${not empty nodeName&&'IT生技現場管控確認' eq nodeName}">
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"  perCall="fragileupdate"
                                                    serialNo="${wfmachineinoutprocessEntity.serialno}"></fox:action>
                                    </c:when>
                                    <c:otherwise>
                                        <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                                    serialNo="${wfmachineinoutprocessEntity.serialno}"></fox:action>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','機台進出管制區申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfmachineinoutprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<input type="hidden" id="nodeName"  value="${nodeName}"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/safety/wfmachineinoutprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>