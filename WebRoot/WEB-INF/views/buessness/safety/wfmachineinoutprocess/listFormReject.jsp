<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>機台進出管制區申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfmachineinoutprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfmachineinoutprocessEntity.id }"/>
    <input id="serialno" name="wfmachineinout.serialno" type="hidden" value="${wfmachineinoutprocessEntity.serialno }"/>
    <input id="makerno" name="wfmachineinout.makerno" type="hidden" value="${wfmachineinoutprocessEntity.makerno }"/>
    <input id="makername" name="wfmachineinout.makername" type="hidden" value="${wfmachineinoutprocessEntity.makername }"/>
    <input id="makerdeptno" name="wfmachineinout.makerdeptno" type="hidden" value="${wfmachineinoutprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfmachineinout.makerfactoryid" type="hidden"
           value="${wfmachineinoutprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">機台進出管制區申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfmachineinoutprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfmachineinoutprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfmachineinoutprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfmachineinoutprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfmachineinoutprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfmachineinoutprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfmachineinoutprocessEntity.makerno}/${wfmachineinoutprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="wfmachineinout.applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfmachineinoutprocessEntity.applyno }" onblur="queryUserInfo('apply');"/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="wfmachineinout.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfmachineinoutprocessEntity.applyname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="wfmachineinout.applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfmachineinoutprocessEntity.applydeptno }"/>
                            </td>
                            <td width="3%">費用代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applycostno" name="wfmachineinout.applycostno" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfmachineinoutprocessEntity.applycostno }"/>
                            </td>
                            <td width="4%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="wfmachineinout.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfmachineinoutprocessEntity.applyfactoryid }"
                                       data-options="width: 120,validType:'comboxValidate[\'applyfactoryid\',\'请選擇所在廠區\']',onSelect:function(){onchangeFactory('apply');}"/>
                                <input id="applyfactoryname" name="wfmachineinout.applyfactoryname" type="hidden" value="${wfmachineinoutprocessEntity.applyfactoryname }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="wfmachineinout.applyleveltype" class="easyui-validatebox" data-options="width: 80"
                                       value="${wfmachineinoutprocessEntity.applyleveltype }"/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="wfmachineinout.applymanager" class="easyui-validatebox" data-options="width: 80"
                                       value="${wfmachineinoutprocessEntity.applymanager }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="wfmachineinout.applyemail" class="easyui-validatebox"
                                       value="${wfmachineinoutprocessEntity.applyemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td>使用區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyarea" name="wfmachineinout.applyarea" class="easyui-combobox" data-options="width: 70,validType:'comboxValidate[\'applyarea\',\'请選擇區域\']',onSelect:function(){onchangeArea('apply');}"
                                       value="${wfmachineinoutprocessEntity.applyarea }" panelHeight="auto"/>&nbsp;/
                                <input id="applybuilding" name="wfmachineinout.applybuilding" class="easyui-combobox" data-options="width: 70,validType:'comboxValidate[\'applybuilding\',\'请選擇樓棟\']',onSelect:function(){onchangeBuilding('apply');}"
                                       value="${wfmachineinoutprocessEntity.applybuilding }" panelHeight="auto"/>

                                <input id="applyareaname" name="wfmachineinout.applyareaname" type="hidden" value="${wfmachineinoutprocessEntity.applyareaname }"/>
                                <input id="applybuildingname" name="wfmachineinout.applybuildingname" type="hidden" value="${wfmachineinoutprocessEntity.applybuildingname }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applytel" name="wfmachineinout.applytel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfmachineinoutprocessEntity.applytel }" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="wfmachineinout.applydeptname" class="easyui-validatebox" data-options="width: 400,required:true"
                                       value="${wfmachineinoutprocessEntity.applydeptname }"/>
                            </td>
                            <td>安保區域&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style2">
                                <div class="applysectypeDiv"></div>
                                <input id="applysectype" name="wfmachineinout.applysectype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfmachineinoutprocessEntity.applysectype }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">設備接收人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">設備接收人<br/>工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="receiverno" name="wfmachineinout.receiverno" class="easyui-validatebox"
                                       data-options="width: 80,required:true" value="${wfmachineinoutprocessEntity.receiverno }" onblur="queryUserInfo('receiver');"/>
                            </td>
                            <td width="4%">設備接收人</td>
                            <td width="6%" class="td_style1">
                                <input id="receivername" name="wfmachineinout.receivername"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfmachineinoutprocessEntity.receivername }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="receiverdeptno" name="wfmachineinout.receiverdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfmachineinoutprocessEntity.receiverdeptno }"/>
                            </td>
                            <td width="3%">費用代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="receivercostno" name="wfmachineinout.receivercostno" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfmachineinoutprocessEntity.receivercostno }"/>
                            </td>
                            <td width="4%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="receiverfactoryid" name="wfmachineinout.receiverfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfmachineinoutprocessEntity.receiverfactoryid }"
                                       data-options="width: 120,validType:'comboxValidate[\'receiverfactoryid\',\'请選擇所在廠區\']',onSelect:function(){onchangeFactory('receiver');}"/>
                                <input id="receiverfactoryname" name="wfmachineinout.receiverfactoryname" type="hidden" value="${wfmachineinoutprocessEntity.receiverfactoryname }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="receiverleveltype" name="wfmachineinout.receiverleveltype" class="easyui-validatebox" data-options="width: 80"
                                       value="${wfmachineinoutprocessEntity.receiverleveltype }"/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="receivermanager" name="wfmachineinout.receivermanager" class="easyui-validatebox" data-options="width: 80"
                                       value="${wfmachineinoutprocessEntity.receivermanager }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="receiveremail" name="wfmachineinout.receiveremail" class="easyui-validatebox"
                                       value="${wfmachineinoutprocessEntity.receiveremail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'receiveremail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td>使用區域&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="receiverarea" name="wfmachineinout.receiverarea" class="easyui-combobox" data-options="width: 70,validType:'comboxValidate[\'receiverarea\',\'请選擇區域\']',onSelect:function(){onchangeArea('receiver');}"
                                       value="${wfmachineinoutprocessEntity.receiverarea }" panelHeight="auto"/>&nbsp;/
                                <input id="receiverbuilding" name="wfmachineinout.receiverbuilding" class="easyui-combobox" data-options="width: 70,validType:'comboxValidate[\'receiverbuilding\',\'请選擇樓棟\']',onSelect:function(){onchangeBuilding('receiver');}"
                                       value="${wfmachineinoutprocessEntity.receiverbuilding }" panelHeight="auto"/>

                                <input id="receiverareaname" name="wfmachineinout.receiverareaname" type="hidden" value="${wfmachineinoutprocessEntity.receiverareaname }"/>
                                <input id="receiverbuildingname" name="wfmachineinout.receiverbuildingname" type="hidden" value="${wfmachineinoutprocessEntity.receiverbuildingname }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="receivertel" name="wfmachineinout.receivertel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfmachineinoutprocessEntity.receivertel }" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="receiverdeptname" name="wfmachineinout.receiverdeptname" class="easyui-validatebox" data-options="width: 400,required:true"
                                       value="${wfmachineinoutprocessEntity.receiverdeptname }"/>
                            </td>
                            <td>安保區域&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style2">
                                <div class="receiversectypeDiv"></div>
                                <input id="receiversectype" name="wfmachineinout.receiversectype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfmachineinoutprocessEntity.receiversectype }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">設備明細</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="wfmachineinoutTable" width="130%">
                                        <tr align="center">
                                            <td width="30px">序號</td>
                                            <td width="110px">品牌型號&nbsp;<font color="red">*</font></td>
                                            <td width="110px">品名<font color="red">*</font></td>
                                            <td width="110px">序列號/設備編號&nbsp;<font color="red">*</font></td>
                                            <td width="100px">設備來源&nbsp;<font color="red">*</font></td>
                                            <td width="80px">需求數量&nbsp;<font color="red">*</font></td>
                                            <td width="200px">需求說明&nbsp;<font color="red">*</font></td>
                                            <td width="380px">資安檢查項&nbsp;<font color="red">*</font></td>
                                            <td width="50px">操作</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wfmachineinoutitems!=null&&wfmachineinoutitems.size()>0}">
                                            <c:forEach items="${wfmachineinoutitems}" var="machineinoutitems" varStatus="status">
                                                <tr align="center" id="machineinoutitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <input id="brand${status.index+1}"
                                                               name="wfmachineinoutitems[${status.index+1}].brand" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${machineinoutitems.brand}"/>
                                                    </td>
                                                    <td>
                                                        <input id="productname${status.index+1}"
                                                               name="wfmachineinoutitems[${status.index+1}].productname" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${machineinoutitems.productname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="serialnumber${status.index+1}"
                                                               name="wfmachineinoutitems[${status.index+1}].serialnumber" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${machineinoutitems.serialnumber}"/>
                                                    </td>
                                                    <td>
                                                        <input id="sourceid${status.index+1}" name="wfmachineinoutitems[${status.index+1}].sourceid" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadSourcetype(${status.index+1});},onSelect:function(){onchangeSourceType(${status.index+1});}" style="width:100px;"
                                                               class="easyui-combobox" editable="false" value="${machineinoutitems.sourceid}"/>
                                                        <input id="sourcename${status.index+1}" name="wfmachineinoutitems[${status.index+1}].sourcename" type="hidden" value="${machineinoutitems.sourcename}"/>
                                                    </td>
                                                    <td>
                                                        <input id="demandnumber${status.index+1}"
                                                               name="wfmachineinoutitems[${status.index+1}].demandnumber" class="easyui-validatebox" style="width:50px;" readonly
                                                               data-options="required:true" value="${machineinoutitems.demandnumber}"/>
                                                    </td>

                                                    <td>
                                                        <input id="demanddesc${status.index+1}"
                                                               name="wfmachineinoutitems[${status.index+1}].demanddesc" class="easyui-validatebox" style="width:200px;"
                                                               data-options="required:true" value="${machineinoutitems.demanddesc}"/>
                                                    </td>
                                                    <td>
                                                        <div class="zacheckidDiv${status.index+1}" style="float: left;"></div>
                                                        <input id="zacheckid${status.index+1}" name="wfmachineinoutitems[${status.index+1}].zacheckid"
                                                               type="hidden" class="easyui-validatebox" data-options="width: 100"
                                                               value="${machineinoutitems.zacheckid }"/>
                                                        <input id="zacheckname${status.index+1}" name="wfmachineinoutitems[${status.index+1}].zacheckname" type="hidden" value="${machineinoutitems.zacheckname}"/>
                                                        <div style="float: left;">
                                                            <input id="zacheckother${status.index+1}" name="wfmachineinoutitems[${status.index+1}].zacheckother" class="easyui-validatebox" data-options="width: 120" readonly="readonly"
                                                                   value="${machineinoutitems.zacheckother}"/>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="machinedeltr(${status.index+1});return false;"/>
                                                        <input id="shunxu${status.index+1}" type="hidden" name="wfmachineinoutitems[${status.index+1}].shunxu" value="${status.index+1}"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${wfmachineinoutitems.size()==0 || wfmachineinoutitems==null}">
                                            <tr align="center" id="machineinoutitems1">
                                                <td>1</td>
                                                <td>
                                                    <input id="brand1" name="wfmachineinoutitems[1].brand" class="easyui-validatebox" style="width:100px;" data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="productname1" name="wfmachineinoutitems[1].productname" class="easyui-validatebox" style="width:100px;" data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="serialnumber1"
                                                           name="wfmachineinoutitems[1].serialnumber" class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="sourceid1" name="wfmachineinoutitems[1].sourceid" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadSourcetype(1);},onSelect:function(){onchangeSourceType(1);}" style="width:100px;"
                                                           class="easyui-combobox" editable="false" value=""/>
                                                    <input id="sourcename1" name="wfmachineinoutitems[1].sourcename" type="hidden" value=""/>
                                                </td>
                                                <td>
                                                    <input id="demandnumber1"
                                                           name="wfmachineinoutitems[1].demandnumber" class="easyui-validatebox" style="width:50px;" readonly
                                                           data-options="required:true" value="1"/>
                                                </td>

                                                <td>
                                                    <input id="demanddesc1"
                                                           name="wfmachineinoutitems[1].demanddesc" class="easyui-validatebox" style="width:200px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <div class="zacheckidDiv1" style="float: left;"></div>
                                                    <input id="zacheckid1" name="wfmachineinoutitems[1].zacheckid"
                                                           type="hidden" class="easyui-validatebox" data-options="width: 100" value=""/>
                                                    <input id="zacheckname1" name="wfmachineinoutitems[1].zacheckname" type="hidden" value=""/>
                                                    <div style="float: left;">
                                                        <input id="zacheckother1" name="wfmachineinoutitems[1].zacheckother" class="easyui-validatebox" data-options="width: 120" readonly="readonly"
                                                               value=""/>
                                                    </div>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="machinedeltr(1);return false;"/>
                                                    <input id="shunxu1" type="hidden" name="wfmachineinoutitems[1].shunxu" value="1"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr0">
                                            <td colspan="9" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="machineItemAdd" style="width:150px;float:left;" value="添加一行"/>
                                            </td>
                                        </tr>

                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td rowspan="2">設備流向&nbsp;<font color="red">*</font></td>
                            <td>攜出位置</td>
                            <td colspan="6" class="td_style2">
                                <input id="takeoutplant" name="wfmachineinout.takeoutplant" style="text-align: center"
                                       class="easyui-validatebox" data-options="width:150,required:true"
                                       value="${wfmachineinoutprocessEntity.takeoutplant }"/>廠區
                                <input id="takeoutfactory" name="wfmachineinout.takeoutfactory" style="text-align: center"
                                       class="easyui-validatebox" data-options="width:150,required:true"
                                       value="${wfmachineinoutprocessEntity.takeoutfactory }"/>廠
                                <input id="takeoutbuilding" name="wfmachineinout.takeoutbuilding" style="text-align: center"
                                       class="easyui-validatebox" data-options="width:100,required:true"
                                       value="${wfmachineinoutprocessEntity.takeoutbuilding }"/>棟
                                <input id="takeoutlayer" name="wfmachineinout.takeoutlayer" style="text-align: center"
                                       class="easyui-validatebox" data-options="width:100,required:true"
                                       value="${wfmachineinoutprocessEntity.takeoutlayer }"/>層
                            </td>
                            <td colspan="2" class="td_style2">
                                <div class="takeoutsectypeDiv"></div>
                                <input id="takeoutsectype" name="wfmachineinout.takeoutsectype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfmachineinoutprocessEntity.takeoutsectype }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>攜入位置</td>
                            <td colspan="6" class="td_style2">
                                <input id="takeinplant" name="wfmachineinout.takeinplant" style="text-align: center"
                                       class="easyui-validatebox" data-options="width:150,required:true"
                                       value="${wfmachineinoutprocessEntity.takeinplant }"/>廠區
                                <input id="takeinfactory" name="wfmachineinout.takeinfactory" style="text-align: center"
                                       class="easyui-validatebox" data-options="width:150,required:true"
                                       value="${wfmachineinoutprocessEntity.takeinfactory }"/>廠
                                <input id="takeinbuilding" name="wfmachineinout.takeinbuilding" style="text-align: center"
                                       class="easyui-validatebox" data-options="width:100,required:true"
                                       value="${wfmachineinoutprocessEntity.takeinbuilding }"/>棟
                                <input id="takeinlayer" name="wfmachineinout.takeinlayer" style="text-align: center"
                                       class="easyui-validatebox" data-options="width:100,required:true"
                                       value="${wfmachineinoutprocessEntity.takeinlayer }"/>層
                            </td>
                            <td colspan="2" class="td_style2">
                                <div class="takeinsectypeDiv"></div>
                                <input id="takeinsectype" name="wfmachineinout.takeinsectype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfmachineinoutprocessEntity.takeinsectype }"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="wfmachineinout.attachids" value="${wfmachineinoutprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">備註</td>
                            <td width="90%" class="td_style2">
                                1.此表單僅適用于iPEBG三線崗管制區域機台進出使用，多於5臺的需要填寫批量申請附件；<br/>
                                2.同廠區內機台跨車間移轉，由用戶單位申請警衛押運機台但無需通過資訊查驗；<br/>
                                3.機台進入車間后必須經過IT生技或資訊資安管控，方可接入網絡（私自入網，以破壞資訊處理設備論處）；<br/>
                                4.特保區不允許將機台移至非特保區，如有特殊需求必須開具專案聯絡單配合此表單使用；<br/>
                                5.若無設備編號，可以填寫設備的製造序號；<br/>
                                6.作業流程：申請單位填單申請→權限主管核准→申請單位自查自檢→產安簽字確認→IT生技查驗張貼“易碎貼”登記易碎貼編號并簽字確認→門崗警衛查驗、核對信息無誤后簽字放行。IT生技及中原資訊負責人簽字一欄，由負責申請單位運維的負責人簽核。<br/>
                                7.用戶需如實填寫單據資料，如不屬實，追究連帶責任；<br/>
                                8.此表單不適用于跨廠區機臺移轉，跨廠區機臺移轉需另簽核聯絡單；
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','機台進出管制區申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="abchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">申請單位部級主管
                                                                    <a href="javascript:addHq('abcharge');">添加一位</a>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="abchargeno" onblur="getUserNameByEmpno(this,'abcharge');" name="wfmachineinout.abchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['abchargeno']}"
                                                               value="${wfmachineinoutprocessEntity.abchargeno }"/><c:if test="${requiredMap['abchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="abchargename" name="wfmachineinout.abchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['abchargeno']}"
                                                                value="${wfmachineinoutprocessEntity.abchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="rbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">接收單位部級主管
                                                                    <a href="javascript:addHq('rbcharge');">添加一位</a>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="rbchargeno" onblur="getUserNameByEmpno(this,'rbcharge');" name="wfmachineinout.rbchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['rbchargeno']}"
                                                               value="${wfmachineinoutprocessEntity.rbchargeno }"/><c:if test="${requiredMap['rbchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="rbchargename" name="wfmachineinout.rbchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['rbchargeno']}"
                                                                value="${wfmachineinoutprocessEntity.rbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="acchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">申請單位廠級主管
                                                                    <a href="javascript:addHq('accharge');">添加一位</a>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="acchargeno" onblur="getUserNameByEmpno(this,'accharge');" name="wfmachineinout.acchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['acchargeno']}"
                                                               value="${wfmachineinoutprocessEntity.acchargeno }"/><c:if test="${requiredMap['acchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="acchargename" name="wfmachineinout.acchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['acchargeno']}"
                                                                value="${wfmachineinoutprocessEntity.acchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="rchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">設備接收人</td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="rchargeno" name="wfmachineinout.rchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['rchargeno']}"
                                                               readonly
                                                               value="${wfmachineinoutprocessEntity.rchargeno }"/><c:if
                                                            test="${requiredMap['rchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="rchargename" name="wfmachineinout.rchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['rchargeno']}"
                                                                value="${wfmachineinoutprocessEntity.rchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="rcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">接收單位廠級主管
                                                                    <a href="javascript:addHq('rccharge');">添加一位</a>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="rcchargeno" onblur="getUserNameByEmpno(this,'rccharge');" name="wfmachineinout.rcchargeno" class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['rcchargeno']}"  value="${wfmachineinoutprocessEntity.rcchargeno }"/><c:if
                                                            test="${requiredMap['rcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="rcchargename" name="wfmachineinout.rcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['rcchargeno']}"
                                                                value="${wfmachineinoutprocessEntity.rcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="itgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">IT生技現場管控確認</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(243,'itgchargeTable','itgchargeno','itgchargename',$('#applyfactoryid').combobox('getValue'),'wfmachineinout')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="itgchargeno" name="wfmachineinout.itgchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['itgchargeno']}"
                                                               readonly
                                                               value="${wfmachineinoutprocessEntity.itgchargeno }"/><c:if
                                                            test="${requiredMap['itgchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="itgchargename" name="wfmachineinout.itgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['itgchargeno']}"
                                                                value="${wfmachineinoutprocessEntity.itgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zaqchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">資安確認</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(244,'zaqchargeTable','zaqchargeno','zaqchargename',$('#applyfactoryid').combobox('getValue'),'wfmachineinout')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zaqchargeno" name="wfmachineinout.zaqchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zaqchargeno']}"
                                                               readonly
                                                               value="${wfmachineinoutprocessEntity.zaqchargeno }"/><c:if
                                                            test="${requiredMap['zaqchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zaqchargename" name="wfmachineinout.zaqchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zaqchargeno']}"
                                                                value="${wfmachineinoutprocessEntity.zaqchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="pschargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產安確認</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(245,'pschargeTable','pschargeno','pschargename',$('#applyfactoryid').combobox('getValue'),'wfmachineinout')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pschargeno" name="wfmachineinout.pschargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pschargeno']}"
                                                               readonly
                                                               value="${wfmachineinoutprocessEntity.pschargeno }"/><c:if
                                                            test="${requiredMap['pschargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pschargename" name="wfmachineinout.pschargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pschargeno']}"
                                                                value="${wfmachineinoutprocessEntity.pschargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zazchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">資安主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(246,'zazchargeTable','zazchargeno','zazchargename',$('#applyfactoryid').combobox('getValue'),'wfmachineinout')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zazchargeno" name="wfmachineinout.zazchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zazchargeno']}"
                                                               readonly
                                                               value="${wfmachineinoutprocessEntity.zazchargeno }"/><c:if
                                                            test="${requiredMap['zazchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zazchargename" name="wfmachineinout.zazchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zazchargeno']}"
                                                                value="${wfmachineinoutprocessEntity.zazchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfmachineinoutprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;"
                                   onclick="canelTask('${wfmachineinoutprocessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
    <div id="dlg"></div>
</form>
</div>
<script src='${ctx}/static/js/safety/wfmachineinoutprocess.js?random=<%= Math.random()%>'></script>
<script type="text/javascript">
    if ("${wfmachineinoutprocessEntity.abchargeno}" != "") {
        var nostr = "${wfmachineinoutprocessEntity.abchargeno}";
        var namestr = "${wfmachineinoutprocessEntity.abchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#abchargeTable tr:eq(" + (i + 2) + ")").find("#abchargeno").val(notr[i]);
            $("#abchargeTable tr:eq(" + (i + 2) + ")").find("#abchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#abchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='abchargeno' name='wfmachineinout.abchargeno' style='width: 80px' value='' onblur='getUserNameByEmpno(this,'abcharge');'/>/<input id='abchargename' name='wfmachineinout.abchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wfmachineinoutprocessEntity.rbchargeno}" != "") {
        var nostr = "${wfmachineinoutprocessEntity.rbchargeno}";
        var namestr = "${wfmachineinoutprocessEntity.rbchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#rbchargeTable tr:eq(" + (i + 2) + ")").find("#rbchargeno").val(notr[i]);
            $("#rbchargeTable tr:eq(" + (i + 2) + ")").find("#rbchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#rbchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='rbchargeno' name='wfmachineinout.rbchargeno' style='width: 80px' value='' onblur='getUserNameByEmpno(this,'rbcharge');'/>/<input id='rbchargename' name='wfmachineinout.rbchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wfmachineinoutprocessEntity.acchargeno}" != "") {
        var nostr = "${wfmachineinoutprocessEntity.acchargeno}";
        var namestr = "${wfmachineinoutprocessEntity.acchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#acchargeTable tr:eq(" + (i + 2) + ")").find("#acchargeno").val(notr[i]);
            $("#acchargeTable tr:eq(" + (i + 2) + ")").find("#acchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#acchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='acchargeno' name='wfmachineinout.acchargeno' style='width: 80px' value='' onblur='getUserNameByEmpno(this,'accharge');'/>/<input id='acchargename' name='wfmachineinout.acchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wfmachineinoutprocessEntity.rcchargeno}" != "") {
        var nostr = "${wfmachineinoutprocessEntity.rcchargeno}";
        var namestr = "${wfmachineinoutprocessEntity.rcchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#rcchargeTable tr:eq(" + (i + 2) + ")").find("#rcchargeno").val(notr[i]);
            $("#rcchargeTable tr:eq(" + (i + 2) + ")").find("#rcchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#rcchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='rcchargeno' name='wfmachineinout.rcchargeno' style='width: 80px' value='' onblur='getUserNameByEmpno(this,'rccharge');'/>/<input id='rcchargename' name='wfmachineinout.rcchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
</script>
</body>
</html>