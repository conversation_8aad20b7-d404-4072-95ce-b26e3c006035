<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>商務餐廳客戶/外來人員就餐申請表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/safety/wfcustomereatprocess.js?random=<%= Math.random()%>'></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
        .overdiv{
            overflow-y: auto;
            overflow-x: auto;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfcustomereatprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfcustomereatprocessEntity.id }"/>
    <input id="serialno" name="wfcustomereat.serialno" type="hidden" value="${wfcustomereatprocessEntity.serialno }"/>
    <input id="makerno" name="wfcustomereat.makerno" type="hidden" value="${wfcustomereatprocessEntity.makerno }"/>
    <input id="makername" name="wfcustomereat.makername" type="hidden" value="${wfcustomereatprocessEntity.makername }"/>
    <input id="makerdeptno" name="wfcustomereat.makerdeptno" type="hidden" value="${wfcustomereatprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfcustomereat.makerfactoryid" type="hidden" value="${wfcustomereatprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">商務餐廳客戶/外來人員就餐申請表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfcustomereatprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfcustomereatprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfcustomereatprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfcustomereatprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfcustomereatprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfcustomereatprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfcustomereatprocessEntity.makerno}/${wfcustomereatprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">提報人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">提報人工號&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="dealno" name="wfcustomereat.dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfcustomereatprocessEntity.dealno }" onblur="queryUserInfo('deal');"/>
                            </td>
                            <td width="4%">提報人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="wfcustomereat.dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfcustomereatprocessEntity.dealname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="wfcustomereat.dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfcustomereatprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="4%">費用代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealcostno" name="wfcustomereat.dealcostno" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfcustomereatprocessEntity.dealcostno }"/>
                            </td>
                            <td width="4%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealfactoryid" name="wfcustomereat.dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfcustomereatprocessEntity.dealfactoryid }"
                                       data-options="width: 120,validType:'comboxValidate[\'dealfactoryid\',\'请選擇所在廠區\']',onSelect:function(){onchangeFactory('deal');}"/>
                                <input id="dealfactoryname" name="wfcustomereat.dealfactoryname" type="hidden" value="${wfcustomereatprocessEntity.dealfactoryname }"/>
                                <input id="dealnofactoryid" name="wfcustomereat.dealnofactoryid" type="hidden" value="${wfcustomereatprocessEntity.dealnofactoryid}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealdeptname" name="wfcustomereat.dealdeptname" class="easyui-validatebox" data-options="width: 400,required:true"
                                       value="${wfcustomereatprocessEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealtel" name="wfcustomereat.dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfcustomereatprocessEntity.dealtel }" data-options="required:true,prompt:'579+66666'"
                                       onblur="validApplyTel('dealtel')"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="wfcustomereat.dealemail" class="easyui-validatebox"
                                       value="${wfcustomereatprocessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請內容</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <%--<div style="overflow-x: auto;width: 1200px;">--%>
                                <c:if test="${wfcustomereatitems!=null && wfcustomereatitems.size()>0&& wfcustomereatitems.size()>10}">
                                <div style="overflow-x: auto;overflow-y: auto;height: 400px" width="1200px">
                                </c:if>
                                <c:if test="${wfcustomereatitems!=null && wfcustomereatitems.size()>0&& wfcustomereatitems.size()<10}">
                                <div style="overflow-x: auto;overflow-y: auto" width="1200px">
                                </c:if>
                                <c:if test="${wfcustomereatitems==null}">
                                <div class="overdiv" width="1200px" id="overflowdiv">
                                    </c:if>
                                    <table id="wfcustomereatitemsTable" width="120%">
                                        <tr align="center">
                                            <td width="30px">項次</td>
                                            <td width="100px">工號</td>
                                            <td width="100px">姓名/客戶單位&nbsp;<font color="red">*</font></td>
                                            <td width="150px">接洽部門&nbsp;<font color="red">*</font></td>
                                            <td width="110px">掛靠費用代碼&nbsp;<font color="red">*</font></td>
                                            <td width="90px">職稱</td>
                                            <td width="90px">資位</td>
                                            <td width="120px">就餐開始日期&nbsp;<font color="red">*</font></td>
                                            <td width="120px">就餐結束日期&nbsp;<font color="red">*</font></td>
                                            <td width="200px">餐別&nbsp;<font color="red">*</font></td>
                                            <td width="300px">備註</td>
                                            <td width="50px">操作</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wfcustomereatitems!=null&&wfcustomereatitems.size()>0}">
                                            <c:forEach items="${wfcustomereatitems}" var="customereatitems" varStatus="status">
                                                <tr align="center" id="customereatitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <input id="applyno${status.index+1}"
                                                               onblur="getUserNameByEmpno2(this,'apply',status.index+1);"
                                                               name="wfcustomereatitems[${status.index+1}].applyno"
                                                               class="easyui-validatebox" style="width:70px;"
                                                               value="${customereatitems.applyno}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyname${status.index+1}"
                                                               name="wfcustomereatitems[${status.index+1}].applyname"
                                                               class="easyui-validatebox" style="width:70px;"
                                                               value="${customereatitems.applyname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applydeptname${status.index+1}"
                                                               name="wfcustomereatitems[${status.index+1}].applydeptname"
                                                               class="easyui-validatebox" style="width:120px;" data-options="required:true"
                                                               value="${customereatitems.applydeptname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applycostno${status.index+1}"
                                                               name="wfcustomereatitems[${status.index+1}].applycostno"
                                                               class="easyui-validatebox" style="width:70px;" data-options="required:true"
                                                               value="${customereatitems.applycostno}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applymanager${status.index+1}"
                                                               name="wfcustomereatitems[${status.index+1}].applymanager"
                                                               class="easyui-validatebox" style="width:70px;"
                                                               value="${customereatitems.applymanager}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyleveltype${status.index+1}"
                                                               name="wfcustomereatitems[${status.index+1}].applyleveltype"
                                                               class="easyui-validatebox" style="width:70px;"
                                                               value="${customereatitems.applyleveltype}"/>
                                                    </td>
                                                    <td>
                                                        <input id="applystartdate${status.index+1}" name="wfcustomereatitems[${status.index+1}].applystartdate" class="easyui-validatebox Wdate" data-options="required:true,prompt:'请选择开始时间'" style="width:100px"
                                                               value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${customereatitems.applystartdate}"/>" onclick="WdatePicker({onpicked:function(){applyenddate${status.index+1}.click()},doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd'})"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyenddate${status.index+1}" name="wfcustomereatitems[${status.index+1}].applyenddate" class="easyui-validatebox Wdate" data-options="required:true,prompt:'请选择結束时间'" style="width:100px"
                                                               value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${customereatitems.applyenddate}"/>" onclick="WdatePicker({doubleCalendar:true,skin:'whyGreen',minDate:'#F{$dp.$D(\'applystartdate${status.index+1}\')}',dateFmt:'yyyy-MM-dd'})"/>
                                                    </td>
                                                    <td>
                                                        <div class="applytypeDiv${status.index+1}" style="float: left;"></div>
                                                        <input id="applytype${status.index+1}" name="wfcustomereatitems[${status.index+1}].applytype"
                                                               type="hidden" class="easyui-validatebox" data-options="width: 100"
                                                               value="${customereatitems.applytype }"/>
                                                    </td>
                                                    <td>
                                                        <input id="applymemo${status.index+1}"
                                                               name="wfcustomereatitems[${status.index+1}].applymemo"
                                                               class="easyui-validatebox" style="width:250px;"
                                                               value="${customereatitems.applymemo}"/>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="customereatdeltr(${status.index+1});return false;"/>
                                                        <input id="shunxu${status.index+1}" type="hidden" name="wfcustomereatitems[${status.index+1}].shunxu" value="${status.index+1}"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${wfcustomereatitems.size()==0 || wfcustomereatitems==null}">
                                            <tr align="center" id="customereatitems1">
                                                <td>1</td>
                                                <td>
                                                    <input id="applyno1"
                                                           onblur="getUserNameByEmpno2(this,'apply',1);"
                                                           name="wfcustomereatitems[1].applyno" class="easyui-validatebox" style="width:70px;" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyname1" name="wfcustomereatitems[1].applyname" data-options="required:true"
                                                           class="easyui-validatebox" style="width:70px;" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applydeptname1" name="wfcustomereatitems[1].applydeptname" data-options="required:true"
                                                           class="easyui-validatebox" style="width:120px;" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applycostno1" name="wfcustomereatitems[1].applycostno" data-options="required:true"
                                                           class="easyui-validatebox" style="width:70px;" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applymanager1" name="wfcustomereatitems[1].applymanager"
                                                           class="easyui-validatebox" style="width:70px;" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applyleveltype1" name="wfcustomereatitems[1].applyleveltype"
                                                           class="easyui-validatebox" style="width:70px;" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applystartdate1" name="wfcustomereatitems[1].applystartdate" class="easyui-validatebox Wdate" data-options="required:true,prompt:'请选择开始时间'" style="width:100px"
                                                           value="" onclick="WdatePicker({onpicked:function(){applyenddate1.click()},doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd'})"/>
                                                </td>
                                                <td>
                                                    <input id="applyenddate1" name="wfcustomereatitems[1].applyenddate" class="easyui-validatebox Wdate" data-options="required:true,prompt:'请选择結束时间'" style="width:100px"
                                                           value="" onclick="WdatePicker({doubleCalendar:true,skin:'whyGreen',minDate:'#F{$dp.$D(\'applystartdate1\')}',dateFmt:'yyyy-MM-dd'})"/>
                                                </td>
                                                <td>
                                                    <div class="applytypeDiv1" style="float: left;"></div>
                                                    <input id="applytype1" name="wfcustomereatitems[1].applytype"
                                                           type="hidden" class="easyui-validatebox" data-options="width: 100" value=""/>
                                                </td>
                                                <td>
                                                    <input id="applymemo1" name="wfcustomereatitems[1].applymemo"
                                                           class="easyui-validatebox" style="width:250px;" value=""/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="customereatdeltr(1);return false;"/>
                                                    <input id="shunxu1" type="hidden" name="wfcustomereatitems[1].shunxu" value="1"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr">
                                            <td colspan="12" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="customereatItemAdd" style="width:150px;float:left;" value="添加一行"/>
                                            </td>
                                        </tr>

                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">
                                <a href="${ctx}/wfcustomereatprocess/downLoad/batchImportTpl" id="btnBatchImportTpl">模板下載</a>
                            </td>
                            <td width="90%" class="td_style1">
                                &nbsp;&nbsp;&nbsp;&nbsp;<a href="#" id="batchImport" class="easyui-linkbutton"
                                                           data-options="iconCls:'icon-hamburg-cv'" style="width: 100px;"
                                                           onclick="openBatchImportWin();">批量導入</a>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
                                    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="wfcustomereat.attachids" value="${wfcustomereatprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center" style="height: 100%" >
                            <td>備註</td>
                            <td colspan="9" class="td_style2">
                                <input id="applyMemo" type="hidden" value="${applyMemo}"/>
                                <textarea id="applyTextareaMemo" class="easyui-validatebox"
                                          disabled readonly
                                          style="width:99%;resize:none;background-color: #F2F5F7;border: 0px;outline: none;">${applyMemo}</textarea>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','商務餐廳客戶/外來人員就餐申請表');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zwqchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zwqchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(270,'zwqchargeTable','zwqchargeno','zwqchargename',$('#dealfactoryid').combobox('getValue'),'wfcustomereat')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zwqchargeno" name="wfcustomereat.zwqchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zwqchargeno']}"
                                                               readonly
                                                               value="${wfcustomereatprocessEntity.zwqchargeno }"/><c:if
                                                            test="${requiredMap['zwqchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zwqchargename" name="wfcustomereat.zwqchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zwqchargeno']}"
                                                                value="${wfcustomereatprocessEntity.zwqchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealnofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="wfcustomereat.kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wfcustomereatprocessEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="wfcustomereat.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfcustomereatprocessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealnofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="wfcustomereat.bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfcustomereatprocessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="wfcustomereat.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfcustomereatprocessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#dealnofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wfcustomereat.cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfcustomereatprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wfcustomereat.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfcustomereatprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="hqjgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">${requiredMap['hqjgchargeno_name']}
                                                                    <a href="#"
                                                                       onclick="addHq('wfcustomereat','hqjgcharge');">添加一位</a>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hqjgchargeno"
                                                               onblur="gethqUserNameByEmpno(this,'hqjgcharge');"
                                                               name="wfcustomereat.hqjgchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hqjgchargeno']}"
                                                               value="${wfcustomereatprocessEntity.hqjgchargeno }"/><c:if
                                                            test="${requiredMap['hqjgchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hqjgchargename" name="wfcustomereat.hqjgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hqjgchargeno']}"
                                                                value="${wfcustomereatprocessEntity.hqjgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>


                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="hqzwchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">${requiredMap['hqzwchargeno_name']}
                                                                    <a href="#"
                                                                       onclick="addHq('wfcustomereat','hqzwcharge');">添加一位</a>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hqzwchargeno"
                                                               onblur="gethqUserNameByEmpno(this,'hqzwcharge');"
                                                               name="wfcustomereat.hqzwchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hqzwchargeno']}"
                                                               value="${wfcustomereatprocessEntity.hqzwchargeno }"/><c:if
                                                            test="${requiredMap['hqzwchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hqzwchargename" name="wfcustomereat.hqzwchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hqzwchargeno']}"
                                                                value="${wfcustomereatprocessEntity.hqzwchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealnofactoryid').val(),'wfcustomereat')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wfcustomereat.zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfcustomereatprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="wfcustomereat.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfcustomereatprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#dealdeptno').val(),'pcchargeno','pcchargename',$('#dealnofactoryid').val(),'wfcustomereat')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="wfcustomereat.pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${wfcustomereatprocessEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="wfcustomereat.pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wfcustomereatprocessEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfcustomereatprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;"
                                   onclick="canelTask('${wfcustomereatprocessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <div id="win"></div>
    <div id="dlg"></div>
</form>
<div id="optionWin" class="easyui-window" title="商務餐廳客戶/外來人員就餐申請單信息導入" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%">
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="file" type="file" style="width: 300px"
                           accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>
                    <input id="isExcel2003" name="isExcel2003" type="hidden" value="">

                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <span ID="labelListAddResult"></span><a href="${ctx}/wfptalkprocess/downLoad/errorExcel" id="downloadError"
                                                            plain="true">查看錯誤信息</a>
                </td>
            </tr>
        </table>
    </form>
</div>
<script type="text/javascript">
    applyMemoHeight();
    if ("${wfcustomereatprocessEntity.hqjgchargeno}" != "") {
        var nostr = "${wfcustomereatprocessEntity.hqjgchargeno}";
        var namestr = "${wfcustomereatprocessEntity.hqjgchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hqjgchargeTable tr:eq(" + (i + 2) + ")").find("#hqjgchargeno").val(notr[i]);
            $("#hqjgchargeTable tr:eq(" + (i + 2) + ")").find("#hqjgchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hqjgchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hqjgchargeno' name='wfcustomereat.hqjgchargeno' style='width: 80px' value='' onblur='gethqUserNameByEmpno(this,'hqjgcharge');'/>/<input id='hqjgchargename' name='wfcustomereat.hqjgchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wfcustomereatprocessEntity.hqzwchargeno}" != "") {
        var nostr = "${wfcustomereatprocessEntity.hqzwchargeno}";
        var namestr = "${wfcustomereatprocessEntity.hqzwchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hqzwchargeTable tr:eq(" + (i + 2) + ")").find("#hqzwchargeno").val(notr[i]);
            $("#hqzwchargeTable tr:eq(" + (i + 2) + ")").find("#hqzwchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hqzwchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hqzwchargeno' name='wfcustomereat.hqzwchargeno' style='width: 80px' value='' onblur='gethqUserNameByEmpno(this,'hqzwcharge');'/>/<input id='hqzwchargename' name='wfcustomereat.hqzwchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
</script>
</body>
</html>