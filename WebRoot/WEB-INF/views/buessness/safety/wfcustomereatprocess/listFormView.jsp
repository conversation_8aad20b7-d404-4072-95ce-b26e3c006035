<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>商務餐廳客戶/外來人員就餐申請表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/safety/wfcustomereatprocess.js?random=<%= Math.random()%>'></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfcustomereatprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfcustomereatprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfcustomereatprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">商務餐廳客戶/外來人員就餐申請表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfcustomereatprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfcustomereatprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfcustomereatprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfcustomereatprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfcustomereatprocessEntity.makerno}/${wfcustomereatprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">提報人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">提報人工號</td>
                            <td width="5%" class="td_style2">${wfcustomereatprocessEntity.dealno}</td>
                            <td width="4%">提報人</td>
                            <td width="6%" class="td_style2">${wfcustomereatprocessEntity.dealname}</td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style2">${wfcustomereatprocessEntity.dealdeptno}</td>
                            <td width="4%">費用代碼</td>
                            <td width="6%" class="td_style2">${wfcustomereatprocessEntity.dealcostno}</td>
                            <td width="4%">所在廠區</td>
                            <td width="6%" class="td_style2">${wfcustomereatprocessEntity.dealfactoryname}</td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱</td>
                            <td colspan="3" class="td_style2">${wfcustomereatprocessEntity.dealdeptname}</td>
                            <td>聯繫方式</td>
                            <td class="td_style2">${wfcustomereatprocessEntity.dealtel}</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfcustomereatprocessEntity.dealemail}</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請內容</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <%--<div style="overflow-x: auto;width: 1200px;">--%>
                                <c:if test="${wfcustomereatitems!=null && wfcustomereatitems.size()>0&& wfcustomereatitems.size()>10}">
                                <div style="overflow-x: auto;overflow-y: auto;height: 400px" width="1200px">
                                </c:if>
                                <c:if test="${wfcustomereatitems!=null && wfcustomereatitems.size()>0&& wfcustomereatitems.size()<10}">
                                <div style="overflow-x: auto;overflow-y: auto" width="1200px">
                                </c:if>
                                <c:if test="${wfcustomereatitems==null}">
                                <div class="overdiv" width="1200px" id="overflowdiv">
                                    </c:if>
                                    <table id="wfcustomereatitemsTable" width="100%">
                                        <tr align="center">
                                            <td width="30px">項次</td>
                                            <td width="100px">工號</td>
                                            <td width="100px">姓名/客戶單位</td>
                                            <td width="150px">接洽部門</td>
                                            <td width="110px">掛靠費用代碼</td>
                                            <td width="90px">職稱</td>
                                            <td width="90px">資位</td>
                                            <td width="120px">就餐開始日期</td>
                                            <td width="120px">就餐結束日期</td>
                                            <td width="200px">餐別</td>
                                            <td width="300px">備註</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wfcustomereatitems!=null&&wfcustomereatitems.size()>0}">
                                            <c:forEach items="${wfcustomereatitems}" var="customereatitems" varStatus="status">
                                                <tr align="center" id="customereatitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>${customereatitems.applyno}</td>
                                                    <td>${customereatitems.applyname}</td>
                                                    <td>${customereatitems.applydeptname}</td>
                                                    <td>${customereatitems.applycostno}</td>
                                                    <td>${customereatitems.applymanager}</td>
                                                    <td>${customereatitems.applyleveltype}</td>
                                                    <td>
                                                        <input id="applystartdate${status.index+1}" name="wfcustomereatitems[${status.index+1}].applystartdate" class="easyui-validatebox Wdate" data-options="required:true,prompt:'请选择开始时间'" style="width:100px"
                                                               value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${customereatitems.applystartdate}"/>" onclick="WdatePicker({onpicked:function(){applyenddate${status.index+1}.click()},doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd'})"/>
                                                    </td>
                                                    <td>
                                                        <input id="applyenddate${status.index+1}" name="wfcustomereatitems[${status.index+1}].applyenddate" class="easyui-validatebox Wdate" data-options="required:true,prompt:'请选择結束时间'" style="width:100px"
                                                               value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${customereatitems.applyenddate}"/>" onclick="WdatePicker({doubleCalendar:true,skin:'whyGreen',minDate:'#F{$dp.$D(\'applystartdate${status.index+1}\')}',dateFmt:'yyyy-MM-dd'})"/>
                                                    </td>
                                                    <td>${customereatitems.applytype }</td>
                                                    <td>${customereatitems.applymemo}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <input type="hidden" id="attachids" name="attachids" value="${wfcustomereatprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','商務餐廳客戶/外來人員就餐申請表');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfcustomereatprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfcustomereatprocessEntity.workstatus!=null&&wfcustomereatprocessEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
</body>
</html>