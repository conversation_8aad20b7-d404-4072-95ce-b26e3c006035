<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>料號新增異動申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfitemnonewchange/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfitemnonewchangeEntity.id }"/>
    <input id="serialno" name="wfitemnonewchange.serialno" type="hidden" value="${wfitemnonewchangeEntity.serialno }"/>
    <input id="makerno" name="wfitemnonewchange.makerno" type="hidden" value="${wfitemnonewchangeEntity.makerno }"/>
    <input id="makername" name="wfitemnonewchange.makername" type="hidden" value="${wfitemnonewchangeEntity.makername }"/>
    <input id="makerdeptno" name="wfitemnonewchange.makerdeptno" type="hidden" value="${wfitemnonewchangeEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfitemnonewchange.makerfactoryid" type="hidden" value="${wfitemnonewchangeEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">料號新增異動申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfitemnonewchangeEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfitemnonewchangeEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfitemnonewchangeEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfitemnonewchangeEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfitemnonewchangeEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfitemnonewchangeEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfitemnonewchangeEntity.makerno}/${wfitemnonewchangeEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">申請人詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="wfitemnonewchange.applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfitemnonewchangeEntity.applyno }" onblur="queryUserInfo('apply');"/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="wfitemnonewchange.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfitemnonewchangeEntity.applyname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="wfitemnonewchange.applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfitemnonewchangeEntity.applydeptno }"/>
                            </td>
                            <td width="4%">廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="wfitemnonewchange.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfitemnonewchangeEntity.applyfactoryid }"
                                       data-options="width: 120,validType:'comboxValidate[\'applyfactoryid\',\'请選擇所在廠區\']',onSelect:function(){onchangeFactory('apply');}"/>
                                <input id="applyfactoryname" name="wfitemnonewchange.applyfactoryname" type="hidden" value="${wfitemnonewchangeEntity.applyfactoryname }"/>
                                <input id="applynofactoryid" name="wfitemnonewchange.applynofactoryid" type="hidden" value="${wfitemnonewchangeEntity.applynofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>費用代碼</td>
                            <td class="td_style1">
                                <input id="applycostno" name="wfitemnonewchange.applycostno" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfitemnonewchangeEntity.applycostno }"/>
                            </td>
                            <td>申請單位&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="wfitemnonewchange.applydeptname" class="easyui-validatebox" data-options="width: 400,required:true"
                                       value="${wfitemnonewchangeEntity.applydeptname }"/>
                            </td>
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applytel" name="wfitemnonewchange.applytel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfitemnonewchangeEntity.applytel }" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td class="td_style1" colspan="3">
                                <input id="applyemail" name="wfitemnonewchange.applyemail" class="easyui-validatebox"
                                       value="${wfitemnonewchangeEntity.applyemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                            <td>料號類別&nbsp;<font color="red">*</font></td>
                            <td colspan="3">
                                <div class="partnoDiv" style="float: left;"></div>
                                <input id="partno" name="wfitemnonewchange.partno"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfitemnonewchangeEntity.partno }"/>
                                <input id="partname" name="wfitemnonewchange.partname" type="hidden"
                                       value="${wfitemnonewchangeEntity.partname }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">料號信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="8" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="wfitemnoinfosTable" width="130%">
                                        <tr align="center">
                                            <td width="30px">序號</td>
                                            <td width="80px">料號</td>
                                            <td width="80px">品名&nbsp;<font color="red">*</font></td>
                                            <td width="50px">品牌&nbsp;<font color="red">*</font></td>
                                            <td width="80px">規格及型號&nbsp;<font color="red">*</font></td>
                                            <td width="50px">單位&nbsp;<font color="red">*</font></td>
                                            <td width="60px">數量&nbsp;<font color="red">*</font></td>
                                            <td width="100px">預計需求日期&nbsp;<font color="red">*</font></td>
                                            <td width="80px">請購週期&nbsp;<font color="red">*</font></td>
                                            <td width="100px">用途說明&nbsp;<font color="red">*</font></td>
                                            <td width="100px">作業類別&nbsp;<font color="red">*</font></td>
                                            <td width="100px">作業原因&nbsp;<font color="red">*</font></td>
                                            <td width="50px">操作</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wfitemnoinfos!=null&&wfitemnoinfos.size()>0}">
                                            <c:forEach items="${wfitemnoinfos}" var="itemnoinfos" varStatus="status">
                                                <tr align="center" id="itemnoinfos${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <input id="itemno${status.index+1}"
                                                               name="itemnoinfos[${status.index+1}].itemno" class="easyui-validatebox" style="width:100px;"
                                                               value="${itemnoinfos.itemno}"/>
                                                    </td>
                                                    <td>
                                                        <input id="productname${status.index+1}"
                                                               name="itemnoinfos[${status.index+1}].productname" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${itemnoinfos.productname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="brand${status.index+1}"
                                                               name="itemnoinfos[${status.index+1}].brand" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${itemnoinfos.brand}"/>
                                                    </td>
                                                    <td>
                                                        <input id="model${status.index+1}"
                                                               name="itemnoinfos[${status.index+1}].model" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${itemnoinfos.model}"/>
                                                    </td>
                                                    <td>
                                                        <input id="unit${status.index+1}"
                                                               name="itemnoinfos[${status.index+1}].unit" class="easyui-validatebox" style="width:50px;"
                                                               data-options="required:true" value="${itemnoinfos.unit}"/>
                                                    </td>

                                                    <td>
                                                        <input id="itemnumber${status.index+1}"
                                                               name="itemnoinfos[${status.index+1}].itemnumber" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${itemnoinfos.itemnumber}"/>
                                                    </td>
                                                    <td>
                                                        <input id="expectdate${status.index+1}" name="itemnoinfos[${status.index+1}].expectdate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                                               data-options="required:true,width: 100"
                                                               value="<fmt:formatDate value="${itemnoinfos.expectdate}"/>"/>
                                                    </td>
                                                    <td>
                                                        <input id="cycle${status.index+1}"
                                                               name="itemnoinfos[${status.index+1}].cycle" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${itemnoinfos.cycle}"/>
                                                    </td>
                                                    <td>
                                                        <input id="useage${status.index+1}"
                                                               name="itemnoinfos[${status.index+1}].useage" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${itemnoinfos.useage}"/>
                                                    </td>
                                                    <td>
                                                        <input id="jobtype${status.index+1}"
                                                               name="itemnoinfos[${status.index+1}].jobtype" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${itemnoinfos.jobtype}"/>
                                                    </td>
                                                    <td>
                                                        <input id="jobreason${status.index+1}"
                                                               name="itemnoinfos[${status.index+1}].jobreason" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${itemnoinfos.jobreason}"/>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="itemnodeltr(${status.index+1});return false;"/>
                                                        <input id="shunxu${status.index+1}" type="hidden" name="itemnoinfos[${status.index+1}].shunxu" value="${status.index+1}"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${wfitemnoinfos.size()==0 || wfitemnoinfos==null}">
                                            <tr align="center" id="itemnoinfos1">
                                                <td>1</td>
                                                <td>
                                                    <input id="itemno1" name="itemnoinfos[1].itemno" class="easyui-validatebox" style="width:100px;" value=""/>
                                                </td>
                                                <td>
                                                    <input id="productname1" name="itemnoinfos[1].productname" class="easyui-validatebox" style="width:100px;" data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="brand1" name="itemnoinfos[1].brand" class="easyui-validatebox" style="width:100px;" data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="model1" name="itemnoinfos[1].model" class="easyui-validatebox" style="width:100px;" data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="unit1" name="itemnoinfos[1].unit" class="easyui-validatebox" style="width:50px;" data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="itemnumber1" name="itemnoinfos[1].itemnumber" class="easyui-validatebox" style="width:100px;" data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="expectdate1" name="itemnoinfos[1].expectdate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                                           data-options="required:true,width: 100" value=""/>
                                                </td>
                                                <td>
                                                    <input id="cycle1" name="itemnoinfos[1].cycle" class="easyui-validatebox" style="width:100px;" data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="useage1" name="itemnoinfos[1].useage" class="easyui-validatebox" style="width:100px;" data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="jobtype1" name="itemnoinfos[1].jobtype" class="easyui-validatebox" style="width:100px;" data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="jobreason1" name="itemnoinfos[1].jobreason" class="easyui-validatebox" style="width:100px;" data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="itemnodeltr(1);return false;"/>
                                                    <input id="shunxu1" type="hidden" name="itemnoinfos[1].shunxu" value="1"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr0">
                                            <td colspan="13" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="itemnoinfosAdd" style="width:150px;float:left;" value="添加一行"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <c:choose>
                                    <c:when test="${file.size()>0}">
                                        <input type="hidden" id="wfitemnonewchange.attachids" name="attachids" value="${wfitemnonewchangeEntity.attachids }"/>
                                    </c:when>
                                    <c:otherwise>
                                        <input type="hidden" id="wfitemnonewchange.attachids" name="attachids" value=""/>
                                    </c:otherwise>
                                </c:choose>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">備註</td>
                            <td width="90%" class="td_style2">
                                1.此申請單只作為料號新增/異動使用，需求物料品名、品牌、規格需正確詳細填寫，并請合理考量需求日期；<br/>
                                2.直接接觸產品新增：需簽核料號新增說明及《iPEBG庶務類物品料號新增/異動申請表》，由處級主管核准并需會簽三工單位專案負責人/三工單位評估報告；<br/>
                                3.非生產新增：需簽核料號新增說明及《iPEBG庶務類物品料號新增/異動申請表》，並由處級主管核准；<br/>
                                4.直接接觸產品異動：需簽核《iPEBG庶務類物品料號新增/異動申請表》，並由廠級主管核准；<br/>
                                5.非生產異動：需簽核《iPEBG庶務類物品料號新增/異動申請表》，生產簽核至廠級主管/周邊簽核至部級主管；<br/>
                                6.請將簽核完畢之單據至少于需求日期前40天提供至總務，總務將根據各單位送單時間合理進行排配，并有序進行詢價議價或異動作業；<br/>
                                7.為節省ECS費用系統資源，總務採購接單后可對規格及單位做相應調整，以便後續使用。
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','料號新增異動申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="wfitemnonewchange.kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wfitemnonewchangeEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="wfitemnonewchange.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfitemnonewchangeEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'bchargeno','bchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="wfitemnonewchange.bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfitemnonewchangeEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="wfitemnonewchange.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfitemnonewchangeEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'cchargeno','cchargename',$('#applynofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wfitemnonewchange.cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfitemnonewchangeEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wfitemnonewchange.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfitemnonewchangeEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="sgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">三工單位</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(255,'sgchargeTable','sgchargeno','sgchargename',$('#applyfactoryid').combobox('getValue'),'wfitemnonewchange')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="sgchargeno" name="wfitemnonewchange.sgchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['sgchargeno']}"
                                                               readonly
                                                               value="${wfitemnonewchangeEntity.sgchargeno }"/><c:if
                                                            test="${requiredMap['sgchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="sgchargename" name="wfitemnonewchange.sgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['sgchargeno']}"
                                                                value="${wfitemnonewchangeEntity.sgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applynofactoryid').val(),'wfitemnonewchange')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wfitemnonewchange.zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfitemnonewchangeEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="wfitemnonewchange.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfitemnonewchangeEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>


                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zwchchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">總務初核</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(36,'zwchchargeTable','zwchchargeno','zwchchargename',$('#applyfactoryid').combobox('getValue'),'wfitemnonewchange')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zwchchargeno" name="wfitemnonewchange.zwchchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zwchchargeno']}"
                                                               readonly
                                                               value="${wfitemnonewchangeEntity.zwchchargeno }"/><c:if
                                                            test="${requiredMap['zwchchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zwchchargename" name="wfitemnonewchange.zwchchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zwchchargeno']}"
                                                                value="${wfitemnonewchangeEntity.zwchchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zwfhchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">總務複核</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(257,'zwfhchargeTable','zwfhchargeno','zwfhchargename',$('#applyfactoryid').combobox('getValue'),'wfitemnonewchange')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zwfhchargeno" name="wfitemnonewchange.zwfhchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zwfhchargeno']}"
                                                               readonly
                                                               value="${wfitemnonewchangeEntity.zwfhchargeno }"/><c:if
                                                            test="${requiredMap['zwfhchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zwfhchargename" name="wfitemnonewchange.zwfhchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zwfhchargeno']}"
                                                                value="${wfitemnonewchangeEntity.zwfhchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cgzchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">採購製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(273,'cgzchargeTable','cgzchargeno','cgzchargename',$('#applyfactoryid').combobox('getValue'),'wfitemnonewchange')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cgzchargeno" name="wfitemnonewchange.cgzchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cgzchargeno']}"
                                                               readonly
                                                               value="${wfitemnonewchangeEntity.cgzchargeno }"/><c:if
                                                            test="${requiredMap['cgzchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cgzchargename" name="wfitemnonewchange.cgzchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cgzchargeno']}"
                                                                value="${wfitemnonewchangeEntity.cgzchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <%--<table width="18%" style="float: left;margin-left: 5px;"  id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#applydeptno').val(),'zcchargeno','zcchargename',$('#applynofactoryid').val(),'wfitemnonewchange')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="wfitemnonewchange.zcchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wfitemnonewchangeEntity.zcchargeno }"/><c:if test="${requiredMap['zcchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="wfitemnonewchange.zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfitemnonewchangeEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"  id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">產品處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#applydeptno').val(),'pcchargeno','pcchargename',$('#applynofactoryid').val(),'wfitemnonewchange')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="wfitemnonewchange.pcchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${wfitemnonewchangeEntity.pcchargeno }"/><c:if test="${requiredMap['pcchargeno'].equals('true')}"><span id="pcchargeredDiv"><font color="red">*</font></span></c:if>
                                                        /<input id="pcchargename" name="wfitemnonewchange.pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wfitemnonewchangeEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>--%>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cgjdchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">採購接單</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(258,'cgjdchargeTable','cgjdchargeno','cgjdchargename',$('#applyfactoryid').combobox('getValue'),'wfitemnonewchange')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cgjdchargeno" name="wfitemnonewchange.cgjdchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cgjdchargeno']}"
                                                               readonly
                                                               value="${wfitemnonewchangeEntity.cgjdchargeno }"/><c:if
                                                            test="${requiredMap['cgjdchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cgjdchargename" name="wfitemnonewchange.cgjdchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cgjdchargeno']}"
                                                                value="${wfitemnonewchangeEntity.cgjdchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cgzychargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">採購作業</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(259,'cgzychargeTable','cgzychargeno','cgzychargename',$('#applyfactoryid').combobox('getValue'),'wfitemnonewchange')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cgzychargeno" name="wfitemnonewchange.cgzychargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cgzychargeno']}"
                                                               readonly
                                                               value="${wfitemnonewchangeEntity.cgzychargeno }"/><c:if
                                                            test="${requiredMap['cgzychargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="cgzychargename" name="wfitemnonewchange.cgzychargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cgzychargeno']}"
                                                                value="${wfitemnonewchangeEntity.cgzychargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfitemnonewchangeEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="canelTask('${wfitemnonewchangeEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
    <div id="dlg"></div>
</form>
</div>
<script src='${ctx}/static/js/safety/wfitemnonewchange.js?random=<%= Math.random()%>'></script>
</body>
</html>