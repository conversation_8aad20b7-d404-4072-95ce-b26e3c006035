<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>料號新增異動申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfitemnonewchange/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfitemnonewchangeEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfitemnonewchangeEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">料號新增異動申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfitemnonewchangeEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfitemnonewchangeEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfitemnonewchangeEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfitemnonewchangeEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfitemnonewchangeEntity.makerno}/${wfitemnonewchangeEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">申請人詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號</td>
                            <td width="6%">${wfitemnonewchangeEntity.applyno}</td>
                            <td width="4%">申請人</td>
                            <td width="6%">${wfitemnonewchangeEntity.applyname}</td>
                            <td width="4%">單位代碼</td>
                            <td width="6%">${wfitemnonewchangeEntity.applydeptno}</td>
                            <td width="4%">廠區</td>
                            <td width="7%">${wfitemnonewchangeEntity.applyfactoryname}</td>
                        </tr>
                        <tr align="center">
                            <td>費用代碼</td>
                            <td>${wfitemnonewchangeEntity.applycostno }</td>
                            <td>申請單位</td>
                            <td colspan="3">${wfitemnonewchangeEntity.applydeptname }</td>
                            <td>聯繫方式</td>
                            <td>${wfitemnonewchangeEntity.applytel }</td>
                        </tr>
                        <tr align="center">
                            <td>聯繫郵箱</td>
                            <td colspan="3">${wfitemnonewchangeEntity.applyemail}</td>
                            <td>料號類別</td>
                            <td colspan="3">${wfitemnonewchangeEntity.partname }</td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">料號信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="8" width="100%">
                                <table id="wfitemnoinfosTable" width="100%">
                                    <tr align="center">
                                        <td width="30px">序號</td>
                                        <td width="80px">料號</td>
                                        <td width="80px">品名</td>
                                        <td width="50px">品牌</td>
                                        <td width="80px">規格及型號</td>
                                        <td width="50px">單位</td>
                                        <td width="60px">數量</td>
                                        <td width="100px">預計需求日期</td>
                                        <td width="80px">請購週期</td>
                                        <td width="100px">用途說明</td>
                                        <td width="100px">作業類別</td>
                                        <td width="100px">作業原因</td>
                                    </tr>
                                    <tbody id="info_Body0">
                                    <c:if test="${wfitemnoinfos!=null&&wfitemnoinfos.size()>0}">
                                        <c:forEach items="${wfitemnoinfos}" var="itemnoinfos" varStatus="status">
                                            <tr align="center" id="itemnoinfos${status.index+1}">
                                                <td>${status.index+1}</td>
                                                <td>${itemnoinfos.itemno}</td>
                                                <td>${itemnoinfos.productname}</td>
                                                <td>${itemnoinfos.brand}</td>
                                                <td>${itemnoinfos.model}</td>
                                                <td>${itemnoinfos.unit}</td>
                                                <td>${itemnoinfos.itemnumber}</td>
                                                <td>
                                                    <input id="expectdate${status.index+1}" name="itemnoinfos[${status.index+1}].expectdate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                                           data-options="width: 100" disabled value="<fmt:formatDate value="${itemnoinfos.expectdate}"/>"/>
                                                </td>
                                                <td>${itemnoinfos.cycle}</td>
                                                <td>${itemnoinfos.useage}</td>
                                                <td>${itemnoinfos.jobtype}</td>
                                                <td>${itemnoinfos.jobreason}</td>
                                            </tr>
                                        </c:forEach>
                                    </c:if>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <c:if test="${wfitemnonewchangeEntity.receiveno!=null&&wfitemnonewchangeEntity.receiveno!=''}">
                            <tr>
                                <td colspan="11" class="td_style1">採購接單信息</td>
                            </tr>
                            <tr align="center">
                                <td>接單日期</td>
                                <td colspan="2">
                                    <input name="wfitemnonewchange.receivedate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                           data-options="width: 100" disabled value="<fmt:formatDate value="${wfitemnonewchangeEntity.receivedate}"/>"/>
                                </td>
                                <td>採購接單人工號</td>
                                <td colspan="2">${wfitemnonewchangeEntity.receiveno }</td>
                                <td>採購接單人員姓名</td>
                                <td colspan="4">${wfitemnonewchangeEntity.receivename }</td>
                            </tr>
                        </c:if>
                        <c:if test="${wfitemnonewchangeEntity.zyfxno!=null&&wfitemnonewchangeEntity.zyfxno!=''}">
                            <tr>
                                <td colspan="11" class="td_style1">採購作業信息</td>
                            </tr>
                            <tr align="center">
                                <td width="100px">資源分析</td>
                                <td width="100px">負責人工號</td>
                                <td width="80px">${wfitemnonewchangeEntity.zyfxno }</td>
                                <td width="100px">負責人姓名</td>
                                <td width="80px">${wfitemnonewchangeEntity.zyfxname }</td>
                                <td width="80px">開始日期</td>
                                <td width="120px">
                                    <input id="zyfxstartdate" name="wfitemnonewchange.zyfxstartdate" class="Wdate"
                                           style="width: 100px;" disabled
                                           value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.zyfxstartdate}"/>"/>
                                </td>
                                <td width="80px">結束日期</td>
                                <td width="120px">
                                    <input id="zyfxenddate" name="wfitemnonewchange.zyfxenddate" class="Wdate"
                                           style="width: 100px;" disabled
                                           value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.zyfxenddate}"/>"/>
                                </td>
                                <td width="80px">備註</td>
                                <td>${wfitemnonewchangeEntity.zyfxuse}</td>
                            </tr>
                            <tr align="center">
                                <td>廠商詢價報價</td>
                                <td>負責人工號</td>
                                <td>${wfitemnonewchangeEntity.csxjno}</td>
                                <td>負責人姓名</td>
                                <td>${wfitemnonewchangeEntity.csxjname}</td>
                                <td>開始日期</td>
                                <td>
                                    <input id="csxjstartdate" name="wfitemnonewchange.csxjstartdate" class="Wdate"
                                           style="width: 100px;" disabled
                                           value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.csxjstartdate}"/>"/>
                                </td>
                                <td>結束日期</td>
                                <td>
                                    <input id="csxjenddate" name="wfitemnonewchange.csxjenddate" class="Wdate"
                                           style="width: 100px;" disabled
                                           value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.csxjenddate}"/>"/>
                                </td>
                                <td>備註</td>
                                <td>${wfitemnonewchangeEntity.csxjuse }</td>
                            </tr>
                            <tr align="center">
                                <td>紙質議價表簽核</td>
                                <td>負責人工號</td>
                                <td>${wfitemnonewchangeEntity.zzyjno }</td>
                                <td>負責人姓名</td>
                                <td>${wfitemnonewchangeEntity.zzyjname }</td>
                                <td>開始日期</td>
                                <td>
                                    <input id="zzyjstartdate" name="wfitemnonewchange.zzyjstartdate" class="Wdate"
                                           style="width: 100px;" disabled
                                           value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.zzyjstartdate}"/>"/>
                                </td>
                                <td>結束日期</td>
                                <td>
                                    <input id="zzyjenddate" name="wfitemnonewchange.zzyjenddate" class="Wdate"
                                           style="width: 100px;" disabled
                                           value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.zzyjenddate}"/>"/>
                                </td>
                                <td>備註</td>
                                <td>${wfitemnonewchangeEntity.zzyjuse }</td>
                            </tr>
                            <tr align="center">
                                <td>系統料號單簽核</td>
                                <td>負責人工號</td>
                                <td>${wfitemnonewchangeEntity.xtlhno }</td>
                                <td>負責人姓名</td>
                                <td>${wfitemnonewchangeEntity.xtlhname }</td>
                                <td>開始日期</td>
                                <td>
                                    <input id="xtlhstartdate" name="wfitemnonewchange.xtlhstartdate" class="Wdate"
                                           style="width: 100px;" disabled
                                           value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.xtlhstartdate}"/>"/>
                                </td>
                                <td>結束日期</td>
                                <td>
                                    <input id="xtlhenddate" name="wfitemnonewchange.xtlhenddate" class="Wdate"
                                           style="width: 100px;" disabled
                                           value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.xtlhenddate}"/>"/>
                                </td>
                                <td>備註</td>
                                <td>${wfitemnonewchangeEntity.xtlhuse }</td>
                            </tr>
                            <tr align="center">
                                <td>商務總處單據簽核</td>
                                <td>負責人工號</td>
                                <td>${wfitemnonewchangeEntity.swzcno }</td>
                                <td>負責人姓名</td>
                                <td>${wfitemnonewchangeEntity.swzcname }</td>
                                <td>開始日期</td>
                                <td>
                                    <input id="swzcstartdate" name="wfitemnonewchange.swzcstartdate" class="Wdate"
                                           style="width: 100px;" disabled
                                           value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.swzcstartdate}"/>"/>
                                </td>
                                <td>結束日期</td>
                                <td>
                                    <input id="swzcenddate" name="wfitemnonewchange.swzcenddate" class="Wdate"
                                           style="width: 100px;" disabled
                                           value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.swzcenddate}"/>"/>
                                </td>
                                <td>備註</td>
                                <td>${wfitemnonewchangeEntity.swzcuse }</td>
                            </tr>
                            <tr>
                                <td colspan="8" class="td_style1">採購結案信息</td>
                            </tr>
                            <tr align="center">
                                <td>結案日期</td>
                                <td colspan="2">
                                    <input id="caseclosedate" name="wfitemnonewchange.caseclosedate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd" disabled
                                           data-options="width: 100" value="<fmt:formatDate value="${wfitemnonewchangeEntity.caseclosedate}"/>"/>
                                </td>
                                <td>採購結案人員工號</td>
                                <td colspan="2">${wfitemnonewchangeEntity.casecloseno }</td>
                                <td>採購結案人員姓名</td>
                                <td colspan="4">${wfitemnonewchangeEntity.caseclosename }</td>
                            </tr>
                        </c:if>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="92%" class="td_style1" colspan="10">
                                <input type="hidden" id="attachids" name="attachids" value="${wfitemnonewchangeEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">備註</td>
                            <td width="92%" colspan="10" style="text-align: left">
                                1.此申請單只作為料號新增/異動使用，需求物料品名、品牌、規格需正確詳細填寫，并請合理考量需求日期；<br/>
                                2.直接接觸產品新增：需簽核料號新增說明及《iPEBG庶務類物品料號新增/異動申請表》，由處級主管核准并需會簽三工單位專案負責人/三工單位評估報告；<br/>
                                3.非生產新增：需簽核料號新增說明及《iPEBG庶務類物品料號新增/異動申請表》，並由處級主管核准；<br/>
                                4.直接接觸產品異動：需簽核《iPEBG庶務類物品料號新增/異動申請表》，並由廠級主管核准；<br/>
                                5.非生產異動：需簽核《iPEBG庶務類物品料號新增/異動申請表》，生產簽核至廠級主管/周邊簽核至部級主管；<br/>
                                6.請將簽核完畢之單據至少于需求日期前40天提供至總務，總務將根據各單位送單時間合理進行排配，并有序進行詢價議價或異動作業；<br/>
                                7.為節省ECS費用系統資源，總務採購接單后可對規格及單位做相應調整，以便後續使用。
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="11">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','料號新增異動申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfitemnonewchangeEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfitemnonewchangeEntity.workstatus!=null&&(wfitemnonewchangeEntity.workstatus==3||wfitemnonewchangeEntity.workstatus==2)}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/safety/wfitemnonewchange.js?random=<%= Math.random()%>'></script>
</body>
</html>