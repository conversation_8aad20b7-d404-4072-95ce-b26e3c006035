<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>料號新增異動申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/wfitemnonewchange/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfitemnonewchangeEntity.id }"/>
    <input id="serialno" name="wfitemnonewchange.serialno" type="hidden" value="${wfitemnonewchangeEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">料號新增異動申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfitemnonewchangeEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfitemnonewchangeEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfitemnonewchangeEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfitemnonewchangeEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfitemnonewchangeEntity.makerno}/${wfitemnonewchangeEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">申請人詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="4%">申請人工號</td>
                            <td width="6%" class="td_style2">${wfitemnonewchangeEntity.applyno}</td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style2">${wfitemnonewchangeEntity.applyname}</td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style2">${wfitemnonewchangeEntity.applydeptno}</td>
                            <td width="4%">廠區</td>
                            <td width="7%" class="td_style2">${wfitemnonewchangeEntity.applyfactoryname}</td>
                        </tr>
                        <tr align="center">
                            <td>費用代碼</td>
                            <td class="td_style2">${wfitemnonewchangeEntity.applycostno }</td>
                            <td>申請單位</td>
                            <td colspan="3" class="td_style2">${wfitemnonewchangeEntity.applydeptname }</td>
                            <td>聯繫方式</td>
                            <td class="td_style2">${wfitemnonewchangeEntity.applytel }</td>
                        </tr>
                        <tr align="center">
                            <td>聯繫郵箱</td>
                            <td class="td_style2" colspan="3">${wfitemnonewchangeEntity.applyemail}</td>
                            <td>料號類別</td>
                            <td colspan="3" class="td_style2">${wfitemnonewchangeEntity.partname }</td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">料號信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="8" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="wfitemnoinfosTable" width="120%">
                                        <tr align="center">
                                            <td width="30px">序號</td>
                                            <td width="80px">料號</td>
                                            <td width="80px">品名</td>
                                            <td width="50px">品牌</td>
                                            <td width="80px">規格及型號</td>
                                            <td width="50px">單位</td>
                                            <td width="60px">數量</td>
                                            <td width="100px">預計需求日期</td>
                                            <td width="80px">請購週期</td>
                                            <td width="100px">用途說明</td>
                                            <td width="100px">作業類別</td>
                                            <td width="100px">作業原因</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wfitemnoinfos!=null&&wfitemnoinfos.size()>0}">
                                            <c:forEach items="${wfitemnoinfos}" var="itemnoinfos" varStatus="status">
                                                <tr align="center" id="itemnoinfos${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>${itemnoinfos.itemno}</td>
                                                    <td>${itemnoinfos.productname}</td>
                                                    <td>${itemnoinfos.brand}</td>
                                                    <td>${itemnoinfos.model}</td>
                                                    <td>${itemnoinfos.unit}</td>
                                                    <td>${itemnoinfos.itemnumber}</td>
                                                    <td>
                                                        <input id="expectdate${status.index+1}" name="itemnoinfos[${status.index+1}].expectdate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                                               data-options="width: 100" disabled value="<fmt:formatDate value="${itemnoinfos.expectdate}"/>"/>
                                                    </td>
                                                    <td>${itemnoinfos.cycle}</td>
                                                    <td>${itemnoinfos.useage}</td>
                                                    <td>${itemnoinfos.jobtype}</td>
                                                    <td>${itemnoinfos.jobreason}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>

                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <c:choose>
                            <c:when test="${not empty nodeName && '採購接單' eq nodeName}">
                                <tr>
                                    <td colspan="11" class="td_style1">採購接單信息</td>
                                </tr>
                                <tr align="center">
                                    <td>接單日期</td>
                                    <td>
                                        <input id="receivedate" name="wfitemnonewchange.receivedate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                               data-options="required:true,width: 120" value="<fmt:formatDate value="${wfitemnonewchangeEntity.receivedate}"/>"/>
                                    </td>
                                    <td>採購接單人工號</td>
                                    <td>
                                        <input id="receiveno" name="wfitemnonewchange.receiveno" class="easyui-validatebox"
                                               data-options="width: 100,required:true"
                                               value="${wfitemnonewchangeEntity.receiveno }" onblur="queryUserInfo('receive');"/>
                                    </td>
                                    <td>採購接單人員姓名</td>
                                    <td >
                                        <input id="receivename" name="wfitemnonewchange.receivename"
                                               class="easyui-validatebox inputCss"
                                               data-options="width:100" readonly value="${wfitemnonewchangeEntity.receivename }"/>
                                    </td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <c:if test="${nodeOrder ge 10}">
                                    <tr>
                                        <td colspan="11" class="td_style1">採購接單信息</td>
                                    </tr>
                                    <tr align="center">
                                        <td>接單日期</td>
                                        <td colspan="2">
                                            <input name="wfitemnonewchange.receivedate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                                   data-options="width: 120" disabled value="<fmt:formatDate value="${wfitemnonewchangeEntity.receivedate}"/>"/>
                                        </td>
                                        <td>採購接單人工號</td>
                                        <td colspan="2">${wfitemnonewchangeEntity.receiveno }</td>
                                        <td>採購接單人員姓名</td>
                                        <td colspan="4">${wfitemnonewchangeEntity.receivename }</td>
                                    </tr>
                                </c:if>
                            </c:otherwise>
                        </c:choose>
                        <c:choose>
                            <c:when test="${not empty nodeName && '採購作業' eq nodeName}">
                                <tr>
                                    <td colspan="11" class="td_style1">採購作業信息</td>
                                </tr>
                                <tr align="center">
                                    <td>資源分析</td>
                                    <td>負責人工號<font color="red">*</font></td>
                                    <td>
                                        <input id="zyfxno" name="wfitemnonewchange.zyfxno" class="easyui-validatebox"
                                               data-options="required:true,width: 100"
                                               value="${wfitemnonewchangeEntity.zyfxno }" onblur="queryUserInfo('zyfx');"/>
                                    </td>
                                    <td>負責人姓名</td>
                                    <td>
                                        <input id="zyfxname" name="wfitemnonewchange.zyfxname"
                                               class="easyui-validatebox inputCss"
                                               data-options="width:100" readonly value="${wfitemnonewchangeEntity.zyfxname }"/>
                                    </td>
                                    <td>開始日期<font color="red">*</font></td>
                                    <td>
                                        <input id="zyfxstartdate" name="wfitemnonewchange.zyfxstartdate" class="Wdate"
                                               style="width: 100px;"
                                               value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.zyfxstartdate}"/>"
                                               onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/>
                                    </td>
                                    <td>結束日期<font color="red">*</font></td>
                                    <td>
                                        <input id="zyfxenddate" name="wfitemnonewchange.zyfxenddate" class="Wdate"
                                               style="width: 100px;"
                                               value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.zyfxenddate}"/>"
                                               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'zyfxstartdate\')}'})"/>
                                    </td>
                                    <td>備註</td>
                                    <td>
                                        <input id="zyfxuse" name="wfitemnonewchange.zyfxuse" class="easyui-validatebox"
                                               data-options="width: 100,required:true" value="${wfitemnonewchangeEntity.zyfxuse }"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>廠商詢價報價</td>
                                    <td>負責人工號<font color="red">*</font></td>
                                    <td>
                                        <input id="csxjno" name="wfitemnonewchange.csxjno" class="easyui-validatebox"
                                               data-options="width: 100,required:true"
                                               value="${wfitemnonewchangeEntity.csxjno }" onblur="queryUserInfo('csxj');"/>
                                    </td>
                                    <td>負責人姓名</td>
                                    <td>
                                        <input id="csxjname" name="wfitemnonewchange.csxjname"
                                               class="easyui-validatebox inputCss"
                                               data-options="width:100" readonly value="${wfitemnonewchangeEntity.csxjname }"/>
                                    </td>
                                    <td>開始日期<font color="red">*</font></td>
                                    <td>
                                        <input id="csxjstartdate" name="wfitemnonewchange.csxjstartdate" class="Wdate"
                                               style="width: 100px;"
                                               value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.csxjstartdate}"/>"
                                               onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/>
                                    </td>
                                    <td>結束日期<font color="red">*</font></td>
                                    <td>
                                        <input id="csxjenddate" name="wfitemnonewchange.csxjenddate" class="Wdate"
                                               style="width: 100px;"
                                               value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.csxjenddate}"/>"
                                               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'csxjstartdate\')}'})"/>
                                    </td>
                                    <td>備註</td>
                                    <td>
                                        <input id="csxjuse" name="wfitemnonewchange.csxjuse" class="easyui-validatebox"
                                               data-options="width: 100,required:true" value="${wfitemnonewchangeEntity.csxjuse }"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>紙質議價表簽核</td>
                                    <td>負責人工號<font color="red">*</font></td>
                                    <td>
                                        <input id="zzyjno" name="wfitemnonewchange.zzyjno" class="easyui-validatebox"
                                               data-options="width: 100,required:true"
                                               value="${wfitemnonewchangeEntity.zzyjno }" onblur="queryUserInfo('zzyj');"/>
                                    </td>
                                    <td>負責人姓名</td>
                                    <td>
                                        <input id="zzyjname" name="wfitemnonewchange.zzyjname"
                                               class="easyui-validatebox inputCss"
                                               data-options="width:100" readonly value="${wfitemnonewchangeEntity.zzyjname }"/>
                                    </td>
                                    <td>開始日期<font color="red">*</font></td>
                                    <td>
                                        <input id="zzyjstartdate" name="wfitemnonewchange.zzyjstartdate" class="Wdate"
                                               style="width: 100px;"
                                               value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.zzyjstartdate}"/>"
                                               onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/>
                                    </td>
                                    <td>結束日期<font color="red">*</font></td>
                                    <td>
                                        <input id="zzyjenddate" name="wfitemnonewchange.zzyjenddate" class="Wdate"
                                               style="width: 100px;"
                                               value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.zzyjenddate}"/>"
                                               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'zzyjstartdate\')}'})"/>
                                    </td>
                                    <td>備註</td>
                                    <td>
                                        <input id="zzyjuse" name="wfitemnonewchange.zzyjuse" class="easyui-validatebox"
                                               data-options="width: 100,required:true" value="${wfitemnonewchangeEntity.zzyjuse }"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>系統料號單簽核</td>
                                    <td>負責人工號<font color="red">*</font></td>
                                    <td>
                                        <input id="xtlhno" name="wfitemnonewchange.xtlhno" class="easyui-validatebox"
                                               data-options="width: 100,required:true"
                                               value="${wfitemnonewchangeEntity.xtlhno }" onblur="queryUserInfo('xtlh');"/>
                                    </td>
                                    <td>負責人姓名</td>
                                    <td>
                                        <input id="xtlhname" name="wfitemnonewchange.xtlhname"
                                               class="easyui-validatebox inputCss"
                                               data-options="width:100" readonly value="${wfitemnonewchangeEntity.xtlhname }"/>
                                    </td>
                                    <td>開始日期<font color="red">*</font></td>
                                    <td>
                                        <input id="xtlhstartdate" name="wfitemnonewchange.xtlhstartdate" class="Wdate"
                                               style="width: 100px;"
                                               value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.xtlhstartdate}"/>"
                                               onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/>
                                    </td>
                                    <td>結束日期<font color="red">*</font></td>
                                    <td>
                                        <input id="xtlhenddate" name="wfitemnonewchange.xtlhenddate" class="Wdate"
                                               style="width: 100px;"
                                               value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.xtlhenddate}"/>"
                                               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'xtlhstartdate\')}'})"/>
                                    </td>
                                    <td>備註</td>
                                    <td>
                                        <input id="xtlhuse" name="wfitemnonewchange.xtlhuse" class="easyui-validatebox"
                                               data-options="width: 100,required:true" value="${wfitemnonewchangeEntity.xtlhuse }"/>
                                    </td>
                                </tr>
                                <tr align="center">
                                    <td>商務總處單據簽核</td>
                                    <td>負責人工號<font color="red">*</font></td>
                                    <td>
                                        <input id="swzcno" name="wfitemnonewchange.swzcno" class="easyui-validatebox"
                                               data-options="width: 100,required:true"
                                               value="${wfitemnonewchangeEntity.swzcno }" onblur="queryUserInfo('swzc');"/>
                                    </td>
                                    <td>負責人姓名</td>
                                    <td>
                                        <input id="swzcname" name="wfitemnonewchange.swzcname"
                                               class="easyui-validatebox inputCss"
                                               data-options="width:100" readonly value="${wfitemnonewchangeEntity.swzcname }"/>
                                    </td>
                                    <td>開始日期<font color="red">*</font></td>
                                    <td>
                                        <input id="swzcstartdate" name="wfitemnonewchange.swzcstartdate" class="Wdate"
                                               style="width: 100px;"
                                               value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.swzcstartdate}"/>"
                                               onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/>
                                    </td>
                                    <td>結束日期<font color="red">*</font></td>
                                    <td>
                                        <input id="swzcenddate" name="wfitemnonewchange.swzcenddate" class="Wdate"
                                               style="width: 100px;"
                                               value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfitemnonewchangeEntity.swzcenddate}"/>"
                                               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'swzcstartdate\')}'})"/>
                                    </td>
                                    <td>備註</td>
                                    <td>
                                        <input id="swzcuse" name="wfitemnonewchange.swzcuse" class="easyui-validatebox"
                                               data-options="width: 100,required:true" value="${wfitemnonewchangeEntity.swzcuse }"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="11" class="td_style1">採購結案信息</td>
                                </tr>
                                <tr align="center">
                                    <td>結案日期<font color="red">*</font></td>
                                    <td colspan="2">
                                        <input id="caseclosedate" name="wfitemnonewchange.caseclosedate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                               data-options="required:true,width: 120" value="<fmt:formatDate value="${wfitemnonewchangeEntity.caseclosedate}"/>"/>
                                    </td>
                                    <td>採購結案人員工號<font color="red">*</font></td>
                                    <td colspan="2">
                                        <input id="casecloseno" name="wfitemnonewchange.casecloseno" class="easyui-validatebox"
                                               data-options="width: 100,required:true"
                                               value="${wfitemnonewchangeEntity.casecloseno }" onblur="queryUserInfo('caseclose');"/>
                                    </td>
                                    <td>採購結案人員姓名</td>
                                    <td colspan="4">
                                        <input id="caseclosename" name="wfitemnonewchange.caseclosename"
                                               class="easyui-validatebox inputCss"
                                               data-options="width:100" readonly value="${wfitemnonewchangeEntity.caseclosename }"/>
                                    </td>
                                </tr>
                            </c:when>
                        </c:choose>
                        <tr align="center">
                            <td width="8%">附件</td>
                            <td width="92%" class="td_style1" colspan="10">
                                <input type="hidden" id="attachids" name="attachids" value="${wfitemnonewchangeEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">備註</td>
                            <td width="92%" class="td_style2" colspan="10">
                                1.此申請單只作為料號新增/異動使用，需求物料品名、品牌、規格需正確詳細填寫，并請合理考量需求日期；<br/>
                                2.直接接觸產品新增：需簽核料號新增說明及《iPEBG庶務類物品料號新增/異動申請表》，由處級主管核准并需會簽三工單位專案負責人/三工單位評估報告；<br/>
                                3.非生產新增：需簽核料號新增說明及《iPEBG庶務類物品料號新增/異動申請表》，並由處級主管核准；<br/>
                                4.直接接觸產品異動：需簽核《iPEBG庶務類物品料號新增/異動申請表》，並由廠級主管核准；<br/>
                                5.非生產異動：需簽核《iPEBG庶務類物品料號新增/異動申請表》，生產簽核至廠級主管/周邊簽核至部級主管；<br/>
                                6.請將簽核完畢之單據至少于需求日期前40天提供至總務，總務將根據各單位送單時間合理進行排配，并有序進行詢價議價或異動作業；<br/>
                                7.為節省ECS費用系統資源，總務採購接單后可對規格及單位做相應調整，以便後續使用。
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%" class="td_style2" colspan="10">
                                <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox" style="width:1000px;height:60px;" rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                        <c:choose>
                            <c:when test="${not empty nodeName &&'採購接單' eq nodeName}">
                             <tr align="center">
                                <td colspan="11" style="border:none;text-align:center;margin-top:10px">
                                    <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"  perCall="jdupdate" serialNo="${wfitemnonewchangeEntity.serialno}"></fox:action>
                                </td>
                             </tr>
                            </c:when>
                            <c:when test="${not empty nodeName &&'採購作業' eq nodeName}">
                             <tr align="center">
                                <td colspan="11" style="border:none;text-align:center;margin-top:10px">
                                    <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-add'" style="width: 150px;"  onclick="updateRate();">保存採購信息</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                </td>
                             </tr>
                             <tr align="center">
                                 <td colspan="11" style="border:none;text-align:center;margin-top:10px">
                                    <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"  perCall="zyupdate" serialNo="${wfitemnonewchangeEntity.serialno}"></fox:action>
                                 </td>
                             </tr>
                            </c:when>
                            <c:otherwise>
                             <tr align="center">
                                <td colspan="11" style="border:none;text-align:center;margin-top:10px">
                                    <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;" serialNo="${wfitemnonewchangeEntity.serialno}"></fox:action>
                                </td>
                             </tr>
                            </c:otherwise>
                        </c:choose>


                        <tr>
                            <th style="text-align:left;" colspan="11>&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','料號新增異動申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="11" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="11" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfitemnonewchangeEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/safety/wfitemnonewchange.js?random=<%= Math.random()%>'></script>
</body>
</html>