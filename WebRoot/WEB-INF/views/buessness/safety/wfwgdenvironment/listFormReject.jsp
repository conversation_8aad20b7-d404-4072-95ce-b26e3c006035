<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>環保三同時申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
        #projectdescription::-webkit-input-placeholder::after {
            display:block;
            content:"prompt:'1.項目規模﹑特點﹕\A 2.工藝流程：\A3.主要設備﹑設施(含環保設施)：'";
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfwgdenvironment/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfwgdenvironmentEntity.id }"/>
    <input id="serialno" name="wfwgdenvironment.serialno" type="hidden" value="${wfwgdenvironmentEntity.serialno }"/>
    <input id="makerno" name="wfwgdenvironment.makerno" type="hidden" value="${wfwgdenvironmentEntity.makerno }"/>
    <input id="makername" name="wfwgdenvironment.makername" type="hidden" value="${wfwgdenvironmentEntity.makername }"/>
    <input id="makerdeptno" name="wfwgdenvironment.makerdeptno" type="hidden" value="${wfwgdenvironmentEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfwgdenvironment.makerfactoryid" type="hidden" value="${wfwgdenvironmentEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">環保三同時申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfwgdenvironmentEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfwgdenvironmentEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfwgdenvironmentEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfwgdenvironmentEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfwgdenvironmentEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfwgdenvironmentEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfwgdenvironmentEntity.makerno}/${wfwgdenvironmentEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="applyno" name="wfwgdenvironment.applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfwgdenvironmentEntity.applyno }"
                                       onblur="queryUserInfo('apply');"/>
                            </td>
                            <td width="8%">申請人</td>
                            <td width="10%" class="td_style1">
                                <input id="applyname" name="wfwgdenvironment.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wfwgdenvironmentEntity.applyname }"/>
                            </td>
                            <td width="8%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="applydeptno" name="wfwgdenvironment.applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfwgdenvironmentEntity.applydeptno }"/>
                            </td>
                            <td width="8%">費用代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="applycostno" name="wfwgdenvironment.applycostno"
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfwgdenvironmentEntity.applycostno }"/>
                            </td>
                            <td width="8%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="15%" class="td_style1">
                                <input id="applyfactoryid" name="wfwgdenvironment.applyfactoryid"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${wfwgdenvironmentEntity.applyfactoryid }"
                                       data-options="width: 120,validType:'comboxValidate[\'applyfactoryid\',\'请選擇所在廠區\']',onSelect:function(){onchangeFactory('apply');}"/>
                                <input id="applyfactoryname" name="wfwgdenvironment.applyfactoryname" type="hidden" value="${wfwgdenvironmentEntity.applyfactoryname }"/>
                                <input id="applynofactoryid" name="wfwgdenvironment.applynofactoryid" type="hidden" value="${wfwgdenvironmentEntity.applynofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="wfwgdenvironment.applydeptname"
                                       class="easyui-validatebox" data-options="width: 320,required:true"
                                       value="${wfwgdenvironmentEntity.applydeptname }"/>
                            </td>
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applytel" name="wfwgdenvironment.applytel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfwgdenvironmentEntity.applytel }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="wfwgdenvironment.applyemail" class="easyui-validatebox"
                                       value="${wfwgdenvironmentEntity.applyemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td>項目名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="projectname" name="wfwgdenvironment.projectname"
                                       class="easyui-validatebox" data-options="width: 350,required:true"
                                       value="${wfwgdenvironmentEntity.projectname }"/>
                            </td>
                            <td>建設單位&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="buildcompany" name="wfwgdenvironment.buildcompany"
                                       class="easyui-validatebox" data-options="width: 200,required:true"
                                       value="${wfwgdenvironmentEntity.buildcompany }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>項目地點&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="projectaddress" name="wfwgdenvironment.projectaddress"
                                       class="easyui-validatebox" data-options="width: 200,required:true"
                                       value="${wfwgdenvironmentEntity.projectaddress }"/>
                            </td>
                            <td colspan="2">預計施工工期&nbsp;<font color="red">*</font></td>
                            <td colspan="4" class="td_style1">
                                &nbsp;&nbsp;
                                <input id="expectstartdate" name="wfwgdenvironment.expectstartdate" class="wfwgdenvironment.expectstartdate" data-options="width:150,required:true"
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfwgdenvironmentEntity.expectstartdate}"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                                &nbsp;&nbsp;~&nbsp;&nbsp;
                                <input id="expectenddate" name="wfwgdenvironment.expectenddate" class="wfwgdenvironment.expectstartdate" data-options="width:150,required:true"
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfwgdenvironmentEntity.expectenddate}"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'expectstartdate\')}'})" />
                            </td>
                        </tr>
                        <tr align="center">
                            <td>項目內容說明&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style2">
                                    <textarea id="projectdescription" name="wfwgdenvironment.projectdescription" class="easyui-validatebox"
                                              oninput="return LessThan(this);"
                                              onchange="return LessThan(this);"
                                              onpropertychange="return LessThan(this);"
                                              maxlength="1300" onblur="onblurr();"  onfocus="onfocuss();"
                                              style="width:99%;height:150px;" data-options="required:true,validType:'length[0,1300]'"
                                              rows="5" cols="6">${wfwgdenvironmentEntity.projectdescription }</textarea><span id="txtNum"></span>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">廢水：（需附廢水檢測報告）</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="waterTable" width="100%">
                                        <tr align="center">
                                            <td width="30px">項次</td>
                                            <td width="100px">污染源位置&nbsp;<font color="red">*</font></td>
                                            <td width="100px">排水製程/設備&nbsp;<font color="red">*</font></td>
                                            <td width="100px">槽位&nbsp;<font color="red">*</font></td>
                                            <td width="100px">藥劑及濃度&nbsp;<font color="red">*</font></td>
                                            <td width="80px">排放方式及週期&nbsp;<font color="red">*</font></td>
                                            <td width="100px">廢水產生量&nbsp;<font color="red">*</font></td>
                                            <td width="100px">預計排水日期&nbsp;<font color="red">*</font></td>
                                            <td width="100px">備註</td>
                                            <td width="50px">操作</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wfwwater!=null&&wfwwater.size()>0}">
                                            <c:forEach items="${wfwwater}" var="wateritems" varStatus="status">
                                                <tr align="center" id="wateritems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <input id="wplace${status.index+1}"
                                                               name="wateritems[${status.index+1}].wplace" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${wateritems.wplace}"/>
                                                    </td>
                                                    <td>
                                                        <input id="wprocess${status.index+1}"
                                                               name="wateritems[${status.index+1}].wprocess" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${wateritems.wprocess}"/>
                                                    </td>
                                                    <td>
                                                        <input id="wposition${status.index+1}"
                                                               name="wateritems[${status.index+1}].wposition" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${wateritems.wposition}"/>
                                                    </td>
                                                    <td>
                                                        <input id="wdrug${status.index+1}"
                                                               name="wateritems[${status.index+1}].wdrug" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${wateritems.wdrug}"/>
                                                    </td>
                                                    <td>
                                                        <input id="wcycle${status.index+1}"
                                                               name="wateritems[${status.index+1}].wcycle" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${wateritems.wcycle}"/>
                                                    </td>
                                                    <td>
                                                        <input id="wyield${status.index+1}"
                                                               name="wateritems[${status.index+1}].wyield" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${wateritems.wyield}"/>
                                                    </td>
                                                    <td>
                                                        <input id="wdate${status.index+1}" name="wateritems[${status.index+1}].wdate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                                               data-options="required:true,width: 100"
                                                               value="<fmt:formatDate value="${wateritems.wdate}"/>"/>
                                                    </td>
                                                    <td>
                                                        <input id="wremarks${status.index+1}"
                                                               name="wateritems[${status.index+1}].wremarks" class="easyui-validatebox" style="width:100px;"
                                                               value="${wateritems.wremarks}"/>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="waterdeltr(${status.index+1});return false;"/>
                                                        <input id="wshunxu${status.index+1}" type="hidden" name="wateritems[${status.index+1}].shunxu" value="${status.index+1}"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${wfwwater.size()==0 || wfwwater==null}">
                                            <tr align="center" id="wateritems1">
                                                <td>1</td>
                                                <td>
                                                    <input id="wplace1" name="wateritems[1].wplace" class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="wprocess1" name="wateritems[1].wprocess" class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="wposition1" name="wateritems[1].wposition" class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="wdrug1" name="wateritems[1].wdrug" class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="wcycle1" name="wateritems[1].wcycle" class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="wyield1" name="wateritems[1].wyield" class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="wdate1" name="wateritems[1].wdate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                                           data-options="required:true,width: 100" value=""/>
                                                </td>
                                                <td>
                                                    <input id="wremarks1" name="wateritems[1].wremarks" class="easyui-validatebox" style="width:100px;" value=""/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="waterdeltr(1);return false;"/>
                                                    <input id="wshunxu1" type="hidden" name="wfmachineinoutitems[1].shunxu" value="1"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="wnottr0">
                                            <td colspan="10" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="waterAdd" style="width:150px;float:left;" value="添加一行"/>
                                            </td>
                                        </tr>

                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">廢氣：</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="gasTable" width="100%">
                                        <tr align="center">
                                            <td width="30px">項次</td>
                                            <td width="100px">污染源位置&nbsp;<font color="red">*</font></td>
                                            <td width="100px">製程名稱&nbsp;<font color="red">*</font></td>
                                            <td width="100px">化學品名稱&nbsp;<font color="red">*</font></td>
                                            <td width="100px">化學品用量（t/月）<font color="red">*</font></td>
                                            <td width="150px">化學品主要成份&nbsp;<font color="red">*</font></td>
                                            <td width="100px">排風量&nbsp;<font color="red">*</font></td>
                                            <td width="100px">備註</td>
                                            <td width="50px">操作</td>
                                        </tr>
                                        <tbody id="info_Body1">
                                        <c:if test="${wfwgas!=null&&wfwgas.size()>0}">
                                            <c:forEach items="${wfwgas}" var="gasitems" varStatus="status">
                                                <tr align="center" id="gasitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <input id="gplace${status.index+1}"
                                                               name="wgasitems[${status.index+1}].gplace" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${gasitems.gplace}"/>
                                                    </td>
                                                    <td>
                                                        <input id="gprocess${status.index+1}"
                                                               name="wgasitems[${status.index+1}].gprocess" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${gasitems.gprocess}"/>
                                                    </td>
                                                    <td>
                                                        <input id="gname${status.index+1}"
                                                               name="wgasitems[${status.index+1}].gname" class="easyui-validatebox" style="width:120px;"
                                                               data-options="required:true" value="${gasitems.gname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="gusage${status.index+1}"
                                                               name="wgasitems[${status.index+1}].gusage" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${gasitems.gusage}"/>
                                                    </td>
                                                    <td>
                                                        <input id="gcomponent${status.index+1}"
                                                               name="wgasitems[${status.index+1}].gcomponent" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${gasitems.gcomponent}"/>
                                                    </td>
                                                    <td>
                                                        <input id="gyield${status.index+1}"
                                                               name="wgasitems[${status.index+1}].gyield" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${gasitems.gyield}"/>
                                                    </td>
                                                    <td>
                                                        <input id="gremarks${status.index+1}"
                                                               name="wgasitems[${status.index+1}].gremarks" class="easyui-validatebox" style="width:100px;"
                                                               value="${gasitems.gremarks}"/>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="gasdeltr(${status.index+1});return false;"/>
                                                        <input id="gshunxu${status.index+1}" type="hidden" name="wgasitems[${status.index+1}].shunxu" value="${status.index+1}"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${wfwgas.size()==0 || wfwgas==null}">
                                            <tr align="center" id="gasitems1">
                                                <td>1</td>
                                                <td>
                                                    <input id="gplace1"
                                                           name="wgasitems[1].gplace" class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="gprocess1"
                                                           name="wgasitems[1].gprocess" class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="gname1"
                                                           name="wgasitems[1].gname" class="easyui-validatebox" style="width:120px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="gusage1"
                                                           name="wgasitems[1].gusage" class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="gcomponent1"
                                                           name="wgasitems[1].gcomponent" class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="gyield1"
                                                           name="wgasitems[1].gyield" class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="gremarks1"
                                                           name="wgasitems[1].gremarks" class="easyui-validatebox" style="width:100px;" value=""/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="gasdeltr(1);return false;"/>
                                                    <input id="gshunxu1" type="hidden" name="wgasitems[1].shunxu" value="1"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="gnottr0">
                                            <td colspan="9" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="gasAdd" style="width:150px;float:left;" value="添加一行"/>
                                            </td>
                                        </tr>

                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">危廢：</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="dangerousTable" width="100%">
                                        <tr align="center">
                                            <td width="30px">項次</td>
                                            <td width="100px">污染源位置&nbsp;<font color="red">*</font></td>
                                            <td width="100px">製程名稱&nbsp;<font color="red">*</font></td>
                                            <td width="100px">危廢名稱&nbsp;<font color="red">*</font></td>
                                            <td width="100px">危廢產生量（t/月）<font color="red">*</font></td>
                                            <td width="150px">危廢主要成份&nbsp;<font color="red">*</font></td>
                                            <td width="100px">排廢週期&nbsp;<font color="red">*</font></td>
                                            <td width="100px">備註</td>
                                            <td width="50px">操作</td>
                                        </tr>
                                        <tbody id="info_Body2">
                                        <c:if test="${wfwdangerous!=null&&wfwdangerous.size()>0}">
                                            <c:forEach items="${wfwdangerous}" var="dangerousitems" varStatus="status">
                                                <tr align="center" id="dangerousitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <input id="dplace${status.index+1}"
                                                               name="wdangerousitems[${status.index+1}].dplace" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${dangerousitems.dplace}"/>
                                                    </td>
                                                    <td>
                                                        <input id="dprocess${status.index+1}"
                                                               name="wdangerousitems[${status.index+1}].dprocess" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${dangerousitems.dprocess}"/>
                                                    </td>
                                                    <td>
                                                        <input id="dname${status.index+1}"
                                                               name="wdangerousitems[${status.index+1}].dname" class="easyui-validatebox" style="width:120px;"
                                                               data-options="required:true" value="${dangerousitems.dname}"/>
                                                    </td>
                                                    <td>
                                                        <input id="dyield${status.index+1}"
                                                               name="wdangerousitems[${status.index+1}].dyield" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${dangerousitems.dyield}"/>
                                                    </td>
                                                    <td>
                                                        <input id="dcomponent${status.index+1}"
                                                               name="wdangerousitems[${status.index+1}].dcomponent" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${dangerousitems.dcomponent}"/>
                                                    </td>
                                                    <td>
                                                        <input id="dcycle${status.index+1}"
                                                               name="wdangerousitems[${status.index+1}].dcycle" class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true" value="${dangerousitems.dcycle}"/>
                                                    </td>
                                                    <td>
                                                        <input id="dremarks${status.index+1}"
                                                               name="wdangerousitems[${status.index+1}].dremarks" class="easyui-validatebox" style="width:100px;"
                                                               value="${dangerousitems.dremarks}"/>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="dangerousdeltr(${status.index+1});return false;"/>
                                                        <input id="dshunxu${status.index+1}" type="hidden" name="wdangerousitems[${status.index+1}].shunxu" value="${status.index+1}"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${wfwdangerous.size()==0 || wfwdangerous==null}">
                                            <tr align="center" id="dangerousitems1">
                                                <td>1</td>
                                                <td>
                                                    <input id="dplace1"
                                                           name="wdangerousitems[1].dplace" class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="dprocess1"
                                                           name="wdangerousitems[1].dprocess" class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="dname1"
                                                           name="wdangerousitems[1].dname" class="easyui-validatebox" style="width:120px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="dyield1"
                                                           name="wdangerousitems[1].dyield" class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="dcomponent1"
                                                           name="wdangerousitems[1].dcomponent" class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="dcycle1"
                                                           name="wdangerousitems[1].dcycle" class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="dremarks1"
                                                           name="wdangerousitems[1].dremarks" class="easyui-validatebox" style="width:100px;" value=""/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="dangerousdeltr(1);return false;"/>
                                                    <input id="dshunxu1" type="hidden" name="wdangerousitems[1].shunxu" value="1"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="dnottr0">
                                            <td colspan="9" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="dangerousAdd" style="width:150px;float:left;" value="添加一行"/>
                                            </td>
                                        </tr>

                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>其他項目&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style2">
                                    <textarea id="otherproject" name="wfwgdenvironment.otherproject" class="easyui-validatebox"
                                              oninput="return LessThan2(this,'txtNum2');"
                                              onchange="return LessThan2(this,'txtNum2');"
                                              onpropertychange="return LessThan2(this,'txtNum2');"
                                              maxlength="300" style="width:99%;height:80px;" data-options="required:true,validType:'length[0,300]'"
                                              rows="5" cols="6">${wfwgdenvironmentEntity.otherproject }</textarea><span id="txtNum2"></span>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>申請附件</td>
                            <td colspan="9" class="td_style2">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="wfwgdenvironment.attachids" value="${wfwgdenvironmentEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','環保三同時申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hachargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠區環安窗口審單</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole5(249,'hachargeno','hachargename','haqchargeno','haqchargename','yschargeno','yschargename',$('#applyfactoryid').combobox('getValue'),'','')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hachargeno" name="wfwgdenvironment.hachargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hachargeno']}"
                                                               readonly
                                                               value="${wfwgdenvironmentEntity.hachargeno }"/><c:if
                                                            test="${requiredMap['hachargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hachargename" name="wfwgdenvironment.hachargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hachargeno']}"
                                                                value="${wfwgdenvironmentEntity.hachargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole6($('#applydeptno').val(),'kchargeno','kchargename',$('#applynofactoryid').val(),'k2chargeno','k2chargename')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="wfwgdenvironment.kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wfwgdenvironmentEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="wfwgdenvironment.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfwgdenvironmentEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole6($('#applydeptno').val(),'bchargeno','bchargename',$('#applynofactoryid').val(),'b2chargeno','b2chargename')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="wfwgdenvironment.bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfwgdenvironmentEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="wfwgdenvironment.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfwgdenvironmentEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole6($('#applydeptno').val(),'cchargeno','cchargename',$('#applynofactoryid').val(),'c2chargeno','c2chargename')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wfwgdenvironment.cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfwgdenvironmentEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wfwgdenvironment.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfwgdenvironmentEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applynofactoryid').val(),'wfwgdenvironment')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wfwgdenvironment.zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfwgdenvironmentEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="wfwgdenvironment.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfwgdenvironmentEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#applydeptno').val(),'zcchargeno','zcchargename',$('#applynofactoryid').val(),'wfwgdenvironment')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="wfwgdenvironment.zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wfwgdenvironmentEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="wfwgdenvironment.zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfwgdenvironmentEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="haqchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">廠區環安確認</td>
                                                                <td style="border: none;">
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="haqchargeno" name="wfwgdenvironment.haqchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['haqchargeno']}"
                                                               readonly
                                                               value="${wfwgdenvironmentEntity.haqchargeno }"/><c:if
                                                            test="${requiredMap['haqchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="haqchargename" name="wfwgdenvironment.haqchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['haqchargeno']}"
                                                                value="${wfwgdenvironmentEntity.haqchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="hakchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠區環安課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole7(250,'hakchargeTable','hakchargeno','hakchargename',$('#applyfactoryid').combobox('getValue'),'wfwgdenvironment','hak2chargeTable','hak2chargeno','hak2chargename')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hakchargeno" name="wfwgdenvironment.hakchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hakchargeno']}"
                                                               readonly
                                                               value="${wfwgdenvironmentEntity.hakchargeno }"/><c:if
                                                            test="${requiredMap['hakchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hakchargename" name="wfwgdenvironment.hakchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hakchargeno']}"
                                                                value="${wfwgdenvironmentEntity.hakchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"  id="habchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠區環安部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole7(251,'habchargeTable','habchargeno','habchargename',$('#applyfactoryid').combobox('getValue'),'wfwgdenvironment','hab2chargeTable','hab2chargeno','hab2chargename')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="habchargeno" name="wfwgdenvironment.habchargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['habchargeno']}"
                                                               readonly
                                                               value="${wfwgdenvironmentEntity.habchargeno }"/><c:if test="${requiredMap['habchargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="habchargename" name="wfwgdenvironment.habchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['habchargeno']}"
                                                                value="${wfwgdenvironmentEntity.habchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"  id="yschargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">廠區環安工程驗收</td>
                                                                <td style="border: none;"></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="yschargeno" name="wfwgdenvironment.yschargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['yschargeno']}"
                                                               readonly
                                                               value="${wfwgdenvironmentEntity.yschargeno }"/><c:if test="${requiredMap['yschargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="yschargename" name="wfwgdenvironment.yschargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['yschargeno']}"
                                                                value="${wfwgdenvironmentEntity.yschargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"  id="haq2chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">需求單位確認</td>
                                                                <td style="border: none;"></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="haq2chargeno" name="wfwgdenvironment.haq2chargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['haq2chargeno']}"
                                                               readonly
                                                               value="${wfwgdenvironmentEntity.haq2chargeno }"/><c:if test="${requiredMap['haq2chargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="haq2chargename" name="wfwgdenvironment.haq2chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['haq2chargeno']}"
                                                                value="${wfwgdenvironmentEntity.haq2chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"  id="k2chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">課級主管</td>
                                                                <td style="border: none;"></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="k2chargeno" name="wfwgdenvironment.k2chargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['k2chargeno']}"
                                                               readonly
                                                               value="${wfwgdenvironmentEntity.k2chargeno }"/><c:if test="${requiredMap['k2chargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="k2chargename" name="wfwgdenvironment.k2chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['k2chargeno']}"
                                                                value="${wfwgdenvironmentEntity.k2chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"  id="hak2chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">廠區環安課級主管</td>
                                                                <td style="border: none;"></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hak2chargeno" name="wfwgdenvironment.hak2chargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['hak2chargeno']}"
                                                               readonly
                                                               value="${wfwgdenvironmentEntity.hak2chargeno }"/><c:if test="${requiredMap['hak2chargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hak2chargename" name="wfwgdenvironment.hak2chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hak2chargeno']}"
                                                                value="${wfwgdenvironmentEntity.hak2chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"  id="b2chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">部級主管</td>
                                                                <td style="border: none;"></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="b2chargeno" name="wfwgdenvironment.b2chargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['b2chargeno']}"
                                                               readonly
                                                               value="${wfwgdenvironmentEntity.b2chargeno }"/><c:if test="${requiredMap['b2chargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="b2chargename" name="wfwgdenvironment.b2chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['b2chargeno']}"
                                                                value="${wfwgdenvironmentEntity.b2chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"  id="c2chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">廠級主管</td>
                                                                <td style="border: none;"></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="c2chargeno" name="wfwgdenvironment.c2chargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['c2chargeno']}"
                                                               readonly
                                                               value="${wfwgdenvironmentEntity.c2chargeno }"/><c:if test="${requiredMap['c2chargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="c2chargename" name="wfwgdenvironment.c2chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['c2chargeno']}"
                                                                value="${wfwgdenvironmentEntity.c2chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"  id="hab2chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">廠區環安部級主管</td>
                                                                <td style="border: none;"></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hab2chargeno" name="wfwgdenvironment.hab2chargeno"
                                                               class="easyui-validatebox" data-options="width:80,required:${requiredMap['hab2chargeno']}"
                                                               readonly
                                                               value="${wfwgdenvironmentEntity.hab2chargeno }"/><c:if test="${requiredMap['hab2chargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hab2chargename" name="wfwgdenvironment.hab2chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hab2chargeno']}"
                                                                value="${wfwgdenvironmentEntity.hab2chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfwgdenvironmentEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="canelTask('${wfwgdenvironmentEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" name="wfwgdenvironment.wastewater" value="${wfwgdenvironmentEntity.wastewater}"/>
    <input type="hidden" name="wfwgdenvironment.wastegas" value="${wfwgdenvironmentEntity.wastegas}"/>
    <input type="hidden" name="wfwgdenvironment.dangerous" value="${wfwgdenvironmentEntity.dangerous}"/>
    <input type="hidden" name="wfwgdenvironment.wother" value="${wfwgdenvironmentEntity.wother}"/>
    <input type="hidden" name="wfwgdenvironment.projectaccept" value="${wfwgdenvironmentEntity.projectaccept}"/>
    <div id="win"></div>
    <div id="dlg"></div>
</form>
<script src='${ctx}/static/js/safety/wfwgdenvironment.js?random=<%= Math.random()%>'></script>
</body>
</html>