<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>環保三同時申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfwgdenvironment/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfwgdenvironmentEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfwgdenvironmentEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">環保三同時申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfwgdenvironmentEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfwgdenvironmentEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfwgdenvironmentEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfwgdenvironmentEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfwgdenvironmentEntity.makerno}/${wfwgdenvironmentEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">申請人工號</td>
                            <td width="10%" class="td_style2">${wfwgdenvironmentEntity.applyno}</td>
                            <td width="8%">申請人</td>
                            <td width="10%" class="td_style2">${wfwgdenvironmentEntity.applyname}</td>
                            <td width="8%">單位代碼</td>
                            <td width="10%" class="td_style2">${wfwgdenvironmentEntity.applydeptno}</td>
                            <td width="8%">費用代碼</td>
                            <td width="10%" class="td_style2">${wfwgdenvironmentEntity.applycostno}</td>
                            <td width="8%">所在廠區</td>
                            <td width="15%" class="td_style2">${wfwgdenvironmentEntity.applyfactoryname }</td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱</td>
                            <td colspan="3" class="td_style2">${wfwgdenvironmentEntity.applydeptname }</td>
                            <td>聯繫方式</td>
                            <td class="td_style2">${wfwgdenvironmentEntity.applytel }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfwgdenvironmentEntity.applyemail}</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td>項目名稱</td>
                            <td colspan="5" class="td_style2">${wfwgdenvironmentEntity.projectname }</td>
                            <td>建設單位</td>
                            <td colspan="3" class="td_style2">${wfwgdenvironmentEntity.buildcompany }</td>
                        </tr>
                        <tr align="center">
                            <td>項目地點</td>
                            <td colspan="3" class="td_style2">${wfwgdenvironmentEntity.projectaddress }</td>
                            <td colspan="2">預計施工工期</td>
                            <td colspan="4" class="td_style2">
                                &nbsp;&nbsp;
                                <input id="expectstartdate" name="wfwgdenvironment.expectstartdate" class="Wdate" data-options="width:150" readonly
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfwgdenvironmentEntity.expectstartdate}"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})" />
                                &nbsp;&nbsp;~&nbsp;&nbsp;
                                <input id="expectenddate" name="wfwgdenvironment.expectenddate" class="Wdate" data-options="width:150" readonly
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfwgdenvironmentEntity.expectenddate}"/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'expectstartdate\')}'})" />
                            </td>
                        </tr>
                        <tr align="center">
                            <td>項目內容說明</td>
                            <td colspan="9" class="td_style2">
                                    <textarea name="wfwgdenvironment.projectdescription" class="easyui-validatebox"
                                              maxlength="1300" style="width:99%;height:150px;" readonly
                                              rows="5" cols="6">${wfwgdenvironmentEntity.projectdescription }</textarea>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">廢水：（需附廢水檢測報告）</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <table id="waterTable" width="100%">
                                    <tr align="center">
                                        <td width="30px">項次</td>
                                        <td width="100px">污染源位置</td>
                                        <td width="100px">排水製程/設備</td>
                                        <td width="100px">槽位</td>
                                        <td width="100px">藥劑及濃度</td>
                                        <td width="80px">排放方式及週期</td>
                                        <td width="100px">廢水產生量</td>
                                        <td width="100px">預計排水日期</td>
                                        <td width="100px">備註</td>
                                    </tr>
                                    <tbody id="info_Body0">
                                    <c:if test="${wfwwater!=null&&wfwwater.size()>0}">
                                        <c:forEach items="${wfwwater}" var="wateritems" varStatus="status">
                                            <tr align="center" id="wateritems${status.index+1}">
                                                <td>${status.index+1}</td>
                                                <td>${wateritems.wplace}</td>
                                                <td>${wateritems.wprocess}</td>
                                                <td>${wateritems.wposition}</td>
                                                <td>${wateritems.wdrug}</td>
                                                <td>${wateritems.wcycle}</td>
                                                <td>${wateritems.wyield}</td>
                                                <td>
                                                    <input id="wdate${status.index+1}" name="wateritems[${status.index}].wdate" class="easyui-my97 inputCss" datefmt="yyyy-MM-dd"
                                                           data-options="width: 100" readonly
                                                           value="<fmt:formatDate value="${wateritems.wdate}"/>"/>
                                                </td>
                                                <td>${wateritems.wremarks}</td>
                                            </tr>
                                        </c:forEach>
                                    </c:if>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">廢氣：</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <table id="gasTable" width="100%">
                                    <tr align="center">
                                        <td width="30px">項次</td>
                                        <td width="100px">污染源位置</td>
                                        <td width="100px">製程名稱</td>
                                        <td width="100px">化學品名稱</td>
                                        <td width="100px">化學品用量（t/月）</td>
                                        <td width="150px">化學品主要成份</td>
                                        <td width="100px">排風量</td>
                                        <td width="100px">備註</td>
                                    </tr>
                                    <tbody id="info_Body1">
                                    <c:if test="${wfwgas!=null&&wfwgas.size()>0}">
                                        <c:forEach items="${wfwgas}" var="gasitems" varStatus="status">
                                            <tr align="center" id="gasitems${status.index+1}">
                                                <td>${status.index+1}</td>
                                                <td>${gasitems.gplace}</td>
                                                <td>${gasitems.gprocess}</td>
                                                <td>${gasitems.gname}</td>
                                                <td>${gasitems.gusage}</td>
                                                <td>${gasitems.gcomponent}</td>
                                                <td>${gasitems.gyield}</td>
                                                <td>${gasitems.gremarks}</td>
                                            </tr>
                                        </c:forEach>
                                    </c:if>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">危廢：</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <table id="dangerousTable" width="100%">
                                    <tr align="center">
                                        <td width="30px">項次</td>
                                        <td width="100px">污染源位置</td>
                                        <td width="100px">製程名稱</td>
                                        <td width="100px">危廢名稱</td>
                                        <td width="100px">危廢產生量（t/月）</td>
                                        <td width="150px">危廢主要成份</td>
                                        <td width="100px">排廢週期</td>
                                        <td width="100px">備註</td>
                                    </tr>
                                    <tbody id="info_Body2">
                                    <c:if test="${wfwdangerous!=null&&wfwdangerous.size()>0}">
                                        <c:forEach items="${wfwdangerous}" var="dangerousitems" varStatus="status">
                                            <tr align="center" id="dangerousitems${status.index+1}">
                                                <td>${status.index+1}</td>
                                                <td>${dangerousitems.dplace}</td>
                                                <td>${dangerousitems.dprocess}</td>
                                                <td>${dangerousitems.dname}</td>
                                                <td>${dangerousitems.dyield}</td>
                                                <td>${dangerousitems.dcomponent}</td>
                                                <td>${dangerousitems.dcycle}</td>
                                                <td>${dangerousitems.dremarks}</td>
                                            </tr>
                                        </c:forEach>
                                    </c:if>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>其他項目</td>
                            <td colspan="9" class="td_style2">
                                    <textarea id="otherproject" name="wfwgdenvironment.otherproject" class="easyui-validatebox"
                                              maxlength="300" style="width:99%;height:80px;"readonly
                                              rows="5" cols="6">${wfwgdenvironmentEntity.otherproject }</textarea>
                            </td>
                        </tr>
                        <c:if test="${wfwgdenvironmentEntity.wastewater!=null && wfwgdenvironmentEntity.wastewater!=''}">
                            <tr>
                                <td colspan="10" class="td_style1">環境管理單位評估/規劃結果</td>
                            </tr>
                            <tr align="center">
                                <td>廢水</td>
                                <td colspan="9" class="td_style2">
                                    <textarea  name="wfwgdenvironment.wastewater" class="easyui-validatebox"
                                               maxlength="500" style="width:99%;height:80px;" readonly
                                               rows="5" cols="6">${wfwgdenvironmentEntity.wastewater }</textarea>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>廢氣</td>
                                <td colspan="9" class="td_style2">
                                    <textarea  name="wfwgdenvironment.wastegas" class="easyui-validatebox"
                                               maxlength="500" style="width:99%;height:80px;" readonly
                                               rows="5" cols="6">${wfwgdenvironmentEntity.wastegas }</textarea>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>危廢</td>
                                <td colspan="9" class="td_style2">
                                    <textarea name="wfwgdenvironment.dangerous" class="easyui-validatebox"
                                              maxlength="500" style="width:99%;height:80px;" readonly
                                              rows="5" cols="6">${wfwgdenvironmentEntity.dangerous }</textarea>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>其他事項</td>
                                <td colspan="9" class="td_style2">
                                    <textarea name="wfwgdenvironment.wother" class="easyui-validatebox"
                                              maxlength="500" style="width:99%;height:80px;" readonly
                                              rows="5" cols="6">${wfwgdenvironmentEntity.wother }</textarea>
                                </td>
                            </tr>
                            <tr align="center">
                                <td>審核附件</td>
                                <td colspan="9" class="td_style2">
                                    <input type="hidden"  name="wfwgdenvironment.attachids2" value="${wfwgdenvironmentEntity.attachids2 }"/>
                                    <div>
                                        <c:forEach items="${file2}" varStatus="i" var="item">
                                            <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                                <div class="float_L">
                                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                                </div>
                                            </div>
                                        </c:forEach>
                                    </div>
                                </td>
                            </tr>
                        </c:if>
                        <c:if test="${wfwgdenvironmentEntity.projectaccept!=null && wfwgdenvironmentEntity.projectaccept!=''}">
                            <tr align="center">
                                <td>工程驗收情況</td>
                                <td colspan="9" class="td_style2">
                                    <textarea name="wfwgdenvironment.projectaccept" class="easyui-validatebox"
                                              maxlength="500" style="width:99%;height:80px;" readonly
                                              rows="5" cols="6">${wfwgdenvironmentEntity.projectaccept }</textarea>
                                </td>
                            </tr>
                        </c:if>
                        <tr align="center">
                            <td>申請附件</td>
                            <td colspan="9" class="td_style2">
                                <input type="hidden" id="attachids" name="wfwgdenvironment.attachids" value="${wfwgdenvironmentEntity.attachids }"/>
                                <div>
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>批註</td>
                            <td colspan="9" class="td_style2">
						        <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                          style="width:99%;height:60px;" rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','環保三同時申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfwgdenvironmentEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfwgdenvironmentEntity.workstatus!=null&&wfwgdenvironmentEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/safety/wfwgdenvironment.js?random=<%= Math.random()%>'></script>
</body>
</html>