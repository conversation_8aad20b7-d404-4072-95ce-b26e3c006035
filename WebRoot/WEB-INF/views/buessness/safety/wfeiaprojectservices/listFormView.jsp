<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>環評項目服務申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfeiaprojectservices/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfeiaprojectservicesEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfeiaprojectservicesEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">環評項目服務申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfeiaprojectservicesEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfeiaprojectservicesEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfeiaprojectservicesEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfeiaprojectservicesEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfeiaprojectservicesEntity.makerno}/${wfeiaprojectservicesEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">申請人工號</td>
                            <td width="10%" class="td_style2">${wfeiaprojectservicesEntity.applyno }</td>
                            <td width="8%">申請人</td>
                            <td width="10%" class="td_style2">${wfeiaprojectservicesEntity.applyname }</td>
                            <td width="8%">單位代碼</td>
                            <td width="10%" class="td_style2">${wfeiaprojectservicesEntity.applydeptno }</td>
                            <td width="8%">費用代碼</td>
                            <td width="10%" class="td_style2">${wfeiaprojectservicesEntity.applycostno }</td>
                            <td width="8%">所在廠區</td>
                            <td width="15%" class="td_style2">${wfeiaprojectservicesEntity.applyfactoryname }</td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱</td>
                            <td colspan="3" class="td_style2">${wfeiaprojectservicesEntity.applydeptname }</td>
                            <td>聯繫方式</td>
                            <td class="td_style2">${wfeiaprojectservicesEntity.applytel}</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfeiaprojectservicesEntity.applyemail }</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td>項目名稱</td>
                            <td colspan="3" class="td_style2">${wfeiaprojectservicesEntity.projectname }</td>
                            <td>法人</td>
                            <td colspan="5" class="td_style2">${wfeiaprojectservicesEntity.layperson}</td>
                        </tr>
                        <tr align="center">
                            <td>項目地點</td>
                            <td colspan="3" class="td_style2">${wfeiaprojectservicesEntity.projectaddress }</td>
                            <td>產品</td>
                            <td class="td_style2">${wfeiaprojectservicesEntity.product }</td>
                            <td>產能</td>
                            <td class="td_style2">${wfeiaprojectservicesEntity.capacity }</td>
                            <td>投資額</td>
                            <td class="td_style2">${wfeiaprojectservicesEntity.investment }</td>
                        </tr>
                        <tr align="center">
                            <td>需求說明</td>
                            <td colspan="9" class="td_style2">
                                    <textarea id="applyreason" name="applyreason" class="easyui-validatebox"
                                              maxlength="500" style="width:99%;height:150px;"
                                              rows="5" cols="6">${wfeiaprojectservicesEntity.applyreason }</textarea>
                            </td>
                        </tr>
                        <c:if test="${wfeiaprojectservicesEntity.examinereason!=null&&wfeiaprojectservicesEntity.examinereason!=''}">
                            <tr align="center">
                                <td>現場調查說明</td>
                                <td colspan="9" class="td_style2">
                                    <textarea id="examinereason" name="examinereason" class="easyui-validatebox"
                                              style="width:99%;height:80px;"
                                              rows="5" cols="6">${wfeiaprojectservicesEntity.examinereason }</textarea>
                                </td>
                            </tr>
                        </c:if>
                        <c:if test="${wfeiaprojectservicesEntity.checkreason!=null&&wfeiaprojectservicesEntity.checkreason!=''}">
                            <tr align="center">
                                <td>審查說明</td>
                                <td colspan="9" class="td_style2">
                                    <textarea id="checkreason" name="checkreason" class="easyui-validatebox"
                                              style="width:99%;height:80px;"
                                              rows="5" cols="6">${wfeiaprojectservicesEntity.checkreason }</textarea>
                                </td>
                            </tr>
                        </c:if>
                        <tr align="center">
                            <td>附件</td>
                            <td colspan="9" class="td_style1">
                                <input type="hidden" id="attachids" name="attachids" value="${wfeiaprojectservicesEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>批註</td>
                            <td colspan="9" class="td_style2">
                                <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                          style="width:99%;height:60px;"
                                          rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','環評項目服務申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfeiaprojectservicesEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfeiaprojectservicesEntity.workstatus!=null&&wfeiaprojectservicesEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/safety/wfeiaprojectservices.js?random=<%= Math.random()%>'></script>
</body>
</html>