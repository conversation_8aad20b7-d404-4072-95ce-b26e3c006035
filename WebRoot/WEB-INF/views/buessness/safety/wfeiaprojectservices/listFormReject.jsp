<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>環評項目服務申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
        #applyreason::-webkit-input-placeholder::after {
            display:block;
            content:"prompt:'項目建設地點位於XXX，涵蓋的樓層範圍為XXX；\A項目生產產品主要為XXX，規劃產能為日加工XXX千件；\A項目投資金額為XXX億元；\A項目工藝流程圖：\A項目掛靠費用代碼：'";
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfeiaprojectservices/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfeiaprojectservicesEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfeiaprojectservicesEntity.serialno }"/>
    <input id="makerno" name="makerno" type="hidden" value="${wfeiaprojectservicesEntity.makerno }"/>
    <input id="makername" name="makername" type="hidden" value="${wfeiaprojectservicesEntity.makername }"/>
    <input id="makerdeptno" name="makerdeptno" type="hidden" value="${wfeiaprojectservicesEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="makerfactoryid" type="hidden"
           value="${wfeiaprojectservicesEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">環評項目服務申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfeiaprojectservicesEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfeiaprojectservicesEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfeiaprojectservicesEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfeiaprojectservicesEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfeiaprojectservicesEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfeiaprojectservicesEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfeiaprojectservicesEntity.makerno}/${wfeiaprojectservicesEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="applyno" name="applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfeiaprojectservicesEntity.applyno }"
                                       onblur="queryUserInfo('apply');"/>
                            </td>
                            <td width="8%">申請人</td>
                            <td width="10%" class="td_style1">
                                <input id="applyname" name="applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wfeiaprojectservicesEntity.applyname }"/>
                            </td>
                            <td width="8%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="applydeptno" name="applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfeiaprojectservicesEntity.applydeptno }"/>
                            </td>
                            <td width="8%">費用代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="applycostno" name="applycostno"
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfeiaprojectservicesEntity.applycostno }"/>
                            </td>
                            <td width="8%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="15%" class="td_style1">
                                <input id="applyfactoryid" name="applyfactoryid"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${wfeiaprojectservicesEntity.applyfactoryid }"
                                       data-options="width: 120,validType:'comboxValidate[\'applyfactoryid\',\'请選擇所在廠區\']'"/>
                                <input id="applyfactoryname" name="applyfactoryname" type="hidden" value="${wfeiaprojectservicesEntity.applyfactoryname }"/>
                                <input id="applynofactoryid" name="applynofactoryid" type="hidden" value="${wfeiaprojectservicesEntity.applynofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="applydeptname"
                                       class="easyui-validatebox" data-options="width: 320,required:true"
                                       value="${wfeiaprojectservicesEntity.applydeptname }"/>
                            </td>
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applytel" name="applytel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfeiaprojectservicesEntity.applytel }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="applyemail" class="easyui-validatebox"
                                       value="${wfeiaprojectservicesEntity.applyemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td>項目名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="projectname" name="projectname"
                                       class="easyui-validatebox" data-options="width: 320,required:true"
                                       value="${wfeiaprojectservicesEntity.projectname }"/>
                            </td>
                            <td>法人&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="laypersonid" name="laypersonid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfeiaprojectservicesEntity.laypersonid }"
                                       data-options="width: 300,validType:'comboxValidate[\'laypersonid\',\'请選擇法人\']',onSelect:function(){onchangeLayperson();}"/>
                                <input id="layperson" name="layperson" type="hidden" value="${wfeiaprojectservicesEntity.layperson}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>項目地點&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="projectaddress" name="projectaddress"
                                       class="easyui-validatebox" data-options="width: 320,required:true"
                                       value="${wfeiaprojectservicesEntity.projectaddress }"/>
                            </td>
                            <td>產品&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="product" name="product"
                                       class="easyui-validatebox" data-options="width: 130,required:true"
                                       value="${wfeiaprojectservicesEntity.product }"/>
                            </td>
                            <td>產能&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="capacity" name="capacity"
                                       class="easyui-validatebox" data-options="width: 130,required:true"
                                       value="${wfeiaprojectservicesEntity.capacity }"/>
                            </td>
                            <td>投資額&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="investment" name="investment"
                                       class="easyui-validatebox" data-options="width: 150,required:true"
                                       value="${wfeiaprojectservicesEntity.investment }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>需求說明&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style2">
                                    <textarea id="applyreason" name="applyreason" class="easyui-validatebox"
                                              oninput="return LessThan(this);"
                                              onchange="return LessThan(this);"
                                              onpropertychange="return LessThan(this);"
                                              maxlength="500" onblur="onblurr();"  onfocus="onfocuss();"
                                              style="width:99%;height:150px;" data-options="required:true,validType:'length[0,500]'"
                                              rows="5" cols="6">${wfeiaprojectservicesEntity.applyreason }</textarea><span id="txtNum"></span>
                            </td>
                        </tr>
                        <tr align="center">
                        <td>附件&nbsp;<font color="red">*</font></td>
                        <td colspan="9" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                            <input type="hidden" id="attachids" name="attachids" value="${wfeiaprojectservicesEntity.attachids }"/>
                            <div id="dowloadUrl">
                                <c:forEach items="${file}" varStatus="i" var="item">
                                    <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                        <div class="float_L">
                                            <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                        </div>
                                        <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                    </div>
                                </c:forEach>
                            </div>
                        </td>
                    </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="9" class="td_style2">
                                1.需求單位提此單時需一併提供《項目環境影響評價基本資料清單》和《樓層化學品&工業用水&工業廢水排放明細表》，附件模板如下；<br/>
                                2.環評辦理時間至少6個月（從接單算起），因涉及政府審批，完成日期以實際為準；<br/>
                                3.附件模板：<a href="${ctx}/ossAdmin/download/1557d93b9dd64870a26eef3d784b0103" id="btnBatchImportTpl">《項目環境影響評價基本資料清單》</a>和
                                <a href="${ctx}/ossAdmin/download/4896a191d39d4df9b1e9fd13a1cf06b5" id="btnBatchImportTpl2">《樓層化學品&工業用水&工業廢水排放明細表》</a>。
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','環評項目服務申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hachargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠區環安窗口審單</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole5(249,'hachargeno','hachargename','haqchargeno','haqchargename','','',$('#applyfactoryid').combobox('getValue'),'','')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hachargeno" name="hachargeno" class="easyui-validatebox" data-options="width:80,required:${requiredMap['hachargeno']}"
                                                               readonly value="${wfeiaprojectservicesEntity.hachargeno }"/>
                                                        <c:if test="${requiredMap['hachargeno'].equals('true')}"><font color="red">*</font></c:if>
                                                        /<input id="hachargename" name="hachargename" readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hachargeno']}"
                                                                value="${wfeiaprojectservicesEntity.hachargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole6($('#applydeptno').val(),'kchargeno','kchargename',$('#applynofactoryid').val(),'k2chargeno','k2chargename')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wfeiaprojectservicesEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfeiaprojectservicesEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole6($('#applydeptno').val(),'bchargeno','bchargename',$('#applynofactoryid').val(),'b2chargeno','b2chargename')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfeiaprojectservicesEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfeiaprojectservicesEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole6($('#applydeptno').val(),'cchargeno','cchargename',$('#applynofactoryid').val(),'c2chargeno','c2chargename')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfeiaprojectservicesEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfeiaprojectservicesEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#applydeptno').val(),'zchargeno','zchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfeiaprojectservicesEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfeiaprojectservicesEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#applydeptno').val(),'zcchargeno','zcchargename',$('#applynofactoryid').val(),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zcchargeno" name="zcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                               readonly
                                                               value="${wfeiaprojectservicesEntity.zcchargeno }"/><c:if
                                                            test="${requiredMap['zcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zcchargename" name="zcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zcchargeno']}"
                                                                value="${wfeiaprojectservicesEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="haqchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">廠區環安確認</td>
                                                                <td style="border: none;">
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="haqchargeno" name="haqchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['haqchargeno']}"
                                                               readonly
                                                               value="${wfeiaprojectservicesEntity.haqchargeno }"/><c:if
                                                            test="${requiredMap['haqchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="haqchargename" name="haqchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['haqchargeno']}"
                                                                value="${wfeiaprojectservicesEntity.haqchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="hakchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠區環安課級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(250,'hakchargeTable','hakchargeno','hakchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hakchargeno" name="hakchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hakchargeno']}"
                                                               readonly
                                                               value="${wfeiaprojectservicesEntity.hakchargeno }"/><c:if
                                                            test="${requiredMap['hakchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hakchargename" name="hakchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hakchargeno']}"
                                                                value="${wfeiaprojectservicesEntity.hakchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="k2chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">課級主管</td>
                                                                <td style="border: none;">
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="k2chargeno" name="k2chargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['k2chargeno']}"
                                                               readonly
                                                               value="${wfeiaprojectservicesEntity.k2chargeno }"/><c:if
                                                            test="${requiredMap['k2chargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="k2chargename" name="k2chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['k2chargeno']}"
                                                                value="${wfeiaprojectservicesEntity.k2chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="b2chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">部級主管</td>
                                                                <td style="border: none;">
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="b2chargeno" name="b2chargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['b2chargeno']}"
                                                               readonly
                                                               value="${wfeiaprojectservicesEntity.b2chargeno }"/><c:if
                                                            test="${requiredMap['b2chargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="b2chargename" name="b2chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['b2chargeno']}"
                                                                value="${wfeiaprojectservicesEntity.b2chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="c2chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">廠級主管</td>
                                                                <td style="border: none;">
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="c2chargeno" name="c2chargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['c2chargeno']}"
                                                               readonly
                                                               value="${wfeiaprojectservicesEntity.c2chargeno }"/><c:if
                                                            test="${requiredMap['c2chargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="c2chargename" name="c2chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['c2chargeno']}"
                                                                value="${wfeiaprojectservicesEntity.c2chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="habchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠區環安部級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(251,'habchargeTable','habchargeno','habchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="habchargeno" name="habchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['habchargeno']}"
                                                               readonly
                                                               value="${wfeiaprojectservicesEntity.habchargeno }"/><c:if
                                                            test="${requiredMap['habchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="habchargename" name="habchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['habchargeno']}"
                                                                value="${wfeiaprojectservicesEntity.habchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="hpchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環評接單窗口</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(252,'hpchargeTable','hpchargeno','hpchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hpchargeno" name="hpchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hpchargeno']}"
                                                               readonly
                                                               value="${wfeiaprojectservicesEntity.hpchargeno }"/><c:if
                                                            test="${requiredMap['hpchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hpchargename" name="hpchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hpchargeno']}"
                                                                value="${wfeiaprojectservicesEntity.hpchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="hpkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環評課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(253,'hpkchargeTable','hpkchargeno','hpkchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hpkchargeno" name="hpkchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hpkchargeno']}"
                                                               readonly
                                                               value="${wfeiaprojectservicesEntity.hpkchargeno }"/><c:if
                                                            test="${requiredMap['hpkchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hpkchargename" name="hpkchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hpkchargeno']}"
                                                                value="${wfeiaprojectservicesEntity.hpkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="sjbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">CAA系統設計部部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(254,'sjbchargeTable','sjbchargeno','sjbchargename',$('#applyfactoryid').combobox('getValue'),null)"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="sjbchargeno" name="sjbchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['sjbchargeno']}"
                                                               readonly
                                                               value="${wfeiaprojectservicesEntity.sjbchargeno }"/><c:if
                                                            test="${requiredMap['sjbchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="sjbchargename" name="sjbchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['sjbchargeno']}"
                                                                value="${wfeiaprojectservicesEntity.sjbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfeiaprojectservicesEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;"
                                   onclick="canelTask('${wfeiaprojectservicesEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <input type="hidden" name="examinereason"  value="${wfeiaprojectservicesEntity.examinereason }"></input>
    <input type="hidden" name="checkreason"  value="${wfeiaprojectservicesEntity.checkreason }"></input>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
    <div id="dlg"></div>
</form>
<script src='${ctx}/static/js/safety/wfeiaprojectservices.js?random=<%= Math.random()%>'></script>
</body>
</html>