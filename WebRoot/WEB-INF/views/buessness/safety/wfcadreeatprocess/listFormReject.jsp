<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>商務餐廳幹部就餐申請表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/safety/wfcadreeatprocess.js?random=<%= Math.random()%>'></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }

        .overdiv {
            overflow-y: auto;
            overflow-x: auto;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfcadreeatprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfcadreeatprocessEntity.id }"/>
    <input id="serialno" name="wfcadreeat.serialno" type="hidden" value="${wfcadreeatprocessEntity.serialno }"/>
    <input id="makerno" name="wfcadreeat.makerno" type="hidden" value="${wfcadreeatprocessEntity.makerno }"/>
    <input id="makername" name="wfcadreeat.makername" type="hidden" value="${wfcadreeatprocessEntity.makername }"/>
    <input id="makerdeptno" name="wfcadreeat.makerdeptno" type="hidden" value="${wfcadreeatprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfcadreeat.makerfactoryid" type="hidden" value="${wfcadreeatprocessEntity.makerfactoryid }"/>
    <input id="applytype" name="wfcadreeat.applytype" type="hidden" value="${wfcadreeatprocessEntity.applytype }"/>
    <input id="workstatus" name="wfcadreeat.workstatus" type="hidden" value="${wfcadreeatprocessEntity.workstatus }"/>
    <div class="commonW">
        <div class="headTitle">${titleMemo}</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfcadreeatprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfcadreeatprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfcadreeatprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfcadreeatprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfcadreeatprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfcadreeatprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfcadreeatprocessEntity.makerno}/${wfcadreeatprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">提報人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">提報人工號&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="dealno" name="wfcadreeat.dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfcadreeatprocessEntity.dealno }" onblur="queryUserInfo('deal');"/>
                            </td>
                            <td width="4%">提報人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="wfcadreeat.dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfcadreeatprocessEntity.dealname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="wfcadreeat.dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfcadreeatprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="4%">費用代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealcostno" name="wfcadreeat.dealcostno" class="easyui-validatebox inputCss"
                                       data-options="width: 80"
                                       value="${wfcadreeatprocessEntity.dealcostno }"/>
                            </td>
                            <td width="4%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealfactoryid" name="wfcadreeat.dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfcadreeatprocessEntity.dealfactoryid }"
                                       data-options="width: 120,validType:'comboxValidate[\'dealfactoryid\',\'请選擇所在廠區\']'"/>
                                <input id="dealfactoryname" name="wfcadreeat.dealfactoryname" type="hidden"
                                       value="${wfcadreeatprocessEntity.dealfactoryname }"/>
                                <input id="dealnofactoryid" name="wfcadreeat.dealnofactoryid" type="hidden"
                                       value="${wfcadreeatprocessEntity.dealnofactoryid}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealdeptname" name="wfcadreeat.dealdeptname" class="easyui-validatebox"
                                       data-options="width: 400,required:true"
                                       value="${wfcadreeatprocessEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealtel" name="wfcadreeat.dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfcadreeatprocessEntity.dealtel }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="validApplyTel('dealtel')"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="wfcadreeat.dealemail" class="easyui-validatebox"
                                       value="${wfcadreeatprocessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請內容</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div id="tt" class="easyui-tabs" style="width: 1200px">
                                    <div id="tb"  style="height:auto" title="新增" data-options="refreshable: false">
                                        <div class="overdiv" width="1200px" id="overflowdiv">
                                            <table id="wfcadreeatitemsTable" width="120%" >
                                                <tr align="center">
                                                    <td width="30px">項次</td>
                                                    <td width="90px">工號&nbsp;<font color="red">*</font></td>
                                                    <td width="90px">姓名&nbsp;<font color="red">*</font></td>
                                                    <td width="150px">部門名稱&nbsp;<font color="red">*</font></td>
                                                    <td width="90px">掛靠費用代碼&nbsp;<font color="red">*</font></td>
                                                    <td width="90px">法人&nbsp;<font color="red">*</font></td>
                                                    <td width="90px">職稱&nbsp;<font color="red">*</font></td>
                                                    <td width="90px">資位&nbsp;<font color="red">*</font></td>
                                                    <td width="120px">就餐開始日期&nbsp;<font color="red">*</font></td>
                                                    <td width="120px">就餐結束日期&nbsp;<font color="red">*</font></td>
                                                    <td width="90px">就餐地點&nbsp;<font color="red">*</font></td>
                                                    <td width="240px">備註</td>
                                                    <td width="50px">操作</td>
                                                </tr>
                                                <tbody id="info_Body0">
                                                <c:if test="${wfcadreeatitems!=null&&wfcadreeatitems.size()>0}">
                                                    <c:forEach items="${wfcadreeatitems}" var="cadreeatitems" varStatus="status">
                                                        <tr align="center" id="cadreeatitems${status.index+1}" class="wfcadreeatitemsTr">
                                                            <td>${status.index+1}</td>
                                                            <td>
                                                                <input id="applyno${status.index+1}"
                                                                       onblur="getUserNameByEmpno2(this,'apply',status.index+1);"
                                                                       name="wfcadreeatitems[${status.index+1}].applyno"
                                                                       class="easyui-validatebox" style="width:70px;"
                                                                       data-options="required:true"
                                                                       value="${cadreeatitems.applyno}"/>
                                                            </td>
                                                            <td>
                                                                <input id="applyname${status.index+1}"
                                                                       name="wfcadreeatitems[${status.index+1}].applyname"
                                                                       class="easyui-validatebox" style="width:70px;" data-options="required:true"
                                                                       value="${cadreeatitems.applyname}"/>
                                                            </td>
                                                            <td>
                                                                <input id="applydeptname${status.index+1}"
                                                                       name="wfcadreeatitems[${status.index+1}].applydeptname"
                                                                       class="easyui-validatebox" style="width:120px;" data-options="required:true"
                                                                       value="${cadreeatitems.applydeptname}"/>
                                                            </td>
                                                            <td>
                                                                <input id="applycostno${status.index+1}"
                                                                       name="wfcadreeatitems[${status.index+1}].applycostno"
                                                                       class="easyui-validatebox" style="width:70px;" data-options="required:true"
                                                                       value="${cadreeatitems.applycostno}"/>
                                                            </td>
                                                            <td>
                                                                <input id="artificialperson${status.index+1}"
                                                                       name="wfcadreeatitems[${status.index+1}].artificialperson"
                                                                       class="easyui-validatebox" style="width:70px;" data-options="required:true"
                                                                       value="${cadreeatitems.artificialperson}"/>
                                                            </td>
                                                            <td>
                                                                <input id="applymanager${status.index+1}"
                                                                       name="wfcadreeatitems[${status.index+1}].applymanager"
                                                                       class="easyui-validatebox" style="width:70px;" data-options="required:true"
                                                                       value="${cadreeatitems.applymanager}"/>
                                                            </td>
                                                            <td>
                                                                <input id="applyleveltype${status.index+1}"
                                                                       name="wfcadreeatitems[${status.index+1}].applyleveltype"
                                                                       class="easyui-validatebox" style="width:70px;" data-options="required:true"
                                                                       value="${cadreeatitems.applyleveltype}"/>
                                                            </td>
                                                            <td>
                                                                <input id="applystartdate${status.index+1}"
                                                                       name="wfcadreeatitems[${status.index+1}].applystartdate"
                                                                       class="easyui-validatebox Wdate"
                                                                       data-options="required:true,prompt:'請選擇開始時間'"
                                                                       style="width:100px"
                                                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${cadreeatitems.applystartdate}"/>"
                                                                       onclick="WdatePicker({onpicked:function(){applyenddate${status.index+1}.click()},doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"/>
                                                            </td>
                                                            <td>
                                                                <input id="applyenddate${status.index+1}"
                                                                       name="wfcadreeatitems[${status.index+1}].applyenddate"
                                                                       class="easyui-validatebox Wdate"
                                                                       data-options="required:true,prompt:'請選擇結束時間'"
                                                                       style="width:100px"
                                                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${cadreeatitems.applyenddate}"/>"
                                                                       onclick="WdatePicker({doubleCalendar:true,skin:'whyGreen',minDate:'#F{$dp.$D(\'applystartdate${status.index+1}\')}',dateFmt:'yyyy-MM-dd'})"/>
                                                            </td>
                                                            <td>
                                                                <input id="diningplaceid${status.index+1}" name="wfcadreeatitems[${status.index+1}].diningplaceid"
                                                                       data-options="validType:'comboxValidate[\'diningplaceid${status.index+1}\',\'请選擇就餐地點\']',onSelect:function(){onChangeDP('',${status.index+1});}"
                                                                       style="width:80px;" panelHeight="auto" class="easyui-combobox" editable="false" value="${cadreeatitems.diningplaceid}"/>
                                                                <input id="diningplacename${status.index+1}" name="wfcadreeatitems[${status.index+1}].diningplacename" type="hidden" value="${cadreeatitems.diningplacename}"/>
                                                            </td>
                                                            <td>
                                                                <input id="applymemo${status.index+1}"
                                                                       name="wfcadreeatitems[${status.index+1}].applymemo"
                                                                       class="easyui-validatebox" style="width:300px;"
                                                                       value="${cadreeatitems.applymemo}"/>
                                                            </td>
                                                            <td>
                                                                <input type="image"
                                                                       src="${ctx}/static/images/deleteRow.png"
                                                                       onclick="cadreeatdeltr(${status.index+1});return false;"/>
                                                                <input id="shunxu${status.index+1}" type="hidden"
                                                                       name="wfcadreeatitems[${status.index+1}].shunxu"
                                                                       value="${status.index+1}"/>
                                                            </td>
                                                        </tr>
                                                    </c:forEach>
                                                </c:if>
                                                <c:if test="${wfcadreeatitems.size()==0 || wfcadreeatitems==null}">
                                                    <tr align="center" id="cadreeatitems1" class="wfcadreeatitemsTr">
                                                        <td>1</td>
                                                        <td>
                                                            <input id="applyno1"
                                                                   onblur="getUserNameByEmpno2(this,'apply',1);"
                                                                   name="wfcadreeatitems[1].applyno"
                                                                   class="easyui-validatebox" style="width:70px;"
                                                                   data-options="required:true" value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="applyname1"
                                                                   name="wfcadreeatitems[1].applyname" data-options="required:true"
                                                                   class="easyui-validatebox" style="width:70px;"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="applydeptname1" data-options="required:true"
                                                                   name="wfcadreeatitems[1].applydeptname"
                                                                   class="easyui-validatebox" style="width:120px;"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="applycostno1" data-options="required:true"
                                                                   name="wfcadreeatitems[1].applycostno"
                                                                   class="easyui-validatebox" style="width:70px;"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="artificialperson1" data-options="required:true"
                                                                   name="wfcadreeatitems[1].artificialperson"
                                                                   class="easyui-validatebox" style="width:70px;"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="applymanager1" data-options="required:true"
                                                                   name="wfcadreeatitems[1].applymanager"
                                                                   class="easyui-validatebox" style="width:70px;"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="applyleveltype1" data-options="required:true"
                                                                   name="wfcadreeatitems[1].applyleveltype"
                                                                   class="easyui-validatebox" style="width:70px;"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="applystartdate1"
                                                                   name="wfcadreeatitems[1].applystartdate"
                                                                   class="easyui-validatebox Wdate"
                                                                   data-options="required:true,prompt:'请选择开始时间'"
                                                                   style="width:100px"
                                                                   value=""
                                                                   onclick="WdatePicker({onpicked:function(){applyenddate1.click()},doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"/>
                                                        </td>
                                                        <td>
                                                            <input id="applyenddate1"
                                                                   name="wfcadreeatitems[1].applyenddate"
                                                                   class="easyui-validatebox Wdate"
                                                                   data-options="required:true,prompt:'请选择結束时间'"
                                                                   style="width:100px"
                                                                   value=""
                                                                   onclick="WdatePicker({doubleCalendar:true,skin:'whyGreen',minDate:'#F{$dp.$D(\'applystartdate1\')}',dateFmt:'yyyy-MM-dd'})"/>
                                                        </td>
                                                        <td>
                                                            <input id="diningplaceid1" name="wfcadreeatitems[1].diningplaceid"
                                                                   data-options="validType:'comboxValidate[\'diningplaceid1\',\'请選擇就餐地點\']',onSelect:function(){onChangeDP('',1);}"
                                                                   style="width:80px;" panelHeight="auto" class="easyui-combobox" editable="false" value=""/>
                                                            <input id="diningplacename1" name="wfcadreeatitems[1].diningplacename" type="hidden" value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="applymemo1" name="wfcadreeatitems[1].applymemo" class="easyui-validatebox" style="width:300px;" value=""/>
                                                        </td>
                                                        <td>
                                                            <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="cadreeatdeltr(1);return false;"/>
                                                            <input id="shunxu1" type="hidden" name="wfcadreeatitems[1].shunxu" value="1"/>
                                                        </td>
                                                    </tr>
                                                </c:if>
                                                </tbody>
                                                <tr align="left" class="nottr">
                                                    <td colspan="13" width="100%" style="text-align:left;padding-left:10px;">
                                                        <input type="button" id="cadreeatItemAdd" style="width:150px;float:left;" value="添加一行"/>
                                                    </td>
                                                </tr>
                                                <tr align="center">
                                                    <td colspan="2">
                                                        <a href="${ctx}/wfcadreeatprocess/downLoad/batchImportTpl" id="btnBatchImportTpl">模板下載</a>
                                                    </td>
                                                    <td colspan="11" class="td_style1">
                                                        &nbsp;&nbsp;&nbsp;&nbsp;<a href="#" id="batchImport" class="easyui-linkbutton"
                                                                                   data-options="iconCls:'icon-hamburg-cv'" style="width: 100px;"
                                                                                   onclick="openBatchImportWin();">批量導入</a>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                    <div id="tb1" style="height:auto" title="變更" data-options="refreshable: false">
                                        <div class="overdiv" width="1200px" id="overflowdivCha">
                                            <table id="wfcadreeatitemsChaTable" width="130%">
                                                <tr align="center">
                                                    <td width="30px">項次</td>
                                                    <td width="90px">工號&nbsp;<font color="red">*</font></td>
                                                    <td width="90px">姓名&nbsp;<font color="red">*</font></td>
                                                    <td width="150px">部門名稱&nbsp;<font color="red">*</font></td>
                                                    <td width="90px">掛靠費用代碼&nbsp;<font color="red">*</font></td>
                                                    <td width="90px">法人&nbsp;<font color="red">*</font></td>
                                                    <td width="90px">職稱&nbsp;<font color="red">*</font></td>
                                                    <td width="90px">資位&nbsp;<font color="red">*</font></td>
                                                    <td width="120px">就餐開始日期&nbsp;<font color="red">*</font></td>
                                                    <td width="120px">就餐結束日期&nbsp;<font color="red">*</font></td>
                                                    <td width="90px">原就餐地點&nbsp;<font color="red">*</font></td>
                                                    <td width="90px">現就餐地點&nbsp;<font color="red">*</font></td>
                                                    <td width="240px">備註</td>
                                                    <td width="50px">操作</td>
                                                </tr>
                                                <tbody id="info_BodyCha">
                                                <c:if test="${wfcadreeatitemsCha!=null&&wfcadreeatitemsCha.size()>0}">
                                                    <c:forEach items="${wfcadreeatitemsCha}" var="cadreeatitems" varStatus="status">
                                                        <tr align="center" id="cadreeatitemsCha${status.index+1}" class="wfcadreeatitemsChaTr">
                                                            <td>${status.index+1}</td>
                                                            <td>
                                                                <input id="applynoCha${status.index+1}"
                                                                       onblur="getUserNameByEmpnoCha(this,'apply',status.index+1);"
                                                                       name="wfcadreeatitemsCha[${status.index+1}].applyno"
                                                                       class="easyui-validatebox" style="width:70px;"
                                                                       data-options="required:true"
                                                                       value="${cadreeatitems.applyno}"/>
                                                            </td>
                                                            <td>
                                                                <input id="applynameCha${status.index+1}"
                                                                       name="wfcadreeatitemsCha[${status.index+1}].applyname"
                                                                       class="easyui-validatebox" style="width:70px;" data-options="required:true"
                                                                       value="${cadreeatitems.applyname}"/>
                                                            </td>
                                                            <td>
                                                                <input id="applydeptnameCha${status.index+1}"
                                                                       name="wfcadreeatitemsCha[${status.index+1}].applydeptname"
                                                                       class="easyui-validatebox" style="width:120px;" data-options="required:true"
                                                                       value="${cadreeatitems.applydeptname}"/>
                                                            </td>
                                                            <td>
                                                                <input id="applycostnoCha${status.index+1}"
                                                                       name="wfcadreeatitemsCha[${status.index+1}].applycostno"
                                                                       class="easyui-validatebox" style="width:70px;" data-options="required:true"
                                                                       value="${cadreeatitems.applycostno}"/>
                                                            </td>
                                                            <td>
                                                                <input id="artificialpersonCha${status.index+1}"
                                                                       name="wfcadreeatitemsCha[${status.index+1}].artificialperson"
                                                                       class="easyui-validatebox" style="width:70px;" data-options="required:true"
                                                                       value="${cadreeatitems.artificialperson}"/>
                                                            </td>
                                                            <td>
                                                                <input id="applymanagerCha${status.index+1}"
                                                                       name="wfcadreeatitemsCha[${status.index+1}].applymanager"
                                                                       class="easyui-validatebox" style="width:70px;" data-options="required:true"
                                                                       value="${cadreeatitems.applymanager}"/>
                                                            </td>
                                                            <td>
                                                                <input id="applyleveltypeCha${status.index+1}"
                                                                       name="wfcadreeatitemsCha[${status.index+1}].applyleveltype"
                                                                       class="easyui-validatebox" style="width:70px;" data-options="required:true"
                                                                       value="${cadreeatitems.applyleveltype}"/>
                                                            </td>
                                                            <td>
                                                                <input id="applystartdateCha${status.index+1}"
                                                                       name="wfcadreeatitemsCha[${status.index+1}].applystartdate"
                                                                       class="easyui-validatebox Wdate"
                                                                       data-options="required:true,prompt:'请选择开始时间'"
                                                                       style="width:100px"
                                                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${cadreeatitems.applystartdate}"/>"
                                                                       onclick="WdatePicker({onpicked:function(){applyenddate${status.index+1}.click()},doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"/>
                                                            </td>
                                                            <td>
                                                                <input id="applyenddateCha${status.index+1}"
                                                                       name="wfcadreeatitemsCha[${status.index+1}].applyenddate"
                                                                       class="easyui-validatebox Wdate"
                                                                       data-options="required:true,prompt:'请选择結束时间'"
                                                                       style="width:100px"
                                                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${cadreeatitems.applyenddate}"/>"
                                                                       onclick="WdatePicker({doubleCalendar:true,skin:'whyGreen',minDate:'#F{$dp.$D(\'applystartdateCha${status.index+1}\')}',dateFmt:'yyyy-MM-dd'})"/>
                                                            </td>
                                                            <td>
                                                                <input id="diningplaceoldCha${status.index+1}"
                                                                       name="wfcadreeatitemsCha[${status.index+1}].diningplaceold"
                                                                       class="easyui-validatebox" style="width:70px;" data-options="required:true"
                                                                       value="${cadreeatitems.diningplaceold}"/>
                                                            </td>
                                                            <td>
                                                                <input id="diningplaceidCha${status.index+1}" name="wfcadreeatitemsCha[${status.index+1}].diningplaceid"
                                                                       data-options="validType:'comboxValidate[\'diningplaceidCha${status.index+1}\',\'请選擇就餐地點\']',onSelect:function(){onChangeDP('Cha',${status.index+1});}"
                                                                       style="width:80px;" panelHeight="auto" class="easyui-combobox" editable="false" value="${cadreeatitems.diningplaceid}"/>
                                                                <input id="diningplacenameCha${status.index+1}" name="wfcadreeatitemsCha[${status.index+1}].diningplacename" type="hidden" value="${cadreeatitems.diningplacename}"/>
                                                            </td>
                                                            <td>
                                                                <input id="applymemoCha${status.index+1}"
                                                                       name="wfcadreeatitemsCha[${status.index+1}].applymemo"
                                                                       class="easyui-validatebox" style="width:300px;"
                                                                       value="${cadreeatitems.applymemo}"/>
                                                            </td>
                                                            <td>
                                                                <input type="image"
                                                                       src="${ctx}/static/images/deleteRow.png"
                                                                       onclick="cadreeatdeltrCha(${status.index+1});return false;"/>
                                                                <input id="shunxuCha${status.index+1}" type="hidden"
                                                                       name="wfcadreeatitemsCha[${status.index+1}].shunxu"
                                                                       value="${status.index+1}"/>
                                                            </td>
                                                        </tr>
                                                    </c:forEach>
                                                </c:if>
                                                <c:if test="${wfcadreeatitemsCha.size()==0 || wfcadreeatitemsCha==null}">
                                                    <tr align="center" id="cadreeatitemsCha1" class="wfcadreeatitemsChaTr">
                                                        <td>1</td>
                                                        <td>
                                                            <input id="applynoCha1"
                                                                   onblur="getUserNameByEmpnoCha(this,'apply',1);"
                                                                   name="wfcadreeatitemsCha[1].applyno"
                                                                   class="easyui-validatebox" style="width:70px;"
                                                                   data-options="required:true" value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="applynameCha1"
                                                                   name="wfcadreeatitemsCha[1].applyname" data-options="required:true"
                                                                   class="easyui-validatebox" style="width:70px;"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="applydeptnameCha1" data-options="required:true"
                                                                   name="wfcadreeatitemsCha[1].applydeptname"
                                                                   class="easyui-validatebox" style="width:120px;"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="applycostnoCha1" data-options="required:true"
                                                                   name="wfcadreeatitemsCha[1].applycostno"
                                                                   class="easyui-validatebox" style="width:70px;"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="artificialpersonCha1" data-options="required:true"
                                                                   name="wfcadreeatitemsCha[1].artificialperson"
                                                                   class="easyui-validatebox" style="width:70px;"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="applymanagerCha1" data-options="required:true"
                                                                   name="wfcadreeatitemsCha[1].applymanager"
                                                                   class="easyui-validatebox" style="width:70px;"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="applyleveltypeCha1" data-options="required:true"
                                                                   name="wfcadreeatitemsCha[1].applyleveltype"
                                                                   class="easyui-validatebox" style="width:70px;"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="applystartdateCha1"
                                                                   name="wfcadreeatitemsCha[1].applystartdate"
                                                                   class="easyui-validatebox Wdate"
                                                                   data-options="required:true,prompt:'请选择开始时间'"
                                                                   style="width:100px"
                                                                   value=""
                                                                   onclick="WdatePicker({onpicked:function(){applyenddate1.click()},doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"/>
                                                        </td>
                                                        <td>
                                                            <input id="applyenddateCha1"
                                                                   name="wfcadreeatitemsCha[1].applyenddate"
                                                                   class="easyui-validatebox Wdate"
                                                                   data-options="required:true,prompt:'请选择結束时间'"
                                                                   style="width:100px"
                                                                   value=""
                                                                   onclick="WdatePicker({doubleCalendar:true,skin:'whyGreen',minDate:'#F{$dp.$D(\'applystartdateCha1\')}',dateFmt:'yyyy-MM-dd'})"/>
                                                        </td>
                                                        <td>
                                                            <input id="diningplaceoldCha1"
                                                                   name="wfcadreeatitemsCha[1].diningplaceold"
                                                                   class="easyui-validatebox" style="width:70px;" data-options="required:true"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="diningplaceidCha1" name="wfcadreeatitemsCha[1].diningplaceid"
                                                                   data-options="validType:'comboxValidate[\'diningplaceidCha1\',\'请選擇就餐地點\']',onSelect:function(){onChangeDP('Cha',1);}"
                                                                   style="width:80px;" panelHeight="auto" class="easyui-combobox" editable="false" value=""/>
                                                            <input id="diningplacenameCha1" name="wfcadreeatitemsCha[1].diningplacename" type="hidden" value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="applymemoCha1" name="wfcadreeatitemsCha[1].applymemo" class="easyui-validatebox" style="width:300px;" value=""/>
                                                        </td>
                                                        <td>
                                                            <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="cadreeatdeltrCha(1);return false;"/>
                                                            <input id="shunxuCha1" type="hidden" name="wfcadreeatitemsCha[1].shunxu" value="1"/>
                                                        </td>
                                                    </tr>
                                                </c:if>
                                                </tbody>
                                                <tr align="left" class="nottr">
                                                    <td colspan="14" width="100%" style="text-align:left;padding-left:10px;">
                                                        <input type="button" id="cadreeatItemAddCha" style="width:150px;float:left;" value="添加一行"/>
                                                    </td>
                                                </tr>
                                                <tr align="center">
                                                    <td colspan="2">
                                                        <a href="${ctx}/wfcadreeatprocess/downLoad/batchImportTplCha" id="btnBatchImportTplCha">模板下載</a>
                                                    </td>
                                                    <td colspan="12" class="td_style1">
                                                        &nbsp;&nbsp;&nbsp;&nbsp;<a href="#" id="batchImport2" class="easyui-linkbutton"
                                                                                   data-options="iconCls:'icon-hamburg-cv'" style="width: 100px;"
                                                                                   onclick="openBatchImportWinCha();">批量導入</a>
                                                    </td>
                                                </tr>

                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <c:choose>
                                    <c:when test="${file.size()>0}">
                                        <input type="hidden" id="attachids" name="wfcadreeat.attachids" value="${wfcadreeatprocessEntity.attachids }"/>
                                    </c:when>
                                    <c:otherwise>
                                        <input type="hidden" id="attachids" name="wfcadreeat.attachids" value=""/>
                                    </c:otherwise>
                                </c:choose>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="9" class="td_style2">
                                <textarea class="easyui-validatebox"
                                          disabled readonly
                                          style="width:99%;height:80px;resize:none;background-color: #F2F5F7;outline: none;" rows="5" cols="4">${applyMemo}</textarea>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','商務餐廳幹部就餐申請表');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <c:choose>
                                <c:when test="${wfcadreeatprocessEntity.makerfactoryid=='CAATY'||wfcadreeatprocessEntity.makerfactoryid=='IPETY'}">
                                    <td colspan="10" style="text-align:left;">
                                        <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                            <tr>
                                                <td style="border:none">
                                                    <table width="18%" style="float: left;margin-left: 5px;" id="cfcschargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['cfcschargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole2(610,'cfcschargeTable','cfcschargeno','cfcschargename',$('#dealfactoryid').combobox('getValue'),'wfcadreeat')"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="cfcschargeno" name="wfcadreeat.cfcschargeno"
                                                                       class="easyui-validatebox"
                                                                       data-options="width:80,required:${requiredMap['cfcschargeno']}"
                                                                       readonly
                                                                       value="${wfcadreeatprocessEntity.cfcschargeno }"/><c:if
                                                                    test="${requiredMap['cfcschargeno'].equals('true')}"><font
                                                                    color="red">*</font></c:if>
                                                                /<input id="cfcschargename" name="wfcadreeat.cfcschargename"
                                                                        readonly class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['cfcschargeno']}"
                                                                        value="${wfcadreeatprocessEntity.cfcschargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <table width="18%" style="float: left;margin-left: 5px;" id="cfzgchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['cfzgchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole2(611,'cfzgchargeTable','cfzgchargeno','cfzgchargename',$('#dealfactoryid').combobox('getValue'),'wfcadreeat')"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="cfzgchargeno" name="wfcadreeat.cfzgchargeno"
                                                                       class="easyui-validatebox"
                                                                       data-options="width:80,required:${requiredMap['cfzgchargeno']}"
                                                                       readonly
                                                                       value="${wfcadreeatprocessEntity.cfzgchargeno }"/><c:if
                                                                    test="${requiredMap['cfzgchargeno'].equals('true')}"><font
                                                                    color="red">*</font></c:if>
                                                                /<input id="cfzgchargename" name="wfcadreeat.cfzgchargename"
                                                                        readonly class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['cfzgchargeno']}"
                                                                        value="${wfcadreeatprocessEntity.cfzgchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <table width="18%" style="float: left;margin-left: 5px;" id="hqrzchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['hqrzchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole2(600,'hqrzchargeTable','hqrzchargeno','hqrzchargename',$('#dealfactoryid').combobox('getValue'),'wfcadreeat')"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="hqrzchargeno" name="wfcadreeat.hqrzchargeno"
                                                                       class="easyui-validatebox"
                                                                       data-options="width:80,required:${requiredMap['hqrzchargeno']}"
                                                                       readonly
                                                                       value="${wfcadreeatprocessEntity.hqrzchargeno }"/><c:if
                                                                    test="${requiredMap['hqrzchargeno'].equals('true')}"><font
                                                                    color="red">*</font></c:if>
                                                                /<input id="hqrzchargename" name="wfcadreeat.hqrzchargename"
                                                                        readonly class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['hqrzchargeno']}"
                                                                        value="${wfcadreeatprocessEntity.hqrzchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <table width="18%" style="float: left;margin-left: 5px;"
                                                           id="hqjgchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['hqjgchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole2(81,'hqjgchargeTable','hqjgchargeno','hqjgchargename',$('#dealfactoryid').combobox('getValue'),'wfcadreeat')"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="hqjgchargeno"
                                                                       name="wfcadreeat.hqjgchargeno"
                                                                       class="easyui-validatebox"
                                                                       data-options="width:80,required:${requiredMap['hqjgchargeno']}"
                                                                       readonly
                                                                       value="${wfcadreeatprocessEntity.hqjgchargeno }"/><c:if
                                                                    test="${requiredMap['hqjgchargeno'].equals('true')}"><font
                                                                    color="red">*</font></c:if>
                                                                /<input id="hqjgchargename" name="wfcadreeat.hqjgchargename"
                                                                        readonly class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['hqjgchargeno']}"
                                                                        value="${wfcadreeatprocessEntity.hqjgchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <table width="18%" style="float: left;margin-left: 5px;"
                                                           id="sqdwzgchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: center;">${requiredMap['sqdwzgchargeno_name']}
                                                                            <a href="#"
                                                                               onclick="addHq('wfcadreeat','sqdwzgcharge');">添加一位</a>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="sqdwzgchargeno"
                                                                       onblur="gethqUserNameByEmpno(this,'sqdwzgcharge');"
                                                                       name="wfcadreeat.sqdwzgchargeno"
                                                                       class="easyui-validatebox"
                                                                       data-options="width:80,required:${requiredMap['sqdwzgchargeno']}"
                                                                       value="${wfcadreeatprocessEntity.sqdwzgchargeno }"/><c:if
                                                                    test="${requiredMap['sqdwzgchargeno'].equals('true')}"><font
                                                                    color="red">*</font></c:if>
                                                                /<input id="sqdwzgchargename" name="wfcadreeat.sqdwzgchargename"
                                                                        readonly class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['sqdwzgchargeno']}"
                                                                        value="${wfcadreeatprocessEntity.sqdwzgchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="border:none">
                                                    <table width="18%" style="float: left;margin-left: 5px;" id="gcfdchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['gcfdchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole2(647,'gcfdchargeTable','gcfdchargeno','gcfdchargename',$('#dealfactoryid').combobox('getValue'),'wfcadreeat')"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="gcfdchargeno" name="wfcadreeat.gcfdchargeno"
                                                                       class="easyui-validatebox"
                                                                       data-options="width:80,required:${requiredMap['gcfdchargeno']}"
                                                                       readonly
                                                                       value="${wfcadreeatprocessEntity.gcfdchargeno }"/><c:if
                                                                    test="${requiredMap['gcfdchargeno'].equals('true')}"><font
                                                                    color="red">*</font></c:if>
                                                                /<input id="gcfdchargename" name="wfcadreeat.gcfdchargename"
                                                                        readonly class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['gcfdchargeno']}"
                                                                        value="${wfcadreeatprocessEntity.gcfdchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </c:when>
                                <c:otherwise>
                                    <td colspan="10" style="text-align:left;">
                                        <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                            <tr>
                                                <td style="border:none">
                                                    <table width="18%" style="float: left;margin-left: 5px;" id="zwqchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['zwqchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole2(270,'zwqchargeTable','zwqchargeno','zwqchargename',$('#dealfactoryid').combobox('getValue'),'wfcadreeat')"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="zwqchargeno" name="wfcadreeat.zwqchargeno"
                                                                       class="easyui-validatebox"
                                                                       data-options="width:80,required:${requiredMap['zwqchargeno']}"
                                                                       readonly
                                                                       value="${wfcadreeatprocessEntity.zwqchargeno }"/><c:if
                                                                    test="${requiredMap['zwqchargeno'].equals('true')}"><font
                                                                    color="red">*</font></c:if>
                                                                /<input id="zwqchargename" name="wfcadreeat.zwqchargename"
                                                                        readonly class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['zwqchargeno']}"
                                                                        value="${wfcadreeatprocessEntity.zwqchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>

                                                    <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealnofactoryid').val())"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="kchargeno" name="wfcadreeat.kchargeno"
                                                                       class="easyui-validatebox"
                                                                       data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                       readonly
                                                                       value="${wfcadreeatprocessEntity.kchargeno }"/><c:if
                                                                    test="${requiredMap['kchargeno'].equals('true')}"><font
                                                                    color="red">*</font></c:if>
                                                                /<input id="kchargename" name="wfcadreeat.kchargename"
                                                                        readonly class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                        value="${wfcadreeatprocessEntity.kchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>

                                                    <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealnofactoryid').val())"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="bchargeno" name="wfcadreeat.bchargeno"
                                                                       class="easyui-validatebox"
                                                                       data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                       readonly
                                                                       value="${wfcadreeatprocessEntity.bchargeno }"/><c:if
                                                                    test="${requiredMap['bchargeno'].equals('true')}"><font
                                                                    color="red">*</font></c:if>
                                                                /<input id="bchargename" name="wfcadreeat.bchargename"
                                                                        readonly class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                        value="${wfcadreeatprocessEntity.bchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>

                                                    <table width="18%" style="float: left;margin-left: 5px;"
                                                           id="zwshchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['zwshchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole2(271,'zwshchargeTable','zwshchargeno','zwshchargename',$('#dealfactoryid').combobox('getValue'),'wfcadreeat')"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="zwshchargeno" name="wfcadreeat.zwshchargeno"
                                                                       class="easyui-validatebox"
                                                                       data-options="width:80,required:${requiredMap['zwshchargeno']}"
                                                                       readonly
                                                                       value="${wfcadreeatprocessEntity.zwshchargeno }"/><c:if
                                                                    test="${requiredMap['zwshchargeno'].equals('true')}"><font
                                                                    color="red">*</font></c:if>
                                                                /<input id="zwshchargename" name="wfcadreeat.zwshchargename"
                                                                        readonly class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['zwshchargeno']}"
                                                                        value="${wfcadreeatprocessEntity.zwshchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>

                                                    <table width="18%" style="float: left;margin-left: 5px;"
                                                           id="zwhzchargeTable">
                                                        <tr>
                                                            <td>
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td style="border: none;text-align: right;">${requiredMap['zwhzchargeno_name']}</td>
                                                                        <td style="border: none;">
                                                                            <div class="float_L qhUserIcon"
                                                                                 onclick="selectRole2(272,'zwhzchargeTable','zwhzchargeno','zwhzchargename',$('#dealfactoryid').combobox('getValue'),'wfcadreeat')"></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td><input id="zwhzchargeno" name="wfcadreeat.zwhzchargeno"
                                                                       class="easyui-validatebox"
                                                                       data-options="width:80,required:${requiredMap['zwhzchargeno']}"
                                                                       readonly
                                                                       value="${wfcadreeatprocessEntity.zwhzchargeno }"/><c:if
                                                                    test="${requiredMap['zwhzchargeno'].equals('true')}"><font
                                                                    color="red">*</font></c:if>
                                                                /<input id="zwhzchargename" name="wfcadreeat.zwhzchargename"
                                                                        readonly class="easyui-validatebox"
                                                                        data-options="width:80,required:${requiredMap['zwhzchargeno']}"
                                                                        value="${wfcadreeatprocessEntity.zwhzchargename }"/>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </c:otherwise>
                            </c:choose>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfcadreeatprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="canelTask('${wfcadreeatprocessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabledReject" type="hidden" value="disabled"/>
    <input type="hidden" id="dealfactorymakeid" value="${wfcadreeatprocessEntity.makerfactoryid }"/>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
    <div id="dlg"></div>
</form>
<div id="optionWin" class="easyui-window" title="商務餐廳幹部就餐申請單信息導入" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%">
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="file" type="file" style="width: 300px"
                           accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>
                    <input id="isExcel2003" name="isExcel2003" type="hidden" value="">

                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <span ID="labelListAddResult"></span><a href="${ctx}/wfcadreeatprocess/downLoad/errorExcel" id="downloadError"
                                                            plain="true">查看錯誤信息</a>
                </td>
            </tr>
        </table>
    </form>
</div>
</body>
</html>