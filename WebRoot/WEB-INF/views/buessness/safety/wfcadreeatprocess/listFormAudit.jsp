<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>商務餐廳幹部就餐申請表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/safety/wfcadreeatprocess.js?random=<%= Math.random()%>'></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfcadreeatprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfcadreeatprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfcadreeatprocessEntity.serialno }"/>
    <input id="applytype" name="applytype" type="hidden" value="${wfcadreeatprocessEntity.applytype }"/>
    <input id="workstatus" name="workstatus" type="hidden" value="${wfcadreeatprocessEntity.workstatus }"/>
    <div class="commonW">
        <div class="headTitle">${titleMemo}</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfcadreeatprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfcadreeatprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfcadreeatprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfcadreeatprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfcadreeatprocessEntity.makerno}/${wfcadreeatprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">提報人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="5%">提報人工號</td>
                            <td width="5%" class="td_style2">${wfcadreeatprocessEntity.dealno}</td>
                            <td width="4%">提報人</td>
                            <td width="6%" class="td_style2">${wfcadreeatprocessEntity.dealname}</td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style2">${wfcadreeatprocessEntity.dealdeptno}</td>
                            <td width="4%">費用代碼</td>
                            <td width="6%" class="td_style2">${wfcadreeatprocessEntity.dealcostno}</td>
                            <td width="4%">所在廠區</td>
                            <td width="6%" class="td_style2">${wfcadreeatprocessEntity.dealfactoryname}</td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱</td>
                            <td colspan="3" class="td_style2">${wfcadreeatprocessEntity.dealdeptname}</td>
                            <td>聯繫方式</td>
                            <td class="td_style2">${wfcadreeatprocessEntity.dealtel}</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfcadreeatprocessEntity.dealemail}</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請內容</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div id="tt" class="easyui-tabs" style="width: 1200px">
                                    <div id="tb"  style="height:auto" title="新增" data-options="refreshable: false">
                                        <div class="overdiv" width="1200px" id="overflowdiv">
                                            <table id="wfcadreeatitemsTable" width="120%">
                                                <tr align="center">
                                                    <td width="30px">項次</td>
                                                    <td width="80px">工號</td>
                                                    <td width="80px">姓名</td>
                                                    <td width="150px">部門名稱</td>
                                                    <td width="80px">掛靠費用代碼</td>
                                                    <td width="160px">法人</td>
                                                    <td width="60px">職稱</td>
                                                    <td width="50px">資位</td>
                                                    <td width="80px">就餐開始日期</td>
                                                    <td width="80px">就餐結束日期</td>
                                                    <td width="90px">就餐地點</td>
                                                    <td width="240px">備註</td>
                                                </tr>
                                                <tbody id="info_Body0">
                                                <c:if test="${wfcadreeatitems!=null&&wfcadreeatitems.size()>0}">
                                                    <c:forEach items="${wfcadreeatitems}" var="cadreeatitems" varStatus="status">
                                                        <tr align="center" id="cadreeatitems${status.index+1}" class="wfcadreeatitemsTr">
                                                            <td>${status.index+1}</td>
                                                            <td>${cadreeatitems.applyno}</td>
                                                            <td>${cadreeatitems.applyname}</td>
                                                            <td>${cadreeatitems.applydeptname}</td>
                                                            <td>${cadreeatitems.applycostno}</td>
                                                            <td>${cadreeatitems.artificialperson}</td>
                                                            <td>${cadreeatitems.applymanager}</td>
                                                            <td>${cadreeatitems.applyleveltype}</td>
                                                            <td><fmt:formatDate  pattern="yyyy-MM-dd" value="${cadreeatitems.applystartdate}"/></td>
                                                            <td><fmt:formatDate  pattern="yyyy-MM-dd" value="${cadreeatitems.applyenddate}"/></td>
                                                            <td>${cadreeatitems.diningplacename}</td>
                                                            <td>${cadreeatitems.applymemo}</td>
                                                        </tr>
                                                    </c:forEach>
                                                </c:if>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div id="tb1" style="height:auto" title="變更" data-options="refreshable: false">
                                        <div class="overdiv" width="1200px" id="overflowdivCha">
                                            <table id="wfcadreeatitemsChaTable" width="130%">
                                                <tr align="center">
                                                    <td width="30px">項次</td>
                                                    <td width="80px">工號</td>
                                                    <td width="80px">姓名</td>
                                                    <td width="150px">部門名稱</td>
                                                    <td width="80px">掛靠費用代碼</td>
                                                    <td width="160px">法人</td>
                                                    <td width="60px">職稱</td>
                                                    <td width="50px">資位</td>
                                                    <td width="80px">就餐開始日期</td>
                                                    <td width="80px">就餐結束日期</td>
                                                    <td width="90px">原就餐地點</td>
                                                    <td width="90px">現就餐地點</td>
                                                    <td width="240px">備註</td>
                                                </tr>
                                                <tbody id="info_BodyCha">
                                                <c:if test="${wfcadreeatitemsCha!=null&&wfcadreeatitemsCha.size()>0}">
                                                    <c:forEach items="${wfcadreeatitemsCha}" var="cadreeatitems" varStatus="status">
                                                        <tr align="center" id="cadreeatitemsCha${status.index+1}" class="wfcadreeatitemsChaTr">
                                                            <td>${status.index+1}</td>
                                                            <td>${cadreeatitems.applyno}</td>
                                                            <td>${cadreeatitems.applyname}</td>
                                                            <td>${cadreeatitems.applydeptname}</td>
                                                            <td>${cadreeatitems.applycostno}</td>
                                                            <td>${cadreeatitems.artificialperson}</td>
                                                            <td>${cadreeatitems.applymanager}</td>
                                                            <td>${cadreeatitems.applyleveltype}</td>
                                                            <td><fmt:formatDate  pattern="yyyy-MM-dd" value="${cadreeatitems.applystartdate}"/></td>
                                                            <td><fmt:formatDate  pattern="yyyy-MM-dd" value="${cadreeatitems.applyenddate}"/></td>
                                                            <td>${cadreeatitems.diningplaceold}</td>
                                                            <td>${cadreeatitems.diningplacename}</td>
                                                            <td>${cadreeatitems.applymemo}</td>
                                                        </tr>
                                                    </c:forEach>
                                                </c:if>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <input type="hidden" id="attachids" name="wfcadreeat.attachids" value="${wfcadreeatprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">批註</td>
                            <td width="90%">
						<textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wfcadreeatprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','商務餐廳幹部就餐申請表');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfcadreeatprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<input id="disOrEnabledReject" type="hidden" value="disabled"/>
<div id="dlg"></div>
</body>
</html>