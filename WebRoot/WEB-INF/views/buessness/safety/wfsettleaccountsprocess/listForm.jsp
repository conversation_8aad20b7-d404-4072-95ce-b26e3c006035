<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>餐廳就餐費用結算申請表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }

        .overdiv {
            overflow-y: auto;
            overflow-x: auto;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfsettleaccountsprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfsettleaccountsprocessEntity.id }"/>
    <input id="serialno" name="wfsettleaccounts.serialno" type="hidden" value="${wfsettleaccountsprocessEntity.serialno }"/>
    <input id="makerno" name="wfsettleaccounts.makerno" type="hidden" value="${wfsettleaccountsprocessEntity.makerno }"/>
    <input id="makername" name="wfsettleaccounts.makername" type="hidden" value="${wfsettleaccountsprocessEntity.makername }"/>
    <input id="makerdeptno" name="wfsettleaccounts.makerdeptno" type="hidden" value="${wfsettleaccountsprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfsettleaccounts.makerfactoryid" type="hidden" value="${wfsettleaccountsprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">餐廳就餐費用結算申請表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfsettleaccountsprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfsettleaccountsprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfsettleaccountsprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfsettleaccountsprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfsettleaccountsprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfsettleaccountsprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfsettleaccountsprocessEntity.makerno}/${wfsettleaccountsprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="5%">提報人工號&nbsp;<font color="red">*</font></td>
                            <td width="5%" class="td_style1">
                                <input id="dealno" name="wfsettleaccounts.dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfsettleaccountsprocessEntity.dealno }" onblur="queryUserInfo('deal');"/>
                            </td>
                            <td width="4%">提報人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="wfsettleaccounts.dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfsettleaccountsprocessEntity.dealname }"/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="wfsettleaccounts.dealdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfsettleaccountsprocessEntity.dealdeptno }"/>
                            </td>
                            <td width="4%">費用代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealcostno" name="wfsettleaccounts.dealcostno" class="easyui-validatebox inputCss"
                                       data-options="width: 80"
                                       value="${wfsettleaccountsprocessEntity.dealcostno }"/>
                            </td>
                            <td width="4%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealfactoryid" name="wfsettleaccounts.dealfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfsettleaccountsprocessEntity.dealfactoryid }"
                                       data-options="width: 120,validType:'comboxValidate[\'dealfactoryid\',\'请選擇所在廠區\']',onSelect:function(){onchangeFactory('deal');}"/>
                                <input id="dealfactoryname" name="wfsettleaccounts.dealfactoryname" type="hidden"
                                       value="${wfsettleaccountsprocessEntity.dealfactoryname}"/>
                                <input id="dealnofactoryid" name="wfsettleaccounts.dealnofactoryid" type="hidden"
                                       value="${wfsettleaccountsprocessEntity.dealnofactoryid}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealdeptname" name="wfsettleaccounts.dealdeptname" class="easyui-validatebox"
                                       data-options="width: 400,required:true"
                                       value="${wfsettleaccountsprocessEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealtel" name="wfsettleaccounts.dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfsettleaccountsprocessEntity.dealtel }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="validApplyTel('dealtel')"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="wfsettleaccounts.dealemail" class="easyui-validatebox"
                                       value="${wfsettleaccountsprocessEntity.dealemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'dealemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>供應包商&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style2">
                                <div class="supplierDiv" style="float: left;"></div>
                                <input id="supplier" name="wfsettleaccounts.supplier"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfsettleaccountsprocessEntity.supplier}"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請內容</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <%--<div style="overflow-x: auto;width: 1200px;">--%>
                                <c:if test="${wfsettleaccountsitems!=null && wfsettleaccountsitems.size()>0&& wfsettleaccountsitems.size()>10}">
                                <div style="overflow-x: auto;overflow-y: auto;height: 400px" width="1200px">
                                    </c:if>
                                    <c:if test="${wfsettleaccountsitems!=null && wfsettleaccountsitems.size()>0&& wfsettleaccountsitems.size()<10}">
                                    <div style="overflow-x: auto;overflow-y: auto" width="1200px">
                                        </c:if>
                                        <c:if test="${wfsettleaccountsitems==null}">
                                        <div class="overdiv" width="1200px" id="overflowdiv">
                                            </c:if>
                                            <table id="wfsettleaccountsitemsTable" width="120%">
                                                <tr align="center">
                                                    <td width="30px">項次</td>
                                                    <td width="80px">類別&nbsp;<font color="red">*</font></td>
                                                    <td width="120px">開始日期&nbsp;<font color="red">*</font></td>
                                                    <td width="120px">結束日期&nbsp;<font color="red">*</font></td>
                                                    <td width="90px">早餐</td>
                                                    <td width="90px">中餐</td>
                                                    <td width="90px">晚餐</td>
                                                    <td width="90px">夜宵</td>
                                                    <td width="90px">掛靠費用代碼&nbsp;<font color="red">*</font></td>
                                                    <td width="90px">人數合計&nbsp;<font color="red">*</font></td>
                                                    <td width="90px">金額合計&nbsp;<font color="red">*</font></td>
                                                    <td width="300px">需求說明</td>
                                                    <td width="50px">操作</td>
                                                </tr>
                                                <tbody id="info_Body0">
                                                <c:if test="${wfsettleaccountsitems!=null&&wfsettleaccountsitems.size()>0}">
                                                    <c:forEach items="${wfsettleaccountsitems}" var="settleaccountsitems" varStatus="status">
                                                        <tr align="center" id="settleaccountsitems${status.index+1}">
                                                            <td>${status.index+1}</td>
                                                            <td>
                                                                <input id="applytype${status.index+1}" name="wfsettleaccountsitems[${status.index+1}].applytype" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onSelect:function(){onchangeApplyType(${status.index+1});}" style="width:60px;"
                                                                       class="easyui-combobox" editable="false" value="${settleaccountsitems.applytype}"/>
                                                            </td>
                                                            <td>
                                                                <input id="applystartdate${status.index+1}"
                                                                       name="wfsettleaccountsitems[${status.index+1}].applystartdate"
                                                                       class="easyui-validatebox Wdate"
                                                                       data-options="required:true,prompt:'请选择开始时间'"
                                                                       style="width:100px"
                                                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${settleaccountsitems.applystartdate}"/>"
                                                                       onclick="WdatePicker({onpicked:function(){applyenddate${status.index+1}.click()},doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd'})"/>
                                                            </td>
                                                            <td>
                                                                <input id="applyenddate${status.index+1}"
                                                                       name="wfsettleaccountsitems[${status.index+1}].applyenddate"
                                                                       class="easyui-validatebox Wdate"
                                                                       data-options="required:true,prompt:'请选择結束时间'"
                                                                       style="width:100px"
                                                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${settleaccountsitems.applyenddate}"/>"
                                                                       onclick="WdatePicker({doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd'})"/>
                                                            </td>
                                                            <td>
                                                                <input id="breakfast${status.index+1}"
                                                                       name="wfsettleaccountsitems[${status.index+1}].breakfast"
                                                                       class="easyui-validatebox" style="width:70px;"
                                                                       value="${settleaccountsitems.breakfast}"/>
                                                            </td>
                                                            <td>
                                                                <input id="chinesemeal${status.index+1}"
                                                                       name="wfsettleaccountsitems[${status.index+1}].chinesemeal"
                                                                       class="easyui-validatebox" style="width:70px;"
                                                                       value="${settleaccountsitems.chinesemeal}"/>
                                                            </td>
                                                            <td>
                                                                <input id="dinner${status.index+1}"
                                                                       name="wfsettleaccountsitems[${status.index+1}].dinner"
                                                                       class="easyui-validatebox" style="width:70px;"
                                                                       value="${settleaccountsitems.dinner}"/>
                                                            </td>
                                                            <td>
                                                                <input id="midnightsnack${status.index+1}"
                                                                       name="wfsettleaccountsitems[${status.index+1}].midnightsnack"
                                                                       class="easyui-validatebox" style="width:70px;"
                                                                       value="${settleaccountsitems.midnightsnack}"/>
                                                            </td>
                                                            <td>
                                                                <input id="relycostno${status.index+1}"
                                                                       name="wfsettleaccountsitems[${status.index+1}].relycostno"
                                                                       class="easyui-validatebox" style="width:70px;" data-options="required:true"
                                                                       value="${settleaccountsitems.relycostno}"/>
                                                            </td>
                                                            <td>
                                                                <input id="peopletotal${status.index+1}"
                                                                       name="wfsettleaccountsitems[${status.index+1}].peopletotal"
                                                                       class="easyui-validatebox" style="width:70px;" data-options="required:true"
                                                                       value="${settleaccountsitems.peopletotal}"/>
                                                            </td>
                                                            <td>
                                                                <input id="moneytotal${status.index+1}"
                                                                       name="wfsettleaccountsitems[${status.index+1}].moneytotal"
                                                                       class="easyui-validatebox" style="width:70px;" data-options="required:true"
                                                                       value="${settleaccountsitems.moneytotal}"/>
                                                            </td>
                                                            <td>
                                                                <input id="applymemo${status.index+1}"
                                                                       name="wfsettleaccountsitems[${status.index+1}].applymemo"
                                                                       class="easyui-validatebox" style="width:320px;"
                                                                       value="${settleaccountsitems.applymemo}"/>
                                                            </td>
                                                            <td>
                                                                <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="settleaccountsdeltr(${status.index+1});return false;"/>
                                                                <input id="shunxu${status.index+1}" type="hidden" name="wfsettleaccountsitems[${status.index+1}].shunxu" value="${status.index+1}"/>
                                                            </td>
                                                        </tr>
                                                    </c:forEach>
                                                </c:if>
                                                <c:if test="${wfsettleaccountsitems.size()==0 || wfsettleaccountsitems==null}">
                                                    <tr align="center" id="settleaccountsitems1">
                                                        <td>1</td>
                                                        <td>
                                                            <input id="applytype1" name="wfsettleaccountsitems[1].applytype" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadApplyType(1);}" style="width:60px;"
                                                                   class="easyui-combobox" editable="false" value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="applystartdate1"
                                                                   name="wfsettleaccountsitems[1].applystartdate"
                                                                   class="easyui-validatebox Wdate"
                                                                   data-options="required:true,prompt:'请选择开始时间'"
                                                                   style="width:100px"
                                                                   value=""
                                                                   onclick="WdatePicker({onpicked:function(){applyenddate1.click()},doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd'})"/>
                                                        </td>
                                                        <td>
                                                            <input id="applyenddate1"
                                                                   name="wfsettleaccountsitems[1].applyenddate"
                                                                   class="easyui-validatebox Wdate"
                                                                   data-options="required:true,prompt:'请选择結束时间'"
                                                                   style="width:100px"
                                                                   value=""
                                                                   onclick="WdatePicker({doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd'})"/>
                                                        </td>
                                                        <td>
                                                            <input id="breakfast1"
                                                                   name="wfsettleaccountsitems[1].breakfast"
                                                                   class="easyui-validatebox" style="width:70px;"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="chinesemeal1"
                                                                   name="wfsettleaccountsitems[1].chinesemeal"
                                                                   class="easyui-validatebox" style="width:70px;"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="dinner1"
                                                                   name="wfsettleaccountsitems[1].dinner"
                                                                   class="easyui-validatebox" style="width:70px;"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="midnightsnack1"
                                                                   name="wfsettleaccountsitems[1].midnightsnack"
                                                                   class="easyui-validatebox" style="width:70px;"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="relycostno1"
                                                                   name="wfsettleaccountsitems[1].relycostno"
                                                                   class="easyui-validatebox" style="width:70px;" data-options="required:true"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="peopletotal1"
                                                                   name="wfsettleaccountsitems[1].peopletotal"
                                                                   class="easyui-validatebox" style="width:70px;" data-options="required:true"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="moneytotal1"
                                                                   name="wfsettleaccountsitems[1].moneytotal"
                                                                   class="easyui-validatebox" style="width:70px;" data-options="required:true"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input id="applymemo1"
                                                                   name="wfsettleaccountsitems[1].applymemo"
                                                                   class="easyui-validatebox" style="width:320px;"
                                                                   value=""/>
                                                        </td>
                                                        <td>
                                                            <input type="image" src="${ctx}/static/images/deleteRow.png" onclick="settleaccountsdeltr(1);return false;"/>
                                                            <input id="shunxu1" type="hidden" name="wfsettleaccountsitems[1].shunxu" value="1"/>
                                                        </td>
                                                    </tr>
                                                </c:if>
                                                </tbody>
                                                <tr align="left" class="nottr">
                                                    <td colspan="13" width="100%" style="text-align:left;padding-left:10px;">
                                                        <input type="button" id="settleaccountsItemAdd" style="width:150px;float:left;" value="添加一行"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">
                                <a href="${ctx}/wfsettleaccountsprocess/downLoad/batchImportTpl" id="btnBatchImportTpl">模板下載</a>
                            </td>
                            <td width="90%" class="td_style1">
                                &nbsp;&nbsp;&nbsp;&nbsp;<a href="#" id="batchImport" class="easyui-linkbutton"
                                                           data-options="iconCls:'icon-hamburg-cv'" style="width: 100px;"
                                                           onclick="openBatchImportWin();">批量導入</a>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="wfsettleaccounts.attachids" value="${wfsettleaccountsprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            <td colspan="9" class="td_style2">
                               <input id="applyMemo" type="hidden" value="${applyMemo}"/>
                                   <textarea id="applyTextareaMemo" class="easyui-validatebox"
                                             disabled readonly
                                             style="width:99%;resize:none;background-color: #F2F5F7;border: 0px;outline: none;">${applyMemo}</textarea>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_jiucanfeiyongjiesuanshenqingdan','餐廳就餐費用結算申請表','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zwqchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zwqchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(270,'zwqchargeTable','zwqchargeno','zwqchargename',$('#dealfactoryid').combobox('getValue'),'wfsettleaccounts')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zwqchargeno" name="wfsettleaccounts.zwqchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zwqchargeno']}"
                                                               readonly
                                                               value="${wfsettleaccountsprocessEntity.zwqchargeno }"/><c:if
                                                            test="${requiredMap['zwqchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zwqchargename" name="wfsettleaccounts.zwqchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zwqchargeno']}"
                                                                value="${wfsettleaccountsprocessEntity.zwqchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="kchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['kchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealnofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="wfsettleaccounts.kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wfsettleaccountsprocessEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="wfsettleaccounts.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfsettleaccountsprocessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="bchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['bchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealnofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="bchargeno" name="wfsettleaccounts.bchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['bchargeno']}"
                                                               readonly
                                                               value="${wfsettleaccountsprocessEntity.bchargeno }"/><c:if
                                                            test="${requiredMap['bchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="bchargename" name="wfsettleaccounts.bchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['bchargeno']}"
                                                                value="${wfsettleaccountsprocessEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="cchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['cchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#dealnofactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="cchargeno" name="wfsettleaccounts.cchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['cchargeno']}"
                                                               readonly
                                                               value="${wfsettleaccountsprocessEntity.cchargeno }"/><c:if
                                                            test="${requiredMap['cchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="cchargename" name="wfsettleaccounts.cchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['cchargeno']}"
                                                                value="${wfsettleaccountsprocessEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="hqzwchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">${requiredMap['hqzwchargeno_name']}
                                                                    <a href="#"
                                                                       onclick="addHq('wfsettleaccounts','hqzwcharge');">添加一位</a>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hqzwchargeno" onblur="gethqUserNameByEmpno(this,'hqzwcharge');" name="wfsettleaccounts.hqzwchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hqzwchargeno']}"
                                                               value="${wfsettleaccountsprocessEntity.hqzwchargeno }"/><c:if
                                                            test="${requiredMap['hqzwchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hqzwchargename" name="wfsettleaccounts.hqzwchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hqzwchargeno']}"
                                                                value="${wfsettleaccountsprocessEntity.hqzwchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="hqjgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">${requiredMap['hqjgchargeno_name']}
                                                                    <a href="#" onclick="addHq('wfsettleaccounts','hqjgcharge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hqjgchargeno" onblur="gethqUserNameByEmpno(this,'hqjgcharge');" name="wfsettleaccounts.hqjgchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hqjgchargeno']}"
                                                               value="${wfsettleaccountsprocessEntity.hqjgchargeno }"/><c:if
                                                            test="${requiredMap['hqjgchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hqjgchargename" name="wfsettleaccounts.hqjgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hqjgchargeno']}"
                                                                value="${wfsettleaccountsprocessEntity.hqjgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealnofactoryid').val(),'wfsettleaccounts')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zchargeno" name="wfsettleaccounts.zchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zchargeno']}"
                                                               readonly
                                                               value="${wfsettleaccountsprocessEntity.zchargeno }"/><c:if
                                                            test="${requiredMap['zchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zchargename" name="wfsettleaccounts.zchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zchargeno']}"
                                                                value="${wfsettleaccountsprocessEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hqjg2chargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">${requiredMap['hqjg2chargeno_name']}
                                                                    <a href="#" onclick="addHq('wfsettleaccounts','hqjg2charge');">添加一位</a></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="hqjg2chargeno" onblur="gethqUserNameByEmpno(this,'hqjg2charge');" name="wfsettleaccounts.hqjg2chargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['hqjg2chargeno']}"
                                                               value="${wfsettleaccountsprocessEntity.hqjg2chargeno }"/><c:if
                                                            test="${requiredMap['hqjg2chargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="hqjg2chargename" name="wfsettleaccounts.hqjg2chargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['hqjg2chargeno']}"
                                                                value="${wfsettleaccountsprocessEntity.hqjg2chargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="pcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['pcchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('pcchargeTable',$('#dealdeptno').val(),'pcchargeno','pcchargename',$('#dealnofactoryid').val(),'wfcustomereat')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="pcchargeno" name="wfsettleaccounts.pcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                               readonly
                                                               value="${wfsettleaccountsprocessEntity.pcchargeno }"/><c:if
                                                            test="${requiredMap['pcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="pcchargename" name="wfsettleaccounts.pcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['pcchargeno']}"
                                                                value="${wfsettleaccountsprocessEntity.pcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
</form>
<script src='${ctx}/static/js/safety/wfsettleaccountsprocess.js?random=<%= Math.random()%>'></script>
<div id="optionWin" class="easyui-window" title="餐廳就餐費用結算申請單信息導入" style="width:350px;height:300px;"
     collapsible="false" maximizable="false" minimizable="false" resizable="false" modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post" class="fm" enctype="multipart/form-data">
        <br/>
        <table width="100%">
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="file" type="file" style="width: 300px"
                           accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>
                    <input id="isExcel2003" name="isExcel2003" type="hidden" value="">
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <a href="#" id="btnUploadExcel" class="easyui-linkbutton" onclick="btnUploadExcel();">文檔上傳</a>
                </td>
            </tr>
            <tr align="center">
                <td style="width: 60%; white-space: nowrap;">
                    <span ID="labelListAddResult"></span><a href="${ctx}/wfsettleaccountsprocess/downLoad/errorExcel" id="downloadError"
                                                            plain="true">查看錯誤信息</a>
                </td>
            </tr>
        </table>
    </form>
</div>
<script type="text/javascript">
    applyMemoHeight();
    if ("${wfsettleaccountsprocessEntity.hqjgchargeno}" != "") {
        var nostr = "${wfsettleaccountsprocessEntity.hqjgchargeno}";
        var namestr = "${wfsettleaccountsprocessEntity.hqjgchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hqjgchargeTable tr:eq(" + (i + 2) + ")").find("#hqjgchargeno").val(notr[i]);
            $("#hqjgchargeTable tr:eq(" + (i + 2) + ")").find("#hqjgchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hqjgchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hqjgchargeno' name='wfsettleaccounts.hqjgchargeno' style='width: 80px' value='' onblur='gethqUserNameByEmpno(this,'hqjgcharge');'/>/<input id='hqjgchargename' name='wfsettleaccounts.hqjgchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wfsettleaccountsprocessEntity.hqzwchargeno}" != "") {
        var nostr = "${wfsettleaccountsprocessEntity.hqzwchargeno}";
        var namestr = "${wfsettleaccountsprocessEntity.hqzwchargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hqzwchargeTable tr:eq(" + (i + 2) + ")").find("#hqzwchargeno").val(notr[i]);
            $("#hqzwchargeTable tr:eq(" + (i + 2) + ")").find("#hqzwchargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hqzwchargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hqzwchargeno' name='wfsettleaccounts.hqzwchargeno' style='width: 80px' value='' onblur='gethqUserNameByEmpno(this,'hqzwcharge');'/>/<input id='hqzwchargename' name='wfsettleaccounts.hqzwchargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }
    if ("${wfsettleaccountsprocessEntity.hqjg2chargeno}" != "") {
        var nostr = "${wfsettleaccountsprocessEntity.hqjg2chargeno}";
        var namestr = "${wfsettleaccountsprocessEntity.hqjg2chargename}";
        var notr = nostr.split(",");
        var nametr = namestr.split(",");
        for (var i = 0; i < notr.length; i++) {
            $("#hqjg2chargeTable tr:eq(" + (i + 2) + ")").find("#hqjg2chargeno").val(notr[i]);
            $("#hqjg2chargeTable tr:eq(" + (i + 2) + ")").find("#hqjg2chargename").val(nametr[i]);
            if (i != notr.length - 1) {
                $("#hqjg2chargeTable").append(
                    "<tr align='center'>"
                    + "<td><input id='hqjg2chargeno' name='wfsettleaccounts.hqjg2chargeno' style='width: 80px' value='' onblur='gethqUserNameByEmpno(this,'hqjg2charge');'/>/<input id='hqjg2chargename' name='wfsettleaccounts.hqjg2chargename' style='width: 80px'  value='' readonly/>"
                    + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
                    + "</tr>"
                );
            }
        }
    }

</script>
</body>
</html>
