<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>餐廳就餐費用結算申請表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }

        .overdiv {
            overflow-y: auto;
            overflow-x: auto;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfsettleaccountsprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfsettleaccountsprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfsettleaccountsprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">餐廳就餐費用結算申請表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfsettleaccountsprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfsettleaccountsprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfsettleaccountsprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfsettleaccountsprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfsettleaccountsprocessEntity.makerno}/${wfsettleaccountsprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="5%">提報人工號</td>
                            <td width="5%" class="td_style2">${wfsettleaccountsprocessEntity.dealno}</td>
                            <td width="4%">提報人</td>
                            <td width="6%" class="td_style2">${wfsettleaccountsprocessEntity.dealname}</td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style2">${wfsettleaccountsprocessEntity.dealdeptno}</td>
                            <td width="4%">費用代碼</td>
                            <td width="6%" class="td_style2">${wfsettleaccountsprocessEntity.dealcostno}</td>
                            <td width="4%">所在廠區</td>
                            <td width="6%" class="td_style2">${wfsettleaccountsprocessEntity.dealfactoryname}</td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱</td>
                            <td colspan="3" class="td_style2">${wfsettleaccountsprocessEntity.dealdeptname}</td>
                            <td>聯繫方式</td>
                            <td class="td_style2">${wfsettleaccountsprocessEntity.dealtel}</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfsettleaccountsprocessEntity.dealemail }</td>
                        </tr>
                        <tr align="center">
                            <td>供應包商</td>
                            <td colspan="9" class="td_style2">${wfsettleaccountsprocessEntity.supplier}</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請內容</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <%--<div style="overflow-x: auto;width: 1200px;">--%>
                                <c:if test="${wfsettleaccountsitems!=null && wfsettleaccountsitems.size()>0&& wfsettleaccountsitems.size()>10}">
                                <div style="overflow-x: auto;overflow-y: auto;height: 400px" width="1200px">
                                    </c:if>
                                    <c:if test="${wfsettleaccountsitems!=null && wfsettleaccountsitems.size()>0&& wfsettleaccountsitems.size()<10}">
                                    <div style="overflow-x: auto;overflow-y: auto" width="1200px">
                                        </c:if>
                                        <c:if test="${wfsettleaccountsitems==null}">
                                        <div class="overdiv" width="1200px" id="overflowdiv">
                                            </c:if>
                                            <table id="wfsettleaccountsitemsTable" width="100%">
                                                <tr align="center">
                                                    <td width="30px">項次</td>
                                                    <td width="80px">類別</td>
                                                    <td width="120px">開始日期</td>
                                                    <td width="120px">結束日期</td>
                                                    <td width="90px">早餐</td>
                                                    <td width="90px">中餐</td>
                                                    <td width="90px">晚餐</td>
                                                    <td width="90px">夜宵</td>
                                                    <td width="90px">掛靠費用代碼</td>
                                                    <td width="90px">人數合計</td>
                                                    <td width="90px">金額合計</td>
                                                    <td width="300px">需求說明</td>
                                                </tr>
                                                <tbody id="info_Body0">
                                                <c:if test="${wfsettleaccountsitems!=null&&wfsettleaccountsitems.size()>0}">
                                                    <c:forEach items="${wfsettleaccountsitems}" var="settleaccountsitems" varStatus="status">
                                                        <tr align="center" id="settleaccountsitems${status.index+1}">
                                                            <td>${status.index+1}</td>
                                                            <td>${settleaccountsitems.applytype}</td>
                                                            <td>
                                                                <input id="applystartdate${status.index+1}"
                                                                       name="wfsettleaccountsitems[${status.index+1}].applystartdate"
                                                                       class="easyui-validatebox Wdate"
                                                                       data-options="required:true,prompt:'请选择开始时间'"
                                                                       style="width:100px"
                                                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${settleaccountsitems.applystartdate}"/>"
                                                                       onclick="WdatePicker({onpicked:function(){applyenddate${status.index+1}.click()},doubleCalendar:true,skin:'whyGreen',dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"/>
                                                            </td>
                                                            <td>
                                                                <input id="applyenddate${status.index+1}"
                                                                       name="wfsettleaccountsitems[${status.index+1}].applyenddate"
                                                                       class="easyui-validatebox Wdate"
                                                                       data-options="required:true,prompt:'请选择結束时间'"
                                                                       style="width:100px"
                                                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${settleaccountsitems.applyenddate}"/>"
                                                                       onclick="WdatePicker({doubleCalendar:true,skin:'whyGreen',minDate:'#F{$dp.$D(\'applystartdate${status.index+1}\')}',dateFmt:'yyyy-MM-dd'})"/>
                                                            </td>
                                                            <td>${settleaccountsitems.breakfast}</td>
                                                            <td>${settleaccountsitems.chinesemeal}</td>
                                                            <td>${settleaccountsitems.dinner}</td>
                                                            <td>${settleaccountsitems.midnightsnack}</td>
                                                            <td>${settleaccountsitems.relycostno}</td>
                                                            <td>${settleaccountsitems.peopletotal}</td>
                                                            <td>${settleaccountsitems.moneytotal}</td>
                                                            <td>${settleaccountsitems.applymemo}</td>
                                                        </tr>
                                                    </c:forEach>
                                                </c:if>
                                                </tbody>
                                            </table>
                                        </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${wfsettleaccountsprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','餐廳就餐費用結算申請表');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfsettleaccountsprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr class="no-print">
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <c:if test="${wfsettleaccountsprocessEntity.workstatus!=null&&wfsettleaccountsprocessEntity.workstatus==3}">
                                    <a href="#" id="btnPrint" class="easyui-linkbutton"
                                       data-options="iconCls:'icon-print'"
                                       style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>
                                </c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/safety/wfsettleaccountsprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>