<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>管制區資安管制設備攜入攜出申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfequipmentinoutprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfequipmentinoutprocessEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${wfequipmentinoutprocessEntity.serialno }"/>
    <div class="commonW">
        <div class="headTitle">資安管制設備攜入攜出申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfequipmentinoutprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfequipmentinoutprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfequipmentinoutprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfequipmentinoutprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${wfequipmentinoutprocessEntity.makerno}/${wfequipmentinoutprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td>申請人類別</td>
                            <td colspan="9" class="td_style2" style="text-align: left;">
                                ${wfequipmentinoutprocessEntity.applytypename}
                            </td>
                        </tr>
                        <c:if test="${wfequipmentinoutprocessEntity.applytype=='1'}">
                            <tr>
                                <td colspan="10" class="td_style1">申請人基本信息</td>
                            </tr>
                            <tr align="center">
                                <td width="8%">申請人工號</td>
                                <td width="10%" class="td_style2">${wfequipmentinoutprocessEntity.applyno}</td>
                                <td width="8%">申請人</td>
                                <td width="10%" class="td_style2">${wfequipmentinoutprocessEntity.applyname}</td>
                                <td width="8%">單位代碼</td>
                                <td width="10%" class="td_style2">${wfequipmentinoutprocessEntity.applydeptno}</td>
                                <td width="8%">費用代碼</td>
                                <td width="10%" class="td_style2">${wfequipmentinoutprocessEntity.applycostno}</td>
                                <td width="8%">所在廠區</td>
                                <td width="15%" class="td_style2">${wfequipmentinoutprocessEntity.applyfactoryname}</td>
                            </tr>
                            <tr align="center">
                                <td>部門名稱</td>
                                <td colspan="3" class="td_style2">${wfequipmentinoutprocessEntity.applydeptname}</td>
                                <td>聯繫方式</td>
                                <td class="td_style2">${wfequipmentinoutprocessEntity.applytel}</td>
                                <td>聯繫郵箱</td>
                                <td colspan="3" class="td_style2">${wfequipmentinoutprocessEntity.applyemail}</td>
                            </tr>
                        </c:if>
                        <c:if test="${wfequipmentinoutprocessEntity.applytype=='2'}">
                            <tr>
                                <td colspan="10" class="td_style1">客戶/訪客基本信息</td>
                            </tr>
                            <tr align="center">
                                <td>所屬公司名稱</td>
                                <td colspan="3" class="td_style2">${wfequipmentinoutprocessEntity.companyname }</td>
                                <td colspan="2">所屬公司聯繫方式</td>
                                <td colspan="4" class="td_style2">${wfequipmentinoutprocessEntity.companytel }</td>
                            </tr>
                            <tr align="center">
                                <td>客戶/訪客姓名</td>
                                <td class="td_style2" colspan="3">${wfequipmentinoutprocessEntity.clientname }</td>
                                    <%--<td>接待人</td>
                                    <td class="td_style2">${wfequipmentinoutprocessEntity.clientreceiver}</td>--%>
                                <td colspan="2">證件類型</td>
                                <td class="td_style2">${wfequipmentinoutprocessEntity.clientidtype }</td>
                                <td>證件編號</td>
                                <td colspan="2" class="td_style2">${wfequipmentinoutprocessEntity.clientid }</td>
                            </tr>

                        </c:if>
                        <tr>
                            <td colspan="10" class="td_style1">接待人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">接待人工號</td>
                            <td width="10%" class="td_style2">${wfequipmentinoutprocessEntity.receiverno }</td>
                            <td width="8%">接待人</td>
                            <td width="10%" class="td_style2">${wfequipmentinoutprocessEntity.receivername }</td>
                            <td width="8%">單位代碼</td>
                            <td width="10%" class="td_style2">${wfequipmentinoutprocessEntity.receiverdeptno }</td>
                            <td width="8%">費用代碼</td>
                            <td colspan="3" class="td_style2">${wfequipmentinoutprocessEntity.receivercostno }</td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱</td>
                            <td colspan="3" class="td_style2">${wfequipmentinoutprocessEntity.receiverdeptname }</td>
                            <td>聯繫方式</td>
                            <td class="td_style2">${wfequipmentinoutprocessEntity.receivertel }</td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style2">${wfequipmentinoutprocessEntity.receiveremail}</td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td>申請期限</td>
                            <td colspan="9" class="td_style2">
                                <input id="applystarttime" name="wfequipmentinout.applystarttime" class="Wdate"
                                       data-options="width:150"
                                       style="width:150px" readonly
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfequipmentinoutprocessEntity.applystarttime}"/>"/>
                                &nbsp;&nbsp;&nbsp;&nbsp;—— &nbsp;&nbsp;&nbsp;&nbsp;
                                <input id="applyendtime" name="wfequipmentinout.applyendtime" class="Wdate"
                                       data-options="width:150"
                                       style="width:150px" readonly
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfequipmentinoutprocessEntity.applyendtime}"/>"/>(最長為3天)
                            </td>
                        </tr>
                        <tr align="center">
                            <td>設備類型</td>
                            <td colspan="9" class="td_style2">
                                ${wfequipmentinoutprocessEntity.equtypename }
                                <c:if test="${wfequipmentinoutprocessEntity.equother!=''||wfequipmentinoutprocessEntity.equother!=null}">${wfequipmentinoutprocessEntity.equother}</c:if>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="wfmachineinoutTable" width="100%">
                                        <tr align="center">
                                            <td width="30px">項次</td>
                                            <td width="100px">資產所屬</td>
                                            <td width="100px">品牌/型號</td>
                                            <td width="100px">顏色</td>
                                            <td width="100px">有無無線網卡<br>(CDMA/GPRS)</td>
                                            <td width="120px">電腦編號/資安管制編號</td>
                                            <td width="120px">端口/功能是否保留</td>
                                            <td width="120px">保留端口選擇</td>
                                            <td width="200px">保留原因</td>
                                            <td width="100px">保管人工號</td>
                                            <td width="100px">保管人姓名</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wfequipmentinoutitems!=null&&wfequipmentinoutitems.size()>0}">
                                            <c:forEach items="${wfequipmentinoutitems}" var="equipmentinoutitems"
                                                       varStatus="status">
                                                <tr align="center" id="equipmentinoutitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>${equipmentinoutitems.zctypename}</td>
                                                    <td>${equipmentinoutitems.brand}</td>
                                                    <td>${equipmentinoutitems.color}</td>
                                                    <td>${equipmentinoutitems.ynwlan}</td>
                                                    <td>${equipmentinoutitems.serialnumber}</td>
                                                    <td>${equipmentinoutitems.ynreserve}</td>
                                                    <td>
                                                        <c:if test="${equipmentinoutitems.reserveport!=null}">
                                                            <input id="reserveport${status.index+1}"
                                                                   name="wfequipmentinoutitems[${status.index+1}].reserveport"
                                                                   disabled class="easyui-combobox"
                                                                   data-options="width:150,prompt:'選擇保留端口,可多選',validType:'comboxValidate[\'reserveport${status.index+1}\',\'请選擇保留端口\']'"
                                                                   value="${equipmentinoutitems.reserveport}"/>
                                                        </c:if>
                                                    </td>
                                                    <td>${equipmentinoutitems.reservecause}</td>
                                                    <td>${equipmentinoutitems.trusteeno}</td>
                                                    <td>${equipmentinoutitems.trusteename}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">設備流通路徑</td>
                            <td width="92%" colspan="9" class="td_style2">
                                ${wfequipmentinoutprocessEntity.equout3}區
                                ${wfequipmentinoutprocessEntity.equout}棟
                                ${wfequipmentinoutprocessEntity.equout2}層
                                    <c:if test="${wfequipmentinoutprocessEntity.equouttype=='0'}"><font
                                            style="color: blue">特保區</font></c:if>
                                <c:if test="${wfequipmentinoutprocessEntity.equouttype=='1'}">非特保區</c:if>
                                    &nbsp;&nbsp;&nbsp;&nbsp;——> &nbsp;&nbsp;&nbsp;&nbsp;
                                    ${wfequipmentinoutprocessEntity.equin3}區
                                ${wfequipmentinoutprocessEntity.equin}棟
                                ${wfequipmentinoutprocessEntity.equin2}層
                                    <c:if test="${wfequipmentinoutprocessEntity.equintype=='0'}"><font
                                            style="color: blue">特保區</font></c:if>
                                <c:if test="${wfequipmentinoutprocessEntity.equintype=='1'}">非特保區</c:if>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="8%">需求說明</td>
                            <td width="92%" colspan="9" class="td_style2">
                                    <textarea id="applyreason" name="wfequipmentinout.applyreason"
                                              onpropertychange="return LessThanAuto(this,'txtNum');"
                                              class="easyui-validatebox" style="width:99%;height:60px;" readonly
                                              rows="5" cols="3">${wfequipmentinoutprocessEntity.applyreason}</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            </td>
                            <td colspan="9" class="td_style2">
                                1.設備攜入時，資安人員需點檢物理隔離項：USB、DVD、SD卡槽、wifi、藍牙、攝像頭等是否已貼封或拆除；<br>
                                2.設備攜出時，資安人員需進行清查資料、格式化硬盤、端口貼封、警衛押運等進行說明及管控<br>
                                3.以上內容資安人員簽核時必須進行備註說明。
                            </td>
                        </tr>
                        <tr align="center">
                            <td>批註</td>
                            <td colspan="9" class="td_style2">
						    <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                      style="width:1000px;height:60px;"
                                      rows="4" cols="4"></textarea>
                                <%--<input id="attachidsremark" name="attachidsremark" class="easyui-validatebox"  data-options="width:1000px,height:80px,multiline:true"  />--%>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <%--<tr align="center">
                            <td width="10%">附件</td>
                            <td width="90%" class="td_style1">
                                <input type="hidden" id="attachids"
                                       name="attachids" value="${wfequipmentinoutprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/newEsign/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>--%>

                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wfequipmentinoutprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','管制區資安管制設備攜入攜出申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfequipmentinoutprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
</div>
<input id="disOrEnabled" type="hidden" value="disabled"/>
<!--<input id="disOrEnabled" type="hidden" value=""/>-->
<div id="dlg"></div>
<script src='${ctx}/static/js/safety/wfequipmentinoutprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>