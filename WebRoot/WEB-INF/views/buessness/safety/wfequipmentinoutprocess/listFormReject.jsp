<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>管制區資安管制設備攜入攜出申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfequipmentinoutprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfequipmentinoutprocessEntity.id }"/>
    <input id="serialno" name="wfequipmentinout.serialno" type="hidden" value="${wfequipmentinoutprocessEntity.serialno }"/>
    <input id="makerno" name="wfequipmentinout.makerno" type="hidden" value="${wfequipmentinoutprocessEntity.makerno }"/>
    <input id="makername" name="wfequipmentinout.makername" type="hidden" value="${wfequipmentinoutprocessEntity.makername }"/>
    <input id="makerdeptno" name="wfequipmentinout.makerdeptno" type="hidden" value="${wfequipmentinoutprocessEntity.makerdeptno }"/>
    <input id="makerfactoryid" name="wfequipmentinout.makerfactoryid" type="hidden" value="${wfequipmentinoutprocessEntity.makerfactoryid }"/>
    <div class="commonW">
        <div class="headTitle">資安管制設備攜入攜出申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfequipmentinoutprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfequipmentinoutprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfequipmentinoutprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfequipmentinoutprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfequipmentinoutprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfequipmentinoutprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfequipmentinoutprocessEntity.makerno}/${wfequipmentinoutprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td>申請人類別</td>
                            <td colspan="9" class="td_style2" style="text-align: left;">
                                <div class="applytypeDiv"></div>
                                <input id="applytype" name="wfequipmentinout.applytype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfequipmentinoutprocessEntity.applytype}"/>
                                <input id="applytypename" name="wfequipmentinout.applytypename" type="hidden"
                                       value="${wfequipmentinoutprocessEntity.applytypename }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請人基本信息</td>
                        </tr>
                        <tr align="center" class="apply">
                            <td width="8%">申請人工號 <span class="applyRedDiv"><font color="red">*</font></span></td>
                            <td width="10%" class="td_style1">
                                <input id="applyno" name="wfequipmentinout.applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfequipmentinoutprocessEntity.applyno }"
                                       onblur="queryUserInfo('apply');"/>
                            </td>
                            <td width="8%">申請人</td>
                            <td width="10%" class="td_style1">
                                <input id="applyname" name="wfequipmentinout.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wfequipmentinoutprocessEntity.applyname }"/>
                            </td>
                            <td width="8%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="applydeptno" name="wfequipmentinout.applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfequipmentinoutprocessEntity.applydeptno }"/>
                            </td>
                            <td width="8%">費用代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="applycostno" name="wfequipmentinout.applycostno"
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfequipmentinoutprocessEntity.applycostno }"/>
                            </td>
                            <td width="8%">所在廠區&nbsp;<span class="applyRedDiv"><font color="red">*</font></span>
                            </td>
                            <td width="15%" class="td_style1">
                                <input id="applyfactoryid" name="wfequipmentinout.applyfactoryid"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${wfequipmentinoutprocessEntity.applyfactoryid }"
                                       data-options="width: 120,validType:'comboxValidate[\'applyfactoryid\',\'请選擇所在廠區\']',onSelect:function(){onchangeFactory('apply');}"/>
                                <input id="applyfactoryname" name="wfequipmentinout.applyfactoryname" type="hidden" value="${wfequipmentinoutprocessEntity.applyfactoryname }"/>
                                <input id="applynofactoryid" name="wfequipmentinout.applynofactoryid" type="hidden" value="${wfequipmentinoutprocessEntity.applynofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱&nbsp;<span class="applyRedDiv"><font color="red">*</font></span></td>
                            <td colspan="3" class="td_style1">
                                <input id="applydeptname" name="wfequipmentinout.applydeptname"
                                       class="easyui-validatebox" data-options="width: 380,required:true"
                                       value="${wfequipmentinoutprocessEntity.applydeptname }"/>
                            </td>
                            <td>聯繫方式&nbsp;<span class="applyRedDiv"><font color="red">*</font></span></td>
                            <td class="td_style1">
                                <input id="applytel" name="wfequipmentinout.applytel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfequipmentinoutprocessEntity.applytel }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<span class="applyRedDiv"><font color="red">*</font></span></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="wfequipmentinout.applyemail" class="easyui-validatebox"
                                       value="${wfequipmentinoutprocessEntity.applyemail }" style="width:300px;"
                                       data-options="required:true,validType:'email[\'applyemail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">客戶/訪客基本信息</td>
                        </tr>
                        <tr align="center" class="client">
                            <td>所屬公司名稱 <span class="clientRedDiv"><font color="red">*</font></span></td>
                            <td colspan="3" class="td_style1">
                                <input id="companyname" name="wfequipmentinout.companyname"
                                       class="easyui-validatebox" data-options="width: 380"
                                       value="${wfequipmentinoutprocessEntity.companyname }"/>
                            </td>
                            <td colspan="2">所屬公司聯繫方式 <span class="clientRedDiv"><font
                                    color="red">*</font></span></td>
                            <td colspan="4" class="td_style1">
                                <input id="companytel" name="wfequipmentinout.companytel" class="easyui-validatebox"
                                       style="width:200px;"
                                       value="${wfequipmentinoutprocessEntity.companytel }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>客戶/訪客姓名 <span class="clientRedDiv"><font color="red">*</font></span></td>
                            <td class="td_style1" colspan="3">
                                <input id="clientname" name="wfequipmentinout.clientname" class="easyui-validatebox"
                                       style="width:200px;"
                                       value="${wfequipmentinoutprocessEntity.clientname }"/>
                            </td>
                            <%--<td>接待人</td>
                            <td class="td_style1">
                                <input id="clientreceiver" name="wfequipmentinout.clientreceiver"
                                       class="easyui-validatebox"
                                       style="width:100px;"
                                       value="${wfequipmentinoutprocessEntity.clientreceiver }"/>
                            </td>--%>
                            <td colspan="2">證件類型 <span class="clientRedDiv"><font color="red">*</font></span></td>
                            <td class="td_style1">
                                <input id="clientidtype" name="wfequipmentinout.clientidtype" class="easyui-validatebox"
                                       style="width:100px;"
                                       value="${wfequipmentinoutprocessEntity.clientidtype }"/>
                            </td>
                            <td>證件編號 <span class="clientRedDiv"><font color="red">*</font></span></td>
                            <td colspan="2" class="td_style1">
                                <input id="clientid" name="wfequipmentinout.clientid" class="easyui-validatebox"
                                       style="width:200px;"
                                       value="${wfequipmentinoutprocessEntity.clientid }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">接待人基本信息</td>
                        </tr>
                        <tr align="center">
                            <td width="8%">接待人工號&nbsp;<font color="red">*</font></td>
                            <td width="10%" class="td_style1">
                                <input id="receiverno" name="wfequipmentinout.receiverno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfequipmentinoutprocessEntity.receiverno }"
                                       onblur="queryUserInfo('receiver');"/>
                            </td>
                            <td width="8%">接待人</td>
                            <td width="10%" class="td_style1">
                                <input id="receivername" name="wfequipmentinout.receivername"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly
                                       value="${wfequipmentinoutprocessEntity.receivername }"/>
                            </td>
                            <td width="8%">單位代碼</td>
                            <td width="10%" class="td_style1">
                                <input id="receiverdeptno" name="wfequipmentinout.receiverdeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${wfequipmentinoutprocessEntity.receiverdeptno }"/>
                            </td>
                            <td width="8%">費用代碼</td>
                            <td colspan="1" class="td_style1">
                                <input id="receivercostno" name="wfequipmentinout.receivercostno"
                                       class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${wfequipmentinoutprocessEntity.receivercostno }"/>
                            </td>
                            <td width="8%">所在廠區<font color="red">*</font></td>
                            <td width="15%" class="td_style1">
                                <input id="receiverfactoryid" name="wfequipmentinout.receiverfactoryid"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${wfequipmentinoutprocessEntity.receiverfactoryid }"
                                       data-options="width: 120,validType:'comboxValidate[\'receiverfactoryid\',\'请選擇所在廠區\']',onSelect:function(){onchangeFactory('receiver');}"/>
                                <input id="receiverfactoryname" name="wfequipmentinout.receiverfactoryname"
                                       type="hidden" value="${wfequipmentinoutprocessEntity.receiverfactoryname }"/>
                                <input id="receivernofactoryid" name="wfequipmentinout.receivernofactoryid"
                                       type="hidden" value="${wfequipmentinoutprocessEntity.receivernofactoryid }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>部門名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="receiverdeptname" name="wfequipmentinout.receiverdeptname"
                                       class="easyui-validatebox" data-options="width: 380,required:true"
                                       value="${wfequipmentinoutprocessEntity.receiverdeptname }"/>
                            </td>
                            <td>聯繫方式&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="receivertel" name="wfequipmentinout.receivertel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${wfequipmentinoutprocessEntity.receivertel }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="receiveremail" name="wfequipmentinout.receiveremail"
                                       class="easyui-validatebox"
                                       value="${wfequipmentinoutprocessEntity.receiveremail}" style="width:300px;"
                                       data-options="required:true,validType:'email[\'receiveremail\',\'郵箱的格式不正確\']'"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td>申請期限&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style2">
                                <input id="applystarttime" name="wfequipmentinout.applystarttime" class="Wdate"
                                       data-options="width:150,required:true"
                                       style="width:150px"
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfequipmentinoutprocessEntity.applystarttime}"/>"
                                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-%d'})"/>
                                &nbsp;&nbsp;&nbsp;&nbsp;—— &nbsp;&nbsp;&nbsp;&nbsp;
                                <input id="applyendtime" name="wfequipmentinout.applyendtime" class="Wdate"
                                       data-options="width:150,required:true"
                                       style="width:150px"
                                       value="<fmt:formatDate  pattern="yyyy-MM-dd" value="${wfequipmentinoutprocessEntity.applyendtime}"/>"
                                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'applystarttime\')}',maxDate:'#F{$dp.$D(\'applystarttime\',{d:2});}'})"/>(最長為3天)
                            </td>
                        </tr>
                        <tr align="center">
                            <td>設備類型&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style2">
                                <div class="equtypeDiv" style="float: left;"></div>
                                <input id="equtype" name="wfequipmentinout.equtype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfequipmentinoutprocessEntity.equtype }"/>
                                <input id="equtypename" name="wfequipmentinout.equtypename" type="hidden"
                                       value="${wfequipmentinoutprocessEntity.equtypename }"/>
                                <div style="float: left;">
                                    <input id="equother" name="wfequipmentinout.equother" class="easyui-validatebox"
                                           data-options="width: 250" readonly="readonly"
                                           value="${wfequipmentinoutprocessEntity.equother}"/>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="wfmachineinoutTable" width="130%">
                                        <tr align="center">
                                            <td width="30px">項次</td>
                                            <td width="100px">資產所屬&nbsp;<font color="red">*</font></td>
                                            <td width="100px">品牌/型號&nbsp;<font color="red">*</font></td>
                                            <td width="100px">顏色&nbsp;<font color="red">*</font></td>
                                            <td width="100px">有無無線網卡<br>(CDMA/GPRS)<font color="red">*</font></td>
                                            <td width="120px">電腦編號/資安管制編號&nbsp;<font color="red">*</font></td>
                                            <td width="120px">端口/功能是否保留&nbsp;<font color="red">*</font></td>
                                            <td width="120px">保留端口選擇</td>
                                            <td width="200px">保留原因</td>
                                            <td width="100px">保管人工號&nbsp;<font color="red">*</font></td>
                                            <td width="100px">保管人姓名&nbsp;<font color="red">*</font></td>
                                            <td width="50px">操作</td>
                                        </tr>
                                        <tbody id="info_Body0">
                                        <c:if test="${wfequipmentinoutitems!=null&&wfequipmentinoutitems.size()>0}">
                                            <c:forEach items="${wfequipmentinoutitems}" var="equipmentinoutitems"
                                                       varStatus="status">
                                                <tr align="center" id="equipmentinoutitems${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <input id="zctype${status.index+1}"
                                                               name="wfequipmentinoutitems[${status.index+1}].zctype"
                                                               data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadZctype(${status.index+1});},onSelect:function(){onchangeZctype(${status.index+1});}"
                                                               style="width:100px;" class="easyui-combobox" value="${equipmentinoutitems.zctype}"/>
                                                        <input id="zctypename${status.index+1}" name="wfequipmentinoutitems[${status.index+1}].zctypename"
                                                               type="hidden" value="${equipmentinoutitems.zctypename}"/>
                                                    </td>
                                                    <td>
                                                        <input id="brand${status.index+1}"
                                                               name="wfequipmentinoutitems[${status.index+1}].brand"
                                                               class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true"
                                                               value="${equipmentinoutitems.brand}"/>
                                                    </td>
                                                    <td>
                                                        <input id="color${status.index+1}"
                                                               name="wfequipmentinoutitems[${status.index+1}].color"
                                                               class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true"
                                                               value="${equipmentinoutitems.color}"/>
                                                    </td>
                                                    <td>
                                                        <div class="ynwlan${status.index+1}Div"></div>
                                                        <input id="ynwlan${status.index+1}Value"
                                                               name="ynwlan${status.index+1}Value"
                                                               type="hidden" class="easyui-validatebox"
                                                               data-options="width: 100"
                                                               value="${equipmentinoutitems.ynwlan}"/>
                                                    </td>
                                                    <td>
                                                        <input id="serialnumber${status.index+1}"
                                                               name="wfequipmentinoutitems[${status.index+1}].serialnumber"
                                                               class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true"
                                                               value="${equipmentinoutitems.serialnumber}"/>
                                                    </td>
                                                    <td>
                                                        <div class="ynreserve${status.index+1}Div"></div>
                                                        <input id="ynreserve${status.index+1}Value"
                                                               name="ynreserve${status.index+1}Value"
                                                               type="hidden" class="easyui-validatebox"
                                                               data-options="width: 100"
                                                               value="${equipmentinoutitems.ynreserve}"/>
                                                    </td>
                                                    <td>
                                                        <input id="reserveport${status.index+1}"
                                                               name="wfequipmentinoutitems[${status.index+1}].reserveport"
                                                               class="easyui-combobox"
                                                               data-options="width:150,prompt:'選擇保留端口,可多選',validType:'comboxValidate[\'reserveport${status.index+1}\',\'请選擇保留端口\']'"
                                                               value="${equipmentinoutitems.reserveport}"/>
                                                    </td>
                                                    <td>
                                                        <input id="reservecause${status.index+1}"
                                                               name="wfequipmentinoutitems[${status.index+1}].reservecause"
                                                               class="easyui-validatebox" style="width:200px;"
                                                               value="${equipmentinoutitems.reservecause}"/>
                                                    </td>
                                                    <td>
                                                        <input id="trusteeno${status.index+1}"
                                                               name="wfequipmentinoutitems[${status.index+1}].trusteeno"
                                                               class="easyui-validatebox" style="width:100px;"
                                                               data-options="required:true"
                                                               onblur="queryUserInfo1(this,'${status.index+1}');"
                                                               value="${equipmentinoutitems.trusteeno}"/>
                                                    </td>
                                                    <td>
                                                        <input id="trusteename${status.index+1}"
                                                               name="wfequipmentinoutitems[${status.index+1}].trusteename"
                                                               class="easyui-validatebox inputCss" style="width:100px;" readonly
                                                               value="${equipmentinoutitems.trusteename}"/>
                                                    </td>
                                                    <td>
                                                        <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                               onclick="equipmentdeltr(${status.index+1});return false;"/>
                                                        <input id="shunxu${status.index+1}" type="hidden"
                                                               name="wfequipmentinoutitems[${status.index+1}].shunxu"
                                                               value="${status.index+1}"/>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${wfequipmentinoutitems.size()==0 || wfequipmentinoutitems==null}">
                                            <tr align="center" id="equipmentinoutitems1">
                                                <td>1</td>
                                                <td>
                                                    <input id="zctype1" name="wfequipmentinoutitems[1].zctype"
                                                           data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadZctype(1);},onSelect:function(){onchangeZctype(1);}"
                                                           style="width:100px;"
                                                           class="easyui-combobox" editable="false" value=""/>
                                                    <input id="zctypename1" name="wfequipmentinoutitems[1].zctypename"
                                                           type="hidden" value=""/>
                                                </td>
                                                <td>
                                                    <input id="brand1"
                                                           name="wfequipmentinoutitems[1].brand"
                                                           class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="color1"
                                                           name="wfequipmentinoutitems[1].color"
                                                           class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <div class="ynwlan1Div"></div>
                                                    <input id="ynwlan1Value" name="ynwlan1Value"
                                                           type="hidden" class="easyui-validatebox"
                                                           data-options="width: 100"
                                                           value=""/>
                                                </td>
                                                <td>
                                                    <input id="serialnumber1"
                                                           name="wfequipmentinoutitems[1].serialnumber"
                                                           class="easyui-validatebox" style="width:100px;"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <div class="ynreserve1Div"></div>
                                                    <input id="ynreserve1Value" name="ynreserve1Value"
                                                           type="hidden" class="easyui-validatebox"
                                                           data-options="width: 100"
                                                           value=""/>
                                                </td>
                                                <td>
                                                    <input id="reserveport1" name="wfequipmentinoutitems[1].reserveport"
                                                           class="easyui-combobox"
                                                           data-options="width:150,prompt:'選擇保留端口,可多選',validType:'comboxValidate[\'reserveport1\',\'请選擇保留端口\']'"
                                                           value=""/>
                                                </td>
                                                <td>
                                                    <input id="reservecause1"
                                                           name="wfequipmentinoutitems[1].reservecause"
                                                           class="easyui-validatebox" style="width:200px;"
                                                           value=""/>
                                                </td>
                                                <td>
                                                    <input id="trusteeno1"
                                                           name="wfequipmentinoutitems[1].trusteeno"
                                                           class="easyui-validatebox" style="width:100px;"
                                                           onblur="queryUserInfo1(this,'1');"
                                                           data-options="required:true" value=""/>
                                                </td>
                                                <td>
                                                    <input id="trusteename1"
                                                           name="wfequipmentinoutitems[1].trusteename"
                                                           class="easyui-validatebox inputCss" style="width:100px;"
                                                           readonly value=""/>
                                                </td>
                                                <td>
                                                    <input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="equipmentdeltr(1);return false;"/>
                                                    <input id="shunxu1" type="hidden"
                                                           name="wfequipmentinoutitems[1].shunxu" value="1"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                        <tr align="left" class="nottr0">
                                            <td colspan="12" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="equipmentItemAdd"
                                                       style="width:150px;float:left;"
                                                       value="添加一行"/>
                                            </td>
                                        </tr>

                                    </table>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>設備流通路徑&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style2">
                                <input id="equout3" name="wfequipmentinout.equout3" class="easyui-validatebox"
                                       data-options="width: 100,required:true"
                                       value="${wfequipmentinoutprocessEntity.equout3}"/>&nbsp;區&nbsp;
                                <input id="equout" name="wfequipmentinout.equout" class="easyui-validatebox"
                                       data-options="width: 100,required:true"
                                       value="${wfequipmentinoutprocessEntity.equout}"/>棟
                                <input id="equout2" name="wfequipmentinout.equout2" class="easyui-validatebox"
                                       data-options="width: 100,required:true"
                                       value="${wfequipmentinoutprocessEntity.equout2}"/>層
                                <input id="equouttype" name="wfequipmentinout.equouttype" class="easyui-combobox"
                                       panelHeight="auto" value="${wfequipmentinoutprocessEntity.equouttype }"
                                       data-options="width: 120,validType:'comboxValidate[\'equouttype\',\'请選擇\']'"/>
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;——> &nbsp;&nbsp;&nbsp;&nbsp;
                                <input id="equin3" name="wfequipmentinout.equin3" class="easyui-validatebox"
                                       data-options="width: 100,required:true"
                                       value="${wfequipmentinoutprocessEntity.equin3}"/>&nbsp;區
                                <input id="equin" name="wfequipmentinout.equin" class="easyui-validatebox"
                                       data-options="width: 100,required:true"
                                       value="${wfequipmentinoutprocessEntity.equin}"/>棟
                                <input id="equin2" name="wfequipmentinout.equin2" class="easyui-validatebox"
                                       data-options="width: 100,required:true"
                                       value="${wfequipmentinoutprocessEntity.equin2}"/>層
                                <input id="equintype" name="wfequipmentinout.equintype" class="easyui-combobox"
                                       panelHeight="auto" value="${wfequipmentinoutprocessEntity.equintype }"
                                       data-options="width: 120,validType:'comboxValidate[\'equintype\',\'请選擇\']'"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>需求說明&nbsp;<font color="red">*</font></td>
                            <td colspan="9" class="td_style2">
                                    <textarea id="applyreason" name="wfequipmentinout.applyreason"
                                              oninput="return LessThanAuto(this,'txtNum');"
                                              onchange="return LessThanAuto(this,'txtNum');"
                                              onpropertychange="return LessThanAuto(this,'txtNum');"
                                              maxlength="100" class="easyui-validatebox" style="width:99%;height:60px;"
                                              data-options="required:true"
                                              rows="5"
                                              cols="3">${wfequipmentinoutprocessEntity.applyreason}</textarea><span
                                    id="txtNum"></span>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>備註</td>
                            </td>
                            <td colspan="9" class="td_style2">
                                1.設備攜入時，資安人員需點檢物理隔離項：USB、DVD、SD卡槽、wifi、藍牙、攝像頭等是否已貼封或拆除；<br>
                                2.設備攜出時，資安人員需進行清查資料、格式化硬盤、端口貼封、警衛押運等進行說明及管控<br>
                                3.以上內容資安人員簽核時必須進行備註說明。
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <%--<tr align="center">
                            <td width="10%">附件<font color="red">*</font></td>
                            <td width="90%" class="td_style1">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
                                    <input id="attachidsUpload" name="attachidsUpload" type="file" onchange="uploadFile();" class="ui-input-file"/>
								</span>
                                <c:choose>
                                    <c:when test="${file.size()>0}">
                                        <input type="hidden" id="attachids" name="attachids"
                                               value="${wfequipmentinoutprocessEntity.attachids }"/>
                                    </c:when>
                                    <c:otherwise>
                                        <input type="hidden" id="attachids" name="attachids" value=""/>
                                    </c:otherwise>
                                </c:choose>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/newEsign/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="delAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>--%>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','管制區資安管制設備攜入攜出申請單');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td>
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zachargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zachargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(23,'zachargeTable','zachargeno','zachargename',$('#receiverfactoryid').combobox('getValue'),'wfequipmentinout')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zachargeno" name="wfequipmentinout.zachargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zachargeno']}"
                                                               readonly
                                                               value="${wfequipmentinoutprocessEntity.zachargeno }"/><c:if
                                                            test="${requiredMap['zachargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zachargename" name="wfequipmentinout.zachargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zachargeno']}"
                                                                value="${wfequipmentinoutprocessEntity.zachargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="acchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['acchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('acchargeTable',$('#receiverdeptno').val(),'acchargeno','acchargename',$('#receivernofactoryid').val(),'wfequipmentinout')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="acchargeno" name="wfequipmentinout.acchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['acchargeno']}"
                                                               readonly
                                                               value="${wfequipmentinoutprocessEntity.acchargeno }"/><c:if
                                                            test="${requiredMap['acchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="acchargename" name="wfequipmentinout.acchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['acchargeno']}"
                                                                value="${wfequipmentinoutprocessEntity.acchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zxywkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zxywkchargeno_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(22,'zxywkchargeTable','zxywkchargeno','zxywkchargename',$('#receiverfactoryid').combobox('getValue'),'wfequipmentinout')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxywkchargeno" name="wfequipmentinout.zxywkchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxywkchargeno']}"
                                                               readonly
                                                               value="${wfequipmentinoutprocessEntity.zxywkchargeno }"/><c:if
                                                            test="${requiredMap['zxywkchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxywkchargename"
                                                                name="wfequipmentinout.zxywkchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxywkchargeno']}"
                                                                value="${wfequipmentinoutprocessEntity.zxywkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="aqzgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['aqzgchargeno_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(662,'aqzgchargeTable','aqzgchargeno','aqzgchargename',$('#receiverfactoryid').combobox('getValue'),'wfequipmentinout')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="aqzgchargeno" name="wfequipmentinout.aqzgchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['aqzgchargeno']}"
                                                               readonly
                                                               value="${wfequipmentinoutprocessEntity.aqzgchargeno }"/><c:if
                                                            test="${requiredMap['aqzgchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="aqzgchargename"
                                                                name="wfequipmentinout.aqzgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['aqzgchargeno']}"
                                                                value="${wfequipmentinoutprocessEntity.aqzgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="qxzgchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['qxzgchargeno_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(663,'qxzgchargeTable','qxzgchargeno','qxzgchargename',$('#receiverfactoryid').combobox('getValue'),'wfequipmentinout')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="qxzgchargeno" name="wfequipmentinout.qxzgchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['qxzgchargeno']}"
                                                               readonly
                                                               value="${wfequipmentinoutprocessEntity.qxzgchargeno }"/><c:if
                                                            test="${requiredMap['qxzgchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="qxzgchargename"
                                                                name="wfequipmentinout.qxzgchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['qxzgchargeno']}"
                                                                value="${wfequipmentinoutprocessEntity.qxzgchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zxywcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zxywcchargeno_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(25,'zxywcchargeTable','zxywcchargeno','zxywcchargename',$('#receiverfactoryid').combobox('getValue'),'wfequipmentinout')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zxywcchargeno" name="wfequipmentinout.zxywcchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zxywcchargeno']}"
                                                               readonly
                                                               value="${wfequipmentinoutprocessEntity.zxywcchargeno }"/><c:if
                                                            test="${requiredMap['zxywcchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zxywcchargename"
                                                                name="wfequipmentinout.zxywcchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zxywcchargeno']}"
                                                                value="${wfequipmentinoutprocessEntity.zxywcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="zabchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['zabchargeno_name']}</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(154,'zabchargeTable','zabchargeno','zabchargename',$('#receiverfactoryid').combobox('getValue'),'wfequipmentinout')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="zabchargeno" name="wfequipmentinout.zabchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['zabchargeno']}"
                                                               readonly
                                                               value="${wfequipmentinoutprocessEntity.zabchargeno }"/><c:if
                                                            test="${requiredMap['zabchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="zabchargename" name="wfequipmentinout.zabchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['zabchargeno']}"
                                                                value="${wfequipmentinoutprocessEntity.zabchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="xcjwchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['xcjwchargeno_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(664,'xcjwchargeTable','xcjwchargeno','xcjwchargename',$('#receiverfactoryid').combobox('getValue'),'wfequipmentinout')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="xcjwchargeno" name="wfequipmentinout.xcjwchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['xcjwchargeno']}"
                                                               readonly
                                                               value="${wfequipmentinoutprocessEntity.xcjwchargeno }"/><c:if
                                                            test="${requiredMap['xcjwchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="xcjwchargename"
                                                                name="wfequipmentinout.xcjwchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['xcjwchargeno']}"
                                                                value="${wfequipmentinoutprocessEntity.xcjwchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="xcjwqrchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">${requiredMap['xcjwqrchargeno_name']}
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(665,'xcjwqrchargeTable','xcjwqrchargeno','xcjwqrchargename',$('#receiverfactoryid').combobox('getValue'),'wfequipmentinout')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="xcjwqrchargeno"
                                                               name="wfequipmentinout.xcjwqrchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['xcjwqrchargeno']}"
                                                               readonly
                                                               value="${wfequipmentinoutprocessEntity.xcjwqrchargeno }"/><c:if
                                                            test="${requiredMap['xcjwqrchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="xcjwqrchargename"
                                                                name="wfequipmentinout.xcjwqrchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['xcjwqrchargeno']}"
                                                                value="${wfequipmentinoutprocessEntity.xcjwqrchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfequipmentinoutprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;"
                                   onclick="canelTask('${wfequipmentinoutprocessEntity.serialno }');">取消申請</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input id="disOrEnabled" type="hidden" value=""/>
    <input type="hidden" id="onlyKchargeSignle" value="1" />
    <!--<input id="disOrEnabled" type="hidden" value="disabled"/>-->
    <div id="win"></div>
    <div id="dlg"></div>
</form>
</div>
<script src='${ctx}/static/js/safety/wfequipmentinoutprocess.js?random=<%= Math.random()%>'></script>
</body>
</html>