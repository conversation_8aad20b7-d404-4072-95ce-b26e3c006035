<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>物品放行單業務表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src='${ctx}/static/js/safety/wfstuffpermitprocess.js?random=<%= Math.random()%>'></script>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
            color: black;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/wfstuffpermitprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfstuffpermitprocessEntity.id }"/>
    <input id="serialno" name="stuffpermit.serialno" type="hidden" value="${wfstuffpermitprocessEntity.serialno }"/>
    <input id="makerno" name="stuffpermit.makerno" type="hidden" value="${wfstuffpermitprocessEntity.makerno }"/>
    <input id="makername" name="stuffpermit.makername" type="hidden" value="${wfstuffpermitprocessEntity.makername }"/>
    <input id="makerdeptno" name="stuffpermit.makerdeptno" type="hidden" value="${wfstuffpermitprocessEntity.makerdeptno }"/>
    <div class="commonW">
        <div class="headTitle">物品放行單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfstuffpermitprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfstuffpermitprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfstuffpermitprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfstuffpermitprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <c:if test="${empty wfstuffpermitprocessEntity.makerno}">
            <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        </c:if>
        <c:if test="${not empty wfstuffpermitprocessEntity.makerno}">
            <div class="position_R margin_R">
                填單人：${wfstuffpermitprocessEntity.makerno}/${wfstuffpermitprocessEntity.makername}</div>
        </c:if>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td>使用廠區&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="usefactoryid" name="stuffpermit.usefactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfstuffpermitprocessEntity.usefactoryid }"
                                       data-options="width: 120,required:true,validType:'comboxValidate[\'usefactoryid\',\'请选择所在廠區\']',onSelect:function(){onchangeFactory();LKchoose();clearCheckInfo();}"/>
                                <input type="hidden" id="applyfactoryid" name="stuffpermit.applyfactoryid" value="${wfstuffpermitprocessEntity.applyfactoryid}"></input>
                            </td>
                            <td>申請類別</td>
                            <td colspan="5" class="td_style1">
                                <input id="applytype" name="stuffpermit.applytype" class="easyui-combobox" data-options="width: 200,onSelect:function(){typeCheck();}"
                                       value="${wfstuffpermitprocessEntity.applytype}" panelHeight="auto" editable="false"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">申請人信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">申請人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="stuffpermit.applyno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfstuffpermitprocessEntity.applyno}" onblur="queryUserInfo('apply');"/>
                            </td>
                            <td width="6%">申請人姓名</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="stuffpermit.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfstuffpermitprocessEntity.applyname }"/>
                            </td>
                            <td width="6%">手機號碼&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applymobile" name="stuffpermit.applymobile"
                                       class="easyui-validatebox" data-options="required:true,width: 100,validType:'phone[\'applymobile\',\'手機號格式不正確\']'"
                                       value="${wfstuffpermitprocessEntity.applymobile }"/>
                            </td>
                            <td width="6%">分機號碼&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyphone" name="stuffpermit.applyphone" class="easyui-validatebox"
                                       style="width:80px;"
                                       value="${wfstuffpermitprocessEntity.applyphone}"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="6%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="stuffpermit.applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       value="${wfstuffpermitprocessEntity.applydeptno }" readonly/>
                            </td>
                            <td>單位名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="stuffpermit.applydeptname" class="easyui-validatebox"
                                       data-options="width: 350"
                                       value="${wfstuffpermitprocessEntity.applydeptname }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">接收單位信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">接收人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="receno" name="stuffpermit.receno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${wfstuffpermitprocessEntity.receno}" onblur="queryUserInfo('rece');"/>
                            </td>
                            <td width="6%">接收人姓名</td>
                            <td width="6%" class="td_style1">
                                <input id="recename" name="stuffpermit.recename"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfstuffpermitprocessEntity.recename }"/>
                            </td>
                            <td width="6%">手機號碼&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="recemobile" name="stuffpermit.recemobile"
                                       class="easyui-validatebox" data-options="required:true,width: 100,validType:'phone[\'recemobile\',\'手機號格式不正確\']'"
                                       value="${wfstuffpermitprocessEntity.recemobile }"/>
                            </td>
                            <td width="6%">分機號碼&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="recephone" name="stuffpermit.recephone" class="easyui-validatebox"
                                       style="width:80px;"
                                       value="${wfstuffpermitprocessEntity.recephone}"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="6%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="recedeptno" name="stuffpermit.recedeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       value="${wfstuffpermitprocessEntity.recedeptno }" readonly/>
                            </td>
                            <td>單位名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="5" class="td_style1">
                                <input id="recedeptname" name="stuffpermit.recedeptname" class="easyui-validatebox"
                                       data-options="width: 350"
                                       value="${wfstuffpermitprocessEntity.recedeptname }"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">運輸物品信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <input id="stuffpermitItemTableIndex" type="hidden"
                                           value="<c:if test="${stuffpermititemEntity!=null && stuffpermititemEntity.size()>0}">${stuffpermititemEntity.size() +1}</c:if>
                                        <c:if test="${stuffpermititemEntity==null}">2</c:if>">
                                    </input>
                                    <table id="stuffpermitItemTable" width="100%">
                                        <tr align="center">
                                            <td>物品名稱&nbsp;<font color="red">*</font></td>
                                            <td>數量&nbsp;<font color="red">*</font></td>
                                            <td>單位&nbsp;<font color="red">*</font></td>
                                            <td>有效時間&nbsp;<font color="red">*</font></td>
                                            <td>備註(原因)&nbsp;<font color="red">*</font></td>
                                            <td>操作</td>
                                        </tr>
                                        <c:if test="${stuffpermititemEntity!=null&&stuffpermititemEntity.size()>0}">
                                        <c:forEach items="${stuffpermititemEntity}" var="stuffpermititem" varStatus="status">
                                        <tr align="center" id="stuffpermitItem${status.index+1}">
                                            <td><input id="stuffpermit_stuffno${status.index+1}"
                                                       name="stuffpermititems[${status.index}].stuffno"
                                                       class="easyui-validatebox" data-options="required:true"
                                                       style="width: 200px;"
                                                       value="${stuffpermititem.stuffno}"/>
                                            </td>
                                            <td><input id="stuffpermit_count${status.index+1}"
                                                       name="stuffpermititems[${status.index}].count"
                                                       class="easyui-validatebox" data-options="required:true,validType:'pinteger[\'stuffpermit_count${status.index+1}\',\'只能輸入正整數\']'"
                                                       style="width: 80px;"
                                                       value="${stuffpermititem.count}"/>
                                            </td>
                                            <td><input id="stuffpermit_unit${status.index+1}"
                                                       name="stuffpermititems[${status.index}].unit"
                                                       class="easyui-validatebox" data-options="required:true"
                                                       style="width: 80px;"
                                                       value="${stuffpermititem.unit}"/>
                                            </td>
                                            <td><input id="stuffpermit_effectime${status.index+1}"
                                                       name="stuffpermititems[${status.index}].effectime"
                                                       class="easyui-validatebox Wdate"
                                                       data-options="width:130,required:true" value="<fmt:formatDate  pattern="yyyy-MM-dd HH:mm"
                                                       value="${stuffpermititem.effectime}"/>"
                                                       onclick="WdatePicker({skin:'whyGreen',minDate:'%y-%M-%d',maxDate:'%y-%M-{%d+1}',dateFmt:'yyyy-MM-dd HH:mm'})"/>
                                            </td>
                                            <td><input id="stuffpermit_reson${status.index+1}"
                                                       name="stuffpermititems[${status.index}].reson"
                                                       class="easyui-validatebox" data-options="required:true"
                                                       style="width: 400px;"
                                                       value="${stuffpermititem.reson}"/>
                                            </td>
                                            <td><input type="image" src="${ctx}/static/images/deleteRow.png"
                                                       onclick="stuffpermitdeltr(${status.index+1});return false;"/>
                                            </td>
                                        </tr>
                                        </c:forEach>
                                        </c:if>
                                        <c:if test="${stuffpermititemEntity==null}">
                                            <tr align="center" id="stuffpermitItem1">
                                                <td><input id="stuffpermit_stuffno1"
                                                           name="stuffpermititems[0].stuffno"
                                                           class="easyui-validatebox" data-options="required:true"
                                                           style="width: 200px;" value=""/>
                                                </td>
                                                <td><input id="stuffpermit_count1"
                                                           name="stuffpermititems[0].count"
                                                           class="easyui-validatebox" data-options="required:true,validType:'pinteger[\'stuffpermit_count1\',\'只能輸入正整數\']'"
                                                           style="width: 80px;" value=""/>
                                                </td>
                                                <td><input id="stuffpermit_unit1"
                                                           name="stuffpermititems[0].unit"
                                                           class="easyui-validatebox" data-options="required:true"
                                                           style="width: 80px;" value=""/>
                                                </td>
                                                <td><input id="stuffpermit_effectime1"
                                                           name="stuffpermititems[0].effectime"
                                                           class="easyui-validatebox Wdate"
                                                           data-options="width:130,required:true" value=""
                                                           onclick="WdatePicker({skin:'whyGreen',minDate:'%y-%M-%d',maxDate:'%y-%M-{%d+1}',dateFmt:'yyyy-MM-dd HH:mm'})"/>
                                                </td>
                                                <td><input id="stuffpermit_reson1"
                                                           name="stuffpermititems[0].reson"
                                                           class="easyui-validatebox" data-options="required:true"
                                                           style="width: 400px;" value=""/>
                                                </td>
                                                <td><input type="image" src="${ctx}/static/images/deleteRow.png"
                                                           onclick="stuffpermitdeltr(${status.index+1});return false;"/>
                                                </td>
                                            </tr>
                                        </c:if>
                                        <tr class="nottr"  align="left">
                                            <td colspan="6" width="100%" style="text-align:left;padding-left:10px;">
                                                <input type="button" id="stuffpermitItemAdd"
                                                       style="width:100px;float:left;" value="添加一筆"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">物品流向信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="4">物品發放地點&nbsp;<font color="red">*</font></td>
                            <td colspan="2">至</td>
                            <td colspan="2">物品接收地點&nbsp;<font color="red">*</font></td>
                        </tr>
                        <tr align="center">
                            <td colspan="4">
                                <input id="fromarea" name="stuffpermit.fromarea" class="easyui-validatebox"
                                       data-options="required:true"
                                       style="width: 350px;" value="${wfstuffpermitprocessEntity.fromarea}"/>
                            </td>
                            <td colspan="2">~</td>
                            <td colspan="2">
                                <input id="toarea" name="stuffpermit.toarea" class="easyui-validatebox"
                                       data-options="required:true"
                                       style="width: 350px;" value="${wfstuffpermitprocessEntity.toarea}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">是否出三線門崗&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style2">
                                <div class="outdoorDiv"></div>
                                <input id="outdoor" name="stuffpermit.outdoor"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfstuffpermitprocessEntity.outdoor}"/>
                            </td>
                            <td colspan="2">是否入三線門崗&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style2">
                                <div class="indoorDiv"></div>
                                <input id="indoor" name="stuffpermit.indoor"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${wfstuffpermitprocessEntity.indoor}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">出區門崗(一線)</td>
                            <td colspan="2" class="td_style2">
                                <input id="outarea" name="stuffpermit.outarea" class="easyui-combobox" data-options="width: 150,onSelect:function(){outAreaCheck();}"
                                       value="${wfstuffpermitprocessEntity.outarea }" panelHeight="auto"/>
                            </td>
                            <td colspan="2">入區門崗(一線)</td>
                            <td colspan="2" class="td_style2">
                                <input id="inarea" name="stuffpermit.inarea" class="easyui-combobox" data-options="width: 150,onSelect:function(){inAreaCheck();}"
                                       value="${wfstuffpermitprocessEntity.inarea }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">運輸方式信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">運輸方式&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style2">
                                <input id="transportmode" name="stuffpermit.transportmode" class="easyui-combobox"
                                       value="${wfstuffpermitprocessEntity.transportmode }" panelHeight="auto"
                                       data-options="width: 100,onSelect:function(){transportmodeCheck();}" editable="false"/>
                            </td>
                            <td colspan="2">運輸人員&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style2">
                                <input id="transportperson" name="stuffpermit.transportperson" class="easyui-combobox"
                                       value="${wfstuffpermitprocessEntity.transportperson }" panelHeight="auto"
                                       data-options="width: 100,onSelect:function(){personCheck();}" editable="false"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">工號/證件號碼&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style2">
                                <input id="empnoorid" name="stuffpermit.empnoorid" class="easyui-validatebox"
                                       data-options="required:true" onchange="empNoCheck(this)"
                                       style="width: 300px;" value="${wfstuffpermitprocessEntity.empnoorid}"/>
                            </td>
                            <td colspan="2">姓名&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style2">
                                <input id="transname" name="stuffpermit.transname" class="easyui-validatebox"
                                       data-options="required:true"
                                       style="width: 300px;" value="${wfstuffpermitprocessEntity.transname}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">部門/廠商名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style2">
                                <input id="deptorsupplier" name="stuffpermit.deptorsupplier" class="easyui-validatebox"
                                       data-options="required:true"
                                       style="width: 300px;" value="${wfstuffpermitprocessEntity.deptorsupplier}"/>
                            </td>
                            <td colspan="2">車型</td>
                            <td colspan="2" class="td_style2">
                                <input id="carmodel" name="stuffpermit.carmodel" class="easyui-validatebox"
                                       style="width: 300px;" value="${wfstuffpermitprocessEntity.carmodel}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">車牌</td>
                            <td colspan="2" class="td_style2">
                                <input id="carno" name="stuffpermit.carno" class="easyui-validatebox"
                                       style="width: 300px;" value="${wfstuffpermitprocessEntity.carno}"/>
                            </td>
                            <td colspan="2">其他工具名稱</td>
                            <td colspan="2" class="td_style2">
                                <input id="elsename" name="stuffpermit.elsename" class="easyui-validatebox"
                                       style="width: 300px;" value="${wfstuffpermitprocessEntity.elsename}"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="12%">備註</td>
                            <td width="88%" class="td_style2">
                                1.本單僅適用于辦公耗材、勞保用品、生活用品等物品放行，不適用于資訊類物品及物料放行；<br/>
                                2.放行單填寫信息須與實際情況相符，物品流向要合理，須在有效時間內在對應的門崗出區入區，否則警衛不予放行；<br/>
                                3.①當運輸方式選擇“人工”時，“車型”、“車牌”、“其他工具名稱”欄位為非必填項，②當運輸方式選擇“貨車”時，”其他工具名稱“欄位為非必填項，③當運輸方式選擇“其他工具”時，“車型”、“車牌”欄位為非必填項。<br/>
                                4.蘭考廠區安全管理部24H服務熱線：分機直撥：582-110；手機直撥：0371-26552110
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="12%">附件</td>
                            <td width="88%" class="td_style2">
                                <span class="sl-custom-file">
                                    <input type="button" value="点击上传文件" class="btn-file"/>
								    <input id="attachidsUpload" name="attachidsUpload" type="file"
                                           onchange="oosUploadFile('${workFlowId}');" class="ui-input-file"/>
								</span>
                                <input type="hidden" id="attachids" name="stuffpermit.attachids"
                                       value="${wfstuffpermitprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:if test="${file!=null&&file.size()>0}">
                                        <c:forEach items="${file}" varStatus="i" var="item">
                                            <div id="${item.id}"
                                                 style="line-height:30px;margin-left:5px;" class="float_L">
                                                <div class="float_L">
                                                    <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                                </div>
                                                <div class="float_L deleteBtn" onclick="oosDelAtt('${item.id}')"></div>
                                            </div>
                                        </c:forEach>
                                    </c:if>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('dzqh_wupinfangxingdan','物品放行單業務表','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">申請人課級主管確認
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#applydeptno').val(),'kchargeno','kchargename',$('#applyfactoryid').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="kchargeno" name="stuffpermit.kchargeno"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['kchargeno']}"
                                                               readonly
                                                               value="${wfstuffpermitprocessEntity.kchargeno }"/><c:if
                                                            test="${requiredMap['kchargeno'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="kchargename" name="stuffpermit.kchargename"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['kchargeno']}"
                                                                value="${wfstuffpermitprocessEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl1Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">放行權限主管簽核
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(90,'yl1Table','ylno1','ylname1',$('#usefactoryid').combobox('getValue'),'stuffpermit','','',$('#applydeptno').val())"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno1" name="stuffpermit.ylno1"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno1']}"
                                                               readonly
                                                               value="${wfstuffpermitprocessEntity.ylno1 }"/><c:if
                                                            test="${requiredMap['ylno1'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname1" name="stuffpermit.ylname1"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno1']}"
                                                                value="${wfstuffpermitprocessEntity.ylname1 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl2Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    出門崗(三線)警衛簽核
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon" id="chargerDiv1"
                                                                         onclick="selectRole2(91,'yl2Table','ylno2','ylname2',$('#usefactoryid').combobox('getValue'),'stuffpermit')"></div>
                                                                    <div id="chargerDiv1_" class="float_L qhUserIcon" style="display: none"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno2" name="stuffpermit.ylno2"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno2']}"
                                                               readonly
                                                               value="${wfstuffpermitprocessEntity.ylno2 }"/><c:if
                                                            test="${requiredMap['ylno2'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname2" name="stuffpermit.ylname2"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno2']}"
                                                                value="${wfstuffpermitprocessEntity.ylname2 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl3Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    出區門崗(一線)警衛簽核
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon" id="chargerDiv2"
                                                                         onclick="selectRole2(92,'yl3Table','ylno3','ylname3',$('#usefactoryid').combobox('getValue'),'stuffpermit')"></div>
                                                                    <div id="chargerDiv2_" class="float_L qhUserIcon" style="display: none"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno3" name="stuffpermit.ylno3"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno3']}"
                                                               readonly
                                                               value="${wfstuffpermitprocessEntity.ylno3 }"/><c:if
                                                            test="${requiredMap['ylno3'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname3" name="stuffpermit.ylname3"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno3']}"
                                                                value="${wfstuffpermitprocessEntity.ylname3 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl4Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    入區門崗(一線)警衛簽核
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon" id="chargerDiv3"
                                                                         onclick="selectRole2(93,'yl4Table','ylno4','ylname4',$('#usefactoryid').combobox('getValue'),'stuffpermit')"></div>
                                                                    <div id="chargerDiv3_" class="float_L qhUserIcon" style="display: none"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno4" name="stuffpermit.ylno4"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno4']}"
                                                               readonly
                                                               value="${wfstuffpermitprocessEntity.ylno4 }"/><c:if
                                                            test="${requiredMap['ylno4'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname4" name="stuffpermit.ylname4"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno4']}"
                                                                value="${wfstuffpermitprocessEntity.ylname4 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl5Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">
                                                                    入門崗(三線)警衛簽核
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon" id="chargerDiv4"
                                                                         onclick="selectRole2(94,'yl5Table','ylno5','ylname5',$('#usefactoryid').combobox('getValue'),'stuffpermit')"></div>
                                                                    <div id="chargerDiv4_" class="float_L qhUserIcon" style="display: none"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno5" name="stuffpermit.ylno5"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno5']}"
                                                               readonly
                                                               value="${wfstuffpermitprocessEntity.ylno5 }"/><c:if
                                                            test="${requiredMap['ylno5'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname5" name="stuffpermit.ylname5"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno5']}"
                                                                value="${wfstuffpermitprocessEntity.ylname5 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl6Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: center;">接收人確認</td>
                                                                <td style="border: none;">
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno6" name="stuffpermit.ylno6"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno6']}"
                                                               readonly
                                                               value="${wfstuffpermitprocessEntity.ylno6 }"/><c:if
                                                            test="${requiredMap['ylno6'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname6" name="stuffpermit.ylname6"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno6']}"
                                                                value="${wfstuffpermitprocessEntity.ylname6 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <table width="18%" style="float: left;margin-left: 5px;" id="yl7Table">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">安全單位確認</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(95,'yl7Table','ylno7','ylname7',$('#usefactoryid').combobox('getValue'),'stuffpermit')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><input id="ylno7" name="stuffpermit.ylno7"
                                                               class="easyui-validatebox"
                                                               data-options="width:80,required:${requiredMap['ylno7']}"
                                                               readonly
                                                               value="${wfstuffpermitprocessEntity.ylno7 }"/><c:if
                                                            test="${requiredMap['ylno7'].equals('true')}"><font
                                                            color="red">*</font></c:if>
                                                        /<input id="ylname7" name="stuffpermit.ylname7"
                                                                readonly class="easyui-validatebox"
                                                                data-options="width:80,required:${requiredMap['ylno7']}"
                                                                value="${wfstuffpermitprocessEntity.ylname7 }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <input type="hidden" id="disOrEnabled"  value=""/>
    <input type="hidden" id="disOrEnabledText"  value=""/>
    <input type="hidden" id="isoutAudit"   value="${wfstuffpermitprocessEntity.isout}" />
    <input type="hidden" id="isinAudit"   value="${wfstuffpermitprocessEntity.isin}" />
    <div id="win"></div>
</form>
</div>
</body>
</html>