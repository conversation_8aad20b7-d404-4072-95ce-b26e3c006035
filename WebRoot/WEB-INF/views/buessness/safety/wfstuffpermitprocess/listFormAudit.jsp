<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>物品放行單業務表</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src='${ctx}/static/js/safety/wfstuffpermitprocess.js?random=<%= Math.random()%>'></script>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
<style type="text/css">
    .td_style2 {
        text-align: left;
        color: black;
    }
</style>
</head>
<body>
<form id="mainform" action="${ctx}/wfstuffpermitprocess/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${wfstuffpermitprocessEntity.id }"/>
    <input id="serialno" name="stuffpermit.serialno" type="hidden" value="${wfstuffpermitprocessEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">物品放行單業務表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${wfstuffpermitprocessEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${wfstuffpermitprocessEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${wfstuffpermitprocessEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${wfstuffpermitprocessEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${wfstuffpermitprocessEntity.makerno}/${wfstuffpermitprocessEntity.makername}</div>
        <div class="clear"></div>
        <table class="formList">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td>使用廠區</td>
                            <td class="td_style1">
                                <input id="usefactoryid" name="stuffpermit.usefactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${wfstuffpermitprocessEntity.usefactoryid }" disabled
                                       data-options="width: 120"/>
                            </td>
                            <td>申請類別</td>
                            <td colspan="5" class="td_style1">
                                <input id="applytype" name="stuffpermit.applytype" class="easyui-combobox" data-options="width: 200"
                                       value="${wfstuffpermitprocessEntity.applytype}" panelHeight="auto" editable="false" disabled/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">申請人信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">申請人工號</td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="stuffpermit.applyno" class="easyui-validatebox inputCss"
                                       data-options="width: 80" readonly
                                       value="${wfstuffpermitprocessEntity.applyno}"/>
                            </td>
                            <td width="6%">申請人姓名</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="stuffpermit.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfstuffpermitprocessEntity.applyname }"/>
                            </td>
                            <td width="6%">手機號碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applymobile" name="stuffpermit.applymobile"
                                       class="easyui-validatebox inputCss" data-options="width: 100" readonly
                                       value="${wfstuffpermitprocessEntity.applymobile }"/>
                            </td>
                            <td width="6%">分機號碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applyphone" name="stuffpermit.applyphone" class="easyui-validatebox inputCss"
                                       style="width:80px;" readonly
                                       value="${wfstuffpermitprocessEntity.applyphone}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="6%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="stuffpermit.applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       value="${wfstuffpermitprocessEntity.applydeptno }" readonly/>
                            </td>
                            <td>單位名稱</td>
                            <td colspan="5" class="td_style1">
                                <input id="applydeptname" name="stuffpermit.applydeptname" class="easyui-validatebox inputCss"
                                       data-options="width: 350" readonly
                                       value="${wfstuffpermitprocessEntity.applydeptname }"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">接收單位信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">接收人工號</td>
                            <td width="6%" class="td_style1">
                                <input id="receno" name="stuffpermit.receno" class="easyui-validatebox inputCss"
                                       data-options="width: 80" readonly
                                       value="${wfstuffpermitprocessEntity.receno}"/>
                            </td>
                            <td width="6%">接收人姓名</td>
                            <td width="6%" class="td_style1">
                                <input id="recename" name="stuffpermit.recename"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${wfstuffpermitprocessEntity.recename }"/>
                            </td>
                            <td width="6%">手機號碼</td>
                            <td width="6%" class="td_style1">
                                <input id="recemobile" name="stuffpermit.recemobile"
                                       class="easyui-validatebox inputCss" data-options="width: 100" readonly
                                       value="${wfstuffpermitprocessEntity.recemobile }"/>
                            </td>
                            <td width="6%">分機號碼</td>
                            <td width="6%" class="td_style1">
                                <input id="recephone" name="stuffpermit.recephone" class="easyui-validatebox inputCss"
                                       style="width:80px;" readonly
                                       value="${wfstuffpermitprocessEntity.recephone}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="6%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="recedeptno" name="stuffpermit.recedeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       value="${wfstuffpermitprocessEntity.recedeptno }" readonly/>
                            </td>
                            <td>單位名稱</td>
                            <td colspan="5" class="td_style1">
                                <input id="recedeptname" name="stuffpermit.recedeptname" class="easyui-validatebox inputCss"
                                       data-options="width: 350" readonly
                                       value="${wfstuffpermitprocessEntity.recedeptname }"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">運輸物品信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x: auto;width: 1200px;">
                                    <table id="stuffpermitItemTable" width="100%">
                                        <tr align="center">
                                            <td>物品名稱</td>
                                            <td>數量</td>
                                            <td>單位</td>
                                            <td>有效時間</td>
                                            <td>備註(原因)</td>
                                        </tr>
                                        <c:if test="${stuffpermititemEntity!=null&&stuffpermititemEntity.size()>0}">
                                            <c:forEach items="${stuffpermititemEntity}" var="stuffpermititem" varStatus="status">
                                                <tr align="center" id="stuffpermitItem${status.index+1}">
                                                    <td>${stuffpermititem.stuffno}</td>
                                                    <td>${stuffpermititem.count}</td>
                                                    <td>${stuffpermititem.unit}</td>
                                                    <td><input id="stuffpermit_effectime${status.index+1}"
                                                               name="stuffpermititems[${status.index}].effectime"
                                                               class="easyui-validatebox Wdate inputCss"
                                                               data-options="width:130" value="<fmt:formatDate  pattern="yyyy-MM-dd HH:mm"
                                                               value="${stuffpermititem.effectime}"/>" disabled readonly />
                                                    </td>
                                                    <td>${stuffpermititem.reson}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="8" class="td_style1">物品流向信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="4">物品發放地點</td>
                            <td colspan="2">至</td>
                            <td colspan="2">物品接收地點</td>
                        </tr>
                        <tr align="center">
                            <td colspan="4">
                                ${wfstuffpermitprocessEntity.fromarea}
                            </td>
                            <td colspan="2">~</td>
                            <td colspan="2">
                                ${wfstuffpermitprocessEntity.toarea}
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">是否出三線門崗</td>
                            <td colspan="2" class="td_style2">
                                <div class="outdoorDiv"></div>
                                <input id="outdoor" name="stuffpermit.outdoor"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100" disabled
                                       value="${wfstuffpermitprocessEntity.outdoor}"/>
                            </td>
                            <td colspan="2">是否入三線門崗</td>
                            <td colspan="2" class="td_style2">
                                <div class="indoorDiv"></div>
                                <input id="indoor" name="stuffpermit.indoor"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100" disabled
                                       value="${wfstuffpermitprocessEntity.indoor}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">出區門崗(一線)</td>
                            <td colspan="2" class="td_style2">
                                <input id="outarea" name="stuffpermit.outarea" class="easyui-combobox" data-options="width: 150" disabled
                                       value="${wfstuffpermitprocessEntity.outarea }" panelHeight="auto"/>
                            </td>
                            <td colspan="2">入區門崗(一線)</td>
                            <td colspan="2" class="td_style2">
                                <input id="inarea" name="stuffpermit.inarea" class="easyui-combobox" data-options="width: 150" disabled
                                       value="${wfstuffpermitprocessEntity.inarea }" panelHeight="auto"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="8" class="td_style1">運輸方式信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">運輸方式</td>
                            <td colspan="2" class="td_style2">
                                <input id="transportmode" name="stuffpermit.transportmode" class="easyui-combobox"
                                       value="${wfstuffpermitprocessEntity.transportmode }" panelHeight="auto" disabled
                                       data-options="width: 100" editable="false"/>
                            </td>
                            <td colspan="2">運輸人員</td>
                            <td colspan="2" class="td_style2">
                                <input id="transportperson" name="stuffpermit.transportperson" class="easyui-combobox"
                                       value="${wfstuffpermitprocessEntity.transportperson }" panelHeight="auto" disabled
                                       data-options="width: 100" editable="false"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">工號/證件號碼</td>
                            <td colspan="2" class="td_style2">
                                <input id="empnoorid" name="stuffpermit.empnoorid" class="easyui-validatebox inputCss" readonly
                                       style="width: 300px;" value="${wfstuffpermitprocessEntity.empnoorid}"/>
                            </td>
                            <td colspan="2">姓名</td>
                            <td colspan="2" class="td_style2">
                                <input id="transname" name="stuffpermit.transname" class="easyui-validatebox inputCss" readonly
                                       style="width: 300px;" value="${wfstuffpermitprocessEntity.transname}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">部門/廠商名稱</td>
                            <td colspan="2" class="td_style2">
                                <input id="deptorsupplier" name="stuffpermit.deptorsupplier" class="easyui-validatebox inputCss" readonly
                                       style="width: 300px;" value="${wfstuffpermitprocessEntity.deptorsupplier}"/>
                            </td>
                            <td colspan="2">車型</td>
                            <td colspan="2" class="td_style2">
                                <input id="carmodel" name="stuffpermit.carmodel" class="easyui-validatebox inputCss" readonly
                                       style="width: 300px;" value="${wfstuffpermitprocessEntity.carmodel}"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">車牌</td>
                            <td colspan="2" class="td_style2">
                                <input id="carno" name="stuffpermit.carno" class="easyui-validatebox inputCss" readonly
                                       style="width: 300px;" value="${wfstuffpermitprocessEntity.carno}"/>
                            </td>
                            <td colspan="2">其他工具名稱</td>
                            <td colspan="2" class="td_style2">
                                <input id="elsename" name="stuffpermit.elsename" class="easyui-validatebox inputCss" readonly
                                       style="width: 300px;" value="${wfstuffpermitprocessEntity.elsename}"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="12%">備註</td>
                            <td width="88%" class="td_style2">
                                1.本單僅適用于辦公耗材、勞保用品、生活用品等物品放行，不適用于資訊類物品及物料放行；<br/>
                                2.放行單填寫信息須與實際情況相符，物品流向要合理，須在有效時間內在對應的門崗出區入區，否則警衛不予放行；<br/>
                                3.①當運輸方式選擇“人工”時，“車型”、“車牌”、“其他工具名稱”欄位為非必填項，②當運輸方式選擇“貨車”時，”其他工具名稱“欄位為非必填項，③當運輸方式選擇“其他工具”時，“車型”、“車牌”欄位為非必填項。<br/>
                                4.蘭考廠區安全管理部24H服務熱線：分機直撥：582-110；手機直撥：0371-26552110
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="12%">附件</td>
                            <td width="88%" class="td_style2">
                                <input type="hidden" id="attachids" name="stuffpermit.attachids" value="${wfstuffpermitprocessEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/ossAdmin/download/${item.id}">${item.name}</a>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td width="12%">批註</td>
                            <td width="88%" class="td_style2">
						        <textarea id="attachidsremark" name="attachidsremark" class="easyui-validatebox"
                                  style="width:1000px;height:60px;"
                                  rows="4" cols="4"></textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${wfstuffpermitprocessEntity.serialno}"></fox:action>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','物品放行單業務表');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${wfstuffpermitprocessEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
	</div>
	</form>
  </div>
  <div id="dlg"></div>
  <input type="hidden" id="disOrEnabled"  value="disabled"/>
  <input type="hidden" id="disOrEnabledText"  value="disabled"/>
  <input type="hidden" id="isoutAudit"   value="${wfstuffpermitprocessEntity.isout}" />
  <input type="hidden" id="isinAudit"   value="${wfstuffpermitprocessEntity.isin}" />
</body>
</html>