<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title></title>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div>
</div>
<table id="roleListdg3"></table>
<script type="text/javascript">
    var dg;
    var d;
    $(function () {
        var kchargeno = $('#kchargeno').val();
        var bchargeno = $('#bchargeno').val();
        var cchargeno = $('#cchargeno').val();
        var deptno = $('#deptNo').val();
        var chargeno = $('#chargeNo').val();
        var chargename = $('#chargeName').val();
        var factoryid = $('#factoryId').val();
        /* 因禮金單子部級廠級設定為會簽節點，故禮金的單子的按單位代碼的會簽節點全部按照課級帶出 開始*/
        if ($("#onlyKchargeSignle").val() == "1") {
            bchargeno = "";
            cchargeno = "";
        }
        if ((chargeno == "acchargeno" && chargename == "acchargename") || (chargeno == "rcchargeno" && chargename == "rcchargename")) {
            chargeno = "cchargeno";
            chargename = "cchargename";
        }
        /* 因禮金單子部級廠級設定為會簽節點，故禮金的單子的按單位代碼的會簽節點全部按照課級帶出結束 */
        var param = {
            "kchargeno": kchargeno,
            "bchargeno": bchargeno,
            "cchargeno": cchargeno,
            "deptno": deptno,
            "chargeno": chargeno,
            "chargename": chargename,
            "factoryid": factoryid
        };
//    console.log(param);
        var isCheckSort = false;
        dg = $('#roleListdg3').datagrid({
            method: "get",
            url: '${ctx}/system/auditingNode/getAuditCommonUserss',
            queryParams: param,
            nowrap: false,
            fit: true,
            fitColumns: true,
            border: false,
            striped: true,
            cache: false,  //关闭AJAX相应的缓存
            pagination: false,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            remoteSort: false,
            singleSelect: false,
            checkOnSelect: true,
            columns: [[
                {field: 'itemid', title: 'Order ID', width: 200, checkbox: true},
                {field: 'empno', title: '帐号', sortable: true, width: 100, align: 'center'},
                {field: 'username', title: '主管名稱', sortable: true, width: 100, align: 'center'}
            ]],
            onClickRow: function (rowIndex, rowData) {
                if (!isCheckSort) {
                    sortByClick('roleListdg3', rowIndex);
                    isCheckSort = false;
                }
            }
            ,
            onCheck: function (rowIndex, rowData) {
                var isSelected = false;
                var selectItems = dg.datagrid('getSelections');
                for (var i = 0; i < selectItems.length; i++) {
                    var index = dg.datagrid('getRowIndex', selectItems[i]);
                    // console.log(rowIndex);
                    if (rowIndex == index) {
                        isSelected = true;
                    }
                }
                var dr = dg.datagrid('getRows')[rowIndex];
                if ((undefined == dr["num"] || "" == dr["num"]) && isSelected) {
                    sortByClick('roleListdg3', rowIndex);
                    isCheckSort = true;
                } else {
                    isCheckSort = false;
                }
            },
            onUncheck: function (rowIndex, rowData) {
//            console.log("onUncheck111111111111");
                var isSelected = false;
                var selectItems = dg.datagrid('getSelections');
                for (var i = 0; i < selectItems.length; i++) {
                    var index = dg.datagrid('getRowIndex', selectItems[i]);
                    // console.log(rowIndex);
                    if (rowIndex == index) {
                        isSelected = true;
                    }
                }
                var dr = dg.datagrid('getRows')[rowIndex];
                if ((undefined == dr["num"] || "" != dr["num"]) && !isSelected) {
                    sortByClick('roleListdg3', rowIndex);
                    isCheckSort = true;
                } else {
                    isCheckSort = false;
                }
            },
            onUncheckAll: function (rowIndex, rowData) {
                var alllRows = dg.datagrid('getRows');
                for (var i = 0; i < alllRows.length; i++) {
                    alllRows[i]["num"] = "";
                    dg.datagrid('refreshRow', i);
                }
            }
            ,
            onSelectAll: function (rowIndex, rowData) {
                var alllRows = dg.datagrid('getRows');
                for (var i = 0; i < alllRows.length; i++) {
                    alllRows[i]["num"] = i + 1;
                    dg.datagrid('refreshRow', i);
                }
            }
        });
    });
</script>
</body>
</html>