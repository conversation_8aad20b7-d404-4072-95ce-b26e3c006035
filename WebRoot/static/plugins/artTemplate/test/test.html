<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>语法插件单元测试</title>
<link rel="stylesheet" href="./js/qunit/qunit.css">
<script src="./js/qunit/qunit.js"></script>
<script src="../dist/template-debug.js"></script>

<script id="default" type="text/html">{{value}}</script>
<script id="noEscape" type="text/html">{{#value}}</script>
<script id="include" type="text/html">{{include 'include-content'}}</script>
<script id="include-content" type="text/html">{{#value}}</script>
<script id="print" type="text/html">{{print value value2}}</script>
<script id="sandbox" type="text/html">{{typeof document}}</script>
<script id="sandbox2" type="text/html">{{window.$sandbox = true}}</script>
<script id="html" type="text/html">""''\\</script>
<script id="debug-render" type="text/html">{{a.b.c.d.e.f}}</script>
<script id="debug-syntax" type="text/html">{{a b c d e f}}</script>
<script id="helper" type="text/html">{{test value}}</script>
<script id="helper2" type="text/html">{{#value | test}}</script>
<script id="helper3" type="text/html">{{value | test | link: 'abcd'}}</script>

<script>
if (!window.console) {
	window.console = {
		log: function () {}
	}
}
test('基本', function () {
	var data = {value: 'hello <em>world</em>'};
	equal(typeof template.compile('{{value}}'), 'function', '编译成函数');
	equal(template('default', data), 'hello &#60;em&#62;world&#60;/em&#62;', '编码输出');
	equal(template('noEscape', data), 'hello <em>world</em>', '原文输出');
});

test('特殊类型变量输出', function () {
	var data = {value: 'hello <em>world</em>'};
	equal(template('default', {value:function(){return 'hello world'}}), 'hello world', '函数类型运算后输出');
	equal(template('default', {value:0}), '0', 'Number类型输出');
	equal(template('default', {value:false}), '', 'Boolean(false)类型输出空值');
	equal(template('default', {value:true}), '', 'Boolean(true)类型输出空值');
	equal(template('default', {value:{}}), '', 'Object类型输出空值');
});

test('特殊字符输出', function () {
	equal(template('html', {}), '""\'\'\\\\', '编码输出js特殊字符');
});

test('XSS防范测试', function () {
	equal(template('default', {value:'<>"\'&'}), '&#60;&#62;&#34;&#39;&#38;', 'HTML字符转义');
});

test('空值处理', function () {
	equal(template('default', {value:''}), '', '空字符串输出');
	equal(template('default', {}), '', 'undefined转成空值');
	equal(template('default', {value:null}), '', 'null转成空值');
});

test('内置方法', function () {
	var data = {value: 'hello <em>world</em>', value2: '...'};
	equal(template('include', data), 'hello <em>world</em>', 'include');
	equal(template('print', data), 'hello <em>world</em>...', 'print');
});

test('辅助方法', function () {
	var data = {value: 'hello world'};
	template.helper('test', function (content) {
		return '<em>' + content + '</em>';
	});

	template.helper('link', function (content, url) {
		return '<i' + content + 'i>[' + url + ']';
	});

	equal(template('helper', data), '<em>hello world</em>', '兼容以前的语法');
	equal(template('helper2', data), '<em>hello world</em>', '使用新的管道风格');
	equal(template('helper3', {value: '<--->'}), '<i<em>&#60;---&#62;</em>i>[abcd]', '嵌套使用');
});

test('沙箱', function () {
	equal(template('sandbox', {}), 'undefined', '拒绝读取外部对象');
	template('sandbox2', {});
	equal(window.$sandbox, undefined, '防范污染外部对象');
});

test('调试', function () {
	var onerror = template.onerror;


	var error = null;
	template.onerror = function (e) {
		console.log(e)
		error = e;
	};
	template('debug-render-xxxxxxxxx', {});
	deepEqual({
		name: error.name,
		message: error.message
	}, {
		name: 'Render Error',
		message: 'Template not found'
	}, '没有找到模板');



	error = null;
	template.onerror = function (e) {
		console.log(e)
		error = e;
	};
	template('debug-render', {});
	deepEqual({
		name: error.name,
		line: error.line
	}, {
		name: 'Render Error',
		line: 1
	}, '渲染错误调试');



	error = null;
	template.onerror = function (e) {
		console.log(e)
		error = e;
	};
	template('debug-syntax', {});
	deepEqual({
		name: error.name
	}, {
		name: 'Syntax Error'
	}, '语法错误调试');

	template.onerror = onerror;
});

test('配置功能', function () {
	template.defaults.escape = false;
	template.defaults.openTag = '<<';
	template.defaults.closeTag = '>>';
	var data = {value: 'hello <em>world</em>'};
	equal(template.compile('<<value>>')(data), 'hello <em>world</em>', '自定义界定符');
	equal(template.compile('<<#value>>')(data), 'hello <em>world</em>', '关闭默认编码输出');

	template.defaults.escape = true;
	template.defaults.openTag = '{{';
	template.defaults.closeTag = '}}';
});
</script>
</head>
<body>
	<div id="qunit"></div>
	<div id="qunit-fixture">test markup</div>
</body>
</html>
