/* 
 * My97 DatePicker 4.6
 * Ƥ������:blue
 */
 
/* ����ѡ������ DIV */
.WdateDiv{
	width:180px;
	background-color:#fff;
	border:#87B3DA 1px solid;
	padding:2px;
}
/* ˫�������Ŀ�� */
.WdateDiv2{
	width:360px;
}
.WdateDiv *{font-size:9pt;}

/****************************
 * ����ͼ�� ȫ����A��ǩ
 ***************************/
.WdateDiv .NavImg a{
	cursor:pointer;
	display:block;
	width:16px;
	height:16px;
}

.WdateDiv .NavImgll a{
	float:left;
	background-image:url(navLeft.gif);
}
.WdateDiv .NavImgl a{
	float:left;
	background:url(left.gif);
}
.WdateDiv .NavImgr a{
	float:right;
	background-image:url(right.gif);
}
.WdateDiv .NavImgrr a{
	float:right;
	background-image:url(navRight.gif);
}

/****************************
 * ����·����
 ***************************/
/* ����·��� DIV */
.WdateDiv #dpTitle{
	height:24px;
	background:url(bg.jpg);
	padding-top:2px;
	margin-bottom:2px;
}
/* ����·������ INPUT */
.WdateDiv .yminput{
	margin-top:2px;
	text-align:center;
	border:0px;
	height:16px;
	width:50px;
	color:#FFF;
	background-color:transparent;
	cursor:pointer;
}
/* ����·�������ý���ʱ����ʽ INPUT */
.WdateDiv .yminputfocus{
	margin-top:2px;
	text-align:center;
	font-weight:bold;
	color:#195184;
	border:#939393 1px solid;
	height:16px;
	width:50px;
}
/* �˵�ѡ��� DIV */
.WdateDiv .menuSel{
	z-index:1;
	position:absolute;
	background-color:#FFFFFF;
	border:#A3C6C8 1px solid;
	display:none;
}
/* �˵�����ʽ TD */
.WdateDiv .menu{
	cursor:pointer;
	background-color:#fff;
	color:#3C82C0;
}
/* �˵���mouseover��ʽ TD */
.WdateDiv .menuOn{
	cursor:pointer;
	background-color:#B5CEE8;
}
/* �˵���Чʱ����ʽ TD */
.WdateDiv .invalidMenu{
	color:#aaa;
}
/* ��ѡ����ƫ�� DIV */
.WdateDiv .YMenu{
	margin-top:16px;
}
/* ��ѡ����ƫ�� DIV */
.WdateDiv .MMenu{
	margin-top:16px;
	*width:62px;
}
/* ʱѡ����λ�� DIV */
.WdateDiv .hhMenu{
	margin-top:-86px; 
	margin-left:26px;
}
/* ��ѡ����λ�� DIV */
.WdateDiv .mmMenu{
	margin-top:-44px; 
	margin-left:26px;
}
/* ��ѡ����λ�� DIV */
.WdateDiv .ssMenu{
	margin-top:-23px; 
	margin-left:26px;
}

/****************************
 * �����
 ***************************/
 .WdateDiv .Wweek {
 	text-align:center;
	background:#B7C9FD;
	border-right:#BDEBEE 1px solid;
 }
/****************************
 * ����,�������
 ***************************/
 /* ������ TR */
.WdateDiv .MTitle{
	color:#BCEAED;
	background-color:#5D99CF;
}
/* ��������� TABLE */
.WdateDiv .WdayTable{
	line-height:20px;	
	color:#3C82C0;	
}
/* ���ڸ����ʽ TD */
.WdateDiv .Wday{
	cursor:pointer;
}
/* ���ڸ��mouseover��ʽ TD */
.WdateDiv .WdayOn{
	cursor:pointer;
	color:#FFF;
	background-color:#4A88C5;
}
/* ��ĩ���ڸ����ʽ TD */
.WdateDiv .Wwday{
	cursor:pointer;
	color:#D01214;
}
/* ��ĩ���ڸ��mouseover��ʽ TD */
.WdateDiv .WwdayOn{
	cursor:pointer;
	color:#FEA7A3;
	background-color:#4A88C5;
}
.WdateDiv .Wtoday{
	cursor:pointer;
	color:#65933C;
}
.WdateDiv .Wselday{
	color:#FFF;	
	background-color:#87B0DA;
}
.WdateDiv .WspecialDay{
	background-color:#66F4DF;
}
/* �����·ݵ����� */
.WdateDiv .WotherDay{ 
	cursor:pointer;
	color:#B5CEE8;	
}
/* �����·ݵ�����mouseover��ʽ */
.WdateDiv .WotherDayOn{ 
	cursor:pointer;
	background-color:#87B0DA;	
}
/* ��Ч���ڵ���ʽ,�������ڷ�Χ�������ڸ����ʽ,����ѡ������� */
.WdateDiv .WinvalidDay{
	color:#aaa;
}
/****************************
 * ʱ�����
 ***************************/
/* ʱ���� DIV */
.WdateDiv #dpTime{
	
}
/* ʱ������ SPAN */
.WdateDiv #dpTime #dpTimeStr{
	margin-left:1px;
	color:#3C81C2;
}
/* ʱ������� INPUT */
.WdateDiv #dpTime input{
	height:16px;
	width:18px;
	text-align:center;
	color:#333;
	border:#3C81C2 1px solid;	
}
/* ʱ�� ʱ INPUT */
.WdateDiv #dpTime .tB{
	border-right:0px;
}
/* ʱ�� �ֺͼ���� ':' INPUT */
.WdateDiv #dpTime .tE{
	border-left:0;
	border-right:0;
}
/* ʱ�� �� INPUT */
.WdateDiv #dpTime .tm{
	width:7px;
	border-left:0;
	border-right:0;
}
/* ʱ���ұߵ����ϰ�ť BUTTON */
.WdateDiv #dpTime #dpTimeUp{
	height:10px;
	width:13px;
	border:0px;
	background-image:url(up.jpg);
}
/* ʱ���ұߵ����°�ť BUTTON */
.WdateDiv #dpTime #dpTimeDown{
	height:10px;
	width:13px;
	border:0px;
	background-image:url(down.jpg);
}
/****************************
 * ����
 ***************************/
 .WdateDiv #dpQS {
 	float:left;
	margin-right:3px;
	margin-top:3px;
	background-image:url(qs.jpg);
	width:20px;
	height:20px;
	cursor:pointer;
 }
.WdateDiv #dpControl {
	text-align:right;
	margin-top:3px;
}
.WdateDiv .dpButton{ 
	height:18px;
	width:45px;
	border:0px;
	padding-top:2px;
	background:url(btnbg.jpg);
	color:#FFF;
}