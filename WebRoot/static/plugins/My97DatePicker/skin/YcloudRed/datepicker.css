/* 
 * My97 DatePicker 4.7
 * Ƥ������:YcloudRed
 * Ƥ������:�������� <EMAIL>
 */
 
/* ����ѡ������ DIV */
.WdateDiv { width:180px; padding:2px; background-color:#fff; border: solid 1px #eb2f14; }
/* ˫�������Ŀ�� */
.WdateDiv2{width:360px;}
.WdateDiv * { font:9pt Arial, Helvetica, sans-serif; }
/****************************
 * ����ͼ�� ȫ����A��ǩ
 ***************************/
.WdateDiv .NavImg a{ display:block;width:16px; height:16px; cursor:pointer; }
.WdateDiv .NavImgll a{ float:left;background:url(bg.gif) no-repeat 4px 6px; }
.WdateDiv .NavImgl a{ float:left;background:url(bg.gif) no-repeat 4px -10px; }
.WdateDiv .NavImgr a{ float:right;background:url(bg.gif) no-repeat -20px -10px; }
.WdateDiv .NavImgrr a{ float:right;background:url(bg.gif) no-repeat -20px 6px; }
/****************************
 * ����·����
 ***************************/
/* ����·��� DIV */
.WdateDiv #dpTitle { height:24px; margin-bottom:2px; background:#ea3017 url(qs.jpg) repeat-x bottom; }
/* ����·������ INPUT */
.WdateDiv .yminput { width:50px; height:20px; margin-top:2px; border:0px; background:transparent; cursor:pointer; color:white; line-height:14px; text-align:center; }
/* ����·�������ý���ʱ����ʽ INPUT */
.WdateDiv .yminputfocus { width:50px; height:20px; margin-top:2px; border:#939393 1px solid; color:red; font-weight:bold; line-height:14px; text-align:center; }
/* �˵�ѡ��� DIV */
.WdateDiv .menuSel { z-index:1;position:absolute; display:none; background-color:#FFFFFF; border:#eb2f14 1px solid; }
/* �˵�����ʽ TD */
.WdateDiv .menu { cursor:pointer; background-color:#fff; color:#e12b17; }
/* �˵���mouseover��ʽ TD */
.WdateDiv .menuOn { cursor:pointer; color:white; background-color:#e12b17; }
/* �˵���Чʱ����ʽ TD */
.WdateDiv .invalidMenu { color:#aaa; }
/* ��ѡ����ƫ�� DIV */
.WdateDiv .YMenu { margin-top:20px; }
/* ��ѡ����ƫ�� DIV */
.WdateDiv .MMenu { margin-top:20px; *width:62px;
}
/* ʱѡ����λ�� DIV */
.WdateDiv .hhMenu { margin:-90px 0 0 26px; }
/* ��ѡ����λ�� DIV */
.WdateDiv .mmMenu { margin:-46px 0 0 26px; }
/* ��ѡ����λ�� DIV */
.WdateDiv .ssMenu { margin:-24px 0 0 26px; }
/****************************
 * �����
 ***************************/
 .WdateDiv .Wweek { background:#DAF3F5; border-right:#BDEBEE 1px solid; text-align:center; }
/****************************
 * ����,�������
 ***************************/
 /* ������ TR */
.WdateDiv .MTitle { background:#ef341c; color:white; }
.WdateDiv .WdayTable2{
	border-collapse:collapse;
	border:#eb2f14 1px solid;
}
.WdateDiv .WdayTable2 table{
	border:0;
}
/* ��������� TABLE */
.WdateDiv .WdayTable { background:white; border:#eb2f14 1px solid; color:red; }
.WdateDiv .WdayTable td{
	line-height:20px;
	text-align:center;
}
/* ���ڸ����ʽ TD */
.WdateDiv .Wday { cursor:pointer; }
/* ���ڸ��mouseover��ʽ TD */
.WdateDiv .WdayOn { background-color:#e12b17; cursor:pointer; font-size:13px; color:yellow; }
/* ��ĩ���ڸ����ʽ TD */
.WdateDiv .Wwday { cursor:pointer; color:green; }
/* ��ĩ���ڸ��mouseover��ʽ TD */
.WdateDiv .WwdayOn { background-color:#e12b17; color:yellow; cursor:pointer; font-size:13px; }
.WdateDiv .Wtoday { cursor:pointer; color:blue; font-size:13px; }
.WdateDiv .Wselday { background-color:#e12b17; color:yellow; font-size:13px; }
.WdateDiv .WspecialDay{	background-color:#EAF330;}
/* �����·ݵ����� */
.WdateDiv .WotherDay { cursor:pointer; color:#666; }
/* �����·ݵ�����mouseover��ʽ */
.WdateDiv .WotherDayOn { cursor:pointer; color:#666; }
/* ��Ч���ڵ���ʽ,�������ڷ�Χ�������ڸ����ʽ,����ѡ������� */
.WdateDiv .WinvalidDay { color:#aaa; }

/****************************
 * ʱ�����
 ***************************/
/* ʱ���� DIV */
.WdateDiv #dpTime { float:left;	margin-top:3px;	margin-right:30px; }
/* ʱ������ SPAN */
.WdateDiv #dpTime #dpTimeStr { margin-left:1px; color:green; }
/* ʱ������� INPUT */
.WdateDiv #dpTime input { width:18px; height:16px; border:#eb2f14 1px solid; color:black; line-height:14px; text-align:center; }
/* ʱ�� ʱ INPUT */
.WdateDiv #dpTime .tB { border-right:0px; }
/* ʱ�� �ֺͼ���� ':' INPUT */
.WdateDiv #dpTime .tE { border-left:0; border-right:0; }
/* ʱ�� �� INPUT */
.WdateDiv #dpTime .tm { width:7px; border-left:0; border-right:0; }
/* ʱ���ұߵ����ϰ�ť BUTTON */
.WdateDiv #dpTime #dpTimeUp { border:solid 1px #d80303; border-right:solid 1px #c33210; height:10px; width:13px; padding:0; background:#c33210 url(bg.gif) no-repeat -10px 2px; *background:#c33210 url(bg.gif) no-repeat -11px 1px;
}
/* ʱ���ұߵ����°�ť BUTTON */
.WdateDiv #dpTime #dpTimeDown { border:solid 1px #d80303; border-right:solid 1px #c33210; height:10px; width:13px; padding:0; background:#c33210 url(bg.gif) no-repeat -10px -17px; *background:#c33210 url(bg.gif) no-repeat -11px -18px;
}
/****************************
 * ����
 ***************************/
 .WdateDiv #dpQS { float:left; width:20px; height:20px; margin:3px 3px 0 0; background-image:url(qs.jpg); cursor:pointer; }
.WdateDiv #dpControl { margin-top:3px; text-align:right; }
.WdateDiv .dpButton { width:45px; height:21px; overflow:hidden; background:#c33210 url(qs.jpg) repeat-x bottom; color:white; border: solid 1px #D80303; *border: solid 1px;
line-height:19px; }